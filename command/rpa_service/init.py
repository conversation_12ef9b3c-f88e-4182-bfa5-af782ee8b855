def init_rpa():
    """从数据库生成 init_rpa.json"""
    import json
    from robot_processor.ext import db
    from robot_processor.rpa_service.models import Rpa

    rpas = []
    for rpa in db.session.query(Rpa):
        rpas.append(dict(
            name=rpa.name,
            description=rpa.description,
            tag=rpa.tag,
            guid=rpa.guid,
            ext_info=rpa.ext_info,
            task=rpa.task,
            argument=rpa.argument,
            limits=[
                dict(rule_type=rpa_limit.rule_type, rule_value=rpa_limit.rule_value)
                for rpa_limit in rpa.limits
            ],
        ))

    with open("init_rpa.json", "w") as f:
        json.dump(rpas, f, ensure_ascii=False, indent=1)
