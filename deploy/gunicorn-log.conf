[loggers]
keys=root, gunicorn.error, gunicorn.access

[handlers]
keys=stdout, stderr, access

[formatters]
keys=generic, access

[logger_root]
level=INFO
handlers=stdout

[logger_gunicorn.error]
level=INFO
handlers=stderr
propagate=1
qualname=gunicorn.error

[logger_gunicorn.access]
level=WARN
handlers=access
propagate=0
qualname=gunicorn.access

[handler_stdout]
class=StreamHandler
formatter=generic
args=(sys.stdout, )

[handler_access]
class=StreamHandler
formatter=access
args=(sys.stdout, )

[handler_stderr]
class=StreamHandler
formatter=generic
args=(sys.stderr, )

[formatter_generic]
format=%(asctime)s [%(process)d] [%(levelname)s] %(message)s
datefmt=%Y-%m-%d %H:%M:%S
class=logging.Formatter

[formatter_access]
format=%(message)s
class=logging.Formatter
