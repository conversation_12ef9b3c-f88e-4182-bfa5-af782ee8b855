stages:
  - build
  - test
  - deploy

.sonar:
  extends: .sonar-template
  image: registry.leyantech.com/infra/sonar-python:latest
  tags:
    - digismart-office-dev-docker
  variables:
    GIT_CLEAN_FLAGS: -ffdx -e .venv
    PYTHON_VERSION: "3.11"
    SRC_DIRS: robot_processor,rpa,external
    CUSTOM_SCRIPT: sed -i 's/addopts=/addopts= -n 8/' pytest.ini;
    SONAR_MODE: pytest
  script:
    - start-sonar-analysis.sh
    - sh check_branchpoint.sh

mypy:
  stage: test
  image: registry.leyantech.com/infra/sonar-python:latest
  tags:
    - digismart-office-dev-docker
  only:
    - merge_requests
  variables:
    GIT_CLEAN_FLAGS: -ffdx -e .venv
  script:
    - alternatives --set python3 /usr/bin/python3.11
    - virtualenv -p python3 --no-download --system-site-packages ./.venv
    - export PATH=$PWD/.venv/bin:$PATH
    - pip install -r dev-requirements.txt
    - mypy --non-interactive --cache-dir ./.venv/.mypy_cache robot_processor rpa external

include:
  - template: sonar-python.gitlab-ci.yml
