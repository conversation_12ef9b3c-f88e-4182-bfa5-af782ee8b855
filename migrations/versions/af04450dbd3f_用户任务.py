"""empty message

Revision ID: af04450dbd3f
Revises: 00ba3545de8f
Create Date: 2022-03-14 11:38:00.546219

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'af04450dbd3f'
down_revision = '00ba3545de8f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_task',
                    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
                    sa.Column('created_at', sa.Integer(), nullable=False),
                    sa.Column('updated_at', sa.Integer(), nullable=False),
                    sa.<PERSON>umn('object_id', sa.String(length=32), nullable=False),
                    sa.<PERSON>umn('operator', sa.String(length=32), nullable=False),
                    sa.Column('method', sa.<PERSON>('BatchImport', name='method'), nullable=False),
                    sa.<PERSON>umn('status', sa.Enum('Pending', 'Running', 'Success', 'Failed', 'PartialFailed', name='status'), nullable=False),
                    sa.Column('request', sa.JSON(), nullable=False),
                    sa.Column('result', sa.JSON(), nullable=False),
                    sa.PrimaryKeyConstraint('id')
                    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_task')
    # ### end Alembic commands ###
