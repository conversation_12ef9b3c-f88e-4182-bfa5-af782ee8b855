"""widget-info

Revision ID: dc7f593a5af6
Revises: 70387937ece6
Create Date: 2021-09-06 12:14:08.695030

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'dc7f593a5af6'
down_revision = 'd04b056fd10f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('widget_info',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('key', sa.String(length=64), nullable=True),
    sa.Column('widget_id', sa.Integer(), nullable=True),
    sa.Column('option_value', sa.<PERSON>(), nullable=True),
    sa.Column('widget_collection_id', sa.Integer(), nullable=True),
    sa.<PERSON>umn('before', sa.<PERSON>(), nullable=True),
    sa.<PERSON>umn('order', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['widget_collection_id'], ['widget_collection.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_widget_info_key'), 'widget_info', ['key'], unique=False)
    op.create_index(op.f('ix_widget_info_widget_id'), 'widget_info', ['widget_id'], unique=False)
    op.drop_column('step', 'ui_schema')
    op.drop_column('widget_collection', 'data')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_widget_info_widget_id'), table_name='widget_info')
    op.drop_index(op.f('ix_widget_info_key'), table_name='widget_info')
    op.drop_table('widget_info')
    op.add_column('widget_collection', sa.Column('data', mysql.JSON(), nullable=True))
    op.add_column('step', sa.Column('ui_schema', mysql.JSON(), nullable=True))
    # ### end Alembic commands ###
