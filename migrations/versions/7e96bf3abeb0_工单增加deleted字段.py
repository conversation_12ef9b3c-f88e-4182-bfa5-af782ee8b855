"""工单增加deleted字段

Revision ID: 7e96bf3abeb0
Revises: 808180730390
Create Date: 2021-11-02 11:50:03.822200

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7e96bf3abeb0'
down_revision = '808180730390'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('business_order', sa.<PERSON>umn('deleted', sa.<PERSON>(), nullable=True))
    op.add_column('form', sa.Column('deleted', sa.<PERSON>(), nullable=True))
    op.add_column('form_timeline', sa.Column('deleted', sa.<PERSON>(), nullable=True))
    op.add_column('job', sa.<PERSON>umn('deleted', sa.<PERSON>an(), nullable=True))
    op.add_column('step_retry', sa.<PERSON>umn('deleted', sa.<PERSON>(), nullable=True))
    op.add_column('widget_collection', sa.<PERSON>umn('deleted', sa.<PERSON>(), nullable=True))
    op.add_column('widget_info', sa.<PERSON>('deleted', sa.<PERSON>olean(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('widget_info', 'deleted')
    op.drop_column('widget_collection', 'deleted')
    op.drop_column('step_retry', 'deleted')
    op.drop_column('job', 'deleted')
    op.drop_column('form_timeline', 'deleted')
    op.drop_column('form', 'deleted')
    op.drop_column('business_order', 'deleted')
    # ### end Alembic commands ###
