"""组件新数据模型

Revision ID: 5e4c372bcd5d
Revises: b77a2f85df5b
Create Date: 2024-02-26 18:30:16.161533

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '5e4c372bcd5d'
down_revision = 'c149ba22f95c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('component',
    sa.Column('id', sa.String(128), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('schema', sa.JSON(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('static_config',
    sa.Column('key', sa.String(64), nullable=False),
    sa.Column('config', sa.J<PERSON>(), nullable=False),
    sa.PrimaryKeyConstraint('key')
    )
    op.create_table('form_symbol',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('form_id', sa.Integer(), nullable=False),
    sa.Column('step_uuid', sa.String(36), nullable=False),
    sa.Column('step_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(128), nullable=False),
    sa.Column('type_spec', sa.JSON(), nullable=False),
    sa.Column('v_type', sa.String(32), sa.Computed("json_unquote(json_extract(type_spec, '$.type'))", persisted=True), nullable=False),
    sa.Column('options', sa.JSON(), nullable=False),
    sa.Column('parent', sa.String(128), nullable=True),
    sa.Column('parent_path', sa.Text(), nullable=True),
    sa.Column('redirect', sa.String(256), nullable=True),
    sa.Column('component_id', sa.String(128), nullable=True),
    sa.Column('render_config', sa.JSON(), nullable=False),
    sa.Column('label', sa.String(128), sa.Computed("json_unquote(json_extract(render_config, '$.label'))", persisted=True), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.Index('idx_form_symbol_ident', 'form_id', 'step_uuid', 'step_id', 'name', unique=False),
    sa.Index('idx_form_symbol_type', 'v_type', unique=False),
    sa.Index('idx_form_symbol_component', 'component_id', unique=False)
    )
    op.add_column('step', sa.Column('parent', sa.String(32), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('step', 'parent')
    op.drop_table('form_symbol')
    op.drop_table('static_config')
    op.drop_table('component')
    # ### end Alembic commands ###
