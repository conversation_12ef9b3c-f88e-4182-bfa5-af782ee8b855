"""移除买家昵称持久化

Revision ID: e9e2a8f5bfe0
Revises: e11cd2bbfcd1
Create Date: 2023-02-17 11:49:58.329060

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'e9e2a8f5bfe0'
down_revision = 'e11cd2bbfcd1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('business_order_search_model',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('sid', sa.String(length=32), nullable=False, comment='店铺 id'),
    sa.Column('form_id', sa.Integer(), nullable=False),
    sa.Column('step_name', sa.String(length=32), nullable=False),
    sa.Column('business_order_id', sa.Integer(), nullable=False),
    sa.Column('bo_status', sa.Integer(), nullable=False),
    sa.Column('job_status', sa.Integer(), nullable=False),
    sa.Column('job_id', sa.Integer(), nullable=False),
    sa.Column('creator', sa.String(length=32), nullable=True),
    sa.Column('assignee', sa.String(length=32), nullable=True),
    sa.Column('updated_at', sa.Integer(), nullable=True),
    sa.Column('is_timeout', sa.Integer(), nullable=True),
    sa.Column('message_ts', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('business_order_id', 'business_order_search_model', ['business_order_id'], unique=False)
    op.create_index('store_assignee', 'business_order_search_model', ['assignee', 'updated_at'], unique=False)
    op.create_index('store_creator', 'business_order_search_model', ['creator', 'updated_at'], unique=False)
    op.create_index('store_form', 'business_order_search_model', ['form_id', 'updated_at'], unique=False)
    op.create_table('form_version',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('form_id', sa.Integer(), nullable=True),
    sa.Column('step_id', sa.JSON(), nullable=True, comment='当前版本已发布的步骤版本'),
    sa.Column('job_road', sa.JSON(), nullable=True, comment='工单流转路径'),
    sa.ForeignKeyConstraint(['form_id'], ['form.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.alter_column('form', 'message_digest',
               existing_type=mysql.VARCHAR(length=16),
               type_=sa.String(length=32),
               existing_comment='判断两个工单是否相同',
               existing_nullable=True)
    op.add_column('form_sync', sa.Column('form_mold', sa.Enum('CUSTOM', 'BUYER', name='formmold'), nullable=True))
    op.add_column('job_pool', sa.Column('assignee', sa.String(length=32), sa.Computed("CONCAT(job_pool.assignee_user_id,':', job_pool.assignee_user_type)", ), nullable=True))
    op.create_index(op.f('ix_job_pool_assignee'), 'job_pool', ['assignee'], unique=False)
    op.add_column('job_task', sa.Column('job_type', sa.String(length=32), nullable=True, comment='执行job的类型'))
    op.add_column('step', sa.Column('raw_step', sa.JSON(), nullable=True, comment='step快照'))
    op.alter_column('step', 'message_digest',
               existing_type=mysql.VARCHAR(length=16),
               type_=sa.String(length=32),
               existing_comment='判断两个工单是否相同',
               existing_nullable=True)
    op.add_column('task', sa.Column('tag', sa.String(length=64), nullable=True, comment='应用标签'))
    op.drop_column('task', 'tags')
    op.add_column('task_template', sa.Column('tag', sa.String(length=64), nullable=True, comment='应用标签'))
    op.drop_column('task_template', 'tags')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('task_template', sa.Column('tags', mysql.VARCHAR(length=64), nullable=True, comment='应用标签'))
    op.drop_column('task_template', 'tag')
    op.add_column('task', sa.Column('tags', mysql.VARCHAR(length=64), nullable=True, comment='应用标签'))
    op.drop_column('task', 'tag')
    op.alter_column('step', 'message_digest',
               existing_type=sa.String(length=32),
               type_=mysql.VARCHAR(length=16),
               existing_comment='判断两个工单是否相同',
               existing_nullable=True)
    op.drop_column('step', 'raw_step')
    op.drop_column('job_task', 'job_type')
    op.drop_index(op.f('ix_job_pool_assignee'), table_name='job_pool')
    op.drop_column('job_pool', 'assignee')
    op.drop_column('form_sync', 'form_mold')
    op.alter_column('form', 'message_digest',
               existing_type=sa.String(length=32),
               type_=mysql.VARCHAR(length=16),
               existing_comment='判断两个工单是否相同',
               existing_nullable=True)
    op.drop_table('form_version')
    op.drop_index('store_form', table_name='business_order_search_model')
    op.drop_index('store_creator', table_name='business_order_search_model')
    op.drop_index('store_assignee', table_name='business_order_search_model')
    op.drop_index('business_order_id', table_name='business_order_search_model')
    op.drop_table('business_order_search_model')
    # ### end Alembic commands ###
