"""add can publish for form

Revision ID: 2a7525727600
Revises: 4a2eacf0658a
Create Date: 2021-07-09 12:23:19.865173

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.

revision = '2a7525727600'
down_revision = '4a2eacf0658a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('form', sa.Column('can_publish', sa.<PERSON>(), nullable=False))
    op.add_column('widget', sa.Column('type', sa.String(length=64), nullable=True))
    op.add_column('widget', sa.Column('unique', sa.Bo<PERSON>(), nullable=False))
    op.alter_column('widget', 'label',
                    existing_type=mysql.VARCHAR(length=64),
                    nullable=False)
    op.alter_column('widget', 'type',
                    existing_type=mysql.VARCHAR(length=64),
                    nullable=False)

def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('form', 'can_publish')
    op.drop_column('widget', 'type')
    op.alter_column('widget', 'type',
                    existing_type=mysql.VARCHAR(length=64),
                    nullable=True)
    op.alter_column('widget', 'label',
                    existing_type=mysql.VARCHAR(length=64),
                    nullable=True)
    op.drop_column('widget', 'unique')
    # ### end Alembic commands ###
