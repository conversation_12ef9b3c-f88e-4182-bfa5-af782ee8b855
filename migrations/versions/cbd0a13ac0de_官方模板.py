"""官方模板

Revision ID: cbd0a13ac0de
Revises: 32cca068554b
Create Date: 2022-07-21 13:41:49.551083

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'cbd0a13ac0de'
down_revision = '32cca068554b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('form_template', sa.Column('creator', sa.String(length=128), nullable=True, comment='创建人'))
    op.add_column('form_template', sa.Column('visibility_level', sa.Enum('ORG', 'PLATFORM', 'ERP', name='visibilitytype'), nullable=True, comment='可见维度,哪种类型的店铺可以看到模板'))
    op.add_column('form_template', sa.Column('visibility_type', sa.JSO<PERSON>(), nullable=True, comment='可见维度,哪种类型的店铺可以看到模板'))
    op.add_column('form_template', sa.Column('deleted', sa.<PERSON>(), nullable=True))
    op.add_column('step_template', sa.Column('branch', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('step_template', 'branch')
    op.drop_column('form_template', 'deleted')
    op.drop_column('form_template', 'visibility_type')
    op.drop_column('form_template', 'visibility_level')
    op.drop_column('form_template', 'creator')
    # ### end Alembic commands ###
