"""add subusers

Revision ID: 3b7bb339773f
Revises: ae3a87a6c5c4
Create Date: 2021-06-07 15:29:34.002123

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3b7bb339773f'
down_revision = 'ae3a87a6c5c4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sub_user',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('shop_id', sa.Integer(), nullable=True),
    sa.Column('nick', sa.String(length=64), nullable=False, comment='子账号用户名'),
    sa.Column('seller_id', sa.Integer(), nullable=True, comment='子账号所属的主账号的唯一标识'),
    sa.Column('sub_id', sa.String(length=32), nullable=True, comment='子账号Id'),
    sa.Column('status', sa.Integer(), nullable=True, comment='子账号当前状态 1正常 -1删除 2冻结'),
    sa.ForeignKeyConstraint(['shop_id'], ['shop.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sub_user_nick'), 'sub_user', ['nick'], unique=False)
    op.create_index(op.f('ix_sub_user_sub_id'), 'sub_user', ['sub_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_sub_user_sub_id'), table_name='sub_user')
    op.drop_index(op.f('ix_sub_user_nick'), table_name='sub_user')
    op.drop_table('sub_user')
    # ### end Alembic commands ###
