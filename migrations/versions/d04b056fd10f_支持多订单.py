"""支持多订单

Revision ID: d04b056fd10f
Revises: 70387937ece6
Create Date: 2021-09-06 14:54:33.174287

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd04b056fd10f'
down_revision = '70387937ece6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('transfer_info', sa.Column('trade_info', sa.JSON(), nullable=True, comment='订单信息'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('transfer_info', 'trade_info')
    # ### end Alembic commands ###
