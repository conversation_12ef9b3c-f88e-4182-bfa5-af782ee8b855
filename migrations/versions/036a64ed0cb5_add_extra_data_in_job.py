"""add extra data in job

Revision ID: 036a64ed0cb5
Revises: 928d9e8833c8
Create Date: 2021-07-14 14:53:14.104015

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '036a64ed0cb5'
down_revision = '928d9e8833c8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('job', sa.Column('extra_data', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('job', 'extra_data')
    # ### end Alembic commands ###
