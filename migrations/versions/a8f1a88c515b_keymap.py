"""keymap

Revision ID: a8f1a88c515b
Revises: 6657c5e3879b
Create Date: 2021-07-02 15:53:17.117175

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a8f1a88c515b'
down_revision = '6657c5e3879b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('step', sa.<PERSON>umn('key_map', sa.JSON(), nullable=True))
    op.add_column('task', sa.Column('arguments', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('task', 'arguments')
    op.drop_column('step', 'key_map')
    # ### end Alembic commands ###
