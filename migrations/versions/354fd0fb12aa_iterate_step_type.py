"""step_type: 新增遍历网关

Revision ID: 354fd0fb12aa
Revises: 2868e38b8868
Create Date: 2023-07-06 18:39:22.220823

"""
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '354fd0fb12aa'
down_revision = 'a0bcaf2c20c9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('step', 'step_type',
                    existing_type=mysql.ENUM('human', 'auto', 'exclusive_gateway', 'iterate_gw_begin',
                                             'iterate_gw_end'),
                    nullable=True,
                    existing_comment='步骤类型')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('step', 'step_type',
                    existing_type=mysql.ENUM('human', 'auto', 'exclusive_gateway'),
                    nullable=True,
                    existing_comment='步骤类型')
    # ### end Alembic commands ###
