"""异常任务通知

Revision ID: 8697bab4c25e
Revises: 0eb4465471ed
Create Date: 2024-01-30 15:31:20.086054

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '8697bab4c25e'
down_revision = '0eb4465471ed'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('task_template')
    op.add_column('notice_user_exception', sa.Column('job_id', sa.Integer(), nullable=True, comment='任务 ID'))
    op.create_index(op.f('ix_notice_user_exception_notice_id'), 'notice_user_exception', ['notice_id'], unique=False)
    op.create_foreign_key(None, 'notice_user_exception', 'notice', ['notice_id'], ['id'])
    op.drop_index('ix_notice_user_mapping_created_at', table_name='notice_user_mapping')
    op.add_column('notify_org_exception', sa.Column('job_id', sa.Integer(), nullable=True, comment='任务 ID'))
    op.create_foreign_key(None, 'notify_org_exception', 'notice', ['notice_id'], ['id'])
    op.add_column('step', sa.Column('jump', sa.JSON(), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('step', 'jump')
    op.drop_constraint(None, 'notify_org_exception', type_='foreignkey')
    op.drop_column('notify_org_exception', 'job_id')
    op.create_index('ix_notice_user_mapping_created_at', 'notice_user_mapping', ['created_at'], unique=False)
    op.drop_constraint(None, 'notice_user_exception', type_='foreignkey')
    op.drop_index(op.f('ix_notice_user_exception_notice_id'), table_name='notice_user_exception')
    op.drop_column('notice_user_exception', 'job_id')
    op.create_table('task_template',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('updated_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=32), nullable=True, comment='任务名称'),
    sa.Column('description', mysql.TEXT(), nullable=True, comment='任务描述'),
    sa.Column('uuid', mysql.VARCHAR(length=32), nullable=True),
    sa.Column('task_type', mysql.VARCHAR(length=32), nullable=True),
    sa.Column('platform', mysql.JSON(), nullable=True, comment='适用平台'),
    sa.Column('code', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('erp_channel', mysql.ENUM('JST', 'WDT', 'WDTULTI', 'KUAIMAI', 'BAISHENG', 'WANLINIU', 'DUOHONG', 'BAISHOUTAO'), nullable=True),
    sa.Column('tradetime_limit', mysql.INTEGER(), autoincrement=False, nullable=True, comment='受限于3个月内订单'),
    sa.Column('tag', mysql.VARCHAR(length=64), nullable=True, comment='应用标签'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
