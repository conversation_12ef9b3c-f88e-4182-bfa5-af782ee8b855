"""string amount

Revision ID: 7be1f050452f
Revises: d592bd49484b
Create Date: 2021-08-16 15:58:48.823135

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '7be1f050452f'
down_revision = 'd592bd49484b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('transfer_info', 'amount',
               existing_type=mysql.FLOAT(),
               type_=sa.String(length=16),
               existing_comment='收款金额',
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('transfer_info', 'amount',
               existing_type=sa.String(length=16),
               type_=mysql.FLOAT(),
               existing_comment='收款金额',
               existing_nullable=False)
    # ### end Alembic commands ###
