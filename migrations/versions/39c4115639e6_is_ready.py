"""is_ready

Revision ID: 39c4115639e6
Revises: ae3a87a6c5c4
Create Date: 2021-06-07 16:17:50.949664

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '39c4115639e6'
down_revision = 'ac1aea5f1b3e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('form', sa.Column('is_ready', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('form', 'is_ready')
    # ### end Alembic commands ###
