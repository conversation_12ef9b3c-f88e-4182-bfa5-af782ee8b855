"""新增批量打款操作

Revision ID: 8b53419c0a97
Revises: 6394d2d71a27
Create Date: 2021-07-23 16:01:20.616469

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '8b53419c0a97'
down_revision = '5f950cebb4c4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('transfer_info',
                  sa.Column('operate_comment', sa.Text(), nullable=True,
                            comment='操作评论'))
    op.add_column('transfer_info', sa.Column('pre_status',
                                             sa.Enum('WAIT_APPROVAL',
                                                     'WAIT_PAY', 'PAYING',
                                                     'PAY_FINISH', 'PAY_FAILED',
                                                     'CLOSED', 'INIT',
                                                     name='paymentstatus'),
                                             nullable=True, comment='前导状态'))
    op.add_column('transfer_info',
                  sa.Column('operate_name', sa.String(length=64), nullable=True,
                            comment='操作人'))
    op.add_column('transfer_info',
                  sa.Column('operate_timestamp', sa.Integer(), nullable=True,
                            comment='操作时间'))
    op.alter_column('transfer_info', 'status',
                    existing_type=sa.Enum('WAIT_APPROVAL', 'WAIT_PAY', 'PAYING',
                                 'PAY_FINISH', 'PAY_FAILED', 'CLOSED', 'INIT'),
                    nullable=False,
                    existing_comment='付款状态')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('transfer_info', 'operate_timestamp')
    op.drop_column('transfer_info', 'operate_name')
    op.drop_column('transfer_info', 'pre_status')
    op.drop_column('transfer_info', 'operate_comment')
    op.alter_column('transfer_info', 'status',
                    existing_type=sa.Enum('WAIT_APPROVAL', 'WAIT_PAY', 'PAYING',
                                          'PAY_FINISH', 'PAY_FAILED'),
                    nullable=False,
                    existing_comment='付款状态')
    # ### end Alembic commands ###
