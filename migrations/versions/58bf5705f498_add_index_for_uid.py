"""add index for uid

Revision ID: 58bf5705f498
Revises: ba8a50a95b90
Create Date: 2021-06-09 11:32:41.553194

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '58bf5705f498'
down_revision = 'ba8a50a95b90'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_business_order_uid'), 'business_order', ['uid'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_business_order_uid'), table_name='business_order')
    # ### end Alembic commands ###
