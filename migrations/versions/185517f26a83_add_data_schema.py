"""add data_schema

Revision ID: 185517f26a83
Revises: 14ad00996854
Create Date: 2023-05-25 11:33:08.263353

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '185517f26a83'
down_revision = '14ad00996854'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('widget', sa.Column('data_schema_template', sa.JSON(), nullable=True, comment='数据的结构定义和验证规则'))
    op.add_column('widget_info', sa.Column('data_schema', sa.JSON(), nullable=True, comment='数据的结构定义和验证规则'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('widget_info', 'data_schema')
    op.drop_column('widget', 'data_schema_template')
    # ### end Alembic commands ###
