"""exception pool

Revision ID: 14ad00996854
Revises: 7e831b8212f5
Create Date: 2023-05-24 18:19:39.994738

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '14ad00996854'
down_revision = '7e831b8212f5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('exception_rule',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('priority', sa.Integer(), nullable=True, comment='优先级，数值越大优先级越高'),
    sa.Column('enabled', sa.<PERSON>(), nullable=True, comment='规则是否生效'),
    sa.<PERSON>umn('scopes', sa.JSON(), nullable=True, comment='匹配规则生效范围，如人工步骤、RPA创建补发单等'),
    sa.<PERSON>umn('rules', sa.JSON(), nullable=True, comment='匹配规则'),
    sa.Column('extra_config', sa.JSON(), nullable=True, comment='额外的配置信息'),
    sa.Column('reason', sa.Text(), nullable=False, comment='异常原因'),
    sa.Column('suggestion', sa.Text(), nullable=False, comment='操作建议'),
    sa.Column('updated_by', sa.String(length=32), nullable=False, comment='更新人'),
    sa.Column('hit_count', sa.Integer(), nullable=True, comment='规则命中次数'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('exception_rule_unmatched',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('scope', sa.String(length=32), nullable=True, comment='任务类型'),
    sa.Column('raw_exc', sa.Text(), nullable=False, comment='原始错误信息'),
    sa.Column('message_digest', sa.String(length=32), nullable=False, comment='原始错误信息的摘要，用于索引'),
    sa.Column('relate_job', sa.JSON(), nullable=True, comment='关联的 Job'),
    sa.Column('hit_count', sa.Integer(), sa.Computed('json_length(relate_job)', ), nullable=True, comment='发生次数'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_message_digest', 'exception_rule_unmatched', ['message_digest'], unique=False)
    op.create_table('exceptional_business_order_pool',
    sa.Column('exception_info', sa.JSON(), nullable=True, comment='异常信息. 异常原因/操作建议'),
    sa.Column('reason', sa.String(length=128), nullable=True, comment='异常原因'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updater', sa.JSON(), nullable=True, comment='异常池更新人'),
    sa.Column('is_processing', sa.Boolean(), nullable=True, comment='是否在处理中'),
    sa.Column('org_id', sa.Integer(), nullable=False, comment='租户id'),
    sa.Column('sid', sa.String(length=32), nullable=False, comment='店铺id'),
    sa.Column('shop_name', sa.String(length=128), nullable=False, comment='店铺名称'),
    sa.Column('shop_platform', sa.String(length=64), nullable=False, comment='店铺平台'),
    sa.Column('sys_type', sa.Integer(), nullable=False, comment='系统类型，表示是否是老系统创建的工单，还是重构后创建的工单。 1: 老系统 2:新系统'),
    sa.Column('name', sa.String(length=32), nullable=False, comment='工单名称'),
    sa.Column('bo_info', sa.JSON(), nullable=False, comment='工单信息'),
    sa.Column('business_order_id', sa.String(length=32), nullable=False, comment='工单id'),
    sa.Column('bo_created_at', sa.Integer(), nullable=True, comment='工单创建时间'),
    sa.Column('creator_name', sa.String(length=32), nullable=True, comment='工单创建人'),
    sa.Column('current_job_info', sa.JSON(), nullable=True, comment='当前任务信息'),
    sa.Column('current_job_id', sa.String(length=32), nullable=True, comment='当前任务id'),
    sa.Column('current_job_name', sa.String(length=32), nullable=True, comment='当前任务名称'),
    sa.Column('current_job_rpa_name', sa.String(length=32), nullable=True, comment='当前任务rpa名称'),
    sa.Column('assignee', sa.JSON(), nullable=True, comment='当前任务处理人'),
    sa.Column('assignee_name', sa.String(length=32), nullable=True, comment='当前任务处理人名称'),
    sa.Column('task_type', sa.String(length=32), nullable=True, comment='任务类型'),
    sa.Column('auto_retry_status', sa.String(length=32), nullable=True, comment='自动重试状态'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('exceptional_business_order_pool.assignee_name.idx', 'exceptional_business_order_pool', ['assignee_name'], unique=False)
    op.create_index('exceptional_business_order_pool.bo_id_and_job_id.idx', 'exceptional_business_order_pool', ['business_order_id', 'current_job_id'], unique=False)
    op.create_index('exceptional_business_order_pool.creator_name.idx', 'exceptional_business_order_pool', ['creator_name'], unique=False)
    op.create_index('exceptional_business_order_pool.current_job_name.idx', 'exceptional_business_order_pool', ['current_job_name'], unique=False)
    op.create_index('exceptional_business_order_pool.current_job_rpa_name.idx', 'exceptional_business_order_pool', ['current_job_rpa_name'], unique=False)
    op.create_index('exceptional_business_order_pool.name.idx', 'exceptional_business_order_pool', ['name'], unique=False)
    op.create_index('exceptional_business_order_pool.org_and_sid_and_ts.idx', 'exceptional_business_order_pool', ['org_id', 'sid', 'updated_at'], unique=False)
    op.create_index('exceptional_business_order_pool.reason.idx', 'exceptional_business_order_pool', ['reason'], unique=False)
    op.create_index('exceptional_business_order_pool.updated_at.idx', 'exceptional_business_order_pool', ['updated_at'], unique=False)
    op.create_table('step_auto_skip',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('step_uuid', sa.String(length=32), nullable=True),
    sa.Column('can_skip', sa.Boolean(), nullable=True),
    sa.Column('update_user', sa.String(length=128), nullable=True),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('step_skip',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('step_uuid', sa.String(length=32), nullable=True),
    sa.Column('can_skip', sa.Boolean(), nullable=True),
    sa.Column('update_user', sa.String(length=128), nullable=True),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.alter_column('job', 'data',
               existing_type=mysql.JSON(),
               comment=None,
               existing_comment='用户填写的表单数据',
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('job', 'data',
               existing_type=mysql.JSON(),
               comment='用户填写的表单数据',
               existing_nullable=True)
    op.drop_table('step_skip')
    op.drop_table('step_auto_skip')
    op.drop_index('exceptional_business_order_pool.updated_at.idx', table_name='exceptional_business_order_pool')
    op.drop_index('exceptional_business_order_pool.reason.idx', table_name='exceptional_business_order_pool')
    op.drop_index('exceptional_business_order_pool.org_and_sid_and_ts.idx', table_name='exceptional_business_order_pool')
    op.drop_index('exceptional_business_order_pool.name.idx', table_name='exceptional_business_order_pool')
    op.drop_index('exceptional_business_order_pool.current_job_rpa_name.idx', table_name='exceptional_business_order_pool')
    op.drop_index('exceptional_business_order_pool.current_job_name.idx', table_name='exceptional_business_order_pool')
    op.drop_index('exceptional_business_order_pool.creator_name.idx', table_name='exceptional_business_order_pool')
    op.drop_index('exceptional_business_order_pool.bo_id_and_job_id.idx', table_name='exceptional_business_order_pool')
    op.drop_index('exceptional_business_order_pool.assignee_name.idx', table_name='exceptional_business_order_pool')
    op.drop_table('exceptional_business_order_pool')
    op.drop_index('ix_message_digest', table_name='exception_rule_unmatched')
    op.drop_table('exception_rule_unmatched')
    op.drop_table('exception_rule')
    # ### end Alembic commands ###
