"""add collection in template

Revision ID: 928d9e8833c8
Revises: 27dfc979916e
Create Date: 2021-07-14 11:07:25.453118

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '928d9e8833c8'
down_revision = 'a4e3f58655c5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('step_template', sa.Column('widget_collection_id', sa.String(length=32), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('step_template', 'widget_collection_id')
    # ### end Alembic commands ###
