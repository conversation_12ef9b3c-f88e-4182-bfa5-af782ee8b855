"""add shop

Revision ID: 783d2d0cb774
Revises: d94763b8ee41
Create Date: 2021-06-22 18:32:49.582205

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '783d2d0cb774'
down_revision = 'd94763b8ee41'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('shop', sa.Column('seller_id', sa.String(length=32), nullable=True))
    op.add_column('shop', sa.Column('open_id', sa.String(length=128), nullable=True))
    op.create_unique_constraint(None, 'shop', ['open_id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'shop', type_='unique')
    op.drop_column('shop', 'open_id')
    op.drop_column('shop', 'seller_id')
    # ### end Alembic commands ###
