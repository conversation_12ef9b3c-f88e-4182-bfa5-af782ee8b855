"""job表增加字段

Revision ID: 81d2eb5d5fc0
Revises: 2338705963a7
Create Date: 2021-09-24 13:08:28.397023

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '81d2eb5d5fc0'
down_revision = '2338705963a7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('business_order', sa.Column('mid', sa.String(length=128), nullable=True, comment='飞梭帐号'))
    op.alter_column('business_order', 'creator_type',
               existing_type=mysql.ENUM('USER', 'ASSISTANT', 'RPA'),
               comment='创建人类别',
               existing_nullable=True)
    op.create_index(op.f('ix_business_order_mid'), 'business_order', ['mid'], unique=False)
    op.add_column('job', sa.Column('assignee_type', sa.Enum('USER', 'ASSISTANT', 'RPA', 'LEYAN', name='creator'), nullable=True, comment='assignee类型'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('job', 'assignee_type')
    op.drop_index(op.f('ix_business_order_mid'), table_name='business_order')
    op.alter_column('business_order', 'creator_type',
               existing_type=mysql.ENUM('USER', 'ASSISTANT', 'RPA'),
               comment=None,
               existing_comment='创建人类别',
               existing_nullable=True)
    op.drop_column('business_order', 'mid')
    # ### end Alembic commands ###
