"""add widgets and customize_form

Revision ID: 6657c5e3879b
Revises: 783d2d0cb774
Create Date: 2021-06-24 10:58:24.773133

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6657c5e3879b'
down_revision = '5fca54dcbed5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('customize_form',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('sid', sa.String(length=32), nullable=True),
    sa.Column('raw_data', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('widget',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('category', sa.Enum('ONLINE_RETAIL', 'BASIC', name='widgetcategory'), nullable=True, comment='电商/基础'),
    sa.Column('schema', sa.JSON(), nullable=True),
    sa.Column('options', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('step', sa.Column('customize_form_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'step', 'customize_form', ['customize_form_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'step', type_='foreignkey')
    op.drop_column('step', 'customize_form_id')
    op.drop_table('widget')
    op.drop_table('customize_form')
    # ### end Alembic commands ###
