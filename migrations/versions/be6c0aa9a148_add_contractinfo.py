"""add-ContractInfo

Revision ID: be6c0aa9a148
Revises: bbe44a80781d
Create Date: 2022-01-11 12:55:44.925175

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'be6c0aa9a148'
down_revision = 'bbe44a80781d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('contract_info',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('org_id', sa.String(length=32), nullable=True),
    sa.Column('end_ts', sa.Integer(), nullable=True),
    sa.<PERSON>umn('product_code', sa.<PERSON><PERSON>('FS_001', 'FS_YOUTH_001', name='productcode'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_contract_info_end_ts'), 'contract_info', ['end_ts'], unique=False)
    op.create_index(op.f('ix_contract_info_org_id'), 'contract_info', ['org_id'], unique=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_contract_info_org_id'), table_name='contract_info')
    op.drop_index(op.f('ix_contract_info_end_ts'), table_name='contract_info')
    op.drop_table('contract_info')
    # ### end Alembic commands ###
