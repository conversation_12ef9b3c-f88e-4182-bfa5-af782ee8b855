"""自动重试

Revision ID: b02ba5cf98a5
Revises: 34367093fa64
Create Date: 2023-06-02 17:29:45.284261

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'b02ba5cf98a5'
down_revision = '34367093fa64'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('job_auto_retry_record',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('enable', sa.<PERSON>(), nullable=True, comment='是否进入自动重试'),
    sa.Column('description', sa.String(length=128), nullable=True, comment='详情，若没有进入自动重试，描述原因 AutoRetryStatus'),
    sa.<PERSON>umn('job_id', sa.Integer(), nullable=True),
    sa.<PERSON>umn('message_id', sa.String(length=128), nullable=True),
    sa.Column('eta', sa.Integer(), nullable=True, comment='重试任务执行时间'),
    sa.Column('seq', sa.Integer(), nullable=True, comment='重试任务执行序号'),
    sa.Column('status', sa.String(length=32), nullable=False, comment='重试任务执行状态'),
    sa.Column('result', sa.JSON(), nullable=True, comment='重试任务执行结果'),
    sa.Column('first_fail_timestamp', sa.Integer(), nullable=True, comment='第一次失败时间,搭配StepAutoRetry.retry_duration使用'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_job_auto_retry_record_job_id'), 'job_auto_retry_record', ['job_id'], unique=False)
    op.create_index(op.f('ix_job_auto_retry_record_message_id'), 'job_auto_retry_record', ['message_id'], unique=False)
    op.create_table('step_auto_retry',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('step_uuid', sa.String(length=32), nullable=True),
    sa.Column('can_retry', sa.Boolean(), nullable=True),
    sa.Column('update_user', sa.String(length=128), nullable=True),
    sa.Column('retry_interval', sa.Integer(), nullable=True),
    sa.Column('retry_duration', sa.Integer(), nullable=True, comment='重试持续时间'),
    sa.Column('retry_times', sa.Integer(), nullable=True, comment='重试次数'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_step_auto_retry_step_uuid'), 'step_auto_retry', ['step_uuid'], unique=True)
    op.drop_index('ix_sub_user_nick', table_name='sub_user')
    op.drop_index('ix_sub_user_sub_id', table_name='sub_user')
    op.drop_table('sub_user')
    op.add_column('exception_rule', sa.Column('can_auto_retry', sa.Boolean(), nullable=True, comment='是否可以自动重试'))
    op.add_column('rpa', sa.Column('can_auto_retry', sa.Boolean(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rpa', 'can_auto_retry')
    op.drop_column('exception_rule', 'can_auto_retry')
    op.create_table('sub_user',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('updated_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('shop_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('nick', mysql.VARCHAR(length=64), nullable=False, comment='子账号用户名'),
    sa.Column('seller_id', mysql.VARCHAR(length=32), nullable=True, comment='子账号所属的主账号的唯一标识'),
    sa.Column('sub_id', mysql.VARCHAR(length=32), nullable=True, comment='子账号Id'),
    sa.Column('status', mysql.INTEGER(), autoincrement=False, nullable=True, comment='子账号当前状态 1正常 -1删除 2冻结'),
    sa.ForeignKeyConstraint(['shop_id'], ['shop.id'], name='sub_user_ibfk_1'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('ix_sub_user_sub_id', 'sub_user', ['sub_id'], unique=False)
    op.create_index('ix_sub_user_nick', 'sub_user', ['nick'], unique=False)
    op.drop_index(op.f('ix_step_auto_retry_step_uuid'), table_name='step_auto_retry')
    op.drop_table('step_auto_retry')
    op.drop_index(op.f('ix_job_auto_retry_record_message_id'), table_name='job_auto_retry_record')
    op.drop_index(op.f('ix_job_auto_retry_record_job_id'), table_name='job_auto_retry_record')
    op.drop_table('job_auto_retry_record')
    # ### end Alembic commands ###
