"""add bo_id in job_record

Revision ID: 233dc730db6b
Revises: 9dea46a26a80
Create Date: 2023-03-02 13:46:09.578133

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '233dc730db6b'
down_revision = '9dea46a26a80'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('daily_job_record', sa.Column('bo_id', sa.Integer(), nullable=True, comment='business_order外键'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('daily_job_record', 'bo_id')
    # ### end Alembic commands ###
