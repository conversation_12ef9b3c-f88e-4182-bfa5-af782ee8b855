"""买家自助填写开关

Revision ID: 1be6f6186943
Revises: cde3d7967cdd
Create Date: 2022-05-12 18:03:31.344535

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '1be6f6186943'
down_revision = 'cde3d7967cdd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('form', sa.Column('enable_service_count', sa.<PERSON>(), nullable=True, comment='买家自助填写开关'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('form', 'enable_service_count')
    # ### end Alembic commands ###
