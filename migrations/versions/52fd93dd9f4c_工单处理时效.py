"""empty message

Revision ID: 52fd93dd9f4c
Revises: e9e2a8f5bfe0
Create Date: 2023-02-20 18:59:30.018164

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '52fd93dd9f4c'
down_revision = 'e9e2a8f5bfe0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('daily_job_record',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('sid', sa.String(length=32), nullable=True),
    sa.Column('step_uuid', sa.String(length=32), nullable=True),
    sa.Column('step_name', sa.String(length=32), nullable=True),
    sa.Column('step_type', sa.String(length=32), nullable=True, comment='步骤类型'),
    sa.Column('assigner_name', sa.String(length=32), nullable=True, comment='执行人名称'),
    sa.Column('assigner_id', sa.Integer(), nullable=True),
    sa.Column('assigner_type', sa.Integer(), nullable=True),
    sa.Column('form_id', sa.Integer(), nullable=True),
    sa.Column('form_name', sa.String(length=64), nullable=True),
    sa.Column('date', mysql.DATETIME(fsp=3), nullable=True),
    sa.Column('cost', sa.Integer(), nullable=True, comment='耗时'),
    sa.Column('execute_times_total', sa.Integer(), nullable=True, comment='执行次数'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_daily_job_record_assigner_name'), 'daily_job_record', ['assigner_name'], unique=False)
    op.create_index(op.f('ix_daily_job_record_date'), 'daily_job_record', ['date'], unique=False)
    op.create_index(op.f('ix_daily_job_record_form_name'), 'daily_job_record', ['form_name'], unique=False)
    op.create_index(op.f('ix_daily_job_record_sid'), 'daily_job_record', ['sid'], unique=False)
    op.create_index(op.f('ix_daily_job_record_step_name'), 'daily_job_record', ['step_name'], unique=False)
    op.create_index('ix_sid_step_name_step_type_assigner_name_form_name', 'daily_job_record', ['sid', 'step_name', 'step_type', 'assigner_name', 'form_name'], unique=False)
    op.create_table('job_record',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('bo_id', sa.Integer(), nullable=True, comment='business_order外键'),
    sa.Column('sid', sa.String(length=32), nullable=True),
    sa.Column('step_uuid', sa.String(length=32), nullable=True),
    sa.Column('step_name', sa.String(length=32), nullable=True),
    sa.Column('step_type', sa.String(length=32), nullable=True, comment='步骤类型'),
    sa.Column('job_id', sa.Integer(), nullable=True),
    sa.Column('start', sa.Integer(), nullable=True, comment='开始时间戳'),
    sa.Column('end', sa.Integer(), nullable=True, comment='结束时间戳'),
    sa.Column('cost', sa.Integer(), nullable=True, comment='耗时'),
    sa.Column('assigner_name', sa.String(length=32), nullable=True, comment='执行人名称'),
    sa.Column('assigner_id', sa.Integer(), nullable=True),
    sa.Column('assigner_type', sa.Integer(), nullable=True),
    sa.Column('form_id', sa.Integer(), nullable=True),
    sa.Column('form_name', sa.String(length=64), nullable=True),
    sa.Column('next_jobs', sa.JSON(), nullable=True, comment='后序任务id列表'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_job_record_assigner_name'), 'job_record', ['assigner_name'], unique=False)
    op.create_index(op.f('ix_job_record_end'), 'job_record', ['end'], unique=False)
    op.create_index(op.f('ix_job_record_form_name'), 'job_record', ['form_name'], unique=False)
    op.create_index(op.f('ix_job_record_sid'), 'job_record', ['sid'], unique=False)
    op.create_index(op.f('ix_job_record_start'), 'job_record', ['start'], unique=False)
    op.create_index(op.f('ix_job_record_step_name'), 'job_record', ['step_name'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_job_record_step_name'), table_name='job_record')
    op.drop_index(op.f('ix_job_record_start'), table_name='job_record')
    op.drop_index(op.f('ix_job_record_sid'), table_name='job_record')
    op.drop_index(op.f('ix_job_record_form_name'), table_name='job_record')
    op.drop_index(op.f('ix_job_record_end'), table_name='job_record')
    op.drop_index(op.f('ix_job_record_assigner_name'), table_name='job_record')
    op.drop_table('job_record')
    op.drop_index('ix_sid_step_name_step_type_assigner_name_form_name', table_name='daily_job_record')
    op.drop_index(op.f('ix_daily_job_record_step_name'), table_name='daily_job_record')
    op.drop_index(op.f('ix_daily_job_record_sid'), table_name='daily_job_record')
    op.drop_index(op.f('ix_daily_job_record_form_name'), table_name='daily_job_record')
    op.drop_index(op.f('ix_daily_job_record_date'), table_name='daily_job_record')
    op.drop_index(op.f('ix_daily_job_record_assigner_name'), table_name='daily_job_record')
    op.drop_table('daily_job_record')
    # ### end Alembic commands ###
