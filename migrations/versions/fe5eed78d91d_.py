"""empty message

Revision ID: fe5eed78d91d
Revises: ef1e3524694f
Create Date: 2023-10-26 11:29:58.376716

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'fe5eed78d91d'
down_revision = 'ef1e3524694f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('business_order', sa.Column('flag', sa.String(length=32), sa.Computed("json_unquote(data -> '$.flag')", ), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_column('business_order', 'flag')

    # ### end Alembic commands ###
