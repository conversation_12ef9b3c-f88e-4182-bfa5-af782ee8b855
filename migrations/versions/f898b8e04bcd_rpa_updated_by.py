"""rpa updated by

Revision ID: f898b8e04bcd
Revises: 24efc717ffa4
Create Date: 2023-11-20 18:38:32.876574

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'f898b8e04bcd'
down_revision = '24efc717ffa4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rpa', sa.Column('creator', sa.String(length=64), nullable=True))
    op.add_column('rpa', sa.Column('updater', sa.String(length=64), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rpa', 'updater')
    op.drop_column('rpa', 'creator')
    # ### end Alembic commands ###
