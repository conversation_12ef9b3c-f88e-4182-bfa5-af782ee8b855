"""店铺唯一识别

Revision ID: 7e831b8212f5
Revises: 2ab42d907c51
Create Date: 2023-04-10 11:02:39.280261

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '7e831b8212f5'
down_revision = '2ab42d907c51'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('business_order_id', table_name='business_order_excepted_pool')
    op.drop_index('ix_business_order_excepted_pool_sid', table_name='business_order_excepted_pool')
    op.drop_table('business_order_excepted_pool')
    op.create_index(op.f('ix_business_order_form_version_id'), 'business_order', ['form_version_id'], unique=False)
    op.add_column('form_version', sa.Column('updated_at', sa.Integer(), nullable=True))
    op.add_column('shop', sa.Column('channel_id', sa.Integer(), nullable=True))
    op.create_unique_constraint(None, 'shop', ['channel_id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'shop', type_='unique')
    op.drop_column('shop', 'channel_id')
    op.drop_column('form_version', 'updated_at')
    op.drop_index(op.f('ix_business_order_form_version_id'), table_name='business_order')
    op.create_table('business_order_excepted_pool',
    sa.Column('created_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('updated_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('sid', mysql.VARCHAR(length=32), nullable=True, comment='店铺 id'),
    sa.Column('business_order_id', mysql.INTEGER(), autoincrement=False, nullable=True, comment='工单id'),
    sa.Column('job_id', mysql.INTEGER(), autoincrement=False, nullable=True, comment='job_id'),
    sa.Column('exception_type', mysql.ENUM('ASSIGNEE_DISABLED', 'ASSIGNEE_DELETED', 'NO_VALID_ASSIGNEE', 'JOB_RUNTIME_EXCEPTION', 'STEP_DELETE_CRUSH', 'PREV_STEP_DELETE_CRUSH', 'NEXT_STEP_DELETE_CRUSH', 'BATCH_IMPORT_TASK', 'TAOBAO_MEMO_OVER_LENGTH', 'CONNECTION_TIMEOUT', 'ORDER_NOT_CENSORED', 'ITEM_NOT_FOUND', 'LOGISTICS_NOT_FOUND', 'ORDER_NO_NOT_FOUND', 'RPA_ERROR', 'UNKNOWN'), nullable=True, comment='异常类型'),
    sa.Column('exception_category', mysql.ENUM('MEMO_OVER_LENGTH', 'NO_VALID_ASSIGNEE', 'RUN_TIME_ERROR', 'NETWORK_ERROR', 'UNKNOWN'), nullable=True, comment='异常种类'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('ix_business_order_excepted_pool_sid', 'business_order_excepted_pool', ['sid'], unique=False)
    op.create_index('business_order_id', 'business_order_excepted_pool', ['business_order_id'], unique=False)
    # ### end Alembic commands ###
