"""remove unique

Revision ID: dd15bba1c50c
Revises: 2a5dd3df1290
Create Date: 2021-07-01 17:18:43.907409

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'dd15bba1c50c'
down_revision = '2a5dd3df1290'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('name', table_name='task')
    op.alter_column('transfer_info', 'uid',
               existing_type=mysql.VARCHAR(length=255),
               nullable=True,
               existing_comment='买家昵称')
    op.alter_column('transfer_info', 'business_order_id',
               existing_type=mysql.INTEGER(),
               nullable=True,
               existing_comment='工单号')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('transfer_info', 'business_order_id',
               existing_type=mysql.INTEGER(),
               nullable=False,
               existing_comment='工单号')
    op.alter_column('transfer_info', 'uid',
               existing_type=mysql.VARCHAR(length=255),
               nullable=False,
               existing_comment='买家昵称')
    op.create_index('name', 'task', ['name'], unique=False)
    op.add_column('step', sa.Column('customize_form_id', mysql.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('step_ibfk_2', 'step', 'customize_form', ['customize_form_id'], ['id'])
    op.create_table('customize_form',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('updated_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('sid', mysql.VARCHAR(length=32), nullable=True),
    sa.Column('raw_data', mysql.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_table('widget',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('updated_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('category', mysql.ENUM('ONLINE_RETAIL', 'BASIC'), nullable=True, comment='电商/基础'),
    sa.Column('schema', mysql.JSON(), nullable=True),
    sa.Column('options', mysql.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
