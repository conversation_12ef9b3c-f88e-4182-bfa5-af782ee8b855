"""empty message

Revision ID: a0bcaf2c20c9
Revises: 2868e38b8868
Create Date: 2023-07-06 18:33:41.837717

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'a0bcaf2c20c9'
down_revision = '2868e38b8868'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('business_order', 'updated_at',
               existing_type=mysql.INTEGER(),
               comment='最近更新时间，当有操作步骤执行时，会触发更新该字段',
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('business_order', 'updated_at',
               existing_type=mysql.INTEGER(),
               comment=None,
               existing_comment='最近更新时间，当有操作步骤执行时，会触发更新该字段',
               existing_nullable=False)
    # ### end Alembic commands ###
