"""自动编号组件

Revision ID: 5b19eff3d691
Revises: 4e1230f842f6
Create Date: 2023-07-14 17:48:10.629869

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '5b19eff3d691'
down_revision = '4e1230f842f6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('widget_auto_number',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.<PERSON>umn('updated_at', sa.Integer(), nullable=False),
    sa.<PERSON>umn('widget_key', sa.String(length=64), nullable=False),
    sa.<PERSON>umn('current_number', sa.Integer(), nullable=False),
    sa.<PERSON>KeyConstraint('id')
    )
    op.create_index(op.f('ix_widget_auto_number_widget_key'), 'widget_auto_number', ['widget_key'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_widget_auto_number_widget_key'), table_name='widget_auto_number')
    op.drop_table('widget_auto_number')
    # ### end Alembic commands ###
