"""不用额外的字段存储重试信息

Revision ID: 7c19feb71b80
Revises: 9f032e99b9b9
Create Date: 2023-06-13 14:53:01.510521

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '7c19feb71b80'
down_revision = '9f032e99b9b9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('exception_rule', 'can_auto_retry')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('exception_rule', sa.Column('can_auto_retry', mysql.TINYINT(display_width=1), autoincrement=False, nullable=True, comment='是否可以自动重试'))
    # ### end Alembic commands ###
