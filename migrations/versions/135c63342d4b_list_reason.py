"""list reason

Revision ID: 135c63342d4b
Revises: cfdfed84cdbd
Create Date: 2021-07-06 19:42:01.022165

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '135c63342d4b'
down_revision = 'cfdfed84cdbd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('transfer_info', 'payment_reason',
               existing_type=mysql.VARCHAR(length=512),
               type_=sa.JSON(),
               existing_comment='付款理由',
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('transfer_info', 'payment_reason',
               existing_type=sa.JSON(),
               type_=mysql.VARCHAR(length=512),
               existing_comment='付款理由',
               existing_nullable=True)
    # ### end Alembic commands ###
