"""rpa配置规则增加排序

Revision ID: 91ae309e14cf
Revises: 2906fbbcd8dc
Create Date: 2022-10-10 14:29:59.309535

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '91ae309e14cf'
down_revision = '2906fbbcd8dc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('task_argument', sa.Column('sort', sa.Integer(), nullable=True, comment='排序字段'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('task_argument', 'sort')
    # ### end Alembic commands ###
