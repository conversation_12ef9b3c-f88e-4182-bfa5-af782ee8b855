"""form-template增加字段

Revision ID: e11cd2bbfcd1
Revises: ab3eea5d8650
Create Date: 2023-02-14 13:59:49.555207

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'e11cd2bbfcd1'
down_revision = 'ab3eea5d8650'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('form_template', sa.Column('form_mold', sa.Enum('CUSTOM', 'BUYER', name='formmold'), nullable=True, comment='工单类型,不同的类型工单模板的配置有不同限制'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('form_template', 'form_mold')
    # ### end Alembic commands ###
