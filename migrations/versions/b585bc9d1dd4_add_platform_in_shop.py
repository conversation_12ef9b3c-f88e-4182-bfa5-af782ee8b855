"""add platform in shop

Revision ID: b585bc9d1dd4
Revises: e0c5c36eda61
Create Date: 2021-07-28 10:47:28.501481

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b585bc9d1dd4'
down_revision = 'e0c5c36eda61'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('shop', sa.Column('platform', sa.String(length=64), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('shop', 'platform')
    # ### end Alembic commands ###
