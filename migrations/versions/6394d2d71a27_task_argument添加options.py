"""task argument添加options

Revision ID: 6394d2d71a27
Revises: b0e6c67a47b8
Create Date: 2021-07-19 20:55:14.547463

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '6394d2d71a27'
down_revision = 'c5560721814b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('task_argument', sa.Column('options', sa.JSON(), nullable=True))
    op.alter_column('widget', 'label',
               existing_type=mysql.VARCHAR(length=64),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('widget', 'label',
               existing_type=mysql.VARCHAR(length=64),
               nullable=False)
    op.drop_column('task_argument', 'options')
    # ### end Alembic commands ###
