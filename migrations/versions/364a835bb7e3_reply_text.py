"""reply text

Revision ID: 364a835bb7e3
Revises: 58bf5705f498
Create Date: 2021-06-09 16:16:11.976870

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '364a835bb7e3'
down_revision = '58bf5705f498'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('step', 'buyer_reply',
               existing_type=mysql.TEXT(),
               type_=sa.JSON(),
               existing_comment='买家引导话术',
               existing_nullable=True)
    op.alter_column('step_template', 'buyer_reply',
               existing_type=mysql.TEXT(),
               type_=sa.JSON(),
               existing_comment='买家引导话术',
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('step_template', 'buyer_reply',
               existing_type=sa.JSON(),
               type_=mysql.TEXT(),
               existing_comment='买家引导话术',
               existing_nullable=True)
    op.alter_column('step', 'buyer_reply',
               existing_type=sa.JSON(),
               type_=mysql.TEXT(),
               existing_comment='买家引导话术',
               existing_nullable=True)
    # ### end Alembic commands ###
