"""job id

Revision ID: 5fca54dcbed5
Revises: 60e9c274f0d5
Create Date: 2021-07-02 14:15:45.647708

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '5fca54dcbed5'
down_revision = '60e9c274f0d5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('transfer_info', sa.Column('job_id', sa.Integer(), nullable=True, comment='对应任务ID'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('transfer_info', 'job_id')
    op.add_column('step', sa.Column('customize_form_id', mysql.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('step_ibfk_2', 'step', 'customize_form', ['customize_form_id'], ['id'])
    op.create_table('customize_form',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('updated_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('sid', mysql.VARCHAR(length=32), nullable=True),
    sa.Column('raw_data', mysql.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_table('widget',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('updated_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('category', mysql.ENUM('ONLINE_RETAIL', 'BASIC'), nullable=True, comment='电商/基础'),
    sa.Column('schema', mysql.JSON(), nullable=True),
    sa.Column('options', mysql.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
