"""add meta for erp info and add wdt

Revision ID: 43a9ed966d7a
Revises: b5415b0a9539
Create Date: 2021-10-15 07:15:32.066470

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '43a9ed966d7a'
down_revision = 'b5415b0a9539'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('erp_info', sa.Column('meta', sa.JSON(), nullable=True))
    op.alter_column('erp_info', 'erp_type',
                    existing_type=mysql.ENUM('JST'),
                    type_=sa.Enum('JST', 'WDT', name='erptype'),
                    existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('erp_info', 'meta')
    op.alter_column('erp_info', 'erp_type',
                    existing_type=mysql.Enum('JST', 'WDT'),
                    type_=sa.Enum('JST', name='erptype'),
                    existing_nullable=True)
    # ### end Alembic commands ###
