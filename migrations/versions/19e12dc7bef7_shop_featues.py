"""shop featues

Revision ID: 19e12dc7bef7
Revises: 24655a9f2ec2
Create Date: 2021-08-09 16:55:26.040072

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '19e12dc7bef7'
down_revision = '24655a9f2ec2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('shop', sa.Column('features', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('shop', 'features')
    # ### end Alembic commands ###
