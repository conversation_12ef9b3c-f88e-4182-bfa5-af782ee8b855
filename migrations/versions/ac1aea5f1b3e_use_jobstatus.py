"""use jobstatus

Revision ID: ac1aea5f1b3e
Revises: 3b7bb339773f
Create Date: 2021-06-08 11:40:20.263526

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ac1aea5f1b3e'
down_revision = '3b7bb339773f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('step', 'status',
               existing_type=sa.VARCHAR(length=9),
               type_=sa.Enum('PENDING', 'RUNNING', 'FAILED', 'SUCCEED', name='jobstatus'),
               existing_nullable=True)
    op.alter_column('step_template', 'status',
               existing_type=sa.VARCHAR(length=9),
               type_=sa.Enum('PENDING', 'RUNNING', 'FAILED', 'SUCCEED', name='jobstatus'),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('step_template', 'status',
               existing_type=sa.Enum('PENDING', 'RUNNING', 'FAILED', 'SUCCEED', name='jobstatus'),
               type_=sa.VARCHAR(length=9),
               existing_nullable=True)
    op.alter_column('step', 'status',
               existing_type=sa.Enum('PENDING', 'RUNNING', 'FAILED', 'SUCCEED', name='jobstatus'),
               type_=sa.VARCHAR(length=9),
               existing_nullable=True)
    # ### end Alembic commands ###
