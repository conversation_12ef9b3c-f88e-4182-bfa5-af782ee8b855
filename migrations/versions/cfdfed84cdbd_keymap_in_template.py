"""keymap in template

Revision ID: cfdfed84cdbd
Revises: 56e87bf4d37f
Create Date: 2021-07-05 14:24:05.869943

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'cfdfed84cdbd'
down_revision = 'e5fbdcdf67ba'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('step_template', sa.Column('key_map', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('widget', 'label')
    op.drop_column('step_template', 'key_map')
    op.add_column('step', sa.Column('json_schema', mysql.JSON(), nullable=True))
    op.add_column('step', sa.Column('customize_form_id', mysql.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('step_ibfk_2', 'step', 'customize_form', ['customize_form_id'], ['id'])
    op.create_table('customize_form',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('updated_at', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('sid', mysql.VARCHAR(length=32), nullable=True),
    sa.Column('raw_data', mysql.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
