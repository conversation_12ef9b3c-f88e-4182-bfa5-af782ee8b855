"""empty message

Revision ID: cde3d7967cdd
Revises: f5afe93ece94
Create Date: 2022-04-29 10:30:15.366647

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'cde3d7967cdd'
down_revision = 'f5afe93ece94'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('job_remind_record',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('from_user', sa.J<PERSON>(), nullable=False),
    sa.Column('v_from_user_id', sa.Integer(), sa.Computed("cast(json_unquote(from_user -> '$.user_id') as UNSIGNED)", ), nullable=True),
    sa.<PERSON>umn('to_user', sa.<PERSON>(), nullable=False),
    sa.<PERSON>umn('v_to_user_id', sa.Integer(), sa.<PERSON>mp<PERSON>("cast(json_unquote(to_user -> '$.user_id') as UNSIGNED)", ), nullable=True),
    sa.Column('data', sa.JSON(), nullable=False),
    sa.Column('message_digest', sa.String(length=32), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('unique_job_remind', 'job_remind_record', ['v_from_user_id', 'v_to_user_id', 'message_digest'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('unique_job_remind', table_name='job_remind_record')
    op.drop_table('job_remind_record')
    # ### end Alembic commands ###
