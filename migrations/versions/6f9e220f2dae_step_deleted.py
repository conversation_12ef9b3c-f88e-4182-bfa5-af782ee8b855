"""step-deleted

Revision ID: 6f9e220f2dae
Revises: e92b7a86358b
Create Date: 2021-07-07 17:05:31.642524

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6f9e220f2dae'
down_revision = 'e92b7a86358b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('step', sa.Column('deleted', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('step', 'deleted')
    # ### end Alembic commands ###
