"""红点

Revision ID: d02cd1a0f6ac
Revises: 2b02401f245d
Create Date: 2022-06-16 09:49:36.419097

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'd02cd1a0f6ac'
down_revision = 'e82f4a766dc1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.create_table('job_visit_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('unique_job_id_user_id', 'job_visit_history', ['job_id', 'user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('unique_job_id_user_id', table_name='job_visit_history')
    op.drop_table('job_visit_history')
    # ### end Alembic commands ###
