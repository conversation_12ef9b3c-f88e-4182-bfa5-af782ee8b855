"""empty message

Revision ID: 3a95b3ab5e32
Revises: be6c0aa9a148
Create Date: 2022-01-26 15:34:28.009137

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '3a95b3ab5e32'
down_revision = 'be6c0aa9a148'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('step', sa.Column('assignee_groups', sa.JSON(), nullable=True, comment='客服组'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('step', 'assignee_groups')
    # ### end Alembic commands ###
