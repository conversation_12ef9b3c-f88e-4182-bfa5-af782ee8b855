"""自定义表头

Revision ID: 841f6958db8f
Revises: af04450dbd3f
Create Date: 2022-03-16 18:07:13.846885

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '841f6958db8f'
down_revision = 'af04450dbd3f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('page_title',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.Integer(), nullable=False),
    sa.Column('sid', sa.String(length=32), nullable=False, comment='店铺id'),
    sa.Column('aid', sa.String(length=128), nullable=True, comment='客服'),
    sa.<PERSON>umn('form_id', sa.Integer(), nullable=True, comment='关联的后台工单 id'),
    sa.<PERSON>umn('name', sa.String(length=128), nullable=True, comment='表头名'),
    sa.Column('page_type', sa.Enum('REPORTS', name='pagetype'), nullable=True, comment='列表页的类型'),
    sa.Column('titles', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_page_title_sid'), 'page_title', ['sid'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_page_title_sid'), table_name='page_title')
    op.drop_table('page_title')
    # ### end Alembic commands ###
