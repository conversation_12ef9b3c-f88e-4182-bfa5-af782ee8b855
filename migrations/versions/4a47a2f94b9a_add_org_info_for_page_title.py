"""add org info for page title

Revision ID: 4a47a2f94b9a
Revises: 24d860b292eb
Create Date: 2022-07-19 16:25:18.561081

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '4a47a2f94b9a'
down_revision = 'cbd0a13ac0de'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('page_title', sa.Column('org_id', sa.String(length=32), nullable=True))
    op.add_column('page_title', sa.Column('org_sid', sa.JSON(), nullable=True))
    op.add_column('page_title', sa.Column('form_name', sa.String(length=64), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('page_title', 'form_name')
    op.drop_column('page_title', 'org_sid')
    op.drop_column('page_title', 'org_id')
    # ### end Alembic commands ###
