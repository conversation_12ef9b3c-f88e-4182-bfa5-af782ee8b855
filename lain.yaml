appname: robot-processor

build:
  base: registry.leyantech.com/base-images/python:3.11
  prepare:
    version:
      files:
        - requirements.txt
    script:
      - pip install --no-cache-dir -r requirements.txt
  script:
    - rm -f config.cfg
    - pip install --no-cache-dir -r requirements.txt
    - yum install -y fontconfig wqy-microhei-fonts
    - fc-cache -fv

web:
  cmd: gunicorn -c deploy/gunicorn-server.py -b 0.0.0.0:8000 --limit-request-line 8190 robot_processor.app:app
  port: 8000
  memory: 512MB
  num_instances: 1
  healthcheck: 'http://localhost:8000/health'
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/path: "/metrics/"
    prometheus.io/port: "9191"

web.feature:
  cmd: gunicorn -c deploy/gunicorn-server.py -b 0.0.0.0:8000 --limit-request-line 8190 robot_processor.app:app
  port: 8000
  memory: 512MB
  num_instances: 1
  healthcheck: 'http://localhost:8000/health'

web.pyscript-runner:
  cmd: gunicorn -c deploy/gunicorn-server.py -b 0.0.0.0:8000 --limit-request-line 8190 sandbox.server:app
  port: 8000
  memory: 512MB
  num_instances: 1
  healthcheck: 'http://localhost:8000/health'

worker.queue:
  cmd: dramatiq --queues job --processes 1 --threads 8 robot_processor.app:broker robot_processor.app
  port: 3000
  memory: 1G
  num_instances: 2
  healthcheck: 'http://localhost:5005/health'
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/path: "/metrics/"
    prometheus.io/port: "9191"

worker.retries-queue:
  cmd: dramatiq --queues job-retries --processes 1 --threads 8 robot_processor.app:broker robot_processor.app
  port: 3000
  memory: 1G
  num_instances: 2
  healthcheck: 'http://localhost:5005/health'
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/path: "/metrics/"
    prometheus.io/port: "9191"

worker.background-task:
  cmd: dramatiq --queues background-task --processes 1 --threads 8 robot_processor.app:broker robot_processor.app
  port: 3000
  memory: 1G
  num_instances: 2
  healthcheck: 'http://localhost:5005/health'
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/path: "/metrics/"
    prometheus.io/port: "9191"


worker.kafka:
  cmd: flask kafka
  memory: 512MB
  num_instances: 1
  healthcheck: 'http://localhost:5005/health'
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/path: "/metrics/"
    prometheus.io/port: "9191"

worker.binlog-job-consumer:  # 消费 job binlog，做一些 job 相关的事情
  cmd: flask job-binlog start-consumer
  memory: 256MB
  num_instances: 1
  healthcheck: 'http://localhost:5005/health'

worker.bo-binlog-consumer:
  cmd: flask bo-binlog start-consumer
  memory: 256MB
  num_instances: 1
  healthcheck: 'http://localhost:5005/health'

worker.user_task:
  cmd: flask start-user-task-worker
  memory: 256MB
  num_instances: 1
  healthcheck: 'http://localhost:5005/health'
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/path: "/metrics/"
    prometheus.io/port: "9191"


worker.tcron-jobs:
  cmd: flask tcron-cron
  healthcheck: 'http://localhost:5005/health'


worker.tmall-rate:
  cmd: flask tmall-rate
  healthcheck: 'http://localhost:5005/health'


worker.customize-redmine-635910:
  cmd: flask customize-redmine-635910
  healthcheck: 'http://localhost:5005/health'


worker.taobao-sms-callback:
  cmd: flask taobao-sms-callback
  healthcheck: 'http://localhost:5005/health'


worker.web-grpc:
  # 同时以 gRPC 和 HTTP 两种协议提供 robot-processor 的 API
  # 这些 API 主要供前端访问，也可以在内网以 gRPC 方式访问
  cmd: flask start-web-grpc
  healthcheck: 'http://localhost:5005/health'


worker.kafka-trade-event:
    cmd: flask kafka-trade-event
    healthcheck: 'http://localhost:5005/health'

web.tax-bureau-proxy:
  cmd: gunicorn -c tax_bureau_proxy/gunicorn-server.py -b 0.0.0.0:8000 --limit-request-line 8190 tax_bureau_proxy:app
  port: 8000
  memory: 512MB
  num_instances: 1
  healthcheck: 'http://localhost:5005/health'
