aiokafka==0.7.2           # via leyan-kafka
alembic==1.10.2           # via flask-migrate
alibabacloud_dysmsapi20170525==3.1.2
aliyun-python-sdk-core==2.13.36  # via aliyun-python-sdk-kms, oss2
aliyun-python-sdk-kms==2.16.0  # via oss2
aliyun-python-sdk-address-purification==1.0.1 # via aliyun-address-purification
apscheduler==3.10.1       # via -r requirements.in
arrow==1.2.3              # via robot-extension
avro-python3==1.10.0      # via confluent-kafka, robot-extension
beartype==0.16.4          # via -r requirements.in
blinker==1.7.0            # via leyan-kafka
bottle==0.12.25           # via common-libs
cachetools==5.3.0         # via google-auth
cattrs==24.1.2           # via -r requirements.in
certifi==2022.12.7        # via requests, sentry-sdk
cffi==1.15.1              # via cryptography
charset-normalizer==3.1.0  # via requests
chevron==0.14.0
click==8.1.3              # via common-libs, flask
common-libs==0.0.49.post0+6.gc8d7502       # via -r requirements.in, leyan-proto, tcron-jobs
confluent-kafka[avro]==1.9.2  # leyan-kafka
cpca==0.5.5               # via -r requirements.in
crcmod==1.7               # via oss2
cryptography==39.0.2      # via aliyun-python-sdk-core
dacite==1.8.0             # via lepollo
deprecated==1.2.13        # via limits
deprecation==2.1.0        # via -r requirements.in
diskcache==5.6.3          # via -r requirements.in
dpath==2.1.4              # via -r requirements.in
dramatiq==1.11.0          # via -r requirements.in, sentry-dramatiq
et-xmlfile==1.1.0         # via openpyxl
exceptiongroup==1.1.1     # via cattrs
fastavro==1.8.4           # via confluent-kafka
flask==2.2.5              # via -r requirements.in, flask-caching, flask-migrate, flask-sqlalchemy
flask-caching==1.10.1     # via -r requirements.in
flask-migrate==3.1.0      # via -r requirements.in
flask-sqlalchemy==3.1.1   # via -r requirements.in, flask-migrate
google-api-core==2.8.0    # via leyan-proto-generated
google-auth==2.16.2       # via google-api-core
googleapis-common-protos==1.56.0  # via google-api-core
greenlet==1.1.3.post0     # via sqlalchemy
grpc-interceptor==0.15.0  # via -r requirements.in
grpc-stubs==1.24.8        # via leyan-proto-generated
grpcio==1.43.0            # via common-libs, grpc-interceptor, grpc-stubs, leyan-proto-generated
gunicorn==20.1.0          # via -r requirements.in
hiredis==2.2.3            # via -r requirements.in
idna==3.4                 # via requests
importlib-metadata==6.0.0  # via alembic, flask
importlib-resources==5.12.0  # via alembic
itsdangerous==2.1.2       # via flask
jaeger-client==4.8.0      # via common-libs
javaproperties==0.8.1     # via lepollo
jinja2==3.1.2             # via flask
jmespath==0.10.0          # via aliyun-python-sdk-core
kafka-python==2.0.2       # via aiokafka
lepollo==1.1.2            # via common-libs
leyan-avro==30.13.5       # via -r requirements.in
leyan-kafka==0.5.7        # via -r requirements.in
leyan-proto[grpc-gateway]==1.42.19  # via -r requirements.in, tcron-jobs
limits==2.6.2             # via -r requirements.in
loguru==0.6.0             # via -r requirements.in
mako==1.2.4               # via alembic
markupsafe==2.1.2         # via jinja2, mako, werkzeug
memory-profiler==0.61.0   # via -r requirements.in
more-itertools==9.1.0     # via robot-extension
mq-http-sdk==1.0.3        # via -r requirements.in
numpy==1.24.2             # via pandas
objectpath==0.6.1         # via -r requirements.in
openpyxl==3.1.2           # via -r requirements.in
opentracing==2.4.0        # via common-libs, jaeger-client, requests-opentracing
oss2==2.14.0              # via -r requirements.in
packaging==21.3           # via deprecation, limits
pandas==2.0.3             # via cpca
phonenumbers==8.13.7      # via -r requirements.in
pillow==10.0.1            # via -r requirements.in
prometheus-client==0.16.0  # via dramatiq
protobuf==3.12.2          # via common-libs, google-api-core, googleapis-common-protos, leyan-proto-generated
protobuf-cpp-extension==3.12.2; sys_platform == "linux"  # 参考: https://git.leyantech.com/coe/oncall/-/issues/6273 因为只为 linux 构建了 extension，所以声明了 sys_platform 限定条件
psutil==5.9.4             # via memory-profiler
pyahocorasick==2.0.0      # via cpca
pyasn1==0.4.8             # via pyasn1-modules, rsa
pyasn1-modules==0.2.8     # via google-auth
pycparser==2.21           # via cffi
pycryptodome==3.17        # via oss2
pydantic==1.10.13         # via -r requirements.in, robot-extension,
pyjwt==2.1.0              # via -r requirements.in
pymysql==1.0.2            # via -r requirements.in
pyparsing==3.0.9          # via packaging
python-consul==1.1.0      # via common-libs
python-dateutil==2.8.2    # via arrow, lepollo, pandas
python-dotenv==0.15.0     # via -r requirements.in
python-snappy==0.6.1      # via leyan-kafka
pytimeparse==1.1.8        # via lepollo
pytz==2022.7.1            # via apscheduler, pandas
pyyaml==6.0.1
ratelimiter==1.2.0.post1  # via lepollo
redis==3.5.3              # via -r requirements.in
requests==2.28.2          # via confluent-kafka, google-api-core, lepollo, leyan-kafka, oss2, python-consul, requests-opentracing
requests-opentracing==0.3.0  # via -r requirements.in
result==0.15.0            # via -r requirements.in
robot-extension==0.1.35   # via -r requirements.in
robot-types==0.2.74
rsa==4.9                  # via google-auth
semantic-version==2.10.0  # via -r requirements.in
sentry-dramatiq==0.3.2    # via -r requirements.in
sentry-sdk==1.16.0        # via sentry-dramatiq
six==1.16.0               # via apscheduler, common-libs, google-auth, grpcio, oss2, protobuf, python-consul, python-dateutil, thrift
sqlalchemy==2.0.23        # via -r requirements.in, alembic, flask-sqlalchemy, sqlalchemy-utils
sqlalchemy-utils==0.41.1  # via -r requirements.in
sqlparse==0.4.4           # via -r requirements.in
statsd==4.0.1             # via common-libs
tcron-jobs==0.0.3         # via -r requirements.in
threadloop==1.0.2         # via jaeger-client
thrift==0.16.0            # via jaeger-client
tornado==6.2              # via jaeger-client, threadloop
types-futures==3.3.8      # via types-protobuf
types-protobuf==3.19.10   # via leyan-proto-generated
typing-extensions==4.7.0  # via alembic, lepollo, limits, pydantic
tzlocal==5.2              # via apscheduler
urllib3==1.26.15          # via requests, sentry-sdk
werkzeug==3.0.1           # via flask
wrapt==1.15.0             # via deprecated
xlsxwriter==3.1.9         # via -r requirements.in
zipp==3.15.0              # via importlib-metadata, importlib-resources
gmssl==3.2.2              # via -r requirements.in
clickhouse-connect==0.7.2
emoji==2.12.1
tenacity==8.5.0
psycopg2-binary==2.9.9
redmail==0.6.0
matplotlib==3.9.2
pebble==5.1.0
