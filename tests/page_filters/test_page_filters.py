from robot_processor.business_order.page_filters.models import PageFilter
from robot_processor.enums import NodeType
from robot_processor.ext import db


def test_get_page_filters(client):
    resp = client.get("/v1/pages/filters")
    assert resp.status_code == 200
    assert resp.json.get("filters") == []

    page_filter = PageFilter(
        user_id=client.assistant.user_id,
        name="test",
        org_id=client.shop.org_id,
        node=NodeType.ALL_TASK,
        condition={},
    )
    db.session.add(page_filter)
    db.session.commit()

    resp = client.get("/v1/pages/filters")

    assert resp.status_code == 200
    assert len(resp.json.get("filters")) == 1

    resp = client.get("/v1/pages/filters?node={}".format(NodeType.PAY_CHECK))

    assert resp.status_code == 200
    assert len(resp.json.get("filters")) == 0


def test_post_page_filters(client):
    resp = client.post("/v1/pages/filters", json={
        "name": "test",
        "node": NodeType.DATA_STATISTIC_ALL,
        "condition": {}
    })

    assert resp.status_code == 201

    pf = PageFilter.query.filter(PageFilter.name == "test").first()
    assert pf.node == NodeType.DATA_STATISTIC_ALL

    resp = client.get("/v1/pages/filters?node={}".format(NodeType.DATA_STATISTIC_ALL))

    assert resp.status_code == 200
    assert len(resp.json.get("filters")) == 1


def test_delete_page_filters(client):
    resp = client.post("/v1/pages/filters", json={
        "name": "test",
        "node": NodeType.DATA_STATISTIC_ALL,
        "condition": {}
    })
    assert resp.status_code == 201
    page_filter_id = resp.json.get("id")

    resp = client.delete("/v1/pages/filters/{}".format(page_filter_id))
    assert resp.status_code == 200

    resp = client.get("/v1/pages/filters?node={}".format(NodeType.DATA_STATISTIC_ALL))
    assert resp.status_code == 200
    assert len(resp.json.get("filters")) == 0
