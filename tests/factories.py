import time
from typing import Generic
from typing import Optional
from typing import <PERSON><PERSON><PERSON>
from typing import Union

import factory
from factory import alchemy
from factory import fuzzy
from pytest import fixture

from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import BusinessOrderTradeMap
from robot_processor.business_order.models import Job
from robot_processor.business_order.models import JobApprover
from robot_processor.business_order.models import JobExecuteCron
from robot_processor.business_order.models import JobPool
from robot_processor.business_order.models import JobTask
from robot_processor.buyer_table.models import BuyerTable
from robot_processor.buyer_table.models import ServiceBuffet
from robot_processor.enums import AssigneeRule
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import SelectType
from robot_processor.enums import StepType
from robot_processor.enums import WidgetValueUniqueCheckType
from robot_processor.ext import db
from robot_processor.form.models import Form
from robot_processor.form.models import FormShop
from robot_processor.form.models import FormTemplate
from robot_processor.form.models import FormVersion
from robot_processor.form.models import Operator
from robot_processor.form.models import Step
from robot_processor.form.models import StepRetry
from robot_processor.form.models import StepSkip
from robot_processor.form.models import StepTemplate
from robot_processor.form.models import Widget
from robot_processor.form.models import WidgetCollection
from robot_processor.form.models import WidgetInfo
from robot_processor.knowledge.models import Document
from robot_processor.knowledge.models import KnowledgeBase
from robot_processor.knowledge.models import KnowledgeBaseRole
from robot_processor.knowledge.models import KnowledgeBaseRoleToStaffMapping
from robot_processor.knowledge.models import KnowledgeBaseToShopMapping
from robot_processor.knowledge.models import Project
from robot_processor.notify.models import Message
from robot_processor.rpa_service.models import Rpa
from robot_processor.rpa_service.models import RpaContext
from robot_processor.shop.kiosk_models import KioskOrg
from robot_processor.shop.models import ContractInfo
from robot_processor.shop.models import ErpInfo
from robot_processor.shop.models import GrantRecord
from robot_processor.shop.models import MiniAppTemplate
from robot_processor.shop.models import Shop

T = TypeVar("T")


class FactoryMixin(alchemy.SQLAlchemyModelFactory, Generic[T]):
    class Meta:
        sqlalchemy_session = db.session
        sqlalchemy_session_persistence = "commit"

    @classmethod
    def create(cls, **kwargs) -> T:
        return super().create(**kwargs)


class ServiceBuffetFactory(FactoryMixin[ServiceBuffet]):
    class Meta:
        model = ServiceBuffet

    org_id = fuzzy.FuzzyInteger(1000, 10000)
    sid = fuzzy.FuzzyText()


@fixture
def service_buffet_factory():
    return ServiceBuffetFactory


@fixture
def mock_service_buffet() -> ServiceBuffet:
    return ServiceBuffetFactory.create()


class BuyerTableFactory(FactoryMixin[BuyerTable]):
    class Meta:
        model = BuyerTable

    service_buffet_id = fuzzy.FuzzyInteger(1000, 10000)
    name = fuzzy.FuzzyText()
    form_id = fuzzy.FuzzyInteger(1000, 10000)


@fixture
def buyer_table_factory():
    return BuyerTableFactory


@fixture
def mock_buyer_table() -> BuyerTable:
    return BuyerTableFactory.create()


class StepFactory(FactoryMixin[Step]):
    class Meta:
        model = Step

    name = fuzzy.FuzzyText()
    description = fuzzy.FuzzyText()
    step_uuid = fuzzy.FuzzyText()
    prev_step_ids = []
    next_step_ids = []
    is_dirty = False


@fixture
def step_factory():
    return StepFactory


@fixture
def mock_step():
    return StepFactory.create()


class StepSkipFactory(FactoryMixin[StepSkip]):
    class Meta:
        model = StepSkip

    step_uuid = fuzzy.FuzzyText()
    can_skip = False
    update_user = fuzzy.FuzzyText()
    deleted = False


@fixture
def step_skip_factory():
    return StepSkipFactory


@fixture
def mock_step_skip():
    return StepSkipFactory.create()


class StepRetryFactory(FactoryMixin[StepSkip]):
    class Meta:
        model = StepRetry

    step_uuid = fuzzy.FuzzyText()
    can_retry = False
    update_user = fuzzy.FuzzyText()
    deleted = False


@fixture
def step_retry_factory():
    return StepRetryFactory


@fixture
def mock_step_retry():
    return StepRetryFactory.create()


class FormFactory(FactoryMixin[Form]):
    class Meta:
        model = Form

    name = fuzzy.FuzzyText()
    description = fuzzy.FuzzyText()
    update_user = fuzzy.FuzzyText()
    uuid = fuzzy.FuzzyText()


@fixture
def form_factory():
    return FormFactory


class FormShopFactory(FactoryMixin[FormShop]):
    class Meta:
        model = FormShop


@fixture
def form_shop_factory():
    return FormShopFactory


@fixture
def mock_form() -> Form:
    return FormFactory.create()


@fixture
def create_simple_form(
    client,
    human_step_factory,
    form_factory,
    begin_step_factory,
    widget_info_factory,
    widget_collection_factory,
    widget,
):
    """创建仅包含第一步的表单，且第一步为人工步骤, 并绑定了指定 label 的若干 widgets, 如果通过 key-value 形式指定 widget label 和 value，则创建对应的工单."""

    def create(
        name: str,
        widget_labels: Union[list, dict],
        shop: Optional[Shop] = None,
        bo_status: BusinessOrderStatus | None = None,
    ) -> Form:
        shop = shop or client.shop
        form = form_factory.create(name=name)
        form.subscribe(shop, True)
        if not widget_labels:
            return form
        begin_step = begin_step_factory.create(
            form_id=form.id,
            widget_collection_id=widget_collection_factory.create().id,
            name="起始步骤",
        )
        wc = widget_collection_factory.create()
        human_step = human_step_factory.create(form_id=form.id, widget_collection_id=wc.id, name="测试步骤")
        begin_step.next_step_ids = [human_step.step_uuid]
        human_step.prev_step_ids = [begin_step.step_uuid]
        bo_data = {}
        for label in widget_labels:
            widget_ = widget(label)
            widget_info = widget_info_factory.create(
                widget_id=widget_.id,
                option_value={
                    "label": label,
                    "required": True,
                    "hasTopSpace": False,
                    "placeholder": "placeholder",
                    "valueUnique": False,
                    "checkType": WidgetValueUniqueCheckType.REMIND.value,
                },
                widget_collection_id=wc.id,
            )
            if isinstance(widget_labels, dict):
                bo_data[widget_info.key] = widget_labels[label]
        if bo_data:
            BusinessOrderFactory.create(form_id=form.id, sid=shop.sid, data=bo_data, status=bo_status)
        db.session.commit()
        begin_step.update_raw_step()
        human_step.update_raw_step()
        form.snapshot()
        return form

    return create


class HumanStepFactory(StepFactory):
    step_type = StepType.human
    assistants_v2 = {
        "assignee_rule": AssigneeRule.RANDOM.value,
        "select_type": SelectType.all.value,
    }


@fixture
def human_step_factory():
    return HumanStepFactory


class AutoStepFactory(StepFactory):
    step_type = StepType.auto


@fixture
def auto_step_factory():
    return AutoStepFactory


class ExclusiveStepFactory(StepFactory):
    step_type = StepType.exclusive_gateway


@fixture
def exclusive_step_factory():
    return ExclusiveStepFactory


class BeginStepFactory(StepFactory):
    step_type = StepType.begin


@fixture
def begin_step_factory():
    return BeginStepFactory


class FormVersionFactory(FactoryMixin[FormVersion]):
    class Meta:
        model = FormVersion

    version_descriptor = fuzzy.FuzzyText()


@fixture
def form_version_factory():
    return FormVersionFactory


@fixture
def mock_form_version() -> FormVersion:
    return FormVersionFactory.create()


class WidgetCollectionFactory(FactoryMixin[WidgetCollection]):
    class Meta:
        model = WidgetCollection


@fixture
def widget_collection_factory():
    return WidgetCollectionFactory


@fixture
def mock_widget_collection() -> WidgetCollection:
    return WidgetCollectionFactory.create()


class WidgetInfoFactory(FactoryMixin[WidgetInfo]):
    class Meta:
        model = WidgetInfo

    key = fuzzy.FuzzyText()
    before = False


class WidgetFactory(FactoryMixin[Widget]):
    class Meta:
        model = Widget

    label = fuzzy.FuzzyText()


@fixture
def widget_factory():
    return WidgetFactory


@fixture
def widget_info_factory():
    return WidgetInfoFactory


@fixture
def mock_widget_info() -> WidgetInfo:
    return WidgetInfoFactory.create()


class OperatorFactory(FactoryMixin[Operator]):
    class Meta:
        model = Operator


@fixture
def operator_factory():
    return OperatorFactory


class JobFactory(FactoryMixin[Job]):
    class Meta:
        model = Job

    @classmethod
    def create2(cls, step: Step, bo: BusinessOrder, **kwargs) -> Job:
        """使用现有的 step 和 business order 创建新的 job."""
        return cls.create(step_id=step.id, step_uuid=step.step_uuid, business_order_id=bo.id, **kwargs)


@fixture
def job_factory():
    return JobFactory


@fixture
def mock_job() -> Job:
    return JobFactory.create()


class JobPoolFactory(FactoryMixin[JobPool]):
    class Meta:
        model = JobPool


@fixture
def job_pool_factory():
    return JobPoolFactory


class JobApproverFactory(FactoryMixin[JobApprover]):
    class Meta:
        model = JobApprover


@fixture
def job_approver_factory():
    return JobApproverFactory


class BusinessOrderFactory(FactoryMixin[BusinessOrder]):
    class Meta:
        model = BusinessOrder

    sid = fuzzy.FuzzyText()
    aid = fuzzy.FuzzyText()
    uid = fuzzy.FuzzyText()
    form_id = fuzzy.FuzzyInteger(0, 10000)
    buyer_open_uid = fuzzy.FuzzyText()


@fixture
def business_order_factory():
    return BusinessOrderFactory


@fixture
def mock_business_order(mock_form, begin_step_factory) -> BusinessOrder:
    begin_step_factory.create(form_id=mock_form.id)
    form_version = mock_form.snapshot()
    business_order = BusinessOrderFactory.create()
    business_order.form_version_id = form_version.id
    db.session.commit()
    return business_order


@fixture
def auto_job_testbed(
    form_factory,
    begin_step_factory,
    human_step_factory,
    auto_step_factory,
    business_order_factory,
    job_factory,
    client,
):
    from robot_processor.form.form_subscribe import FormSubscribe

    form = form_factory.create()
    begin_step = begin_step_factory.create(form_id=form.id)
    start_step = human_step_factory.create(form_id=form.id)
    begin_step.next_step_ids = [start_step.step_uuid]
    start_step.prev_step_ids = [begin_step.step_uuid]

    def build(key_map: dict, *, start_step_symbols=None, bo_data=None):
        auto_step = auto_step_factory.create(form_id=form.id, key_map=key_map)
        auto_step.prev_step_ids = [start_step.step_uuid]
        start_step.next_step_ids = [auto_step.step_uuid]
        db.session.commit()
        if start_step_symbols:
            start_step.set_symbols(start_step_symbols)
        FormSubscribe(form).subscribe(client.shop, True)
        form_version = form.snapshot()
        order = business_order_factory.create(form_id=form.id, form_version_id=form_version.id, sid=client.shop.sid)
        if bo_data:
            order.data = {**order.data, **bo_data}
        order.set_current_execute_job(job_factory.create2(begin_step, order))
        order.set_current_execute_job(job_factory.create2(start_step, order))
        auto_job = job_factory.create2(auto_step, order)
        order.set_current_execute_job(auto_job)
        db.session.commit()
        return auto_job

    return build


@fixture
def human_only_form_testbed(client, form_factory, begin_step_factory, human_step_factory):
    """只有一个人工步骤的工单"""
    from robot_processor.form.form_subscribe import FormSubscribe

    form = form_factory.create()
    begin_step = begin_step_factory.create(form_id=form.id)
    human_step = human_step_factory.create(form_id=form.id)
    begin_step.next_step_ids = [human_step.step_uuid]
    human_step.prev_step_ids = [begin_step.step_uuid]

    def build(*, symbols=None, validation_config=None, shop=None, form_name=None):
        if form_name is not None:
            form.name = form_name
        if symbols:
            human_step.compatible_set_symbols(symbols, rewrite_widget_collection=True)
        if validation_config:
            human_step.validation_config = validation_config
        shop = shop or client.shop
        FormSubscribe(form).subscribe(shop, True)
        form.snapshot()
        begin_step.update_raw_step()
        human_step.update_raw_step()
        db.session.commit()
        return human_step

    return build


class BusinessOrderTradeMapFactory(FactoryMixin[BusinessOrderTradeMap]):
    class Meta:
        model = BusinessOrderTradeMap

    business_order_id = fuzzy.FuzzyInteger(0, 10000)
    tid_or_oid = fuzzy.FuzzyText()


@fixture
def mock_business_order_trade_map() -> BusinessOrderTradeMap:
    return BusinessOrderTradeMapFactory.create()


class JobTaskFactory(FactoryMixin[JobTask]):
    class Meta:
        model = JobTask


@fixture
def mock_job_task() -> JobTask:
    return JobTaskFactory.create()


class JobExecuteCronFactory(FactoryMixin[JobExecuteCron]):
    class Meta:
        model = JobExecuteCron

    expect_execute_time = fuzzy.FuzzyInteger(0, 10000)


@fixture
def job_execute_cron_factory():
    return JobExecuteCronFactory


class RpaFactory(FactoryMixin[Rpa]):
    class Meta:
        model = Rpa


@fixture
def rpa_factory():
    return RpaFactory


class RpaContextFactory(FactoryMixin[RpaContext]):
    class Meta:
        model = RpaContext


@fixture
def rpa_context_factory():
    return RpaContextFactory


class GrantRecordFactory(FactoryMixin[GrantRecord]):
    class Meta:
        model = GrantRecord

    access_token = fuzzy.FuzzyText()
    expires_at_ms = 1000 * time.time()


class MiniAppTemplateFactory(FactoryMixin[MiniAppTemplate]):
    class Meta:
        model = MiniAppTemplate

    app_id = fuzzy.FuzzyText()
    app_version = "0.0.1"


@fixture
def mini_app_template_factory():
    return MiniAppTemplateFactory


@fixture
def grant_record_factory():
    return GrantRecordFactory


@fixture
def mock_grant_record() -> GrantRecord:
    return GrantRecordFactory.create()


class ShopFactory(FactoryMixin[Shop]):
    class Meta:
        model = Shop

    title = fuzzy.FuzzyText()
    sid = fuzzy.FuzzyText()
    nick = fuzzy.FuzzyText()
    org_id = fuzzy.FuzzyInteger(1000, 10000)
    channel_id = factory.Sequence(lambda n: 1000 + n)
    platform = "TAOBAO"


@fixture
def shop_factory():
    return ShopFactory


class KioskOrgFactory(FactoryMixin[KioskOrg]):
    class Meta:
        model = KioskOrg


@fixture
def kiosk_org_factory():
    return KioskOrgFactory


class ContractInfoFactory(FactoryMixin[ContractInfo]):
    class Meta:
        model = ContractInfo

    org_id = factory.Sequence(lambda n: 100 + n)
    end_ts = int(time.time() + 10**6)


@fixture
def contract_info_factory():
    return ContractInfoFactory


class ErpInfoFactory(FactoryMixin[ErpInfo]):
    class Meta:
        model = ErpInfo

    token = fuzzy.FuzzyText()
    nick = fuzzy.FuzzyText()


@fixture
def mock_erp_info() -> ErpInfo:
    return ErpInfoFactory.create()


@fixture
def erp_info_factory():
    return ErpInfoFactory


class FormTemplateFactory(FactoryMixin[FormTemplate]):
    class Meta:
        model = FormTemplate


@fixture
def mock_form_template() -> FormTemplate:
    return FormTemplateFactory.create()


class StepTemplateFactory(FactoryMixin[StepTemplate]):
    class Meta:
        model = StepTemplate

    name = fuzzy.FuzzyText()
    description = fuzzy.FuzzyText()
    uuid = fuzzy.FuzzyText()


@fixture
def mock_step_template() -> StepTemplate:
    return StepTemplateFactory.create()


class MessageContentFactory(factory.Factory):
    class Meta:
        model = dict

    title = fuzzy.FuzzyText()
    content = fuzzy.FuzzyText()
    action = []


class NotifyMessageFactory(FactoryMixin[Message]):
    class Meta:
        model = Message

    message_channels = ["QN_PLUGIN", "CLIENT", "WEB_PLUGIN"]
    message_type = "WINDOW"
    message_kind = fuzzy.FuzzyChoice(
        [
            "SHORT_URL_EXPIRE",
            "TASK_TIMEOUT",
            "TASK_EXPIRE",
            "TASK_NOTIFIER",
            "JOB_REMIND",
            "EXCEPTION",
        ]
    )
    content = factory.SubFactory(MessageContentFactory)
    # 发送时间
    send_ts = factory.LazyFunction(lambda: int(time.time() - 10))
    # 消息产生时间
    message_ts = factory.LazyFunction(lambda: int(time.time() - 20))
    status = "UN_SEND"
    reason = fuzzy.FuzzyText(length=10)


@fixture
def notify_message_factory():
    return NotifyMessageFactory


class KnowledgeBaseFactory(FactoryMixin[KnowledgeBase]):
    class Meta:
        model = KnowledgeBase

    icon = fuzzy.FuzzyText()
    name = fuzzy.FuzzyText()
    intro = fuzzy.FuzzyText()
    org_id = str(fuzzy.FuzzyInteger(1000, 10000))
    creator_id = fuzzy.FuzzyInteger(1000, 10000)
    updator_id = fuzzy.FuzzyInteger(1000, 10000)
    deleted_at = None


@fixture
def knowledge_base_factory():
    return KnowledgeBaseFactory


class KnowledgeBaseRoleFactory(FactoryMixin[KnowledgeBaseRole]):
    class Meta:
        model = KnowledgeBaseRole

    name = fuzzy.FuzzyText()
    creator_id = fuzzy.FuzzyInteger(1000, 10000)
    updator_id = fuzzy.FuzzyInteger(1000, 10000)
    deleted_at = None
    has_limited_knowledge_base: False


@fixture
def knowledge_base_role_factory():
    return KnowledgeBaseRoleFactory


class KnowledgeBaseRoleToStaffMappingFactory(FactoryMixin[KnowledgeBaseRoleFactory]):
    class Meta:
        model = KnowledgeBaseRoleToStaffMapping


@fixture
def knowledge_base_role_to_staff_mapping_factory():
    return KnowledgeBaseRoleToStaffMappingFactory


class KnowledgeBaseToShopMappingFactory(FactoryMixin[KnowledgeBaseToShopMapping]):
    class Meta:
        model = KnowledgeBaseToShopMapping


@fixture
def knowledge_base_to_shop_mapping_factory():
    return KnowledgeBaseToShopMappingFactory


class ProjectFactory(FactoryMixin[Project]):
    class Meta:
        model = Project

    name = fuzzy.FuzzyText()
    org_id = str(fuzzy.FuzzyInteger(1000, 10000))
    parent_id = 0
    prev_id = 0
    next_id = 0
    creator_id = fuzzy.FuzzyInteger(1000, 10000)
    deleted_at = None


@fixture
def project_factory():
    return ProjectFactory


class DocumentFactory(FactoryMixin[Document]):
    class Meta:
        model = Document

    intro = fuzzy.FuzzyText()
    preview = fuzzy.FuzzyText()
    content = dict()


@fixture
def document_factory():
    return DocumentFactory
