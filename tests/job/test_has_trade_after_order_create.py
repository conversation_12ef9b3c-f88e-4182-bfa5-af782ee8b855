import arrow
import pytest as pytest
from dramatiq import Retry
from leyan_proto.digismart.buyer_server.buyer_server_rpc_pb2 import Get<PERSON><PERSON>anBuyerIdByBuyerNickResponse
from leyan_proto.digismart.dgt_common_pb2 import DgtResponseCode

from robot_processor.enums import JobStatus
from robot_processor.ext import db
from robot_processor.job.has_trade_after_order_create import HasTradeAfterOrderCreateExecutor
from robot_processor.shop.models import Grant<PERSON><PERSON>ord


def test_has_trade(mocker, client, mock_full_job, mock_job_args, mock_job_states):
    mock_job_args(
        usernick="usernick",
    )
    mocker.patch(
        "robot_processor.client.buyer.BuyerClient.get_buyer_info_by_buyer_nick",
        return_value=GetLeyanBuyerIdByBuyerNickResponse(code=DgtResponseCode.OK, buyer_open_uid="123"),
    )
    mocker.patch("robot_processor.client.trade.TradeClient._get_trades_by_buyer", return_value=([], {}))
    executor = HasTradeAfterOrderCreateExecutor(mock_full_job)
    grant_record = GrantRecord(shop_id=executor.shop.id, access_token="token")
    db.session.add(grant_record)
    db.session.commit()
    with pytest.raises(Retry):
        executor.process()

    mocker.patch(
        "robot_processor.client.trade.TradeClient.get_trades_by_buyer",
        return_value={"trades": [{"trade_id": "123", "created_at": arrow.now().strftime("%Y-%m-%d %H:%M:%S")}]},
    )

    status, _ = executor.process()
    assert status == JobStatus.SUCCEED
