from pytest import fixture
from result import Ok

from robot_processor.client.rpa_client.schema import Response, ExecuteWorkflowRes
from robot_processor.enums import BusinessOrderStatus, JobStatus
from robot_processor.job.rpa_wdtulti_get_invoice import RpaWDTUltiGetInvoiceExecutor


RPA_RESPONSE = {
    'success': True,
    'error_msg': '',
    'suggestion': '任务完成',
    'meta': {
        'workflow_id': 73,
        'execution_id': 3442
    },
    'output': """[
        {
            "label": "发票信息",
            "name": "order",
            "object": [
                {
                    "detail_list": [
                        {
                            "单位名称": "Pcs",
                            "原始单号": "291534503932",
                            "原始子单号": "291534503932:10083288606099",
                            "商品名称": "科颜萃双抗精华改善面部暗沉减黄提亮部抗皱精华液 15ml【赠2ml】",
                            "商品规格": "科颜萃双抗精华改善面部暗沉减黄提亮部抗皱精华液 15ml【赠2ml】",
                            "备注": "",
                            "总价": "49.0000",
                            "折扣总价": "0.0000",
                            "折扣税额": "0.0000",
                            "数量": "1",
                            "税前单价": "43.360000",
                            "税前总价": "43.3600",
                            "税前折扣金额": "0.0000",
                            "税务编码": "1070223040000000000",
                            "税率": "0.1300",
                            "税额": "5.6400",
                            "类别": "",
                            "订单编号": "JY202404154934"
                        }
                    ],
                    "创建人": "系统用户",
                    "创建时间": "2024-04-18 12:45:15",
                    "原始单号": "291534503932",
                    "发票下载地址": "",
                    "发票下载地址ODF": "",
                    "发票下载地址XML": "",
                    "发票代码": "",
                    "发票号码": "",
                    "发票打印备注": "",
                    "发票抬头": "个人",
                    "发票状态": "开票申请失败",
                    "发票种类": "数电票(全电票)",
                    "发票类型": "增值税普通发票",
                    "发票编号": "FP202404180009",
                    "地址信息": "",
                    "复核人": "",
                    "实际合计税额": "5.6400",
                    "实际合计金额": "0.0000",
                    "客户网民": "jd_5bb0**3581f6c",
                    "店铺": "京东-科颜萃旗舰店",
                    "开户银行": "",
                    "开票人": "管理员",
                    "开票方": "宁波科颜萃电子商务有限公司",
                    "开票方备注": "",
                    "开票日期": "",
                    "开票服务商名称": "百望数电票",
                    "开票类型": "个人",
                    "总金额": "49.0000",
                    "折扣税额": "0.0000",
                    "收款人": "",
                    "收款方纳税人识别号": "",
                    "电子邮箱": "",
                    "确认人": "系统用户",
                    "税前折扣金额": "0.0000",
                    "税控盘号": "",
                    "红蓝票": "蓝票",
                    "联系电话": "",
                    "订单编号": "JY202404154934",
                    "货品合计金额": "43.3600",
                    "邮件发送状态": "未发送",
                    "银行账号": "",
                    "错误信息": "远程服务错误电局账号【***********】登录失效，请稍后再试"
                }
            ],
            "type": "object"
        }
    ]"""
}

NEED_FILTER_RPA_RESPONSE = {
    'success': True,
    'error_msg': '',
    'suggestion': '任务完成',
    'meta': {
        'workflow_id': 73,
        'execution_id': 3442
    },
    'output': """
        [
          {
            "label": "发票信息",
            "name": "order",
            "object": [
              {
                "detail_list": [
                  {
                    "单位名称": "Pcs",
                    "原始单号": "3810709875987335239",
                    "原始子单号": "3810709875987335239",
                    "商品名称": "【二代升级】泊本6D玻尿酸水乳套装补水保湿舒缓修护敏感肌护肤",
                    "商品规格": "水+乳",
                    "备注": "",
                    "总价": "74.5000",
                    "折扣总价": "0.0000",
                    "折扣税额": "0.0000",
                    "数量": "1",
                    "税前单价": "65.920000",
                    "税前总价": "65.9200",
                    "税前折扣金额": "0.0000",
                    "税务编码": "1070223040000000000",
                    "税率": "0.1300",
                    "税额": "8.5800",
                    "类别": "",
                    "订单编号": "JY202403123827"
                  },
                  {
                    "单位名称": "Pcs",
                    "原始单号": "3810709875987335239",
                    "原始子单号": "3810709875987335239",
                    "商品名称": "【二代升级】泊本6D玻尿酸水乳套装补水保湿舒缓修护敏感肌护肤",
                    "商品规格": "水+乳",
                    "备注": "",
                    "总价": "74.5000",
                    "折扣总价": "0.0000",
                    "折扣税额": "0.0000",
                    "数量": "1",
                    "税前单价": "65.920000",
                    "税前总价": "65.9200",
                    "税前折扣金额": "0.0000",
                    "税务编码": "1070223040000000000",
                    "税率": "0.1300",
                    "税额": "8.5800",
                    "类别": "",
                    "订单编号": "JY202403123827"
                  },
                  {
                    "单位名称": "Pcs",
                    "原始单号": "3810709875987335239",
                    "原始子单号": "AD202403120560",
                    "商品名称": "泊本透明质酸钠补水保湿面膜25ml",
                    "商品规格": "泊本透明质酸钠补水保湿面膜25ml",
                    "备注": "",
                    "总价": "0.0100",
                    "折扣总价": "0.0100",
                    "折扣税额": "0.0000",
                    "数量": "2",
                    "税前单价": "0.005000",
                    "税前总价": "0.0100",
                    "税前折扣金额": "0.0100",
                    "税务编码": "1070223040000000000",
                    "税率": "0.1300",
                    "税额": "0.0000",
                    "类别": "赠品",
                    "订单编号": "JY202403123827"
                  }
                ],
                "创建人": "周春燕",
                "创建时间": "2024-04-24 13:43:38",
                "原始单号": "3810709875987335239",
                "发票下载地址": "http://fp.baiwang.com/format/d?d=FD372566199843F9D10D2AC6F3EA4AE16128D9DEB43D4C4E3764D7E34DEEC86A",
                "发票下载地址ODF": "http://fp.baiwang.com/format/d?d=FD372566199843F9D10D2AC6F3EA4AE18717BAB0FE39D336D8C1C53F160216F5",
                "发票下载地址XML": "http://fp.baiwang.com/format/d?d=FD372566199843F9D10D2AC6F3EA4AE1E8CBC3B48DCFDD25317FF4EDE8BD1F99",
                "发票代码": "",
                "发票号码": "24932000000026833241",
                "发票打印备注": "",
                "发票抬头": "江苏中核华瑞建设有限公司",
                "发票状态": "开票成功",
                "发票种类": "数电票(全电票)",
                "发票类型": "增值税普通发票",
                "发票编号": "FP202404240012",
                "地址信息": "",
                "复核人": "",
                "实际合计税额": "17.1600",
                "实际合计金额": "0.0000",
                "客户网民": "AAHdMHhSABB**gh9Ixx0w4QB",
                "店铺": "天猫-泊本旗舰店",
                "开户银行": "",
                "开票人": "管理员",
                "开票方": "宁波伯通伟达品牌管理有限公司",
                "开票方备注": "",
                "开票日期": "2024-04-24 15:05:56",
                "开票服务商名称": "百望数电票",
                "开票类型": "企业",
                "总金额": "149.0000",
                "折扣税额": "0.0000",
                "收款人": "",
                "收款方纳税人识别号": "91320211581066381X",
                "电子邮箱": "",
                "确认人": "周春燕",
                "税前折扣金额": "0.0100",
                "税控盘号": "",
                "红蓝票": "蓝票",
                "联系电话": "",
                "订单编号": "JY202403123827",
                "货品合计金额": "131.8400",
                "邮件发送状态": "未发送",
                "银行账号": "",
                "错误信息": "",
                "首条操作明细": ""
              },
              {
                "detail_list": [
                  {
                    "单位名称": "Pcs",
                    "原始单号": "3810709875987335239",
                    "原始子单号": "3810709875987335239",
                    "商品名称": "【二代升级】泊本6D玻尿酸水乳套装补水保湿舒缓修护敏感肌护肤",
                    "商品规格": "水+乳",
                    "备注": "",
                    "总价": "74.5000",
                    "折扣总价": "0.0000",
                    "折扣税额": "0.0000",
                    "数量": "1",
                    "税前单价": "65.920000",
                    "税前总价": "65.9200",
                    "税前折扣金额": "0.0000",
                    "税务编码": "1070223040000000000",
                    "税率": "0.1300",
                    "税额": "8.5800",
                    "类别": "",
                    "订单编号": "JY202403123827"
                  },
                  {
                    "单位名称": "Pcs",
                    "原始单号": "3810709875987335239",
                    "原始子单号": "3810709875987335239",
                    "商品名称": "【二代升级】泊本6D玻尿酸水乳套装补水保湿舒缓修护敏感肌护肤",
                    "商品规格": "水+乳",
                    "备注": "",
                    "总价": "74.5000",
                    "折扣总价": "0.0000",
                    "折扣税额": "0.0000",
                    "数量": "1",
                    "税前单价": "65.920000",
                    "税前总价": "65.9200",
                    "税前折扣金额": "0.0000",
                    "税务编码": "1070223040000000000",
                    "税率": "0.1300",
                    "税额": "8.5800",
                    "类别": "",
                    "订单编号": "JY202403123827"
                  },
                  {
                    "单位名称": "Pcs",
                    "原始单号": "3810709875987335239",
                    "原始子单号": "AD202403120560",
                    "商品名称": "泊本透明质酸钠补水保湿面膜25ml",
                    "商品规格": "泊本透明质酸钠补水保湿面膜25ml",
                    "备注": "",
                    "总价": "0.0100",
                    "折扣总价": "0.0100",
                    "折扣税额": "0.0000",
                    "数量": "2",
                    "税前单价": "0.005000",
                    "税前总价": "0.0100",
                    "税前折扣金额": "0.0100",
                    "税务编码": "1070223040000000000",
                    "税率": "0.1300",
                    "税额": "0.0000",
                    "类别": "赠品",
                    "订单编号": "JY202403123827"
                  }
                ],
                "创建人": "周春燕",
                "创建时间": "2024-04-24 13:43:16",
                "原始单号": "3810709875987335239",
                "发票下载地址": "http://fp.baiwang.com/format/d?d=CDF0D9C879DCD0972F34FE7E72B615AED2FC3C408BF87A327451969B90A1EBB7",
                "发票下载地址ODF": "http://fp.baiwang.com/format/d?d=CDF0D9C879DCD0972F34FE7E72B615AE5C8A8D3B6CA0E195D399FF55A0575024",
                "发票下载地址XML": "http://fp.baiwang.com/format/d?d=CDF0D9C879DCD0972F34FE7E72B615AE721BF86DBC7440505607C8859E15F7FF",
                "发票代码": "",
                "发票号码": "24932000000026823225",
                "发票打印备注": "",
                "发票抬头": "江苏中核华瑞建设有限公司",
                "发票状态": "开票成功",
                "发票种类": "数电票(全电票)",
                "发票类型": "增值税普通发票",
                "发票编号": "FP202404240011",
                "地址信息": "",
                "复核人": "",
                "实际合计税额": "17.1600",
                "实际合计金额": "0.0000",
                "客户网民": "AAHdMHhSABB**gh9Ixx0w4QB",
                "店铺": "天猫-泊本旗舰店",
                "开户银行": "",
                "开票人": "管理员",
                "开票方": "宁波伯通伟达品牌管理有限公司",
                "开票方备注": "",
                "开票日期": "2024-04-24 15:05:50",
                "开票服务商名称": "百望数电票",
                "开票类型": "企业",
                "总金额": "149.0000",
                "折扣税额": "0.0000",
                "收款人": "",
                "收款方纳税人识别号": "91320211581066381X",
                "电子邮箱": "",
                "确认人": "周春燕",
                "税前折扣金额": "0.0100",
                "税控盘号": "",
                "红蓝票": "蓝票",
                "联系电话": "",
                "订单编号": "JY202403123827",
                "货品合计金额": "131.8400",
                "邮件发送状态": "未发送",
                "银行账号": "",
                "错误信息": "",
                "首条操作明细": " 手工冲红发票: FP202404240011 "
              },
              {
                "detail_list": [
                  {
                    "单位名称": "Pcs",
                    "原始单号": "3810709875987335239",
                    "原始子单号": "3810709875987335239",
                    "商品名称": "【二代升级】泊本6D玻尿酸水乳套装补水保湿舒缓修护敏感肌护肤",
                    "商品规格": "水+乳",
                    "备注": "",
                    "总价": "-84.5000",
                    "折扣总价": "0.0000",
                    "折扣税额": "0.0000",
                    "数量": "-1",
                    "税前单价": "74.770000",
                    "税前总价": "-74.7700",
                    "税前折扣金额": "0.0000",
                    "税务编码": "1070223040000000000",
                    "税率": "0.1300",
                    "税额": "-9.7300",
                    "类别": "",
                    "订单编号": "JY202403123827"
                  },
                  {
                    "单位名称": "Pcs",
                    "原始单号": "3810709875987335239",
                    "原始子单号": "3810709875987335239",
                    "商品名称": "【二代升级】泊本6D玻尿酸水乳套装补水保湿舒缓修护敏感肌护肤",
                    "商品规格": "水+乳",
                    "备注": "",
                    "总价": "-84.5000",
                    "折扣总价": "0.0000",
                    "折扣税额": "0.0000",
                    "数量": "-1",
                    "税前单价": "74.770000",
                    "税前总价": "-74.7700",
                    "税前折扣金额": "0.0000",
                    "税务编码": "1070223040000000000",
                    "税率": "0.1300",
                    "税额": "-9.7300",
                    "类别": "",
                    "订单编号": "JY202403123827"
                  }
                ],
                "创建人": "系统用户",
                "创建时间": "2024-04-20 18:20:15",
                "原始单号": "3810709875987335239",
                "发票下载地址": "",
                "发票下载地址ODF": "",
                "发票下载地址XML": "",
                "发票代码": "",
                "发票号码": "",
                "发票打印备注": "",
                "发票抬头": "江苏中核华瑞建设有限公司",
                "发票状态": "待审核",
                "发票种类": "数电票(全电票)",
                "发票类型": "增值税普通发票",
                "发票编号": "FP202404200011",
                "地址信息": "",
                "复核人": "",
                "实际合计税额": "-19.4600",
                "实际合计金额": "0.0000",
                "客户网民": "AAHdMHhSABB**gh9Ixx0w4QB",
                "店铺": "天猫-泊本旗舰店",
                "开户银行": "",
                "开票人": "管理员",
                "开票方": "宁波伯通伟达品牌管理有限公司",
                "开票方备注": "发票冲红",
                "开票日期": "",
                "开票服务商名称": "百望数电票",
                "开票类型": "企业",
                "总金额": "-169.0000",
                "折扣税额": "0.0000",
                "收款人": "",
                "收款方纳税人识别号": "91320211581066381X",
                "电子邮箱": "",
                "确认人": "系统用户",
                "税前折扣金额": "0.0000",
                "税控盘号": "",
                "红蓝票": "红票",
                "联系电话": "",
                "订单编号": "JY202403123827",
                "货品合计金额": "-149.5400",
                "邮件发送状态": "未发送",
                "银行账号": "",
                "错误信息": "",
                "首条操作明细": " 冲红发票: FP202404200011 "
              },
              {
                "detail_list": [
                  {
                    "单位名称": "Pcs",
                    "原始单号": "3810709875987335239",
                    "原始子单号": "3810709875987335239",
                    "商品名称": "【二代升级】泊本6D玻尿酸水乳套装补水保湿舒缓修护敏感肌护肤",
                    "商品规格": "水+乳",
                    "备注": "",
                    "总价": "-84.5000",
                    "折扣总价": "0.0000",
                    "折扣税额": "0.0000",
                    "数量": "-1",
                    "税前单价": "74.770000",
                    "税前总价": "-74.7700",
                    "税前折扣金额": "0.0000",
                    "税务编码": "1070223040000000000",
                    "税率": "0.1300",
                    "税额": "-9.7300",
                    "类别": "",
                    "订单编号": "JY202403123827"
                  },
                  {
                    "单位名称": "Pcs",
                    "原始单号": "3810709875987335239",
                    "原始子单号": "3810709875987335239",
                    "商品名称": "【二代升级】泊本6D玻尿酸水乳套装补水保湿舒缓修护敏感肌护肤",
                    "商品规格": "水+乳",
                    "备注": "",
                    "总价": "-84.5000",
                    "折扣总价": "0.0000",
                    "折扣税额": "0.0000",
                    "数量": "-1",
                    "税前单价": "74.770000",
                    "税前总价": "-74.7700",
                    "税前折扣金额": "0.0000",
                    "税务编码": "1070223040000000000",
                    "税率": "0.1300",
                    "税额": "-9.7300",
                    "类别": "",
                    "订单编号": "JY202403123827"
                  }
                ],
                "创建人": "系统用户",
                "创建时间": "2024-04-20 18:20:15",
                "原始单号": "3810709875987335239",
                "发票下载地址": "",
                "发票下载地址ODF": "",
                "发票下载地址XML": "",
                "发票代码": "",
                "发票号码": "",
                "发票打印备注": "",
                "发票抬头": "江苏中核华瑞建设有限公司",
                "发票状态": "待审核",
                "发票种类": "数电票(全电票)",
                "发票类型": "增值税普通发票",
                "发票编号": "FP202404200010",
                "地址信息": "",
                "复核人": "",
                "实际合计税额": "-19.4600",
                "实际合计金额": "0.0000",
                "客户网民": "AAHdMHhSABB**gh9Ixx0w4QB",
                "店铺": "天猫-泊本旗舰店",
                "开户银行": "",
                "开票人": "管理员",
                "开票方": "宁波伯通伟达品牌管理有限公司",
                "开票方备注": "发票冲红",
                "开票日期": "",
                "开票服务商名称": "百望数电票",
                "开票类型": "企业",
                "总金额": "-169.0000",
                "折扣税额": "0.0000",
                "收款人": "",
                "收款方纳税人识别号": "91320211581066381X",
                "电子邮箱": "",
                "确认人": "系统用户",
                "税前折扣金额": "0.0000",
                "税控盘号": "",
                "红蓝票": "红票",
                "联系电话": "",
                "订单编号": "JY202403123827",
                "货品合计金额": "-149.5400",
                "邮件发送状态": "未发送",
                "银行账号": "",
                "错误信息": "",
                "首条操作明细": " 冲红发票: FP202404200010 "
              }
            ],
            "type": "object"
          }
        ]
    """     # noqa
}


class TestRpaWDTUltiGetInvoiceExecutor:
    @fixture(autouse=True)
    def setup(self, mock_business_order, mock_job, mock_step, shop_factory):
        mock_business_order.shop = shop_factory.create()
        mock_step.next_step_ids = []
        mock_step.raw_step = {
            "name": "ut",
            "task": {
                "task_type": "WDTULTI_GET_INVOICE",
                "context": {
                    "workflow_id": "73"
                }
            }}
        mock_job.step_id = mock_step.id
        mock_job.business_order = mock_business_order
        mock_job.business_order.status = BusinessOrderStatus.INIT

    def test_request(self, mocker, client, mock_full_job, mock_job_args, mock_job_states, requests_mock):
        mock_job_args(**{
            "tid": [{'tid': '1748074083623375569'}]
        })
        executor = RpaWDTUltiGetInvoiceExecutor(mock_full_job)
        mocker.patch(
            "robot_processor.client.rpa_client.client.RpaServerClient.execute_workflow",
            return_value=Ok(Response[ExecuteWorkflowRes](
                success=True,
                data=ExecuteWorkflowRes(execution_id=3442)
            ))
        )
        status, _ = executor.process()
        assert status == JobStatus.RUNNING
        assert mock_job_states.get_job_state("execution_id") == 3442

    def test_call_back(self, mocker, mock_full_job, mock_job_states, mock_job_args):
        mock_job_args(**{
            "tid": [{'tid': '1748074083623375569'}]
        })
        executor = RpaWDTUltiGetInvoiceExecutor(mock_full_job)
        mocker.patch(
            "robot_processor.job.rpa_wdtulti_get_invoice.RpaWDTUltiGetInvoiceExecutor.download_file_to_oss",
            return_value="https://stg-locker.oss-cn-beijing.aliyuncs.com/files/1/45dbc4807c10c55a47ddc346a9a6ff44"
        )

        executor.on_success(RPA_RESPONSE)
        mock_job_states.write_job_widget_collection.assert_called_with({
            "datas": [
                {
                    "invoice_serial_no": "FP202404180009",
                    "invoice_business_type": "个人",
                    "tid": "291534503932",
                    "shop_name": "京东-科颜萃旗舰店",
                    "payer_name": "宁波科颜萃电子商务有限公司",
                    "invoice_isv_name": "百望数电票",
                    "invoice_status": "开票申请失败",
                    "invoice_category": "数电票(全电票)",
                    "invoice_kind": "增值税普通发票",
                    "red_or_blue": "蓝票",
                    "invoice_title": "个人",
                    "invoice_number": "",
                    "invoice_code": "",
                    "payee_tax_identification_number": "",
                    "wdt_order_number": "JY202404154934",
                    "customer_nick": "jd_5bb0**3581f6c",
                    "tel_number": "",
                    "payer_email": "",
                    "payer_address": "",
                    "payer_bank": "",
                    "payer_bank_account": "",
                    "drawer": "管理员",
                    "payee_name": "",
                    "reviewer": "",
                    "creator": "系统用户",
                    "confirmor": "系统用户",
                    "amount": "49.0000",
                    "goods_total_amount": "43.3600",
                    "actual_total_tax": "5.6400",
                    "before_tax_discount_amount": "0.0000",
                    "discount_tax": "0.0000",
                    "invoice_start_time": "",
                    "invoice_payer_memo": "",
                    "invoice_print_memo": "",
                    "invoice_download_url": "",
                    "invoice_odf_download_url": "",
                    "invoice_xml_download_url": "",
                    "email_send_status": "未发送",
                    "actual_total_amount": "0.0000",
                    "error_message": "远程服务错误电局账号【***********】登录失效，请稍后再试",
                    "created_time": "2024-04-18 12:45:15",
                    "detail_list": [
                        {
                            "wdt_order_number": "JY202404154934",
                            "tid": "291534503932",
                            "oid": "291534503932:10083288606099",
                            "goods_name": "科颜萃双抗精华改善面部暗沉减黄提亮部抗皱精华液 15ml【赠2ml】",
                            "goods_spec": "科颜萃双抗精华改善面部暗沉减黄提亮部抗皱精华液 15ml【赠2ml】",
                            "unit": "Pcs",
                            "qty": "1",
                            "total_amount": "49.0000",
                            "tax_rate": "0.1300",
                            "pin": "1070223040000000000",
                            "before_tax_total_price": "43.3600",
                            "before_tax_unit_price": "43.360000",
                            "tax": "5.6400", "memo": "",
                            "before_tax_discount_amount": "0.0000",
                            "discount_tax": "0.0000",
                            "discount_total_price": "0.0000",
                            "kind": ""
                        }
                    ],
                    "oss_url": "https://stg-locker.oss-cn-beijing.aliyuncs.com/files/1/45dbc4807c10c55a47ddc346a9a6ff44",  # noqa
                    "latest_operate_log": None
                }
            ]
        })

    def test_call_back_with_need_filter(self, mocker, mock_full_job, mock_job_states, mock_job_args):
        mock_job_args(**{
            "tid": [{'tid': '1748074083623375569'}]
        })
        executor = RpaWDTUltiGetInvoiceExecutor(mock_full_job)
        mocker.patch(
            "robot_processor.job.rpa_wdtulti_get_invoice.RpaWDTUltiGetInvoiceExecutor.download_file_to_oss",
            return_value="https://stg-locker.oss-cn-beijing.aliyuncs.com/files/1/45dbc4807c10c55a47ddc346a9a6ff44"
        )

        executor.on_success(NEED_FILTER_RPA_RESPONSE)
        mock_job_states.write_job_widget_collection.assert_called_with({
            "datas": [{
                'actual_total_amount': '0.0000',
                'actual_total_tax': '17.1600',
                'amount': '149.0000',
                'before_tax_discount_amount': '0.0100',
                'confirmor': '周春燕',
                'created_time': '2024-04-24 13:43:38',
                'creator': '周春燕',
                'customer_nick': 'AAHdMHhSABB**gh9Ixx0w4QB',
                'detail_list': [
                    {
                        'before_tax_discount_amount': '0.0000',
                        'before_tax_total_price': '65.9200',
                        'before_tax_unit_price': '65.920000',
                        'discount_tax': '0.0000',
                        'discount_total_price': '0.0000',
                        'goods_name': '【二代升级】泊本6D玻尿酸水乳套装补水保湿舒缓修护敏感肌护肤',
                        'goods_spec': '水+乳',
                        'kind': '',
                        'memo': '',
                        'oid': '3810709875987335239',
                        'pin': '1070223040000000000',
                        'qty': '1',
                        'tax': '8.5800',
                        'tax_rate': '0.1300',
                        'tid': '3810709875987335239',
                        'total_amount': '74.5000',
                        'unit': 'Pcs',
                        'wdt_order_number': 'JY202403123827'
                    },
                    {
                        'before_tax_discount_amount': '0.0000',
                        'before_tax_total_price': '65.9200',
                        'before_tax_unit_price': '65.920000',
                        'discount_tax': '0.0000',
                        'discount_total_price': '0.0000',
                        'goods_name': '【二代升级】泊本6D玻尿酸水乳套装补水保湿舒缓修护敏感肌护肤',
                        'goods_spec': '水+乳',
                        'kind': '',
                        'memo': '',
                        'oid': '3810709875987335239',
                        'pin': '1070223040000000000',
                        'qty': '1',
                        'tax': '8.5800',
                        'tax_rate': '0.1300',
                        'tid': '3810709875987335239',
                        'total_amount': '74.5000',
                        'unit': 'Pcs',
                        'wdt_order_number': 'JY202403123827'
                    },
                    {
                        'before_tax_discount_amount': '0.0100',
                        'before_tax_total_price': '0.0100',
                        'before_tax_unit_price': '0.005000',
                        'discount_tax': '0.0000',
                        'discount_total_price': '0.0100',
                        'goods_name': '泊本透明质酸钠补水保湿面膜25ml',
                        'goods_spec': '泊本透明质酸钠补水保湿面膜25ml',
                        'kind': '赠品',
                        'memo': '',
                        'oid': 'AD202403120560',
                        'pin': '1070223040000000000',
                        'qty': '2',
                        'tax': '0.0000',
                        'tax_rate': '0.1300',
                        'tid': '3810709875987335239',
                        'total_amount': '0.0100',
                        'unit': 'Pcs',
                        'wdt_order_number': 'JY202403123827'
                    }
                ],
                'discount_tax': '0.0000',
                'drawer': '管理员',
                'email_send_status': '未发送',
                'error_message': '',
                'goods_total_amount': '131.8400',
                'invoice_business_type': '企业',
                'invoice_category': '数电票(全电票)',
                'invoice_code': '',
                'invoice_download_url': 'http://fp.baiwang.com/format/d?d=FD372566199843F9D10D2AC6F3EA4AE16128D9DEB43D4C4E3764D7E34DEEC86A',  # noqa
                'invoice_isv_name': '百望数电票',
                'invoice_kind': '增值税普通发票',
                'invoice_number': '24932000000026833241',
                'invoice_odf_download_url': 'http://fp.baiwang.com/format/d?d=FD372566199843F9D10D2AC6F3EA4AE18717BAB0FE39D336D8C1C53F160216F5',  # noqa
                'invoice_payer_memo': '',
                'invoice_print_memo': '',
                'invoice_serial_no': 'FP202404240012',
                'invoice_start_time': '2024-04-24 15:05:56',
                'invoice_status': '开票成功',
                'invoice_title': '江苏中核华瑞建设有限公司',
                'invoice_xml_download_url': 'http://fp.baiwang.com/format/d?d=FD372566199843F9D10D2AC6F3EA4AE1E8CBC3B48DCFDD25317FF4EDE8BD1F99',  # noqa
                'latest_operate_log': '',
                'oss_url': "https://stg-locker.oss-cn-beijing.aliyuncs.com/files/1/45dbc4807c10c55a47ddc346a9a6ff44",
                'payee_name': '',
                'payee_tax_identification_number': '91320211581066381X',
                'payer_address': '',
                'payer_bank': '',
                'payer_bank_account': '',
                'payer_email': '',
                'payer_name': '宁波伯通伟达品牌管理有限公司',
                'red_or_blue': '蓝票',
                'reviewer': '',
                'shop_name': '天猫-泊本旗舰店',
                'tel_number': '',
                'tid': '3810709875987335239',
                'wdt_order_number': 'JY202403123827'}
            ]
        })
