import json

from robot_processor.client.aliyun_logistics_client import QueryLogisticsResp
from robot_processor.enums import JobStatus
from robot_processor.job.redmine_597874 import Redmine597874Executor

logistics_resp = '''
{
    "status":"0",
    "msg":"ok",
    "result":{
        "number":"433507608243244",
        "type":"YUNDA",
        "list":[
            {
                "time":"2023-11-07 02:11:33",
                "status":"快件丢失已面向商家\\/消费者解决，如有疑问请致电理赔服务专线13061749385"
            },
            {
                "time":"2023-11-02 04:31:33",
                "status":"【广州市】快件已启动理赔程序，如有疑问请致电理赔服务专线：13061749385"
            },
            {
                "time":"2023-10-24 22:21:56",
                "status":"【佛山市】已离开 广东佛山分拨交付中心；发往 广东广州南沙 保税区公司"
            },
            {
                "time":"2023-10-24 22:18:21",
                "status":"【佛山市】已到达 广东佛山分拨交付中心"
            },
            {
                "time":"2023-10-24 14:39:37",
                "status":"【广州市】已离开 广东广州分拨交付中心；发往 广东佛山分拨交付中心"
            },
            {
                "time":"2023-10-24 14:36:53",
                "status":"【广州市】已到达 广东广州分拨交付中心"
            },
            {
                "time":"2023-10-23 02:33:58",
                "status":"【南通市】已离开 江苏南通分拨交付中心；发往 广东广州分拨交付中心"
            },
            {
                "time":"2023-10-22 21:17:50",
                "status":"【南通市】已到达 江苏南通分拨交付中心"
            },
            {
                "time":"2023-10-21 23:35:50",
                "status":"【南通市】江苏南通分拨营销市场部-饶踏踏（13566739244） 已揽收"
            }
        ],
        "deliverystatus":"5",
        "issign":"0",
        "expName":"韵达快递",
        "expSite":"www.yundaex.com",
        "expPhone":"95546",
        "logo":"https:\\/\\/img3.fegine.com\\/express\\/yd.jpg",
        "courier":"",
        "courierPhone":"13061749385",
        "updateTime":"2023-11-07 02:11:33",
        "takeTime":"16天2小时35分"
    }
}
'''


def test_redmine_597874(client, mocker, mock_job_args, mock_full_job, mock_job_states):
    mocker.patch('robot_processor.client.aliyun_logistics_client.AliyunLogisticsClient.query_logistics',
                 return_value=QueryLogisticsResp(**json.loads(logistics_resp)))
    mock_job_args(logistics_no="433507608243244")
    status, msg = Redmine597874Executor(mock_full_job).process()
    assert status == JobStatus.SUCCEED
