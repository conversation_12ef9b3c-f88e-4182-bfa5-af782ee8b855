from pytest import fixture
from pytest_mock import MockerFixture

from robot_processor.job.job_model_wrapper import JobArguments


class TestRenderTextTemplateByMustache:
    def widgets(self):
        return [
            {
                "key": "ba4fa7d26b514d4cb822c514d05ea2d5",
                "type": "table",
                "option_value": {
                    "icon": "ficon-text-input",
                    "label": "快递公司名称",
                    "order": 0,
                    "fields": [
                        {
                            "id": 36,
                            "key": "e5903530-d098-4687-a2df-73358d5ad890",
                            "type": "string",
                            "before": False,
                            "data_schema": {
                                "fields": [
                                    {
                                        "name": "e5903530-d098-4687-a2df-73358d5ad890",
                                        "type": "string",
                                        "title": "快递公司名称",
                                        "constraints": {"required": False},
                                    }
                                ],
                                "multi_row": False,
                            },
                            "widget_type": "string",
                            "option_value": {
                                "name": "logistics_name",
                                "label": "快递公司名称",
                                "children": [],
                                "required": False,
                                "selected": True,
                                "widgetType": "string",
                                "allow_types": ["string"],
                                "data_schema": {
                                    "fields": [
                                        {
                                            "name": "e5903530-d098-4687-a2df-73358d5ad890",
                                            "type": "string",
                                            "title": "快递公司名称",
                                            "constraints": {"required": False},
                                        }
                                    ],
                                    "multi_row": False,
                                },
                                "defaultType": "string",
                                "outputLabel": "快递公司名称",
                                "widget_type": "string",
                                "data_binding": {
                                    "level": "$.orders",
                                    "expression": "orders[].logistics_name",
                                },
                                "isOutputField": True,
                            },
                        },
                        {
                            "id": 36,
                            "key": "5f94f7e4-15f4-4d75-9789-9710c26dccd6",
                            "type": "string",
                            "before": False,
                            "data_schema": {
                                "fields": [
                                    {
                                        "name": "5f94f7e4-15f4-4d75-9789-9710c26dccd6",
                                        "type": "string",
                                        "title": "快递单号",
                                        "constraints": {"required": False},
                                    }
                                ],
                                "multi_row": False,
                            },
                            "widget_type": "string",
                            "option_value": {
                                "name": "logistics_no",
                                "label": "快递单号",
                                "children": [],
                                "required": False,
                                "selected": True,
                                "widgetType": "string",
                                "allow_types": ["string"],
                                "data_schema": {
                                    "fields": [
                                        {
                                            "name": "5f94f7e4-15f4-4d75-9789-9710c26dccd6",
                                            "type": "string",
                                            "title": "快递单号",
                                            "constraints": {"required": False},
                                        }
                                    ],
                                    "multi_row": False,
                                },
                                "defaultType": "string",
                                "outputLabel": "快递单号",
                                "widget_type": "string",
                                "data_binding": {
                                    "level": "$.orders",
                                    "expression": "orders[].logistics_no",
                                },
                                "isOutputField": True,
                            },
                        },
                        {
                            "id": 36,
                            "key": "d236d296-2c41-4f99-b4cf-f0b4438c0aca",
                            "type": "string",
                            "before": False,
                            "data_schema": {
                                "fields": [
                                    {
                                        "name": "d236d296-2c41-4f99-b4cf-f0b4438c0aca",
                                        "type": "string",
                                        "title": "旺店通系统单号",
                                        "constraints": {"required": False},
                                    }
                                ],
                                "multi_row": False,
                            },
                            "widget_type": "string",
                            "option_value": {
                                "name": "trade_no",
                                "label": "旺店通系统单号",
                                "children": [],
                                "required": False,
                                "selected": True,
                                "widgetType": "string",
                                "allow_types": ["string"],
                                "defaultType": "string",
                                "hasTopSpace": False,
                                "outputLabel": "旺店通系统单号",
                                "widget_type": "string",
                                "data_binding": {
                                    "level": "$.orders",
                                    "expression": "orders[].trade_no",
                                },
                                "linkProvider": False,
                                "isOutputField": True,
                            },
                        },
                    ],
                    "category": "3",
                    "multiRow": True,
                    "required": True,
                    "widget_id": 35,
                    "ref_config": [],
                    "widgetType": "table",
                    "data_schema": {
                        "fields": [
                            {
                                "name": "e5903530-d098-4687-a2df-73358d5ad890",
                                "type": "string",
                                "title": "快递公司名称",
                                "constraints": {"required": False},
                            },
                            {
                                "name": "5f94f7e4-15f4-4d75-9789-9710c26dccd6",
                                "type": "string",
                                "title": "快递单号",
                                "constraints": {"required": False},
                            },
                        ],
                        "multi_row": True,
                    },
                    "description": "复合组件，可以支持用户自定义多列多行数据，类似表单样式",
                    "layoutWidth": 1,
                    "widget_meta": [],
                    "widget_type": "table",
                    "schema_value": {},
                    "fieldDescriptors": {"type": "table"},
                },
            }
        ]

    @fixture(autouse=True)
    def mock_bo_widgets(self, mocker: MockerFixture):
        yield mocker.patch(
            "robot_processor.business_order.handlers.bo_widget_wrapper",
            return_value=self.widgets(),
        )

    def args(self):
        return JobArguments(
            bo_data={
                "ba4fa7d26b514d4cb822c514d05ea2d5": [
                    {
                        "e5903530-d098-4687-a2df-73358d5ad890": "圆通",
                        "5f94f7e4-15f4-4d75-9789-9710c26dccd6": "123456",
                    },
                    {
                        "e5903530-d098-4687-a2df-73358d5ad890": "顺丰",
                        "5f94f7e4-15f4-4d75-9789-9710c26dccd6": "654321",
                    },
                    {
                        "e5903530-d098-4687-a2df-73358d5ad890": "中通",
                        "5f94f7e4-15f4-4d75-9789-9710c26dccd6": "456789",
                    },
                ]
            },
            scopes=[],
            widgets=self.widgets(),
            task_arguments=[],
            key_map={},
            shop_nick="",
        )

    def testcase(self):
        content = [
            {"text": "亲，您的订单已经发货啦，请您不要着急哈。\n", "type": "text"},
            {"text": "{{#", "type": "text"},
            {
                "key": {
                    "key": "ba4fa7d26b514d4cb822c514d05ea2d5",
                    "type": "table",
                    "field": None,
                    "multi_row": True,
                    "widget_type": "table",
                },
                "type": "concept",
                "mustache": True,
                "label": "快递公司名称",
                "stepName": "订单信息回执",
                "showLabel": "快递公司名称",
            },
            {"text": "}}\n", "type": "text"},
            {
                "text": "快递公司:{{e5903530-d098-4687-a2df-73358d5ad890}} 单号: {{5f94f7e4-15f4-4d75-9789-9710c26dccd6}}\n",
                "type": "text",
            },
            {"text": "{{/快递公司名称}}", "type": "text"},
        ]
        args = self.args()
        assert args._render_text_template(content) == """亲，您的订单已经发货啦，请您不要着急哈。
快递公司:圆通 单号: 123456
快递公司:顺丰 单号: 654321
快递公司:中通 单号: 456789
"""
