from typing import Named<PERSON>uple
from unittest.mock import MagicMock

from pytest import fixture

from robot_processor.business_order.models import Job
from robot_processor.enums import JobStatus
from robot_processor.job.doudian_memo_api import DoudianMemoAPIExecutor
from tests.testbed import BusinessOrderBuilder


@fixture
def mock_shop_with_doudian_xyz(shop_factory, grant_record_factory):
    mock_shop = shop_factory.create(platform="DOUDIAN")
    grant_record_factory.create(
        shop_id=mock_shop.id,
        access_token="access token",
        app="doudian-xyz"
    )
    yield mock_shop


@fixture
def testbed(client, mock_shop_with_doudian_xyz):
    testbed = BusinessOrderBuilder(
        {
            "form_template": "doudian-memo.default",
            "creator_user_id": 321,
            "creator_type": "LEYAN",
            "aid": "ut",
            "jobs": [
                {"name": "human_step_1", "assignee": "ut"},
                {"name": "修改订单备注", "status": JobStatus.INIT},
            ],
            "current_job": "修改订单备注",
            "data": {
                "tid": "1234567",
                "human_and_rpa:human_step_1:order": [{"tid": "1234567"}],
            },
        },
        mock_shop_with_doudian_xyz,
        "tests.job.resources",
    )
    testbed.build()

    step = testbed.form.steps.filter_by(name="修改订单备注").first()
    job = testbed.business_order.jobs.filter_by(step=step).first()

    yield NamedTuple("Testbed", job=Job)(job)


def test_doudian_memo(mocker, testbed):
    mocker.patch("robot_processor.job.doudian_memo_api.doudian_cloud"
                 ".get_order_detail",
                 return_value=MagicMock(
                    seller_words="旧备注"
                )
    )
    mocker.patch("robot_processor.job.doudian_memo_api.doudian_cloud.update_memo")
    status, error = DoudianMemoAPIExecutor(testbed.job).process()
    assert status == JobStatus.SUCCEED, error
