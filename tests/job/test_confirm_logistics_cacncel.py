import pytest
from dramatiq import Retry

from robot_processor.enums import JobStatus, LogisticsCancelStatus, JobTaskRunStatus
from robot_processor.job.confirm_logistics_cancel import ConfirmLogisticsCancelExecutor


def test_process(client, mock_full_job, mock_job_args, mock_job_states):
    mock_job_args(logistics_cancel_status='勿动')
    executor = ConfirmLogisticsCancelExecutor(mock_full_job)
    assert executor.get_job_task() is None
    with pytest.raises(Retry):
        executor.process()

    # SUCCESS
    job_task = executor.get_job_task()
    job_task.run_status = JobTaskRunStatus.SUCCEED.value
    mock_job_states.write_job_states({
        "logistics_cancel_status": {
            "status": LogisticsCancelStatus.SUCCEED.value,
            "status_time": "status_time111",
            "status_desc": "status_desc111"
        }
    })
    mock_job_args(logistics_cancel_status='勿动')
    status, _ = executor.process()
    assert status == JobStatus.SUCCEED
    assert mock_job_states.bo_data['logistics_cancel_status'] == LogisticsCancelStatus.SUCCEED.value

    # FAILED
    job_task.run_status = JobTaskRunStatus.SUCCEED.value
    mock_job_states.write_job_states({
        "logistics_cancel_status": {
            "status": LogisticsCancelStatus.FAILED.value,
            "status_time": "status_time111",
            "status_desc": "status_desc111"
        }
    })
    mock_job_args(logistics_cancel_status='勿动')
    status, _ = executor.process()
    assert mock_job_states.bo_data['logistics_cancel_status'] == LogisticsCancelStatus.FAILED.value
    assert status == JobStatus.SUCCEED

    job_task.run_status = JobTaskRunStatus.FAILED.value
    job_task.data = dict(error_msg="没有订单号或物流单号")
    with pytest.raises(Retry):
        executor.process()
