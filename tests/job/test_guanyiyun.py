import json
from copy import deepcopy

from result import Ok, Err

from robot_processor.enums import JobStatus, ErpType
from robot_processor.job.guanyiyun.guanyiyun import GuanyiyunRPAExecutor, GYYGetReturnOrdersRPAExecutor

CREATE_TRADE_RETURN_ORDER_FORM = {
    "tid": [{"tid": "1001"}],
    "note": "xxx\nyyy\nzzz",
    "express_code": "114514",
    "logistics_name": "物流公司",
    "warehouse_in_name": ["临时收货仓"],
    "warehouse_out_name": ["临时发货仓"],
    "items_in": [{"spu_name": "04", "sku": "4644774856896",
                  "spu": "XHZ-9012D", "quantity": "1",
                  "outer_sku": "YH-BX38A119-Y", "price": 1}],
    "items_out": {"after_sales_type": "non_original", "sku_list": [{
        "spu_name": "05", "sku": "4644774856896",
        "spu": "XHZ-9012D", "quantity": "1",
        "outer_sku": "YH-BX38A119-Y", "price": 1
    }]},
    "receiver_info": {
        'name': '石',
        'mobile': '17627337264',
        'state': '上海',
        'city': '上海市',
        'zone': '徐汇区',
        'town': '徐家街道',
        'address': '凯旋路4',
    }
}

CREATE_TRADE_RETURN_ORDER_RESPONSE = """
{
	"success": true,
	"message": "调用服务成功",
	"code": 200,
	"request": {
		"sid": "03012758",
		"title": "佛山市优益电器有限公司",
		"site": "guanyi",
		"namespace": "guanyi-erp",
		"method": "create-trade-return-order",
		"query": {
			"subUserNicks": "13726386318"
		},
		"payload": {
			"tid": "638327099159",
			"note": "hc01不加热  换底座",
			"expressCode": "JT3048927246951",
			"logisticsName": "韵达快递",
			"warehouseInName": "中山可发货仓",
			"warehouseOutName": "中山可发货仓",
			"itemsIn": [
				{
					"spu_id": "728050703372",
					"sku_id": "5050238817329",
					"outer_sku_id": "6921777203705",
					"qty": null,
					"pic": "https://img.alicdn.com/bao/uploaded/i2/2070121664/O1CN01HCHrI01OA9uew9EfD_!!2070121664.jpg",
					"price": 0,
					"type": 1
				},
				{
					"spu_id": "734167474288",
					"sku_id": "734167474288",
					"outer_sku_id": "6970137021562",
					"qty": null,
					"pic": "https://img.alicdn.com/bao/uploaded/i4/2070121664/O1CN01R9z6YP1OA9vDhXgfO_!!0-item_pic.jpg",
					"price": 0,
					"type": 1
				}
			],
			"itemsOut": [
				{
					"spu_id": "630460904460",
					"sku_id": "630460906381",
					"outer_sku_id": "7001500030012",
					"qty": 1,
					"pic": "",
					"price": 0,
					"type": 1
				}
			]
		},
		"traceId": "",
		"baggageInfo": {}
	},
	"context": {
		"rid": "9l1044h2q2h",
		"cid": "qoalRJG_9Nhb8InAHttA",
		"selectCount": 2,
		"onlineCount": 3,
		"execTime": 1564,
		"startTime": "2023-10-12 19:44:09.749",
		"endTime": "2023-10-12 19:44:11.313"
	},
	"version": "0.10.10-guanyibeta.3",
	"result": {
		"createDate": 1697111051000,
		"id": "658583118074",
		"tenantId": "232750299646",
		"code": "RGO658583118074",
		"pid": "638327099159",
		"pcode": "SO638327099159",
		"shopId": "236017244720",
		"platformCode": "1952965490578659471",
		"vipId": "638327133375",
		"warehouseInId": "233723725116",
		"warehouseOutId": "233723725116",
		"expressId": null,
		"expressCode": "JT3048927246951",
		"receiverName": "廖*敏",
		"receiverPhone": null,
		"receiverMobile": "159****4562",
		"receiverPhoneBlur": "",
		"receiverMobileBlur": "159*****562",
		"receiverZip": null,
		"receiverAddress": "湖南省 湘潭市 岳塘区 下摄司***************",
		"areaId": "430304",
		"createName": "天猫oidire售后客服",
		"approve": false,
		"approveName": null,
		"approveDate": null,
		"receive": 0,
		"receiveName": null,
		"receiveDate": null,
		"tradeReturnOrderDetailInList": [],
		"tradeReturnOrderDetailOutList": [],
		"tradeReturnOrderPaymentList": [],
		"vipName": "银**",
		"vipCode": "<EMAIL>",
		"shopName": "oidire旗舰店",
		"shopType": 101,
		"warehouseInName": "中山可发货仓",
		"warehouseOutName": "中山可发货仓",
		"areaName": "湖南省-湘潭市-岳塘区",
		"expressName": "",
		"tradeRefundTypeId": null,
		"tradeRefundTypeName": null,
		"refundId": "",
		"refundVersion": null,
		"refundPhase": null,
		"reason": "",
		"status": "",
		"statusCode": "",
		"billType": null,
		"prime": false,
		"agreeRefuse": null,
		"agreeRefuseMessage": null,
		"cancel": false,
		"cancelName": null,
		"cancelDate": null,
		"businessManId": null,
		"businessManName": null,
		"note": "hc01不加热  换底座",
		"sanwu": "否",
		"wms": 0,
		"tagId": null,
		"tagName": "实际退款金额为0时，入库提醒",
		"color": null,
		"wmsOrder": 0,
		"wmsDate": null,
		"ww": "银**",
		"changeOutExpressId": 10007056,
		"changeOutExpressName": "韵达快递",
		"agStatus": 0,
		"agErrMsg": null,
		"jdySaleReturnCode": null,
		"jdySaleReturnStatus": 0,
		"postFeeBearRole": null,
		"itemType": null,
		"returnTypeName": "换货",
		"createOrder": false,
		"createRefund": false,
		"swapOrderCode": null,
		"returnType": 1,
		"agreeRefuseName": null,
		"agreeRefuseDate": null,
		"sumQty": 0,
		"sumAmount": 0,
		"sumOriginAmount": 0,
		"sumAmountAfter": 0,
		"subTypeValue": "",
		"storeName": null,
		"platformType": 0,
		"otherServiceFee": 0,
		"drpTenantId": null,
		"salesTradeOrderCodes": [
			"SO638327099159"
		],
		"tradeRefundCodes": [],
		"kingdeeSyncStatus": 0,
		"kingdeeSyncMemo": null,
		"drpTenantName": null,
		"printStatus": 0,
		"sourceType": 0,
		"tradeReturnOrderTags": [],
		"afterSaleCustomTypeId": null,
		"afterSaleCustomTypeNames": null,
		"platformEncryptType": null,
		"receiverNameEncrypt": "",
		"vipEmailEncrypt": null,
		"vipIdcardEncrypt": null,
		"receiverMobileEncrypt": "",
		"receiverPhoneEncrypt": null,
		"receiverAddressEncrypt": "",
		"commodityStatus": 0,
		"returnMethod": null,
		"returnMethodName": null,
		"orderflag": null,
		"returnOrderCode": null,
		"preDeliveryOrderCode": null,
		"shipperCode": "0",
		"shipperName": null,
		"sellerMemo": null,
		"cloudAnnotation": null,
		"drpCustomerName": null,
		"drpOrder": false,
		"allowBatches": null,
		"picUrlList": null,
		"tradeReturnOrderMessageDTOList": null,
		"sourceName": "手动新增"
	},
	"runtime": {
		"authInfo": {
			"shopID": "03012758",
			"shopName": "佛山市优益电器有限公司",
			"subUserID": "613343148666",
			"subUserNick": "13726386318"
		},
		"timeout": 40000,
		"execTime": 1514,
		"startTime": "2023/10/12 下午7:44:09",
		"endTime": "2023/10/12 下午7:44:11",
		"loadTime": "2023/10/12 下午7:44:01",
		"href": "https://v2.guanyierp.com/index"
	}
}""" # noqa


class TestGuanyiyunRPAExecutor:
    def test_parse_address(self, mocker, client, mock_full_job, mock_job_args, mock_job_states):
        form = deepcopy(CREATE_TRADE_RETURN_ORDER_FORM)
        form.update({
            "receiver_info": {
                'name': '石',
                'mobile': '17627337264',
                'state': '上海市',
                'city': '上海市',
                'zone': '徐汇区',
                'town': '徐家街道',
                'address': '凯旋路4',
            }
        })
        mock_job_args(**form)
        executor = GuanyiyunRPAExecutor(mock_full_job)
        args = executor.get_request_body()
        assert args.get("receiverInfo") == {
            "receiverName": "石",
            "receiverMobile": "17627337264",
            "provinceId": "上海",
            "cityId": "上海市",
            "areaId": "徐汇区",
            "town": "徐家街道",
            "receiverAddress": "上海上海市徐汇区徐家街道凯旋路4"
        }

        form.update({
            "receiver_info": {
                'name': '石',
                'mobile': '17627337264',
                'state': '上海市',
                'city': '上海市',
                'town': '书院镇',
                'address': '凯旋路4',
            }
        })
        mock_job_args(**form)
        executor = GuanyiyunRPAExecutor(mock_full_job)
        args = executor.get_request_body()
        assert args.get("receiverInfo") == {
            "receiverName": "石",
            "receiverMobile": "17627337264",
            "provinceId": "上海",
            "cityId": "上海市",
            "town": "书院镇",
            "receiverAddress": "上海上海市书院镇凯旋路4"
        }

    def test_parse_form(self, mocker, client, mock_full_job, mock_job_args, mock_job_states):
        mock_job_args(**CREATE_TRADE_RETURN_ORDER_FORM)
        executor = GuanyiyunRPAExecutor(mock_full_job)
        args = executor.get_request_body()
        assert args == {
            "tid": "1001",
            "note": "xxx\nyyy\nzzz",
            "expressCode": "114514",
            "logisticsName": "物流公司",
            "warehouseInName": "临时收货仓",
            "warehouseOutName": "临时发货仓",
            "itemsIn": [{
                "spu_id": "XHZ-9012D",
                "sku_id": "4644774856896",
                "outer_sku_id": "YH-BX38A119-Y",
                "qty": 1,
                "price": 0.0,
                "type": 1
            }],
            "itemsOut": [{
                "spu_id": "XHZ-9012D",
                "sku_id": "4644774856896",
                "outer_sku_id": "YH-BX38A119-Y",
                "qty": 1,
                "price": 0.0,
                "type": 1
            }],
            "receiverInfo": {
                "provinceId": "上海",
                "cityId": "上海市",
                "areaId": "徐汇区",
                "town": "徐家街道",
                "receiverName": "石",
                "receiverMobile": "17627337264",
                "receiverAddress": "上海上海市徐汇区徐家街道凯旋路4"
            }
        }

    def test_parse_form_without_receiver_info(self, mocker, client, mock_full_job, mock_job_args, mock_job_states):
        form = CREATE_TRADE_RETURN_ORDER_FORM
        form.pop("receiver_info")
        mock_job_args(**form)
        executor = GuanyiyunRPAExecutor(mock_full_job)
        args = executor.get_request_body()
        assert args == {
            "tid": "1001",
            "note": "xxx\nyyy\nzzz",
            "expressCode": "114514",
            "logisticsName": "物流公司",
            "warehouseInName": "临时收货仓",
            "warehouseOutName": "临时发货仓",
            "itemsIn": [{
                "spu_id": "XHZ-9012D",
                "sku_id": "4644774856896",
                "outer_sku_id": "YH-BX38A119-Y",
                "qty": 1,
                "price": 0.0,
                "type": 1
            }],
            "itemsOut": [{
                "spu_id": "XHZ-9012D",
                "sku_id": "4644774856896",
                "outer_sku_id": "YH-BX38A119-Y",
                "qty": 1,
                "price": 0.0,
                "type": 1
            }]
        }

    def test_success(self, mocker, client, mock_full_job, mock_job_args, mock_job_states, mock_erp_info):
        mock_erp_info.erp_type = ErpType.GUANYIYUN
        mock_job_args(**CREATE_TRADE_RETURN_ORDER_FORM)
        executor = GuanyiyunRPAExecutor(mock_full_job)
        mocker.patch(
            "rpa.erp.guanyiyun.GuanyiyunClient.get_erp_tid",
            return_value=Ok("xxx")
        )
        mocker.patch(
            "rpa.mola.MolaClient.call_namespace_method",
            return_value=Ok(json.loads(CREATE_TRADE_RETURN_ORDER_RESPONSE))
        )
        status, e = executor.process()
        assert status == JobStatus.SUCCEED
        assert e == ""

        mock_job_states.write_job_output.assert_called_with({
            "code": "RGO658583118074"
        })

    def test_timeout(self, mocker, client, mock_full_job, mock_job_args, mock_job_states, mock_erp_info):
        mock_erp_info.erp_type = ErpType.GUANYIYUN
        mock_job_args(**CREATE_TRADE_RETURN_ORDER_FORM)
        executor = GuanyiyunRPAExecutor(mock_full_job)
        mocker.patch(
            "rpa.mola.MolaClient.call_namespace_method",
            return_value=Err("request mola timeout")
        )
        status, e = executor.process()
        assert status == JobStatus.FAILED
        assert e == "request mola timeout"

    def test_invalid_form(self, client, mock_full_job, mock_job_args, mock_job_states):
        CREATE_TRADE_RETURN_ORDER_FORM.update({"items_out": "xx"})
        mock_job_args(**CREATE_TRADE_RETURN_ORDER_FORM)
        executor = GuanyiyunRPAExecutor(mock_full_job)
        status, e = executor.process()
        assert status == JobStatus.FAILED
        assert e.startswith("参数解析时发生问题")

    def test_invalid_order(self, mocker, client, mock_full_job, mock_job_args, mock_job_states, mock_erp_info):
        mock_erp_info.erp_type = ErpType.GUANYIYUN
        CREATE_TRADE_RETURN_ORDER_FORM.update({"items_out": {"after_sales_type": "non_original", "sku_list": [{
            "spu_name": "05", "sku": "4644774856896",
            "spu": "XHZ-9012D", "quantity": "1",
            "outer_sku": "YH-BX38A119-Y", "price": 1
        }]}})
        mock_job_args(**CREATE_TRADE_RETURN_ORDER_FORM)
        executor = GuanyiyunRPAExecutor(mock_full_job)
        mocker.patch(
            "rpa.erp.guanyiyun.GuanyiyunClient.get_erp_tid",
            return_value=Err("未找到该订单")
        )
        mocker.patch(
            "rpa.mola.MolaClient.call_namespace_method",
            return_value=Ok({
                'success': True, 'message': '调用服务成功', 'code': 200,
                'request': {}, 'context': {}, 'version': '0.10.4-test231052',
                'result': {'code': '1188'}
            })
        )
        status, e = executor.process()
        assert status == JobStatus.FAILED
        assert e == "未找到该订单"


class TestGYYGetReturnOrdersRPAExecutor:
    def test_success(self, mocker, client, mock_full_job, mock_job_args, mock_job_states, mock_erp_info):
        mock_erp_info.erp_type = ErpType.GUANYIYUN
        mock_job_args(**{
            "tid": "RGO556443031702"
        })
        executor = GYYGetReturnOrdersRPAExecutor(mock_full_job)
        mock_mola_method = mocker.patch(
            "rpa.mola.MolaClient.call_namespace_method",
            return_value=Ok(
                {
                    "success": True,
                    "message": "调用服务成功",
                    "code": 200,
                    "result": {"data": {
                        "createOrder": True,
                        "createRefund": False,
                        "swapOrderCode": "SO556443172243",
                        "returnType": 1
                    }, "status": "200"}
                }
            )
        )
        status, e = executor.process()
        assert status == JobStatus.SUCCEED
        assert e == ""

        mock_job_states.write_job_output.assert_called_with({"return_order_id": "556443172243"})
        mock_mola_method.assert_called_with(namespace='guanyiyun-erp', method='get-trade-return-order-info',
                                            data={'tid': '556443031702'})
