import pytest
from flask import json

from robot_processor.enums import JobStatus
from robot_processor.job.jst_after_sale_list import JstAfterSaleTradeExecutor
from rpa.erp.jst import AfterSaleResponseData

mock_after_sale_list_resp = {'msg': '执行成功', 'code': 0, 'data': {'datas': [
    {'refund_version': None, 'order_labels': ['发货后售后', '线上发货'], 'drp_co_id_from': None, 'type': '维修',
     'good_status': 'SELLER_RECEIVED', 'buyer_apply_refund': None, 'order_status': 'Sent',
     'modified': '2023-12-21 15:27:56', 'payment': 0, 'order_type': '普通订单', 'shop_status': None,
     'created': '2023-12-21 15:07:15', 'is_split': None, 'warehouse': '3｜北京-办公室', 'shop_name': '天猫',
     'urefund': 0, 'io_id': None, 'labels': '', 'node': '', 'shop_id': 15475848, 'wms_co_id': 13508273,
     'drp_co_id_to': None, 'confirm_date': '2023-12-21 15:27:56', 'items': [
        {'box_id': None, 'i_id': 'EE001', 'as_id': *********, 'item_sign': None, 'remark': '',
         'pic': 'https://img.alicdn.com/bao/uploaded/i2/3642336162/O1CN010TH6Vh1vOFNeRD96k_!!3642336162.png',
         'type': '维修', 'des': '', 'price': 319, 'combine_sku_id': None, 'r_qty': 1, 'defective_qty': None,
         'outer_oi_id': None, 'amount': 319, 'sku_type': 'normal', 'asi_id': 980003314, 'sku_id': '6971503540031',
         'shop_amount': None, 'shop_sku_id': None, 'receive_date': '2023-12-21 15:07:15', 'raw_so_id': None,
         'properties_value': '小笨钟', 'is_gift': None, 'qty': 1, 'name': '小笨钟'}], 'status': 'Confirmed',
     'refund': 0, 'modifier_name': '李瀚成', 'as_id': *********, 'outer_as_id': 'S9220920231221150715808', 'freight': 0,
     'remark': '闪电-10763', 'result': '', 'so_id': None, 'o_id': 92209, 'currency': None, 'free_amount': 0,
     'question_type': '产品-不工作', 'co_id': 13508273, 'shop_freight': 0, 'shop_site': '淘宝天猫',
     'refund_phase': None, 'wh_id': 1, 'is_merge': None, 'as_date': '2023-12-21 15:07:15', 'shop_type': None,
     'creator_name': '杨文杰', 'logistics_company': '', 'l_id': None, 'ts': 4906599154}], 'requestId': None,
                                                                    'page_index': 1, 'has_next': False, 'data_count': 1,
                                                                    'page_count': 1, 'page_size': 1}}


@pytest.fixture
def mock_after_sale_list(mocker):
    mocker.patch("robot_processor.job.jst_after_sale_list.JstQmSDK.query_refund_trade_list",
                 return_value=AfterSaleResponseData(**mock_after_sale_list_resp["data"]))


def test_jst_after_sale_list(mock_full_job, mock_after_sale_list, jst_new_sdk, mock_job_args):
    exe = JstAfterSaleTradeExecutor(mock_full_job)
    status, _ = exe.process()
    assert status == JobStatus.SUCCEED

    mock_job_args(good_status="other")
    status, msg = exe.process()
    assert status == JobStatus.FAILED
    assert msg == "未找到订单信息 找不到售后单"


@pytest.fixture
def mock_no_trades(mocker):
    mocker.patch("robot_processor.job.jst_after_sale_list.JstQmSDK.query_refund_trade_list",
                 return_value=AfterSaleResponseData(**json.loads(
                     """{"response":{"data_count":0,"has_next":false,"page_count":0,"page_index":0,"page_size":0}}"""
                 )["response"]))


def test_jst_after_sale_list_no_trades(mock_full_job, mock_no_trades, jst_new_sdk):
    exe = JstAfterSaleTradeExecutor(mock_full_job)
    status, msg = exe.process()
    assert status == JobStatus.FAILED
    assert "未找到订单信息 找不到售后单" == msg
