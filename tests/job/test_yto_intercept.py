import json
from unittest.mock import MagicMock, patch

import arrow
from faker import Faker
from lepollo.mock import patch_config
from pytest import fixture, mark
from pydantic_factories import ModelFactory

from robot_processor.business_order.models import InterceptEvent, WebhookMessages
from robot_processor.db import in_transaction
from robot_processor.enums import JobStatus, AuthType
from robot_processor.job.yto_intercept_report import YTOInterceptReportExecutor
from robot_processor.job.yto_intercept_confirm import YTOInterceptConfirmExecutor
from robot_processor.shop.auth_manager import Credentials, CredentialShopMapping
from robot_processor.form import named_type_spec
from rpa.yto.sdk import yto_config
from rpa.yto.schema import InterceptStatusPushMessage, TrackingInfo
from robot_processor.client.logistics_clients.exception_manager import (
    config as exception_config,
)
from tests.job.conftest import MockJobArgs, MockJobStates
from robot_processor.client.conf import app_config

faker = Faker(locale="zh_CN")


class TestYTOInterceptReport:
    @fixture(scope="function")
    def setup(self):
        from robot_processor.client.conf import app_config

        # mock 店铺信息
        shop = MagicMock()
        shop.org_id = faker.random_int()
        shop.channel_id = faker.random_int()
        # 给店铺绑定圆通授权信息
        yto_auth = YtoCredentialFactory.build()
        shop.get_logistic_grant_records = MagicMock(return_value=[yto_auth.dict()])

        with in_transaction() as trx:
            credential = Credentials(
                org_id=shop.org_id,
                auth_type=AuthType.YTO,
                auth_account=faker.pystr(),
                auth_extra_data=yto_auth.dict(),
            )
            trx.add(credential)
            trx.flush()
            trx.add(
                CredentialShopMapping(channel_id=shop.channel_id, auth_id=credential.id)
            )
        # mock 工单执行步骤
        job, order = MagicMock(), MagicMock()
        job.id = faker.random_int()
        order.id = faker.random_int()
        job.business_order = order
        executor = YTOInterceptReportExecutor(job)
        # mock JobArguments
        args = MockJobArgs({})
        executor.__dict__["args"] = args
        # mock JobStates
        states = MockJobStates({})
        executor.__dict__["states"] = states
        # 绑定 job 和 shop
        executor.job_wrapper.__dict__["shop"] = shop

        with patch_config(app_config) as conf:
            conf.YTO_QUERY_TRACE_CUSTOMER_CODE_TO_KEY = json.dumps(yto_auth.dict())
            yield {
                "executor": executor,
                "args": args,
                "states": states,
                "credential": yto_auth,
                "waybill_no": faker.pystr(),
            }

    @fixture
    def setup运单号为空(self, setup):
        setup["args"].bo_data.pop("waybill_no", None)
        yield

    @mark.usefixtures("setup运单号为空")
    def test运单号为空(self, setup):
        status, err_message = setup["executor"].process()
        assert status is JobStatus.FAILED
        assert err_message == "运单号和物流公司不得为空！"

    def test_multi_credentials(self, setup, requests_mock):
        partner_key = setup["credential"].partner_key
        setup["sdk_request"] = requests_mock.post(
            f"{yto_config.app_endpoint}/wanted_report_adapter/v1/tuuzc5/{partner_key}",
            json={"statusCode": 0, "statusMessage": "拦截成功"},
            status_code=200,
        )
        setup["args"].mock_args(waybill_no=setup["waybill_no"])

        shop = setup["executor"].job_wrapper.shop
        yto_auth = YtoCredentialFactory.build()

        with in_transaction() as trx:
            credential = Credentials(
                org_id=shop.org_id,
                auth_type=AuthType.YTO,
                auth_account=faker.pystr(),
                auth_extra_data=yto_auth.dict(),
            )
            trx.add(credential)
            trx.flush()
            trx.add(
                CredentialShopMapping(channel_id=shop.channel_id, auth_id=credential.id)
            )

            setup["sdk_request"] = requests_mock.post(
                f"{yto_config.app_endpoint}/wanted_report_adapter/v1/tuuzc5/{yto_auth.dict().get('partner_key')}",
                json={"statusCode": 0, "statusMessage": "拦截成功"},
                status_code=200,
            )

            setup["args"].mock_args(auth_account=credential.auth_account)

            status, _ = setup["executor"].process()

            assert status is JobStatus.SUCCEED
            setup["states"].write_job_widget_collection.assert_called_with(
                {
                    "interceptAt": None,
                    "interceptStatus": "拦截发起成功",
                    "errorMsg": None,
                    "suggestion": None,
                    "matchErrorType": None,
                    "matchErrorMsg": None,
                    "ztoCenterBizNo": None,
                }
            )

            assert InterceptEvent.query.filter_by(
                logistics_no=setup["waybill_no"],
                logistics_company="yto",
                type="REPORT",
                business_order_id=setup["executor"].order.id,
            ).one()
            trx.rollback()

    @fixture
    def setup接口异常(self, setup, requests_mock):
        partner_key = setup["credential"].partner_key
        setup["sdk_request"] = requests_mock.post(
            f"{yto_config.app_endpoint}/wanted_report_adapter/v1/tuuzc5/{partner_key}",
            json={"statusCode": -1, "statusMessage": "签名错误"},
            status_code=401,
        )
        setup["args"].mock_args(waybill_no=setup["waybill_no"])
        yield

    @mark.usefixtures("setup接口异常")
    def test接口异常(self, setup):
        status, err_message = setup["executor"].process()
        assert status is JobStatus.FAILED
        assert err_message == "签名错误"

    @fixture
    def setup已派件拦截失败(self, setup, requests_mock):
        partner_key = setup["credential"].partner_key
        setup["sdk_request"] = requests_mock.post(
            f"{yto_config.app_endpoint}/wanted_report_adapter/v1/tuuzc5/{partner_key}",
            json={"statusCode": 1, "statusMessage": "已做派件扫描，拦截失败"},
            status_code=200,
        )
        setup["args"].mock_args(waybill_no=setup["waybill_no"])
        exception_config._properties["logistics.errors_match_rule.yto"] = json.dumps(
            [
                {
                    "error_type": "MATCH_ERROR_DELIVERY",
                    "error_code": [],
                    "regex": [".*已做派件扫描，拦截失败.*"],
                    "suggestion": "不可进行拦截，已进行派送",
                }
            ]
        )

        yield

    @mark.usefixtures("setup已派件拦截失败")
    def test已派件拦截失败(self, setup):
        mock_intercept_at = "2024-01-01 00:00:00"
        with patch("arrow.arrow.Arrow.format", return_value=mock_intercept_at):
            status, _ = setup["executor"].process()
        assert status is JobStatus.SUCCEED
        setup["states"].write_job_widget_collection.assert_called_with(
            {
                "interceptAt": mock_intercept_at,
                "interceptStatus": "拦截发起失败",
                "errorMsg": "已做派件扫描，拦截失败",
                "suggestion": "不可进行拦截，已进行派送",
                "matchErrorType": 2,
                "matchErrorMsg": "已派送",
                "ztoCenterBizNo": None
            }
        )

    @fixture
    def setup拦截发起成功(self, setup, requests_mock):
        partner_key = setup["credential"].partner_key
        setup["sdk_request"] = requests_mock.post(
            f"{yto_config.app_endpoint}/wanted_report_adapter/v1/tuuzc5/{partner_key}",
            json={"statusCode": 0, "statusMessage": "拦截成功"},
            status_code=200,
        )
        setup["args"].mock_args(waybill_no=setup["waybill_no"])
        yield

    @mark.usefixtures("setup拦截发起成功")
    def test拦截发起成功(self, setup):
        status, _ = setup["executor"].process()

        assert status is JobStatus.SUCCEED
        setup["states"].write_job_widget_collection.assert_called_with(
            {
                "interceptAt": None,
                "interceptStatus": "拦截发起成功",
                "errorMsg": None,
                "suggestion": None,
                "matchErrorType": None,
                "matchErrorMsg": None,
                "ztoCenterBizNo": None,
            }
        )

        assert InterceptEvent.query.filter_by(
            logistics_no=setup["waybill_no"],
            logistics_company="yto",
            type="REPORT",
            business_order_id=setup["executor"].order.id,
        ).one()


class TestYTOInterceptConfirm:
    @fixture(scope="function")
    def setup(self):
        # mock 店铺信息
        shop = MagicMock()
        shop.org_id = faker.random_int()
        shop.channel_id = faker.random_int()
        # 给店铺绑定圆通授权信息
        yto_auth = YtoCredentialFactory.build()

        with in_transaction() as trx:
            credential = Credentials(
                org_id=shop.org_id,
                auth_type=AuthType.YTO,
                auth_account=faker.pystr(),
                auth_extra_data=yto_auth.dict(),
            )
            trx.add(credential)
            trx.flush()
            trx.add(
                CredentialShopMapping(channel_id=shop.channel_id, auth_id=credential.id)
            )
        # mock 工单执行步骤
        job, order = MagicMock(), MagicMock()
        job.id = faker.random_int()
        job.created_at = arrow.now().int_timestamp
        order.id = faker.random_int()
        job.business_order = order
        executor = YTOInterceptConfirmExecutor(job)
        # mock JobArguments
        waybill_no = faker.pystr()
        args = MockJobArgs({"waybill_no": waybill_no})
        executor.__dict__["args"] = args
        # mock JobStates
        states = MockJobStates({})
        executor.__dict__["states"] = states
        # 绑定 job 和 shop
        executor.job_wrapper.__dict__["shop"] = shop

        # 注册 InterceptEvent
        InterceptEvent.record_wait_report_event(waybill_no, "yto", order.id)

        with patch_config(app_config) as conf:
            conf.YTO_QUERY_TRACE_CUSTOMER_CODE_TO_KEY = json.dumps(yto_auth.dict())
            yield {
                "executor": executor,
                "args": args,
                "states": states,
                "credential": yto_auth,
                "job": job,
                "waybill_no": waybill_no,
            }

    @fixture
    def setup超时(self, setup):
        setup["job"].created_at = arrow.now().shift(years=-1).int_timestamp
        yield

    @mark.usefixtures("setup超时")
    def test超时(self, setup):
        setup["executor"].process()
        setup["states"].write_job_widget_collection.assert_called_with(
            {
                "errorMsg": "等待拦截状态刷新超时",
                "interceptStatus": "等待拦截状态刷新超时",
                "matchErrorType": None,
                "suggestion": None,
            }
        )
        assert (
            InterceptEvent.get_latest_intercept_event(
                setup["waybill_no"], "yto"
            ).event_status
            is InterceptEvent.EventStatus.TIMEOUT
        )

    @fixture
    def setup根据拦截推送结果失败(self, setup):
        push_message = InterceptStatusPushMessageFactory.build()
        push_message.data.resultCode = (
            InterceptStatusPushMessage.ResultCode.SIGNED.value
        )
        push_message.data.result = InterceptStatusPushMessage.ResultCode.SIGNED.label
        webhook_message = WebhookMessages.record_logistics_intercept_event(
            "yto", setup["waybill_no"], push_message.dict()
        )
        InterceptEvent.mark_intercept_report_status(
            setup["waybill_no"],
            "yto",
            InterceptEvent.InterceptStatus.FAILED,
            webhook_message.id,
        )
        yield

    @mark.usefixtures("setup根据拦截推送结果失败")
    def test根据拦截推送结果失败(self, setup):
        setup["executor"].process()
        setup["states"].write_job_widget_collection.assert_called_with(
            {
                "errorMsg": "拦截失败, 快件已签收",
                "interceptStatus": "拦截失败",
                "matchErrorType": None,
                "suggestion": None,
            }
        )
        assert (
            InterceptEvent.get_latest_intercept_event(
                setup["waybill_no"], "yto"
            ).event_status
            is InterceptEvent.EventStatus.FINISHED
        )

    @fixture
    def setup根据物流轨迹判断拦截结果未知(self, setup, requests_mock):
        partner_key = setup["credential"].partner_key
        requests_mock.post(
            f"{yto_config.app_endpoint}/track_query_adapter/v1/tuuzc5/{partner_key}",
            json=[
                {
                    "waybill_No": setup["waybill_no"],
                    "upload_Time": "2024-01-01 00:00:00",
                    "infoContent": TrackingInfo.InfoContent.DEPARTURE,
                    "processInfo": "已从 xxx 发出",
                }
            ],
        )
        yield

    @mark.usefixtures("setup根据物流轨迹判断拦截结果未知")
    def test根据物流轨迹判断拦截结果未知(self, setup):
        with patch_config(app_config) as conf:
            conf.YTO_QUERY_TRACE_CUSTOMER_CODE_TO_KEY = json.dumps(setup["credential"].dict())
            status, error_message = setup["executor"].process()
            assert status is JobStatus.RUNNING
            assert error_message is None
            setup["states"].write_job_widget_collection.assert_called_with(
                {
                    "errorMsg": None,
                    "interceptStatus": "等待确认拦截状态",
                    "matchErrorType": None,
                    "suggestion": None,
                }
            )

    @fixture
    def setup根据物流轨迹判断拦截结果成功(self, setup, requests_mock):
        partner_key = setup["credential"].partner_key
        requests_mock.post(
            f"{yto_config.app_endpoint}/track_query_adapter/v1/tuuzc5/{partner_key}",
            json=[
                {
                    "waybill_No": setup["waybill_no"],
                    "upload_Time": "2024-01-01 00:00:00",
                    "infoContent": TrackingInfo.InfoContent.TMS_RETURN,
                    "processInfo": "xxx",
                }
            ],
        )
        yield

    @mark.usefixtures("setup根据物流轨迹判断拦截结果成功")
    def test根据物流轨迹判断拦截结果成功(self, setup):
        with patch_config(app_config) as conf:
            conf.YTO_QUERY_TRACE_CUSTOMER_CODE_TO_KEY = json.dumps(setup["credential"].dict())
            status, error_message = setup["executor"].process()
            assert status is JobStatus.SUCCEED
            assert error_message is None
            setup["states"].write_job_widget_collection.assert_called_with(
                {
                    "errorMsg": None,
                    "interceptStatus": "拦截成功",
                    "matchErrorType": None,
                    "suggestion": None,
                }
            )
            # 根据物流轨迹判断拦截结果，不应该更新 InterceptEvent 的状态
            assert (
                InterceptEvent.get_latest_intercept_event(
                    setup["waybill_no"], "yto"
                ).event_status
                is InterceptEvent.EventStatus.WAIT_INTERCEPT
            )

    @fixture
    def setup根据物流轨迹判断拦截结果失败(self, setup, requests_mock):
        partner_key = setup["credential"].partner_key
        requests_mock.post(
            f"{yto_config.app_endpoint}/track_query_adapter/v1/tuuzc5/{partner_key}",
            json=[
                {
                    "waybill_No": setup["waybill_no"],
                    "upload_Time": "2024-01-01 00:00:00",
                    "infoContent": TrackingInfo.InfoContent.SIGNED,
                    "processInfo": "xxx",
                }
            ],
        )
        yield

    @mark.usefixtures("setup根据物流轨迹判断拦截结果失败")
    def test根据物流轨迹判断拦截结果失败(self, setup):
        with patch_config(app_config) as conf:
            conf.YTO_QUERY_TRACE_CUSTOMER_CODE_TO_KEY = json.dumps(setup["credential"].dict())
            status, error_message = setup["executor"].process()
            assert status is JobStatus.FAILED
            assert error_message == "拦截失败，快件已签收"
            setup["states"].write_job_widget_collection.assert_called_with(
                {
                    "errorMsg": "拦截失败，快件已签收",
                    "interceptStatus": "拦截失败",
                    "matchErrorType": None,
                    "suggestion": None,
                }
            )
            assert (
                InterceptEvent.get_latest_intercept_event(
                    setup["waybill_no"], "yto"
                ).event_status
                is InterceptEvent.EventStatus.WAIT_INTERCEPT
            )

    @fixture
    def setup过滤发起拦截前的物流轨迹(self, setup, requests_mock):
        partner_key = setup["credential"].partner_key
        requests_mock.post(
            f"{yto_config.app_endpoint}/track_query_adapter/v1/tuuzc5/{partner_key}",
            json=[
                {
                    "waybill_No": setup["waybill_no"],
                    "upload_Time": "2024-01-01 00:00:00",
                    "infoContent": TrackingInfo.InfoContent.DEPARTURE,
                    "processInfo": "从 xxx 发出",
                },
                {
                    "waybill_No": setup["waybill_no"],
                    "upload_Time": "2024-01-01 01:00:00",
                    "infoContent": TrackingInfo.InfoContent.TMS_RETURN,
                    "processInfo": "第一次拦截",
                },
                {
                    "waybill_No": setup["waybill_no"],
                    "upload_Time": "2024-01-01 02:00:00",
                    "infoContent": TrackingInfo.InfoContent.DEPARTURE,
                    "processInfo": "取消拦截，物流继续",
                },
                {
                    "waybill_No": setup["waybill_no"],
                    "upload_Time": "2024-01-01 03:00:00",
                    "infoContent": TrackingInfo.InfoContent.DEPARTURE,
                    "processInfo": "取消拦截后，新的从 xxx 发出",
                },
            ],
        )
        # 本次发起拦截时间，用于过滤发起拦截前的物流轨迹
        setup["args"].mock_args(intercept_at="2024-01-01 02:00:00")
        yield

    @mark.usefixtures("setup过滤发起拦截前的物流轨迹")
    def test过滤发起拦截前的物流轨迹(self, setup):
        with patch_config(app_config) as conf:
            conf.YTO_QUERY_TRACE_CUSTOMER_CODE_TO_KEY = json.dumps(setup["credential"].dict())
            status, error_message = setup["executor"].process()
            assert status is JobStatus.RUNNING
            assert error_message is None
            setup["states"].write_job_widget_collection.assert_called_with(
                {
                    "errorMsg": None,
                    "interceptStatus": "等待确认拦截状态",
                    "matchErrorType": None,
                    "suggestion": None,
                }
            )
            # 根据物流轨迹判断拦截结果，不应该更新 InterceptEvent 的状态
            assert (
                InterceptEvent.get_latest_intercept_event(
                    setup["waybill_no"], "yto"
                ).event_status
                is InterceptEvent.EventStatus.WAIT_INTERCEPT
            )


class YtoCredentialFactory(ModelFactory):
    __model__ = named_type_spec.Credential.YTO.model
    __faker__ = faker


class InterceptStatusPushMessageFactory(ModelFactory):
    __model__ = InterceptStatusPushMessage
    __faker__ = faker
