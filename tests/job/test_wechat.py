from unittest.mock import MagicMock

from result import Err, Ok

from robot_processor.enums import JobStatus
from robot_processor.job.wechat import WechatExecutor
from robot_processor.job.job_model_wrapper import SendMessageArguments


def test_process_skiped(mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        wechat_group='wechat_group', wechat_id='wechat_id', wechat_nick='wechat_nick',
        content='content',
        image_urls=['http://example.com/image'],
        send_channel='RPA_CLIENT',
        skip=True,
    ))
    executor = WechatExecutor(MagicMock())
    status, msg = executor.process()
    assert status == JobStatus.SUCCEED


def test_process_missing_wechat_group(mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        wechat_id='wechat_id', wechat_nick='wechat_nick',
        content='content',
        image_urls=['http://example.com/image'],
        send_channel='RPA_CLIENT',
        skip=False,
    ))
    executor = WechatExecutor(MagicMock())
    status, msg = executor.process()
    assert status == JobStatus.FAILED
    assert msg.startswith('微信群名为空')


def test_process_missing_wechat_id(mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        wechat_group='wechat_group', wechat_nick='wechat_nick',
        content='content',
        image_urls=['http://example.com/image'],
        send_channel='RPA_CLIENT',
        skip=False,
    ))
    executor = WechatExecutor(MagicMock())
    status, msg = executor.process()
    assert status == JobStatus.FAILED
    assert msg.startswith('微信ID为空')


def test_process_fail(mocker, mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        wechat_group='wechat_group', wechat_id='wechat_id', wechat_nick='wechat_nick',
        content='content',
        image_urls=['http://example.com/image'],
        send_channel='RPA_CLIENT',
        skip=False,
    ))
    mock_send_message = mocker.patch('rpa.mola.client.MolaClient.send_wechat_message',
                                     return_value=Err("error_message"))
    executor = WechatExecutor(MagicMock())
    mocker.patch.object(executor, 'job_wrapper')
    status, msg = executor.process()
    assert status == JobStatus.FAILED
    assert msg == 'error_message'
    mock_send_message.assert_called_once_with('wechat_group',
                                              [{'text': 'content'}, {'imgPath': 'http://example.com/image'}])


def test_process_succeed(mocker, mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        wechat_group='wechat_group', wechat_id='wechat_id', wechat_nick='wechat_nick',
        content='content',
        image_urls=['http://example.com/image'],
        send_channel='RPA_CLIENT',
        skip=False,
    ))
    mock_send_message = mocker.patch('rpa.mola.client.MolaClient.send_wechat_message',
                                     return_value=Ok(None))
    executor = WechatExecutor(MagicMock())
    mocker.patch.object(executor, 'job_wrapper')
    status, msg = executor.process()
    assert status == JobStatus.SUCCEED
    mock_send_message.assert_called_once_with('wechat_group',
                                              [{'text': 'content'}, {'imgPath': 'http://example.com/image'}])


def test_multi_wechat_id(mocker, mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        wechat_group='wechat_group', wechat_id='wechat_id1,wechat_id2', wechat_nick='wechat_nick',
        content='content',
        image_urls=['http://example.com/image'],
        send_channel='RPA_CLIENT',
        skip=False,
    ))
    mock_send_message = mocker.patch('rpa.mola.client.MolaClient.send_wechat_message',
                                     return_value=Err("error_message"))
    executor = WechatExecutor(MagicMock())
    mocker.patch.object(executor, 'job_wrapper')
    status, msg = executor.process()
    assert status == JobStatus.FAILED
    assert mock_send_message.call_count == 2
