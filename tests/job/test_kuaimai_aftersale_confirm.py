import pytest

from robot_processor.ext import db
from robot_processor.enums import JobStatus, ErpType
from robot_processor.job.kuaimai_confirm_after_sale import KuaimaiConfirmSaleExecutor
from rpa.erp.kuaimai import AfterSaleUploadRes, AfterSaleList, AfterSaleDetail, KuaimaiApiOrdersResp, KuaimaiOrder


@pytest.fixture
def mock_km_sdk_confirm_after_sale_order(mocker):
    p = mocker.patch("robot_processor.job.kuaimai_confirm_after_sale.KmSDK.confirm_after_sale_order")
    p.return_value = AfterSaleUploadRes(**{'traceId': '3260869895228315144', 'success': True, 'errors': []})


@pytest.fixture
def mock_get_after_sale_order_by_id(mocker):
    p = mocker.patch("robot_processor.job.kuaimai_confirm_after_sale.KmSDK.get_after_sale_order_by_id")
    p.return_value = AfterSaleList(
        list=[AfterSaleDetail(
            sid=111
        )]
    )
    yield p


# mock KuaimaiQmSDK get_orders
@pytest.fixture
def mock_get_orders(mocker):
    p = mocker.patch("robot_processor.job.kuaimai_after_sale_upload.KuaimaiQmSDK.get_orders")
    p.return_value = KuaimaiApiOrdersResp(
        trades=[KuaimaiOrder(tid="123456", sid=11, userId=1, type="4,14")]
    )


def test_aftersale_confirm(mock_full_job, mock_km_sdk_confirm_after_sale_order, mock_erp_info,
                           mock_get_after_sale_order_by_id, mock_get_orders, mock_job_args):
    mock_job_args(
        tid=[{"tid": "123"}]
    )
    mock_erp_info.erp_type = ErpType.KUAIMAI
    mock_full_job.shop.erps = [mock_erp_info]
    db.session.commit()
    exe = KuaimaiConfirmSaleExecutor(mock_full_job)
    status, _ = exe.process()
    assert status == JobStatus.SUCCEED
