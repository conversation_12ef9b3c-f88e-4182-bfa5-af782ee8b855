from robot_processor.ext import db
from robot_processor.job.after_sale_logistics import AfterSaleLogisticsExecutor


class TestGetTradeLogisticsInfo:
    def read_mock_response_from_resource(self):
        from pkg_resources import resource_stream
        from json import load

        with resource_stream(
            "tests.job.after_sale_logistics.guanyiyun",
            "response.guanyi-erp.get-trade-logisitics-info.json",
        ) as stream:
            return load(stream)

    def test(self, client, mock_erp_info, requests_mock):
        from robot_processor.shop.models import ErpInfo, ErpType
        from tests.testbed import BusinessOrderBuilder

        resource_name = "job.after-sale-logistics.guanyiyun.default"
        testbed_builder = BusinessOrderBuilder(resource_name, client.shop)
        testbed_builder.build()

        erp_info = ErpInfo()
        erp_info.shop = client.shop
        erp_info.erp_type = ErpType.GUANYIYUN
        db.session.add(erp_info)
        db.session.commit()

        requests_mock.post('/namespaces/guanyi-erp/methods/get-trade-logistics-info',
                           json=self.read_mock_response_from_resource())

        bo = testbed_builder.business_order
        job = bo.current_job
        mail_no_key = job.step.key_map["mail_no"]
        assert bo.data[mail_no_key] == "自动同步"

        executor = AfterSaleLogisticsExecutor(job)
        executor.process()

        db.session.commit()
        assert bo.data[mail_no_key] == "SF1698127702377"
