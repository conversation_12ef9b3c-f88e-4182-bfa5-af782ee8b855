import json
from pytest import fixture

from robot_processor.enums import JobStatus
from robot_processor.job.sto_intercept import StoInterceptExecutor, \
    StoConfirmInterceptExecutor


@fixture(autouse=True)
def init_logistics_errors_rule():
    from robot_processor.client.logistics_clients.exception_manager import config

    config._properties.update(
        {
            "logistics.errors_match_rule.sto": json.dumps(
                [
                    {
                        "error_type": "MATCH_ERROR_UNPICKED",
                        "error_code": [],
                        "regex": [".*该单号没有轨迹记录*."],
                        "suggestion": "不可进行拦截，快件未揽收",
                    },
                    {
                        "error_type": "MATCH_ERROR_SIGN",
                        "error_code": [],
                        "regex": [".*已签收*."],
                        "suggestion": "已签收",
                    },
                    {
                        "error_type": "MATCH_ERROR_INTERCEPTED",
                        "error_code": [],
                        "regex": [".*重复拦截*."],
                        "suggestion": "重复拦截",
                    },
                ]
            )
        }
    )
    yield


def test_cancel_logistic_success(
        mocker, mock_full_job,
        mock_job_args, mock_job_states
):
    mock_job_args(**{"waybillNo": "12345"})
    mocker.patch(
        "robot_processor.client.logistics_clients.sto_domain.StoClient.create_interception",
        return_value=(True, "", {
            "data": {
                "isAddSuccess": "true",
                "errorReason": "",
                "errorCode": "1111",
                "waybillNo": "12345"
            },
            "success": "true",
            "errorCode": "20100",
            "errorMsg": "请求成功"
        })
    )
    executor = StoInterceptExecutor(mock_full_job)
    success, _ = executor.process()


def test_cancel_logistic_failed(
        mocker, mock_full_job,
        mock_job_args, mock_job_states
):
    mock_job_args(**{"waybillNo": "12345"})
    mocker.patch(
        "robot_processor.client.logistics_clients.sto_domain.StoClient.create_interception",
        return_value=(False, None, {
            "data": {
                "isAddSuccess": "false",
                "errorReason": "error!error!",
                "errorCode": "1111",
                "waybillNo": "12345"
            },
            "success": "true",
            "errorCode": "20100",
            "errorMsg": "请求成功"
        })
    )
    executor = StoInterceptExecutor(mock_full_job)
    success, error_msg = executor.process()

    assert success == JobStatus.FAILED
    assert error_msg == "error!error!"


def test_cancel_logistic_failed_and_match_non_retry_error(
        mocker, mock_full_job,
        mock_job_args, mock_job_states
):
    mock_job_args(**{"waybillNo": "12345"})
    mocker.patch(
        "robot_processor.client.logistics_clients.sto_domain.StoClient.create_interception",
        return_value=(False, None, {
            "data": {
                "isAddSuccess": "false",
                "errorReason": "该单号没有轨迹记录",
                "errorCode": "1111",
                "waybillNo": "12345"
            },
            "success": "true",
            "errorCode": "20100",
            "errorMsg": "请求成功"
        })
    )
    executor = StoInterceptExecutor(mock_full_job)
    success, msg = executor.process()

    assert success == JobStatus.FAILED
    assert msg == "不可进行拦截，快件未揽收"


def test_cancel_logistic_failed_and_match_unknown_error(
        mocker, mock_full_job,
        mock_job_args, mock_job_states
):
    mock_job_args(**{"waybillNo": "12345"})
    mocker.patch(
        "robot_processor.client.logistics_clients.sto_domain.StoClient.create_interception",
        return_value=(False, None, {
            "data": {
                "isAddSuccess": "false",
                "errorReason": "未知的错误error!error!",
                "errorCode": "1111",
                "waybillNo": "12345"
            },
            "success": "true",
            "errorCode": "20100",
            "errorMsg": "请求成功"
        })
    )
    executor = StoInterceptExecutor(mock_full_job)
    success, msg = executor.process()

    assert success == JobStatus.FAILED
    assert msg == "未知的错误error!error!"


def test_cancel_logistic_failed_and_match_retry_error(
        mocker, mock_full_job,
        mock_job_args, mock_job_states
):
    mock_job_args(**{"waybillNo": "12345"})
    mocker.patch(
        "robot_processor.client.logistics_clients.sto_domain.StoClient.create_interception",
        return_value=(False, None, {
            "data": {
                "isAddSuccess": "false",
                "errorReason": "已签收",
                "errorCode": "1111",
                "waybillNo": "12345"
            },
            "success": "true",
            "errorCode": "20100",
            "errorMsg": "请求成功"
        })
    )
    executor = StoInterceptExecutor(mock_full_job)
    success, msg = executor.process()

    assert success == JobStatus.SUCCEED


def test_cancel_logistic_failed_and_match_intercepted_error(
        mocker, mock_full_job,
        mock_job_args, mock_job_states
):
    mock_job_args(**{"waybillNo": "12345"})
    mocker.patch(
        "robot_processor.client.logistics_clients.sto_domain.StoClient.create_interception",
        return_value=(False, None, {
            "data": {
                "isAddSuccess": "false",
                "errorReason": "重复拦截",
                "errorCode": "1111",
                "waybillNo": "12345"
            },
            "success": "true",
            "errorCode": "20100",
            "errorMsg": "请求成功"
        })
    )
    mocker.patch(
        "robot_processor.client.logistics_clients.logistics_client."
        "AbstractLogisticsIntercept.check_interception_success_by_callback",
        return_value=True
    )
    executor = StoInterceptExecutor(mock_full_job)
    success, msg = executor.process()

    assert success == JobStatus.SUCCEED


def test_confirm_status_running(
        mocker, mock_full_job,
        mock_job_args, mock_job_states
):
    mock_job_args(**{"waybillNo": "12345"})
    mocker.patch(
        "robot_processor.client.logistics_clients.sto_domain.StoClient.query_trace",
        return_value={
            "success": True,
            "errorCode": "ErrorCode 1",
            "errorMsg": "Error Msg Content",
            "needRetry": False,
            "requestId": "null",
            "expInfo": "null",
            "data": {
                "12345": [
                    {
                        "waybillNo": "12345",
                        "opOrgName": "青浦重固网点",
                        "opOrgCode": "900000",
                        "opOrgCityName": "上海",
                        "opOrgProvinceName": "上海",
                        "opOrgTel": "021-2871662",
                        "opTime": "2020-02-18 17:20:08",
                        "scanType": "拒收",
                        "opEmpName": "张三",
                        "opEmpCode": "007",
                        "memo": "快件已由【】收件",
                        "bizEmpName": "张三",
                        "bizEmpCode": "007",
                        "bizEmpPhone": "13838885038",
                        "bizEmpTel": "13838885038",
                        "nextOrgName": "上海转运中心",
                        "nextOrgCode": "900000",
                        "issueName": "客户拒收",
                        "signoffPeople": "张三",
                        "weight": 6.3,
                        "containerNo": "9005851482073",
                        "orderOrgCode": "361000",
                        "orderOrgName": "福建厦门公司",
                        "transportTaskNo": "83200297594",
                        "carNo": "川AG9966",
                        "opOrgTypeCode": "0003",
                        "partnerName": "null"
                    }
                ]
            }
        }
    )

    executor = StoConfirmInterceptExecutor(mock_full_job)
    success, msg = executor.process()

    assert success == JobStatus.RUNNING


def test_confirm_status_success(
        mocker, mock_full_job,
        mock_job_args, mock_job_states
):
    mock_job_args(**{"waybillNo": "12345"})
    mocker.patch(
        "robot_processor.client.logistics_clients.sto_domain.StoClient.query_trace",
        return_value={
            "success": True,
            "errorCode": "ErrorCode 1",
            "errorMsg": "Error Msg Content",
            "needRetry": False,
            "requestId": "null",
            "expInfo": "null",
            "data": {
                "12345": [
                    {
                        "waybillNo": "12345",
                        "opOrgName": "青浦重固网点",
                        "opOrgCode": "900000",
                        "opOrgCityName": "上海",
                        "opOrgProvinceName": "上海",
                        "opOrgTel": "021-2871662",
                        "opTime": "2020-02-18 17:20:08",
                        "scanType": "退回件",
                        "opEmpName": "张三",
                        "opEmpCode": "007",
                        "memo": "快件已由【】收件",
                        "bizEmpName": "张三",
                        "bizEmpCode": "007",
                        "bizEmpPhone": "13838885038",
                        "bizEmpTel": "13838885038",
                        "nextOrgName": "上海转运中心",
                        "nextOrgCode": "900000",
                        "issueName": "客户拒收",
                        "signoffPeople": "张三",
                        "weight": 6.3,
                        "containerNo": "9005851482073",
                        "orderOrgCode": "361000",
                        "orderOrgName": "福建厦门公司",
                        "transportTaskNo": "83200297594",
                        "carNo": "川AG9966",
                        "opOrgTypeCode": "0003",
                        "partnerName": "null"
                    }
                ]
            }
        }
    )

    executor = StoConfirmInterceptExecutor(mock_full_job)
    success, msg = executor.process()

    assert success == JobStatus.SUCCEED


def test_confirm_status_success_by_callback(
        mocker, mock_full_job, mock_job,
        mock_job_args, mock_job_states, client
):
    content = {
        "content": json.dumps({
            "interceptStatus": 1,
            "operateOrgName": "内蒙古呼伦贝尔公司",
            "operateTime": "1710201902000",
            "operatorName": "苏国欣",
            "waybillNo": "1234"
        })
    }
    client.post(
        "/v1/sto/intercept/callback",
        data=content,
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )

    mock_job_args(**{"waybillNo": "1234"})
    executor = StoConfirmInterceptExecutor(mock_full_job)
    success, msg = executor.process()

    assert success == JobStatus.SUCCEED
