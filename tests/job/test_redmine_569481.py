from unittest import mock

from robot_processor.enums import JobStatus
from robot_processor.job.redmine_569481 import Redmine569481Executor


def test_redmine_569481(mocker, client, mock_job_args, mock_full_job, mock_job_states):
    mocker.patch(
        'robot_processor.job.job_model_wrapper.JobModelWrapper.form_id',
        return_value=1)
    with mock.patch(
        "flask.current_app.config.get",
        new_callable=mock.PropertyMock,
    ) as ck:
        ck.return_value = {
            "1": {'fileName': '1.jpg', 'url': 'http://123'}
        }
        status, msg = Redmine569481Executor(mock_full_job).process()
        assert status == JobStatus.SUCCEED
        mock_job_states.write_job_output.assert_called_with(
            {'image': {'fileName': '1.jpg', 'url': 'http://123'}})


def test_redmine_569481_failed(mocker, client, mock_job_args, mock_full_job, mock_job_states):
    mocker.patch(
        'robot_processor.job.job_model_wrapper.JobModelWrapper.form_id',
        return_value=114514)
    status, msg = Redmine569481Executor(mock_full_job).process()
    assert status == JobStatus.FAILED
    assert msg == "没有图片配置"
