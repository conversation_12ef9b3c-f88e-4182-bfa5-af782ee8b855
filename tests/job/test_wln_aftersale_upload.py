import datetime

import pytest

from robot_processor.enums import ErpType, JobStatus
from robot_processor.job.wln_aftersale_upload import WlnCreateExchangeAPIExecutor
from rpa.erp.wln import WlnClient, TradeResp, Order, Item, WlnAfterSaleResp


@pytest.fixture
def mock_wln_client(client, mock_erp_info):
    mock_erp_info.shop_id = client.shop.id
    mock_erp_info.erp_type = ErpType.WANLINIU
    mock_erp_info.meta = {"co_id": "co_id", "app_key": "app_key", "app_secret": "app_secret"}
    yield WlnClient.init_by_sid(client.sid)


trade_resp = TradeResp(data=[Order(buyer_account='', uid='6B28A5D4D9A33D6595D0B7E596E0C1CF', buyer_mobile='',
                                   buyer='m**;AAHTW229AA2IH7T0WhNCKK0k', buyer_msg='', province='北京', city='北京市',
                                   district='东城区', town='安定门街道', additon='',
                                   create_time=datetime.datetime(2024, 2, 27, 16, 1, 5, tzinfo=datetime.timezone.utc),
                                   currency_code='', currency_sum='0.0', cycle_buy=None, discount_fee='0.0',
                                   end_time=datetime.datetime(2024, 3, 5, 15, 7, 46, tzinfo=datetime.timezone.utc),
                                   approve_time=datetime.datetime(2024, 3, 1, 5, 49, 42, tzinfo=datetime.timezone.utc),
                                   express_code='**************', shop_user_name=None, flag=5, has_refund=0,
                                   gx_origin_trade_id=None, invoice=None, remark=None, split_trade=0, sale_man=None,
                                   oper_apppove='林旭忠', oln_shop_id=None, allot_no=None,
                                   index_time=datetime.datetime(2024, 3, 5, 15, 10, 12, tzinfo=datetime.timezone.utc),
                                   oper_apppove_finance=None, oper_distribution=None, oper_inspection=None,
                                   oper_intimidate=None, oper_pack=None, oper_send=None, oper_weigh=None,
                                   seller_msg='82231722415PSL 预售4天2-28森林',
                                   send_time=datetime.datetime(2024, 3, 1, 8, 34, 23, tzinfo=datetime.timezone.utc),
                                   service_fee='0.0', shop_id=None, shop_name='安之伴旗舰店', shop_nick='安之伴旗舰店',
                                   shop_type='0', source_platform='天猫', status=3, storage_code='YB-DX',
                                   storage_name='物流总仓库', sum_sale='99.0',
                                   sys_shop='D79008D46A8D32A4BBB5EC4F48707D54', tel='', tp_tid='2068624776235702271',
                                   wave_no=None, trade_no='XD240228000047', trade_type=1, zip='000000',
                                   logistic_name='中通-淘系', orders=[
        Item(is_package=0, item_name='女士家居服套装', oln_item_id='82231722415+82231712412',   # noqa
             oln_item_code='82231722415+82231712412',
             oln_item_name='【李佳琦直播间】安之伴棉质睡衣情侣春秋长袖宽松大码男女家居服',
             oln_sku_name='颜色分类:秋黄色-女（预售4天）;尺码:L',
             item_platform_url='http://item.taobao.com/item.htm?id=764975713495',
             item_image_url='https://img.alicdn.com/bao/uploaded/i3/*********/O1CN01RAhYTw1YXC0Lzs9K4_!!*********.jpg',
             oln_sku_id='82231722415PSL', oln_status=3, order_id='EACCD62DE0F43B0C97E762FADA0F925F', payment='99.0',
             price='0.0', gx_payment='0.0', gx_price='0.0', receivable='0.0', remark='', size='1',
             sku_code='82031721780PSL', tax_payment='0.0', talent_id=None, talent_nick_name=None, traffic_sources=None,
             sub_order_no=None, tp_tid='2068624776235702271')])], code=0)


def test_process(mock_full_job, mock_job_args, mock_job_states, mock_wln_client, mocker):
    mocker.patch.object(WlnClient, "get_storage_name_by_code_in_cache", return_value="test")
    mocker.patch.object(WlnClient, "query_trades", return_value=trade_resp)
    mocker.patch.object(WlnClient, "aftersale_upload_with_req",
                        return_value=WlnAfterSaleResp(code=0, data="SH202408080123"))
    mock_job_args(**{
        "trade_no": [{"tid": "2068624776235702271", "oid": "1"}],
        "type_name": "补发",
        "storage_name": "汕头仓",
        "express_match_bill": 1,
        "refund_no": "123456",
        "reason_name": "质量问题",
        "out_goods": {
            "sku_list": [
                {
                    "sku": "11951120808CTM",
                    "spu": "11951120808",
                    "type": 1,
                    "count": 0,
                    "props": "",
                    "source": "erp",
                    "pic_url": "",
                    "quantity": 1,
                    "spu_name": "梭织纯棉素色夹棉带帽女套装",
                    "outer_sku": "11951120808CTM",
                    "outer_spu": "11951120808",
                    "spu_title": "梭织纯棉素色夹棉带帽女套装",
                    "item_title": "梭织纯棉素色夹棉带帽女套装",
                    "short_title": ""
                }
            ],
            "after_sales_type": "non_original"
        },
        "new_address": {"city": "三明市", "name": "肖翔", "town": "梅仙镇", "zone": "尤溪县", "state": "福建省",
                        "mobile": "17350543932", "address": "梅仙镇镇政府旁边", "district": "尤溪县"}
    })
    exe = WlnCreateExchangeAPIExecutor(mock_full_job)
    status, msg = exe.process()
    assert status == JobStatus.SUCCEED
    mock_job_states.write_job_widget_collection.assert_called_with({
        "refund_no": "SH202408080123"
    })


def test_process_without_storage_name(mock_full_job, mock_job_args, mock_job_states, mock_wln_client, mocker):
    mocker.patch.object(WlnClient, "query_trades", return_value=trade_resp)
    mocker.patch.object(WlnClient, "aftersale_upload_with_req",
                        return_value=WlnAfterSaleResp(code=0, data="SH202408080123"))
    mock_job_args(**{
        "trade_no": [{"tid": "2068624776235702271", "oid": "1"}],
        "type_name": "补发",
        "express_match_bill": 1,
        "refund_no": "123456",
        "reason_name": "质量问题",
        "out_goods": {
            "sku_list": [
                {
                    "sku": "11951120808CTM",
                    "spu": "11951120808",
                    "type": 1,
                    "count": 0,
                    "props": "",
                    "source": "erp",
                    "pic_url": "",
                    "quantity": 1,
                    "spu_name": "梭织纯棉素色夹棉带帽女套装",
                    "outer_sku": "11951120808CTM",
                    "outer_spu": "11951120808",
                    "spu_title": "梭织纯棉素色夹棉带帽女套装",
                    "item_title": "梭织纯棉素色夹棉带帽女套装",
                    "short_title": ""
                }
            ],
            "after_sales_type": "non_original"
        },
        "new_address": {"city": "三明市", "name": "肖翔", "town": "梅仙镇", "zone": "尤溪县", "state": "福建省",
                        "mobile": "17350543932", "address": "梅仙镇镇政府旁边", "district": "尤溪县"}
    })
    exe = WlnCreateExchangeAPIExecutor(mock_full_job)
    status, msg = exe.process()
    assert status == JobStatus.SUCCEED
    mock_job_states.write_job_widget_collection.assert_called_with({
        "refund_no": "SH202408080123"
    })
