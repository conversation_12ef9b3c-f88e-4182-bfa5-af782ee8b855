# flake8: noqa E501, W605
import json
from unittest.mock import call

import pytest

from robot_processor.enums import JobStatus
from robot_processor.job.jst_modify_wms import JstModifyWmsExecutor
from rpa.erp.jst import JstBaseOrder, WmsQueryResp

raw_order_data = """{"response": {"data_count": 4, "has_next": false, "orders": [
    {"as_id": "*********", "co_id": ********, "created": "2023-12-14 10:28:54", "creator_name": "李晓峰",
     "f_freight": "0", "f_weight": "0", "free_amount": "13.9", "is_cod": false, "items": [
        {"amount": "13.9", "base_price": "13.9", "i_id": "R吧凳", "is_gift": "false",
         "name": "滑轮矮凳家用万向轮门口懒人换鞋凳多功能美缝轮滑椅子坐凳小板凳", "oi_id": "38724587",
         "outer_oi_id": "$Asr-*********",
         "pic": "https:\/\/img.alicdn.com\/bao\/uploaded\/i4\/2466000425\/O1CN01WTZW3K1F0hBuh8m8d_!!2466000425.jpg",
         "price": "13.9", "properties_value": "【双层加固支架】米白色-静音防滑万向轮", "qty": 1,
         "raw_so_id": "3670776218371071950", "shop_sku_id": "", "sku_id": "R吧凳-米色-B07", "sku_type": "normal"}],
     "l_id": "", "lc_id": "YTO.1029", "link_o_id": "********", "logistics_company": "补件-高宠圆通专用",
     "modified": "2023-12-14 11:04:07", "node": ": 补发轮子*4 螺丝一套 安装工具*1", "o_id": 36722894,
     "oaid": "1w1lz50X7dwZiaUiaF1RNEvawMk4SxuBdDD9HP3Hb7vHaPvJUicJ1YNHKnkWmXNVIkynxIIpfY",
     "open_id": "AAHN8-dTABsHV6KE1LwI9OT3", "order_date": "2023-12-14 10:28:54", "order_from": "AS",
     "paid_amount": "0.0", "pay_amount": "0.0", "pay_date": "2023-12-14 10:28:54", "pays": [], "question_type": "",
     "receiver_city": "芜湖市", "receiver_district": "无为市", "receiver_state": "安徽省", "receiver_zip": "000000",
     "referrer_id": "", "referrer_name": "", "remark": "配件：补发轮子*4 螺丝一套 安装工具*1 ", "shop_buyer_id": "d**",
     "shop_id": ********, "shop_name": "TB劲德源", "shop_site": "淘宝天猫", "skus": "1.R吧凳-米色-B07",
     "so_id": "3670776218371071950", "status": "Delivering", "ts": 10982908854, "type": "补发订单", "weight": "1.6",
     "wms_co_id": ********},
    {"buyer_paid_amount": "11.85", "co_id": ********, "created": "2023-12-10 09:25:29", "f_freight": "0",
     "f_weight": "0", "free_amount": "2.05", "freight": "0.0", "is_cod": false, "is_split": true, "items": [
        {"amount": "13.9", "base_price": "27.8", "buyer_paid_amount": "11.85", "i_id": "R吧凳", "is_gift": "false",
         "is_presale": false, "item_pay_amount": "11.85", "item_status": "Sent",
         "name": "滑轮矮凳家用万向轮门口懒人换鞋凳多功能美缝轮滑椅子坐凳小板凳", "oi_id": "38575822",
         "outer_oi_id": "3670776218372071950",
         "pic": "http:\/\/img.alicdn.com\/bao\/uploaded\/i4\/2466000425\/O1CN01WTZW3K1F0hBuh8m8d_!!2466000425.jpg_30x30.jpg",
         "price": "13.9", "properties_value": "【双层米白架-加厚坐垫】❤米白色", "qty": 1,
         "raw_so_id": "3670776218371071950", "refund_status": "none", "shop_i_id": "745375267098",
         "shop_sku_id": "5309097706147", "sku_id": "R吧凳-米色-B07", "sku_type": "normal"}], "l_id": "YT7432966503312",
     "labels": "吧凳", "lc_id": "YTO.1028", "link_o_id": "********", "logistics_company": "高宠圆通专用",
     "modified": "2023-12-14 11:16:12", "o_id": ********,
     "oaid": "1w1lz50X7dwZiaUiaF1RNEvawMk4SxuBdDD9HP3Hb7vHaPvJUicJ1YNHKnkWmXNVIkynxIIpfY",
     "open_id": "AAHN8-dTABsHV6KE1LwI9OT3", "order_date": "2023-12-09 21:33:29", "order_from": "WAP,SPLIT",
     "paid_amount": "11.85", "pay_amount": "11.85", "pay_date": "2023-12-09 21:33:36", "pays": [
        {"amount": "11.85", "buyer_account": "****", "is_order_pay": true,
         "outer_pay_id": "2023120922001136661403670336", "pay_date": "2023-12-09 21:33:36", "pay_id": "********",
         "payment": "支付宝", "status": "Confirmed"}], "plan_delivery_date": "2023-12-11 21:33:36", "question_type": "",
     "receiver_city": "芜湖市", "receiver_district": "无为市", "receiver_state": "安徽省", "receiver_zip": "000000",
     "referrer_id": "", "referrer_name": "", "remark": "下架  补发轮子*4  螺丝一套 安装工具*1   张煜12.13",
     "seller_flag": "5", "send_date": "2023-12-10 10:14:39", "shop_buyer_id": "d**", "shop_id": ********,
     "shop_name": "TB劲德源", "shop_site": "淘宝天猫", "shop_status": "WAIT_BUYER_CONFIRM_GOODS",
     "skus": "1.R吧凳-米色-B07", "so_id": "3670776218371071950", "status": "Sent", "ts": ***********,
     "type": "普通订单", "weight": "1.6", "wms_co_id": ********},
    {"buyer_paid_amount": "5.88", "co_id": ********, "created": "2023-12-10 09:25:29", "f_freight": "0",
     "f_weight": "0", "free_amount": "1.02", "freight": "0.0", "is_cod": false, "is_split": true, "items": [
        {"amount": "6.9", "base_price": "13.8", "buyer_paid_amount": "5.88", "i_id": "云仓专用F塑料凳",
         "is_gift": "false", "is_presale": false, "item_pay_amount": "5.88", "item_status": "Cancelled",
         "name": "滑轮矮凳家用万向轮门口懒人换鞋凳多功能美缝轮滑椅子坐凳小板凳", "oi_id": "38575823",
         "outer_oi_id": "3670776218373071950",
         "pic": "http:\/\/img.alicdn.com\/bao\/uploaded\/i3\/2466000425\/O1CN01LNDD1R1F0hDU8yoVw_!!2466000425.jpg_30x30.jpg",
         "price": "6.9", "properties_value": "（无轮小号）黄色", "qty": 1, "raw_so_id": "3670776218371071950",
         "refund_status": "success", "remark": "F塑料凳-5096小号黄色-B07:->5096黄色:云仓专用F塑料凳",
         "shop_i_id": "745375267098", "shop_sku_id": "*************", "sku_id": "5096黄色", "sku_type": "normal"}],
     "labels": "发货前售后", "link_o_id": "********", "modified": "2023-12-14 11:16:12", "o_id": ********,
     "oaid": "1w1lz50X7dwZiaUiaF1RNEvawMk4SxuBdDD9HP3Hb7vHaPvJUicJ1YNHKnkWmXNVIkynxIIpfY",
     "open_id": "AAHN8-dTABsHV6KE1LwI9OT3", "order_date": "2023-12-09 21:33:29", "order_from": "WAP,SPLIT",
     "paid_amount": "5.88", "pay_amount": "5.88", "pay_date": "2023-12-09 21:33:36", "pays": [
        {"amount": "5.88", "buyer_account": "****", "is_order_pay": true,
         "outer_pay_id": "2023120922001136661403670336", "pay_date": "2023-12-09 21:33:36", "pay_id": "********",
         "payment": "支付宝", "status": "Confirmed"},
        {"amount": "5.88", "buyer_account": "****", "is_order_pay": false, "outer_pay_id": "250897179059075019",
         "pay_date": "2023-12-14 11:16:08", "pay_id": "********", "payment": "支付宝", "status": "Finished"}],
     "plan_delivery_date": "2023-12-11 21:33:36", "receiver_city": "芜湖市", "receiver_district": "无为市",
     "receiver_state": "安徽省", "receiver_zip": "000000", "referrer_id": "", "referrer_name": "",
     "remark": "下架  补发轮子*4  螺丝一套 安装工具*1   张煜12.13", "seller_flag": "5", "shop_buyer_id": "d**",
     "shop_id": ********, "shop_name": "TB劲德源", "shop_site": "淘宝天猫", "shop_status": "WAIT_BUYER_CONFIRM_GOODS",
     "skus": "0.", "so_id": "3670776218371071950", "status": "Cancelled", "ts": ***********, "type": "普通订单",
     "weight": "0", "wms_co_id": 0},
    {"buyer_paid_amount": "17.73", "co_id": ********, "created": "2023-12-09 21:33:33", "f_freight": "0",
     "f_weight": "0", "free_amount": "3.07", "freight": "0.0", "is_cod": false, "items": [
        {"amount": "13.9", "base_price": "27.8", "buyer_paid_amount": "11.85", "i_id": "R吧凳", "is_gift": "false",
         "is_presale": false, "item_pay_amount": "11.85", "item_status": "Sent",
         "name": "滑轮矮凳家用万向轮门口懒人换鞋凳多功能美缝轮滑椅子坐凳小板凳", "oi_id": "38565160",
         "outer_oi_id": "3670776218372071950",
         "pic": "http:\/\/img.alicdn.com\/bao\/uploaded\/i4\/2466000425\/O1CN01WTZW3K1F0hBuh8m8d_!!2466000425.jpg_30x30.jpg",
         "price": "13.9", "properties_value": "【双层米白架-加厚坐垫】❤米白色", "qty": 1,
         "raw_so_id": "3670776218371071950", "refund_status": "none", "shop_i_id": "745375267098",
         "shop_sku_id": "5309097706147", "sku_id": "R吧凳-米色-B07", "sku_type": "normal"},
        {"amount": "6.9", "base_price": "13.8", "buyer_paid_amount": "5.88", "i_id": "云仓专用F塑料凳",
         "is_gift": "false", "is_presale": false, "item_pay_amount": "5.88", "item_status": "Cancelled",
         "name": "滑轮矮凳家用万向轮门口懒人换鞋凳多功能美缝轮滑椅子坐凳小板凳", "oi_id": "38565161",
         "outer_oi_id": "3670776218373071950",
         "pic": "http:\/\/img.alicdn.com\/bao\/uploaded\/i3\/2466000425\/O1CN01LNDD1R1F0hDU8yoVw_!!2466000425.jpg_30x30.jpg",
         "price": "6.9", "properties_value": "（无轮小号）黄色", "qty": 1, "raw_so_id": "3670776218371071950",
         "refund_status": "success", "remark": "F塑料凳-5096小号黄色-B07:->5096黄色:云仓专用F塑料凳",
         "shop_i_id": "745375267098", "shop_sku_id": "*************", "sku_id": "5096黄色", "sku_type": "normal"}],
     "l_id": "@YT7432966503312", "labels": "发货前售后", "logistics_company": "圆通速递",
     "modified": "2023-12-14 11:16:12", "o_id": ********,
     "oaid": "1w1lz50X7dwZiaUiaF1RNEvawMk4SxuBdDD9HP3Hb7vHaPvJUicJ1YNHKnkWmXNVIkynxIIpfY",
     "open_id": "AAHN8-dTABsHV6KE1LwI9OT3", "order_date": "2023-12-09 21:33:29", "order_from": "WAP,WAP",
     "paid_amount": "17.73", "pay_amount": "17.73", "pay_date": "2023-12-09 21:33:36", "pays": [],
     "plan_delivery_date": "2023-12-11 21:33:36", "receiver_city": "芜湖市", "receiver_district": "无为市",
     "receiver_state": "安徽省", "receiver_zip": "000000", "referrer_id": "", "referrer_name": "",
     "remark": "下架  补发轮子*4  螺丝一套 安装工具*1   张煜12.13补发(36722894)#", "seller_flag": "5",
     "send_date": "2023-12-10 10:14:42", "shop_buyer_id": "d**", "shop_id": ********, "shop_name": "TB劲德源",
     "shop_site": "淘宝天猫", "shop_status": "WAIT_BUYER_CONFIRM_GOODS", "skus": "1.R吧凳-米色-B07",
     "so_id": "3670776218371071950", "status": "Split", "ts": 10982980346, "type": "普通订单", "weight": "1.6",
     "wms_co_id": 0}], "page_count": 1, "page_index": 1, "page_size": 4}}"""


def test_modify_wms(client, mock_full_job, jst_qm, jst_new_sdk, mock_job_args, mocker):
    # 此订单中有两个子订单，客服填写指定的子订单号
    orders = [JstBaseOrder(**i) for i in json.loads(raw_order_data)["response"]["orders"]]
    mocker.patch("robot_processor.job.jst_trade.TradesFinder.process", return_value=orders)
    p = mocker.patch("robot_processor.job.jst_trade.JstNewSDK.modify_wms")
    data = {
        "tid": [{"tid": "1589358686359781075", "oid": "3670776218372071950"}],
        "o_id": ********,
        "oi_id": True
    }
    mock_job_args(
        **data
    )
    exe = JstModifyWmsExecutor(mock_full_job)
    exe.process()
    assert p.call_args == call(********, ********)


@pytest.fixture
def mock_get_wms_info_by_org_id(mocker):
    p = mocker.patch("robot_processor.job.jst_trade.JstSDK.get_wms_info_by_org_id")
    p.return_value = WmsQueryResp(datas=[WmsQueryResp.WmsInfo(
        wms_co_id=********, name="圆通速递", co_id="123", is_main=False
    )])


def test_modify_wms_by_wms_name(client, mock_full_job, jst_qm, jst_new_sdk, mock_job_args, mocker,
                                mock_get_wms_info_by_org_id):
    orders = [JstBaseOrder(**i) for i in json.loads(raw_order_data)["response"]["orders"]]
    mocker.patch("robot_processor.job.jst_trade.TradesFinder.process", return_value=orders)
    mocker.patch("robot_processor.job.jst_trade.JstNewSDK.modify_wms")
    data = {
        "tid": [{"tid": "1589358686359781075", "oid": "3670776218372071950"}],
        "wms_co_name": "圆通速递"
    }
    mock_job_args(
        **data
    )
    exe = JstModifyWmsExecutor(mock_full_job)
    status, _ = exe.process()
    assert status == JobStatus.SUCCEED

