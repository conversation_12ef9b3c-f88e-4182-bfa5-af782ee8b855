import json
from os import path
from unittest.mock import MagicMock

from result import Ok, Err

from robot_processor.enums import JobStatus, ErpType
from robot_processor.job.trade import TradeExecutor
from rpa.erp.wdt import WarehouseQueryResp, Trade
from tests.client.test_mola import MockResponse

__curdir__ = path.dirname(__file__)


def test_wdt_process(mocker, client, mock_erp_type, mock_job_args, mock_full_job, mock_job_states,
                     ignore_token_bucket_rate_limit):
    mock_erp_type(ErpType.WDT)
    data = {"wdt_tid": [{"tid": "1549422229454689575BFe9be"}],
            "logistics_name": "勿动", "warehouse_no": "同步", "paid": "自动",
            "orginal_logistics_no": "123"}
    mock_job_args(**data)
    trade_executor = TradeExecutor(mock_full_job)

    # 1. 先测试获取不到 trade 的情况
    mocker.patch('rpa.erp.wdt.WdtClient.session.request', return_value=MockResponse(200, json.dumps({
        "response": {
            "errorcode": 0,
            "message": "ok",
            "total_count": 0,
            "trades": []
        }
    })))
    status, _ = trade_executor.process()
    assert status == JobStatus.FAILED

    # 2. 再获取到 trade 的情况
    def fake_request(method, url, params, *args, **kwargs):
        wdt_method = params['method']
        with open(path.join(__curdir__, f'mocks/response.{wdt_method}.json')) as f:
            return MockResponse(200, f.read())

    mocker.patch('rpa.erp.wdt.WdtClient.session.request', side_effect=fake_request)

    status, _ = trade_executor.process()
    mock_job_states.write_job_output.assert_called_with(
        {'logistics_name': '淘系安能9.1(瑞格)偏远新疆西藏海南内蒙甘肃宁夏青海（黄山淳安建德绩溪县）', 'paid': '68.0000', 'logistics_no': '760102101922',
         'warehouse_no': '77', 'goods_no': 'JZ-01', 'goods_name': '全身镜落地镜家用女北欧简约网红ins风女生卧室少女试衣穿衣镜子',
         'spec_no': 'JZ-01-椭圆-白', 'spec_name': '简约白,标准款-椭圆-不可旋转',
         'trade_status_zh': '已完成',
         'warehouse_name': '中口旋转木镜仓'}, allow_empty_value=False)
    assert status == JobStatus.SUCCEED


def test_can_not_get_wdt_warehouse_name(client, mock_erp_type, mock_job_args, mock_full_job, mocker):
    # 测试旺店通找不到分仓不报错，返回默认值
    mock_erp_type(ErpType.WDT)
    data = {"wdt_tid": [{"tid": "1549422229454689575BFe9be"}],
            "logistics_name": "勿动", "warehouse_no": "同步", "paid": "自动",
            "orginal_logistics_no": "123"}
    mock_job_args(**data)
    trade_executor = TradeExecutor(mock_full_job)
    trade_info = {"warehouse_no": "not_exist", "consign_status": 0}
    mocker.warehouse_query = MagicMock()
    mocker.warehouse_query.return_value = WarehouseQueryResp(**{
        'response': {'flag': 'failure', 'code': 15, 'message': 'Remote service error', 'sub_code': '2270',
                     'sub_message': '仓库编号在系统中不存在或已停用，请检查warehouse_no是否正确', 'request_id': '15squw6caqrdv'}})
    warehouse_name = trade_executor._get_wdt_warehouse_name(Trade(**trade_info), mocker)
    assert warehouse_name.startswith("找不到分仓信息，请检查分仓编码是否正确")


def test_wanliniu_process(mocker, requests_mock, client, mock_erp_type, mock_job_args, mock_full_job, mock_job_states,
                          ignore_token_bucket_rate_limit):
    mock_erp_type(ErpType.WANLINIU)
    data = {"tid": [{"tid": "JY20220533050071"}], "logistics_no": "自动",
            "logistics_name": "勿动", "warehouse_no": "同步", "pay_amount": "自动", "goods_title": "自动"}
    mock_job_args(**data)

    mock_data = '{"success":true,"message":"调用服务成功","code":200,' \
                '"result":{"data":{"data":[{"delivery_name":"申通-菜鸟","realPayment":10,' \
                '"express_uid":"773142224723191","storage_name":"雁周仓"}]}}}'
    requests_mock.post('/namespaces/wanliniu-erp/methods/get-orders-list', text=mock_data)
    mocker.patch(
        "rpa.erp.wanliniu.WanliniuClient.sku_of_trade_query",
        return_value=Ok({"goods_title": "[\'DS2180M\', \'DS2180M\']"})
    )

    status, _ = TradeExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED

    mocker.patch("rpa.erp.wanliniu.WanliniuClient.trade_query", return_value=Err("fail"))

    status, _ = TradeExecutor(mock_full_job).process()
    assert status == JobStatus.FAILED


def test_baisheng_process(mocker, client, mock_erp_type, mock_job_args, mock_full_job, mock_job_states,
                          ignore_token_bucket_rate_limit):
    mock_erp_type(ErpType.BAISHENG)
    mock_job_args(erp_tid="123456")

    from tests.client.test_mola import MockResponse
    mock_order_list = MockResponse(200, json.dumps({
        "status": "api-success",
        "message": "success",
        "data": {
            "page": {
                "totalResult": 1,
                "pageSize": 100,
                "pageNo": 1,
                "pageTotal": 1
            },
            "orderListGets": [
                {
                    "deal_code": "123456",
                    "shipping_name": "德邦快递",
                    "shipping_sn": "1324321797",
                    "fhck": "仓库1",
                    "payment": "111"
                }]}}))

    mocker.patch("rpa.erp.baisheng.BaiShengClient.get_order_list", return_value=mock_order_list)

    trade_executor = TradeExecutor(mock_full_job)
    status, _ = trade_executor.process()
    mock_job_states.write_job_output.assert_called_with({'logistics_corp': '德邦快递',
                                                         'logistics_no': '1324321797',
                                                         'payment': '111',
                                                         'warehouse': '仓库1'}, allow_empty_value=False)
    assert status == JobStatus.SUCCEED


def test_get_rate_limit_setting(client):
    mock_job = MagicMock()
    mock_job.business_order.sid = 'sid'
    executor = TradeExecutor(mock_job)
    assert executor.get_rate_limit_setting({}) is None
    assert ('JOB_LIMITER_TRADE_sid', '1/second') == executor.get_rate_limit_setting({'JOB_LIMITER_TRADE': '1/second'})

    executor.job_wrapper = MagicMock()
    executor.job_wrapper.shop.org_id = 'mock_org'
    assert ('JOB_LIMITER_TRADE_mock_org', '1/second') == \
           executor.get_rate_limit_setting({'JOB_LIMITER_TRADE': '1/second'})
