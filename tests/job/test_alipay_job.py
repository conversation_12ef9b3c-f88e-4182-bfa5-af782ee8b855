from unittest.mock import MagicMock

from robot_processor.enums import JobStatus
from robot_processor.job.alipay import <PERSON>payExecutor


def test_process_fail(mocker, requests_mock, mock_job_args):
    mock_job_args(tid=[{'tid': '123'}], amount='2.5', payment_reason='test',
                  pic_url='http://example.com/img.png', transfer_status='10000',
                  receive_info={'receive_name': 'receive_name', 'receive_account': 'receive_account',
                                'payment_method': '3'})
    mock_job, mock_job_wrapper = MagicMock(), MagicMock()
    executor = AlipayExecutor(mock_job)
    executor.job_wrapper = mock_job_wrapper
    mock_job.id = 1
    mock_job_wrapper.order.id = 1
    mock_job_wrapper.order.sid = 'sid'
    mock_job_wrapper.order.uid = 'uid'
    mock_job_wrapper.order.aid = 'aid'
    mocker.patch('robot_processor.client.transfer.RobotTransferClient.endpoint', new_callable=lambda: 'http://pay.com')
    requests_mock.post('http://pay.com/rpa/transfers', json={})
    status, msg = executor.process()
    assert status == JobStatus.FAILED


def test_process_succeed(mocker, requests_mock, mock_job_args, mock_action_client):
    mock_job_args(tid=[{'tid': '123'}], amount='2.5', payment_reason='test',
                  comment='test', pic_url='http://example.com/img.png', transfer_status='1',
                  receive_info={'receive_name': 'receive_name', 'receive_account': 'receive_account',
                                'receive_usernick': 'usernick', 'payment_method': '3'})
    mock_job, mock_job_wrapper = MagicMock(), MagicMock()
    executor = AlipayExecutor(mock_job)
    executor.job_wrapper = mock_job_wrapper
    mock_job.id = 1
    mock_job_wrapper.order.id = 1
    mock_job_wrapper.order.sid = 'sid'
    mock_job_wrapper.order.uid = 'uid'
    mock_job_wrapper.order.aid = 'aid'
    mocker.patch('robot_processor.client.transfer.RobotTransferClient.endpoint', new_callable=lambda: 'http://pay.com')
    mock_post = requests_mock.post('http://pay.com/rpa/transfers',
                                   json={'success': True, 'transfer_id': 'transfer_id', 'is_new': True})
    status, msg = executor.process()
    assert status == JobStatus.SUCCEED
    mock_action_client.create_action_log_by_http.assert_called_once()
    action_log_info = mock_action_client.create_action_log_by_http.call_args[0][0]
    assert isinstance(action_log_info, dict)
    assert action_log_info['object_id'] == 'transfer_id'
    assert len(mock_post.request_history) == 1
    req = mock_post.request_history[0]
    body = req.json()
    assert body['sid'] == 'sid'
    assert body['receive_name'] == 'receive_name'
    assert body['receive_usernick'] == 'usernick'
    assert body['payment_method'] == '3'


def test_process_old_style_receive_info(mocker, requests_mock, mock_job_args, mock_action_client):
    mock_job_args(tid=[{'tid': '123'}], amount='2.5', payment_reason='test',
                  comment=[{'label': 'test', 'value': 'test'}], pic_url='http://example.com/img.png',
                  transfer_status='1',
                  receive_name='receive_name', receive_account='receive_account')
    mock_job, mock_job_wrapper = MagicMock(), MagicMock()
    executor = AlipayExecutor(mock_job)
    executor.job_wrapper = mock_job_wrapper
    mock_job.id = 1
    mock_job_wrapper.order.id = 1
    mock_job_wrapper.order.sid = 'sid'
    mock_job_wrapper.order.uid = 'uid'
    mock_job_wrapper.order.aid = 'aid'
    mocker.patch('robot_processor.client.transfer.RobotTransferClient.endpoint', new_callable=lambda: 'http://pay.com')
    mock_post = requests_mock.post('http://pay.com/rpa/transfers',
                                   json={'success': True, 'transfer_id': 'transfer_id', 'is_new': True})
    status, msg = executor.process()
    assert status == JobStatus.SUCCEED
    mock_action_client.create_action_log_by_http.assert_called_once()
    action_log_info = mock_action_client.create_action_log_by_http.call_args[0][0]
    assert isinstance(action_log_info, dict)
    assert action_log_info['object_id'] == 'transfer_id'
    assert len(mock_post.request_history) == 1
    req = mock_post.request_history[0]
    body = req.json()
    assert body['sid'] == 'sid'
    assert body['receive_name'] == 'receive_name'
    assert body['payment_method'] == 2
    assert body['comment'] == ['test']


def test_amount_less_than_1(mocker, requests_mock, mock_job_args):
    mock_job_args(tid=[{'tid': '123'}], amount='0.5', payment_reason='test',
                  pic_url='http://example.com/img.png', transfer_status='10000',
                  receive_info={'receive_name': 'receive_name',
                                'receive_account': 'receive_account',
                                'payment_method': '3'})
    mock_job, mock_job_wrapper = MagicMock(), MagicMock()
    executor = AlipayExecutor(mock_job)
    executor.job_wrapper = mock_job_wrapper
    mock_job.id = 1
    mock_job_wrapper.order.id = 1
    mock_job_wrapper.order.sid = 'sid'
    mock_job_wrapper.order.uid = 'uid'
    mock_job_wrapper.order.aid = 'aid'
    mocker.patch('robot_processor.client.transfer.RobotTransferClient.endpoint',
                 new_callable=lambda: 'http://pay.com')
    requests_mock.post('http://pay.com/rpa/transfers', json={})
    status, msg = executor.process()
    assert status == JobStatus.FAILED
    assert msg.startswith("打款金额不能小于1元")
