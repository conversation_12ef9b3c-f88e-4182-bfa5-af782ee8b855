from collections import namedtuple

import arrow
from pytest import fixture
from faker import Faker

from robot_processor.enums import JobStatus
from robot_processor.db import db
from robot_processor.refund.models import TaobaoRefund
from robot_processor.form.models import WidgetInfo
from robot_processor.job.taobao_refund_info import TaobaoRefundInfoExecutor
from tests.job.conftest import MockJobStates as DefaultMockJobStates

fake = Faker("zh_CN")


def test缺少refundId信息(mocker, mock_full_job):
    executor = TaobaoRefundInfoExecutor(mock_full_job)
    status, err = executor.process()
    assert status == JobStatus.FAILED
    assert err == "缺失 Refund ID 信息"


def test未找到退款信息(mocker, mock_full_job, mock_job_args, mock_job_states):
    mock_job_args(refund_id=123)
    executor = TaobaoRefundInfoExecutor(mock_full_job)
    status, err = executor.process()
    assert status == JobStatus.FAILED
    assert err == "未找到退款信息"


@fixture
def mock_taobao_refund():
    refund_record = TaobaoRefund()
    refund_record.refund_id = fake.pyint()
    refund_record.seller_nick = "ut"
    refund_record.buyer_nick = "ut"
    refund_record.tid = 123
    refund_record.oid = 123
    refund_record.jdp_response = {"refund_get_response": {"refund": {"sid": "YT123", "status": "WAIT_SELLER_AGREE"}}}
    refund_record.jdp_created = arrow.now().datetime
    refund_record.jdp_modified = arrow.now().datetime
    refund_record.status = 'SUCCESS'
    db.session.add(refund_record)
    db.session.commit()
    yield refund_record


class MissingEmpty(DefaultMockJobStates):
    def _write_job_widget_collection(self, data):
        return namedtuple("WriteResult", "missing")(missing=[])


def test成功(mock_taobao_refund, mock_full_job, mock_job_args, mocker):
    states = MissingEmpty({})
    mocker.patch('robot_processor.job.base.JobControllerBase.states', new_callable=lambda: states)

    mock_job_args(refund_id=mock_taobao_refund.refund_id)
    executor = TaobaoRefundInfoExecutor(mock_full_job)
    status, err = executor.process()
    assert status == JobStatus.SUCCEED
    assert err is None


class MissingLogisticsNo(DefaultMockJobStates):
    def _write_job_widget_collection(self, data):
        miss_arg = WidgetInfo.View.LeafFromRootPath(
            routes=[WidgetInfo.View.RawStep(
                key="", type="",
                option_value={"label": "退货快递单号"}
            )]
        )
        return namedtuple("WriteResult", "missing")(
            missing=[miss_arg]
        )


def test未获取到信息(mock_taobao_refund, mock_full_job, mock_job_args, mocker):
    states = MissingLogisticsNo({})
    mocker.patch('robot_processor.job.base.JobControllerBase.states', new_callable=lambda: states)

    mock_job_args(refund_id=mock_taobao_refund.refund_id)
    executor = TaobaoRefundInfoExecutor(mock_full_job)
    status, err = executor.process()
    assert status == JobStatus.FAILED
    assert err == "获取 退货快递单号 信息失败"
