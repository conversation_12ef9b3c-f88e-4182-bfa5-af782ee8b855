from robot_processor.enums import ErpType, JobStatus
from robot_processor.job.wln_modify_trade_remark import WlnModifyTradeRemarkExecutor
from rpa.erp.wln.schemas import WlnModifyTradeRemarkResp


def test_process(client, mocker, mock_erp_info, mock_job_args, mock_full_job, mock_job_states):
    mock_erp_info.erp_type = ErpType.WANLINIU
    mock_erp_info.meta["app_key"] = "ut"
    mock_erp_info.meta["app_secret"] = "ut"
    mock_job_args(bill_code="XD12131312", remark="12312")
    mocker.patch(
        "rpa.erp.wln.wln.WlnClient.modify_trade_remark_with_req",
        return_value=WlnModifyTradeRemarkResp(**{"code": 0, "data": True})
    )
    status, _ = WlnModifyTradeRemarkExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED

    mock_job_states.write_job_widget_collection.assert_called_with({"result": "成功"})
