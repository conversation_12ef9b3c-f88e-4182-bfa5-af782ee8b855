from result import Ok

from robot_processor.enums import JobStatus, ErpType
from robot_processor.job.after_sale_logistics import AfterSaleLogisticsExecutor
from rpa.erp.wdt import TradeQueryResp, WarehouseQueryResp


def test_wanliniu_process(client, mock_erp_type, mock_full_job, mocker, mock_job_args, mock_job_states):
    mock_erp_type(ErpType.WANLINIU)
    mock_job_args(after_sale_tid="")
    status, exc = AfterSaleLogisticsExecutor(mock_full_job).process()
    assert status == JobStatus.FAILED
    assert exc.startswith("缺失 after_sale_tid 字段")

    mock_job_args(after_sale_tid="123")
    logistics_order = {"logistics_corp": "1", "exp_tpl_id": "3", "logistics_order": "2"}
    mocker.patch(
        "rpa.erp.wanliniu.WanliniuClient.after_sale_logistics",
        return_value=Ok(logistics_order)
    )
    status, msg = AfterSaleLogisticsExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED
    mock_job_states.write_job_output.assert_called_with(logistics_order)


def test_kuaimai_process(client, mock_erp_type, mock_full_job, mocker, mock_job_args, mock_job_states):
    mock_erp_type(ErpType.KUAIMAI)
    status, exc = AfterSaleLogisticsExecutor(mock_full_job).process()
    assert status == JobStatus.FAILED
    assert exc.startswith('缺失 after_sale_tid 字段')

    logistics_order = {"logistics_corp": "1", "exp_tpl_id": "3", "logistics_order": "2"}
    mocker.patch(
        "rpa.erp.kuaimai.KuaimaiClient.after_sale_logistics",
        return_value=Ok(logistics_order)
    )

    mock_job_args(after_sale_tid='XD211125003541')
    status, msg = AfterSaleLogisticsExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED
    mock_job_states.write_job_output.assert_called_with(logistics_order)


WDT_TRADE_QUERY_RESPONSE = '''
{
  "response": {
    "errorcode": 0,
    "message": "ok",
    "total_count": 1,
    "trades": [
      {
        "bad_reason": 0,
        "buyer_message": "无痕发货",
        "buyer_message_count": "1",
        "buyer_nick": "",
        "cancel_reason": "0",
        "check_step": "0",
        "checker_id": 211,
        "checker_name": "高怡",
        "checkouter_id": 0,
        "checkouter_name": "",
        "cod_amount": "0.0000",
        "commission": "3.7400",
        "consign_status": 12,
        "created": "2023-03-20 00:56:15",
        "cs_remark": "",
        "cs_remark_change_count": "0",
        "cs_remark_count": "0",
        "currency": "",
        "customer_id": "4553846",
        "customer_name": "",
        "customer_no": "KH202303200265",
        "customer_type": "0",
        "dap_amount": "68.0000",
        "delay_to_time": "0",
        "delivery_term": 1,
        "discount": "108.0000",
        "discount_change": "0.0000",
        "ext_cod_fee": "0.0000",
        "fchecker_id": 0,
        "fchecker_name": "系统",
        "fenxiao_nick": "",
        "fenxiao_tid": "",
        "fenxiao_type": 0,
        "flag_id": "0",
        "flag_name": "",
        "freeze_reason": 0,
        "freeze_reason_info": "",
        "fullname": "系统",
        "gift_mask": "0",
        "goods_amount": "176.0000",
        "goods_cost": "51.0000",
        "goods_count": "1.0000",
        "goods_list": [
          {
            "actual_num": "1.0000",
            "adjust": "0.0000",
            "api_goods_name": "全身镜穿衣落地镜女生家用卧室试衣镜ins风旋转可移动立体大镜子",
            "api_spec_name": "标准款-椭圆简约白;其他;是",
            "barcode": "",
            "base_unit_id": 0,
            "bind_oid": "",
            "cid": 200,
            "class_name": "无",
            "commission": "3.7400",
            "created": "2023-03-20 00:56:15",
            "delivery_term": 1,
            "discount": "108.0000",
            "flag": 0,
            "from_mask": 1,
            "gift_type": 0,
            "goods_id": 177,
            "goods_name": "全身镜落地镜家用女北欧简约网红ins风女生卧室少女试衣穿衣镜子",
            "goods_no": "JZ-01",
            "goods_type": 1,
            "guarantee_mode": "1",
            "invoice_content": "",
            "invoice_type": 0,
            "is_consigned": "1",
            "is_master": "1",
            "is_print_suite": "0",
            "is_received": "1",
            "is_zero_cost": "1",
            "large_type": 0,
            "modified": "2023-03-30 12:10:16",
            "num": "1.0000",
            "order_price": "68.0000",
            "paid": "68.0000",
            "pay_id": "2023032022001157021420779763",
            "pay_status": "2",
            "pay_time": "2023-03-20 00:56:07",
            "platform_goods_id": "655629585687",
            "platform_id": 1,
            "platform_spec_id": "4908218331987",
            "price": "176.0000",
            "prop2": "",
            "rec_id": 11179103,
            "refund_num": "0.0000",
            "refund_status": 0,
            "remark": "",
            "share_amount": "68.0000",
            "share_amount2": "0.0000",
            "share_post": "0.0000",
            "share_price": "68.0000",
            "spec_code": "",
            "spec_id": 5541,
            "spec_name": "简约白,标准款-椭圆-不可旋转",
            "spec_no": "JZ-01-椭圆-白",
            "src_oid": "3268940115984170159",
            "src_tid": "3268940115984170159",
            "stock_reserved": "0",
            "suite_amount": "0.0000",
            "suite_discount": "0.0000",
            "suite_id": 0,
            "suite_name": "",
            "suite_no": "",
            "suite_num": "0.0000",
            "tax_rate": "0.0000",
            "tc_order_id": "",
            "trade_id": 9706139,
            "unit_name": "",
            "weight": "9.5000"
          }
        ],
        "goods_type_count": 1,
        "id_card": "",
        "id_card_type": 0,
        "invoice_content": "",
        "invoice_id": 0,
        "invoice_title": "",
        "invoice_type": 0,
        "is_prev_notify": "0",
        "is_sealed": "0",
        "is_unpayment_sms": "0",
        "large_type": "0",
        "logistics_code": "646",
        "logistics_id": 734,
        "logistics_name": "淘系安能9.1(瑞格)偏远新疆西藏海南内蒙甘肃宁夏青海（黄山淳安建德绩溪县）",
        "logistics_no": "************",
        "logistics_template_id": "0",
        "logistics_type": 95,
        "modified": "2023-03-30 12:10:16",
        "note_count": "0",
        "other_amount": "0.0000",
        "other_cost": "0.0000",
        "package_id": "0",
        "paid": "68.0000",
        "pay_account": "",
        "pay_time": "2023-03-20 00:56:07",
        "pi_amount": "0.0000",
        "platform_id": 1,
        "post_amount": "0.0000",
        "post_cost": "15.7300",
        "pre_charge_time": "",
        "print_remark": "",
        "profit": "-2.4700",
        "raw_goods_count": "1.0000",
        "raw_goods_type_count": 1,
        "receivable": "68.0000",
        "receiver_address": "",
        "receiver_area": "江苏省 南通市 通州区",
        "receiver_city": 320600,
        "receiver_country": "0",
        "receiver_district": 320612,
        "receiver_dtb": " ",
        "receiver_mobile": "",
        "receiver_name": "",
        "receiver_province": "320000",
        "receiver_ring": "叶**",
        "receiver_telno": "",
        "receiver_zip": "000000",
        "refund_status": 0,
        "remark_flag": 0,
        "reserve": "",
        "revert_reason": "0",
        "sales_score": "0",
        "salesman_id": 0,
        "sendbill_template_id": "0",
        "shop_id": "8",
        "shop_name": "誉斯嘉旗舰店",
        "shop_no": "008",
        "shop_platform_id": "1",
        "shop_remark": "",
        "single_spec_no": "JZ-01-椭圆-白",
        "split_from_trade_id": "0",
        "split_package_num": "0",
        "src_tids": "3268940115984170159",
        "stockout_no": "CK202303208223",
        "tax": "0.0000",
        "tax_rate": "0.0000",
        "to_deliver_time": "",
        "trade_from": 1,
        "trade_id": 9706139,
        "trade_mask": "131200",
        "trade_no": "JY202303200266",
        "trade_prepay": "0.0000",
        "trade_status": 110,
        "trade_time": "2023-03-20 00:56:01",
        "trade_type": 1,
        "unmerge_mask": "0",
        "version_id": 5,
        "volume": "42385.0000",
        "warehouse_id": "82",
        "warehouse_no": "77",
        "warehouse_type": 1,
        "weight": "9.5000"
      }
    ]
  }
}
'''

WDT_WAREHOUSE_QUERY_RESPONSE = '''
{
  "response": {
    "errorcode": 0,
    "message": "ok",
    "total_count": 1,
    "warehouses": [
      {
        "address": "",
        "api_key": "",
        "api_object_id": "",
        "city": "无",
        "cod_logistics_id": "0",
        "contact": "",
        "coordinates_x": "0.000000",
        "coordinates_y": "0.000000",
        "created": "2018-09-12 16:40:23",
        "district": "无",
        "division_id": "",
        "ext_warehouse_no": "",
        "flag": "0",
        "is_defect": 0,
        "is_disabled": 0,
        "is_outer_goods": "0",
        "is_outer_stock": "0",
        "match_warehouse_id": "0",
        "mobile": "",
        "modified": "2023-03-07 09:34:19",
        "name": "顺果",
        "picker_num": "0",
        "priority": "57",
        "prop1": "",
        "prop2": "",
        "province": "",
        "remark": "",
        "shop_id": "0",
        "sub_type": "0",
        "tag": "1536741623",
        "telno": "",
        "type": 1,
        "warehouse_id": "43",
        "warehouse_no": "0-31",
        "warehouse_type": "1",
        "zip": ""
      }
    ]
  }
}
'''


def test_wdt_process(client, mock_erp_type, mock_full_job, mocker, mock_job_args, mock_job_states):
    mock_erp_type(ErpType.WDT)
    mock_job_args(wdt_tid="AT202112030001")

    mocker.patch("rpa.erp.wdt.WdtClient.trade_query",
                 return_value=TradeQueryResp(**eval(WDT_TRADE_QUERY_RESPONSE)))
    mocker.patch("rpa.erp.wdt.WdtClient.warehouse_query",
                 return_value=WarehouseQueryResp(**eval(WDT_WAREHOUSE_QUERY_RESPONSE)))
    status, msg = AfterSaleLogisticsExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED


def test_wdtulti_process(client, mock_erp_type, mock_full_job, mocker, mock_job_args, mock_job_states):
    mock_erp_type(ErpType.WDTULTI)
    mock_job_args(wdt_tid="xxx")

    logistics = {
        "wms_co_id": "仓库编号一",
        "wms_co_name": "仓库一",
        "shop_site": "天猫淘宝",
        "pay_amount": "300",
        "l_id": "lno1235566",
        "l_international_id": "3",
        "logistics_company": "物流公司三",
    }
    mocker.patch(
        "rpa.erp.wdtulti.WdtUltiClient.after_sale_logistics",
        return_value=Ok(logistics)
    )
    status, msg = AfterSaleLogisticsExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED
    mock_job_states.write_job_output.assert_called_with(logistics)


def test_guanyiyun_process(client, mock_erp_type, mock_full_job, mocker, mock_job_args, mock_job_states):
    mock_job_args(tid=[{"tid": "1"}])
    mock_erp_type(ErpType.GUANYIYUN)
    mocker.patch(
        "rpa.erp.guanyiyun.GuanyiyunClient.get_trade_logistics_info",
        return_value=Ok([{"mailNo": "123"}])
    )
    status, msg = AfterSaleLogisticsExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED
    mock_job_states.write_job_output.assert_called_with({"mail_no": "123"}, allow_empty_value=False)
