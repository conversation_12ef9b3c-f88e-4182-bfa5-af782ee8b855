from flask import current_app
from pytest import fixture
from result import Ok

from robot_processor.enums import JobStatus
from robot_processor.job.job_model_wrapper import SendMessageArguments
from robot_processor.job.taobao_sms_send_by_oaid import \
    TaobaoSMSSendByOaidExecutor
from robot_processor.sms.models import SMSSendRecord


@fixture
def patch_config(mock_full_job):
    config = current_app.config.get("LXK_ACCESS_TOKENS", {}).copy()
    current_app.config["LXK_ACCESS_TOKENS"] = {mock_full_job.shop.sid: "1"}
    sign_config = current_app.config.get("SMS_SIGN", {}).copy()
    current_app.config["SMS_SIGN"] = {mock_full_job.shop.sid: "1"}
    yield
    current_app.config["LXK_ACCESS_TOKENS"] = config
    current_app.config["SMS_SIGN"] = sign_config


def test_send_sms_by_oaid(mocker, client,
                          mock_full_job, mock_job_args, patch_config):
    tid = [{"tid": "1", "oid": "2"}]
    oaid = "oaid"
    mock_job_args(
        branch=SendMessageArguments(
            content='content',
            skip=False,
            image_urls=[],
            send_channel='RPA_CLIENT',
        ),
        tid=tid,
        oaid=oaid
    )
    mocker.patch('robot_processor.job.taobao_sms_send_by_oaid.get_taobao_trade',
                 return_value=[{"trade_id": "1", "oaid": oaid}])

    mocker.patch(
        'robot_processor.client.taobao.TaobaoClient.sms_send_by_oaid',
        return_value=Ok({
            "module": "uid"
        }))

    sms_send_executor = TaobaoSMSSendByOaidExecutor(mock_full_job)
    status, _ = sms_send_executor.process()
    assert status == JobStatus.SUCCEED
    records = SMSSendRecord.query.all()
    assert len(records) == 1
