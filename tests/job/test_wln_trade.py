import pytest

from robot_processor.enums import JobStatus, ErpType
from robot_processor.job.erp_trade_handler.wln_trade import WlnTradeExecutor
from rpa.erp.wln import WlnClient


@pytest.fixture
def mock_wln_client(client, mock_erp_info, mocker):
    mock_erp_info.shop_id = client.shop.id
    mock_erp_info.erp_type = ErpType.WANLINIU
    mock_erp_info.meta = {"co_id": "co_id", "app_key": "app_key", "app_secret": "app_secret"}
    wc = WlnClient.init_by_sid(client.sid)
    mocker.patch("robot_processor.job.wln_aftersale_upload.WlnClient", return_value=wc)


def test_wln_trade_process(mock_full_job, mocker, mock_job_args, mock_job_states, mock_wln_client):
    mocker.patch("robot_processor.job.erp_trade_handler.wln_trade.add_goods_code")
    from tests.job.test_wln_aftersale_upload import trade_resp
    mock_job_args(**{
        "tid": [{"tid": "2068624776235702271", "oid": "1"}],
    })
    mocker.patch.object(WlnClient, "query_trades", return_value=trade_resp)
    exe = WlnTradeExecutor(mock_full_job)
    status, _ = exe.process()
    assert status == JobStatus.SUCCEED
    assert mock_job_states.write_job_widget_collection.call_count == 1

    mock_job_args(**{
        "tid": [{"tid": "2068624776235702271", "oid": "1"}],
        "trade_no": "XD240228000047"
    })
    mocker.patch.object(WlnClient, "query_trades", return_value=trade_resp)
    exe = WlnTradeExecutor(mock_full_job)
    status, _ = exe.process()
    assert status == JobStatus.SUCCEED
    assert mock_job_states.write_job_widget_collection.call_count == 2

    mock_job_args(**{
        "tid": [{"tid": "2068624776235702271", "oid": "1"}],
        "trade_no": "XD24022800004x"
    })
    mocker.patch.object(WlnClient, "query_trades", return_value=trade_resp)
    exe = WlnTradeExecutor(mock_full_job)
    status, msg = exe.process()
    assert status == JobStatus.FAILED
    assert msg == "未找到符合条件的订单信息"
    assert mock_job_states.write_job_widget_collection.call_count == 2


def test_wln_trade_process_with_express_no(mock_full_job, mocker, mock_job_args, mock_job_states, mock_wln_client):
    mocker.patch("robot_processor.job.erp_trade_handler.wln_trade.add_goods_code")
    from tests.job.test_wln_aftersale_upload import TradeResp
    mock_job_args(**{
        "tid": [{"tid": "2068624776235702271", "oid": "1"}],
        "waybill_no": "114514"
    })
    mocker.patch.object(WlnClient, "query_trades", return_value=TradeResp(data=[], code=0))
    exe = WlnTradeExecutor(mock_full_job)
    status, err = exe.process()
    assert status == JobStatus.FAILED
    assert err == "未找到订单"
