from robot_processor.enums import ErpType, JobStatus
from robot_processor.job.jst_wms_co_id_upload import JstWmsCoIdUploadExecutor
from rpa.erp.jst import WmsQueryResp, OrdersModifyWmsUploadResp


def test_process(client, mocker, mock_erp_type, mock_job_args, mock_full_job):
    mock_erp_type(ErpType.JST)
    mock_job_args(o_id=1123, wms_co_name="测试仓")
    mocker.patch("rpa.erp.jst.JstSDK.get_wms_info_by_org_id",
                 return_value=WmsQueryResp(datas=[WmsQueryResp.WmsInfo(
                     name="测试仓", wms_co_id=123, co_id=1, is_main=False)]))
    patcher = mocker.patch("rpa.erp.jst.JstNewSDK.orders_modifywms_upload",
                           return_value=OrdersModifyWmsUploadResp(issuccess=""))
    status, msg = JstWmsCoIdUploadExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED
    patcher.assert_called_once()


def test_fail_process(client, mocker, mock_erp_type, mock_job_args, mock_full_job):
    mock_erp_type(ErpType.JST)
    mock_job_args(o_id=1123, wms_co_name="测试仓")
    mocker.patch("rpa.erp.jst.JstSDK.get_wms_info_by_org_id",
                 return_value=WmsQueryResp(datas=[WmsQueryResp.WmsInfo(
                     name="测试仓1", wms_co_id=123, co_id=1, is_main=False)]))
    mocker.patch("rpa.erp.jst.JstNewSDK.orders_modifywms_upload",
                 return_value=OrdersModifyWmsUploadResp(issuccess=""))
    status, msg = JstWmsCoIdUploadExecutor(mock_full_job).process()
    assert status == JobStatus.FAILED
