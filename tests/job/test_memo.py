import json
from typing import NamedTuple

from leyan_proto.digismart.trade.dgt_trade_pb2 import (
    UpdateTradeMemoResponse,
    ResponseCode,
)
from pytest import fixture

from robot_processor.business_order.models import Job
from robot_processor.enums import JobStatus
from robot_processor.job.memo import MemoExecutor
from tests.testbed import BusinessOrderBuilder


@fixture
def testbed(client, mock_grant_record, db):
    # tests/job/resources/memo.default.json
    client.shop.records.append(mock_grant_record)
    db.session.commit()
    testbed = BusinessOrderBuilder(
        {
            "form_template": "memo.default",
            "aid": "ut",
            "data": {
                "oid": "1961782599999915238",
                "tid": "196178259995238",
                "37f4a347-d700-443b-b03e-6503138e05e7": [
                    {"tid": "196178259995238", "oid": "1961782599999915238"}
                ],
            },
            "jobs": [
                {"name": "填写表单", "assignee": "ut"},
                {"name": "修改订单备注", "status": JobStatus.INIT},
            ],
            "current_job": "修改订单备注",
        },
        client.shop,
        "tests.job.resources",
    )
    testbed.build()

    step = testbed.form.steps.filter_by(name="修改订单备注").first()
    job = testbed.business_order.jobs.filter_by(step=step).first()

    yield NamedTuple("Testbed", job=Job)(job)


@fixture
def mock_trade_update_trade_memo(mocker):
    from leyan_proto.digismart.trade.dgt_trade_pb2 import UpdateTradeMemoResponse
    from robot_processor.client import trade_client

    yield mocker.patch(
        "robot_processor.client.trade_client.client.UpdateTradeMemo",
        return_value=UpdateTradeMemoResponse(),
        autospec=trade_client.client.UpdateTradeMemo
    )


def test_memo_process(testbed, mock_trade_update_trade_memo, ignore_token_bucket_rate_limit):
    mock_trade_update_trade_memo.return_value = UpdateTradeMemoResponse(
        code=ResponseCode.FAIL,
        msg=json.dumps(
            {
                "error_message": "订单1备注失败: 必须是此交易的卖家或买家",
                "error_tid": ["196178259995238"],
            }
        ),
    )
    status, exc_info = MemoExecutor(testbed.job).process()
    assert status == JobStatus.FAILED
    assert "订单1备注失败: 必须是此交易的卖家或买家" in exc_info
    mock_trade_update_trade_memo.assert_called_once()
    mock_trade_update_trade_memo.reset_mock()

    mock_trade_update_trade_memo.return_value = UpdateTradeMemoResponse(
        code=ResponseCode.API_SUCCESS
    )
    status, exc_info = MemoExecutor(testbed.job).process()
    assert status == JobStatus.SUCCEED
    assert exc_info is None
    mock_trade_update_trade_memo.assert_called_once()
