import json

from robot_processor.enums import JobStatus
from robot_processor.job.taobao_trade import TaobaoTradeExecutor

mock_trade_resp = """
[{
    "trade_id":"3555757584943387148",
    "status":8,
    "type":"FIXED",
    "receiver":{
        "name":"左**",
        "state":"江西省",
        "district":"乐平市",
        "town":"洎阳街道",
        "address":"洎*街道西门加油站斜对面快递超市",
        "zip":"000000",
        "mobile":"*******0317",
        "city":"景德镇市",
        "country":"",
        "phone":""
    },
    "orders":[
        {
            "status":8,
            "spu_id":"704320975135",
            "sku_description":"颜色分类:【加宽乳胶坐垫】米白+移动头枕+脚踏-送腰枕;五星脚材质:钢制脚;扶手类型:升降扶手",
            "refund_status":2,
            "title":"家用电脑椅电竞椅舒适久坐游戏书房办公沙发椅靠背椅直播升降转椅",
            "quantity":1,
            "shipping":{
                "shipping_time":"2023-10-09 16:36:38",
                "contractor":"邮政电商标快EYB",
                "tracking_num":"8125094759101",
                "shipped":false,
                "trace_list":[

                ]
            },
            "sku_id":"4957773965977",
            "oid":"3555757584944387148",
            "price":438,
            "payment":199,
            "pic_path":"https://img.alicdn.com/bao/uploaded/i3/2088817296/O1CN01xxerIe23lcVninq76_!!2088817296.jpg",
            "divide_order_fee":199,
            "outer_sku_id":"4957773965977",
            "outer_sku_id_real":"P款O电脑椅-白【乳胶】头枕+腰枕+脚踏+钢脚",
            "taobao_id":"",
            "estimate_delivery_time":"",
            "total_fee":0,
            "end_time":"",
            "refund_id":"",
            "order_car_attr":""
        },
        {
            "status":8,
            "spu_id":"621806282566",
            "sku_description":"颜 色分类:经典半折黑-小号",
            "refund_status":2,
            "title":"户外压缩摆地摊钓鱼夜市铝合金便携式家用凳折叠凳子马扎加厚椅子",
            "quantity":1,
            "shipping":{
                "shipping_time":"2023-10-09 11:03:26",
                "contractor":"韵 达快递",
                "tracking_num":"433485508254604",
                "shipped":false,
                "trace_list":[

                ]
            },
            "sku_id":"4566674483391",
            "oid":"3555757584945387148",
            "price":19.8,
            "payment":9.9,
            "pic_path":"https://img.alicdn.com/bao/uploaded/i3/2088817296/O1CN01g9Xrin23lcIxaFNKp_!!2088817296.jpg",
            "divide_order_fee":9.9,
            "outer_sku_id":"4566674483391",
            "outer_sku_id_real":"小马扎-黑色",
            "taobao_id":"",
            "estimate_delivery_time":"",
            "total_fee":0,
            "end_time":"",
            "refund_id":"",
            "order_car_attr":""
        }
    ],
    "created_at":"2023-10-08 23:12:56",
    "modified_at":"2023-10-11 13:56:33",
    "paid_at":"2023-10-08 23:13:05",
    "price":208.9,
    "buyer_nick":"yiyun991217",
    "store_id":"110705549",
    "memo":"张静 10.11日  已发快递群核实拒收\\n拦截靳10.11日张静 10.11日 拦截张静 10.11日",
    "payment":208.9,
    "total_fee":457.8,
    "jdp_modified_at":"2023-10-11 13:56:34",
    "seller_nick":"雷度旗舰店",
    "jdp_created_at":"2023-10-08 23:12:58",
    "post_fee":"0.00",
    "buyer_alipay_no":"198********",
    "buyer_open_uid":"AAElp3E6ANnvchERvSFakx1d",
    "alipay_no":"2023100822001116801452552716",
    "finished_at":"",
    "buyer_rated":false,
    "discount":0,
    "step_paid_fee":0,
    "step_trade_status":"INVALID_STEP_TRADE",
    "send_time":"",
    "seller_flag":0,
    "received_payment":0,
    "tmall_coupon_fee":0
}]
"""


def test_taobao_trade(mocker, client, mock_full_job, mock_job_args, mock_job_states):
    mock_job_args(tid=[{"tid": "123"}])
    mocker.patch("robot_processor.job.taobao_trade.get_taobao_trade", return_value=json.loads(mock_trade_resp))
    executor = TaobaoTradeExecutor(mock_full_job)
    status, _ = executor.process()
    assert status == JobStatus.SUCCEED
