import json
from os import path

import pytest

from robot_processor.enums import ErpType, JobStatus
from robot_processor.job.complete_item import CompleteItemExecutor

__curdir__ = path.dirname(__file__)


@pytest.mark.parametrize('platform,data_source,spu_id,sku_id,expected_complete_item', [
    ('TAOBAO', 'platform', 'spu111', 'sku111', {'goods_code': 'T800942',
                                                'goods_pic': '',
                                                'goods_props': '颜色分类:四色双面走位棋【适用于2-6岁】 尺码:均码',
                                                'goods_title': '四色走位双面棋儿童2一3岁蒙氏早教益智玩具宝宝大脑逻辑思维训练'}),
    ('TAOBAO', 'erp', 'spu111', None, {'goods_code': '',
                                       'goods_pic': 'https://img.alicdn.com/bao/uploaded/i1/743798139'
                                                    '/O1CN017hy2ZU29ziPH2ip8E_!!0-item_pic.jpg',
                                       'goods_props': '',
                                       'goods_title': '四色走位双面棋儿童2一3岁蒙氏早教益智玩具宝宝大脑逻辑思维训练'}),
    ('DOUDIAN', 'platform', 'spu111', None, {'goods_code': '',
                                             'goods_pic': '',
                                             'goods_props': '',
                                             'goods_title': 'MM速干衣1'}),
])
def test_process(
        mocker, mock_random_full_job, mock_erp_type, mock_job_args, mock_job_states, ignore_token_bucket_rate_limit,
        platform, data_source, spu_id, sku_id, expected_complete_item, requests_mock
):
    mock_random_full_job.business_order.shop.platform = platform
    mocker.patch('robot_processor.job.complete_item.CompleteItemExecutor.get_widget_option_value', return_value={
        "mode": "spu",
        "label": "商品",
        "required": True,
        "dataSource": data_source,
        "defaultValue": "根据创建工单的子订单，自动赋值SPU/SKU"
    })
    mock_job_args(item=[{"spu": spu_id, "sku": sku_id, "mode": "spu"}])
    mock_erp_type(ErpType.KUAIMAI)

    mocker.patch('robot_processor.client.item.ItemClient.get_provider_sku', return_value={
        'skuInfo': [{
            'spu_id': '709785055773',
            'sku_id': '5151410879397',
            'props': '1627207:***********:颜色分类:四色双面走位棋【适用于2-6岁】;1627207:3232486:尺码:均码',
            'quantity': 997395,
            'price': 37.25,
            'outer_sku_id': 'T800942',
            'item_title': '四色走位双面棋儿童2一3岁蒙氏早教益智玩具宝宝大脑逻辑思维训练',
            'pic_url': ''
        }],
        'combineItems': [],
        'page_no': 1,
        'page_size': 20,
        'total_count': 0
    })

    mocker.patch('robot_processor.client.item.ItemClient.get_provider_item', return_value={
        'item_detail_infos': [{
            'spu_id': '709785055773',
            'item_title': '四色走位双面棋儿童2一3岁蒙氏早教益智玩具宝宝大脑逻辑思维训练',
            'pic_url': 'https://img.alicdn.com/bao/uploaded/i1/743798139/O1CN017hy2ZU29ziPH2ip8E_!!0-item_pic.jpg',
            'cid': ''
        }],
        'page_no': 1,
        'page_size': 20,
        'total_count': 0
    })

    with open(path.join(__curdir__, "../texture/3489769184773184007.json")) as f:
        requests_mock.post('/namespaces/doudian/methods/item.detail', json=json.load(f))

    status, msg = CompleteItemExecutor(mock_random_full_job).process()
    assert status == JobStatus.SUCCEED
    mock_job_states.write_job_output.assert_called_with(expected_complete_item)
