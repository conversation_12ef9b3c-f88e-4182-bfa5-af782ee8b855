from typing import NamedTuple
from unittest.mock import MagicMock

from pytest import fixture

from robot_processor.business_order.models import BusinessOrder, Job
from robot_processor.client.schema import PddOrderInformationGet
from robot_processor.enums import JobStatus
from robot_processor.job.pdd_memo_api import PDDMemoAPIExecutor
from tests.testbed import BusinessOrderBuilder


@fixture
def testbed(client):
    # tests/job/resources/pdd-memo.default.json
    testbed = BusinessOrderBuilder(
        {
            "form_template": "pdd-memo.default",
            "aid": "ut",
            "data": {
                "oid": "1961782599999915238",
                "tid": "196178259995238",
                "37f4a347-d700-443b-b03e-6503138e05e7": [
                    {"tid": "196178259995238", "oid": "1961782599999915238"}
                ],
            },
            "jobs": [
                {"name": "填写表单", "assignee": "ut"},
                {"name": "修改订单备注", "status": JobStatus.INIT},
            ],
            "current_job": "修改订单备注",
        },
        client.shop,
        "tests.job.resources",
    )
    testbed.build()

    step = testbed.form.steps.filter_by(name="修改订单备注").first()
    job = testbed.business_order.jobs.filter_by(step=step).first()

    yield NamedTuple("Testbed", bo=BusinessOrder, job=Job)(testbed.business_order, job)


def test_pdd_memo(mocker, testbed):
    mocker.patch(
        "robot_processor.job.pdd_memo_api.pdd_bridge_client.order_information_get",
        return_value=PddOrderInformationGet.Response(
            order_info_get_response=PddOrderInformationGet.Response.OrderInfoGetResponse(
                order_info=MagicMock(
                    remark="旧备注"
                )
            )
        )
    )
    mocker.patch("robot_processor.job.pdd_memo_api.pdd_bridge_client.update_memo")
    status, error = PDDMemoAPIExecutor(testbed.job).process()
    assert status == JobStatus.SUCCEED, error
