import pytest

from robot_processor.enums import JobStatus
from robot_processor.job.jst_remark_upload import JstRemarkUploadExecutor
from robot_processor.job.jst_set_node import JstSetNodeExecutor
from rpa.erp.jst import JstOrderRemarkUploadResp, JstBaseOrder


@pytest.fixture
def mock_jst_request(mocker):
    # mock JstNewSDK _request
    p = mocker.patch("robot_processor.job.jst_set_node.JstNewSDK._request")
    p.return_value = JstOrderRemarkUploadResp(
        issuccess=True,
        message="",
        code=0
    )
    yield p


@pytest.fixture
def mock_erp_check(mocker):
    # mock JstNewSDK and JstQmSDK __init__ function
    mocker.patch("robot_processor.job.jst_remark_upload.JstNewSDK.__init__", return_value=None)
    mocker.patch("robot_processor.job.jst_set_node.JstNewSDK.__init__", return_value=None)


@pytest.fixture
def mock_qm_query_order(mocker):
    # mock TradesFinder try_get_orders
    mocker.patch("robot_processor.job.jst_set_node.TradesFinder.__init__", return_value=None)
    p = mocker.patch("robot_processor.job.jst_set_node.TradesFinder.try_get_orders")
    p.return_value = [JstBaseOrder(
            shop_id=123,
            items=[],
            pays=[],
            node="12345"
    )]


def test_node_soid_set(client, mock_job_with_bo, mock_jst_request, mock_job_args,
                       mock_qm_query_order, mock_erp_check):
    mock_job_args(
        o_id="123",
        tid=[{"tid": "333"}],
        node="789"
    )
    mock_job_with_bo.business_order.sid = client.shop.sid
    exe = JstSetNodeExecutor(mock_job_with_bo)
    result, _ = exe.process()
    assert result == JobStatus.SUCCEED
    mock_jst_request.assert_called_with(
        "/open/order/node/soid/set",
        {
            "shop_id": 123,
            "items": [
                {
                    "node": "789",
                    "o_id": 123
                }
            ]
        },
        JstOrderRemarkUploadResp
    )


def test_node_soid_append(
        client, mock_job_with_bo, mock_jst_request, mock_job_args,
        mock_qm_query_order, mock_erp_check
):
    mock_job_args(
        o_id="123",
        tid=[{"tid": "333"}],
        node="789",
        is_append=True
    )
    mock_job_with_bo.business_order.sid = client.shop.sid
    exe = JstSetNodeExecutor(mock_job_with_bo)
    result, _ = exe.process()
    assert result == JobStatus.SUCCEED
    mock_jst_request.assert_called_with(*[
        "/open/order/node/soid/set",
        {
            "shop_id": 123,
            "items": [
                {
                    "node": "12345789",
                    "o_id": 123
                }
            ]
        },
        JstOrderRemarkUploadResp
    ])


def test_change_order_remark(
        client, mock_job_with_bo, mock_jst_request, mock_job_args, mock_erp_check, mock_qm_query_order):
    mock_job_args(
        o_id="123",
        tid=[{"tid": "333"}],
        remark="789",
        is_append=True
    )
    mock_job_with_bo.business_order.sid = client.shop.sid
    exe = JstRemarkUploadExecutor(mock_job_with_bo)
    result, _ = exe.process()
    assert result == JobStatus.SUCCEED
