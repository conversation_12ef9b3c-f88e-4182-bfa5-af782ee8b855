from dramatiq import Retry
from flask import current_app
from pytest import raises, fixture

from robot_processor.db import db
from robot_processor.enums import JobStatus
from robot_processor.job.job_model_wrapper import SendMessageArguments
from robot_processor.job.sms_send import SMSSendExecutor
from robot_processor.job.sms_send_result import SMSSendResultExecutor
from robot_processor.sms.models import SMSSendRecord
from robot_processor.sms.moguyun import SendResult


@fixture
def patch_config(mock_full_job):
    config = current_app.config.get("SMS_WHITELIST", {}).copy()
    current_app.config["SMS_WHITELIST"] = [mock_full_job.org_id]
    yield
    current_app.config["SMS_WHITELIST"] = config


def test_sms(mocker, client,
             mock_full_job, mock_job_args, patch_config):
    mock_job_args(
        branch=SendMessageArguments(
            content='content',
            skip=False,
            image_urls=[],
            send_channel='RPA_CLIENT',
        ),
        phone_number="12332132121"
    )
    mocker.patch('robot_processor.sms.moguyun.MoguyunClient.send_msg',
                 return_value=SendResult(
                     code="00000",
                     uid="uid",
                     desc=""
                 ))
    sms_send_executor = SMSSendExecutor(mock_full_job)
    status, _ = sms_send_executor.process()
    assert status == JobStatus.SUCCEED
    records = SMSSendRecord.query.all()
    assert len(records) == 1
    record = records[0]

    mock_job_args(
        uid=record.uid
    )
    with raises(Retry):
        sms_send_result_executor = SMSSendResultExecutor(mock_full_job)
        status, _ = sms_send_result_executor.process()

    record.status = 0
    record.desc = ''
    db.session.add(record)
    db.session.commit()
    status, _ = sms_send_result_executor.process()
    assert status == JobStatus.SUCCEED
