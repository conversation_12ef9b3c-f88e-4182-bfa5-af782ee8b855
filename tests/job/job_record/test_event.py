from unittest.mock import Mock

import arrow
import pytest

from robot_processor.app import app
from robot_processor.enums import JobStatus, StepType, AssigneeRule, \
    JobProcessMark
from robot_processor.ext import db
from robot_processor.form.models import Form
from robot_processor.job.job_record import create_record, JobRecord, \
    update_record_info


@pytest.fixture
def mock_event_human_job(client, mock_job, mock_step, mock_business_order, mock_form):
    from robot_processor.app import app
    app.config["NEED_RECORD_JOB"] = 1
    mock_business_order.sid = client.shop.sid
    mock_job.business_order_id = mock_business_order.id
    mock_step.step_type = StepType.human
    mock_step.form_id = mock_form.id
    mock_job.step = mock_step
    mock_job.status = JobStatus.INIT
    # 兼容测试用例
    mock_business_order.form_version_id = None
    db.session.commit()
    yield mock_job
    app.config["NEED_RECORD_JOB"] = 0


@pytest.fixture
def mock_job_record(mock_event_human_job):
    job = mock_event_human_job
    step = job.step
    jr = JobRecord(
        bo_id=job.business_order_id,
        sid=job.sid,
        step_uuid=job.step_uuid,
        step_name=job.step.name,
        step_type=job.step.step_type.name,
        job_id=job.id,
        start=arrow.now().timestamp(),
        form_id=step.form_id,
        form_name=Form.query.get(step.form_id).name
    )
    db.session.add(jr)
    db.session.commit()
    yield jr


def test_trigger_event(client, mocker, mock_event_human_job: Mock):
    mocker.patch("robot_processor.job.job_record.record_alchemy_event.create_record")
    app.config["RECORD_ORG_IDS"] = [mock_event_human_job.business_order.shop.org_id]  # noqa
    mock_event_human_job.status = JobStatus.PENDING
    db.session.commit()


def test_create_record(client, mock_event_human_job):
    create_record(mock_event_human_job.id, arrow.now().int_timestamp)
    jr = JobRecord.query.filter_by(job_id=mock_event_human_job.id).first()
    assert jr
    assert jr.bo_id == mock_event_human_job.business_order_id


def test_update_record_info(client, mock_event_human_job, mock_job_record):
    assert mock_event_human_job.id == mock_job_record.job_id
    assert mock_job_record.cost is None
    update_record_info(mock_event_human_job.id, arrow.now().int_timestamp)
    assert mock_job_record.cost is not None


def test_trigger_process_mark(client, mock_event_human_job):
    mock_event_human_job.step.AssigneeRule = AssigneeRule.FREE_PICK
    db.session.commit()
    mock_event_human_job.process_mark = JobProcessMark.PICK
    db.session.commit()
