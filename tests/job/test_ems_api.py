import json
from pytest import fixture

from robot_processor.client.logistics_clients.ems_domain import EMSClient
from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.job.ems import EMSQueryLogisticExecutor, \
    EMSChangeAddressExecutor, EmsInterceptExecutor, EMSConfirmInterceptExecutor
from robot_processor.enums import JobStatus


QUERY_LOGISTIC_RESPONSE = r"""{"serialNo": "958aeddf-5ef4-4381-bdaa-1b7a2bd98209", "retCode": "00000", "retMsg": "查询结果正常返回！", "retBody": "{\"responseItems\":[{\"traceNo\":\"9857996673971\",\"waybillNo\":\"9857996673971\",\"opTime\":\"2024-01-29 13:43:53\",\"opOrgProvName\":\"安徽省\",\"opOrgCity\":\"合肥市\",\"opOrgCode\":\"23007900\",\"opOrgName\":\"肥东县电商客户城区揽投部\",\"opCode\":\"203\",\"opName\":\"收寄计费信息\",\"opDesc\":\"中国邮政 已收取快件\",\"productName\":\"快递包裹\"},{\"traceNo\":\"9857996673971\",\"waybillNo\":\"9857996673971\",\"opTime\":\"2024-01-30 18:40:16\",\"opOrgProvName\":\"安徽省\",\"opOrgCity\":\"合肥市\",\"opOrgCode\":\"23007900\",\"opOrgName\":\"肥东县电商客户城区揽投部\",\"opCode\":\"303\",\"opName\":\"揽投配发\",\"opDesc\":\"快件已在【肥东县电商客户城区揽投部】完成分拣，准备发出\",\"productName\":\"快递包裹\"},{\"traceNo\":\"9857996673971\",\"waybillNo\":\"9857996673971\",\"opTime\":\"2024-01-30 18:42:36\",\"opOrgProvName\":\"安徽省\",\"opOrgCity\":\"合肥市\",\"opOrgCode\":\"23007900\",\"opOrgName\":\"肥东县电商客户城区揽投部\",\"opCode\":\"305\",\"opName\":\"揽投发运/封车\",\"opDesc\":\"快件离开【肥东县电商客户城区揽投部】，正在发往【肥东县中心揽投部】\",\"productName\":\"快递包裹\"},{\"traceNo\":\"9857996673971\",\"waybillNo\":\"9857996673971\",\"opTime\":\"2024-01-31 03:23:39\",\"opOrgProvName\":\"安徽省\",\"opOrgCity\":\"合肥市\",\"opOrgCode\":\"23001600\",\"opOrgName\":\"合肥市皇藏峪路邮件处理中心\",\"opCode\":\"954\",\"opName\":\"邮件到达处理中心\",\"opDesc\":\"快件到达【合肥市皇藏峪路快件处理中心】\",\"productName\":\"快递包裹\"},{\"traceNo\":\"9857996673971\",\"waybillNo\":\"9857996673971\",\"opTime\":\"2024-01-31 14:29:23\",\"opOrgProvName\":\"安徽省\",\"opOrgCity\":\"合肥市\",\"opOrgCode\":\"23001600\",\"opOrgName\":\"合肥市皇藏峪路邮件处理中心\",\"opCode\":\"389\",\"opName\":\"处理中心封车\",\"opDesc\":\"快件离开【合肥市皇藏峪路快件处理中心】，正在发往下一站\",\"productName\":\"快递包裹\"},{\"traceNo\":\"9857996673971\",\"waybillNo\":\"9857996673971\",\"opTime\":\"2024-02-01 00:40:31\",\"opOrgProvName\":\"上海市\",\"opOrgCity\":\"上海市\",\"opOrgCode\":\"20000414\",\"opOrgName\":\"上海王港邮件处理中心\",\"opCode\":\"954\",\"opName\":\"邮件到达处理中心\",\"opDesc\":\"快件到达【上海王港快件处理中心】\",\"productName\":\"快递包裹\"},{\"traceNo\":\"9857996673971\",\"waybillNo\":\"9857996673971\",\"opTime\":\"2024-02-01 02:23:15\",\"opOrgProvName\":\"上海市\",\"opOrgCity\":\"上海市\",\"opOrgCode\":\"20000414\",\"opOrgName\":\"上海王港邮件处理中心\",\"opCode\":\"389\",\"opName\":\"处理中心封车\",\"opDesc\":\"快件离开【上海王港快件处理中心】，正在发往下一站\",\"productName\":\"快递包裹\"},{\"traceNo\":\"9857996673971\",\"waybillNo\":\"9857996673971\",\"opTime\":\"2024-02-01 08:30:30\",\"opOrgProvName\":\"上海市\",\"opOrgCity\":\"上海市\",\"opOrgCode\":\"20130044\",\"opOrgName\":\"上海市浦东新区瓦屑揽投部\",\"opCode\":\"306\",\"opName\":\"揽投解车\",\"opDesc\":\"快件到达【上海市浦东新区瓦屑揽投部】\",\"productName\":\"快递包裹\"},{\"traceNo\":\"9857996673971\",\"waybillNo\":\"9857996673971\",\"opTime\":\"2024-02-01 08:35:30\",\"opOrgProvName\":\"上海市\",\"opOrgCity\":\"上海市\",\"opOrgCode\":\"20130044\",\"opOrgName\":\"上海市浦东新区瓦屑揽投部\",\"opCode\":\"702\",\"opName\":\"投递邮件接收-下段\",\"opDesc\":\"快件正在派送中，请耐心等待，保持电话畅通，准备签收，如有疑问请电联快递员【柳栋国，电话:18701971232】或揽投部【电话:021-68192825】，投诉请致电11183。\",\"productName\":\"快递包裹\"},{\"traceNo\":\"9857996673971\",\"waybillNo\":\"9857996673971\",\"opTime\":\"2024-02-01 11:05:52\",\"opOrgProvName\":\"上海市\",\"opOrgCity\":\"上海市\",\"opOrgCode\":\"20130044\",\"opOrgName\":\"上海市浦东新区瓦屑揽投部\",\"opCode\":\"704\",\"opName\":\"投递结果反馈-妥投\",\"opDesc\":\"您的快件已代签收【家人，一楼大厅】，如有疑问请电联快递员【柳栋国，电话:18701971232】。连接美好，无处不在，感谢您使用中国邮政，期待再次为您服务。\",\"deliverCode\":\"20\",\"productName\":\"快递包裹\"}]}", "retDate": "2024-02-02 16:29:10", "success": true}"""  # noqa

QUERY_LOGISTIC_FAILED_RESPONSE = r"""{"serialNo":"19b8cfac-efd9-47fd-8901-4036ab0bc70c","retCode":"S0001","retMsg":"该邮件不是当前客户，不提供查询！","retBody":"{\"responseItems\":[]}","retDate":"2024-02-02 17:40:48","success":false}""" # noqa

create_interception_RESPONSE = r"""{"serialNo": "958aeddf-5ef4-4381-bdaa-1b7a2bd98209", "retCode": "00000", "retMsg": "成功", "retBody": "{\"responseItems\":[]}", "retDate": "2024-02-02 11:31:32", "success": true}""" # noqa

QUERY_INTERCEPT_RESPONSE = r"""{"serialNo": "9b0dd4ca-696e-41ad-9c31-4cafd28016eb", "retCode": "00000", "retMsg": "查询结果正常返回！", "retBody": "{\"responseItems\":[{\"traceNo\":\"8163584115203\",\"waybillNo\":\"8163584115203\",\"opTime\":\"2024-03-13 15:24:47\",\"opOrgProvName\":\"湖南省\",\"opOrgCity\":\"长沙市\",\"opOrgCode\":\"41000223\",\"opOrgName\":\"岳麓区快递包裹揽投部\",\"opCode\":\"203\",\"opName\":\"收寄计费信息\",\"opDesc\":\"中国邮政 已收取快件\",\"productName\":\"电商标快\"},{\"traceNo\":\"8163584115203\",\"waybillNo\":\"8163584115203\",\"opTime\":\"2024-03-13 16:15:21\",\"opOrgProvName\":\"湖南省\",\"opOrgCity\":\"长沙市\",\"opOrgCode\":\"41000223\",\"opOrgName\":\"岳麓区快递包裹揽投部\",\"opCode\":\"345\",\"opName\":\"揽收扫描配发\",\"opDesc\":\"快件已在【岳麓区快递包裹揽投部】完成分拣，准备发出\",\"productName\":\"电商标快\"},{\"traceNo\":\"8163584115203\",\"waybillNo\":\"8163584115203\",\"opTime\":\"2024-03-13 17:02:21\",\"opOrgProvName\":\"湖南省\",\"opOrgCity\":\"长沙市\",\"opOrgCode\":\"41000223\",\"opOrgName\":\"岳麓区快递包裹揽投部\",\"opCode\":\"305\",\"opName\":\"揽投发运/封车\",\"opDesc\":\"快件离开【岳麓区快递包裹揽投部】，正在发往【湖南省寄递事业部长沙市邮区中心快件处理中心】\",\"productName\":\"电商标快\"},{\"traceNo\":\"8163584115203\",\"waybillNo\":\"8163584115203\",\"opTime\":\"2024-03-13 18:42:28\",\"opOrgProvName\":\"湖南省\",\"opOrgCity\":\"长沙市\",\"opOrgCode\":\"41011600\",\"opOrgName\":\"湖南省寄递事业部长沙市邮区中心邮件处理中心\",\"opCode\":\"954\",\"opName\":\"邮件到达处理中心\",\"opDesc\":\"快件到达【湖南省寄递事业部长沙市邮区中心快件处理中心】\",\"productName\":\"电商标快\"},{\"traceNo\":\"8163584115203\",\"waybillNo\":\"8163584115203\",\"opTime\":\"2024-03-14 06:34:39\",\"opOrgProvName\":\"湖南省\",\"opOrgCity\":\"长沙市\",\"opOrgCode\":\"41011600\",\"opOrgName\":\"湖南省寄递事业部长沙市邮区中心邮件处理中心\",\"opCode\":\"389\",\"opName\":\"处理中心封车\",\"opDesc\":\"快件离开【湖南省寄递事业部长沙市邮区中心快件处理中心】，正在发往下一站\",\"productName\":\"电商标快\"},{\"traceNo\":\"8163584115203\",\"waybillNo\":\"8163584115203\",\"opTime\":\"2024-03-14 18:52:58\",\"opOrgProvName\":\"安徽省\",\"opOrgCity\":\"安庆市\",\"opOrgCode\":\"24600071\",\"opOrgName\":\"安庆市邮件处理中心\",\"opCode\":\"954\",\"opName\":\"邮件到达处理中心\",\"opDesc\":\"快件到达【安庆市快件处理中心】\",\"productName\":\"电商标快\"},{\"traceNo\":\"8163584115203\",\"waybillNo\":\"8163584115203\",\"opTime\":\"2024-03-15 06:39:14\",\"opOrgProvName\":\"安徽省\",\"opOrgCity\":\"安庆市\",\"opOrgCode\":\"24600071\",\"opOrgName\":\"安庆市邮件处理中心\",\"opCode\":\"389\",\"opName\":\"处理中心封车\",\"opDesc\":\"快件离开【安庆市快件处理中心】，正在发往下一站\",\"productName\":\"电商标快\"},{\"traceNo\":\"8163584115203\",\"waybillNo\":\"8163584115203\",\"opTime\":\"2024-03-15 08:51:09\",\"opOrgProvName\":\"安徽省\",\"opOrgCity\":\"安庆市\",\"opOrgCode\":\"23146900\",\"opOrgName\":\"桐城市城关揽投部\",\"opCode\":\"954\",\"opName\":\"邮件到达处理中心\",\"opDesc\":\"快件到达【桐城市城关揽投部】\",\"productName\":\"电商标快\"},{\"traceNo\":\"8163584115203\",\"waybillNo\":\"8163584115203\",\"opTime\":\"2024-03-18 08:30:13\",\"opOrgProvName\":\"湖南省\",\"opOrgCity\":\"长沙市\",\"opOrgCode\":\"41000223\",\"opOrgName\":\"岳麓区快递包裹揽投部\",\"opCode\":\"801\",\"opName\":\"撤单\",\"opDesc\":\"快件已撤单成功，将退回至寄件地址。\",\"productName\":\"电商标快\"}]}", "retDate": "2024-03-18 08:33:45", "success": true}"""  # noqa


@fixture(autouse=True)
def init_logistics_errors_rule():
    from robot_processor.client.logistics_clients.exception_manager import config

    config._properties.update(
        {
            "logistics.errors_match_rule.ems": json.dumps([
                {
                    "error_type": "MATCH_ERROR_SIGN",
                    "error_code": [],
                    "regex": [".*该邮件已经妥投，无法改址，建议客户取到邮件后重新寄出*."],
                    "suggestion": "不可进行拦截，用户已签收",
                },
                {
                    "error_type": "MATCH_ERROR_DELIVERY",
                    "error_code": [],
                    "regex": [".*当前邮件正在投递，无法办理改址，建议客户关注邮件信息，"
                              "如出现“派件异常”后可通过微信公众号自助办理*."],
                    "suggestion": "不可进行拦截，已进行派送",
                },
                {
                    "error_type": "MATCH_ERROR_PROXY_SIGN",
                    "error_code": [],
                    "regex": [],
                    "suggestion": "不可进行拦截，站点已代收",
                },
                {
                    "error_type": "MATCH_ERROR_SITE_UNSIGNED",
                    "error_code": [],
                    "regex": [".*该邮件不是当前客户，不提供查询!*."],
                    "suggestion": "联系技术人员，进行协议客户号的绑定，"
                                  "或调整工单模板中的协议客户号信息",
                },
                {
                    "error_type": "MATCH_ERROR_UNPICKED",
                    "error_code": [],
                    "regex": [
                        ".*您的邮件尚未发出，客服这边无法办理，"
                        "请寄件人联系收寄局帮您更改收件地址，"
                        "或等邮件发出后通过微信公众号自助办理*."
                    ],
                    "suggestion": "不可进行拦截，快件未揽收",
                },
                {
                    "error_type": "MATCH_ERROR_INTERCEPTED",
                    "error_code": [],
                    "regex": [".*该单已经做过撤单,不允许撤单!*."],
                    "suggestion": "该单号已拦截过，请耐心等待拦截状态更新",
                },
            ])
        }
    )

    yield


def test_query_logistic_success(
    client,
    mocker, mock_full_job,
    mock_job_args, mock_job_states
):
    mock_full_job.shop = client.shop
    mock_job_args(**{"waybill_no": "114514", "sender_no": "1001"})
    executor = EMSQueryLogisticExecutor(mock_full_job)
    ems_client = EMSClient(LogisticsType.EMS)
    ems_client.init({"keys": [{
        "authorization": "",
        "sm4_key": "TvaBgrhE46sft3nZlfe7xw==",
        "sender_no": "1001"
    }]})
    mocker.patch(
        "robot_processor.job.ems.get_ems_client_by_shop",
        return_value=(ems_client, None)
    )
    mocker.patch(
        "robot_processor.job.ems.EmsInterceptExecutor.before_check",
        return_value=(True, None)
    )
    mocker.patch(
        "robot_processor.client.logistics_clients.ems_domain.EMSClient.send",
        return_value=json.loads(QUERY_LOGISTIC_RESPONSE)
    )
    success, _ = executor.process()

    assert success == JobStatus.SUCCEED
    mock_job_states.write_job_widget_collection.assert_called_with(
        executor.output_model(**json.loads(
            json.loads(QUERY_LOGISTIC_RESPONSE).get("retBody")
        )).dict()
    )

    mock_job_args(**{"waybill_no": "114514", "sender_no": "1001", "get_trace_records_method": "GET_LATEST_RECORD"})
    executor = EMSQueryLogisticExecutor(mock_full_job)
    success, _ = executor.process()

    result = json.loads(
        json.loads(QUERY_LOGISTIC_RESPONSE).get("retBody")
    )
    result["responseItems"] = [result["responseItems"][-1]]

    assert success == JobStatus.SUCCEED
    mock_job_states.write_job_widget_collection.assert_called_with(
        executor.output_model(**result).dict()
    )


def test_get_latest_logistic_record_success(
    mocker, client, mock_full_job,
    mock_job_args, mock_job_states
):
    mock_full_job.shop = client.shop
    mock_job_args(**{"waybill_no": "114514", "sender_no": "1001", "get_trace_records_method": "GET_LATEST_RECORD"})
    executor = EMSQueryLogisticExecutor(mock_full_job)

    ems_client = EMSClient(LogisticsType.EMS)
    ems_client.init({"keys": [{
        "authorization": "",
        "sm4_key": "TvaBgrhE46sft3nZlfe7xw==",
        "sender_no": "1001"
    }]})
    mocker.patch(
        "robot_processor.job.ems.get_ems_client_by_shop",
        return_value=(ems_client, None)
    )
    mocker.patch(
        "robot_processor.job.ems.EmsInterceptExecutor.before_check",
        return_value=(True, None)
    )
    mocker.patch(
        "robot_processor.client.logistics_clients.ems_domain.EMSClient.send",
        return_value=json.loads(QUERY_LOGISTIC_RESPONSE)
    )
    success, _ = executor.process()

    assert success == JobStatus.SUCCEED
    logistic_trace_records = executor.output_model(**json.loads(
        json.loads(QUERY_LOGISTIC_RESPONSE).get("retBody")
    ))
    logistic_trace_records.response_items = [logistic_trace_records.response_items[-1]]

    mock_job_states.write_job_widget_collection.assert_called_with(
        logistic_trace_records.dict()
    )


def test_query_logistic_failed(
    mocker, client, mock_full_job,
    mock_job_args, mock_job_states
):
    mock_full_job.shop = client.shop
    mock_job_args(**{"waybill_no": "114514", "sender_no": "1001"})
    executor = EMSQueryLogisticExecutor(mock_full_job)

    ems_client = EMSClient(LogisticsType.EMS)
    ems_client.init({"keys": [{
        "authorization": "1001",
        "sm4_key": "TvaBgrhE46sft3nZlfe7xw==",
        "sender_no": "1001"
    }]})
    mocker.patch(
        "robot_processor.job.ems.get_ems_client_by_shop",
        return_value=(ems_client, None)
    )
    mocker.patch(
        "robot_processor.job.ems.EmsInterceptExecutor.before_check",
        return_value=(True, None)
    )
    mocker.patch(
        "robot_processor.client.logistics_clients.ems_domain.EMSClient.send",
        return_value=json.loads(QUERY_LOGISTIC_FAILED_RESPONSE)
    )
    success, err = executor.process()

    assert success != JobStatus.SUCCEED
    assert err == "该邮件不是当前客户，不提供查询！"


def test_create_interception_success(
    mocker, client, mock_full_job,
    mock_job_args, mock_job_states
):
    mock_full_job.shop = client.shop
    mock_job_args(**{"waybill_no": "114514", "sender_no": "1001"})
    executor = EmsInterceptExecutor(mock_full_job)

    ems_client = EMSClient(LogisticsType.EMS)
    ems_client.init({"keys": [{
        "authorization": "",
        "sm4_key": "TvaBgrhE46sft3nZlfe7xw==",
        "sender_no": "1001"
    }]})
    mocker.patch(
        "robot_processor.job.ems.get_ems_client_by_shop",
        return_value=(ems_client, None)
    )
    mocker.patch(
        "robot_processor.job.ems.EmsInterceptExecutor.before_check",
        return_value=(True, None)
    )
    mocker.patch(
        "robot_processor.client.logistics_clients.ems_domain.EMSClient.create_interception",
        return_value=(True, "{\"responseItems\":[]}", None)
    )
    mocker.patch(
        "robot_processor.client.logistics_clients.ems_domain.EMSClient.send",
        return_value=json.loads(QUERY_INTERCEPT_RESPONSE)
    )
    success, _ = executor.process()

    assert success == JobStatus.SUCCEED


def test_create_interception_failed(
    mocker, client, mock_full_job,
    mock_job_args, mock_job_states
):
    mock_full_job.shop = client.shop
    mock_job_args(**{"waybill_no": "114514"})
    executor = EmsInterceptExecutor(mock_full_job)

    ems_client = EMSClient(LogisticsType.EMS)
    ems_client.init({"keys": [{
        "authorization": "",
        "sm4_key": "TvaBgrhE46sft3nZlfe7xw==",
        "sender_no": ""
    }]})
    mocker.patch(
        "robot_processor.job.ems.get_ems_client_by_shop",
        return_value=(ems_client, None)
    )
    mocker.patch(
        "robot_processor.job.ems.EmsInterceptExecutor.before_check",
        return_value=(True, None)
    )
    mocker.patch(
        "robot_processor.client.logistics_clients.ems_domain.EMSClient.create_interception",
        return_value=(False, "该邮件已经妥投，无法改址，建议客户取到邮件后重新寄出", None)
    )
    success, _ = executor.process()

    assert success == JobStatus.SUCCEED


def test_create_interception_failed_1(
    mocker, client, mock_full_job,
    mock_job_args, mock_job_states
):
    mock_full_job.shop = client.shop
    mock_job_args(**{"waybill_no": "114514"})
    executor = EmsInterceptExecutor(mock_full_job)

    ems_client = EMSClient(LogisticsType.EMS)
    ems_client.init({"keys": [{
        "authorization": "",
        "sm4_key": "TvaBgrhE46sft3nZlfe7xw==",
        "sender_no": ""
    }]})
    mocker.patch(
        "robot_processor.job.ems.get_ems_client_by_shop",
        return_value=(ems_client, None)
    )
    mocker.patch(
        "robot_processor.job.ems.EmsInterceptExecutor.before_check",
        return_value=(True, None)
    )
    mocker.patch(
        "robot_processor.client.logistics_clients.ems_domain.EMSClient.create_interception",
        return_value=(False, "您的邮件尚未发出，客服这边无法办理，请寄件人联系收寄局帮您更改收件地址，或等邮件发出后通过微信公众号自助办理", None)
    )
    success, _ = executor.process()

    assert success == JobStatus.FAILED


def test_change_address(
    mocker, client, mock_full_job,
    mock_job_args, mock_job_states
):
    mock_full_job.shop = client.shop
    mock_job_args(
        **{"waybill_no": "114514", "sender_no": "1001", "new_address": {
            "city": "三明市",
            "name": "肖翔",
            "town": "梅仙镇",
            "zone": "尤溪县",
            "state": "福建省",
            "mobile": "17350543932",
            "address": "梅仙镇镇政府旁边",
            "district": "尤溪县"
        }})
    executor = EMSChangeAddressExecutor(mock_full_job)

    ems_client = EMSClient(LogisticsType.EMS)
    ems_client.init({"keys": [{
        "authorization": "",
        "sm4_key": "TvaBgrhE46sft3nZlfe7xw==",
        "sender_no": ""
    }]})
    mocker.patch(
        "robot_processor.job.ems.get_ems_client_by_shop",
        return_value=(ems_client, None)
    )
    mocker.patch(
        "robot_processor.job.ems.EmsInterceptExecutor.before_check",
        return_value=(True, None)
    )

    success, _ = executor.process()


def test_confirm_interception_failed(
    mocker, client, mock_full_job,
    mock_job_args, mock_job_states
):
    mock_full_job.shop = client.shop
    mock_job_args(**{"waybill_no": "114514"})
    executor = EMSConfirmInterceptExecutor(mock_full_job)

    ems_client = EMSClient(LogisticsType.EMS)
    ems_client.init({"keys": [{
        "authorization": "",
        "sm4_key": "TvaBgrhE46sft3nZlfe7xw==",
        "sender_no": "1001"
    }]})
    mocker.patch(
        "robot_processor.job.ems.get_ems_client_by_shop",
        return_value=(ems_client, None)
    )
    mocker.patch(
        "robot_processor.job.ems.EMSConfirmInterceptExecutor.before_check",
        return_value=(True, None)
    )
    success, _ = executor.process()
    mocker.patch(
        "robot_processor.client.logistics_clients.ems_domain.EMSClient.send",
        return_value=json.loads(QUERY_LOGISTIC_FAILED_RESPONSE)
    )
    success, err = executor.process()

    assert success == JobStatus.RUNNING


def test_confirm_interception_success(
    mocker, client, mock_full_job,
    mock_job_args, mock_job_states
):
    mock_full_job.shop = client.shop
    mock_job_args(**{"waybill_no": "114514"})
    executor = EMSConfirmInterceptExecutor(mock_full_job)

    ems_client = EMSClient(LogisticsType.EMS)
    ems_client.init({"keys": [{
        "authorization": "",
        "sm4_key": "TvaBgrhE46sft3nZlfe7xw==",
        "sender_no": ""
    }]})
    mocker.patch(
        "robot_processor.job.ems.get_ems_client_by_shop",
        return_value=(ems_client, None)
    )
    mocker.patch(
        "robot_processor.job.ems.EMSConfirmInterceptExecutor.before_check",
        return_value=(True, None)
    )
    mocker.patch(
        "robot_processor.client.logistics_clients.ems_domain.EMSClient.send",
        return_value=json.loads(QUERY_INTERCEPT_RESPONSE)
    )
    mocker.patch(
        "robot_processor.job.ems.EMSConfirmInterceptExecutor.init_logistics_domain",
        return_value={"keys": [{
            "authorization": "",
            "sm4_key": "TvaBgrhE46sft3nZlfe7xw==",
            "sender_no": ""
        }]}
    )
    success, err = executor.process()

    assert success == JobStatus.SUCCEED
