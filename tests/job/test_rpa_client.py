from unittest.mock import MagicMock

from robot_processor.enums import StepType, JobStatus
from robot_processor.job.job_model_wrapper import SendMessageArguments
from robot_processor.job.rpa_client import RPAClient


def test_parse_rpa_identifier(client, rpa_context_factory,
                              step_factory, job_factory, form_factory, rpa):
    rpa_context = rpa_context_factory.create(org_id=client.shop.org_id)
    send_qianniu_rpa = rpa(name='发送千牛消息[RPA客户端]')
    rpa_context.rpa_id = send_qianniu_rpa.id
    form = form_factory.create(name='form1', category='category1')
    form.subscribe(client.shop, True)
    step = step_factory.create(form_id=form.id, widget_collection_id=1, name='step1',
                               step_type=StepType.auto, data={"rpa_id": send_qianniu_rpa.id})
    step.update_raw_step()

    job = job_factory.create(step_id=step.id)
    rpa_client = RPAClient(job)
    assert rpa_client.get_identifier() == 'qn_send_message'


def test_get_rate_limit_settings(mocker):
    mock_job, mock_job_wrapper = MagicMock(), MagicMock()
    rpa_client = RPAClient(mock_job)
    rpa_client.job_wrapper = mock_job_wrapper
    mock_job_wrapper.shop.org_id = '1'
    mocker.patch.object(rpa_client, 'get_identifier', return_value='qn_send_message')
    assert rpa_client.get_rate_limit_setting({}) is None
    assert rpa_client.get_rate_limit_setting({'JOB_LIMITER_RPA_CLIENT:qn_send_message_ORG': '1/seconds'}) == \
        ('JOB_LIMITER_RPA_CLIENT:qn_send_message_ORG_1', '1/seconds')


def mock_rpa_client_job(ident, workflow_id, execution_id=None):
    mock_job, mock_job_wrapper = MagicMock(), MagicMock()
    mock_job.id = 1
    mock_job.raw_step_v2 = {"task": {"context": {"workflow_id": workflow_id}}}
    mock_job.business_order.sid = 'sid'
    mock_job.step.task.rpa_client_metadata.ident = ident
    if execution_id:
        mock_job.extra_data = {'execution_id': execution_id}
    rpa_client = RPAClient(mock_job)
    rpa_client.job_wrapper = mock_job_wrapper
    return rpa_client


def test_process_unknown_rpa_ident():
    rpa_client = mock_rpa_client_job('unknown', 'workflow1')
    status, err = rpa_client.process()
    assert status == JobStatus.FAILED
    assert err == '未知的 rpa client ident'

    rpa_client = mock_rpa_client_job(None, 'workflow1')
    status, err = rpa_client.process()
    assert status == JobStatus.FAILED
    assert err == '未找到 rpa client 任务标识'


def test_process_qn_message(client, mock_job_args, mock_job_states, requests_mock):
    rpa_client = mock_rpa_client_job('qn_send_message', 'workflow1')
    mock_job_args(branch=SendMessageArguments(
        usernick='',
        sender={'value': 'MANUAL_INPUT', 'extra': ['user1']},
        content='content',
        image_urls=['http://images.png'],
        send_channel='rpa_client',
        skip=False,
    ))
    requests_mock.put('/rpacontrol/api/feisuo/workflows/workflow1/_execute',
                      json={'success': True, 'data': {'execution_id': 12345}})
    status, err = rpa_client.process()
    assert status == JobStatus.RUNNING
    assert mock_job_states.get_job_state('execution_id') == 12345
    assert requests_mock.request_history[0].json() == {'payload': {'pid': 'sid', 'receiver': '', 'text': 'content',
                                                                   'sub_id': [None],
                                                                   'image_url': 'http://images.png'},
                                                       'execution_id': 1}


def test_process_wdt_trade_info(client, mock_job_args, mock_job_states, requests_mock):
    rpa_client = mock_rpa_client_job('wdt_trade_info_reveal', 'workflow1')
    mock_job_args(order=[{'tid': '123456789', 'oid': '123456789'}])
    requests_mock.put('/rpacontrol/api/feisuo/workflows/workflow1/_execute',
                      json={'success': True, 'data': {'execution_id': 12345}})
    status, err = rpa_client.process()
    assert status == JobStatus.RUNNING
    assert mock_job_states.get_job_state('execution_id') == 12345
    assert requests_mock.request_history[0].json() == {'payload': {'order': '123456789'}, 'execution_id': 1}


def test_process_qq(client, mock_job_args, mock_job_states, requests_mock):
    rpa_client = mock_rpa_client_job('send_message_qq_group', 'workflow1')
    mock_job_args(branch=SendMessageArguments(
        usernick='',
        sender={'value': 'MANUAL_INPUT', 'extra': ['user1']},
        content='content',
        image_urls=['http://images.png'],
        send_channel='rpa_client',
        qq_group='123456789',
        skip=False,
    ))
    requests_mock.put('/rpacontrol/api/feisuo/workflows/workflow1/_execute',
                      json={'success': True, 'data': {'execution_id': 12345}})
    status, err = rpa_client.process()
    assert status == JobStatus.RUNNING
    assert mock_job_states.get_job_state('execution_id') == 12345
    assert requests_mock.request_history[0].json() == {'payload': {'groupname': '123456789',
                                                                   'content': 'content',
                                                                   'image_urls': ['http://images.png']},
                                                       'execution_id': 1}


def test_process_dingtalk(client, mock_job_args, mock_job_states, requests_mock):
    rpa_client = mock_rpa_client_job('send_dingtalk_group', 'workflow1')
    mock_job_args(branch=SendMessageArguments(
        usernick='',
        sender={'value': 'MANUAL_INPUT', 'extra': ['user1']},
        content='content',
        image_urls=['http://images.png'],
        send_channel='rpa_client',
        dingtalk_group='abc',
        skip=False,
    ))
    requests_mock.put('/rpacontrol/api/feisuo/workflows/workflow1/_execute',
                      json={'success': True, 'data': {'execution_id': 12345}})
    status, err = rpa_client.process()
    assert status == JobStatus.RUNNING
    assert mock_job_states.get_job_state('execution_id') == 12345
    assert requests_mock.request_history[0].json() == {'payload': {'groupname': 'abc',
                                                                   'content': 'content',
                                                                   'image_urls': ['http://images.png']},
                                                       'execution_id': 1}


def test_process_wechat(client, mock_job_args, mock_job_states, requests_mock):
    rpa_client = mock_rpa_client_job('send_wechat_group', 'workflow1')
    mock_job_args(branch=SendMessageArguments(
        usernick='',
        sender={'value': 'MANUAL_INPUT', 'extra': ['user1']},
        content='content',
        image_urls=['http://images.png'],
        send_channel='rpa_client',
        wechat_group='abc',
        skip=False,
    ))
    requests_mock.put('/rpacontrol/api/feisuo/workflows/workflow1/_execute',
                      json={'success': True, 'data': {'execution_id': 12345}})
    status, err = rpa_client.process()
    assert status == JobStatus.RUNNING
    assert mock_job_states.get_job_state('execution_id') == 12345
    assert requests_mock.request_history[0].json() == {
        'payload': {'groupname': 'abc',
                    'content': 'content',
                    'image_urls': ['http://images.png']},
        'execution_id': 1}


def test_process_pdd_with_specific_assistant(client, mock_job_args, mock_job_states, requests_mock):
    rpa_client = mock_rpa_client_job('pdd_client_send_msg', 'workflow1')
    mock_job_args(
        branch=SendMessageArguments(
            usernick='',
            sender={"value": "MANUAL_INPUT", "extra": ["sub_nick1"]},
            content="content",
            image_urls=["url"],
            send_channel="rpa_client",
            skip=False
        ),
        tid=[{"tid": "123123"}]
    )
    requests_mock.put('/rpacontrol/api/feisuo/workflows/workflow1/_execute',
                      json={'success': True, 'data': {'execution_id': 12345}})
    status, _ = rpa_client.process()
    assert status == JobStatus.RUNNING, status
    assert mock_job_states.get_job_state('execution_id') == 12345
    assert requests_mock.request_history[0].json() == {
        'payload': {
            "pid": "sid",
            "receiver": "123123",
            "text": "content",
            "image_url": "url",
            "sub_nick": ["sub_nick1"]
        },
        'execution_id': 1}


def test_process_pdd_with_random_assistant(client, mock_job_args, mock_job_states, requests_mock):
    rpa_client = mock_rpa_client_job('pdd_client_send_msg', 'workflow1')
    mock_job_args(
        branch=SendMessageArguments(
            usernick='',
            sender={"value": "RANDOM_ONLINE", "extra": []},
            content="content",
            image_urls=["url"],
            send_channel="rpa_client",
            skip=False
        ),
        tid=[{"tid": "123123"}]
    )
    requests_mock.put('/rpacontrol/api/feisuo/workflows/workflow1/_execute',
                      json={'success': True, 'data': {'execution_id': 12345}})
    status, _ = rpa_client.process()
    assert status == JobStatus.RUNNING, status
    assert mock_job_states.get_job_state('execution_id') == 12345
    assert requests_mock.request_history[0].json() == {
        'payload': {
            "pid": "sid",
            "receiver": "123123",
            "text": "content",
            "image_url": "url"
        },
        'execution_id': 1}


def test_empty_assistant_error_pdd(client, mock_job_args, mock_job_states, requests_mock):
    rpa_client = mock_rpa_client_job('pdd_client_send_msg', 'workflow1')
    mock_job_args(
        branch=SendMessageArguments(
            usernick='',
            sender={"value": "MANUAL_INPUT", "extra": []},
            content="content",
            image_urls=["url"],
            send_channel="rpa_client",
            skip=False
        ),
        tid=[{"tid": "123123"}]
    )
    requests_mock.put('/rpacontrol/api/feisuo/workflows/workflow1/_execute',
                      json={'success': True, 'data': {'execution_id': 12345}})
    status, error = rpa_client.process()
    assert status == JobStatus.FAILED, status
    assert error == "指定客服为空"


def test_invalid_send_type_pdd(client, mock_job_args, mock_job_states, requests_mock):
    rpa_client = mock_rpa_client_job('pdd_client_send_msg', 'workflow1')
    mock_job_args(
        branch=SendMessageArguments(
            usernick='',
            sender={"value": "INVALID", "extra": []},
            content="content",
            image_urls=["url"],
            send_channel="rpa_client",
            skip=False
        ),
        tid=[{"tid": "123123"}]
    )
    requests_mock.put('/rpacontrol/api/feisuo/workflows/workflow1/_execute',
                      json={'success': True, 'data': {'execution_id': 12345}})
    status, error = rpa_client.process()
    assert status == JobStatus.FAILED, status
    assert error == "发送类型不支持: [INVALID]"
