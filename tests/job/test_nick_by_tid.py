from unittest.mock import MagicMock

from robot_processor.enums import JobStatus
from robot_processor.job.nick_by_tid import NickByTradeExecutor


def test_process_no_tid(mock_job_args):
    mock_job_args(order_no=[])
    status, msg = NickByTradeExecutor(MagicMock()).process()
    assert status == JobStatus.FAILED
    assert msg == '订单号为空'


def test_fail_process(mocker, mock_job_args):
    mock_job_args(order_no=[{'tid': '123'}])
    mocker.patch("robot_processor.client.get_buyer_nick", return_value=None)
    mock_job, mock_job_wrapper = MagicMock(), MagicMock()
    executor = NickByTradeExecutor(mock_job)
    executor.job_wrapper = mock_job_wrapper
    status, msg = executor.process()
    assert status == JobStatus.FAILED
    assert msg == '未查找到订单号对应昵称'


def test_succeed(client, mocker, mock_full_job, mock_job_args, mock_job_states):
    mock_job_args(order_no=[{'tid': '123'}])
    mocker.patch("robot_processor.client.get_buyer_nick", return_value='usernick')
    executor = NickByTradeExecutor(mock_full_job)
    status, _ = executor.process()
    assert status == JobStatus.SUCCEED
    mock_job_states.write_job_output.assert_called_with(dict(usernick='usernick'))
