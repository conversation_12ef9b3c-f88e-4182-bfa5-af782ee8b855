from robot_processor.enums import JobStatus
from robot_processor.ext import db
from robot_processor.job.n8n import N8NExecutor


def test_process(
        mocker, mock_business_order, job_factory, mock_form,
        client, mock_step, mock_grant_record,
        ignore_token_bucket_rate_limit, mock_job_args
):
    mock_form.subscribe(client.shop, True)
    mock_form.update_user = "nick"
    mock_form.name = "form_name 123"
    mock_business_order.form_id = mock_form.id
    mock_business_order.sid = client.shop.sid
    mock_step.form_id = mock_form.id
    client.shop.records.append(mock_grant_record)
    db.session.commit()
    mock_job = job_factory.create(step_id=mock_step.id, step_uuid=mock_step.step_uuid,
                                  business_order_id=mock_business_order.id)

    patcher = mocker.patch('rpa.n8n.n8n_client.session.post')
    executor = N8NExecutor(mock_job)
    status, _ = executor.process()
    assert status == JobStatus.RUNNING
    patcher.assert_called_once()
    bo_detail = patcher.call_args[1]['json']['business_order']
    assert bo_detail['id'] == mock_job.business_order_id
