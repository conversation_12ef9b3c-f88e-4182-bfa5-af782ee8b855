from result import Ok

from robot_processor.enums import JobStatus, ErpType
from robot_processor.job.sku import SkuExecutor


def test_jst_change_sku(mocker, client, mock_erp_type, mock_job_args, mock_full_job):
    mock_erp_type(ErpType.JST)
    mock_job_args(
        tid=[{"tid": "1q23e", "oid": "1q23e"}],
        new_outer_id=[{"sku": "4807141778484", "spu": "644241914591"}],
    )

    mocker.patch("rpa.erp.MolaJstClient.modify_sku", return_value=Ok(None))
    status, exception_info = SkuExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED

    mock_erp_type(ErpType.JST)
    mock_job_args(
        tid=[{"tid": "1q23e", "oid": "1q23e"}],
        new_outer_id=[{"SKU": "4807141778484", "SPU": "644241914591"}],
    )

    mocker.patch("rpa.erp.MolaJstClient.modify_sku", return_value=Ok(None))
    status, exception_info = SkuExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED


def test_kuaimai_change_sku(mocker, client, mock_erp_type, mock_job_args, mock_full_job, requests_mock):
    mock_erp_type(ErpType.KUAIMAI)
    mock_job_args(
        tid=[{"tid": "133", "oid": "123"}],
        new_sku=[{"sku": "4807141778484", "spu": "644241914591", "quantity": 1}],
    )

    requests_mock.post('/namespaces/grayerp/methods/original-order-change-sku-list',
                       json={"success": True, "result": {"data": {}}})
    status, msg = SkuExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED


def test_duohong_change_sku(requests_mock, client, mock_erp_type, mock_job_args, mock_full_job):
    mock_erp_type(ErpType.DUOHONG)
    mock_job_args(
        tid=[{"tid": "133", "oid": "1516575145066781075"}],
        new_sku=[{"sku": "4807141778484", "spu": "644241914591", "outer_sku": "4807141778484", "quantity": 3}]
    )
    requests_mock.post('/namespaces/duohong-erp/methods/do-modify-trade-skus',
                       json={"success": True, "result": {"data": {}}})
    status, msg = SkuExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED

    mock_job_args(
        tid=[{"tid": "133", "oid": "1516575145066781075"}],
        new_sku=[{"SKU": "4807141778484", "SPU": "644241914591",
                  "SKU_OUTER": "4807141778484"}]
    )
    requests_mock.post('/namespaces/duohong-erp/methods/do-modify-trade-skus',
                       json={"success": True, "result": {"data": {}}})
    status, msg = SkuExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED
