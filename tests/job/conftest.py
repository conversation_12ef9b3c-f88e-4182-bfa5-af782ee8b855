from unittest.mock import Mock, MagicMock

from pytest import fixture

from robot_processor.ext import db
from robot_processor.job.job_model_wrapper import Trade, JobArguments


@fixture
def ignore_token_bucket_rate_limit(mocker):
    yield mocker.patch("robot_processor.client._token_bucket_limiter.TokenBucketLimiter.try_acquire_token_for_store",
                       return_value=True)


class MockJobArgs:
    def __init__(self, data, key_map=None):
        self.bo_data = data
        self.key_map = key_map or {}

    def mock_args(self, **kwargs):
        self.bo_data.update(kwargs)

    def get_arg_by_task_arg_name(self, name):
        return self.bo_data.get(name)

    def get_arg_as_str_by_task_arg_name(self, name):
        return self.bo_data.get(name)

    def get_trades(self, arg_name='tid'):
        return [
            Trade(tid=t['tid'], oid=t.get('oid'))
            for t in self.bo_data.get(arg_name, [])
        ]

    def get_send_message_arguments(self):
        return self.get_arg_by_task_arg_name('branch')

    def is_argument_configured(self, arg_name):
        return self.bo_data.get(arg_name) is not None

    def get_configured_arguments(self):
        return list(self.bo_data.keys())

    @staticmethod
    def is_new_product_widget(item: dict):
        return "SPU" in item.keys()

    def is_argument_configured_in_task_arguments(self, arg_name):
        return False

    get_reissue_skus = JobArguments.get_reissue_skus
    get_address = JobArguments.get_address


@fixture
def mock_bo_data():
    return {}


@fixture
def mock_job_args(mocker, mock_bo_data):
    args = MockJobArgs(mock_bo_data)
    mocker.patch('robot_processor.job.base.JobControllerBase.args', new_callable=lambda: args)
    return args.mock_args


@fixture
def mock_erp_type(mock_erp_info):
    def change_erp_type(erp_type):
        mock_erp_info.erp_type = erp_type

    return change_erp_type


@fixture
def mock_random_full_job(client_unauthorized, mock_job, mock_business_order, mock_erp_info, mock_step):
    client, _, shop = client_unauthorized.login()
    mock_job.business_order_id = mock_business_order.id
    mock_business_order.sid = shop.sid
    mock_erp_info.shop_id = shop.id
    mock_job.step_id = mock_step.id
    mock_job.step_uuid = mock_step.step_uuid
    mock_erp_info.meta = {
        'sid': shop.sid,
        'wdt_appkey': 'wdt_appkey',
        'wdt_secret': 'wdt_secret',
        'wdt_salt': 'wdt_salt',
        'appKey': 'appKey',
        'appSecret': 'appSecret',
        'baseUrl': 'http://localhost:8000',
        'after_sale_shop_no': 'after_sale_shop_no',
        'co_id': 'co_id',
    }
    return mock_job


class MockJobStates:
    def __init__(self, data: dict):
        self.bo_data = data
        self.states = {}
        self.write_job_output = Mock(side_effect=self._write_job_output)
        self.write_job_widget_collection = Mock(side_effect=self._write_job_widget_collection)

    def get_job_state(self, state_name, default_value=None):
        return self.states.get(state_name, default_value)

    def write_job_states(self, states, replace=False):
        if replace:
            self.states = states
        else:
            self.states.update(states)

    def _update_bo_data(self, data: dict):
        self.bo_data.update(data)

    def _write_job_output(self, data: dict, allow_empty_value=True):
        missing_args = []
        for k, v in data.items():
            if not (old_v := self.bo_data.get(k)) or old_v not in ("自动", "勿动"):
                continue

            if not allow_empty_value and v == "":
                missing_args.append(k)
                continue

            self.bo_data[k] = v
        return missing_args

    def check_widget_is_table_by_arg_name(self, arg_name):
        return False

    def write_job_output_with_multi_row(self, datas):
        pass

    def _write_job_widget_collection(self, data):
        return MagicMock(missing=[])


@fixture
def mock_job_states(mocker, mock_bo_data):
    states = MockJobStates(mock_bo_data)
    mocker.patch('robot_processor.job.base.JobControllerBase.states', new_callable=lambda: states)
    return states


@fixture
def mock_job_with_bo(mock_job, mock_business_order):
    mock_job.business_order_id = mock_business_order.id
    db.session.commit()
    yield mock_job
