from robot_processor.enums import JobStatus
from robot_processor.job.logistics import LogisticsExecutor
from tests.client.test_mola import MockResponse


def mock_response():
    data = '{"success":true,"result":{"data":{"negotiation_0":{"fields":{' \
           '"time":"2021-12-20 13:54:18","info":"商家(唐狮官方旗舰店)已经发出换新货【内容】' \
           '物流公司：EMS标准快递发货单号：1184045113866收货地址：亚楠， 15908933566， 山东省青岛' \
           '市城阳区上马街道 东风馨苑快递柜， 000000"}},"negotiation_1":{"fields":{"time":"202' \
           '1-12-20 13:52:12","info":"买家(ssisky)于2021-12-20 13时52分12秒创建了换货申请【内容' \
           '】换货商品：【天猫双12】唐狮秋冬款打底裤女外穿加绒加厚魔术铅笔小黑裤高腰黑色保暖棉裤 颜色分类:黑' \
           '色（双扣高腰有拉链款）/L、XL码预售12月31日发货;尺寸:XL换货数量：1收货地址：亚楠， 15908933566' \
           '， 山东省青岛市城阳区上马街道 东风馨苑快递柜， 000000换货原因：7天无理由换货换货说明："}}}}}'
    return MockResponse(status_code=0, text=data)


def mock_error_response():
    data = '{"success":true,"result":{"data":{"negotiation_0":{"fields":{' \
           '"time":"2021-12-20 13:54:18","info":"商家(唐狮官方旗舰店)已经发出换新货【内容】' \
           '收货地址：亚楠， 15908933566， 山东省青岛' \
           '市城阳区上马街道 东风馨苑快递柜， 000000"}},"negotiation_1":{"fields":{"time":"202' \
           '1-12-20 13:52:12","info":"买家(ssisky)于2021-12-20 13时52分12秒创建了换货申请【内容' \
           '】换货商品：【天猫双12】唐狮秋冬款打底裤女外穿加绒加厚魔术铅笔小黑裤高腰黑色保暖棉裤 颜色分类:黑' \
           '色（双扣高腰有拉链款）/L、XL码预售12月31日发货;尺寸:XL换货数量：1收货地址：亚楠， 15908933566' \
           '， 山东省青岛市城阳区上马街道 东风馨苑快递柜， 000000换货原因：7天无理由换货换货说明："}}}}}'
    return MockResponse(status_code=0, text=data)


def test_tmall_logistics_process(mocker, client, mock_job_args, mock_full_job, mock_job_states):
    mocker.patch('time.sleep')
    mock_job_args(dispute_id="143320465203565081")
    mocker.patch("rpa.mola.MolaClient.session.post", return_value=mock_response())

    status, msg = LogisticsExecutor(mock_full_job).process()
    assert status == JobStatus.SUCCEED
    mock_job_states.write_job_output.assert_called()


def test_tmall_logistics_process_fail(mocker, client, mock_job_args, mock_full_job):
    mocker.patch('time.sleep')
    mock_job_args(dispute_id="143320465203565081")
    mocker.patch("rpa.mola.MolaClient.session.post", return_value=mock_error_response())
    status, msg = LogisticsExecutor(mock_full_job).process()
    assert status == JobStatus.FAILED
