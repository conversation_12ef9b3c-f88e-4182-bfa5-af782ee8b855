import pytest

from robot_processor.enums import StepType, JobStatus, JobProcessMark
from robot_processor.ext import db
from robot_processor.form.models import Step
from robot_processor.job.skip_helper import SkipHelper


@pytest.fixture
def mock_business_order(mock_form, business_order_factory, step_factory, widget_collection_factory,
                        widget_info_factory, client, job_factory, widget, rpa):
    """
    一个含有可跳过步骤的表单
    """

    string_widget = widget('单行输入')
    # 一个单行输入组件
    input_widget = widget_info_factory.create(
        widget_id=string_widget.id,
        option_value={"label": "普通单行输入", "category": 2, "required": True,
                      "hasTopSpace": False, "linkProvider": False})

    # 一个用来回填rpa输出的单行输入组件
    output_widget = widget_info_factory.create(
        widget_id=string_widget.id,
        option_value={"label": "一个rpa输出", "unique": True, "category": 2, "required": True,
                      "hasTopSpace": False, "linkProvider": False})

    # step1 是第一步，人工输入
    # step1 中有一个widget是用来给rpa步骤回填的
    _step_1 = step_factory.create(step_type=StepType.human, form_id=mock_form.id,
                                  widget_collection=widget_collection_factory.create(
                                      widget_info=[output_widget]
                                  ))
    kuimai_rpa = rpa(name='订单信息回执[快麦]')

    _step_2 = step_factory.create(step_type=StepType.auto, form_id=mock_form.id,
                                  prev_step_ids=[_step_1.step_uuid], data={"rpa_id": kuimai_rpa.id},
                                  # rpa步骤的输出回写到output组件中
                                  key_map={'goods_title': output_widget.key},
                                  widget_collection=widget_collection_factory.create(
                                      widget_info=[output_widget]
                                  ))
    _step_3 = step_factory.create(step_type=StepType.human, form_id=mock_form.id,
                                  prev_step_ids=[_step_2.step_uuid],
                                  widget_collection=widget_collection_factory.create(
                                      widget_info=[input_widget, output_widget]
                                  ))
    _step_4 = step_factory.create(step_type=StepType.human, form_id=mock_form.id,
                                  prev_step_ids=[_step_3.step_uuid],
                                  widget_collection=widget_collection_factory.create(
                                      widget_info=[input_widget, output_widget]
                                  ))
    _step_1.next_step_ids = [_step_2.step_uuid]
    _step_2.next_step_ids = [_step_3.step_uuid]
    _step_3.next_step_ids = [_step_4.step_uuid]
    db.session.commit()

    mock_form.subscribe(client.shop, True)
    bo = business_order_factory.create(form_id=mock_form.id, sid=client.shop.sid)
    job_1 = job_factory.create(business_order_id=bo.id, step_id=_step_1.id, step_uuid=_step_1.step_uuid)
    job_2 = job_factory.create(business_order_id=bo.id, step_id=_step_2.id, step_uuid=_step_2.step_uuid)
    job_3 = job_factory.create(business_order_id=bo.id, step_id=_step_3.id, step_uuid=_step_3.step_uuid)
    job_4 = job_factory.create(business_order_id=bo.id, step_id=_step_4.id, step_uuid=_step_4.step_uuid)
    bo.job_history = [job_1.id, job_2.id, job_3.id, job_4.id]
    db.session.commit()
    yield bo


def test_skip_rpa(client, mock_business_order):
    first, rpa_job, quote_rpa, last_step = mock_business_order.jobs.all()
    skip_helper = SkipHelper(rpa_job)
    # 验证获取所有可能走到的后序step
    assert len(skip_helper._get_subsequent_steps()) == 2

    # 验证获取所有 有输出项的rpa
    assert rpa_job.step.data["rpa_id"] in skip_helper._has_output_rpas()

    # mock raw_ui_schema
    raw_step_v2 = {
        "key_map": {},
        "step_type": "auto",
        "ui_schema": [{'id': 10,
                       'key': Step.get_step_widgets(last_step.step_id)[0][1],
                       'type': 'usernick',
                       'label': '买家昵称',
                       'order': 2,
                       'before': False,
                       'unique': True,
                       'category': 1,
                       'widget_id': 10,
                       'widget_meta': [],
                       'option_value': {'label': '买家昵称',
                                        'category': 1,
                                        'required': True,
                                        'checkType': 1,
                                        'valueUnique': True,
                                        'defaultValue': '当前买家'}}]}
    rpa_job._raw_step_v2 = raw_step_v2
    assert skip_helper.get_skip_required_inputs(rpa_job.step).pop()["key"] == \
           list(rpa_job.step.key_map.values())[0]

    # 验证跳过之后的状态和process_mark
    skip_helper.skip()
    assert rpa_job.status == JobStatus.SUCCEED
    assert rpa_job.process_mark == JobProcessMark.SKIP


def test_auto_skip_when_fail(client, mock_business_order):
    """
    测试自动跳过
    """
    _, job_fail_auto_skip, next_job, _ = mock_business_order.jobs.all()
    # 将第二步的rpa设置为一个可跳过的rpa
    step = job_fail_auto_skip.step
    step.can_auto_skip_when_fail = True
    step.can_skip = True
    db.session.commit()
    auto_skip_result = SkipHelper(job_fail_auto_skip).try_auto_skip()
    assert auto_skip_result


def test_get_skip_required_inputs(client, mock_business_order):
    job1, rpa_with_output_job, job3, job4 = mock_business_order.jobs.all()
    mock_business_order.job_history = [job1.id, rpa_with_output_job.id,
                                       job3.id, job4.id]
    # 在第一个步骤构造回填的widget_info key
    widget_keys = list(rpa_with_output_job.raw_step_v2["key_map"].values())
    key = widget_keys[0]
    raw_step_v2 = rpa_with_output_job.raw_step_v2
    raw_step_v2["ui_schema"] = [{'id': 10,
                                 'key': key,
                                 'type': 'usernick',
                                 'label': '买家昵称',
                                 'order': 2,
                                 'before': False,
                                 'unique': True,
                                 'category': 1,
                                 'widget_id': 10,
                                 'widget_meta': [],
                                 'option_value': {'label': '买家昵称',
                                                  'category': 1,
                                                  'required': True,
                                                  'checkType': 1,
                                                  'valueUnique': True,
                                                  'defaultValue': '当前买家'}}]
    rpa_with_output_job._raw_step_v2 = raw_step_v2
    db.session.commit()
    resp = client.get(
        f"/v1/seller/skip_inputs/{rpa_with_output_job.id}"
    )

    assert resp.status_code == 200
