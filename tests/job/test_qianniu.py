from unittest.mock import MagicMock

from leyan_proto.digismart.buyer_server.buyer_server_rpc_pb2 import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>erIdByBuyerNickResponse
from leyan_proto.digismart.dgt_common_pb2 import DgtResponseCode
from result import Ok

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.enums import JobStatus
from robot_processor.job.job_model_wrapper import SendMessageArguments
from robot_processor.job.qianniu import SenderSchema, SenderConfig, UserGroup, QianniuExecutor
from robot_processor.job.qianniu import chose_online_user_from_chat


def test_process_skipped(mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        usernick='usernick',
        sender={'value': 'FIXED', 'extra': 'abc'},
        content='content',
        image_urls=[],
        send_channel='RPA_CLIENT',
        skip=True,
    ))
    executor = QianniuExecutor(MagicMock())
    status, msg = executor.process()
    assert status == JobStatus.SUCCEED


def test_long_content(mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        usernick='usernick',
        sender={'value': 'FIXED', 'extra': 'abc'},
        content='c'*1001,
        image_urls=[],
        send_channel='RPA_CLIENT',
        skip=False,
    ))
    executor = QianniuExecutor(MagicMock())
    status, msg = executor.process()
    assert status == JobStatus.FAILED
    assert msg == '内容超过长度限制, 当前长度: 1001, 限制长度: 1000'


def test_no_usernick(mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        usernick='',
        sender={'value': 'FIXED', 'extra': 'abc'},
        content='content',
        image_urls=[],
        send_channel='RPA_CLIENT',
        skip=False,
    ))
    executor = QianniuExecutor(MagicMock())
    status, msg = executor.process()
    assert status == JobStatus.FAILED
    assert msg == '买家信息和订单号都为空'


def test_get_senders_manual_input():
    sender_schema = SenderSchema(value='MANUAL_INPUT', extra=['user1', 'user2'])
    senders = sender_schema.get_senders(MagicMock(), 'usernick')
    assert len(senders) == 2
    assert [s.user_nick for s in senders] == ['user1', 'user2']


def test_get_sender_input_text():
    sender_schema = SenderSchema(value='INPUT_TEXT', extra='user1')
    senders = sender_schema.get_senders(MagicMock(), 'usernick')
    assert len(senders) == 1
    assert senders[0].user_nick == 'user1'


def test_get_sender_updater(client):
    sender_schema = SenderSchema(value='UPDATER', extra='')
    mock_job = MagicMock(shop=client.shop)
    mock_job.prev.is_human.return_value = True
    mock_job.prev.get_assignee_assistant.return_value = client.assistant
    mock_job.get_prev_jobs.return_value = [mock_job.prev]
    senders = sender_schema.get_senders(mock_job, '')
    assert len(senders) == 1
    assert senders[0].user_nick == client.assistant.user_nick


def test_get_sender_creator(client):
    sender_schema = SenderSchema(value='CREATOR', extra='')
    mock_job = MagicMock(shop=client.shop)
    mock_job.business_order.creator_user_id = client.assistant.user_id
    mock_job.business_order.creator_type = client.assistant.user_type
    senders = sender_schema.get_senders(mock_job, '')
    assert len(senders) == 1
    assert senders[0].user_nick == client.assistant.user_nick


def test_get_sender_random():
    sender_schema = SenderSchema(value='RANDOM_ONLINE', extra='user1')
    mock_job = MagicMock()
    senders = sender_schema.get_senders(mock_job, 'usernick')
    assert senders == []


def test_get_sender_fixed(client):
    sender_schema = SenderSchema(value='FIXED', extra=SenderConfig(
        details=[client.another_assistant],
        assignee_groups=[UserGroup(group_uuid='org1_platform_group1', enable=1)]
    ))
    mock_job = MagicMock(shop=client.shop)
    mock_job.business_order.sid = client.shop.sid
    mock_job.business_order.shop = client.shop
    senders = sender_schema.get_senders(mock_job, '')
    assert len(senders) == 2, '根据 fixtures/auth.py 中的设定, org1_platform_group1 内有两个用户'


def test_get_last_sender_by_last_reception(client, mocker):
    sender_schema = SenderSchema(value='LAST_RECEPTION', extra='')
    mock_get_buyer_id = mocker.patch('robot_processor.client.buyer.BuyerClient.get_buyer_info_by_buyer_nick',
                                     return_value=GetLeyanBuyerIdByBuyerNickResponse(
                                         code=DgtResponseCode.OK, leyan_buyer_id="leyan_buyer_id"))
    mock_get_assistant = mocker.patch('robot_processor.client.chat.ChatClient.get_last_assistant_by_store_buyer',
                                      return_value=Ok(client.assistant.user_nick))
    senders = sender_schema.get_senders(MagicMock(shop=client.shop), 'usernick')
    assert len(senders) == 1
    assert senders[0].user_nick == client.assistant.user_nick
    mock_get_buyer_id.assert_called_once()
    mock_get_assistant.assert_called_once()


def test_send_by_chat_client_no_online_assistant(mocker, mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        usernick='usernick',
        sender={'value': 'INPUT_TEXT', 'extra': 'assistant1'},
        content='content',
        image_urls=[],
        send_channel='CHAT_CLIENT',
        skip=False,
    ))
    mock_get_assistants = mocker.patch('robot_processor.client.chat.ChatClient.get_online_assistants_by_sid',
                                       return_value=MagicMock(is_ok=False))
    executor = QianniuExecutor(MagicMock())
    status, msg = executor.process()
    mock_get_assistants.assert_called_once()
    assert status == JobStatus.FAILED
    assert msg == '无客服发送失败'


def test_send_by_chat_client_succeed(mocker, mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        usernick='usernick',
        sender={'value': 'INPUT_TEXT', 'extra': 'assistant1'},
        content='content',
        image_urls=[],
        send_channel='CHAT_CLIENT',
        skip=False,
    ))
    mock_get_assistants = mocker.patch('robot_processor.client.chat.ChatClient.get_online_assistants_by_sid',
                                       return_value=Ok(['assistant1']))
    mock_send_message = mocker.patch('robot_processor.client.chat.ChatClient.send_message',
                                     return_value=Ok(None))
    executor = QianniuExecutor(MagicMock())
    status, msg = executor.process()
    mock_get_assistants.assert_called_once()
    assert status == JobStatus.SUCCEED
    mock_send_message.assert_called_once_with('assistant1', 'usernick', 'content', [])


def test_send_by_rpa_client(mocker, mock_job_args, ignore_token_bucket_rate_limit):
    mock_job_args(branch=SendMessageArguments(
        usernick='usernick',
        sender={'value': 'RANDOM_ONLINE', 'extra': ''},
        content='content',
        image_urls=['http://example.com/img.png'],
        send_channel='RPA_CLIENT',
        skip=False,
    ))
    mock_send_message = mocker.patch('rpa.mola.client.MolaClient.send_qianniu_message',
                                     return_value=Ok({}))
    executor = QianniuExecutor(MagicMock())
    executor.job_wrapper = MagicMock()
    executor.job_wrapper.shop.sid = 'mock-sid'
    status, msg = executor.process()
    assert status == JobStatus.SUCCEED
    mock_send_message.assert_called_once_with('', 'usernick', 'content', 'http://example.com/img.png')


def test_choose_online_senders(mocker):
    def mock_choice(arr):
        if not arr:
            return None
        return arr[0]

    mocker.patch("robot_processor.job.qianniu.chat_client.get_online_assistants_by_sid",
                 return_value=Ok(["assistant1", "assistant2"]))
    mocker.patch("random.choice", side_effect=mock_choice)
    # 有指定senders
    senders = [AccountDetailV2(user_type=2,
                               user_id=None,
                               phone=None,
                               sub_id=None,
                               user_nick='Assistant2'),
               AccountDetailV2(user_type=2,
                               user_id=None,
                               phone=None,
                               sub_id=None,
                               user_nick='assistant3')]
    target = chose_online_user_from_chat("sid", senders)
    assert target == "assistant2"

    # 未指定senders
    senders = []
    target = chose_online_user_from_chat("sid", senders)
    assert target == "assistant1"

    # 指定了senders, 但是senders不在线
    senders = [AccountDetailV2(user_type=2,
                               user_id=None,
                               phone=None,
                               sub_id=None,
                               user_nick='assistant3')]
    target = chose_online_user_from_chat("sid", senders)
    assert target == "assistant1"
