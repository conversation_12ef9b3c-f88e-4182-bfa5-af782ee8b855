from robot_processor.enums import ErpType
from robot_processor.job.complete_trade_item import CompleteTradeItemExecutor
from rpa.erp.wdt import TradeQueryResp

WDT_TRADE_QUERY_RESPONSE = '''
{
  "response": {
    "errorcode": 0,
    "message": "ok",
    "total_count": 1,
    "trades": [
      {
        "bad_reason": 0,
        "buyer_message": "无痕发货",
        "buyer_message_count": "1",
        "buyer_nick": "",
        "cancel_reason": "0",
        "check_step": "0",
        "checker_id": 211,
        "checker_name": "高怡",
        "checkouter_id": 0,
        "checkouter_name": "",
        "cod_amount": "0.0000",
        "commission": "3.7400",
        "consign_status": 12,
        "created": "2023-03-20 00:56:15",
        "cs_remark": "",
        "cs_remark_change_count": "0",
        "cs_remark_count": "0",
        "currency": "",
        "customer_id": "4553846",
        "customer_name": "",
        "customer_no": "KH202303200265",
        "customer_type": "0",
        "dap_amount": "68.0000",
        "delay_to_time": "0",
        "delivery_term": 1,
        "discount": "108.0000",
        "discount_change": "0.0000",
        "ext_cod_fee": "0.0000",
        "fchecker_id": 0,
        "fchecker_name": "系统",
        "fenxiao_nick": "",
        "fenxiao_tid": "",
        "fenxiao_type": 0,
        "flag_id": "0",
        "flag_name": "",
        "freeze_reason": 0,
        "freeze_reason_info": "",
        "fullname": "系统",
        "gift_mask": "0",
        "goods_amount": "176.0000",
        "goods_cost": "51.0000",
        "goods_count": "1.0000",
        "goods_list": [
          {
            "actual_num": "1.0000",
            "adjust": "0.0000",
            "api_goods_name": "全身镜穿衣落地镜女生家用卧室试衣镜ins风旋转可移动立体大镜子",
            "api_spec_name": "标准款-椭圆简约白;其他;是",
            "barcode": "",
            "base_unit_id": 0,
            "bind_oid": "",
            "cid": 200,
            "class_name": "无",
            "commission": "3.7400",
            "created": "2023-03-20 00:56:15",
            "delivery_term": 1,
            "discount": "108.0000",
            "flag": 0,
            "from_mask": 1,
            "gift_type": 0,
            "goods_id": 177,
            "goods_name": "全身镜落地镜家用女北欧简约网红ins风女生卧室少女试衣穿衣镜子",
            "goods_no": "JZ-01",
            "goods_type": 1,
            "guarantee_mode": "1",
            "invoice_content": "",
            "invoice_type": 0,
            "is_consigned": "1",
            "is_master": "1",
            "is_print_suite": "0",
            "is_received": "1",
            "is_zero_cost": "1",
            "large_type": 0,
            "modified": "2023-03-30 12:10:16",
            "num": "1.0000",
            "order_price": "68.0000",
            "paid": "68.0000",
            "pay_id": "2023032022001157021420779763",
            "pay_status": "2",
            "pay_time": "2023-03-20 00:56:07",
            "platform_goods_id": "655629585687",
            "platform_id": 1,
            "platform_spec_id": "4908218331987",
            "price": "176.0000",
            "prop2": "",
            "rec_id": 11179103,
            "refund_num": "0.0000",
            "refund_status": 0,
            "remark": "",
            "share_amount": "68.0000",
            "share_amount2": "0.0000",
            "share_post": "0.0000",
            "share_price": "68.0000",
            "spec_code": "",
            "spec_id": 5541,
            "spec_name": "简约白,标准款-椭圆-不可旋转",
            "spec_no": "JZ-01-椭圆-白",
            "src_oid": "3268940115984170159",
            "src_tid": "3268940115984170159",
            "stock_reserved": "0",
            "suite_amount": "0.0000",
            "suite_discount": "0.0000",
            "suite_id": 0,
            "suite_name": "",
            "suite_no": "",
            "suite_num": "0.0000",
            "tax_rate": "0.0000",
            "tc_order_id": "",
            "trade_id": 9706139,
            "unit_name": "",
            "weight": "9.5000"
          }
        ],
        "goods_type_count": 1,
        "id_card": "",
        "id_card_type": 0,
        "invoice_content": "",
        "invoice_id": 0,
        "invoice_title": "",
        "invoice_type": 0,
        "is_prev_notify": "0",
        "is_sealed": "0",
        "is_unpayment_sms": "0",
        "large_type": "0",
        "logistics_code": "646",
        "logistics_id": 734,
        "logistics_name": "淘系安能9.1(瑞格)偏远新疆西藏海南内蒙甘肃宁夏青海（黄山淳安建德绩溪县）",
        "logistics_no": "************",
        "logistics_template_id": "0",
        "logistics_type": 95,
        "modified": "2023-03-30 12:10:16",
        "note_count": "0",
        "other_amount": "0.0000",
        "other_cost": "0.0000",
        "package_id": "0",
        "paid": "68.0000",
        "pay_account": "",
        "pay_time": "2023-03-20 00:56:07",
        "pi_amount": "0.0000",
        "platform_id": 1,
        "post_amount": "0.0000",
        "post_cost": "15.7300",
        "pre_charge_time": "",
        "print_remark": "",
        "profit": "-2.4700",
        "raw_goods_count": "1.0000",
        "raw_goods_type_count": 1,
        "receivable": "68.0000",
        "receiver_address": "",
        "receiver_area": "江苏省 南通市 通州区",
        "receiver_city": 320600,
        "receiver_country": "0",
        "receiver_district": 320612,
        "receiver_dtb": " ",
        "receiver_mobile": "",
        "receiver_name": "",
        "receiver_province": "320000",
        "receiver_ring": "叶**",
        "receiver_telno": "",
        "receiver_zip": "000000",
        "refund_status": 0,
        "remark_flag": 0,
        "reserve": "",
        "revert_reason": "0",
        "sales_score": "0",
        "salesman_id": 0,
        "sendbill_template_id": "0",
        "shop_id": "8",
        "shop_name": "誉斯嘉旗舰店",
        "shop_no": "008",
        "shop_platform_id": "1",
        "shop_remark": "",
        "single_spec_no": "JZ-01-椭圆-白",
        "split_from_trade_id": "0",
        "split_package_num": "0",
        "src_tids": "3268940115984170159",
        "stockout_no": "CK202303208223",
        "tax": "0.0000",
        "tax_rate": "0.0000",
        "to_deliver_time": "",
        "trade_from": 1,
        "trade_id": 9706139,
        "trade_mask": "131200",
        "trade_no": "JY202303200266",
        "trade_prepay": "0.0000",
        "trade_status": 110,
        "trade_time": "2023-03-20 00:56:01",
        "trade_type": 1,
        "unmerge_mask": "0",
        "version_id": 5,
        "volume": "42385.0000",
        "warehouse_id": "82",
        "warehouse_no": "77",
        "warehouse_type": 1,
        "weight": "9.5000"
      }
    ]
  }
}
'''


def test_wdt_process(mocker, client, mock_erp_type, mock_job_args, mock_full_job, mock_job_states):
    mock_erp_type(ErpType.WDT)

    mock_job_args(erp_tid=[{"tid": "1568978185083689575BF54ff"}], tid=[{"tid": "1568978185083689575BF54ff", "oid": ""}],
                  logistics_no="sf1234", goods_title="勿动", goods_props="同步", goods_code="自动")

    mocker.patch(
        "rpa.erp.wdt.WdtClient.trade_query",
        return_value=(
            TradeQueryResp(**eval(WDT_TRADE_QUERY_RESPONSE))))

    CompleteTradeItemExecutor(mock_full_job).process()
    assert "全身镜落地镜家用女北欧简约网红ins风女生卧室少女试衣穿衣镜子" == mock_job_states.bo_data["goods_title"]


def test_wdtulti_process(mocker, client, mock_erp_type, mock_job_args, mock_full_job, mock_job_states):
    mock_erp_type(ErpType.WDTULTI)
    mock_job_args(erp_tid=[{"tid": "1549422229454689575BFe9be"}], logistics_no="sf1234",
                  goods_title="勿动", goods_props="同步", goods_code="自动",
                  tid=[{"tid": "1549422229454689575BFe9be", "oid": ""}])

    mocker.patch(
        "rpa.erp.wdtulti.WdtUltiClient.trade_query",
        return_value=(
            eval('{"response":{"status": 0,"data": {"total_count": 1,"order": '
                 '[{"logistics_no": "sf1234","detail_list":['
                 '{"goods_name": "商品1", "spec_name": "型号1", "spec_no": "编码1"},'
                 '{"goods_name": "商品2", "spec_name": "型号2", "spec_no": "编码2"}]'
                 '}]}}}')))

    CompleteTradeItemExecutor(mock_full_job).process()
    assert "商品1;商品2" == mock_job_states.bo_data["goods_title"]


def test_jst_process(mocker, client, mock_erp_type, mock_job_args, mock_full_job, mock_job_states):
    mock_erp_type(ErpType.JST)
    mock_job_args(erp_tid="26267101598573730061", is_reissue=False, goods_title="自动")
    from tests.job.test_jst_trade import api_order
    # 对于订单中商品没有的属性，例如property_values，有可能是None，会在后续被过滤并正常处理
    api_order.items[0].properties_value = None
    mocker.patch(
        "robot_processor.job.complete_trade_item.TradesFinder.process",
        return_value=[api_order]
    )

    CompleteTradeItemExecutor(mock_full_job).process()
    assert "短袖1" == mock_job_states.bo_data["goods_title"]
