from unittest.mock import MagicMock

from robot_processor.client.wecom import <PERSON>ComSender
from robot_processor.enums import Job<PERSON>tatus
from robot_processor.job.wecom import WeComExecutor
from robot_processor.job.job_model_wrapper import SendMessageArguments


def test_rate_limit(mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        signature='signature', url='url',
        content='content',
        image_urls=['http://example.com/image'],
        send_channel='RPA_CLIENT',
        skip=True,
    ))
    executor = WeComExecutor(MagicMock())
    limit_key, limit_value = executor.get_rate_limit_setting({})
    assert limit_key.startswith('JOB_LIMITER_SEND_WECOM_GROUP')
    assert len(limit_key.rsplit('_', 1)[-1]) == 32  # md5 hash hex length
    assert limit_value == '20/minute'


def test_process_skipped(mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=key',
        content='content',
        image_urls=['http://example.com/image'],
        send_channel='RPA_CLIENT',
        skip=True,
    ))
    executor = WeComExecutor(MagicMock())
    status, msg = executor.process()
    assert status == JobStatus.SUCCEED


def test_process_call_wecom_fail(mocker, mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=key',
        sender={'value': 'INPUT_TEXT_MOBILE', 'extra': '13800000000,13800000001'},
        content='content',
        image_urls=[],
        send_channel='RPA_CLIENT',
        skip=False,
    ))
    executor = WeComExecutor(MagicMock())
    mocked_post = mocker.patch.object(WeComSender.session, 'post')
    mock_response = MagicMock()
    mock_response.raise_for_status.side_effect = Exception('mock-error')
    mocked_post.return_value = mock_response
    status, msg = executor.process()
    assert status == JobStatus.FAILED
    mocked_post.assert_called_once()
    assert msg == '调用企业微信消息发送接口失败: mock-error'


def test_process_call_wecom_error(mocker, mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=key',
        sender={'value': 'INPUT_TEXT_MOBILE', 'extra': '13800000000,13800000001'},
        content='content',
        image_urls=[],
        send_channel='RPA_CLIENT',
        skip=False,
    ))
    executor = WeComExecutor(MagicMock())
    mocked_post = mocker.patch.object(WeComSender.session, 'post')
    mocked_post.return_value = MagicMock(status_code=200, json=lambda: {'errcode': 400013, 'errmsg': 'mock-error'})
    status, msg = executor.process()
    assert status == JobStatus.FAILED
    mocked_post.assert_called_once()
    assert msg == 'mock-error'


def test_process_send_only_text_succeed(mocker, mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=key',
        sender={'value': 'INPUT_TEXT_MOBILE', 'extra': '13800000000,13800000001'},
        content='content',
        image_urls=[],
        send_channel='RPA_CLIENT',
        skip=False,
    ))
    executor = WeComExecutor(MagicMock())
    mocked_post = mocker.patch.object(WeComSender.session, 'post')
    mocked_post.return_value = MagicMock(status_code=200, json=lambda: {'errcode': 0, 'errmsg': 'ok'})
    status, msg = executor.process()
    assert status == JobStatus.SUCCEED
    mocked_post.assert_called_once()
    post_body = mocked_post.call_args[1]['json']
    assert post_body['msgtype'] == 'text'
    assert post_body['text']['content'] == 'content'
    assert post_body['text']['mentioned_mobile_list'] == ['13800000000', '13800000001']


def test_process_send_text_picture_succeed(mocker, mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=key',
        sender={'value': 'INPUT_TEXT_MOBILE', 'extra': '13800000000,13800000001'},
        content='content',
        image_urls=['http://example.com/img1.png', 'http://example.com/img1.png'],
        text_picture_merge={'value': 'SEPARATE'},
        send_channel='RPA_CLIENT',
        skip=False,
    ))

    mocker.patch('robot_processor.client.wecom.WeComSender.image_base64_md5', return_value=('test', 'test'))

    executor = WeComExecutor(MagicMock())
    mocked_post = mocker.patch.object(WeComSender.session, 'post')
    mocked_post.return_value = MagicMock(status_code=200, json=lambda: {'errcode': 0, 'errmsg': 'ok'})
    status, msg = executor.process()
    assert status == JobStatus.SUCCEED


def test_process_send_markdown_succeed(mocker, mock_job_args):
    mock_job_args(branch=SendMessageArguments(
        url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=key',
        content='content',
        image_urls=['http://example.com/img1.png', 'http://example.com/img1.png'],
        text_picture_merge={'value': 'MERGE'},
        send_channel='RPA_CLIENT',
        skip=False,
    ))
    executor = WeComExecutor(MagicMock())
    mocked_post = mocker.patch.object(WeComSender.session, 'post')
    mocked_post.return_value = MagicMock(status_code=200, json=lambda: {'errcode': 0, 'errmsg': 'ok'})
    status, msg = executor.process()
    assert status == JobStatus.SUCCEED
    mocked_post.assert_called_once()
    post_body = mocked_post.call_args[1]['json']
    assert post_body['msgtype'] == 'markdown'
    assert post_body['markdown']['content'] == 'content \n [图片1链接](http://example.com/img1.png' \
                                               ') \n [图片2链接](http://example.com/img1.png)'
