from robot_processor.enums import JobStatus
from robot_processor.job.doudian_decrypt_api import (DoudianDecryptAPIExecutor,
                                                     DoudianDecryptOutput)


def test_doudian_decrypt_api(mocker, mock_random_full_job, mock_job_args, mock_job_states):
    mock_job_args(
        tid=[{"tid": "123123"}]
    )
    mock_random_full_job.shop.platform = "DOUDIAN"
    mock_receiver_info = {
        "receiver_name": "receiver_name",
        "receiver_phone": "receiver_phone",
        "province": "province",
        "city": "city",
        "town": "town",
        "street": "street",
        "detail": "detail"
    }
    mocker.patch("robot_processor.client.doudian_cloud._request",
                 return_value={
                     "decrypted_data": mock_receiver_info
                 })
    expected_output = DoudianDecryptOutput(
        receiver_name="receiver_name",
        receiver_phone="receiver_phone",
        receiver_address="province city town street detail"
    )
    status, error = DoudianDecryptAPIExecutor(mock_random_full_job).process()
    assert status == JobStatus.SUCCEED, error
    mock_job_states.write_job_widget_collection.assert_called_with(expected_output.dict())
