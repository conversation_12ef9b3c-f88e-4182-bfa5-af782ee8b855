"""兼容性测试"""
from robot_processor.enums import ErpType
from robot_processor.job.trade import TradeExecutor


def test_jst_process(mocker, client, mock_erp_type, mock_job_args, mock_full_job, mock_job_states):
    mock_erp_type(ErpType.JST)
    mock_job_args(erp_tid="26267101598573730061", is_reissue=False, goods_title="自动")
    from tests.job.test_jst_trade import api_order
    mocker.patch(
        "robot_processor.job.complete_trade_item.TradesFinder.process",
        return_value=[api_order]
    )
    TradeExecutor(mock_full_job).process()
    # 根据rpa argument获取的所有rpa输出
    output_args = ['logistics_company', 'wms_co_name', 'pay_amount', 'l_id', 'status', 'supplier_name', 'wave_id',
                   'picker_name']
    write_output_args = mock_job_states.write_job_output.call_args_list[0]
    for arg in output_args:
        assert arg in write_output_args[0][0].keys()
