from typing import Named<PERSON>uple

from pytest import fixture

from robot_processor.business_order.models import Job
from robot_processor.enums import JobStatus
from robot_processor.job.yto_ding import YtoMsgExecutor
from tests.testbed import BusinessOrderBuilder


@fixture
def testbed(client):
    # tests/job/resources/yto-kh.send-message.default.json
    testbed = BusinessOrderBuilder(
        {
            "form_template": "yto-kh.send-message.default",
            "aid": "ut",
            "current_job": "圆钉发消息",
            "jobs": [
                {"name": "填写表单", "assignee": "ut"},
                {"name": "圆钉发消息", "status": JobStatus.INIT},
            ],
            "data": {
                "35be0b54-68f7-4916-b7c0-9bae14d30b4b": [
                    {
                        "uid": "1672904125691",
                        "url": "https://stg-locker.oss-cn-beijing.aliyuncs.com"
                               "/images/1/d9dde5870c67d3d3497b40b063c1fdaa.png",
                        "name": "1672904125692-image.png",
                        "fileId": "1672904125691",
                        "fileName": "1672904125692-image.png",
                    }
                ]
            },
        },
        client.shop,
        "tests.job.resources",
    )
    testbed.build()
    step = testbed.form.steps.filter_by(name="圆钉发消息").first()
    job = testbed.business_order.jobs.filter_by(step=step).first()

    yield NamedTuple("Testbed", job=Job)(job)


def test_yto_msg(testbed, requests_mock):
    requests_mock.post(
        "/namespaces/yto-kh/methods/send-message",
        json={
            "success": True,
            "result": {
                "total": 1,
                "status": "200",
                "rows": [{"code": "SO524302596227", "approve": False}],
            },
        },
    )
    status, msg = YtoMsgExecutor(testbed.job).process()
    assert status == JobStatus.SUCCEED, msg
