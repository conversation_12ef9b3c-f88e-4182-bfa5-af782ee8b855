import pytest

from tests.job.test_jst_trade import api_order
from robot_processor.enums import ErpType, JobStatus
from robot_processor.job.new_jst_after_sale_upload import NewJstAfterSaleUploadExecutor
from rpa.erp.jst import AfterSaleUploadResp

bo_data = {
    'sku_id': {
        'sku_list': [
            {
                'sku': '',
                'spu': '605587899302',
                '_key': '4821927832260',
                'count': 198,
                'price': 5,
                'props': '黑色;均码',
                'sku_id': '',
                'source': 'erp',
                'pic_url': 'https://img.alic',
                'quantity': 1,
                'sku_name': '短袖1',
                'outer_sku': '4821927832260',
                'spu_title': '短袖1',
                'cost_price': 0,
                'item_title': '短袖1',
                'supplier_id': '',
                'outer_sku_id': '4821927832260',
                'supplier_name': '',
                'outer_sku_id_real': ''
            }
        ],
        'after_sales_type': 'non_original'
    },
    'remark': '和姑父和',
    'tid': [{'tid': '1748074083623375569'}],
    'question_type': '其他',
    'process_type': 'api',
    'as_id': '恢复光滑',
    'logistics_type': -1
}

mock_order = {
    "buyer_paid_amount": "0.0",
    "co_id": 11016733,
    "created": "2023-06-26 13:34:26",
    "end_time": "2023-06-27 13:34:22",
    "f_freight": "0",
    "f_weight": "0",
    "free_amount": "0.0",
    "freight": "0.0",
    "is_cod": False,
    "items": [
        {
            "amount": 5.0,
            "base_price": 5.0,
            "i_id": "短袖spu商家编码",
            "is_gift": "false",
            "is_presale": True,
            "item_status": "Cancelled",
            "name": "短袖1",
            "oi_id": "50884",
            "outer_oi_id": "3411605270647683811",
            "pic": "http://img.alicdn.com/bao/uploaded/i1/1762594"
                   "837/O1CN018YRCsk1lbOIcfQc0L_!!1762594837.png_30x30.jpg",
            "price": 5.0,
            "properties_value": "黑色;均码",
            "qty": 1,
            "raw_so_id": "3411605270647683811",
            "refund_status": "none",
            "shop_i_id": "605587899302",
            "shop_sku_id": "4821927832260",
            "sku_id": "短袖sku商家编码",
            "sku_type": "normal"
        }
    ],
    "labels": "线上预售",
    "modified": "2023-07-17 18:05:09",
    "o_id": 47544,
    "oaid": "16rR0ib1fi5adOH3MKJKqNrUHR542MNg1YMKjglchLnM75QO8VUqvnHSibUVkynMt8FOOxnfIL",
    "open_id": "AAEi8-dTABsHV6KE1LwodsBO",
    "order_date": "2023-06-26 13:34:17",
    "order_from": "WAP,WAP",
    "paid_amount": "0.0",
    "pay_amount": "0.0",
    "pays": [

    ],
    "plan_delivery_date": "2023-07-11 13:34:17",
    "question_desc": "",
    "question_type": "",
    "receiver_city": "泉州市",
    "receiver_district": "晋江市",
    "receiver_state": "福建省",
    "receiver_zip": "000000",
    "referrer_id": "",
    "referrer_name": "",
    "remark": "//DK  DH62652552442662  退货退款  23.9  13454324524 李四 a750392237 07-05 11:28",
    "shop_buyer_id": "尽**",
    "shop_id": 11615092,
    "shop_name": "a750392237",
    "shop_site": "淘宝天猫",
    "shop_status": "TRADE_CLOSED_BY_TAOBAO",
    "skus": "0.",
    "so_id": "3411605270647683811",
    "status": "WaitConfirm",
    "ts": 10440806583,
    "type": "普通订单",
    "weight": "0",
    "wms_co_id": 0
}


@pytest.fixture
def mock_jst_sdk(mocker):
    mocker.patch("rpa.erp.jst.JstSDK.__init__", return_value=None)
    yield mocker.patch("rpa.erp.jst.JstSDK.aftersale_upload")


@pytest.fixture
def mock_get_origin_order(mocker):
    p = mocker.patch("robot_processor.job.new_jst_after_sale_upload.NewJstAfterSaleUploadExecutor.get_origin_order")
    p.return_value = api_order
    yield p


def test_upload_with_original(
        client, mocker, mock_erp_type, mock_job_args,
        mock_full_job, mock_jst_sdk, mock_job_states, mock_get_origin_order
):
    """
    测试原单补发流程
    """
    mock_erp_type(ErpType.JST)
    bo_data['sku_id']['after_sales_type'] = 'original'
    mock_job_args(**bo_data)
    mock_data = {"as_id": 123543, "o_id": 4123, "so_id": "ww"}
    mock_jst_sdk.return_value = AfterSaleUploadResp(datas=[AfterSaleUploadResp.Data(**mock_data)])
    job_status, _ = NewJstAfterSaleUploadExecutor(mock_full_job).process()
    assert job_status == JobStatus.SUCCEED
    assert mock_job_states.write_job_output.call_count == 1
    assert mock_job_states.write_job_widget_collection.call_count == 1
