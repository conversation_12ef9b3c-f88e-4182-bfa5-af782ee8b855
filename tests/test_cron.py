import random
import time

import arrow
from flask import current_app
from leyan_proto.digismart.logistics.dgt_logistics_pb2 import DgtGetLogisticsResponse
from pytest import fixture
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import Job
from robot_processor.business_order.models import JobExecuteCron
from robot_processor.business_order.models import JobPool
from robot_processor.business_order.notice.models import Notice
from robot_processor.business_order.notice.models import NotifyOrgException
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import JobStatus
from robot_processor.enums import JobTaskRunStatus
from robot_processor.enums import NoticeCategory
from robot_processor.ext import db
from robot_processor.form.form_subscribe import FormSubscribe


@fixture
def client(gen_fresh_client):
    yield gen_fresh_client()


def test_check_job_timeout(client, mock_business_order, mock_step, mock_form, job_factory):
    from robot_processor.t_cron import check_job_timeout

    now = int(time.time())

    FormSubscribe(mock_form).subscribe(client.shop, True)
    mock_step.form_id = mock_form.id
    mock_step.notifier = {"enabled": True}
    mock_job = job_factory.create2(
        mock_step, mock_business_order, status=JobStatus.PENDING, deadline_info={"job_ts": now + 100}
    )
    db.session.commit()

    check_job_timeout()

    job = Job.query.get(mock_job.id)
    assert job.business_order.is_timeout == 1
    assert job.business_order.message_ts == now + 100

    mock_job.deadline_info = {"job_ts": now - 100}
    db.session.commit()
    check_job_timeout()
    job = Job.query.get(mock_job.id)
    assert job.business_order.is_timeout == 2


def test_job_cron_execute(client, mock_job, mock_business_order):
    from robot_processor.t_cron import job_cron_execute

    now = int(time.time())
    mock_job.business_order_id = mock_business_order.id
    mock_job_cron = JobExecuteCron(
        business_order_id=mock_job.business_order_id,
        job_id=mock_job.id,
        expect_execute_time=now - 20,
        status=JobStatus.INIT,
    )
    db.session.add(mock_job_cron)
    job_cron_execute()
    assert JobStatus.SUCCEED == mock_job_cron.status


def test_confirm_logistics_cancel_status(
    mocker, client, mock_business_order, mock_grant_record, mock_job_task, job_factory, step_factory, mock_form, rpa
):
    from robot_processor.t_cron import confirm_logistics_cancel_status

    FormSubscribe(mock_form).subscribe(client.shop, True)
    step1, step2 = step_factory.create_batch(2, form_id=mock_form.id)
    mock_task = rpa(task="CONFIRM_LOGISTICS_CANCEL", org_id=client.org_id)
    step2.data = {"rpa_id": mock_task.id}
    step2.key_map = {
        "tid": "tid",
        "logistics_no": "logistics_no_uuid",
        "logistics_intercept_rules": [
            {
                "condition_option": "contains_all",
                "condition_rules": [["已离开", "四川成都"], ["全面消杀"]],
                "condition_result": "截单成功",
            }
        ],
    }

    mock_job1 = job_factory.create2(step1, mock_business_order)
    mock_job2 = job_factory.create2(step2, mock_business_order, status=JobStatus.RUNNING)
    mock_business_order.job_history = [mock_job1.id, mock_job2.id]
    mock_business_order.sid = client.shop.sid
    mock_business_order.data = {
        # "tid": "***********",
        "logistics_no_uuid": "1234"
    }

    mock_job_task.run_times = 0
    mock_job_task.run_status = JobTaskRunStatus.RUNNING.value
    mock_job_task.job_id = mock_job2.id

    mock_resp = DgtGetLogisticsResponse()
    mocker.patch("robot_processor.client.logistics_client.client.GetLogistics", return_value=mock_resp)
    mocker.patch("robot_processor.business_order.tasks.package_jobs.send")

    confirm_logistics_cancel_status()
    assert mock_job_task.run_status == JobTaskRunStatus.FAILED.value
    assert mock_job_task.data.get("error_msg") == "没有订单号或物流单号"

    mock_business_order.data["tid"] = "***********"
    flag_modified(mock_business_order, "data")
    mock_job_task.deleted = False
    mock_job_task.run_status = JobTaskRunStatus.RUNNING.value
    db.session.commit()
    confirm_logistics_cancel_status()
    assert mock_job_task.run_status == JobTaskRunStatus.FAILED.value
    assert mock_job_task.data.get("error_msg") == "没有淘宝授权"

    client.shop.records.append(mock_grant_record)
    mock_job_task.deleted = False
    mock_job_task.run_status = JobTaskRunStatus.RUNNING.value
    confirm_logistics_cancel_status()
    assert mock_job_task.run_status == JobTaskRunStatus.RUNNING.value

    mock_job_task.deleted = False
    mock_job_task.run_status = JobTaskRunStatus.RUNNING.value
    mock_shipping = mock_resp.shippings.add()
    mock_shipping.tid = "***********"
    mock_shipping.out_sid = "1234"
    mock_trace = mock_shipping.logisticsTrace.trace_list.add()
    mock_trace.status_desc = (
        "【成都市】已离开 四川成都分拨交付中心(分拨对该快件已全面消杀)；发往 四川成都武侯区武侯祠公司"
    )
    mock_trace.status_time = "2022-06-08 06:18:30"
    mock_trace2 = mock_shipping.logisticsTrace.trace_list.add()
    mock_trace2.status_desc = "【成都市】四川成都武侯区武侯祠公司[028-63203398] 快递员 徐彪（13008146542）正在为您派送"
    mock_trace2.status_time = "2022-06-08 16:53:59"
    confirm_logistics_cancel_status()
    assert mock_job_task.run_status == JobTaskRunStatus.SUCCEED.value

    mock_job_task.deleted = False
    mock_job_task.run_status = JobTaskRunStatus.RUNNING.value
    mock_job_task.run_times = 14
    step2.key_map["logistics_intercept_rules"] = [
        {
            "condition_option": "contains_all",
            "condition_rules": [["上海市", "长宁区"], ["签收"]],
            "condition_result": "截单成功",
        }
    ]
    flag_modified(step2, "key_map")
    step2.update_raw_step()
    raw_step = step2.raw_step
    raw_step["ui_schema"] = [
        {"key": "tid_uuid", "option_value": {"mode": "tid", "label": "订单"}},
        {"key": "logistics_no_uuid", "option_value": {"mode": "string", "label": "物流单号"}},
    ]
    step2.raw_step = raw_step
    db.session.commit()
    confirm_logistics_cancel_status()
    assert mock_job_task.data.get("error_msg") == "超过查询次数"


def test_business_order_archive_over_time(client, business_order_factory, shop_factory):
    from robot_processor.business_order.archive_task import business_order_archive
    from robot_processor.t_cron import cron_business_order_archive

    bo_count = 10
    bos = business_order_factory.create_batch(bo_count, sid=client.shop.sid, archived=False)
    invalid_shop = shop_factory.create()
    invalid_sid = invalid_shop.sid
    need_archive = []
    for bo in bos:
        shift = random.randint(-5, 0)
        bo.status = random.choice(list(BusinessOrderStatus.__members__.values()))
        if shift <= -3 and bo.is_end():
            need_archive.append(bo.id)
        bo.updated_at = arrow.now().shift(months=shift, seconds=-10).timestamp()
    db.session.commit()
    current_app.config["ARCHIVE_ALLOW_TIME"] = "00:00-23:59"
    cron_business_order_archive()
    business_order_archive([client.shop.sid, invalid_sid])
    assert BusinessOrder.query.filter(BusinessOrder.id.in_(need_archive)).count() == 0


def test_jobs_cleanup(client, job_factory):
    from robot_processor.business_order.archive_task import clean_invalid_jobs

    job_factory.create_batch(10, business_order_id=None)
    assert Job.query.count() == 10
    clean_invalid_jobs()
    assert Job.query.count() == 0


def test_job_pools_cleanup(client, job_pool_factory):
    from robot_processor.business_order.archive_task import clean_invalid_job_pools

    for i in range(10):
        job_pool_factory.create(business_order_id=-i, job_id=-i, assignee_user_id=-i)
    assert JobPool.query.count() == 10
    clean_invalid_job_pools()
    assert JobPool.query.count() == 0


def test_cron_form_version_clear(client, mock_form, business_order_factory):
    from robot_processor.business_order.archive_task import form_version_archive
    from robot_processor.form.models import FormVersion
    from robot_processor.t_cron import form_version_clear

    FormSubscribe(mock_form).subscribe(client.shop, True)
    db.session.commit()
    versions = []
    for _ in range(10):
        version = FormVersion.new(mock_form)
        version.update_version_descriptor()
        db.session.add(version)
        db.session.commit()
        versions.append(version)
    need_archive = []
    versions[-1].version_descriptor = "HEAD"
    for idx, version in enumerate(versions):
        version.updated_at = arrow.now().shift(months=-idx, seconds=-10).timestamp()
        if idx <= -1 and random.choice([True, False]):
            need_archive.append(version.id)
            business_order_factory.create(form_id=mock_form.id, form_version_id=version.id)

    db.session.commit()
    current_app.config["ARCHIVE_ALLOW_TIME"] = "00:00-23:59"
    form_version_clear()
    form_version_archive([client.shop.id])
    assert FormVersion.query.filter(FormVersion.id.in_(need_archive), ~FormVersion.deleted).count() == 0


def test_token_refresh(client, mocker, shop_factory, grant_record_factory):
    import arrow
    from result import Ok

    from robot_processor.shop.models import App
    from robot_processor.shop.models import GrantRecord
    from robot_processor.t_cron import refresh_doudian_token

    shop = shop_factory.create(platform="DOUDIAN")
    gr = grant_record_factory.create(
        shop_id=shop.id,
        app=random.choice([App.PIGEON.value, App.DOUDIAN_XYZ.value]),
        expires_at_ms=arrow.now().int_timestamp * 1000,
        access_token="access_token",
        refresh_token="refresh_token",
    )
    mocker.patch(
        "rpa.doudian.client.DoudianOpenAPIClient._openapi_execute",
        return_value=Ok(
            {
                "data": {
                    "access_token": "82bdc687-eff1-4f63-8444-0b43086c25fd",
                    "auth_subject_type": "WuLiuShang",
                    "authority_id": "1321324",
                    "expires_in": 1000,
                    "refresh_token": "ed14a703-1f27-4a0b-9b94-759242744ec8",
                    "scope": "SCOPE",
                    "shop_id": "23323",
                    "shop_name": "测试店铺",
                },
                "code": 10000,
                "msg": "success",
                "sub_code": "",
                "sub_msg": "",
            }
        ),
    )
    refresh_doudian_token()
    gr = GrantRecord.query.get(gr.id)
    assert gr.access_token == "82bdc687-eff1-4f63-8444-0b43086c25fd"
    assert (1000 + arrow.utcnow().int_timestamp) * 1000 - gr.expires_at_ms < 10
    assert gr.refresh_token == "ed14a703-1f27-4a0b-9b94-759242744ec8"


def test_send_out_app_notify_dingding(mock_full_job, mocker):
    # mock DingdingMessageSender
    ding = mocker.patch("robot_processor.client.dingding.DingdingMessageSender.send")
    ding.return_value = True, ""

    notice = Notice(
        title="test",
        content="test",
        category=NoticeCategory.EXCEPTION_TASK,
        exception_notify_config={
            "name": "gfd",
            "title": [{"text": "title", "type": "text"}],
            "content": [{"text": "content", "type": "text"}],
            "enabled": False,
            "order_status": 8,
            "qiwei_secret": None,
            "receive_type": 2,
            "feishu_secret": None,
            "qiwei_webhook": None,
            "feishu_webhook": None,
            "dingding_secret": "SEC9711f0ccdccfe2aa594b3e56c52fe9fc08fcc5ac563e4180a666491fceec10f3",
            "notify_channels": ["dingding"],
            "dingding_webhook": "https://oapi.dingtalk.com/robot/send?access_token=02390be591ca"
            "cde8caff61fc336611d16728f9c7c67dfb03ed6f0aef510c94c4",
            "receivers_in_app": {},
            "receiver_short_cut": None,
        },
    )
    db.session.add(notice)
    db.session.commit()
    org_ex = NotifyOrgException(
        org_id=mock_full_job.shop.org_id, notice_id=notice.id, is_informed=False, job_id=mock_full_job.id
    )
    db.session.add(org_ex)
    db.session.commit()

    from robot_processor.t_cron import send_out_app_notify

    send_out_app_notify()
    ding.assert_called()


def test_send_out_app_notify_qiwei(mock_full_job, mocker):
    # 测试企业微信
    # mock WeComSender
    # wecom = mocker.patch("robot_processor.client.wecom.WeComSender.send")

    notice = Notice(
        title="test",
        content="test",
        category=NoticeCategory.EXCEPTION_TASK,
        exception_notify_config={
            "name": "gfd",
            "title": [{"text": "title", "type": "text"}],
            "content": [{"text": "content", "type": "text"}],
            "enabled": False,
            "order_status": 8,
            "qiwei_secret": None,
            "receive_type": 2,
            "feishu_secret": None,
            "qiwei_webhook": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2a3343"
            "05-a3a5-4928-97b4-724c1701a794",
            "feishu_webhook": None,
            "dingding_secret": None,
            "notify_channels": ["qiwei"],
            "dingding_webhook": None,
            "receivers_in_app": {},
            "receiver_short_cut": None,
        },
    )
    db.session.add(notice)
    db.session.commit()
    org_ex = NotifyOrgException(
        org_id=mock_full_job.shop.org_id, notice_id=notice.id, is_informed=False, job_id=mock_full_job.id
    )
    db.session.add(org_ex)
    db.session.commit()

    from robot_processor.t_cron import send_out_app_notify

    send_out_app_notify()
