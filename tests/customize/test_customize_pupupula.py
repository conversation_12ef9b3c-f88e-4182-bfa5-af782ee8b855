from unittest.mock import call

from pytest import fixture

from robot_processor.customize.customize_pupupula import process
from robot_processor.db import db
from robot_processor.form.models import Form
from robot_processor.shop.models import Shop
from robot_processor.types.common import BuyerInfo


def get_record():
    import json

    return {
        "tid": "3741888925696189713",
        "status": "SIGNED",
        "seller_nick": "北京小魔怪科技有限公司",
        "jdp_response": json.dumps(
            {
                "trade_fullinfo_get_response": {
                    "trade": {
                        "buyer_open_uid": "AAFDp3E6ANnvchERvSEfhXJv",
                        "tid": 3741888925696190000,
                        "tid_str": "3741888925696189713",
                        "status": "TRADE_FINISHED",
                        "type": "fixed",
                        "seller_nick": "北京小魔怪科技有限公司",
                        "buyer_nick": "t**",
                        "created": "2024-01-19 09:09:32",
                        "oaid": "1fzwaibONgjfAJgZl7PZcSibiaaVkAfibSBvKKTaH7Kef5EX777bZqOK9TrVibP2D1vJUJWNERic8",
                        "modified": "2024-01-21 10:42:13",
                        "sign_time": "2024-01-22 14:00:00",
                        "alipay_no": "2024011922001189311415058236",
                        "buyer_alipay_no": "156********",
                        "buyer_email": "",
                        "coupon_fee": 0,
                        "end_time": "2024-01-21 10:42:12",
                        "is_sh_ship": False,
                        "orders": {
                            "order": [
                                {
                                    "buyer_rate": False,
                                    "consign_time": "2024-01-19 16:31:17",
                                    "divide_order_fee": "142.62",
                                    "end_time": "2024-01-21 10:42:12",
                                    "invoice_no": "YT7441762714020",
                                    "logistics_company": "圆通速递",
                                    "num": 1,
                                    "num_iid": 614606870236,
                                    "oid": 3741888925697190000,
                                    "outer_sku_id": "txzj4pj",
                                    "payment": "142.62",
                                    "pic_path": "https://img.alicdn.com"
                                    "/bao/uploaded/i1/3642336162"
                                    "/O1CN01Ce9jhJ1vOFTdOT6yE_!!3642336162.jpg",
                                    "price": "166.00",
                                    "refund_status": "NO_REFUND",
                                    "sku_id": "5254700024538",
                                    "sku_properties_name": "颜色分类:配件包|收纳袋+笔筒+画轴+画卷纸",
                                    "status": "TRADE_FINISHED",
                                    "title": "PUPUPULA踏雪桌椅套装宝宝玩具游戏餐桌儿童实木写字花生桌可调节",
                                },
                                {
                                    "buyer_rate": False,
                                    "consign_time": "2024-01-19 15:06:55",
                                    "divide_order_fee": "1870.38",
                                    "end_time": "2024-01-21 10:42:12",
                                    "invoice_no": "DPK301445286291",
                                    "logistics_company": "德邦快递",
                                    "num": 1,
                                    "num_iid": 614606870236,
                                    "oid": 3741888925698190000,
                                    "outer_sku_id": "wytxz+2y",
                                    "payment": "1870.38",
                                    "pic_path": "https://img.alicdn.com"
                                    "/bao/uploaded/i1/3642336162"
                                    "/O1CN01SZLj8V1vOFTeUEzpn_!!3642336162.jpg",
                                    "price": "2177.00",
                                    "refund_status": "NO_REFUND",
                                    "sku_id": "5254700024537",
                                    "sku_properties_name": "颜色分类:踏雪桌*1+踏雪椅*2[|赠桌垫坐垫]",
                                    "status": "TRADE_FINISHED",
                                    "title": "PUPUPULA踏雪桌椅套装宝宝玩具游戏餐桌儿童实木写字花生桌可调节",
                                },
                            ]
                        },
                        "pay_time": "2024-01-19 09:10:04",
                        "payment": "2013.00",
                        "post_fee": "0.00",
                        "received_payment": "2013.00",
                        "receiver_address": "虹*街道荣**道***号维多利**幢**楼****室",
                        "receiver_city": "上海市",
                        "receiver_country": "",
                        "receiver_district": "长宁区",
                        "receiver_mobile": "*******1635",
                        "receiver_name": "向**",
                        "receiver_state": "上海",
                        "receiver_town": "虹桥街道",
                        "receiver_zip": "000000",
                        "seller_flag": 0,
                        "seller_memo": "奇门回传单号:DPK301445503476,"
                        "DPK301445508727,DPK301445516402,DPK301445524280",
                        "total_fee": "2343.00",
                        "tmall_coupon_fee": 0,
                    }
                }
            }
        ),
    }


@fixture
def setup(mocker):
    mocker.patch(
        "robot_processor.business_order.business_order_manager.BusinessManager.ui_schema_for_create",
        new_callable=mocker.PropertyMock(
            return_value=[
                dict(
                    key="93ee3438-5d1b-4220-a675-e5b306c4924a",
                    type="order",
                    option_value=dict(
                        label="订单/子订单",
                        data_binding=dict(
                            level="$",
                            expression="[jdp_response.trade_fullinfo_get_response."
                            "trade.{tid: tid_str, oid: orders.order[0].oid}]",
                        ),
                    ),
                ),
                dict(
                    key="d2215559-2a14-47db-b77a-ad5182bfa9ab",
                    type="string",
                    option_value=dict(
                        label="买家昵称",
                        data_binding=dict(
                            level="$",
                            expression="jdp_response.trade_fullinfo_get_response.trade.buyer_nick",
                        ),
                    ),
                ),
                dict(
                    key="8a77e755-ca42-41ea-b0d4-e830502386f7",
                    type="string",
                    option_value=dict(
                        label="快递公司",
                        data_binding=dict(
                            level="$",
                            expression="jdp_response.trade_fullinfo_get_response."
                            "trade.orders.order[].logistics_company | [0]",
                        ),
                    ),
                ),
                dict(
                    key="d04c6edb-93f0-43c0-a739-d3344b7485a8",
                    type="",
                    option_value=dict(
                        label="快递单号",
                        data_binding=dict(
                            level="$",
                            expression="jdp_response.trade_fullinfo_get_response."
                            "trade.orders.order[].invoice_no | [0]",
                        ),
                    ),
                ),
                dict(
                    key="a706227e-600d-4307-b3c2-81c30edc89e5",
                    type="",
                    option_value=dict(
                        label="实付金额",
                        data_binding=dict(
                            level="$",
                            expression="jdp_response.trade_fullinfo_get_response.trade.payment",
                        ),
                    ),
                ),
            ]
        ),
    )
    shop = Shop()
    shop.nick = "北京小魔怪科技有限公司"
    shop.sid = "15634126"
    shop.platform = "TMALL"
    form = Form()
    form.id = 1217129
    form.name = "unittest"
    db.session.add_all([shop, form])
    db.session.commit()
    form.snapshot()
    yield mocker.patch(
        "robot_processor.business_order.business_order_manager.BusinessManager.pipeline_create_order"
    )


def test(setup):
    process(get_record())
    patcher = setup
    assert patcher.call_args_list == [
        call(
            {
                "93ee3438-5d1b-4220-a675-e5b306c4924a": [
                    {"tid": "3741888925696189713", "oid": 3741888925697190000}
                ],
                "d2215559-2a14-47db-b77a-ad5182bfa9ab": "t**",
                "8a77e755-ca42-41ea-b0d4-e830502386f7": "圆通速递",
                "d04c6edb-93f0-43c0-a739-d3344b7485a8": "YT7441762714020",
                "a706227e-600d-4307-b3c2-81c30edc89e5": "2013.00",
            },
            BuyerInfo(open_uid="AAFDp3E6ANnvchERvSEfhXJv", nick="t**"),
        )
    ]
