import json
import re
import pytest
from unittest.mock import PropertyMock

from robot_processor.app import app
from robot_processor.customize.create_bo_by_trade_event import \
    MemoInfoExtractor, TradesFilter, get_seller_memo_from_trade_event, \
    MemoAutoBoCreator, match_seller_memo_re_patterns_use_account
from robot_processor.kafka_event.trade_event import TradeEventRecord


@pytest.fixture
def trade_event_record(client, mock_form):
    sample_json = r"""{"data": [{"tid": "3424658043462247244", "status": "WAIT_BUYER_CONFIRM_GOODS", "type": "fixed", "seller_nick": "\u76db\u5343\u751f\u6d3b\u670d\u52a1\u4e13\u8425\u5e97", "buyer_nick": "t**", "created": "2023-07-06 14:35:17", "modified": "2023-07-06 14:52:44", "jdp_hashcode": "-**********", "jdp_response": "{\"trade_fullinfo_get_response\":{\"trade\":{\"buyer_open_uid\":\"AAGxp3E6ANnvchMRvSJFTN2C\",\"tid\":3424658043462247244,\"tid_str\":\"3424658043462247244\",\"status\":\"WAIT_BUYER_CONFIRM_GOODS\",\"type\":\"fixed\",\"seller_nick\":\"\u76db\u5343\u751f\u6d3b\u670d\u52a1\u4e13\u8425\u5e97\",\"buyer_nick\":\"t**\",\"created\":\"2023-07-06 14:35:17\",\"oaid\":\"1Ig8pgcExCc65Yu6v9FND1z2D0T1OQDb2UDFGttFFZgiasJ6zUJWcTnfxJ9jW15AiaFMpPvEn\",\"modified\":\"2023-07-06 14:52:44\",\"trade_attr\":\"{\\\"erpHold\\\":\\\"0\\\"}\",\"alipay_no\":\"2023070622001199801435519769\",\"buyer_alipay_no\":\"191********\",\"buyer_email\":\"\",\"coupon_fee\":0,\"is_sh_ship\":false,\"orders\":{\"order\":[{\"buyer_rate\":false,\"consign_time\":\"2023-07-06 14:52:39\",\"divide_order_fee\":\"10.00\",\"num\":1,\"num_iid\":************,\"oid\":3424658043462247244,\"payment\":\"10.00\",\"pic_path\":\"https://img.alicdn.com/bao/uploaded/i3/1748919670/O1CN01622UZH2LIukHZ75mL_!!0-item_pic.jpg\",\"price\":\"10.00\",\"refund_status\":\"NO_REFUND\",\"status\":\"WAIT_BUYER_CONFIRM_GOODS\",\"title\":\"\u77ed\u89c6\u9891\u5236\u4f5c\u526a\u8f91\u4e3b\u56fe\u62cd\u6444\u4f01\u4e1a\u5ba3\u4f20\u7247\u5e74\u4f1a\u7247\u5934\u5b57\u5e55\u5b9a\u5236mg\u52a8\u753bae\u4ee3\u505a\"}]},\"pay_time\":\"2023-07-06 14:35:19\",\"payment\":\"10.00\",\"post_fee\":\"0.00\",\"price\":\"10.00\",\"received_payment\":\"0.00\",\"receiver_address\":\"\u848b*\u9547\u848b\u5df7**\u9053\u5357\u660c\u5f71\u89c6\u4f20\u64ad\u804c**\u9662**\u697c\",\"receiver_city\":\"\u5357\u660c\u5e02\",\"receiver_country\":\"\",\"receiver_district\":\"\u5357\u660c\u53bf\",\"receiver_mobile\":\"*******2344\",\"receiver_name\":\"\u95eb**\",\"receiver_state\":\"\u6c5f\u897f\u7701\",\"receiver_town\":\"\u848b\u5df7\u9547\",\"receiver_zip\":\"000000\",\"seller_flag\":0,\"total_fee\":\"10.00\",\"tmall_coupon_fee\":0}}}", "jdp_created": "2023-07-06 14:35:18", "jdp_modified": "2023-07-06 14:52:45"}], "database": "sys_info", "es": 1688626365000, "id": 183334964, "isDdl": false, "mysqlType": {"tid": "bigint(20)", "status": "varchar(64)", "type": "varchar(64)", "seller_nick": "varchar(64)", "buyer_nick": "varchar(255)", "created": "datetime", "modified": "datetime", "jdp_hashcode": "varchar(128)", "jdp_response": "mediumtext", "jdp_created": "datetime", "jdp_modified": "datetime"}, "old": [{"modified": "2023-07-06 14:52:39", "jdp_hashcode": "168443130", "jdp_response": "{\"trade_fullinfo_get_response\":{\"trade\":{\"buyer_open_uid\":\"AAGxp3E6ANnvchMRvSJFTN2C\",\"tid\":3424658043462247244,\"tid_str\":\"3424658043462247244\",\"status\":\"WAIT_BUYER_CONFIRM_GOODS\",\"type\":\"fixed\",\"seller_nick\":\"\u76db\u5343\u751f\u6d3b\u670d\u52a1\u4e13\u8425\u5e97\",\"buyer_nick\":\"t**\",\"created\":\"2023-07-06 14:35:17\",\"oaid\":\"1Ig8pgcExCc65Yu6v9FND1z2D0T1OQDb2UDFGttFFZgiasJ6zUJWcTnfxJ9jW15AiaFMpPvEn\",\"modified\":\"2023-07-06 14:52:39\",\"trade_attr\":\"{\\\"erpHold\\\":\\\"0\\\"}\",\"alipay_no\":\"2023070622001199801435519769\",\"buyer_alipay_no\":\"191********\",\"buyer_email\":\"\",\"coupon_fee\":0,\"is_sh_ship\":false,\"orders\":{\"order\":[{\"buyer_rate\":false,\"consign_time\":\"2023-07-06 14:52:39\",\"divide_order_fee\":\"10.00\",\"num\":1,\"num_iid\":************,\"oid\":3424658043462247244,\"payment\":\"10.00\",\"pic_path\":\"https://img.alicdn.com/bao/uploaded/i3/1748919670/O1CN01622UZH2LIukHZ75mL_!!0-item_pic.jpg\",\"price\":\"10.00\",\"refund_status\":\"NO_REFUND\",\"status\":\"WAIT_BUYER_CONFIRM_GOODS\",\"title\":\"\u77ed\u89c6\u9891\u5236\u4f5c\u526a\u8f91\u4e3b\u56fe\u62cd\u6444\u4f01\u4e1a\u5ba3\u4f20\u7247\u5e74\u4f1a\u7247\u5934\u5b57\u5e55\u5b9a\u5236mg\u52a8\u753bae\u4ee3\u505a\"}]},\"pay_time\":\"2023-07-06 14:35:19\",\"payment\":\"10.00\",\"post_fee\":\"0.00\",\"price\":\"10.00\",\"received_payment\":\"0.00\",\"receiver_address\":\"\u848b*\u9547\u848b\u5df7**\u9053\u5357\u660c\u5f71\u89c6\u4f20\u64ad\u804c**\u9662**\u697c\",\"receiver_city\":\"\u5357\u660c\u5e02\",\"receiver_country\":\"\",\"receiver_district\":\"\u5357\u660c\u53bf\",\"receiver_mobile\":\"*******2344\",\"receiver_name\":\"\u95eb**\",\"receiver_state\":\"\u6c5f\u897f\u7701\",\"receiver_town\":\"\u848b\u5df7\u9547\",\"receiver_zip\":\"000000\",\"seller_flag\":0,\"total_fee\":\"10.00\",\"tmall_coupon_fee\":0}}}", "jdp_modified": "2023-07-06 14:52:40"}], "pkNames": ["tid"], "sql": "", "sqlType": {"tid": -5, "status": 12, "type": 12, "seller_nick": 12, "buyer_nick": 12, "created": 93, "modified": 93, "jdp_hashcode": 12, "jdp_response": 2005, "jdp_created": 93, "jdp_modified": 93}, "table": "jdp_tb_trade", "ts": 1688626365572, "type": "UPDATE"}""" # noqa
    sample = json.loads(sample_json)
    sample['data'][0]['seller_nick'] = client.shop.nick

    trade_event_record = TradeEventRecord(
        tid=sample["data"][0]["tid"],
        status=sample["data"][0]["status"],
        seller_nick=sample["data"][0]["seller_nick"],
        created=sample["data"][0]["created"],
        modified=sample["data"][0]["modified"],
        jdp_response=('{"trade_fullinfo_get_response":{"trade":{"seller_memo":"resfgsd //DK '
                      'nick DH1234567890 付款 100.00 *********** 买家真名 qita"}}}')
    )
    app.config['MEMO_AUTO_BO_CREATOR_SELLER_NICKS'] = {client.shop.nick: mock_form.id}
    app.config["AUTO_REFUND_BY_MEMO_SELLER_NICKS"] = {client.shop.nick: mock_form.id}
    return trade_event_record


def test_is_need_process_fail(trade_event_record):
    # 一个不符合正则的备注
    trade_event_record[
        "jdp_response"] = '{"trade_fullinfo_get_response":{"trade":{"seller_memo":"//fsdfdsfsdfdsf07-06 12:49:05"}}}'  # noqa
    assert re.match(match_seller_memo_re_patterns_use_account()[0][0], get_seller_memo_from_trade_event(trade_event_record)) is None  # noqa
    trades_filter = TradesFilter(trade_event_record)
    assert trades_filter.is_need_process(
        seller_nick_to_form_mapping=trades_filter.memo_auto_bo_creator_seller_nicks,
        patterns=trades_filter.match_seller_memo_patterns,
        use_ts_id=True
    ) is False


def test_is_need_process_success(trade_event_record):
    trade_event_record[
        "jdp_response"] = '{"trade_fullinfo_get_response":{"trade":{"seller_memo":"//DK buyer_nick DH1234567890 付款 100.00 *********** 买家昵称 卖家昵称 2021-07-06 12:49:05"}}}'  # noqa
    assert re.match(match_seller_memo_re_patterns_use_account()[0][0], get_seller_memo_from_trade_event(trade_event_record))  # noqa
    trades_filter = TradesFilter(trade_event_record)
    assert trades_filter.is_need_process(
        seller_nick_to_form_mapping=trades_filter.memo_auto_bo_creator_seller_nicks,
        patterns=trades_filter.match_seller_memo_patterns,
        use_ts_id=True
    ) is True


def test_is_need_process_success2(trade_event_record):
    # 备注可能在中间位置
    trade_event_record[
        "jdp_response"] = '{"trade_fullinfo_get_response":{"trade":{"seller_memo":"rewr23423 //DK buyer_nick  DH1234567890 付款 100.00 *********** 买家昵称 卖家昵称 2021-07-06 12:49:********"}}}'  # noqa
    assert re.match(match_seller_memo_re_patterns_use_account()[0][0], get_seller_memo_from_trade_event(trade_event_record))  # noqa
    trades_filter = TradesFilter(trade_event_record)
    assert trades_filter.is_need_process(
        seller_nick_to_form_mapping=trades_filter.memo_auto_bo_creator_seller_nicks,
        patterns=trades_filter.match_seller_memo_patterns,
        use_ts_id=True
    ) is True


def test_is_need_process_success3(trade_event_record):
    # 备注可能用逗号分割
    trade_event_record[
        "jdp_response"] = '{"trade_fullinfo_get_response":{"trade":{"seller_memo":"rewr23423 //DK， buyer_nick ,DH1234567890， 付款， 100.00， *********** 买家昵称 卖家昵称 2021-07-06 12:49:********"}}}'  # noqa
    assert re.match(match_seller_memo_re_patterns_use_account()[0][0], get_seller_memo_from_trade_event(trade_event_record))  # noqa
    trades_filter = TradesFilter(trade_event_record)
    assert trades_filter.is_need_process(
        seller_nick_to_form_mapping=trades_filter.memo_auto_bo_creator_seller_nicks,
        patterns=trades_filter.match_seller_memo_patterns,
        use_ts_id=True
    ) is True


def test_extract(trade_event_record):
    trade_event_record[
        "jdp_response"] = '{"trade_fullinfo_get_response":{"trade":{"seller_memo":"rewr23423 //DK，buyer_nick, DH1234567890， 付款， 100.00， *********** 买家昵称 卖家昵称 2021-07-06 12:49:********"}}}'  # noqa
    args = MemoInfoExtractor.extract(trade_event_record)
    assert args.ts_id == "DH1234567890"


@pytest.fixture
def mock_get_form_first_step_widgets_info(mocker):
    p = mocker.patch("robot_processor.customize.create_bo_by_trade_event.MemoAutoBoCreator.get_form_first_step_widgets_info")  # noqa
    p.return_value = {"widget_uuid1": "value"}


@pytest.fixture
def mock_business_order_manager(mocker, mock_business_order):
    p = mocker.patch("robot_processor.customize.create_bo_by_trade_event.BusinessManager.fill_business_order")  # noqa
    p.return_value = (None, mock_business_order)
    p2 = mocker.patch("robot_processor.customize.create_bo_by_trade_event.BusinessManager.init_new_business_order")  # noqa
    yield p, p2


def test_do_create_bo(client, trade_event_record,
                      mock_get_form_first_step_widgets_info,
                      mock_business_order_manager):
    args = MemoInfoExtractor.extract(trade_event_record)
    MemoAutoBoCreator.do_create_bo(args, client.shop, "test_tid")


def test_refund_match(trade_event_record):
    # 一个不符合正则的备注
    trade_event_record[
        "jdp_response"] = '{"trade_fullinfo_get_response":{"trade":{"seller_memo":"//fsdfdsfsdfdsf07-06 12:49:05"}}}'  # noqa
    trades_filter = TradesFilter(trade_event_record)
    assert trades_filter.is_need_process(
        seller_nick_to_form_mapping=trades_filter.auto_refund_by_memo_seller_nicks,
        patterns=trades_filter.auto_refund_by_memo_patterns,
        use_ts_id=False
    ) is False

    # 符合条件的备注
    trade_event_record[
        "jdp_response"] = '{"trade_fullinfo_get_response":{"trade":{"seller_memo":"rewr23423 //DK buyer_nick  DH1234567890 我要开通退款申请通道。 付款 100.00 *********** 买家昵称 卖家昵称 2021-07-06 12:49:********"}}}'  # noqa
    trades_filter = TradesFilter(trade_event_record)
    assert trades_filter.is_need_process(
        seller_nick_to_form_mapping=trades_filter.auto_refund_by_memo_seller_nicks,
        patterns=trades_filter.auto_refund_by_memo_patterns,
        use_ts_id=False
    ) is True

    # 符合条件的备注
    trade_event_record[
        "jdp_response"] = """{"trade_fullinfo_get_response":{"trade":{"seller_memo":"rewr23423 //DK buyer_nick  DH1234567890 付款 100.00 *********** 买家昵称 卖家昵称 2021-07-06 12:49:******** 我要开通退款申请通道。"}}}"""  # noqa
    trades_filter = TradesFilter(trade_event_record)
    assert trades_filter.is_need_process(
        seller_nick_to_form_mapping=trades_filter.auto_refund_by_memo_seller_nicks,
        patterns=trades_filter.auto_refund_by_memo_patterns,
        use_ts_id=False
    ) is True


def test_not_match_seller_nick(trade_event_record):
    rec = trade_event_record
    rec["seller_nick"] = "xxxxxxx"
    rec[
        "jdp_response"] = """{"trade_fullinfo_get_response":{"trade":{"seller_memo":"rewr23423 //DK buyer_nick  DH1234567890 付款 100.00 *********** 买家昵称 卖家昵称 2021-07-06 12:49:******** 我要开通退款申请通道。"}}}"""  # noqa
    trades_filter = TradesFilter(rec)
    assert trades_filter.is_need_process(
        seller_nick_to_form_mapping=trades_filter.auto_refund_by_memo_seller_nicks,
        patterns=trades_filter.auto_refund_by_memo_patterns,
        use_ts_id=False
    ) is False


def test_not_found_shop(trade_event_record, mocker):
    rec = trade_event_record
    rec[
        "jdp_response"] = """{"trade_fullinfo_get_response":{"trade":{"seller_memo":"rewr23423 //DK buyer_nick  DH1234567890 付款 100.00 *********** 买家昵称 卖家昵称 2021-07-06 12:49:******** 我要开通退款申请通道。"}}}"""  # noqa
    mocker.patch(
        "robot_processor.customize.create_bo_by_trade_event.TradesFilter.shop",
        new_callable=PropertyMock(return_value=None)
    )
    trades_filter = TradesFilter(rec)
    assert trades_filter.is_need_process(
        seller_nick_to_form_mapping=trades_filter.auto_refund_by_memo_seller_nicks,
        patterns=trades_filter.auto_refund_by_memo_patterns,
        use_ts_id=False
    ) is False


def test_do_create_refund_bo(
        client, trade_event_record,
        mock_get_form_first_step_widgets_info,
        mock_business_order_manager
):
    MemoAutoBoCreator.do_create_refund_bo(client.shop, "test_tid")


def test_do_create(
        trade_event_record,
        mock_get_form_first_step_widgets_info,
        mock_business_order_manager,
):
    trade_event_record[
        "jdp_response"] = '{"trade_fullinfo_get_response":{"trade":{"seller_memo":"rewr23423 //DK buyer_nick  DH1234567890 我要开通退款申请通道。 付款 100.00 *********** 买家昵称 卖家昵称 2021-07-06 12:49:********"}}}'  # noqa
    memo_auto_bo_creator = MemoAutoBoCreator(record=trade_event_record, change_type="UPDATE")
    memo_auto_bo_creator.process_ts_auto_create()
    memo_auto_bo_creator.process_refund()
