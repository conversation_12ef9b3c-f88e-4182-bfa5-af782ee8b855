from robot_extension.widget import value_handler


def test_value_handler():
    # 非法
    assert 1 == value_handler(1)

    data = {"type": "int", "value": 1}
    # 基础
    assert value_handler(data) == 1
    # 单选下拉
    data = {"type": "select", "value": ["1", "2"]}

    assert value_handler(data) == "1>2"
    # 地址
    address = {
        "state": "上海市", "city": "上海市",
        "address": "长宁路1033号", "name": "bear",
        "town": "华阳路街道",
        "mobile": "123456",
    }
    data = {"type": "address", "value": address}

    msg = "上海市 上海市  华阳路街道 长宁路1033号 bear 123456"

    assert value_handler(data) == msg


def test_other_value_handler():
    # 订单问题细分
    data = {"type": "order-question", "value": [["1", "2"], ["ab", "cc"]]}
    assert value_handler(data) == "1>2;ab>cc"
