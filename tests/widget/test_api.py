from robot_processor.ext import db
from robot_processor.form.models import Address, AddressTown


def test_get_address(client):
    address_province_1 = Address(
        code="11", province="河北省", city="", zone=''
    )
    address_city = Address(
        code="111", province="河南省", city="郑州市", zone=''
    )
    address_zone = Address(
        code="21", province="江苏省", city="南京市", zone='玄武区'
    )

    address_province_2 = Address(
        code="22", province="湖北省", city="天门市", zone=''
    )
    address_town_1 = AddressTown(town='梅园新村街道', pcode='21000000', code='21001000')
    address_town_2 = AddressTown(town='梅园新村街道', pcode='22000000', code='22001000')

    address_direct = Address(
        code="333", province="北京市", city="北京市市辖区", zone='东城区'
    )

    db.session.add(address_province_1)
    db.session.add(address_city)
    db.session.add(address_zone)
    db.session.add(address_town_1)
    db.session.add(address_town_2)
    db.session.add(address_province_2)
    db.session.add(address_direct)
    db.session.commit()
    resp = client.get("/v1/widget-data/address?")

    assert resp.status_code == 200
    result = resp.json.get('result')
    assert 1 == len(result)
    assert "河北省" == result[0].get('province')
    assert not result[0].get('city')

    resp = client.get("/v1/widget-data/address?province=河南省")

    assert resp.status_code == 200
    result = resp.json.get('result')
    assert 1 == len(result)
    assert "郑州市" == result[0].get('city')

    resp = client.get("/v1/widget-data/address?province=河南")

    assert resp.status_code == 200
    result = resp.json.get('result')
    assert 1 == len(result)
    assert "郑州市" == result[0].get('city')

    resp = client.get("/v1/widget-data/address?province=江苏省&city=南京市&zone=玄武区")

    assert resp.status_code == 200
    result = resp.json.get('result')
    assert 1 == len(result)
    assert "南京市" == result[0].get('city')
    assert "梅园新村街道" == result[0].get('town')

    resp = client.get("/v1/widget-data/address?province=湖北&city=天门市&zone=1&query_town=true")

    assert resp.status_code == 200
    result = resp.json.get('result')
    assert 1 == len(result)
    assert "天门市" == result[0].get('city')
    assert "梅园新村街道" == result[0].get('town')

    resp = client.get(
        "/v1/widget-data/address",
        query_string=dict(province="北京市", city="北京市")
    )
    assert resp.status_code == 200
    result = resp.json["result"]
    assert len(result) == 1
    result_address = result[0]
    assert result_address["province"] == "北京市"
    assert result_address["city"] == "北京市"  # 北京市市辖区 in db
    assert result_address["zone"] == "东城区"
