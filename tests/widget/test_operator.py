from pytest import raises

from robot_processor.form.errors import FormError
from robot_processor.form.widget_condition import check_if_satisfy_condition
from robot_processor.form.widget_condition import ValueType, Operator


def test_product_in():
    value_type = ValueType.OBJECT
    operator = Operator.IN
    src = {"sku": "4706918257151", "spu": "651886369079", "price": 1}

    assert check_if_satisfy_condition(
        value_type, operator, src, ["xxxxx", "651886369079"]
    )
    assert not check_if_satisfy_condition(
        value_type, operator, src, ["xxxxx", "yyyyyy"]
    )


def test_number():
    value_type = ValueType.NUMBER
    src = "1.01"
    target = 1

    assert check_if_satisfy_condition(value_type, Operator.GT, src, target)
    assert not check_if_satisfy_condition(value_type, Operator.LT, src, target)


def test_string():
    value_type = ValueType.STRING
    src = "abc"
    target = ["a", "ab"]

    assert check_if_satisfy_condition(value_type, Operator.MATCH_ANY, src, target)
    assert not check_if_satisfy_condition(
        value_type, Operator.EXACTLY_MATCH_ANY, src, target
    )


def test_address():
    value_type = ValueType.OBJECT
    src = {
        "city": "北京市市辖区",
        "name": "xxx",
        "zone": "东城区",
        "state": "北京市",
        "mobile": "18888888888",
        "address": "中山公园",
    }

    assert check_if_satisfy_condition(value_type, Operator.PROVINCE_EQ, src, "北京市")
    assert not check_if_satisfy_condition(value_type, Operator.PROVINCE_EQ, src, "上海市")

    assert check_if_satisfy_condition(value_type, Operator.PROVINCE_IN, src, ["北京市"])
    assert not check_if_satisfy_condition(
        value_type, Operator.PROVINCE_IN, src, ["上海市"]
    )


def test_single_select():
    value_type = ValueType.ENUM
    target = [["1", "1.1"], ["2", "2.1"]]
    assert check_if_satisfy_condition(value_type, Operator.IN, ["1", "1.1"], target)

    assert not check_if_satisfy_condition(value_type, Operator.IN, ["1", "1.2"], target)


def test_multi_select():
    value_type = ValueType.ENUM
    src = [["1", "1.1"], ["1", "1.2"], ["2", "2.1"]]
    target = [["1", "1.1"], ["2", "2.1"], ["3", "3.1"]]
    assert check_if_satisfy_condition(value_type, Operator.CONTAINS_ANY, src, target)
    assert not check_if_satisfy_condition(
        value_type, Operator.CONTAINS_ALL, src, target
    )
    target = [["1", "1.1"], ["2", "2.1"]]
    assert check_if_satisfy_condition(value_type, Operator.CONTAINS_ALL, src, target)

    assert not check_if_satisfy_condition(
        value_type, Operator.DISJOINT, src, [["1", "1.1"], ["2", "2.1"]]
    )
    assert check_if_satisfy_condition(
        value_type, Operator.DISJOINT, src, [["3", "3.1"]]
    )


def test_data_time():
    value_type = ValueType.DATETIME
    assert check_if_satisfy_condition(
        value_type, Operator.DATE_EQ, "2021-09-18 12:03:12", "2021-09-18"
    )
    assert check_if_satisfy_condition(
        value_type, Operator.DATE_BEFORE, "2021-09-18 12:03:12", "2021-09-19"
    )
    assert not check_if_satisfy_condition(
        value_type, Operator.DATE_AFTER, "2021-09-18 12:03:12", "2021-09-19"
    )
    assert check_if_satisfy_condition(
        value_type,
        Operator.DATE_BETWEEN,
        "2021-09-18 12:03:12",
        ["2021-09-17", "2021-09-18"],
    )
    assert check_if_satisfy_condition(
        value_type, Operator.TIME_EQ, "2021-09-18 12:03:12", "12:03:12"
    )
    assert check_if_satisfy_condition(
        value_type, Operator.TIME_BEFORE, "2021-09-18 12:03:12", "12:05:12"
    )
    assert not check_if_satisfy_condition(
        value_type, Operator.TIME_AFTER, "2021-09-18 12:03:12", "12:05:12"
    )
    assert check_if_satisfy_condition(
        value_type,
        Operator.TIME_BETWEEN,
        "2021-09-18 12:03:12",
        ["12:00:12", "12:05:12"],
    )
    with raises(FormError):
        check_if_satisfy_condition(value_type, Operator.DATE_EQ, "-", "2021-09-18")


def test_table_time():
    value_type = ValueType.TIME
    assert check_if_satisfy_condition(
        value_type, Operator.TIME_EQ, "12:30:30", "12:30:30"
    )
    assert check_if_satisfy_condition(
        value_type, Operator.TIME_BEFORE, "12:30:00", "12:30:30"
    )
    assert check_if_satisfy_condition(
        value_type, Operator.TIME_AFTER, "12:30:50", "12:30:30"
    )
    assert check_if_satisfy_condition(
        value_type, Operator.TIME_BETWEEN, "12:30:30", ["12:30:00", "12:30:50"]
    )


def test_table_string():
    value_type = ValueType.STRING
    assert check_if_satisfy_condition(value_type, Operator.EQ, "abc", "abc")
    assert not check_if_satisfy_condition(value_type, Operator.EQ, "abc", "abd")
    assert check_if_satisfy_condition(value_type, Operator.IN, "abc", ["abc", "d"])
    assert check_if_satisfy_condition(
        value_type, Operator.MATCH_ANY, "abc", ["b", "c", "d"]
    )
    assert check_if_satisfy_condition(value_type, Operator.MATCH_ALL, "abc", ["b", "c"])
    assert not check_if_satisfy_condition(
        value_type, Operator.MATCH_ALL, "abc", ["b", "c", "d"]
    )
    assert check_if_satisfy_condition(
        value_type, Operator.CONTAINS_ANY, ["a", "b"], ["a", "b", "c"]
    )
    assert check_if_satisfy_condition(
        value_type, Operator.CONTAINS_ALL, ["a", "b"], ["a", "b"]
    )
    assert not check_if_satisfy_condition(
        value_type, Operator.CONTAINS_ALL, ["a", "b"], ["a", "b", "c"]
    )
