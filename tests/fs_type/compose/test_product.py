from result import Ok

from robot_processor.fs_type.base import Operator
from robot_processor.fs_type.basic import String
from robot_processor.fs_type.compose import (
    FsTypeProductList,
    ProductSKUSchema,
    ProductSPUSchema,
    Table,
)


class TestPerformOperation:
    product_sku_1 = ProductSKUSchema(
        mode="sku",
        spu="衣服",
        sku="S码",
        outerSku="S码;白色",
    )
    product_sku_2 = ProductSKUSchema(
        mode="sku",
        spu="衣服",
        sku="M码",
        outerSku="M码;白色",
    )
    product_spu_1 = ProductSPUSchema(mode="spu", spu="衣服")
    product_spu_2 = ProductSPUSchema(mode="spu", spu="裤子")

    def test_in_success(self):
        product_c = FsTypeProductList.from_python_value(
            [
                self.product_sku_1,
                self.product_sku_2,
                self.product_spu_1,
                self.product_spu_2,
            ]
        ).unwrap()

        # 通过 sku 匹配
        assert product_c.perform_operation(
            Operator.IN, None, Table[String](__root__=[String(__root__="M码")])
        ) == Ok(True)
        # 通过 spu 匹配
        assert product_c.perform_operation(
            Operator.IN, None, Table[String](__root__=[String(__root__="衣服")])
        ) == Ok(True)
        # 匹配任一
        assert product_c.perform_operation(
            Operator.IN,
            None,
            Table[String](__root__=[String(__root__="鞋子"), String(__root__="裤子")]),
        )

    def test_in_failure(self):
        product_c = FsTypeProductList.from_python_value(
            [
                self.product_sku_1,
                self.product_sku_2,
                self.product_spu_1,
                self.product_spu_2,
            ]
        ).unwrap()

        assert product_c.perform_operation(
            Operator.IN, None, Table[String](__root__=[String(__root__="黑色")])
        ) == Ok(False)

    def test_exist_success(self):
        product_c = FsTypeProductList.from_python_value(
            [
                self.product_sku_1,
                self.product_sku_2,
                self.product_spu_1,
                self.product_spu_2,
            ]
        ).unwrap()
        assert product_c.perform_operation(Operator.EXIST, None, None) == Ok(True)

    def test_exist_failure(self):
        product_c = FsTypeProductList.from_python_value([]).unwrap()
        assert product_c.perform_operation(Operator.EXIST, None, None) == Ok(False)
