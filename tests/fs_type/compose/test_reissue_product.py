from result import Ok

from robot_processor.fs_type.base import Operator
from robot_processor.fs_type.basic import String
from robot_processor.fs_type.compose import Table, FsTypeReissueProductList, ReissueProductSchema


class TestPerformOperation:
    clothes_reissue_product = ReissueProductSchema(
        SPU="衣服",
        SKU="S码",
        TITLE="",
        PICTURE=None,
        DESCRIPTION=None,
        INVENTORY=None,
        REISSUE_QUANTITY=1,
        SKU_OUTER=""
    )
    pants_reissue_product = ReissueProductSchema(
        SPU="裤子",
        SKU="M码",
        TITLE="",
        PICTURE=None,
        DESCRIPTION=None,
        INVENTORY=None,
        REISSUE_QUANTITY=1,
        SKU_OUTER=""
    )

    def test_in_success(self):
        product_c = FsTypeReissueProductList.from_python_value([
            self.clothes_reissue_product,
            self.pants_reissue_product
        ]).unwrap()

        assert product_c.perform_operation(
            Operator.IN,
            None,
            Table[String].from_python_value(["衣服", "L码"]).unwrap()
        ) == Ok(True)
        assert product_c.perform_operation(
            Operator.IN,
            None,
            Table[String].from_python_value(["鞋子", "M码"]).unwrap()
        ) == Ok(True)

    def test_in_failure(self):
        product_c = FsTypeReissueProductList.from_python_value([self.pants_reissue_product]).unwrap()

        assert product_c.perform_operation(
            Operator.IN,
            None,
            Table[String].from_python_value(["鞋子", "L码"]).unwrap()
        ) == Ok(False)

    def test_exist_success(self):
        product_c = FsTypeReissueProductList.from_python_value([self.pants_reissue_product]).unwrap()
        assert product_c.perform_operation(Operator.EXIST, None, None) == Ok(True)

    def test_exist_failure(self):
        product_c = FsTypeReissueProductList.from_python_value([]).unwrap()
        assert product_c.perform_operation(Operator.EXIST, None, None) == Ok(False)
