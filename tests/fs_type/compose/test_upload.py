from robot_processor.fs_type.base import FsTypeLiteral
from robot_processor.fs_type.compose import FsTypeUploadList


def test_tableschema():
    tableschema = FsTypeUploadList.generate_tableschema()
    assert tableschema.dict(exclude_none=True) == {
        "title": "文件",
        "fields": [
            {
                "name": "fileName",
                "title": "文件名",
                "type": FsTypeLiteral.string,
                "format": "default",
                "constraints": {"required": False, "unique": False},
            },
            {
                "name": "url",
                "title": "文件地址",
                "type": FsTypeLiteral.string,
                "format": "default",
                "constraints": {"required": False, "unique": False},
            },
        ],
    }
