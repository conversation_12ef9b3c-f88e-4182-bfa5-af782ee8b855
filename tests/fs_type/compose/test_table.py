from result import Ok

from robot_processor.fs_type.base import TableSchema, FsTypeLiteral, Operator
from robot_processor.fs_type.basic import String
from robot_processor.fs_type.compose import Table


class TestCreateModelFromTableschema:
    def tableschema(self):
        return TableSchema(
            title="自定义",
            description="这是一个简单的自定义表格",
            fields=[
                TableSchema.FieldDescriptor(
                    name="tid",
                    title="订单",
                    type=FsTypeLiteral.string,
                    example="2541823527468072222",
                    constraints=TableSchema.FieldConstraint(required=True),
                ),
                TableSchema.FieldDescriptor(
                    name="oid",
                    title="子订单",
                    type=FsTypeLiteral.string,
                    example="2541823527468082222",
                ),
            ],
        )

    def nested_tableschema(self):
        return TableSchema(
            title="自定义嵌套表格",
            description="这是一个自定义嵌套表格",
            fields=[
                TableSchema.FieldDescriptor(
                    name="tid",
                    title="订单",
                    type=FsTypeLiteral.string,
                    example="2541823527468072222",
                    constraints=TableSchema.FieldConstraint(required=True),
                ),
                TableSchema.FieldDescriptor(
                    name="oid",
                    title="子订单",
                    type=FsTypeLiteral.string,
                    example="2541823527468082222",
                ),
                TableSchema.FieldDescriptor(
                    name="logistics",
                    title="快递信息",
                    type=FsTypeLiteral.table,
                    fields=[
                        TableSchema.FieldDescriptor(
                            name="logistics_no",
                            title="快递单号",
                            type=FsTypeLiteral.string,
                            example="2541823527468072222",
                            constraints=TableSchema.FieldConstraint(required=True),
                        ),
                        TableSchema.FieldDescriptor(
                            name="logistics_company",
                            title="快递公司",
                            type=FsTypeLiteral.string,
                            example="顺丰",
                            constraints=TableSchema.FieldConstraint(required=True),
                        ),
                    ],
                ),
            ],
        )

    def test_create_customize_trade(self):
        tableschema = self.tableschema()

        customize_table = Table.create_model_from_tableschema(tableschema)
        print(customize_table.schema())

    def test_create_customize_nested_table(self):
        tableschema = self.nested_tableschema()
        customize_table = Table.create_model_from_tableschema(tableschema)
        print(customize_table.schema())


class TestPerformOperation:
    def test_any_match_success(self):
        table_string_c = Table[String].from_python_value(["顺丰快递", "圆通快递"]).unwrap()
        result = table_string_c.perform_operation(
            Operator.ANY_MATCH,
            Operator.MATCH_ANY,
            Table[String].from_python_value(["圆通", "韵达"]).unwrap()
        )
        assert result == Ok(True)

    def test_any_match_failure(self):
        table_string_c = Table[String].from_python_value(["顺丰快递", "圆通快递"]).unwrap()
        result = table_string_c.perform_operation(
            Operator.ANY_MATCH,
            Operator.MATCH_ANY,
            Table[String].from_python_value(["韵达", "圆通速递"]).unwrap(),
        )
        assert result == Ok(False)

    def test_all_match_success(self):
        table_string_c = Table[String].from_python_value(["顺丰快递", "圆通快递", "韵达快递"]).unwrap()
        result = table_string_c.perform_operation(
            Operator.ALL_MATCH,
            Operator.MATCH_ANY,
            Table[String].from_python_value(["顺丰", "圆通", "韵达"]).unwrap()
        )
        assert result == Ok(True)

    def test_contains_all_failure(self):
        table_string_c = Table[String].from_python_value(["顺丰快递", "圆通快递", "韵达快递"]).unwrap()
        result = table_string_c.perform_operation(
            Operator.ALL_MATCH,
            Operator.MATCH_ANY,
            Table[String].from_python_value(["顺丰", "申通"]).unwrap(),
        )
        assert result == Ok(False)

    def test_exist_success(self):
        table_c = Table[String].from_python_value(["broken", "offline"]).unwrap()
        result = table_c.perform_operation(Operator.ALL_MATCH, Operator.EXIST, None)
        assert result == Ok(True)
