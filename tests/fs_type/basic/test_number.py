import re
from result import Ok

from robot_processor.fs_type.base import Operator
from robot_processor.fs_type.basic import String, Number
from robot_processor.fs_type.compose import NumberRange


def test_from_python_value():
    assert Number.from_python_value(123) == Ok(Number(__root__=123))
    assert Number.from_python_value(12.3) == Ok(Number(__root__=12.3))
    assert Number.from_python_value(None) == Ok(None)


def test_from_fs_type():
    int_c = Number(__root__=123)
    assert Number.from_fs_type(int_c) == Ok(Number(__root__=123))

    float_c = Number(__root__=12.3)
    assert Number.from_fs_type(float_c) == Ok(Number(__root__=12.3))

    string_c = String(__root__="123")
    res = Number.from_fs_type(string_c)
    assert res.is_err() and re.match("Number 不支持 String 的转换", str(res.unwrap_err()))


def test_to_json():
    assert Number(__root__=123).to_json() == 123
    assert Number(__root__=12.3).to_json() == 12.3


class TestOperation:
    def test_perform_operation_eq_success(self):
        number = Number(__root__=5)
        result = number.perform_operation(Operator.EQ, None, Number(__root__=5))
        assert result == Ok(True)

    def test_perform_operation_eq_failure(self):
        number = Number(__root__=5)
        result = number.perform_operation(Operator.EQ, None, Number(__root__=6))
        assert result == Ok(False)

    def test_perform_operation_ne_success(self):
        number = Number(__root__=5)
        result = number.perform_operation(Operator.NE, None, Number(__root__=6))
        assert result == Ok(True)

    def test_perform_operation_ne_failure(self):
        number = Number(__root__=5)
        result = number.perform_operation(Operator.NE, None, Number(__root__=5))
        assert result == Ok(False)

    def test_perform_operation_gt_success(self):
        number = Number(__root__=5)
        result = number.perform_operation(Operator.GT, None, Number(__root__=4))
        assert result == Ok(True)

    def test_perform_operation_gt_failure(self):
        number = Number(__root__=5)
        result = number.perform_operation(Operator.GT, None, Number(__root__=5))
        assert result == Ok(False)

    def test_perform_operation_gte_success(self):
        number = Number(__root__=5)
        result = number.perform_operation(Operator.GTE, None, Number(__root__=5))
        assert result == Ok(True)

    def test_perform_operation_gte_failure(self):
        number = Number(__root__=5)
        result = number.perform_operation(Operator.GTE, None, Number(__root__=6))
        assert result == Ok(False)

    def test_perform_operation_lt_success(self):
        number = Number(__root__=5)
        result = number.perform_operation(Operator.LT, None, Number(__root__=6))
        assert result == Ok(True)

    def test_perform_operation_lt_failure(self):
        number = Number(__root__=5)
        result = number.perform_operation(Operator.LT, None, Number(__root__=5))
        assert result == Ok(False)

    def test_perform_operation_lte_success(self):
        number = Number(__root__=5)
        result = number.perform_operation(Operator.LTE, None, Number(__root__=5))
        assert result == Ok(True)

    def test_perform_operation_lte_failure(self):
        number = Number(__root__=5)
        result = number.perform_operation(Operator.LTE, None, Number(__root__=4))
        assert result == Ok(False)

    def test_perform_operation_between_success(self):
        number = Number(__root__=5)
        result = number.perform_operation(
            Operator.BETWEEN,
            None,
            NumberRange(__root__=[Number(__root__=4), Number(__root__=6)])
        )
        assert result == Ok(True)

    def test_perform_operation_between_failure(self):
        number = Number(__root__=5)
        result = number.perform_operation(
            Operator.BETWEEN,
            None,
            NumberRange(__root__=[Number(__root__=6), Number(__root__=7)])
        )
        assert result == Ok(False)

    def test_perform_operation_exist(self):
        number = Number(__root__=5)
        result = number.perform_operation(Operator.EXIST, None, None)
        assert result == Ok(True)
