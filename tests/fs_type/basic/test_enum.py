import re

from result import Ok

from robot_processor.fs_type.base import Operator
from robot_processor.fs_type.basic import String, Enum, EnumOptions, MultiEnum


class OrderQuestionOptions(EnumOptions):
    退货_仅退款 = ["退货", "仅退款"]
    退货_换货 = ["退货", "换货"]
    其他问题 = ["其他问题"]


OrderQuestionEnum = Enum[OrderQuestionOptions]


def test_from_python_value():
    # 传入 list[str]
    input_value = ["退货", "仅退款"]
    res = OrderQuestionEnum.from_python_value(input_value)
    assert res.is_ok()
    enum_c = res.unwrap()
    assert enum_c._inner_value == OrderQuestionEnum(__root__=OrderQuestionOptions.退货_仅退款)._inner_value

    input_value = ["退货", "换货"]
    res = OrderQuestionEnum.from_python_value(input_value)
    assert res.is_ok()
    enum_c = res.unwrap()
    assert enum_c._inner_value == OrderQuestionEnum(__root__=OrderQuestionOptions.退货_换货)._inner_value

    input_value = ["其他问题"]
    res = OrderQuestionEnum.from_python_value(input_value)
    assert res.is_ok()
    enum_c = res.unwrap()
    assert enum_c._inner_value == OrderQuestionEnum(__root__=OrderQuestionOptions.其他问题)._inner_value

    # 传入 list[dict]
    input_value = [{"value": "其他问题", "label": "其他问题"}]
    res = OrderQuestionEnum.from_python_value(input_value)
    assert res.is_ok()
    enum_c = res.unwrap()
    assert enum_c._inner_value == OrderQuestionEnum(__root__=OrderQuestionOptions.其他问题)._inner_value

    input_value = [{"value": "退货", "label": "退货"}, {"value": "换货", "label": "换货"}]
    res = OrderQuestionEnum.from_python_value(input_value)
    assert res.is_ok()
    enum_c = res.unwrap()
    assert enum_c._inner_value == OrderQuestionEnum(__root__=OrderQuestionOptions.退货_换货)._inner_value


class SimpleEnumOptions(EnumOptions):
    Good = "好"
    Bad = "不好"


SimpleEnum = Enum[SimpleEnumOptions]


def test_from_fs_type():
    string_c = String(__root__="好")
    res = SimpleEnum.from_fs_type(string_c)
    assert res.is_ok()
    enum_c = res.unwrap()
    assert enum_c._inner_value == SimpleEnumOptions.Good


def test动态数据源():
    options = ["是", "否"]
    FlagOptions = EnumOptions.build(options)
    FlagEnum = Enum[FlagOptions]

    res = FlagEnum.from_python_value("是")
    assert res.is_ok()
    enum_c = res.unwrap()
    assert enum_c._inner_value.value == "是"

    res = FlagEnum.from_python_value("否")
    assert res.is_ok()
    enum_c = res.unwrap()
    assert enum_c._inner_value.value == "否"

    res = FlagEnum.from_python_value("不知道")
    assert res.is_err() and re.search("value is not a valid enumeration member", str(res.unwrap_err()))


def test_to_json():
    enum_c = Enum[OrderQuestionOptions](__root__=OrderQuestionOptions.退货_换货)
    assert enum_c.to_json() == [{"value": "退货", "label": "退货"}, {"value": "换货", "label": "换货"}]

    enum_c = Enum[OrderQuestionOptions](__root__=OrderQuestionOptions.其他问题)
    assert enum_c.to_json() == [{"value": "其他问题", "label": "其他问题"}]

    enum_c = Enum[SimpleEnumOptions](__root__=SimpleEnumOptions.Good)
    assert enum_c.to_json() == [{"value": "好", "label": "好"}]

    enum_c = MultiEnum[OrderQuestionOptions](__root__=[OrderQuestionOptions.退货_换货, OrderQuestionOptions.其他问题])
    assert enum_c.to_json() == [
        [{"value": "退货", "label": "退货"}, {"value": "换货", "label": "换货"}],
        [{"value": "其他问题", "label": "其他问题"}]
    ]

    enum_c = MultiEnum[SimpleEnumOptions](__root__=[SimpleEnumOptions.Good, SimpleEnumOptions.Bad])
    assert enum_c.to_json() == [[{"value": "好", "label": "好"}], [{"value": "不好", "label": "不好"}]]


def test获取枚举项():
    enum = OrderQuestionEnum.get_enum()
    assert list(enum) == [OrderQuestionOptions.退货_仅退款, OrderQuestionOptions.退货_换货, OrderQuestionOptions.其他问题]

    # 动态生成的
    FlagOptions = EnumOptions.build(["是", "否"])
    FlagEnum = Enum[FlagOptions]
    assert [each.value for each in FlagEnum.get_enum()] == ["是", "否"]


class Color(EnumOptions):
    Red = "red"
    Green = "green"
    Blue = "blue"


class TestEnumPerformOperation:
    def test_enum_eq_success(self):
        enum_c = Enum[Color](__root__=Color.Red)
        result = enum_c.perform_operation(Operator.EQ, None, Enum[Color](__root__=Color.Red))
        assert result == Ok(True)

    def test_enum_eq_failure(self):
        enum_c = Enum[Color](__root__=Color.Red)
        result = enum_c.perform_operation(
            Operator.EQ, None, Enum[Color](__root__=Color.Green)
        )
        assert result == Ok(False)

    def test_enum_match_any_success(self):
        enum_c = Enum[Color](__root__=Color.Red)
        result = enum_c.perform_operation(
            Operator.MATCH_ANY,
            None,
            MultiEnum[Color](__root__=[Color.Red, Color.Blue]),
        )
        assert result == Ok(True)

    def test_enum_match_any_failure(self):
        enum_c = Enum[Color](__root__=Color.Red)
        result = enum_c.perform_operation(
            Operator.MATCH_ANY,
            None,
            MultiEnum[Color](__root__=[Color.Blue, Color.Green])
        )
        assert result == Ok(False)

    def test_enum_disjoint_success(self):
        enum_c = Enum[Color](__root__=Color.Red)
        result = enum_c.perform_operation(
            Operator.DISJOINT,
            None,
            MultiEnum[Color](__root__=[Color.Blue, Color.Green])
        )
        assert result == Ok(True)

    def test_enum_disjoint_failure(self):
        enum_c = Enum[Color](__root__=Color.Red)
        result = enum_c.perform_operation(
            Operator.DISJOINT,
            None,
            MultiEnum[Color](__root__=[Color.Red, Color.Blue])
        )
        assert result == Ok(False)

    def test_enum_exist(self):
        enum_c = Enum[Color](__root__=Color.Red)
        result = enum_c.perform_operation(Operator.EXIST, None, None)
        assert result == Ok(True)


class TestMultiEnumPerformOperation:
    def test_any_eq_success(self):
        enum_c = MultiEnum[Color](__root__=[Color.Red, Color.Green])
        result = enum_c.perform_operation(
            Operator.ANY_MATCH,
            Operator.EQ,
            Enum[Color](__root__=Color.Red)
        )
        assert result == Ok(True)

    def test_any_eq_failure(self):
        enum_c = MultiEnum[Color](__root__=[Color.Blue, Color.Green])
        result = enum_c.perform_operation(
            Operator.ANY_MATCH,
            Operator.EQ,
            Enum[Color](__root__=Color.Red)
        )
        assert result == Ok(False)

    def test_all_eq_success(self):
        enum_c = MultiEnum[Color](__root__=[Color.Red, Color.Red])
        result = enum_c.perform_operation(
            Operator.ALL_MATCH,
            Operator.EQ,
            Enum[Color](__root__=Color.Red)
        )
        assert result == Ok(True)

    def test_all_eq_failure(self):
        enum_c = MultiEnum[Color](__root__=[Color.Red, Color.Green])
        result = enum_c.perform_operation(
            Operator.ALL_MATCH,
            Operator.EQ,
            Enum[Color](__root__=Color.Red)
        )
        assert result == Ok(False)

    def test_any_match_any_success(self):
        enum_c = MultiEnum[Color](__root__=[Color.Red, Color.Green])
        result = enum_c.perform_operation(
            Operator.ANY_MATCH,
            Operator.MATCH_ANY,
            MultiEnum[Color](__root__=[Color.Red, Color.Blue])
        )
        assert result == Ok(True)

    def test_any_match_any_failure(self):
        enum_c = MultiEnum[Color](__root__=[Color.Red, Color.Green])
        result = enum_c.perform_operation(
            Operator.ANY_MATCH,
            Operator.MATCH_ANY,
            MultiEnum[Color](__root__=[Color.Blue])
        )
        assert result == Ok(False)

    def test_all_match_any_success(self):
        enum_c = MultiEnum[Color](__root__=[Color.Red, Color.Green])
        result = enum_c.perform_operation(
            Operator.ALL_MATCH,
            Operator.MATCH_ANY,
            MultiEnum[Color](__root__=[Color.Red, Color.Green])
        )
        assert result == Ok(True)

    def test_all_match_any_failure(self):
        enum_c = MultiEnum[Color](__root__=[Color.Red, Color.Green])
        result = enum_c.perform_operation(
            Operator.ALL_MATCH,
            Operator.MATCH_ANY,
            MultiEnum[Color](__root__=[Color.Red])
        )
        assert result == Ok(False)

    def test_any_disjoint_success(self):
        enum_c = MultiEnum[Color](__root__=[Color.Red, Color.Green])
        result = enum_c.perform_operation(
            Operator.ANY_MATCH,
            Operator.DISJOINT,
            MultiEnum[Color](__root__=[Color.Blue])
        )
        assert result == Ok(True)

    def test_any_disjoint_failure(self):
        enum_c = MultiEnum[Color](__root__=[Color.Red, Color.Green])
        result = enum_c.perform_operation(
            Operator.ANY_MATCH,
            Operator.DISJOINT,
            MultiEnum[Color](__root__=[Color.Red, Color.Green])
        )
        assert result == Ok(False)

    def test_all_disjoint_success(self):
        enum_c = MultiEnum[Color](__root__=[Color.Red, Color.Green])
        result = enum_c.perform_operation(
            Operator.ALL_MATCH,
            Operator.DISJOINT,
            MultiEnum[Color](__root__=[Color.Blue])
        )
        assert result == Ok(True)

    def test_all_disjoint_failure(self):
        enum_c = MultiEnum[Color](__root__=[Color.Red, Color.Green])
        result = enum_c.perform_operation(
            Operator.ALL_MATCH,
            Operator.DISJOINT,
            MultiEnum[Color](__root__=[Color.Red, Color.Blue])
        )
        assert result == Ok(False)
