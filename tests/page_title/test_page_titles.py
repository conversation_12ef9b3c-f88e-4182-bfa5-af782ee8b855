from robot_processor.business_order.page_titles.models import PageTitle
from robot_processor.enums import PageType
from robot_processor.ext import db
from robot_processor.form.models import WidgetInfo


def test_get_page_titles(mocker, client, mock_form, mock_step, widget,
                         mock_widget_info, mock_widget_collection):
    mock_form.subscribe(client.shop, True)
    mock_widget_info.widget_collection_id = mock_widget_collection.id
    mock_widget_info.widget_id = widget('下拉单选').id
    mock_widget_info.option_value = {
        "mode": "single", "label": "打款理由",
        "level": 1, "options": [
            {"label": "好评返现", "value": "好评返现"},
            {"label": "瑕疵赔偿", "value": "瑕疵赔偿"},
            {"label": "补差价", "value": "补差价"},
            {"label": "退换运费", "value": "退换运费"}
        ],
        "required": True, "placeholder": "请选择", "defaultValue": []
    }
    mock_step.form_id = mock_form.id
    mock_step.widget_collection_id = mock_widget_collection.id
    db.session.commit()

    # 选择工单模版，无自定义表头的情况
    resp = client.get(f"/v1/pages/titles?page_type=REPORTS&form_ids={mock_form.id}&mode=header")

    assert resp.status_code == 200

    # 不选择工单模版，无自定义表头的情况
    resp = client.get("/v1/pages/titles?page_type=REPORTS")

    assert resp.status_code == 200

    # 选择工单模版，有自定义表头的情况
    client.post(
        "/v1/pages/titles",
        json={
            "titles": [
                {
                    "key": "key1",
                    "label": "label1",
                    "type": "widget",
                    "widgets": [
                        {"key": "key1", "label": "label1"}
                    ]
                }
            ],
            "page_type": PageType.REPORTS.name,
            "form_ids": [mock_form.id]
        }
    )

    resp = client.get(f"/v1/pages/titles?page_type=REPORTS&mode=customer&form_ids={mock_form.id}")
    assert resp.status_code == 200
    assert resp.json["titles"] == [
        {
            "default_in_customer": True,
            "key": "key1",
            "label": "label1",
            "type": "widget",
            "widgets": [
                {"key": "key1", "label": "label1"}
            ],
            "widget_meta": []
        }
    ]

    # 不选择工单模版，有自定义表头的情况
    client.post(
        "/v1/pages/titles",
        json={
            "titles": [
                {
                    "key": "key1",
                    "label": "label1",
                    "type": "widget"
                }
            ],
            "page_type": PageType.REPORTS.name
        }
    )
    resp = client.get("/v1/pages/titles?page_type=REPORTS&mode=customer")

    assert resp.status_code == 200
    assert resp.json["titles"] == [
        {
            "default_in_customer": True,
            "key": "key1",
            "label": "label1",
            "type": "widget",
            "widget_meta": []
        }
    ]

    mock_widget_info.option_value = {
        "count": 2,
        "label": "评分星星",
        "required": False,
        "rateItems": [
            {
                # ...
                "optionType": "star"
            }
        ],
        "hasTopSpace": False,
        "defaultState": True
    }

    db.session.commit()

    # 选择工单模版，无自定义表头的情况
    resp = client.get(f"/v1/pages/titles?page_type=REPORTS&form_ids={mock_form.id}&mode=header")

    assert resp.status_code == 200

    # 打款表头
    resp = client.get(f"/v1/pages/titles?page_type=TRANSFER_INFO&form_ids={mock_form.id}&mode=header")
    assert resp.status_code == 200

    # 打款表头
    resp = client.get(f"/v1/pages/titles?page_type=TRANSFER_INFO&form_ids={mock_form.id}&mode=customer")
    assert resp.status_code == 200

    mock_widget_info.option_value = \
        {"label": "复合组件", "fields":
            [{"id": 57, "key": "ffc354d8-2d1b-470f-bc5e-dbd7d94d8a66",
              "type": "string", "before": False,
              "data_schema": {"fields": [
                  {"name": "ffc354d8-2d1b-470f-bc5e-dbd7d94d8a66",
                   "type": "string", "title": "订单号",
                   "constraints": {"required": True}}], "multi_row": False},
              "widget_type": "table", "option_value": {"label": "订单号",
                                                       "category": "2",
                                                       "required": True,
                                                       "hasTopSpace": False,
                                                       "linkProvider": False}},
             {"id": 57, "key": "37b633c0-faa5-4411-b645-649048d3c64b",
              "type": "string", "before": False, "data_schema": {"fields": [
                  {"name": "37b633c0-faa5-4411-b645-649048d3c64b",
                   "type": "string",
                   "title": "内部单号", "constraints": {"required": True}}],
                  "multi_row": False},
              "widget_type": "table",
              "option_value": {"label": "内部单号", "category": "2",
                               "required": True,
                               "hasTopSpace": False, "linkProvider": False}},
             {"id": 52, "key": "68381e6a-11ee-4460-af7e-4a65e04a1b77",
              "type": "table", "before": False, "data_schema": {"fields": [
                  {"name": "f2fadd89-a595-4ef9-ae7d-853bf090eca2",
                   "type": "string",
                   "title": "快递公司", "constraints": {"required": True}},
                  {"name": "f91ef74f-76c6-4115-8525-70ed0d6ce848",
                      "type": "string",
                      "title": "快递单号", "constraints": {"required": True}}],
                  "multi_row": True},
              "widget_type": "table",
              "option_value": {"label": "快递信息", "fields": [
                  {"id": 57, "key": "f2fadd89-a595-4ef9-ae7d-853bf090eca2",
                   "type": "string", "before": False,
                   "data_schema": {"fields": [
                       {"name": "f2fadd89-a595-4ef9-ae7d-853bf090eca2",
                        "type": "string", "title": "快递公司",
                        "constraints": {"required": True}}],
                       "multi_row": False},
                   "widget_type": "table",
                   "option_value": {"label": "快递公司", "category": "2",
                                    "required": True, "hasTopSpace": False,
                                    "linkProvider": False}},
                  {"id": 57, "key": "f91ef74f-76c6-4115-8525-70ed0d6ce848",
                   "type": "string", "before": False,
                   "data_schema": {"fields": [
                       {"name": "f91ef74f-76c6-4115-8525-70ed0d6ce848",
                        "type": "string", "title": "快递单号",
                        "constraints": {"required": True}}],
                       "multi_row": False},
                   "widget_type": "table",
                   "option_value": {"label": "快递单号", "category": "2",
                                    "required": True, "hasTopSpace": False,
                                    "linkProvider": False}}], "category": "3",
                  "multiRow": True,
                  "required": True}}],
         "category": "3", "multiRow": True,
         "required": True}

    resp = client.get(f"/v1/pages/titles?page_type=REPORTS&mode=header&form_name={mock_form.name}")

    assert resp.status_code == 200


def test_modify_titles(mocker, client, mock_form):
    mock_form.subscribe(client.shop, True)
    db.session.commit()

    client.post(
        "/v1/pages/titles",
        json={
            "titles": [
                {
                    "key": "key1",
                    "label": "label1",
                    "type": "widget"
                }
            ],
            "page_type": PageType.REPORTS.name,
            "form_name": mock_form.name
        }
    )

    t1 = client.get(f"/v1/pages/titles?page_type=REPORTS&mode=customer&form_name={mock_form.name}")

    client.put(
        f"/v1/pages/titles/{t1.json['id']}",
        json={
            "titles": [
                {
                    "key": "key2",
                    "label": "label2",
                    "type": "system"
                },
                {
                    "key": "key1",
                    "label": "label1",
                    "type": "widget"
                }
            ],
            "page_type": PageType.REPORTS.name,
            "form_name": mock_form.name
        }
    )

    resp = client.get(f"/v1/pages/titles?page_type=REPORTS&mode=customer&form_name={mock_form.name}")

    assert resp.status_code == 200
    assert resp.json["titles"] == [
        {
            "default_in_customer": True,
            "key": "key2",
            "label": "label2",
            "type": "system",
            "widget_meta": []
        },
        {
            "default_in_customer": True,
            "key": "key1",
            "label": "label1",
            "type": "widget",
            "widget_meta": []
        }
    ]


def test_level(client):
    options = [
        {
            "label": "商品清洗后",
            "value": "商品清洗后",
            "children": [
                {
                    "label": "轻微掉色",
                    "value": "轻微掉色"
                }
            ]
        },
        {
            "label": "其他问题",
            "value": "其他问题"
        }
    ]
    level = WidgetInfo.Utils.option_level(options)
    assert level == 2

    options = [{
        "label": "其他问题",
        "value": "其他问题"
    }]
    level = WidgetInfo.Utils.option_level(options)
    assert level == 1


def test_default_page_titles_from_system_variable(client, widget):
    titles = PageTitle.Utils.parse_title_list([widget('工单状态').system_widget_data])
    assert titles == [
        WidgetInfo.View.ReportBase(
            key="system_business_order_status", label='工单状态', type='string', default_in_customer=True, widgets=None
        )
    ]


def test_address_widget_with_mask_enabled(mocker, client, mock_form, mock_step, widget,
                                          mock_widget_info, mock_widget_collection):
    mocker.patch("robot_processor.business_order.page_titles.api.AddressWidgetMask.need_mask", return_value=True)
    mock_form.subscribe(client.shop, True)
    mock_widget_info.widget_collection_id = mock_widget_collection.id
    mock_widget_info.widget_id = widget('收货地址').id
    mock_widget_info.option_value = {"mode": "new", "label": "收货地址",
                                     "category": "1", "required": True, "layoutWidth": 1}
    mock_step.form_id = mock_form.id
    mock_step.widget_collection_id = mock_widget_collection.id
    db.session.commit()

    resp = client.get(f"/v1/pages/titles?page_type=TASK_CENTER&mode=header&form_ids={mock_form.id}")

    assert resp.status_code == 200

    target_key = mock_widget_info.key
    titles = resp.json["titles"]
    assert titles[0]["key"] == [target_key], resp.json
    meta_keys = {x["label"]: x["key"] for x in titles[0]["widget_meta"]}
    meta_values = {x["label"]: x["value"] for x in titles[0]["widget_meta"]}
    assert meta_keys == {
        "姓名": [f"{target_key}_name-masked"],
        "手机号": [f"{target_key}_mobile-masked"],
        "详细地址": [f"{target_key}_address-masked"]
    }
    assert meta_values == {
        "姓名": "name-masked",
        "手机号": "mobile-masked",
        "详细地址": "address-masked"
    }
