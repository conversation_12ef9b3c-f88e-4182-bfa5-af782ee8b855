def test_symbol_resolver():
    from robot_processor.invoice.config_manager.defination import symbol_resolver, Config<PERSON><PERSON>

    assert symbol_resolver.resolve(
        ConfigKey.GOODS_MODE,
        ConfigKey.GOODS_MODE.extra_info.type_spec
    )
    assert symbol_resolver.resolve(
        ConfigKey.CORPORATE_INVOICE_THRESHOLD_RULE,
        ConfigKey.CORPORATE_INVOICE_THRESHOLD_RULE.extra_info.type_spec,
        org_id=1,
    )
