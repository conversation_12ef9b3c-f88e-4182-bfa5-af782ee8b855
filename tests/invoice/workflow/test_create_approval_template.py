from pytest import fixture

from robot_processor.invoice.config_manager import update_config
from robot_processor.invoice.config_manager import Config<PERSON><PERSON>
from robot_processor.invoice.config_manager import ConfigManager


@fixture(autouse=True)
def setup(setup_auth):
    yield


def test创建并更新(mock_context, testbed):
    config_val_raw = {
        "routes": [
            {
                "status": "ENABLED",
                "filter": {
                    "conditions": [
                        {
                            "a": {"var": {"path": "issuing_type"}},
                            "o": ["EQ"],
                            "b": {"const": {"value": "BLUE"}},
                        }
                    ]
                },
                "manual_mapper": {
                    "nodes": [
                        {
                            "strategy": "ANY",
                            "reviewers": [{"id": 111, "type": "LEYAN"}],
                        }
                    ]
                },
            }
        ]
    }
    config = update_config(testbed["org_id"], ConfigKey.APPROVAL_RULESET, config_val_raw)
    assert config.id, "未写入数据库"
    config_manager = ConfigManager(testbed["org_id"])
    config_val = config_manager.get(ConfigKey.APPROVAL_RULESET)
    assert len(config_val.routes) == 1
    template = config_val.routes[0]
    assert template.filter == config_val_raw["routes"][0]["filter"]
    assert template.mode == "manual"
    assert len(template.manual_mapper.nodes) == 1
    node = template.manual_mapper.nodes[0]
    assert node.strategy.name == "ANY"
    assert len(node.reviewers) == 1
    reviewer = node.reviewers[0]
    assert reviewer.id == 111
    assert reviewer.type == "LEYAN"
