from datetime import datetime
from datetime import timedelta

from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceIssued as pb_InvoiceIssued
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow
from pytest import fixture
from robot_types.helper import serialize
from robot_types.model.invoice.config_manager import ApprovalRuleset

from robot_processor.db import in_transaction
from robot_processor.invoice.common.models import rpa_user_info
from robot_processor.invoice.config_manager import ConfigKey
from robot_processor.invoice.config_manager import update_config
from robot_processor.invoice.rpa.services import rpa_control
from robot_processor.invoice.workflow.models import InvoiceIssueQueue
from robot_processor.invoice.workflow.models import InvoiceRequest
from robot_processor.invoice.workflow.models import InvoiceType
from robot_processor.invoice.workflow.models import InvoiceWorkflow
from robot_processor.invoice.workflow.models import InvoiceWorkflowTransition
from robot_processor.invoice.workflow.models import IssuingType
from robot_processor.invoice.workflow.service import IssueBroker
from robot_processor.invoice.workflow.service import WorkflowBuilder
from robot_processor.invoice.workflow.service import WorkflowContext


@fixture
def pb_workflow():
    workflow = pb_InvoiceWorkflow.EditableView(
        invoice_type=InvoiceType.VAT_GENERAL.pb_value,
        issuing_type=IssuingType.BLUE.pb_value,
        issuing_items=[
            pb_InvoiceWorkflow.IssuingItem(
                title="可乐",
                tax_name_abbr="饮料",
                tax_code="*********",
                tax_rate="0.01",
                amount="1",
            )
        ],
    )
    yield workflow


@fixture
def client(gen_fresh_client):
    yield gen_fresh_client()


def make_queue_timeout(queue):
    with in_transaction():
        queue.running_at = queue.running_at - timedelta(hours=1)


def push_issued(workflow):
    issue_broker = IssueBroker.by_request(workflow.processing_invoice_request())
    issuing_time = datetime(2025, 1, 1)
    invoice_number = "24332000000213880845"
    issue_broker.issued(rpa_user_info, invoice_number, issuing_time)
    assert workflow.state == InvoiceWorkflow.State.INVOICING
    assert workflow.processing_invoice_request() is None
    assert workflow.processed_invoice_request() is not None
    queue = InvoiceIssueQueue.query.filter_by(request_id=workflow.processed_invoice_request().id).one()
    assert queue.state == InvoiceIssueQueue.State.RUNNING


def get_queue_query(workflow):
    return InvoiceIssueQueue.query.filter_by(request_id=workflow.latest_invoice_request().id)


@fixture
def mock_taxer_auth(mocker):
    from robot_processor.invoice.tax_bureau.models import CorporateTaxer

    yield mocker.patch.object(CorporateTaxer, "get_auth_info", return_value=dict())


@fixture
def mock_rpa_control_issue_invoice_success(requests_mock, mock_taxer_auth):
    yield requests_mock.post(
        rpa_control.config.endpoint + "/invoice/issue-blue", json={"succeed": True, "data": {"succeed": True}}
    )


@fixture
def mock_rpa_control_issue_invoice_failed(requests_mock, mock_taxer_auth):
    yield requests_mock.post(
        rpa_control.config.endpoint + "/invoice/issue-blue",
        json={"succeed": True, "data": {"succeed": False, "msg": "测试失败"}},
    )


@fixture
def mock_rpa_control_fetch_issue_success(requests_mock, mock_taxer_auth):
    yield requests_mock.post(
        rpa_control.config.endpoint + "/invoice/fetch-issued", json={"succeed": True, "data": {"succeed": True}}
    )


@fixture
def mock_rpa_control_fetch_issue_failed(requests_mock, mock_taxer_auth):
    yield requests_mock.post(
        rpa_control.config.endpoint + "/invoice/issue-blue",
        json={"succeed": False, "msg": "测试失败"},
    )


class TestDraft:
    @fixture
    def workflow(self, pb_workflow, client):
        context = WorkflowContext.Manual(org_id=client.org_id, pb_workflow=pb_workflow)
        create_result = WorkflowBuilder.create_workflow_with_context(context)
        assert create_result.workflow.state == InvoiceWorkflow.State.DRAFT
        yield create_result.workflow

    def test_transition_route(self):
        assert InvoiceWorkflowTransition.get_state_valid_transition_state(InvoiceWorkflow.State.DRAFT) == {
            InvoiceWorkflow.State.DRAFT,  # action=保存
            InvoiceWorkflow.State.IN_REVIEW,  # action=提交
            InvoiceWorkflow.State.PENDING_INVOICING,  # action=提交
            InvoiceWorkflow.State.CLOSED,  # action=关闭
        }

    def test__draft__save_draft(self, workflow):
        """DRAFT -> DRAFT (SAVE_DRAFT)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.SAVE_DRAFT)
        assert workflow.state == InvoiceWorkflow.State.DRAFT

    def test__in_review__submit(self, workflow, client):
        """DRAFT -> IN_REVIEW (SUBMIT)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.SUBMIT)
        update_config(
            client.org_id,
            ConfigKey.APPROVAL_RULESET,
            serialize(
                ApprovalRuleset(
                    routes=[
                        ApprovalRuleset.Routes(
                            ApprovalRuleset.Routes.Status.ENABLED,
                            mode=ApprovalRuleset.Routes.Mode.AUTO,
                            filter=dict(),
                            auto_mapper=ApprovalRuleset.Routes.AutoMapper(
                                action=ApprovalRuleset.Routes.AutoMapper.Action.AGREE
                            ),
                        )
                    ]
                )
            ),
        )
        workflow.submit_without_edit()
        assert workflow.state == InvoiceWorkflow.State.IN_REVIEW

    def test__pending_invoicing__submit(self, workflow, client):
        """DRAFT -> PENDING_INVOICING (SUBMIT)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.SUBMIT)
        workflow.submit_without_edit()
        assert workflow.state == InvoiceWorkflow.State.PENDING_INVOICING

    def test__closed__close(self, workflow, client):
        """DRAFT -> CLOSED (CLOSE)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.CLOSE)
        workflow.close()
        assert workflow.state == InvoiceWorkflow.State.CLOSED


class TestInReview:
    @fixture
    def workflow(self, pb_workflow, client):
        context = WorkflowContext.Manual(org_id=client.org_id, pb_workflow=pb_workflow)
        create_result = WorkflowBuilder.create_workflow_with_context(context)
        update_config(
            client.org_id,
            ConfigKey.APPROVAL_RULESET,
            serialize(
                ApprovalRuleset(
                    routes=[
                        ApprovalRuleset.Routes(
                            ApprovalRuleset.Routes.Status.ENABLED,
                            mode=ApprovalRuleset.Routes.Mode.AUTO,
                            filter=dict(),
                            auto_mapper=ApprovalRuleset.Routes.AutoMapper(
                                action=ApprovalRuleset.Routes.AutoMapper.Action.AGREE
                            ),
                        )
                    ]
                )
            ),
        )
        create_result.workflow.submit_without_edit()
        assert create_result.workflow.state == InvoiceWorkflow.State.IN_REVIEW
        yield create_result.workflow

    def test_transition_route(self):
        assert InvoiceWorkflowTransition.get_state_valid_transition_state(InvoiceWorkflow.State.IN_REVIEW) == {
            InvoiceWorkflow.State.DRAFT,  # action=撤回
            InvoiceWorkflow.State.PENDING_INVOICING,  # action=审核通过
            InvoiceWorkflow.State.REJECTED,  # action=审核拒绝
            InvoiceWorkflow.State.CLOSED,  # action=关闭
        }

    def test__draft__revoke(self, workflow):
        """IN_REVIEW -> DRAFT (REVOKE)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.REVOKE)
        workflow.revoke()
        assert workflow.state == InvoiceWorkflow.State.DRAFT

    def test__pending_invoicing__approve(self, workflow):
        """IN_REVIEW -> PENDING_INVOICING (APPROVE)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.APPROVE)
        workflow.approve()
        assert workflow.state == InvoiceWorkflow.State.PENDING_INVOICING

    def test__rejected__reject(self, workflow):
        """IN_REVIEW -> REJECTED (REJECT)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.REJECT)
        workflow.reject()
        assert workflow.state == InvoiceWorkflow.State.REJECTED

    def test__closed__close(self, workflow):
        """IN_REVIEW -> CLOSED (CLOSE)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.CLOSE)
        workflow.close()
        assert workflow.state == InvoiceWorkflow.State.CLOSED


class TestRejected:
    @fixture
    def workflow(self, pb_workflow, client):
        context = WorkflowContext.Manual(org_id=client.org_id, pb_workflow=pb_workflow)
        create_result = WorkflowBuilder.create_workflow_with_context(context)
        update_config(
            client.org_id,
            ConfigKey.APPROVAL_RULESET,
            serialize(
                ApprovalRuleset(
                    routes=[
                        ApprovalRuleset.Routes(
                            ApprovalRuleset.Routes.Status.ENABLED,
                            mode=ApprovalRuleset.Routes.Mode.AUTO,
                            filter=dict(),
                            auto_mapper=ApprovalRuleset.Routes.AutoMapper(
                                action=ApprovalRuleset.Routes.AutoMapper.Action.REJECT
                            ),
                        )
                    ]
                )
            ),
        )
        create_result.workflow.submit_without_edit()
        create_result.workflow.get_approval_broker().try_auto_approve()
        create_result.workflow.reject()
        assert create_result.workflow.state == InvoiceWorkflow.State.REJECTED
        yield create_result.workflow

    def test_transition_route(self):
        assert InvoiceWorkflowTransition.get_state_valid_transition_state(InvoiceWorkflow.State.REJECTED) == {
            InvoiceWorkflow.State.DRAFT,  # action=撤回
            InvoiceWorkflow.State.IN_REVIEW,  # action=重新提交
            InvoiceWorkflow.State.PENDING_INVOICING,  # action=重新提交
            InvoiceWorkflow.State.CLOSED,  # action=关闭
        }

    def test__draft__revoke(self, workflow):
        """REJECTED -> DRAFT (REVOKE)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.REVOKE)
        workflow.revoke()
        assert workflow.state == InvoiceWorkflow.State.DRAFT

    def test__draft__save_draft(self, workflow, pb_workflow):
        """REJECTED -> DRAFT (SAVE_DRAFT)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.SAVE_DRAFT)
        workflow.save(pb_workflow)
        assert workflow.state == InvoiceWorkflow.State.DRAFT

    def test__in_review__submit(self, workflow):
        """REJECTED -> IN_REVIEW (SUBMIT)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.SUBMIT)
        workflow.submit_without_edit()
        assert workflow.state == InvoiceWorkflow.State.IN_REVIEW

    def test__pending_invoicing__submit(self, workflow):
        """REJECTED -> PENDING_INVOICING (SUBMIT)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.SUBMIT)
        update_config(workflow.org_id, ConfigKey.APPROVAL_RULESET, {})
        workflow.submit_without_edit()
        assert workflow.state == InvoiceWorkflow.State.PENDING_INVOICING

    def test__closed__close(self, workflow):
        """REJECTED -> CLOSED (CLOSE)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.CLOSE)
        workflow.close()
        assert workflow.state == InvoiceWorkflow.State.CLOSED


class TestPendingInvoicing:
    @fixture(autouse=True)
    def teardown(self, client):
        yield
        InvoiceIssueQueue.query.filter_by(org_id=client.org_id).delete()

    @fixture
    def workflow(self, pb_workflow, client):
        context = WorkflowContext.Manual(org_id=client.org_id, pb_workflow=pb_workflow)
        create_result = WorkflowBuilder.create_workflow_with_context(context)
        create_result.workflow.submit_without_edit()
        assert create_result.workflow.state == InvoiceWorkflow.State.PENDING_INVOICING
        yield create_result.workflow

    def test_transition_route(self):
        assert InvoiceWorkflowTransition.get_state_valid_transition_state(InvoiceWorkflow.State.PENDING_INVOICING) == {
            InvoiceWorkflow.State.DRAFT,  # action=撤回
            InvoiceWorkflow.State.INVOICING,  # action=开票
            InvoiceWorkflow.State.INVOICE_FAILED,  # action=开票
            InvoiceWorkflow.State.CLOSED,  # action=关闭
        }

    def test__draft__revoke(self, workflow):
        """PENDING_INVOICING -> DRAFT (REVOKE)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.REVOKE)
        workflow.revoke()
        assert workflow.state == InvoiceWorkflow.State.DRAFT

    def test__invoicing_invoicing__issue(self, workflow, mock_rpa_control_issue_invoice_success):
        """PENDING_INVOICING -> INVOICING (ISSUE)"""
        issue_broker = IssueBroker.init_issue_blue(workflow.to_request_view(), [workflow], "zhejiang", "test").unwrap()
        issue_broker.do_issue()
        queue = InvoiceIssueQueue.get_running(workflow.org_id)
        assert workflow.state == InvoiceWorkflow.State.INVOICING
        assert workflow.processing_invoice_request().state == InvoiceRequest.State.INVOICING
        assert queue.state == InvoiceIssueQueue.State.RUNNING

    def test__invoicing_queued__issue(self, workflow, mock_rpa_control_issue_invoice_success):
        """PENDING_INVOICING -> QUEUED (ISSUE)"""
        with in_transaction() as session:
            running = InvoiceIssueQueue(
                org_id=workflow.org_id,
                task=InvoiceIssueQueue.Task.DO_ISSUE,
                state=InvoiceIssueQueue.State.RUNNING,
                request_id=999,
            )
            session.add(running)
        issue_broker = IssueBroker.init_issue_blue(workflow.to_request_view(), [workflow], "zhejiang", "test").unwrap()
        issue_broker.do_issue()
        queue = InvoiceIssueQueue.query.filter_by(request_id=workflow.processing_invoice_request().id).one()
        assert workflow.state == InvoiceWorkflow.State.INVOICING
        assert workflow.processing_invoice_request().state == InvoiceRequest.State.QUEUED
        assert queue.state == InvoiceIssueQueue.State.QUEUED

    def test__invoice_failed__issue(self, workflow, mock_rpa_control_issue_invoice_failed):
        """PENDING_INVOICING -> INVOICE_FAILED (ISSUE)"""
        issue_broker = IssueBroker.init_issue_blue(workflow.to_request_view(), [workflow], "zhejiang", "test").unwrap()
        issue_broker.do_issue()
        assert workflow.state == InvoiceWorkflow.State.INVOICE_FAILED
        assert workflow.latest_invoice_request().state == InvoiceRequest.State.FAILED
        assert InvoiceIssueQueue.query.filter_by(request_id=workflow.latest_invoice_request().id).count() == 0
        assert workflow.issue_failed_reason == "测试失败"

    def test__closed__close(self, workflow):
        """PENDING_INVOICING -> CLOSED (CLOSE)"""
        InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.CLOSE)
        workflow.close()
        assert workflow.state == InvoiceWorkflow.State.CLOSED
        assert workflow.latest_invoice_request() is None


class TestInvoicing:
    @fixture
    def workflow(self, pb_workflow, client, mock_rpa_control_issue_invoice_success):
        context = WorkflowContext.Manual(org_id=client.org_id, pb_workflow=pb_workflow)
        create_result = WorkflowBuilder.create_workflow_with_context(context)
        create_result.workflow.submit_without_edit()
        issue_broker = IssueBroker.init_issue_blue(
            create_result.workflow.to_request_view(), [create_result.workflow], "zhejiang", "test"
        ).unwrap()
        issue_broker.do_issue()
        queue = InvoiceIssueQueue.query.filter_by(
            request_id=create_result.workflow.processing_invoice_request().id
        ).one()
        assert create_result.workflow.state == InvoiceWorkflow.State.INVOICING
        assert create_result.workflow.processing_invoice_request().state == InvoiceRequest.State.INVOICING
        assert queue.state == InvoiceIssueQueue.State.RUNNING
        yield create_result.workflow

    def test_transition_route(self):
        assert InvoiceWorkflowTransition.get_state_valid_transition_state(InvoiceWorkflow.State.INVOICING) == {
            InvoiceWorkflow.State.INVOICED,  # action=开票成功
            InvoiceWorkflow.State.INVOICING_TIMEOUT,  # action=开票超时
            InvoiceWorkflow.State.INVOICED_WITHOUT_RECEIPT,  # action=开票成功
            InvoiceWorkflow.State.INVOICE_FAILED,  # action=开票失败
        }

    def test__invoiced_with_detail(self, workflow):
        issue_broker = IssueBroker.by_request(workflow.processing_invoice_request())
        issuing_time = datetime(2025, 1, 1)
        invoice_number = "24332000000213880845"
        issue_broker.issued(rpa_user_info, invoice_number, issuing_time)
        assert workflow.state == InvoiceWorkflow.State.INVOICING
        assert workflow.processing_invoice_request() is None
        assert workflow.processed_invoice_request() is not None
        queue = InvoiceIssueQueue.query.filter_by(request_id=workflow.processed_invoice_request().id).one()
        assert queue.state == InvoiceIssueQueue.State.RUNNING

        InvoiceIssueQueue.close(workflow.org_id, workflow.processed_invoice_request().id)
        issue_broker.issued_with_detail(
            pb_InvoiceIssued(total_amount_without_tax="0", total_tax_amount="0", issuing_amount="0")
        )
        assert workflow.state == InvoiceWorkflow.State.INVOICED
        assert InvoiceIssueQueue.query.filter_by(request_id=workflow.processed_invoice_request().id).count() == 0

    def test__invoiced_without_detail(self, workflow):
        queue_query = get_queue_query(workflow)
        push_issued(workflow)
        make_queue_timeout(queue_query.one())
        InvoiceIssueQueue.check_ttl(workflow.org_id)
        assert workflow.state == InvoiceWorkflow.State.INVOICED_WITHOUT_RECEIPT
        assert workflow.issue_failed_reason == "超时未响应"
        assert queue_query.count() == 0

    def test__invoice_timeout(self, workflow):
        queue_query = get_queue_query(workflow)
        make_queue_timeout(queue_query.one())
        InvoiceIssueQueue.check_ttl(workflow.org_id)
        assert workflow.state == InvoiceWorkflow.State.INVOICING_TIMEOUT
        assert workflow.issue_failed_reason == "开票超时"
        assert workflow.processing_invoice_request() is None
        assert workflow.latest_invoice_request().state == InvoiceRequest.State.FAILED
        assert workflow.latest_invoice_request().failed_reason == "开票超时"
        assert queue_query.count() == 0

    def test__invoice_failed(self, workflow):
        queue_query = get_queue_query(workflow)
        issue_broker = IssueBroker.by_request(workflow.processing_invoice_request())
        InvoiceIssueQueue.close(workflow.org_id, workflow.processing_invoice_request().id)
        issue_broker.issue_failed("测试失败")
        assert workflow.state == InvoiceWorkflow.State.INVOICE_FAILED
        assert workflow.issue_failed_reason == "测试失败"
        assert workflow.processing_invoice_request() is None
        assert workflow.latest_invoice_request().state == InvoiceRequest.State.FAILED
        assert workflow.latest_invoice_request().failed_reason == "测试失败"
        assert queue_query.count() == 0


class TestInvoicedWithoutReceipt:
    @fixture
    def workflow(self, pb_workflow, client, mock_rpa_control_issue_invoice_success):
        context = WorkflowContext.Manual(org_id=client.org_id, pb_workflow=pb_workflow)
        create_result = WorkflowBuilder.create_workflow_with_context(context)
        create_result.workflow.submit_without_edit()
        issue_broker = IssueBroker.init_issue_blue(
            create_result.workflow.to_request_view(), [create_result.workflow], "zhejiang", "test"
        ).unwrap()
        issue_broker.do_issue()
        push_issued(create_result.workflow)
        make_queue_timeout(get_queue_query(create_result.workflow).one())
        InvoiceIssueQueue.check_ttl(create_result.workflow.org_id)
        assert create_result.workflow.state == InvoiceWorkflow.State.INVOICED_WITHOUT_RECEIPT
        yield create_result.workflow

    def test_transition_route(self):
        assert InvoiceWorkflowTransition.get_state_valid_transition_state(
            InvoiceWorkflow.State.INVOICED_WITHOUT_RECEIPT
        ) == {
            InvoiceWorkflow.State.INVOICED,  # action=开票成功
            InvoiceWorkflow.State.INVOICED_WITHOUT_RECEIPT,  # action=开票失败
        }

    def test__invoiced(self, workflow, mock_rpa_control_fetch_issue_success):
        queue_query = get_queue_query(workflow)
        assert queue_query.count() == 0
        issue_broker = IssueBroker.by_request(workflow.processed_invoice_request())
        issue_broker.fetch_issue()
        assert workflow.state == InvoiceWorkflow.State.INVOICED_WITHOUT_RECEIPT

        InvoiceIssueQueue.close(workflow.org_id, workflow.processed_invoice_request().id)
        issue_broker.issued_with_detail(
            pb_InvoiceIssued(total_amount_without_tax="0", total_tax_amount="0", issuing_amount="0")
        )
        assert workflow.state == InvoiceWorkflow.State.INVOICED
        assert queue_query.count() == 0

    def test__invoiced_without_detail__failed(self, workflow, mock_rpa_control_issue_invoice_failed):
        queue_query = get_queue_query(workflow)
        assert queue_query.count() == 0
        issue_broker = IssueBroker.by_request(workflow.processed_invoice_request())
        issue_broker.fetch_issue()
        assert workflow.state == InvoiceWorkflow.State.INVOICED_WITHOUT_RECEIPT

    def test__invoice_without_detail__callback(self, workflow, mock_rpa_control_fetch_issue_success):
        queue_query = get_queue_query(workflow)
        assert queue_query.count() == 0
        issue_broker = IssueBroker.by_request(workflow.processed_invoice_request())
        issue_broker.fetch_issue()

        InvoiceIssueQueue.close(workflow.org_id, workflow.processed_invoice_request().id)
        issue_broker.fetch_issued_failed("未知错误")
        assert workflow.state == InvoiceWorkflow.State.INVOICED_WITHOUT_RECEIPT
        assert workflow.issue_failed_reason == "未知错误"
        assert queue_query.count() == 0

    def test__invoice_without_detail__timeout(self, workflow, mock_rpa_control_fetch_issue_success):
        queue_query = get_queue_query(workflow)
        assert queue_query.count() == 0
        issue_broker = IssueBroker.by_request(workflow.processed_invoice_request())
        issue_broker.fetch_issue()

        make_queue_timeout(get_queue_query(workflow).one())
        InvoiceIssueQueue.check_ttl(workflow.org_id)
        assert workflow.state == InvoiceWorkflow.State.INVOICED_WITHOUT_RECEIPT
