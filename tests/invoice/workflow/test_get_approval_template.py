from pytest import fixture


@fixture(autouse=True)
def setup(setup_auth):
    yield


def test查询(mock_context, testbed):
    from robot_processor.invoice.config_manager import ConfigKey
    from robot_processor.invoice.config_manager import ConfigManager

    config_manager = ConfigManager(testbed["org_id"])
    ruleset = config_manager.get(ConfigKey.APPROVAL_RULESET)
    assert len(ruleset.routes) == 2
