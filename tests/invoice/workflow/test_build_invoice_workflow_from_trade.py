from leyan_proto.digismart.robot_web.invoice.workflow_pb2 import BuildInvoiceWorkflowFromTradeQuery
from pytest import fixture
from pytest_mock import MockerFixture

from rpa.erp.wdt import RefundQueryResp


@fixture(autouse=True)
def setup(setup_auth):
    yield


@fixture
def mock_wdt_trade_query(testbed, mocker: MockerFixture):
    from rpa.erp.wdt import WdtClient

    yield mocker.patch.object(WdtClient, "trade_query", autospec=WdtClient.trade_query)


@fixture
def mock_wdt_refund_query_by_trade_no(testbed, mocker: MockerFixture):
    from rpa.erp.wdt import WdtClient

    yield mocker.patch.object(
        WdtClient,
        "refund_query_by_trade_no",
        autospec=WdtClient.refund_query_by_trade_no,
    )


def test_build_invoice_workflow_from_erp_trade(
    testbed,
    workflow_servicer,
    mock_context,
    mock_wdt_trade_query,
    mock_wdt_refund_query_by_trade_no,
):
    from rpa.erp.wdt import GoodsItem
    from rpa.erp.wdt import Trade
    from rpa.erp.wdt import TradeQueryResp

    mock_wdt_trade_query.return_value = TradeQueryResp(
        response=TradeQueryResp.Response(
            trades=[
                Trade(
                    trade_id=1,
                    trade_type=1,
                    shop_id=testbed["wdt_shop_id"],
                    consign_status=0,
                    goods_list=[
                        GoodsItem(
                            goods_name="测试商品-1",
                            spec_no="测试-sku-1",
                            goods_no="测试-spu-1",
                            num="1",
                            paid="100",
                        ),
                        GoodsItem(
                            goods_name="测试商品-2",
                            spec_no="测试-sku-2",
                            goods_no="测试-spu-2",
                            num="1",
                            paid="100",
                        ),
                    ],
                )
            ]
        )
    )
    mock_wdt_refund_query_by_trade_no.return_value = RefundQueryResp(response=RefundQueryResp.Response(refunds=[]))
    request = BuildInvoiceWorkflowFromTradeQuery(mode="erp", tid="ut")
    response = workflow_servicer.BuildInvoiceWorkflowFromTrade(request, mock_context)
    assert response.succeed, response.msg
