from pytest import fixture
from leyan_proto.digismart.robot.invoice import tax_bureau_pb2
from leyan_proto.digismart.robot_web.invoice.tax_bureau_pb2 import GetCorporateBody
from robot_processor.invoice.tax_bureau.models import Corporate, CorporateTaxer
from robot_processor.db import in_transaction


@fixture(autouse=True)
def setup(setup_auth, testbed):
    yield


@fixture
def teardown():
    corporate_id = None

    def set_corporate_id(new):
        nonlocal corporate_id
        corporate_id = new

    yield set_corporate_id

    if corporate_id is not None:
        Corporate.query.filter_by(id=corporate_id).delete()


def test测试企业信息不存在(tax_bureau_servicer, mock_context, teardown):
    request = GetCorporateBody()
    response = tax_bureau_servicer.GetCorporate(request, mock_context)
    assert not response.succeed
    assert response.msg == "未找到企业信息"


def test测试非当前租户权限校验(tax_bureau_servicer, mock_context, teardown):
    with in_transaction() as trx:
        corporate = Corporate(id=3, org_id=111)
        corporate.update(tax_bureau_pb2.Corporate.EditableView())
        trx.add(corporate)
        teardown(corporate.id)
    request = GetCorporateBody(id=corporate.id)
    response = tax_bureau_servicer.GetCorporate(request, mock_context)
    assert not response.succeed
    assert response.msg == "未找到企业信息，请确认登录账号是否正确"


def testcase(tax_bureau_servicer, mock_context):
    request = GetCorporateBody(id=1)
    response = tax_bureau_servicer.GetCorporate(request, mock_context)
    assert response.succeed
    corporate = response.data.corporate
    assert corporate.credit_id == "91310104MA1FR2RL99"
    assert corporate.name == "上海乐言科技股份有限公司"
    assert len(corporate.taxers) == 2
    assert corporate.taxers[0].account == "unittest-1"
    assert corporate.taxers[0].name == "*********1"
    assert corporate.taxers[0].phone == "138****0001"
    assert corporate.taxers[0].role == CorporateTaxer.Role.TAX_AGENT
