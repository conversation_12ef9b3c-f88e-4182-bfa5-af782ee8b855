import pytest

from robot_processor.business_order.job_action import JobAction
from robot_processor.business_order.models import JobPool
from robot_processor.enums import BusinessOrderStatus, JobProcessMark
from robot_processor.ext import db


@pytest.mark.parametrize("business_order_status", [BusinessOrderStatus.INIT, BusinessOrderStatus.RUNNING])
def test_business_order_complete(
        client, mock_job, mock_business_order, mock_form, business_order_status, job_pool_factory
):
    mock_job.business_order_id = mock_business_order.id
    mock_business_order.sid = client.shop.sid
    mock_business_order.status = business_order_status
    mock_business_order.form_id = mock_form.id
    db.session.commit()

    job_pool_factory.create(job_id=mock_job.id, assignee_user_id=client.assistant.user_id,
                            business_order_id=mock_job.business_order_id,
                            assignee_user_type=client.assistant.user_type)

    action = JobAction(job_id=mock_job.id)
    action.do_complete(assistant=client.assistant, operate_reason="ut")

    assert mock_job.process_mark == JobProcessMark.ACCEPT
    assert mock_business_order.status == BusinessOrderStatus.SUCCEED
    assert mock_business_order.extra_data["operate_action"] == "FINISH"
    assert mock_business_order.extra_data["operate_reason"] == "ut"
    assert mock_business_order.update_user == client.assistant.user_nick
    assert JobPool.query.filter_by(job_id=mock_job.id).count() == 0
