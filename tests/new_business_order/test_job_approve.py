from typing import Generator
from unittest.mock import PropertyMock

import pytest

from robot_processor.enums import StepType, SelectType, Creator, AssignStrategy, PlanWhenAssignException, \
    ApproveType, JobStatus, BusinessOrderStatus
from robot_processor.form.models import Form, Step
from robot_processor.business_order.models import BusinessOrder, Job, JobApprover
from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.ext import db
from robot_processor.business_order.job_action import JobAction


@pytest.fixture
def mock_operators() -> Generator[tuple[AccountDetailV2, ...], None, None]:
    yield (
        AccountDetailV2(
            user_id=1,
            user_type=Creator.ASSISTANT.value,
            user_nick="平台客服 - 水曜日"
        ),
        AccountDetailV2(
            user_id=2,
            user_type=Creator.ASSISTANT.value,
            user_nick="平台客服 - 咩咩"
        ),
        AccountDetailV2(
            user_id=3,
            user_type=Creator.LEYAN.value,
            user_nick="乐言账号 - 飒旦"
        ),
        AccountDetailV2(
            user_id=4,
            user_type=Creator.LEYAN.value,
            user_nick="乐言账号 - 图哥"
        ),
        AccountDetailV2(
            user_id=0,
            user_type=Creator.LDAP.value,
            user_nick="LDAP - 傻哥"
        ),
        AccountDetailV2(
            user_id=0,
            user_type=Creator.RPA.value,
            user_nick="RPA - 洛蒂"
        ),
    )


@pytest.fixture
def mock_form_and_steps(
    form_factory, step_factory, mock_operators
) -> Generator[tuple[Form, tuple[Step, ...]], None, None]:
    (
        first_assistant, second_assistant,
        first_leyan_account, second_leyan_account,
        first_ldap_account, rpa
    ) = mock_operators

    form: Form = form_factory.create(
        co_edit=True
    )
    steps: tuple[Step, ...] = step_factory.create_batch(
        3,
        form_id=form.id,
        is_dirty=False,
    )
    (
        begin_step, first_human_step, approve_step,
    ) = steps

    # “起始步骤”
    begin_step.step_type = StepType.begin
    begin_step.prev_step_ids = []
    begin_step.next_step_ids = [first_human_step.step_uuid]
    begin_step.update_raw_step()
    # 第 1 个“人工步骤”
    first_human_step.step_type = StepType.human
    first_human_step.prev_step_ids = [begin_step.step_uuid]
    first_human_step.next_step_ids = [approve_step.step_uuid]
    first_human_step.update_raw_step()
    # 第 3 步为“审批节点”
    approve_step.step_type = StepType.exclusive_gateway
    approve_step.prev_step_ids = [first_human_step.step_uuid]
    approve_step.next_step_ids = []
    approve_step.set_assistants_v2({
      "assignee_groups": [],
      "details": [
        first_leyan_account.dict(),
        second_assistant.dict()
      ],
      "select_type": SelectType.part.value,
      "online_only": False,
      "assign_strategy": AssignStrategy.AUTO.value,
      "assign_account_exception": PlanWhenAssignException.ENTER_EXCEPTION_POOL.value,
      "approve_type": ApproveType.PARALLEL_MULTI_TO_SIGN.value,
      "channel_accounts": [],
      "leyan_accounts": []
    })
    approve_step.update_raw_step()

    form.snapshot()
    db.session.commit()

    yield form, steps


@pytest.fixture
def mock_business_order_and_jobs(
    client, business_order_factory, job_factory, job_approver_factory,
    mock_form_and_steps, mock_operators,
) -> Generator[tuple[BusinessOrder, tuple[Job, ...]], None, None]:
    (
        first_assistant, second_assistant,
        first_leyan_account, second_leyan_account,
        first_ldap_account, rpa
    ) = mock_operators

    form, steps = mock_form_and_steps
    (
        begin_step, first_human_step, approve_step,
    ) = steps

    business_order: BusinessOrder = business_order_factory.create(
        sid=client.shop.sid,
        form_id=form.id,
        form_version_id=form.versions.first().id,
    )
    jobs: tuple[Job, ...] = job_factory.create_batch(
        3,
        business_order_id=business_order.id,
        status=JobStatus.SUCCEED,
    )

    for (job, step) in zip(jobs, steps):
        job.step_id = step.id
        job.step_uuid = step.step_uuid

    (
        begin_job, first_human_job, approve_job,
    ) = jobs

    begin_job.set_assignee_assistant(None)
    first_human_job.set_assignee_assistant(first_assistant)
    approve_job.set_assignee_assistant(None)
    approve_job.status = JobStatus.PENDING
    job_approvers: tuple[JobApprover, ...] = job_approver_factory.create_batch(
        2,
        job_id=approve_job.id,
        step_uuid=approve_step.step_uuid,
        user_id=first_leyan_account.user_id,
        is_valid=True,
        is_approved=False
    )
    (first_approver, second_approver) = job_approvers
    second_approver.user_id = second_leyan_account.user_id

    business_order.current_job_id = approve_job.id
    business_order.job_history = [job.id for job in jobs]
    business_order.status = BusinessOrderStatus.PENDING
    db.session.commit()

    yield business_order, jobs


class TestJobApprove:
    def test_approve_success(self, mocker, mock_business_order_and_jobs, mock_operators):
        (
            first_assistant, second_assistant,
            first_leyan_account, second_leyan_account,
            first_ldap_account, rpa
        ) = mock_operators

        business_order, (begin_job, first_human_job, approve_job,) = mock_business_order_and_jobs

        mocker.patch(
            "robot_processor.assistant.schema.AssistantV2.get_latest_assignee_account_details",
            return_value=[first_leyan_account, second_leyan_account]
        )

        JobAction(job_id=approve_job.id).do_approve(
            assistant=first_leyan_account,
            operate_reason="test"
        )

        assert approve_job.status == JobStatus.PENDING
        assert business_order.status == BusinessOrderStatus.PENDING

        first_approver: JobApprover = JobApprover.query.filter(
            JobApprover.job_id == approve_job.id,
            JobApprover.user_id == first_leyan_account.user_id,
        ).first()
        assert first_approver is not None
        assert first_approver.is_approved

        mocker.patch(
            "robot_processor.assistant.schema.AccountDetailV2.bound_feisuo_user",
            new_callable=PropertyMock(return_value=second_leyan_account)
        )

        JobAction(job_id=approve_job.id).do_approve(
            assistant=second_assistant,
            operate_reason="test"
        )

        approver_count = JobApprover.query.filter(
            JobApprover.job_id == approve_job.id
        ).count()
        assert approver_count == 0

        assert approve_job.status == JobStatus.SUCCEED


class TestJobOverrule:
    def test_overrule_success(self, mock_business_order_and_jobs, mock_operators):
        (
            first_assistant, second_assistant,
            first_leyan_account, second_leyan_account,
            first_ldap_account, rpa
        ) = mock_operators

        business_order, (begin_job, first_human_job, approve_job,) = mock_business_order_and_jobs
        JobAction(job_id=approve_job.id).do_overrule(
            assistant=first_assistant,
            operate_reason="test",
            specified_reject_id=first_human_job.id,
        )

        assert approve_job.status == JobStatus.INIT
        assert business_order.status == BusinessOrderStatus.PENDING
        assert business_order.current_job_id == first_human_job.id
        assert len(approve_job.extra_data_wrapper.get_archived_operated_approver_ids()) == 0
