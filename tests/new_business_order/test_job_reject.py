import pytest

from robot_processor.assistant.schema import AccountDetailV2, Creator
from robot_processor.business_order.job_action import JobAction
from robot_processor.enums import JobStatus, StepType, JobProcessMark, PlanWhenAssignException, \
    BusinessOrderStatus
from robot_processor.error.errors import JobStepTypeUnSupportError, JobStatusUnSupportError, JobDeletedError
from robot_processor.ext import db


@pytest.fixture
def create_job(client, step_factory, job_factory, mock_form, mock_business_order, db):
    def create(job_type, prev_job_type, prev_job_can_retry, job_status):
        prev_job_step = step_factory.create(step_type=prev_job_type, form_id=mock_form.id,
                                            can_retry=prev_job_can_retry)
        cur_step = step_factory.create(step_type=job_type, form_id=mock_form.id,
                                       prev_step_ids=[prev_job_step.id])
        prev_job = job_factory.create(step_id=prev_job_step.id, business_order_id=mock_business_order.id,
                                      step_uuid=prev_job_step.step_uuid, status=JobStatus.SUCCEED)
        job = job_factory.create(step_id=cur_step.id, business_order_id=mock_business_order.id,
                                 step_uuid=cur_step.step_uuid, status=job_status)

        begin_step = step_factory.create(
            form_id=mock_form.id,
            step_type=StepType.begin,
            can_retry=True,
            is_dirty=False,
        )
        begin_job = job_factory.create2(
            begin_step, mock_business_order, status=JobStatus.SUCCEED
        )
        begin_step.prev_step_ids = []
        begin_step.next_step_ids = [prev_job_step.step_uuid]
        prev_job_step.prev_step_ids = [begin_step.step_uuid]
        prev_job_step.next_step_ids = [cur_step.step_uuid]
        cur_step.prev_step_ids = [prev_job_step.step_uuid]

        mock_business_order.current_job_id = job.id
        mock_business_order.job_history = [begin_job.id, prev_job.id, job.id]
        mock_business_order.job_road = [[begin_job.id, prev_job.id, job.id]]
        mock_form.sid = client.sid
        mock_business_order.sid = client.sid
        mock_business_order.form_id = mock_form.id
        db.session.commit()
        return job
    return create


@pytest.fixture
def mock_bo_and_jobs(
    client,
    job_factory, business_order_factory, step_factory, form_factory,
    step_skip_factory, step_retry_factory
):
    rpa_operator = AccountDetailV2(
        user_id=0,
        user_type=Creator.RPA.value,
        user_nick="RPA - 洛蒂"
    )

    platform_assistant = AccountDetailV2(
        user_id=1,
        user_type=Creator.ASSISTANT.value,
        user_nick="平台客服 - 水曜日"
    )

    form = form_factory.create(
        co_edit=True
    )

    steps = step_factory.create_batch(
        4,
        form_id=form.id,
        is_dirty=False,
    )

    (
        first_step, second_step, third_step,
        fourth_step,
    ) = steps

    # “起始步骤”
    first_step.step_type = StepType.begin
    first_step.prev_step_ids = []
    first_step.next_step_ids = [second_step.step_uuid]
    first_step.update_raw_step()
    # 第 1 个“人工步骤”
    second_step.step_type = StepType.human
    second_step.prev_step_ids = [first_step.step_uuid]
    second_step.next_step_ids = [third_step.step_uuid]
    second_step.update_raw_step()
    # 第 3 步为“自动化步骤”
    third_step.step_type = StepType.auto
    third_step.prev_step_ids = [second_step.step_uuid]
    third_step.next_step_ids = [fourth_step.step_uuid]
    third_step.update_raw_step()
    # 第 4 步为“自动化步骤”
    fourth_step.step_type = StepType.auto
    fourth_step.prev_step_ids = [third_step.step_uuid]
    fourth_step.next_step_ids = []
    fourth_step.update_raw_step()

    form.snapshot()

    business_order = business_order_factory.create(
        sid=client.shop.sid,
        form_id=form.id,
        form_version_id=form.versions.first().id,
    )
    jobs = job_factory.create_batch(
        4,
        business_order_id=business_order.id,
        status=JobStatus.SUCCEED,
    )

    for (job, step) in zip(jobs, steps):
        job.step_id = step.id
        job.step_uuid = step.step_uuid

    (
        first_job, second_job, third_job,
        fourth_job,
    ) = jobs

    # “初始步骤”不带有执行客服。
    first_job.set_assignee_assistant(None)
    first_job.process_mark = JobProcessMark.ACCEPT
    # 第一个 “人工步骤”。
    second_job.set_assignee_assistant(platform_assistant)
    second_job.process_mark = JobProcessMark.ACCEPT
    # 第 3 步为自动化步骤，但是执行时被跳过。
    third_job.set_assignee_assistant(rpa_operator)
    third_job.process_mark = JobProcessMark.SKIP
    step_skip_factory.create(step_uuid=third_step.step_uuid, can_skip=True)
    step_retry_factory.create(step_uuid=third_step.step_uuid, can_retry=True)
    # 第 4 步为自动化步骤。
    fourth_job.set_assignee_assistant(rpa_operator)
    fourth_job.process_mark = JobProcessMark.ACCEPT
    step_retry_factory.create(step_uuid=fourth_step.step_uuid, can_retry=True)

    business_order.current_job_id = fourth_job.id
    business_order.job_history = [job.id for job in jobs]
    business_order.status = BusinessOrderStatus.SUCCEED
    db.session.commit()

    yield business_order, jobs


class TestRejectChecker:
    expect_job_status = [JobStatus.PENDING]
    not_support_job_status = [JobStatus.INIT, JobStatus.RUNNING, JobStatus.FAILED]

    not_support_job_type = [StepType.auto.value, StepType.exclusive_gateway.value]
    expect_job_type = [StepType.human.value]

    expect_prev_job_type = [StepType.human.value]
    not_support_prev_job_type = [StepType.auto.value, StepType.exclusive_gateway.value]

    expect_prev_job_retry_config = [True]
    not_support_prev_job_retry_config = [False]

    @pytest.mark.parametrize("job_status", not_support_job_status)
    def test_not_support_job_status(self, create_job, job_status, mock_form):
        job = create_job(job_status=job_status, job_type=None, prev_job_type=None, prev_job_can_retry=None)

        with pytest.raises(JobStatusUnSupportError):
            JobAction(job_id=job.id).check_job_can_reject(job.prev.id)

    @pytest.mark.parametrize("job_status", expect_job_status)
    @pytest.mark.parametrize("job_type", not_support_job_type)
    def test_not_support_job_type(self, mocker, create_job, job_status, job_type):
        job = create_job(job_status=job_status, job_type=job_type, prev_job_type=None, prev_job_can_retry=None)

        with pytest.raises(JobStepTypeUnSupportError):
            JobAction(job_id=job.id).check_job_can_reject(reject_to_job_id=job.prev.id)

    @pytest.mark.parametrize("job_status", expect_job_status)
    @pytest.mark.parametrize("job_type", expect_job_type)
    @pytest.mark.parametrize("prev_job_type", expect_prev_job_type)
    @pytest.mark.parametrize("prev_job_retry_config", not_support_prev_job_retry_config)
    def test_prev_job_not_support_retry(self, client, create_job, job_status,
                                        job_type, prev_job_retry_config, prev_job_type):
        job = create_job(
            job_status=job_status, job_type=job_type,
            prev_job_type=prev_job_type, prev_job_can_retry=prev_job_retry_config,
        )

        job.prev.step.step_type = StepType.begin

        with pytest.raises(JobStepTypeUnSupportError):
            JobAction(job_id=job.id).check_job_can_reject(reject_to_job_id=job.prev.id)

    @pytest.mark.parametrize("job_status", expect_job_status)
    @pytest.mark.parametrize("job_type", expect_job_type)
    @pytest.mark.parametrize("prev_job_type", expect_prev_job_type)
    @pytest.mark.parametrize("prev_job_retry_config", expect_prev_job_retry_config)
    def test_prev_job_step_deleted(self, mock_business_order, client, create_job, db, job_status, job_type,
                                   prev_job_retry_config, prev_job_type):
        job = create_job(
            job_status=job_status, job_type=job_type,
            prev_job_type=prev_job_type, prev_job_can_retry=prev_job_retry_config,
        )
        job.prev.step.deleted = True
        db.session.commit()
        with pytest.raises(JobDeletedError):
            JobAction(job_id=job.id).check_job_can_reject(reject_to_job_id=job.prev.id)

        mock_business_order.current_job_id = job.prev.id
        with pytest.raises(JobStepTypeUnSupportError):
            JobAction(job_id=job.id).check_job_can_reject(reject_to_job_id=job.prev.id)


class TestRejectAction:

    @pytest.mark.parametrize("job_status", TestRejectChecker.expect_job_status)
    @pytest.mark.parametrize("job_type", TestRejectChecker.expect_job_type)
    @pytest.mark.parametrize("prev_job_type", TestRejectChecker.expect_prev_job_type)
    @pytest.mark.parametrize("prev_job_retry_config", TestRejectChecker.expect_prev_job_retry_config)
    def test_job_reject_without_assignee(
            self, client, create_job, job_status, job_type, prev_job_retry_config, prev_job_type):
        job = create_job(
            job_status=job_status, job_type=job_type,
            prev_job_type=prev_job_type, prev_job_can_retry=prev_job_retry_config,
        )
        _operate_reason = "ut"

        job.prev.set_assignee_assistant(client.assistant)
        JobAction(job_id=job.id).do_reject(assistant=client.assistant, operate_reason=_operate_reason,
                                           specified_reject_id=job.prev.id)

        assert job.status == JobStatus.INIT
        assert job.process_mark == JobProcessMark.REJECT
        assert job.prev.status == JobStatus.PENDING
        assert job.business_order.current_job_id == job.prev.id
        assert job.business_order.extra_data["operate_action"] == "REJECT"
        assert job.business_order.extra_data["operate_reason"] == _operate_reason
        assert job.business_order.update_user == client.assistant.user_nick

    @pytest.mark.parametrize("job_status", TestRejectChecker.expect_job_status)
    @pytest.mark.parametrize("job_type", TestRejectChecker.expect_job_type)
    @pytest.mark.parametrize("prev_job_type", TestRejectChecker.expect_prev_job_type)
    @pytest.mark.parametrize("prev_job_retry_config", TestRejectChecker.expect_prev_job_retry_config)
    def test_job_reject_with_assignee(
            self, client, create_job, db,
            job_status, job_type, prev_job_retry_config, prev_job_type,
    ):
        job = create_job(
            job_status=job_status, job_type=job_type,
            prev_job_type=prev_job_type, prev_job_can_retry=prev_job_retry_config,
        )
        db.session.commit()
        _operate_reason = "ut"

        # 设置前一个任务的指派客服
        job.prev.set_assignee_assistant(client.another_assistant)
        JobAction(job_id=job.id).do_reject(
            assistant=client.assistant,
            operate_reason=_operate_reason,
            specified_reject_id=job.prev.id
        )

        assert job.status == JobStatus.INIT
        assert job.process_mark == JobProcessMark.REJECT
        assert job.prev.status == JobStatus.PENDING
        assert job.prev.assignee_user_id == client.another_assistant.user_id
        assert job.prev.assignee == client.another_assistant.user_nick
        assert job.prev.assignee_type == client.another_assistant.user_type
        assert job.business_order.current_job_id == job.prev.id
        assert job.business_order.extra_data["operate_action"] == "REJECT"
        assert job.business_order.extra_data["operate_reason"] == _operate_reason
        assert job.business_order.update_user == client.assistant.user_nick

    @pytest.mark.parametrize("job_status", TestRejectChecker.expect_job_status)
    @pytest.mark.parametrize("job_type", TestRejectChecker.expect_job_type)
    @pytest.mark.parametrize("prev_job_type", TestRejectChecker.expect_prev_job_type)
    @pytest.mark.parametrize("prev_job_retry_config", TestRejectChecker.expect_prev_job_retry_config)
    def test_job_reject_exception(self, client, create_job, job_status, job_type, prev_job_retry_config, prev_job_type):
        from robot_processor.enums import BusinessOrderStatus

        job = create_job(
            job_status=job_status, job_type=job_type,
            prev_job_type=prev_job_type, prev_job_can_retry=prev_job_retry_config,
        )
        _operate_reason = "ut"

        JobAction(job_id=job.id).do_reject(
            assistant=client.assistant,
            operate_reason=_operate_reason,
            specified_reject_id=job.prev.id,
        )
        assert job.business_order.status == BusinessOrderStatus.IN_EXCEPTION
        assert job.business_order.current_job_id == job.prev.id

    @pytest.mark.parametrize("job_status", TestRejectChecker.expect_job_status)
    @pytest.mark.parametrize("job_type", TestRejectChecker.expect_job_type)
    @pytest.mark.parametrize("prev_job_type", TestRejectChecker.expect_prev_job_type)
    @pytest.mark.parametrize("prev_job_retry_config", TestRejectChecker.expect_prev_job_retry_config)
    def test_job_reject_with_re_assign(
            self, mocker, client, create_job, job_status, job_type, prev_job_retry_config, prev_job_type
    ):
        job = create_job(job_status=job_status, job_type=job_type,
                         prev_job_type=prev_job_type, prev_job_can_retry=prev_job_retry_config)
        _operate_reason = "ut"

        JobAction(job_id=job.id).do_reject(
            assistant=client.assistant,
            operate_reason=_operate_reason,
            specified_reject_id=job.prev.id,
            plan_when_assign_exception=PlanWhenAssignException.RE_ASSIGN
        )

        assert job.status == JobStatus.INIT
        assert job.process_mark == JobProcessMark.REJECT
        assert job.prev.status == JobStatus.PENDING
        assert job.prev.assignee is not None
        assert job.business_order.current_job_id == job.prev.id
        assert job.business_order.extra_data["operate_action"] == "REJECT"
        assert job.business_order.extra_data["operate_reason"] == _operate_reason
        assert job.business_order.update_user == client.assistant.user_nick

    @pytest.mark.parametrize("job_status", TestRejectChecker.expect_job_status)
    @pytest.mark.parametrize("job_type", TestRejectChecker.expect_job_type)
    @pytest.mark.parametrize("prev_job_type", TestRejectChecker.expect_prev_job_type)
    @pytest.mark.parametrize("prev_job_retry_config", TestRejectChecker.expect_prev_job_retry_config)
    def test_job_reject_when_bo_succeed(
            self, mocker, client, create_job, job_status, job_type, prev_job_retry_config, prev_job_type
    ):
        job = create_job(job_status=job_status, job_type=job_type,
                         prev_job_type=prev_job_type, prev_job_can_retry=prev_job_retry_config)
        _operate_reason = "ut"

        job.business_order.status = BusinessOrderStatus.SUCCEED
        job.status = JobStatus.SUCCEED

        JobAction(job_id=job.id).reject(
            operate_assistant=client.assistant,
            operate_reason=_operate_reason,
            specified_reject_id=job.id,
            pofa=PlanWhenAssignException.RE_ASSIGN
        )

        assert job.status == JobStatus.PENDING
        assert job.process_mark == JobProcessMark.REJECT
        assert job.assignee is not None
        assert job.business_order.current_job_id == job.id
        assert job.business_order.extra_data["operate_action"] == "REJECT"
        assert job.business_order.extra_data["operate_reason"] == _operate_reason
        assert job.business_order.update_user == client.assistant.user_nick

    def test_reject_to_human_job(
        self, mocker, mock_bo_and_jobs
    ):
        business_order, (
            first_job, second_job, third_job,
            fourth_job,
        ) = mock_bo_and_jobs

        job_action = JobAction(business_order.current_job_id)

        job_action.reject(
            operate_assistant=AccountDetailV2(
                user_id=1,
                user_type=Creator.ASSISTANT.value,
                user_nick="平台客服 - 水曜日"
            ),
            operate_reason="测试不再循环",
            specified_reject_id=second_job.id
        )

        assert business_order.current_job_id == second_job.id
        assert business_order.status == BusinessOrderStatus.PENDING
        assert fourth_job.process_mark == JobProcessMark.REJECT
        assert fourth_job.status == JobStatus.INIT
        assert third_job.process_mark == JobProcessMark.REJECT
        assert third_job.status == JobStatus.INIT
        assert second_job.process_mark == JobProcessMark.REJECT
        assert second_job.status == JobStatus.PENDING

    def test_reject_to_last_auto_job(
        self, mocker, mock_bo_and_jobs
    ):
        business_order, (
            first_job, second_job, third_job,
            fourth_job,
        ) = mock_bo_and_jobs

        job_action = JobAction(business_order.current_job_id)

        mocker.patch(
            "robot_processor.business_order.tasks.execute_job",
            return_value=None
        )

        job_action.reject(
            operate_assistant=AccountDetailV2(
                user_id=1,
                user_type=Creator.ASSISTANT.value,
                user_nick="平台客服 - 水曜日"
            ),
            operate_reason="回到最后一步自动化任务",
            specified_reject_id=fourth_job.id
        )

        assert business_order.current_job_id == fourth_job.id
        assert business_order.status == BusinessOrderStatus.RUNNING
