import pytest
from pytest import fixture

from robot_processor.business_order import job_action
from robot_processor.business_order.job_action import JobAction
from robot_processor.enums import JobStatus, JobType, PaymentStatus, BusinessOrderStatus
from robot_processor.error.errors import AlipayBusinessOrderStatusNotSupportError, JobStatusUnSupportError
from robot_processor.ext import db


@fixture
def create_job(client, mock_business_order, mock_form, job_factory):
    def create(job_status):
        mock_job = job_factory.create(business_order_id=mock_business_order.id, status=job_status)
        mock_business_order.sid = client.sid
        mock_business_order.current_job_id = mock_job.id
        mock_business_order.form_id = mock_form.id
        mock_business_order.job_history = [mock_job.id]
        mock_business_order.job_road = [mock_business_order.job_history]
        db.session.commit()
        return mock_job
    return create


class TestDeleteChecker:
    expect_job_status = [JobStatus.SUCCEED, JobStatus.INIT, JobStatus.FAILED, JobStatus.PENDING]
    not_support_job_status = [JobStatus.RUNNING]

    @pytest.mark.parametrize("job_status", not_support_job_status)
    def test_job_not_support_status(self, create_job, job_status):
        job = create_job(job_status)
        with pytest.raises(JobStatusUnSupportError):
            job_action.JobAction(job_id=job.id).check_job_can_delete()

    @pytest.mark.parametrize("job_status", expect_job_status)
    def test_check_pass(self, create_job, job_status):
        job = create_job(job_status)
        assert job_action.JobAction(job_id=job.id).check_job_can_delete() is None


@fixture
def create_alipay_job(client, step_factory, mock_form, job_factory, mock_business_order, rpa):
    def create(task_type, job_status):
        mock_form.sid = client.sid
        task = rpa(task=task_type, org_id=client.org_id)
        step = step_factory(form_id=mock_form.id, data={'rpa_id': task.id})

        mock_business_order.form_id = mock_form.id
        mock_business_order.sid = client.sid
        job = job_factory.create(step_id=step.id, business_order_id=mock_business_order.id,
                                 step_uuid=step.step_uuid, status=job_status)
        mock_business_order.current_job_id = job.id
        mock_business_order.job_history = [job.id]
        mock_business_order.job_road = [mock_business_order.job_history]
        db.session.commit()
        return job
    return create


class TestAlipayDeleteChecker:
    expect_job_status = [JobStatus.SUCCEED, JobStatus.INIT, JobStatus.FAILED, JobStatus.PENDING]
    expect_auto_job_task_type = [JobType.ALIPAY.value, JobType.CONFIRM_ALIPAY.value]
    not_support_alipay_status = (PaymentStatus.PAYING, PaymentStatus.PAY_FINISH)
    expect_alipay_status = sorted(set(PaymentStatus).difference(not_support_alipay_status))

    @pytest.mark.parametrize("job_status", expect_job_status)
    @pytest.mark.parametrize("alipay_job_task_type", expect_auto_job_task_type)
    @pytest.mark.parametrize("alipay_status", expect_alipay_status)
    def test_check_pass_delete_confirm_alipay_job(
            self, client, create_alipay_job, job_status, alipay_job_task_type, alipay_status, mock_get_transfer
    ):
        mock_job = create_alipay_job(job_status=job_status, task_type=alipay_job_task_type)
        mock_get_transfer.return_value = {"transfer": {"status": alipay_status}}
        assert job_action.JobAction(job_id=mock_job.id).check_job_can_delete() is None

    @pytest.mark.parametrize("job_status", expect_job_status)
    @pytest.mark.parametrize("alipay_job_task_type", expect_auto_job_task_type)
    @pytest.mark.parametrize("alipay_status", not_support_alipay_status)
    def test_fail_delete_confirm_alipay_job(self, create_alipay_job, job_status, alipay_job_task_type,
                                            alipay_status, mock_get_transfer):
        mock_job = create_alipay_job(job_status=job_status, task_type=alipay_job_task_type)
        mock_get_transfer.return_value = {"transfer": {"status": alipay_status}}
        with pytest.raises(AlipayBusinessOrderStatusNotSupportError):
            job_action.JobAction(job_id=mock_job.id).check_job_can_delete()


class TestDeleteAction:

    @pytest.mark.parametrize("job_status", TestDeleteChecker.expect_job_status)
    def test_delete_success(self, client, create_job, job_status):
        job = create_job(job_status=JobStatus.INIT)
        job.business_order.status = BusinessOrderStatus.PENDING
        job_action = JobAction(job_id=job.id)

        job_action.delete(operate_assistant=client.assistant, operate_reason="ut")
        assert job.business_order.deleted is True

    @pytest.mark.parametrize("job_status", TestAlipayDeleteChecker.expect_job_status)
    @pytest.mark.parametrize("alipay_job_task_type", TestAlipayDeleteChecker.expect_auto_job_task_type)
    @pytest.mark.parametrize("alipay_status", TestAlipayDeleteChecker.expect_alipay_status)
    def test_delete_confirm_alipay_job(
            self, client, create_alipay_job, job_status, alipay_job_task_type, alipay_status, mock_get_transfer,
    ):
        job = create_alipay_job(job_status=job_status, task_type=alipay_job_task_type)
        business_order = job.business_order
        business_order.status = BusinessOrderStatus.TO_BO_SUBMITTED
        job_action = JobAction(job_id=job.id)
        mock_get_transfer.return_value = {"transfer": {"status": alipay_status}}
        job_action.delete(operate_assistant=client.assistant, operate_reason="ut")
        assert job.business_order.deleted is True
