def test_handle_business_order_updated_at(client, mocker):
    from robot_processor.ext import db
    from robot_processor.business_order.models import BusinessOrder, BusinessOrderStatus

    # 创建测试用工单。
    bo = BusinessOrder(status=BusinessOrderStatus.INIT, updated_at=1)
    db.session.add(bo)
    db.session.commit()

    # 测试工单状态从 "非成功" 变为 "成功" 的场景。
    old_bo_update_at = bo.updated_at
    mocker.patch('time.time', return_value=1001)
    bo.set_status(BusinessOrderStatus.SUCCEED)
    db.session.commit()
    assert bo.updated_at != old_bo_update_at
    assert bo.updated_at == 1001

    # 测试工单状态为 "成功" 时，修改其他属性的场景。
    old_bo_updated_at = bo.updated_at
    bo.sid = "sid"
    bo.set_updated_at(1188)
    db.session.commit()
    assert bo.updated_at == old_bo_updated_at
    assert bo.updated_at != 1188

    # 测试工单状态由 "成功" 变为 "非成功" 的场景。
    old_bo_updated_at = bo.updated_at
    mocker.patch('time.time', return_value=85857)
    bo.set_status(BusinessOrderStatus.RUNNING)
    bo.message_ts = 1
    db.session.commit()
    assert bo.updated_at != old_bo_updated_at
    assert bo.updated_at == 85857

    # 测试工单采用传入的时间作为最近更新时间。
    old_bo_updated_at = bo.updated_at
    bo.message_ts = 2
    bo.set_updated_at(operate_ts=9999)
    db.session.commit()
    assert bo.updated_at != old_bo_updated_at
    assert bo.updated_at == 9999


def test_job_update_process_mark(client, mock_form_with_step, mocker):
    from robot_processor.ext import db
    from robot_processor.business_order.models import BusinessOrder, Job, AccountDetailV2
    from robot_processor.enums import JobProcessMark, BusinessOrderStatus, Creator

    bo = BusinessOrder(
        extra_data={"operate_reason": "test"},
        updator_type=Creator.ASSISTANT.value,
        updator_id=1,
        update_user="test_user",
    )
    db.session.add(bo)
    db.session.commit()
    job = Job(business_order_id=bo.id)
    db.session.add(job)
    db.session.commit()

    account = AccountDetailV2(
        user_type=Creator.LEYAN.value,
        user_nick="test_leyan_user",
        user_id=2,
    )
    mocker.patch('time.time', return_value=1188)
    job.update_process_mark_and_record_action(
        process_mark=JobProcessMark.PAUSE,
        assistant=account,
        operate_reason="test",
    )
    db.session.commit()

    assert job.process_mark == JobProcessMark.PAUSE
    bo_extra_data = (bo.extra_data or {}).copy()
    assert bo_extra_data.get("operate_action") == JobProcessMark.PAUSE.name
    assert bo_extra_data.get("operate_reason") == "test"
    assert bo.updator_id == 2
    assert bo.updator_type == Creator.LEYAN.value
    assert bo.update_user == "test_leyan_user"
    assert bo.updated_at == 1188

    old_assistant = bo.get_updator_info()

    # is_keep_business_order_updator_info 为 True
    job.update_process_mark_and_record_action(
        process_mark=JobProcessMark.CLOSE,
        assistant=AccountDetailV2(
            user_type=Creator.RPA.value,
            user_nick="test_rpa_user",
            user_id=3,
        ),
        operate_reason="no reason",
        is_keep_business_order_updator_info=True,
    )
    db.session.commit()
    assert job.process_mark == JobProcessMark.CLOSE
    bo_extra_data = (bo.extra_data or {}).copy()
    assert bo_extra_data.get("operate_action") == JobProcessMark.CLOSE.name
    assert bo_extra_data.get("operate_reason") == "no reason"
    assert bo.get_updator_info() == old_assistant

    # 创建测试用工单。
    form = mock_form_with_step.form
    form.subscribe(client.shop, True)
    db.session.commit()
    bo = BusinessOrder(status=BusinessOrderStatus.INIT, updated_at=1, form_id=form.id)
    db.session.add(bo)
    db.session.commit()
    job = bo.init_bo_jobs()
    db.session.add(job)
    db.session.commit()

    # 任务提交的情况。
    mocker.patch('time.time', return_value=1000)
    old_bo_updated_at = bo.updated_at
    job.update_process_mark_and_record_action(
        process_mark=JobProcessMark.ACCEPT,
        assistant=AccountDetailV2(
            user_id=0,
            user_type=Creator.RPA,
            user_nick="RPA应用"
        ),
        operate_reason="test",
    )
    db.session.commit()
    assert job.process_mark == JobProcessMark.ACCEPT
    assert bo.updated_at != old_bo_updated_at
    assert bo.updated_at == 1000
    assert bo.extra_data.get("operate_action") == JobProcessMark.ACCEPT.name
    assert bo.extra_data.get("operate_reason") == "test"

    # 工单已完成的情况
    mocker.patch('time.time', return_value=1001)
    bo.status = BusinessOrderStatus.SUCCEED
    old_bo_updated_at = bo.updated_at
    job.update_process_mark_and_record_action(
        process_mark=JobProcessMark.FINISH,
        assistant=AccountDetailV2(
            user_id=0,
            user_type=Creator.RPA,
            user_nick="RPA应用"
        ),
        operate_reason="test_reason",
    )
    # FINISH 会变成 ACCEPT
    assert job.process_mark == JobProcessMark.ACCEPT
    assert bo.updated_at == old_bo_updated_at
    assert bo.updated_at != 1001
    # 工单和操作日志的记录则不会变更 operate_action
    assert bo.extra_data.get("operate_action") == JobProcessMark.FINISH.name
    assert bo.extra_data.get("operate_reason") == "test_reason"
