from typing import List
from dataclasses import dataclass

from pytest import fixture

from robot_processor.form.models import Form, Step, StepType


@dataclass
class FormFixture:
    form: Form
    steps: List[Step]


@fixture
def mock_form_with_step(
        client, form_factory, step_factory
):
    form = form_factory.create()
    form_1_steps = step_factory.create_batch(3, form_id=form.id, step_type=StepType.human)

    _step_1, _step_2, _step_3 = form_1_steps
    _step_1.step_type = _step_1.step_type.human
    _step_1.next_step_ids = [_step_2.step_uuid]
    _step_2.prev_step_ids = [_step_1.step_uuid]
    _step_2.next_step_ids = [_step_3.step_uuid]
    _step_3.prev_step_ids = [_step_2.step_uuid]

    yield FormFixture(
        form=form,
        steps=form_1_steps,
    )
