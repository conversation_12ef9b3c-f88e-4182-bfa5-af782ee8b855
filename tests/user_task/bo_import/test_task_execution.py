from io import BytesIO

from openpyxl.reader.excel import load_workbook
from pytest import fixture
from robot_types.core import Symbol
from robot_types.core import TypeSpec

from robot_processor.business_order.models import BusinessOrder
from robot_processor.user_task.helper import bo_context
from robot_processor.user_task.helper.excel import Template
from robot_processor.user_task.model import BatchImportBoTask


@fixture
def testbed(human_only_form_testbed, shop_factory):
    shop = shop_factory.create()
    step = human_only_form_testbed(
        shop=shop,
        form_name="测试表单",
        symbols=[
            Symbol(
                TypeSpec("string"),
                "alipay-account",
                render_config=dict(label="支付宝账号", valueUnique=True),
            ),
            Symbol(TypeSpec("datetime"), "datetime", render_config=dict(label="日期时间")),
        ],
    )
    yield {"form": step.form, "step": step, "shop": shop}


@fixture
def mock_task(client, mocker, testbed):
    template = Template.export_form_to_excel_template(testbed["form"].name, [testbed["form"].id])
    excel = template.serialize_to_excel()
    workbook = load_workbook(excel)
    data_sheet = workbook["批量上传数据"]
    # 模拟 10 条有效数据， 1 条无效数据
    inputs = [
        ["alipay_account_1", "2020-01-01 00:00:00"],
        ["alipay_account_2", "2020-01-01 00:00:00"],
        ["alipay_account_3", "2020-01-01 00:00:00"],
        ["alipay_account_4", "2020-01-01 00:00:00"],
        ["alipay_account_5", "2020-01-01 00:00:00"],
        ["alipay_account_6", "2020-01-01 00:00:00"],
        ["alipay_account_7", "2020-01-01 00:00:00"],
        ["alipay_account_8", "2020-01-01 00:00:00"],
        ["alipay_account_9", "2020-01-01 00:00:00"],
        ["alipay_account_2", "2020-a-b 00:00:00"],
    ]
    for idx, cells in enumerate(inputs):
        for col, cell in enumerate(cells):
            data_sheet.cell(idx + 4, col + 2, cell)

    f = BytesIO()
    workbook.save(f)
    f.seek(0)
    mocker.patch("external.oss.get_object", return_value=f.read())
    request = {
        "filename": "批量上传.xlsx",
        "key_in_oss": "ut",
        "sid": testbed["shop"].sid,
        "org_id": testbed["shop"].org_id,
        "user_info": {"user_nick": "ut", "user_type": 4, "phone": "ut", "user_id": 4},
    }
    yield BatchImportBoTask.create_batch_import_bo_task(request)


def test_overall_checks(mock_task):
    state = bo_context.PendingState(mock_task)
    error = state.do_overall_checks()
    assert error == ""
    biz_display = state.task.biz_display()
    assert biz_display["form_name"] == "测试表单"
    assert biz_display["filename"] == "批量上传.xlsx"
    assert biz_display["reason"] is None
    assert biz_display["success_count"] == 0
    assert biz_display["failed_count"] == 0
    assert biz_display["total"] == 10


def test_per_row_checks(mocker, mock_task):
    mock_check_uniq = mocker.patch("robot_processor.client.risk_control.RiskControlClient.check_widget_value_unique")
    mock_check_uniq.return_value = {}
    state = bo_context.PendingState(mock_task)
    state.transition()
    biz_display = state.task.biz_display()
    assert biz_display["status"] == "Running"
    assert state.task.failure_items == [{"row": 13, "reason": "日期时间: 日期格式错误"}]


def test_per_row_checks_with_duplication(mocker, mock_task):
    valid_data = [data for data in mock_task.template.get_data_for_bo_create() if not hasattr(data, "error")]
    assert len(valid_data) == 9

    mock_query_risk = mocker.patch("robot_processor.client.risk_control.RiskControlClient.query_risk")
    mock_query_risk.return_value = {"1", "2"}
    state = bo_context.PendingState(mock_task)
    state.transition()
    biz_display = state.task.biz_display()
    assert biz_display["status"] == "WaitConfirm"
    assert len(biz_display["reason"]) == 9
    for i in range(4, 13):
        assert {
            "index": i,
            "reason": "支付宝账号 唯一性校验失败",
        } in biz_display["reason"]

    mock_query_risk.return_value = set()
    bo_context.WaitConfirmState(mock_task).confirm(ignore_duplication_error=False)
    biz_display = state.task.biz_display()
    assert biz_display["status"] == "Running"
    assert biz_display["reason"] is None
    for i in range(4, 13):
        assert {
            "row": i,
            "reason": "支付宝账号 唯一性校验失败",
        } in state.task.failure_items


def test_create(mocker, mock_task, mock_action_client):
    bo_context.PendingState(mock_task).transition()  # 让系统把 record_count 等状态填写正确
    mocker.patch("robot_processor.business_order.models.Job.end_timing")
    bo_context.start_user_task(mock_task.id)
    instance = bo_context.UserTask.query.get(mock_task.id)
    biz_display = bo_context.BatchImportBoTask(instance).biz_display()
    assert biz_display["status"] == "PartialFailed"
    assert biz_display["success_count"] == 9
    assert biz_display["failed_count"] == 1
    business_order_id = biz_display["business_order_ids"][0]
    assert BusinessOrder.query.get(business_order_id) is not None
    assert mock_action_client.create_action_log_by_kafka.called
