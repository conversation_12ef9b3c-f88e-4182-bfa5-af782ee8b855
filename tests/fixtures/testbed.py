import json
import os
from typing import Optional

from pytest import fixture

from robot_processor.form.models import Component
from robot_processor.form.models import Widget
from robot_processor.rpa_service.enums import RpaStatusEnum
from robot_processor.rpa_service.models import Rpa
from robot_processor.rpa_service.models import RpaContext
from robot_processor.rpa_service.models import RpaLimit


@fixture(scope="session", autouse=True)
def init_rpa(db):
    import json
    import os

    from robot_processor.app import app
    from robot_processor.constants import __curdir__

    with app.app_context():
        init_rpa_json = os.path.join(__curdir__, "..", "command/rpa_service/init_rpa.json")
        with open(init_rpa_json) as f:
            init_rpa = json.load(f)

        for rpa_dict in init_rpa:
            rpa_limits = rpa_dict.pop("limits")
            rpa = Rpa(**rpa_dict)
            # 新增RPA状态
            rpa.status = RpaStatusEnum.ONLINE
            db.session.add(rpa)
            rpa.limits = [RpaLimit(**limit_dict) for limit_dict in rpa_limits]
        db.session.commit()
        yield


@fixture(scope="session", autouse=True)
def setup_widget(db):
    from robot_processor.app import app
    from robot_processor.constants import __curdir__

    with app.app_context():
        with open(os.path.join(__curdir__, "..", "tests/init_data/component.json")) as f:
            component_config = json.load(f)
            for _component in component_config:
                _component["schema"] = json.loads(_component["schema"])
        db.session.bulk_insert_mappings(Component, component_config)

        with open(os.path.join(__curdir__, "..", "tests/init_data/widget.json")) as f:
            widgets_config = json.load(f)
            for _widget in widgets_config:
                _widget["options"] = json.loads(_widget["options"])
                _widget["schema"] = json.loads(_widget["schema"])
                _widget["widget_meta"] = json.loads(_widget["widget_meta"])
                if "system_widget_data" in _widget:
                    _widget["system_widget_data"] = json.loads(_widget["system_widget_data"])
                if "data_schema_template" in _widget:
                    _widget["data_schema_template"] = json.loads(_widget["data_schema_template"])

        db.session.bulk_insert_mappings(Widget, widgets_config)


@fixture
def widget(setup_widget):
    """使用 widget('单行输入') widget('支付宝账号') 等获取 Widget 对象."""

    def get_widget_by_label(label: str) -> Widget:
        w = Widget.query.filter(Widget.label == label).first()
        assert w is not None, f"widget {label} 没有声明在 tests/init_data/widget.json 中"
        return w

    return get_widget_by_label


@fixture
def rpa(db, rpa_context_factory):
    """使用 rpa(name='订单信息回执[快麦]') rpa(task='SMARTCALL') rpa(tag='支付交易') 等获取 Rpa 对象, 并将其注册给指定的租户."""

    def get_rpa_by_name(
        name: Optional[str] = None, task: Optional[str] = None, tag: Optional[str] = None, org_id: Optional[str] = None
    ) -> Rpa:
        if name is not None:
            rpa = Rpa.query.filter(Rpa.name == name).first()
            assert rpa is not None, f"{name=} 的 rpa 应该声明在 command/rpa_service/init_rpa.json 中"
        elif task is not None:
            rpa = Rpa.query.filter(Rpa.task == task).first()
            assert rpa is not None, f"{task=} 的 rpa 应该声明在 command/rpa_service/init_rpa.json 中"
        elif tag is not None:
            rpa = Rpa.query.filter(Rpa.tag == tag).first()
            assert rpa is not None, f"{tag=} 的 rpa 应该声明在 command/rpa_service/init_rpa.json 中"
        else:
            raise ValueError("rpa() 必须指定 name=xxx 或 task=xxx 或 tag=xxx")
        if org_id is not None:
            if not (RpaContext.query.filter_by(org_id=str(org_id), rpa_id=rpa.id).first()):
                rpa_context_factory.create(org_id=str(org_id), rpa_id=rpa.id)
        return rpa

    return get_rpa_by_name


@fixture(autouse=True, scope="session")
def setup_event_config(db):
    from pkg_resources import resource_listdir
    from pkg_resources import resource_stream

    from robot_processor.app import app
    from robot_processor.form.event.models import EventShortcuts

    resource_pacakge = "tests.init_data.event_config"
    # 遍历 resource package 内 json 文件
    with app.app_context():
        for event_config_file in resource_listdir(resource_pacakge, "."):
            if not event_config_file.endswith(".json"):
                continue
            with resource_stream(resource_pacakge, event_config_file) as stream:
                event_config_conf = json.load(stream)
                shortcuts = event_config_conf.pop("shortcuts", [])
                for shortcut in shortcuts:
                    db.session.add(EventShortcuts(**shortcut))
        db.session.commit()
