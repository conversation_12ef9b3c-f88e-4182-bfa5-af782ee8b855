import sys

from pytest import fixture
from pytest_mock import MockFixture


@fixture(scope="function")
def app_context(db):
    from robot_processor.app import app
    from robot_processor.form.event.models import EventShortcuts
    from robot_processor.form.models import Component
    from robot_processor.form.models import Widget
    from robot_processor.notify.models import Message
    from robot_processor.rpa_service.models import Rpa
    from robot_processor.rpa_service.models import RpaLimit
    from robot_processor.shop.models import ContractInfo
    from robot_processor.shop.models import Shop

    reserved_tables = [m.__table__.name for m in [Rpa, RpaLimit, Component, Widget, Shop, ContractInfo, EventShortcuts]]

    with app.app_context() as ctx:
        for table in reversed(db.metadata.sorted_tables):
            if table.name not in reserved_tables:
                db.session.execute(table.delete())
        # db.metadata.sorted_tables 中没有包含通过 bind_key 映射的 table，所以这里才做显式清理
        db.session.execute(Message.__table__.delete())
        yield ctx
        db.session.remove()


@fixture(scope="session")
def db():
    """
    为单元测试初始化数据库，考虑了两种情况：

    1. 如果环境变量 MYSQL_INSTANCE 存在(如：root@test@localhost:33066)，则使用该环境变量指定的数据库实例来创建测试数据库.
    2. 在办公室网络下，将通过 http://test-db/db 创建测试数据库, 开发者无需通过 docker 在自己的开发机上启动 mysql 容器.

    方法 1. 的性能更高，因为你可以把 MySQL 容器部署在自己的开发机上，减小网络延迟。如果你需要在开发机上频繁地执行单元测试，建议使用方法 1.

    方法 2. 最省事，但是需要保证你的开发机能够访问 http://test-db/db.
    """
    import os
    import random
    import string
    import time

    import requests
    import sqlalchemy as sa

    from robot_processor.app import app
    from robot_processor.ext import db

    test_db_url = os.environ.get("TEST_DB_URL", "http://test-db/db")
    try:
        resp = requests.get(test_db_url)
        has_test_db_support = "mysql-5.7" in resp.json().get("engines", [])
    except requests.exceptions.RequestException:
        has_test_db_support = False

    def reinit_flask_sqlalchemy():
        app.extensions.pop("sqlalchemy", None)
        db.init_app(app)
        with app.app_context():
            db.drop_all()
            db.create_all()

    app.config["TESTING"] = True
    if "MYSQL_INSTANCE" in os.environ:
        """在一个已有的 mysql 实例上创建一个新的 database 用于执行此次单元测试.
        MYSQL_INSTANCE 环境变量的值应该是一个字符串, 格式为: `<user>@<password>@<host>`, 需要确保 user 的权限足够创建和删除数据库.

        建议通过如下命令启动 mysql 实例：

        >>> docker run -d -p 33066:3306 -e MYSQL_ROOT_PASSWORD=test --tmpfs /var/lib/mysql registry.leyantech.com/digismart/mysql5.7-utf8mb4   # noqa
        >>> export MYSQL_INSTANCE=root@test@localhost:33066
        """

        mysql_instance = os.environ["MYSQL_INSTANCE"]
        user, password, host = mysql_instance.strip().split("@")
        engine = sa.create_engine(f"mysql+pymysql://{user}:{password}@{host}/")

        random.seed(os.getpid() * time.time())
        db_name = "rp_" + "".join(random.choice(string.ascii_letters) for _ in range(8))
        kiosk_db_name = "rp_" + "".join(random.choice(string.ascii_letters) for _ in range(8))
        jdp_tb_refund_name = "rp_" + "".join(random.choice(string.ascii_letters) for _ in range(8))
        mola_api_name = "rp_" + "".join(random.choice(string.ascii_letters) for _ in range(8))
        item_db_name = "rp_" + "".join(random.choice(string.ascii_letters) for _ in range(8))
        fs_trade_db_name = "rp_" + "".join(random.choice(string.ascii_letters) for _ in range(8))

        with engine.begin() as conn:
            conn.execute(sa.text(f"CREATE DATABASE IF NOT EXISTS {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;"))
            conn.execute(
                sa.text(f"CREATE DATABASE IF NOT EXISTS {kiosk_db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;")
            )  # noqa: E501
            conn.execute(
                sa.text(
                    f"CREATE DATABASE IF NOT EXISTS {jdp_tb_refund_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;"
                )
            )
            conn.execute(
                sa.text(f"CREATE DATABASE IF NOT EXISTS {mola_api_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;")
            )  # noqa: E501
            conn.execute(
                sa.text(f"CREATE DATABASE IF NOT EXISTS {item_db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;")
            )  # noqa: E501
            conn.execute(
                sa.text(
                    f"CREATE DATABASE IF NOT EXISTS "
                    f"{fs_trade_db_name} CHARACTER SET utf8mb4 "
                    f"COLLATE utf8mb4_bin;"
                )
            )  # noqa: E501

        app.config["SQLALCHEMY_DATABASE_URI"] = (
            f"mysql+pymysql://{user}:{password}@{host}/{db_name}?autocommit=true&charset=utf8mb4"
        )
        app.config["SQLALCHEMY_BINDS"] = {
            "notify": f"mysql+pymysql://{user}:{password}@{host}/{db_name}?autocommit=true&charset=utf8mb4",
            "kiosk": f"mysql+pymysql://{user}:{password}@{host}/{kiosk_db_name}?autocommit=true&charset=utf8mb4",
            "jdp_tb_refund": (
                f"mysql+pymysql://{user}:{password}@{host}" f"/{jdp_tb_refund_name}?autocommit=true&charset=utf8mb4"
            ),
            "mola_api": f"mysql+pymysql://{user}:{password}@{host}/{mola_api_name}?autocommit=true&charset=utf8mb4",
            "dgt_item": f"mysql+pymysql://{user}:{password}@{host}/{item_db_name}?autocommit=true&charset=utf8mb4",
            "fs_trade": f"mysql+pymysql://{user}:{password}@{host}/{fs_trade_db_name}?autocommit=true&charset=utf8mb4",
        }
        reinit_flask_sqlalchemy()
        yield db
        with engine.begin() as conn:
            conn.execute(sa.text(f"DROP DATABASE IF EXISTS {db_name};"))
            conn.execute(sa.text(f"DROP DATABASE IF EXISTS {kiosk_db_name};"))
            conn.execute(sa.text(f"DROP DATABASE IF EXISTS {jdp_tb_refund_name};"))
            conn.execute(sa.text(f"DROP DATABASE IF EXISTS {item_db_name};"))
            conn.execute(sa.text(f"DROP DATABASE IF EXISTS {fs_trade_db_name};"))

    elif has_test_db_support:
        res = requests.post(test_db_url, json={"engine": "mysql-5.7"}).json()
        url = f"mysql+pymysql://{res['user']}:{res['password']}@{res['host']}:{res['port']}/{res['name']}?charset=utf8mb4&autocommit=true"  # noqa

        app.config["SQLALCHEMY_DATABASE_URI"] = url
        kiosk_res = requests.post(test_db_url, json={"engine": "mysql-5.7"}).json()
        kiosk_url = f"mysql+pymysql://{kiosk_res['user']}:{kiosk_res['password']}@{kiosk_res['host']}:{kiosk_res['port']}/{kiosk_res['name']}?charset=utf8mb4"  # noqa
        jdp_tb_refund_res = requests.post(test_db_url, json={"engine": "mysql-5.7"}).json()
        jdp_tb_refund_url = (
            f"mysql+pymysql://{jdp_tb_refund_res['user']}:{jdp_tb_refund_res['password']}"
            f"@{jdp_tb_refund_res['host']}:{jdp_tb_refund_res['port']}"
            f"/{jdp_tb_refund_res['name']}?charset=utf8mb4"
        )
        item_res = requests.post(test_db_url, json={"engine": "mysql-5.7"}).json()
        item_url = f"mysql+pymysql://{item_res['user']}:{item_res['password']}@{item_res['host']}:{item_res['port']}/{item_res['name']}?charset=utf8mb4"  # noqa
        fs_trade_res = requests.post(test_db_url, json={"engine": "mysql-5.7"}).json()
        fs_trade_url = f"mysql+pymysql://{fs_trade_res['user']}:{fs_trade_res['password']}@{fs_trade_res['host']}:{fs_trade_res['port']}/{fs_trade_res['name']}?charset=utf8mb4"  # noqa
        fs_trade_url = f"mysql+pymysql://{fs_trade_res['user']}:{fs_trade_res['password']}@{fs_trade_res['host']}:{fs_trade_res['port']}/{fs_trade_res['name']}?charset=utf8mb4"  # noqa
        app.config["SQLALCHEMY_BINDS"] = {
            "kiosk": kiosk_url,
            "jdp_tb_refund": jdp_tb_refund_url,
            "notify": url,
            "mola_api": url,
            "dgt_item": item_url,
            "fs_trade": fs_trade_url,
        }
        reinit_flask_sqlalchemy()
        yield db
        requests.delete(f"{test_db_url}?id={res['id']}").raise_for_status()
        requests.delete(f"{test_db_url}?id={kiosk_res['id']}").raise_for_status()
        requests.delete(f"{test_db_url}?id={jdp_tb_refund_res['id']}").raise_for_status()
        requests.delete(f"{test_db_url}?id={item_res['id']}").raise_for_status()
        requests.delete(f"{test_db_url}?id={fs_trade_res['id']}").raise_for_status()
    else:
        print(
            "\n===== 建议按如下步骤准备 MYSQL_INSTANCE 环境变量: ======\n"
            "$ docker run -d -p 33066:3306 -e MYSQL_ROOT_PASSWORD=test --tmpfs /var/lib/mysql registry.leyantech.com/digismart/mysql5.7-utf8mb4\n"  # noqa
            "$ export MYSQL_INSTANCE=root@test@localhost:33066\n",
            file=sys.stderr,
        )
        raise RuntimeError("无法创建测试数据库, 请参考上述提示准备测试用 MySQL 容器.")


@fixture(autouse=True)
def mock_actor(mocker: MockFixture):
    mocker.patch("robot_processor.dramatiq.LazyActor.send", return_value=None)
    mocker.patch("robot_processor.dramatiq.LazyActor.send_with_options", return_value=None)


@fixture(autouse=True)
def cache(app_context, mocker):
    """应用全局缓存，每个测试用例执行前清空."""
    from flask_caching import Cache

    from robot_processor.ext import cache

    with app_context:
        cache.clear()
        # 方便执行 cache.set.assert_called_once_with() 等断言
        mocker.spy(Cache, "set")
        mocker.spy(Cache, "get")
        mocker.spy(Cache, "clear")
        mocker.spy(Cache, "delete")
        yield cache
