from pytest import fixture
from pytest_mock import MockFixture

from robot_processor.client import ActionClient
from robot_processor.enums import ErpType
from rpa.erp.jst import JstNewSDK
from rpa.erp.jst import JstQmSDK
from rpa.erp.jst import JstSD<PERSON>


@fixture(autouse=True)
def mock_requests():
    import requests_mock

    from rpa.erp.wdtulti import WdtUltiOpenAPIClient
    from rpa.erp.wdtulti import WdtUltiOpenPlatformAPIClient
    from rpa.erp.wdtulti import WdtUltiQM

    adaptor = requests_mock.Adapter()

    def session_mount(session):
        session.mount("http://", adaptor)
        session.mount("https://", adaptor)

    session_mount(WdtUltiQM.session)
    session_mount(WdtUltiOpenAPIClient.session)
    session_mount(WdtUltiOpenPlatformAPIClient.session)

    yield


@fixture
def mock_get_transfer(mocker: MockFixture):
    yield mocker.patch("robot_processor.client.transfer.RobotTransferClient.find_transfer")


@fixture(autouse=True)
def mock_risk_control(mocker):
    from leyan_proto.digismart.dgt_risk_control.dgt_risk_control_pb2 import GetRiskControlReportResponse
    from leyan_proto.digismart.dgt_risk_control.dgt_risk_control_pb2 import ReportResponse

    from robot_processor.client import risk_control_client

    mocker.patch.object(risk_control_client._stub, "Report", return_value=ReportResponse(success=True))
    mocker.patch.object(risk_control_client._stub, "Delete", return_value=ReportResponse(success=True))
    mocker.patch.object(risk_control_client._stub, "Query", return_value=GetRiskControlReportResponse(success=True))


@fixture
def mock_buyer_server_get_buyer_nick_by_open_uid(mocker: MockFixture):
    from leyan_proto.digismart.buyer_server.buyer_server_rpc_pb2 import GetUserNickByBuyerOpenUidResponse
    from leyan_proto.digismart.dgt_common_pb2 import DgtResponseCode

    yield mocker.patch(
        "robot_processor.client.buyer_client._stub.GetUserNickByBuyerOpenUid",
        return_value=GetUserNickByBuyerOpenUidResponse(
            leyan_buyer_id="ut",
            buyer_nick="ut",
            code=DgtResponseCode.OK,
        ),
    )


@fixture
def mock_buyer_server_get_buyer_nick_by_tid(mocker: MockFixture):
    from leyan_proto.digismart.buyer_server.buyer_server_rpc_pb2 import GetUserNickByTidResponse
    from leyan_proto.digismart.dgt_common_pb2 import DgtResponseCode

    yield mocker.patch(
        "robot_processor.client.buyer_client._stub.GetUserNickByTid",
        return_value=GetUserNickByTidResponse(
            tid="ut",
            buyer_nick="ut",
            code=DgtResponseCode.OK,
        ),
    )


@fixture
def mock_trade_server_try_get_plaintext_nick_by_open_uid(mocker: MockFixture):
    from leyan_proto.digismart.trade.dgt_trade_pb2 import ResponseCode
    from leyan_proto.digismart.trade.dgt_trade_pb2 import TryGetPlaintextNickByOpenUidResponse

    yield mocker.patch(
        "robot_processor.client.trade_client.client.TryGetPlaintextNickByOpenUid",
        return_value=TryGetPlaintextNickByOpenUidResponse(
            code=ResponseCode.FAIL,
        ),
    )


@fixture
def mock_trade_get_trade_by_tid(mocker: MockFixture):
    from leyan_proto.digismart.trade.dgt_trade_pb2 import TradeInfo

    yield mocker.patch("robot_processor.client.trade.TradeClient.get_trade_by_tid", return_value=TradeInfo())


@fixture(autouse=True)
def mock_buyer_client_update_receiver_info(mocker):
    yield mocker.patch("robot_processor.client.buyer.BuyerClient.update_receiver_info", return_value=(True, ""))


@fixture
def mock_action_client(mocker) -> ActionClient:
    return mocker.Mock(
        create_action_log_by_http=mocker.patch.object(ActionClient, "create_action_log_by_http"),
        create_action_log_by_kafka=mocker.patch.object(ActionClient, "create_action_log_by_kafka"),
    )


@fixture
def jst_new_sdk(client, mock_erp_info):
    mock_erp_info.shop_id = client.shop.id
    mock_erp_info.erp_type = ErpType.JST
    mock_erp_info.meta = {"co_id": "co_id"}
    yield JstNewSDK(client.sid)


@fixture
def jst_sdk(client, mock_erp_info):
    mock_erp_info.shop_id = client.shop.id
    mock_erp_info.erp_type = ErpType.JST
    mock_erp_info.meta = {"co_id": "co_id"}
    yield JstSDK(client.sid)


@fixture
def jst_qm(client, mock_erp_info):
    mock_erp_info.shop_id = client.shop.id
    mock_erp_info.erp_type = ErpType.JST
    mock_erp_info.meta = {"co_id": "co_id"}
    yield JstQmSDK(client.sid)


@fixture
def mock_scheduler_stub(mocker: MockFixture):
    from robot_processor.t_cron import scheduler

    yield {
        "create_schedule": mocker.patch.object(
            scheduler._stub, "create_schedule", autospec=scheduler._stub.create_schedule
        ),
        "update_schedule": mocker.patch.object(
            scheduler._stub, "update_schedule", autospec=scheduler._stub.update_schedule
        ),
        "close_schedule": mocker.patch.object(
            scheduler._stub, "close_schedule", autospec=scheduler._stub.close_schedule
        ),
    }
