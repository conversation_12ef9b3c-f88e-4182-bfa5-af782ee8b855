from decimal import Decimal

from pytest import fixture

from robot_processor.function.conversion.number_calculator import BinaryOpNode
from robot_processor.function.conversion.number_calculator import FunctionCallNode
from robot_processor.function.conversion.number_calculator import NumberCalculator
from robot_processor.function.conversion.number_calculator import Parser
from robot_processor.function.conversion.number_calculator import SchemaNode


@fixture
def token_1():
    """订单数量 * (运费 + 服务费)"""
    yield [
        {"operatorType": "schema", "step": {"uuid": "1-2-3-4"}, "target": [{"type": "number", "key": "order-count"}]},
        {"operatorType": "symbol", "value": "*"},
        {"operatorType": "parentheses", "value": "("},
        {"operatorType": "schema", "step": {"uuid": "1-2-3-4"}, "target": [{"type": "number", "key": "freight"}]},
        {"operatorType": "symbol", "value": "+"},
        {"operatorType": "schema", "step": {"uuid": "1-2-3-4"}, "target": [{"type": "number", "key": "service-fee"}]},
        {"operatorType": "parentheses", "value": ")"},
    ]


@fixture
def token_2():
    """一个数值组件 + 开票金额 + SUM( 复合组件.数值 ) + SUM( 第一个步骤复合组件.里面的数值 ) / 一个数值组件 + COUNTS( 第一个步骤复合组件.里面的数值 )"""
    yield [
        {
            "step": {"name": "步骤名称2", "uuid": "58adbf31bfde42a5983c9cdd5777ec05"},
            "target": [{"key": "590421dd-63d5-4a64-b8bd-b9f5ae53d34b", "type": "number", "label": "一个数值组件"}],
            "operatorType": "schema",
        },
        {"key": "0c30b7c7-6eaa-4db7-8541-2406d9616c2b", "value": "+", "operatorType": "symbol"},
        {
            "step": {"name": "步骤名称2", "uuid": "58adbf31bfde42a5983c9cdd5777ec05"},
            "target": [{"key": "ea51cdc5-7af3-44a5-b0ff-a54948390e28", "type": "invoiced-amount", "label": "开票金额"}],
            "operatorType": "schema",
        },
        {"key": "5864b9a7-e4f8-42de-8607-d6f3cbb9c4fd", "value": "+", "operatorType": "symbol"},
        {"key": "68a98cf0-1270-4b7e-b39c-367d1010492e", "value": "SUM", "operatorType": "function"},
        {"key": "2eed5f3a-7c12-48b1-a2d9-282539d2b881", "value": "(", "operatorType": "parentheses"},
        {
            "step": {"name": "步骤名称2", "uuid": "58adbf31bfde42a5983c9cdd5777ec05"},
            "target": [
                {"key": "a6aa4c53-9d00-4f33-90f9-d9063806a79f", "type": "table", "label": "复合组件"},
                {"key": "82badc91-76ff-40a6-bdb6-e4aa8ec8debf", "type": "number", "label": "数值"},
            ],
            "operatorType": "schema",
        },
        {"key": "e59cdd89-0948-4096-9880-ccfb2d01601d", "value": ")", "operatorType": "parentheses"},
        {"key": "ef519a22-9650-4b2f-8e2a-414bcf712d37", "value": "+", "operatorType": "symbol"},
        {"key": "48c872a0-76b5-49da-8185-bfc579907d96", "value": "SUM", "operatorType": "function"},
        {"key": "04929175-5599-4646-a9a2-70e71ea9a9bb", "value": "(", "operatorType": "parentheses"},
        {
            "step": {"name": "步骤名称2", "uuid": "58adbf31bfde42a5983c9cdd5777ec05"},
            "target": [
                {"key": "f18aea84-5cbf-4fde-b747-a1bb85a97d95", "type": "table", "label": "第一个步骤复合组件"},
                {"key": "2603725d-97f9-4023-8b90-0a7df2ea05e0", "type": "number", "label": "里面的数值"},
            ],
            "operatorType": "schema",
        },
        {"key": "16dc5024-f4bf-4237-9fed-3dc5b7d73c28", "value": ")", "operatorType": "parentheses"},
        {"key": "122a79a8-8e7c-4f83-b9a8-1bb77345b1da", "value": "/", "operatorType": "symbol"},
        {
            "step": {"name": "步骤名称2", "uuid": "58adbf31bfde42a5983c9cdd5777ec05"},
            "target": [{"key": "590421dd-63d5-4a64-b8bd-b9f5ae53d34b", "type": "number", "label": "一个数值组件"}],
            "operatorType": "schema",
        },
        {"key": "d09127aa-8aa9-4082-9ec4-4b01389dbc42", "value": "+", "operatorType": "symbol"},
        {"key": "9b61b741-ea0c-4a01-bbbe-68a7bf232428", "value": "COUNTS", "operatorType": "function"},
        {"key": "f6ddcb33-c6f2-4f12-bc4f-0d8bbdcf8aaf", "value": "(", "operatorType": "parentheses"},
        {
            "step": {"name": "步骤名称2", "uuid": "58adbf31bfde42a5983c9cdd5777ec05"},
            "target": [
                {"key": "f18aea84-5cbf-4fde-b747-a1bb85a97d95", "type": "table", "label": "第一个步骤复合组件"},
                {"key": "2603725d-97f9-4023-8b90-0a7df2ea05e0", "type": "number", "label": "里面的数值"},
            ],
            "operatorType": "schema",
        },
        {"key": "0a006889-51cc-41df-a342-f7b745684a4c", "value": ")", "operatorType": "parentheses"},
    ]


def test_parser(token_1, token_2):
    expression = Parser(token_1).parse_expression()
    assert isinstance(expression, BinaryOpNode)
    assert expression.op == "*"
    assert isinstance(expression.left, SchemaNode)
    assert expression.left.step_uuid == "1-2-3-4"
    assert expression.left.target == [{"type": "number", "key": "order-count"}]
    assert isinstance(expression.right, BinaryOpNode)
    assert expression.right.op == "+"
    assert isinstance(expression.right.left, SchemaNode)
    assert expression.right.left.step_uuid == "1-2-3-4"
    assert expression.right.left.target == [{"type": "number", "key": "freight"}]
    assert isinstance(expression.right.right, SchemaNode)

    expression = Parser(token_2).parse_expression()
    from pprint import pp

    pp(expression)
    assert isinstance(expression, BinaryOpNode)
    assert expression.op == "+"
    # COUNTS( 第一个步骤复合组件.里面的数值 )
    assert isinstance(expression.right, FunctionCallNode)
    assert expression.right.value == "COUNTS"
    assert isinstance(expression.right.args[0], SchemaNode)
    assert expression.right.args[0].target[0]["key"] == "f18aea84-5cbf-4fde-b747-a1bb85a97d95"
    assert expression.right.args[0].target[1]["key"] == "2603725d-97f9-4023-8b90-0a7df2ea05e0"
    # 一个数值组件 + 开票金额 + SUM( 复合组件.数值 ) + SUM( 第一个步骤复合组件.里面的数值 ) / 一个数值组件
    assert isinstance(expression.left, BinaryOpNode)
    assert expression.left.op == "+"
    # SUM( 第一个步骤复合组件.里面的数值 ) / 一个数值组件
    assert isinstance(expression.left.right, BinaryOpNode)
    assert expression.left.right.op == "/"
    # SUM( 第一个步骤复合组件.里面的数值 )
    assert isinstance(expression.left.right.left, FunctionCallNode)
    assert expression.left.right.left.value == "SUM"
    assert isinstance(expression.left.right.left.args[0], SchemaNode)
    assert expression.left.right.left.args[0].target[0]["key"] == "f18aea84-5cbf-4fde-b747-a1bb85a97d95"
    assert expression.left.right.left.args[0].target[1]["key"] == "2603725d-97f9-4023-8b90-0a7df2ea05e0"
    # 一个数值组件
    assert isinstance(expression.left.right.right, SchemaNode)
    assert expression.left.right.right.target[0]["key"] == "590421dd-63d5-4a64-b8bd-b9f5ae53d34b"
    # 一个数值组件 + 开票金额 + SUM( 复合组件.数值 )
    assert isinstance(expression.left.left, BinaryOpNode)
    assert expression.left.left.op == "+"
    # 一个数值组件 + 开票金额
    assert isinstance(expression.left.left.left, BinaryOpNode)
    assert expression.left.left.left.op == "+"
    # 一个数值组件
    assert isinstance(expression.left.left.left.left, SchemaNode)
    assert expression.left.left.left.left.target[0]["key"] == "590421dd-63d5-4a64-b8bd-b9f5ae53d34b"
    # 开票金额
    assert isinstance(expression.left.left.left.right, SchemaNode)
    assert expression.left.left.left.right.target[0]["key"] == "ea51cdc5-7af3-44a5-b0ff-a54948390e28"
    # SUM( 复合组件.数值 )
    assert isinstance(expression.left.left.right, FunctionCallNode)
    assert expression.left.left.right.value == "SUM"
    assert isinstance(expression.left.left.right.args[0], SchemaNode)
    assert expression.left.left.right.args[0].target[0]["key"] == "a6aa4c53-9d00-4f33-90f9-d9063806a79f"
    assert expression.left.left.right.args[0].target[1]["key"] == "82badc91-76ff-40a6-bdb6-e4aa8ec8debf"


def test_calculate(token_1, token_2):
    calculator = NumberCalculator(expression=token_1, context={"order-count": 3, "freight": 10, "service-fee": 5})
    result = calculator.call()
    assert result.is_ok()
    assert result.ok_value == Decimal(45)

    calculator = NumberCalculator(
        expression=token_2,
        context={
            "590421dd-63d5-4a64-b8bd-b9f5ae53d34b": 10,  # 一个数值组件
            "ea51cdc5-7af3-44a5-b0ff-a54948390e28": 5,  # 开票金额
            "a6aa4c53-9d00-4f33-90f9-d9063806a79f": [  # 复合组件
                {
                    "82badc91-76ff-40a6-bdb6-e4aa8ec8debf": 1,  # 复合组件.数值
                },
                {
                    "82badc91-76ff-40a6-bdb6-e4aa8ec8debf": 2,
                },
            ],
            "f18aea84-5cbf-4fde-b747-a1bb85a97d95": [  # 第一个步骤复合组件
                {
                    "2603725d-97f9-4023-8b90-0a7df2ea05e0": 1,  # 第一个步骤复合组件.里面的数值
                },
                {
                    "2603725d-97f9-4023-8b90-0a7df2ea05e0": 2,
                },
            ],
        },
    )
    result = calculator.call()
    assert result.is_ok()
    # 一个数值组件 + 开票金额 + SUM( 复合组件.数值 ) + SUM( 第一个步骤复合组件.里面的数值 ) / 一个数值组件 + COUNTS( 第一个步骤复合组件.里面的数值 )  noqa
    assert float(result.ok_value) == 10 + 5 + sum([1, 2]) + sum([1, 2]) / 10 + len([1, 2])


def test_second_diff():
    expression = [
        {"key": "c4c13dba-e9c5-45c7-a649-6f2297e17f22", "value": "SECOND_DIFF", "operatorType": "function"},
        {"key": "b9ce93e7-9e1f-4202-907d-77fccbd80323", "value": "(", "operatorType": "parentheses"},
        {
            "step": {"name": "获取仅退款数据", "uuid": "661b7cd9cffa43f8a55a266446928544"},
            "target": [{"key": "b1bae4a6-8521-47ff-af8e-a14245da5b46", "type": "datetime", "label": "售后单创建时间"}],
            "operatorType": "schema",
        },
        {"key": "ba6e2eaa-4061-4921-a540-627d075bcc30", "value": ",", "operatorType": "parentheses"},
        {
            "step": {"name": "获取仅退款数据", "uuid": "661b7cd9cffa43f8a55a266446928544"},
            "target": [{"key": "a89508d2-9916-4bf4-84bc-2bff7b1dd962", "type": "datetime", "label": "订单创建时间"}],
            "operatorType": "schema",
        },
        {"key": "ec6374e0-8b93-40a9-912c-eb1d8164f8a3", "value": ")", "operatorType": "parentheses"},
    ]
    context = {
        "b1bae4a6-8521-47ff-af8e-a14245da5b46": "2025-01-01 00:00:00",
        "a89508d2-9916-4bf4-84bc-2bff7b1dd962": "2025-01-01 00:00:01",
    }
    from robot_processor.function.conversion.number_calculator import NumberCalculator

    func = NumberCalculator()
    func.expression = expression
    func.context = context
    assert func.call().ok() == -1
