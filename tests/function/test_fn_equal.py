from enum import Enum
from robot_processor.form.symbol_table import Value, TypeSpec
from robot_processor.function import FnEqual


class Values(Enum):
    STRING_RED = Value.build_const(TypeSpec(type="string"), "RED")
    STRING_YELLOW = Value.build_const(TypeSpec(type="string"), "YELLOW")
    NUMBER_1 = Value.build_const(TypeSpec(type="number"), 1)
    NUMBER_1_0 = Value.build_const(TypeSpec(type="number"), 1.0)
    NUMBER_2 = Value.build_const(TypeSpec(type="number"), 2)
    DATETIME_2024 = Value.build_const(TypeSpec(type="datetime"), "2024-01-01 12:00:00")
    DATE_2024 = Value.build_const(TypeSpec(type="date"), "2024-01-01")
    DATE_2023 = Value.build_const(TypeSpec(type="date"), "2023-01-01")
    TIME_12 = Value.build_const(TypeSpec(type="time"), "12:00:00")
    TIME_13 = Value.build_const(TypeSpec(type="time"), "13:00:00")


def test_string():
    eq_result = FnEqual(
        FnEqual.Args.LHS.build_fn_arg(Values.STRING_RED.value),
        FnEqual.Args.RHS.build_fn_arg(Values.STRING_RED.value),
    ).call()
    assert eq_result.is_ok()
    assert (eq_value := eq_result.unwrap().get()).is_ok()
    assert eq_value.unwrap() is True

    eq_result = FnEqual(
        FnEqual.Args.LHS.build_fn_arg(Values.STRING_RED.value),
        FnEqual.Args.RHS.build_fn_arg(Values.STRING_YELLOW.value),
    ).call()
    assert eq_result.is_ok()
    assert (eq_value := eq_result.unwrap().get()).is_ok()
    assert eq_value.unwrap() is False


def test_number():
    eq_result = FnEqual(
        FnEqual.Args.LHS.build_fn_arg(Values.NUMBER_1.value),
        FnEqual.Args.RHS.build_fn_arg(Values.NUMBER_1.value),
    ).call()
    assert eq_result.is_ok()
    assert (eq_value := eq_result.unwrap().get()).is_ok()
    assert eq_value.unwrap() is True

    eq_result = FnEqual(
        FnEqual.Args.LHS.build_fn_arg(Values.NUMBER_1.value),
        FnEqual.Args.RHS.build_fn_arg(Values.NUMBER_1_0.value),
    ).call()
    assert eq_result.is_ok()
    assert (eq_value := eq_result.unwrap().get()).is_ok()
    assert eq_value.unwrap() is True

    eq_result = FnEqual(
        FnEqual.Args.LHS.build_fn_arg(Values.NUMBER_1.value),
        FnEqual.Args.RHS.build_fn_arg(Values.NUMBER_2.value),
    ).call()
    assert eq_result.is_ok()
    assert (eq_value := eq_result.unwrap().get()).is_ok()
    assert eq_value.unwrap() is False


def test_datetime():
    datetime_eq_date_result = FnEqual(
        FnEqual.Args.LHS.build_fn_arg(Values.DATETIME_2024.value),
        FnEqual.Args.RHS.build_fn_arg(Values.DATE_2024.value),
    ).call()
    assert datetime_eq_date_result.is_ok()
    assert (datetime_eq_date_value := datetime_eq_date_result.unwrap().get()).is_ok()
    assert datetime_eq_date_value.unwrap() is True

    datetime_eq_date_result = FnEqual(
        FnEqual.Args.LHS.build_fn_arg(Values.DATETIME_2024.value),
        FnEqual.Args.RHS.build_fn_arg(Values.DATE_2023.value),
    ).call()
    assert datetime_eq_date_result.is_ok()
    assert (datetime_eq_date_value := datetime_eq_date_result.unwrap().get()).is_ok()
    assert datetime_eq_date_value.unwrap() is False

    datetime_eq_time_result = FnEqual(
        FnEqual.Args.LHS.build_fn_arg(Values.DATETIME_2024.value),
        FnEqual.Args.RHS.build_fn_arg(Values.TIME_12.value),
    ).call()
    assert datetime_eq_time_result.is_ok()
    assert (datetime_eq_time_value := datetime_eq_time_result.unwrap().get()).is_ok()
    assert datetime_eq_time_value.unwrap() is True

    datetime_eq_time_result = FnEqual(
        FnEqual.Args.LHS.build_fn_arg(Values.DATETIME_2024.value),
        FnEqual.Args.RHS.build_fn_arg(Values.TIME_13.value),
    ).call()
    assert datetime_eq_time_result.is_ok()
    assert (datetime_eq_time_value := datetime_eq_time_result.unwrap().get()).is_ok()
    assert datetime_eq_time_value.unwrap() is False

    date_eq_date_result = FnEqual(
        FnEqual.Args.LHS.build_fn_arg(Values.DATE_2024.value),
        FnEqual.Args.RHS.build_fn_arg(Values.DATE_2024.value),
    ).call()
    assert date_eq_date_result.is_ok()
    assert (date_eq_date_value := date_eq_date_result.unwrap().get()).is_ok()
    assert date_eq_date_value.unwrap() is True

    date_eq_date_result = FnEqual(
        FnEqual.Args.LHS.build_fn_arg(Values.DATE_2024.value),
        FnEqual.Args.RHS.build_fn_arg(Values.DATE_2023.value),
    ).call()
    assert date_eq_date_result.is_ok()
    assert (date_eq_date_value := date_eq_date_result.unwrap().get()).is_ok()
    assert date_eq_date_value.unwrap() is False

    time_eq_time_result = FnEqual(
        FnEqual.Args.LHS.build_fn_arg(Values.TIME_12.value),
        FnEqual.Args.RHS.build_fn_arg(Values.TIME_12.value),
    ).call()
    assert time_eq_time_result.is_ok()
    assert (time_eq_time_value := time_eq_time_result.unwrap().get()).is_ok()
    assert time_eq_time_value.unwrap() is True

    time_eq_time_result = FnEqual(
        FnEqual.Args.LHS.build_fn_arg(Values.TIME_12.value),
        FnEqual.Args.RHS.build_fn_arg(Values.TIME_13.value),
    ).call()
    assert time_eq_time_result.is_ok()
    assert (time_eq_time_value := time_eq_time_result.unwrap().get()).is_ok()
    assert time_eq_time_value.unwrap() is False
