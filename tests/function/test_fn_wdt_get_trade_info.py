from pytest import fixture

from robot_processor.form.symbol_table import NamedTypeSpec
from robot_processor.function.wdt import GetTradeInfo


class TestGetTradeInfo:
    @fixture
    def setup(self, mocker):
        function = GetTradeInfo()
        arg_tid = function.Args.PLATFORM_TRADE_ID.build_fn_arg_const_value("123456")
        arg_credential = function.Args.CREDENTIAL.build_fn_arg_const_value(
            NamedTypeSpec.CREDENTIAL_WDT.model(
                sid="ut",
                after_sale_shop_no="00001",
                app_key="ut-fs",
                app_secret="ut",
            ).dict()
        )
        mocker.patch("rpa.erp.wdt.wdt.WdtClient.trade_query")
        mocker.patch("rpa.erp.wdt.wdt.WdtClient.stockout_order_query_trade")

        yield {
            "function": function,
            "arg_tid": arg_tid,
            "arg_credential": arg_credential,
        }

    def testcase(self, setup):
        function_result = setup["function"].call()
        assert not function_result.is_ok()
        assert str(function_result.unwrap_err()) == "未找到店铺的旺店通授权信息"

        setup["function"].set_arg(setup["arg_credential"])
        function_result = setup["function"].call()
        assert not function_result.is_ok()
        assert str(function_result.unwrap_err()) == "平台订单号和旺店通系统单号和快递单号不能同时为空"

        setup["function"].set_arg(setup["arg_tid"])
        function_result = setup["function"].call()
        assert function_result.is_ok()
