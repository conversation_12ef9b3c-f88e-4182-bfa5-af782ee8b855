from typing import List
from typing import Optional
from typing import TypedDict
from typing import Union
from typing import cast

from robot_processor.rpa_service.models import RpaContext
from robot_processor.shop.models import Shop

from ._base import TestbedBuilder

DEFAULT_RESOURCE_PACKAGE = "tests.testbed.resource.form_template"


class FormDict(TypedDict):
    name: str
    description: str
    steps: List["StepDict"]


class StepDict(TypedDict):
    name: str
    step_type: str
    widget_collection: List["WidgetDict"]
    rpa: str
    prev_steps: List[str]
    next_steps: List[str]
    coupled_step_uuid: Optional[str]


class WidgetDict(TypedDict):
    label: str
    type: str
    key: str


class FormBuilder(TestbedBuilder):
    def __init__(
        self,
        form_dict: Union[FormDict, str],
        shop: Optional[Shop] = None,
        resource_package: str = DEFAULT_RESOURCE_PACKAGE,
    ):
        from io import TextIOWrapper
        from json import loads

        from pkg_resources import resource_stream

        from robot_processor.ext import db
        from robot_processor.form.models import Widget
        from robot_processor.rpa_service.models import Rpa

        if not isinstance(form_dict, dict):
            with resource_stream(
                *self.resource_path(resource_package, form_dict)
            ) as stream:
                form_dict = loads(TextIOWrapper(stream, encoding="utf8").read())

        self.form_dict = form_dict
        self.shop = shop
        widget_map = dict((widget.schema["type"], widget) for widget in Widget.query)
        assert widget_map, "组件信息为空，请检查是否使用 fixture:setup_widget"
        self.widget_map = widget_map
        rpa_map = dict((rpa.name, rpa) for rpa in db.session.query(Rpa))
        assert rpa_map, "RPA 信息为空，请检查是否使用 fixture:init_rpa"
        self.rpa_map = rpa_map

    def build_widget_info(self, widget_dict_list: List[WidgetDict]):
        from robot_processor.ext import db
        from robot_processor.form.models import WidgetCollection
        from robot_processor.form.models import WidgetInfo

        widget_collection = WidgetCollection()
        for widget_dict in widget_dict_list:
            widget_info = WidgetInfo()
            widget_info.key = widget_dict["key"]
            widget_info.widget_id = self.widget_map[widget_dict["type"]].id
            widget_info.option_value = {
                key: value
                for key, value in widget_dict.items()
                if key not in {"key", "type"}
            }
            widget_info.widget_collection = widget_collection
            db.session.add(widget_info)
        db.session.add(widget_collection)
        db.session.commit()

        return widget_collection

    def build_steps(self, step_dict_list: List[StepDict]):
        """
        tips:
            1. step 会默认初始化好 step.raw_step，如果 testcase 需要用到 raw_step 的场景，请注意
            2. 仅 sid 不为空时，才会绑定 rpa 和店铺的关系
        """
        from sqlalchemy.orm.attributes import flag_modified

        from robot_processor.enums import StepType
        from robot_processor.ext import db
        from robot_processor.form.models import Step

        step_map = dict()
        # 初始化
        for step_dict in step_dict_list:
            step = Step()
            step.is_dirty = False
            step.name = step_dict["name"]
            step.step_type = StepType[step_dict["step_type"]]
            if step_dict.get("widget_collection"):
                widget_collection = self.build_widget_info(
                    step_dict["widget_collection"]
                )
                step.widget_collection_id = widget_collection.id
            if rpa_name := step_dict.get("rpa"):
                rpa = self.rpa_map.get(rpa_name)
                assert (
                    rpa
                ), f"未找到名字为 {rpa_name} 的 rpa，请检查配置的 rpa 名字是否正确"
                step.set_task_id(rpa.id)

            # 没有在 StepDict 中声明的字段直接尝试赋值
            for key, value in step_dict.items():
                if key in StepDict.__annotations__:
                    continue
                setattr(step, key, value)

            db.session.add(step)
            step_map[step_dict["name"]] = step
        db.session.commit()

        # 绑定前后关系
        for step_dict in step_dict_list:
            step = step_map[step_dict["name"]]
            step.prev_step_ids = [
                step_map[step_name].step_uuid
                for step_name in step_dict.get("prev_steps", [])
            ]
            step.next_step_ids = [
                step_map[step_name].step_uuid
                for step_name in step_dict.get("next_steps", [])
            ]
            for branch in step.branch or []:
                branch["next"] = step_map[branch["next"]].step_uuid
            flag_modified(step, "branch")
            if step_name := step_dict.get("coupled_step_uuid"):
                step.coupled_step_uuid = step_map[step_name].step_uuid
        db.session.commit()

        return list(step_map.values())

    def build(self):
        from robot_processor.ext import db
        from robot_processor.form.models import Form

        form_dict = self.form_dict
        form = Form()
        form.name = form_dict["name"]
        form.description = form_dict["description"]
        form.steps = self.build_steps(form_dict["steps"])
        db.session.add(form)
        if self.shop:
            self.bind_shop(form, self.shop)
        form.snapshot()
        self.form = form

        return form

    def bind_shop(self, form, shop):
        """
        effect:
            1. set form.sid
            2. register rpa for org
            3. init step.raw_step
        """
        from robot_processor.ext import db
        from robot_processor.form.models import Step

        form.subscribe(shop, True)
        # 初始化 raw_step
        for step in form.steps:
            step = cast(Step, step)
            if rpa_id := step.data.get("rpa_id"):
                org_id = str(shop.org_id)
                if not (
                    RpaContext.query.filter_by(
                        org_id=str(org_id), rpa_id=rpa_id
                    ).first()
                ):
                    rpa_context = RpaContext(org_id=org_id, rpa_id=rpa_id)
                    db.session.add(rpa_context)
            step.update_raw_step()
