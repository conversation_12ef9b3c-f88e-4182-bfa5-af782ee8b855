from typing import List, TypedDict, Union
from ._base import TestbedBuilder

from robot_processor.shop.models import Shop


DEFAULT_RESOURCE_PACKAGE = "tests.testbed.resource.business_order_template"


class BusinessOrderDict(TypedDict):
    name: str  # 用于 fixture 的名称
    description: str  # 仅用作描述，不影响测试
    form_template: str  # 使用哪个工单模板生成工单实例
    current_job: str  # 将当前工单推进到哪一个步骤
    jobs: List['JobDict']


class JobDict(TypedDict):
    name: str  # 用于定位工单步骤


class BusinessOrderBuilder(TestbedBuilder):
    def __init__(
        self,
        business_order_dict: Union[BusinessOrderDict, str, dict],
        shop: Shop,
        resource_package: str = DEFAULT_RESOURCE_PACKAGE,
    ):
        from json import loads
        from io import TextIOWrapper
        from pkg_resources import resource_stream

        self.resource_package = resource_package
        if not isinstance(business_order_dict, dict):
            with resource_stream(
                *self.resource_path(resource_package, business_order_dict)
            ) as stream:
                business_order_dict = loads(
                    TextIOWrapper(stream, encoding="utf8").read()
                )

        self.business_order_dict = self.wrap_business_order_dict(business_order_dict)
        self.shop = shop

    def wrap_business_order_dict(self, business_order_dict):
        business_order_dict.setdefault("creator_user_id", 1)
        business_order_dict.setdefault("creator_type", 6)
        return business_order_dict

    def build_form(self):
        from tests.testbed.helper.form import FormBuilder

        form_template = self.business_order_dict["form_template"]
        shop = self.shop

        if self.resource_package == DEFAULT_RESOURCE_PACKAGE:
            form_builder = FormBuilder(form_template, shop)
        else:
            form_builder = FormBuilder(form_template, shop, self.resource_package)
        self.form = form_builder.build()

    def build_business_order(self):
        from more_itertools import first
        from robot_processor.enums import JobStatus
        from robot_processor.business_order.models import BusinessOrder
        from robot_processor.utils import sort_steps
        from robot_processor.ext import db

        business_order = BusinessOrder()
        business_order.form_id = self.form.id
        business_order.sid = self.shop.sid
        business_order.form_version_id = self.form.versions.first().id
        for field in self.business_order_dict:
            if field in BusinessOrderDict.__annotations__:
                continue
            setattr(business_order, field, self.business_order_dict[field])
        db.session.add(business_order)
        db.session.commit()

        steps = sort_steps(self.form.steps.all())
        # 指定了工单当前步骤，进行推进
        if "current_job" in self.business_order_dict:
            for step in steps:
                job = self.build_job(business_order, step)
                business_order.set_current_execute_job(job)
                # 这个步骤已经处理完了，标记为成功
                if step.name != self.business_order_dict["current_job"]:
                    job.status = JobStatus.SUCCEED
                # 找到目标步骤，退出循环
                else:
                    business_order.current_job_id = job.id
                    break
        # 默认推进工单到第一步
        else:
            job = self.build_job(business_order, first(steps))
            business_order.job_history = [job.id]
            business_order.current_job_id = job.id

        self.build_trade_map(business_order)
        db.session.commit()

        self.business_order = business_order

        return business_order

    def build(self):
        self.build_form()
        business_order = self.build_business_order()

        return business_order

    def build_job(self, business_order, step):
        from robot_processor.business_order.models import Job
        from robot_processor.ext import db

        job_define = {
            each["name"]: each for each in self.business_order_dict.get("jobs", [])
        }
        job = Job.create_by_step(step, business_order.id)
        if step.name in job_define:
            for field in job_define[step.name]:
                if field in JobDict.__annotations__:
                    continue
                setattr(job, field, job_define[step.name][field])
        db.session.add(job)
        db.session.flush()
        db.session.refresh(job)

        return job

    def build_trade_map(self, business_order):
        tid_oid_list = []
        for widget_key in business_order.data or {}:
            if "order" in widget_key:
                data = business_order.data[widget_key]
                if not isinstance(data, list):
                    continue
                for trade_obj in data:
                    if "tid" in trade_obj:
                        tid_oid_list.append(trade_obj["tid"])
                    if "oid" in trade_obj:
                        tid_oid_list.append(trade_obj["oid"])
        if tid_oid_list:
            business_order.create_business_order_trade_map(set(tid_oid_list))
