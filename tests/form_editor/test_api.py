import random

from robot_processor.form.models import Widget
from robot_processor.form.models import WidgetCollection


class TestFormEditor:

    def test_widgets_get(self, client):
        resp = client.get("/v1/form-editor/widgets")
        widget_groups = resp.json["widgets"]
        assert set(group["category"] for group in widget_groups) == {"系统组件", "电商组件", "基础组件", "增强组件"}
        assert sum(len(group["members"]) for group in widget_groups) == Widget.query.count(), "应该能获取到所有的组件"

    def test_widgets_get_failed_at_platform_limit_rule(self, client_unauthorized, db):
        client, _, shop = client_unauthorized.login()
        assert shop.platform == "TAOBAO"
        resp = client.get("/v1/form-editor/widgets")
        widget_groups = resp.json["widgets"]
        widgets = [widget for group in widget_groups for widget in group["members"]]
        assert "商品编码" in [widget["label"] for widget in widgets]

        shop.platform = "PDD"
        db.session.commit()
        resp = client.get("/v1/form-editor/widgets")
        widget_groups = resp.json["widgets"]
        widgets = [widget for group in widget_groups for widget in group["members"]]
        assert len(widgets) > 1
        assert "商品编码" not in [
            widget["label"] for widget in widgets
        ], "根据 tests/init_data/widget.json 的配置，商品编码组件不应该出现在拼多多平台上"

    def test_widget_schema_get(self, client):
        resp = client.get("/v1/form-editor/widgets")
        widget_groups = resp.json["widgets"]
        widgets = [widget for group in widget_groups for widget in group["members"]]
        widget = random.choice(widgets)
        resp = client.post("/v1/form-editor/widgets/schema", json={"widget_ids": [widget["id"]]})
        assert "widget_schemas" in resp.json
        assert len(resp.json["widget_schemas"]) == 1
        widget_schema = resp.json["widget_schemas"][0]

        assert widget_schema["id"] == widget["id"]
        assert widget_schema["label"] == widget["label"]
        assert widget_schema["category"] == widget["category"]

    def test_widget_collection_create_update_get(self, client, db):
        """
        测试 widget collection 的如下操作：创建 -> 更新 -> 发布 -> 更新
        """
        data = {
            "widgets": [
                {
                    "id": 1,
                    "key": "test 1",
                    "before": False,
                    "option_value": {"option 1": "value 1"},
                },
                {"id": 2, "key": "test 2", "before": False, "option_value": {"option 2": "value 2"}},
                {
                    "id": 4,
                    "key": "table_key",
                    "before": False,
                    "option_value": {"table option": "table value"},
                    "data_schema": {"fields": [], "multi_row": False},
                },
            ],
            "form_id": 1,
        }
        resp = client.post("/v1/form-editor/widgets", json=data)
        assert resp.json["success"]
        widget_collection_id = resp.json["widget_collection_id"]
        db.session.close()

        resp = client.get(f"/v1/form-editor/widgets/widget-collection/{widget_collection_id}")
        assert len(resp.json["widgets"]) == 3
        db.session.close()

        put_data = {
            "widget_collection_id": widget_collection_id,
            "widgets": [
                {
                    "id": 1,
                    "key": "test 1",
                    "before": False,
                    "option_value": {"option 1": "value 1"},
                }
            ],
            "form_id": 1,
        }
        resp = client.put("/v1/form-editor/widgets", json=put_data)
        assert resp.json["success"]
        assert resp.json["widget_collection_id"] == widget_collection_id
        # widget collection 的发布操作没有暴露接口，所以这里直接通过 db orm API 进行模拟
        WidgetCollection.query.filter(WidgetCollection.id == widget_collection_id).update({"is_dirty": False})
        db.session.commit()
        db.session.close()

        resp = client.get(f"/v1/form-editor/widgets/widget-collection/{widget_collection_id}?brief=true")
        assert len(resp.json["widgets"]) == 1
        db.session.close()

        put_data2 = {
            "widget_collection_id": widget_collection_id,
            "widgets": [
                {
                    "id": 3,
                    "key": "test 3",
                    "before": False,
                    "option_value": {"option 1": "value 1"},
                },
            ],
            "form_id": 1,
        }
        resp = client.put("/v1/form-editor/widgets", json=put_data2)
        assert resp.json["widget_collection_id"] != widget_collection_id
        new_widget_collection_id = resp.json["widget_collection_id"]
        db.session.close()

        resp = client.get(f"/v1/form-editor/widgets/widget-collection/{new_widget_collection_id}?brief=true")
        assert len(resp.json["widgets"]) == 1
        assert resp.json["widgets"][0]["key"] == "test 3"
        db.session.close()

        old_widget_collection = WidgetCollection.query.get(widget_collection_id)
        new_widget_collection = WidgetCollection.query.get(new_widget_collection_id)
        assert sorted(old_widget_collection.changelog["deleted_widget_key"]) == ["table_key", "test 2"]
        assert new_widget_collection.changelog["deleted_widget_key"] == ["test 1"]
