from robot_processor.db import db
from robot_processor.form.api.form_editor_schema import StepNotifyConfig
from robot_processor.form.models import StepExceptionNotify, Step

# mock a config for StepNotifyConfig
notify_config_as_dict = {
    "enabled": True,
    "order_status": 8,
    "name": "test",
    "notify_channels": ["in_app"],
    "receive_type": 1,
    "title": [
        {
            "text": "this is title",
            "type": "text"
        }
    ],
    "content": [
        {
            "text": "this is content",
            "type": "text"
        }
    ]
}


def test_save_notify_config(mock_step, mock_form):
    step: Step = mock_step
    step.notify_configs = [notify_config_as_dict]
    db.session.commit()
    step_notify_config_in_db = StepExceptionNotify.query.filter_by(
        step_uuid=step.step_uuid,
    ).first()
    assert step_notify_config_in_db
    assert step_notify_config_in_db.config["enabled"] == notify_config_as_dict["enabled"]

    # test get notify config in stepView
    step.form = mock_form
    step.form_id = mock_form.id
    step_view = Step.View.FormEditor.from_orm(step)
    assert step_view.notify_configs[0]["enabled"] == notify_config_as_dict["enabled"]


def test_get_content(mock_full_job):
    config = StepNotifyConfig(**notify_config_as_dict)
    assert config.render_title_content_as_str(mock_full_job, config.title, config.content) == ("this is title",
                                                                                               "this is content")
