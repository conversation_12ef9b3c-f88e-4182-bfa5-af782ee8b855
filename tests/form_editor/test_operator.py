from pytest import fixture


@fixture()
def mock_operators(operator_factory):
    operator_factory.create(
        type="time",
        label="时间等于",
        operator="TIME_EQ",
        option={"ui_type": "time_single"}
    )
    operator_factory.create(
        type="string",
        label="等于任一",
        operator="IN",
        option={"ui_type": "string_any"}
    )


def test_operator(client, mock_operators):
    resp = client.get("/v1/form-editor/widgets/operators")
    assert resp.status_code == 200
    assert resp.json['success']
    assert len(resp.json['data']) == 2
