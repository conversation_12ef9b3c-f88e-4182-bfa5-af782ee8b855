from unittest.mock import MagicMock

import pytest

from external.schema import PddPlatformAckBody
from robot_processor.app import app


def _pdd_success_refund_detail():
    return {
        "after_sales_reason": "str",
        "after_sales_status": 0,
        "after_sales_type": 1,
        "confirm_time": 1638792640659,
        "discount_amount": 0,
        "dispute_refund_status": 0,
        "exchange_shipping_detail": {
            "customer_send_back_ship_id": 0,
            "customer_send_back_trunk_number": "str",
            "exchange_goods_name": "str",
            "exchange_goods_number": 0,
            "exchange_goods_price": 0,
            "exchange_receiver_city": "str",
            "exchange_receiver_city_id": 0,
            "exchange_receiver_province": "str",
            "exchange_receiver_province_id": 0,
            "exchange_receiver_town": "str",
            "exchange_receiver_town_id": 0,
            "merchant_exchange_detail_address": "str",
            "merchant_exchange_detail_address_mask": "str",
            "merchant_exchange_detail_phone": "str",
            "merchant_exchange_detail_phone_mask": "str",
            "merchant_exchange_detail_receiver": "str",
            "merchant_exchange_detail_receiver_mask": "str",
            "merchant_exchange_ship_id": 0,
            "merchant_exchange_trunk_number": "str",
            "sku_id_exchange": "str"
        },
        "expire_time": 1638792640659,
        "express_no": "str",
        "goods_number": 1,
        "goods_price": 100,
        "id": 10,
        "images": [
            "str"
        ],
        "join_or_not": "str",
        "order_amount": 100,
        "order_sn": "str",
        "part_after_sales_type": 1,
        "part_after_sales_value": 1,
        "recreated_at": 1638792640659,
        "refund_amount": 1,
        "refund_operator_role": 1,
        "remark": "联系卖家多次，一直不发货，申请仅退款处理",
        "shipping_name": "str",
        "shipping_status": 0,
        "sku_id": "str",
        "speed_refund_flag": 0,
        "updated_time": "str",
        "user_shipping_status": "str"
    }


@pytest.fixture(params=["has_auth", "no_auth"])
def mock_pdd_client_request(request, mocker):
    import rpa.pdd.api
    mock_resp = PddPlatformAckBody(
        success=True,
        message=None,
        traceId="123",
        request=None,
        result=_pdd_success_refund_detail()
    )
    if request.param == "has_auth":
        mocker.patch.object(rpa.pdd.api, "if_shop_pdd_auth_valid", return_value=True)
    else:
        mocker.patch.object(rpa.pdd.api, "if_shop_pdd_auth_valid", return_value=False)
    mocker.patch("robot_processor.client.pdd_bridge_client.request", return_value=mock_resp)
    yield request.param


def test_refund_detail_api(client, mock_pdd_client_request):
    path = "/v1/pdd-open-api/refund_detail/get"
    payload = {
        "store_id": "123",
        "order_sn": "234",
        "after_sale_id": None
    }
    r = client.post(path, json=payload, headers={
        "Authentication-Token": app.config.get("SERVICE_TOKEN")
    })
    assert r.status_code == 200, r.data.decode()
    resp = r.json
    if mock_pdd_client_request == "has_auth":
        assert resp["success"]
        assert resp["data"]["id"] == 10
    if mock_pdd_client_request == "no_auth":
        assert not resp["success"]
        assert resp["message"] == "店铺未授权"


def test_get_products(client, mocker):
    mock_resp = MagicMock(
        raise_for_status=MagicMock(),
        json=MagicMock(
            return_value={
                "sub_code": 200,
                "msg": "成功",
                "data": [
                    {"service_start_at": 1704952881000,
                     "service_ends_at": 1736488881000,
                     "product_code": "ATX"}
                ]
            }
        )
    )
    mocker.patch(
        "robot_processor.client.pdd_bridge_client.session.get",
        return_value=mock_resp
    )
    path = "/v1/pdd-open-api/auth_products/get"
    params = {"store_id": "123"}
    r = client.post(path, json=params, headers={
        "Authentication-Token": app.config.get("SERVICE_TOKEN")
    })
    assert r.status_code == 200, r.data.decode()
    resp = r.json
    assert resp["success"]
    assert resp["data"] == [
        {"service_start_at": 1704952881000,
         "service_ends_at": 1736488881000,
         "product_code": "ATX"}
    ]
