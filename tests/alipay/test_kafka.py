import uuid

import pytest

from robot_processor.alipay.kafka import transfer_event_handler
from robot_processor.alipay.schema import TransferAction
from robot_processor.enums import BusinessOrderStatus, JobProcessMark, JobType


@pytest.fixture
def mock_data(client, mock_business_order, step_factory, mock_form, job_factory, db, rpa):
    tid_key = uuid.uuid4().hex
    amount_key = uuid.uuid4().hex
    mock_business_order.data = {tid_key: {"tid": "111", "oid": ""}, amount_key: 1}
    task = rpa(task=JobType.ALIPAY, org_id=client.org_id)
    step = step_factory(key_map={
        "tid": tid_key,
        "amount": amount_key,
        "dddd": "key3"
    }, form_id=mock_form.id, data={'rpa_id': task.id})
    mock_business_order.sid = client.sid
    mock_form.subscribe(client.shop, True)
    mock_business_order.form_id = mock_form.id
    job = job_factory.create(step_id=step.id, step_uuid=step.step_uuid, business_order_id=mock_business_order.id)

    record = {
        'bo_id': mock_business_order.id,
        'job_id': job.id,
        'transfer_info': {
            'amount': '2.00', 'payment_reason': ['好评返现'],
            'comment': '测试event', 'pic_url': [],
            'tid': [
                {'tid': '1385739048794032479', 'oid': '1385739048794032479'}
            ],
            'receive_info': {'receive_account': '',
                             'receive_name': '',
                             'payment_method': 1,
                             'receive_usernick': ''}
        },
        'operate_reason': "test",
        'operator_user': "ut",
    }
    return record, job


class TestAlipayEvent:
    def test_edit(self, mock_data):
        record, job = mock_data
        record['action'] = TransferAction.EDIT.name
        record['transfer_info']['amount'] = '10.0'
        assert transfer_event_handler(None, record)
        amount_key = job.raw_step_v2['key_map']['amount']
        amount = job.business_order.data.get(amount_key)
        assert float(amount) == float(record['transfer_info']['amount'])

    def test_close(self, mock_data):
        record, job = mock_data
        record['action'] = TransferAction.CLOSE.name
        assert transfer_event_handler(None, record)
        assert job.business_order.status == BusinessOrderStatus.CLOSE

    def test_recover(self, mock_data):
        record, job = mock_data
        record['action'] = TransferAction.RECOVER.name
        assert transfer_event_handler(None, record)
        assert job.business_order.status == BusinessOrderStatus.RUNNING

    def test_reject(self, mock_data):
        record, job = mock_data
        record['action'] = TransferAction.REJECT.name
        assert transfer_event_handler(None, record)
        assert job.business_order.status == BusinessOrderStatus.RUNNING
        assert job.process_mark == JobProcessMark.REJECT

    def test_fail_pay(self, mock_data):
        # no action
        record, job = mock_data
        record['action'] = TransferAction.PAY.name
        assert not transfer_event_handler(None, record)

    def test_fail_edit(self, mock_data):
        # no job
        record, job = mock_data
        record['action'] = TransferAction.EDIT.name
        record['job_id'] = None
        assert not transfer_event_handler(None, record)
        # no bo
        record['job_id'] = job.id
        record['bo_id'] = None
        assert not transfer_event_handler(None, record)
        # job not found
        record['job_id'] = job.id + 1
        record['bo_id'] = job.business_order.id
        assert not transfer_event_handler(None, record)
