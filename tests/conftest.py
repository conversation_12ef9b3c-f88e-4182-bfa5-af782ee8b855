import typing
from typing import Any

import pytest
from _pytest.fixtures import fixture
from pytest_mock.plugin import MockerFixture  # noqa
from pytest_mock.plugin import _mocker  # # noqa

pytest_plugins = [
    "tests.fixtures.clients",
    "tests.fixtures.infra",
    "tests.fixtures.auth",
    "tests.fixtures.testbed",
    "tests.factories",
]


if typing.TYPE_CHECKING:
    import requests_mock as rm_module

    @pytest.fixture
    def mocker(pytestconfig: Any) -> MockerFixture:
        """
        这个 fixture 源自 pytest_mock 库, 在这里声明该 fixture 的类型，是为了帮助 IDE 识别出这个 fixture 的类型，从而提供代码补全
        """
        ...

    @pytest.fixture
    def requests_mock(request) -> rm_module.Mocker:
        """
        这个 fixture 源自 requests_mock 库，在这里声明该 fixture 的类型，是为了帮助 IDE 识别出这个 fixture 的类型，从而提供代码补全
        """
        ...


@pytest.fixture(autouse=True)
def remove_sqlalchemy_event_listen():
    from sqlalchemy.event import contains
    from sqlalchemy.event import remove

    from robot_processor.business_order.events import bo_set_data_event
    from robot_processor.business_order.events import record_bo_update
    from robot_processor.business_order.models import BusinessOrder

    def _remove_sqlalchemy_event_listen(target, ident, fn):
        if contains(target, ident, fn):
            remove(target, ident, fn)

    _remove_sqlalchemy_event_listen(BusinessOrder.data, "set", bo_set_data_event)
    _remove_sqlalchemy_event_listen(BusinessOrder.data, "modified", bo_set_data_event)
    _remove_sqlalchemy_event_listen(BusinessOrder, "before_update", record_bo_update)


@pytest.fixture
def mock_set_bo_updator_by_nick(mocker):
    patch = mocker.patch("robot_processor.business_order.models.BusinessOrder.set_updator_by_nick")
    yield patch
    assert patch.call_count > 0, "当前测试不应该需要这个mock"


@fixture
def mock_full_job(client, mock_job, mock_business_order, mock_erp_info, mock_step, mock_form):
    mock_job.business_order_id = mock_business_order.id
    mock_business_order.sid = client.shop.sid
    mock_erp_info.shop_id = client.shop.id
    mock_job.step_id = mock_step.id
    mock_job.step_uuid = mock_step.step_uuid
    mock_step.form_id = mock_form.id
    mock_erp_info.meta = {
        "sid": client.shop.sid,
        "wdt_appkey": "wdt_appkey",
        "wdt_secret": "wdt_secret",
        "wdt_salt": "wdt_salt",
        "appKey": "appKey",
        "appSecret": "appSecret",
        "baseUrl": "http://localhost:8000",
        "after_sale_shop_no": "after_sale_shop_no",
        "co_id": "co_id",
    }
    return mock_job
