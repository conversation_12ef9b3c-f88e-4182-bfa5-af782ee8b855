from datetime import datetime

from pytest import fixture
from pytest import raises
from robot_types.core import FnSignature
from robot_types.core import TypeSpec

from sandbox.runner import CodeRunner


@fixture
def datetime_calculator_signature():
    signature = FnSignature(
        name="datetime_calculator",
        params={"base": TypeSpec("datetime"), "unit": TypeSpec("string"), "expr": TypeSpec("number")},
        rtype=TypeSpec("collection", properties={"result": TypeSpec("datetime")}),
    )
    yield signature


@fixture
def datetime_calculator_code():
    code = """
def datetime_calculator(params):
    from datetime import timedelta
    result = params["base"] + timedelta(**{params["unit"]: params["expr"]})
    return {"result": result}
    """
    yield code


@fixture
def datetime_calculator_code_runner(datetime_calculator_signature, datetime_calculator_code):
    code_runner = CodeRunner(datetime_calculator_code, datetime_calculator_signature)
    yield code_runner


class TestCheckParams:
    def test_datetime_calculator(self, datetime_calculator_code_runner):
        params = {"base": ""}
        with raises(TypeError, match="参数 base 类型不匹配"):
            datetime_calculator_code_runner.check_params(params)

        params = {"base": "2024-01-01 00:00:00"}
        datetime_calculator_code_runner.check_params(params)

        params = {"unit": "days"}
        datetime_calculator_code_runner.check_params(params)

        params = {"expr": ""}
        with raises(TypeError, match="参数 expr 类型不匹配"):
            datetime_calculator_code_runner.check_params(params)

        params = {"expr": -1}
        datetime_calculator_code_runner.check_params(params)


class TestCheckReturn:
    def testcase(self, datetime_calculator_code_runner):
        with raises(TypeError, match="返回值 result 类型不匹配"):
            datetime_calculator_code_runner.check_return({"result": ""})


class TestRun:
    def test_datetime_calculator(self, datetime_calculator_code_runner):
        params = {"base": "2024-01-01 00:00:00", "unit": "days", "expr": 1}
        result = datetime_calculator_code_runner.run(params)
        assert result.is_ok()
        assert result.unwrap() == {"result": datetime(2024, 1, 2)}
