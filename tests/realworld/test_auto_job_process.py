from abc import ABC, abstractmethod
from unittest.mock import <PERSON>Mock, patch
from pytest import fixture
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>
from result import Err


def clone_case_info(job_id: int):
    from json import loads, dump
    from sqlalchemy import text
    from robot_processor.db import db
    from robot_processor.business_order.handlers import bo_widget_wrapper
    from robot_processor.business_order.models import BusinessOrder

    # commit 29d5325921f0471def07abde4145fbe5776b7df6
    step_id = db.session.scalar(
        text("select step_id from job where id=:job_id"), {"job_id": job_id}
    )
    bo_id = db.session.scalar(
        text("select business_order_id from job where id=:job_id"), {"job_id": job_id}
    )
    bo_data = loads(
        db.session.scalar(
            text("select `data` from business_order where id=:bo_id"), {"bo_id": bo_id}
        )
    )
    key_map = loads(
        db.session.scalar(
            text("select `key_map` from step where id=:step_id"), {"step_id": step_id}
        )
    )
    task_arguments = loads(
        db.session.scalar(
            text("select `raw_step`->>'$.task.arguments' from step where id=:step_id"),
            {"step_id": step_id},
        )
    )
    widgets = list(
        filter(
            lambda widget_obj: not widget_obj["key"].startswith(
                "system_business_order"
            ),
            bo_widget_wrapper(db.session.get(BusinessOrder, bo_id)),
        )
    )
    with open(f"realworld-case-clone-{job_id}.json", "w") as f:
        dump(
            {
                "bo_data": bo_data,
                "key_map": key_map,
                "task_arguments": task_arguments,
                "widgets": widgets,
            },
            f,
            ensure_ascii=False,
            indent=2,
        )


class CaseMixin(ABC):
    @abstractmethod
    def executor(self):
        pass

    @abstractmethod
    def bo_data(self):
        pass

    @abstractmethod
    def widgets(self):
        pass

    @abstractmethod
    def key_map(self):
        pass

    @abstractmethod
    def arguments(self):
        pass

    @fixture(autouse=True)
    def mock_bo_widgets(self, mocker: MockerFixture):
        yield mocker.patch(
            "robot_processor.business_order.handlers.bo_widget_wrapper",
            return_value=self.widgets(),
        )


class TestCase1(CaseMixin):
    def executor(self):
        from robot_processor.job.alipay import AlipayExecutor

        job = MagicMock()
        order = MagicMock()
        job.business_order = order
        job.raw_step_v2 = {
            "key_map": self.key_map(),
            "task": {"arguments": self.arguments()},
        }
        order.data = self.bo_data()

        return AlipayExecutor(job)

    def bo_data(self):
        return {
            "oid": "3808433521087744400",
            "tid": "3808433521087744400",
            "system_business_order_end_at": "",
            "system_business_order_status": "异常中",
            "system_business_order_deadline": "2024-03-27 19:18:54",
            "system_business_order_form_name": "支付宝批量打款（新）",
            "system_business_order_shop_nick": "织布鸟旗舰店",
            "14016f1276f1426c919aead6757248f5": [
                {"oid": "3808433521087744400", "tid": "3808433521087744400"}
            ],
            "29accf3dc6854a33b367437499f4c7ce": "勿动！工单完成后系统自动填充",
            "********************************": "KT-蓝-方腿+腰枕+脚踏",
            "518b9e1411f6433c91cde5765f2d7209": "",
            "5fd77d6be44e4cd58e483b6ba0899b71": "二团工部",
            "7c2ce25874824bfa91675aa929de4e79": {
                "tid": "3808433521087744400",
                "alipay_no": "2024031022001190651423900290",
                "payment_method": 3,
            },
            "a1a11fc831764256a3c1da887c4e0607": "566",
            "a4fd69e8f0b14e6db3062d191b4d6c60": "淘系杨杰电脑椅韵达偏远新疆内蒙西藏海南青海黑龙江（代发）",
            "dbc8520bdf51496eb6b3462885b55afa": "433773144425465",
            "de16a8df58f6403fbf862b2ed077d878": [
                {
                    "uid": "image37",
                    "url": "https://prd-robot-processor.oss-cn-zhangjiakou.aliyuncs.com"
                           "/mini_sidebar/upload/image/310/********"
                           "/a4dcc5fc-67f9-418b-843b-9d07c698e6fe_image.png",
                    "name": "image.png",
                    "fileId": "image37",
                    "fileName": "a4dcc5fc-67f9-418b-843b-9d07c698e6fe_image.png",
                    "file_type": "image",
                    "key_in_oss": "mini_sidebar/upload/image/310/********"
                                  "/a4dcc5fc-67f9-418b-843b-9d07c698e6fe_image.png",
                }
            ],
            "e24c0a6ab6be4eefbc0ff0b50ccca27b": "勿动！工单完成后系统自动填充",
            "e43087bbe96d4820a62227d1874e9062": [[{"label": "破损", "value": "破损"}]],
            "e68691402f834abebcf695a5165bdac4": "杨杰电脑椅仓",
            "e705ea1e71ce4ee5ad52d8526a69d7ea": "38",
            "eac5bdc874e04498b7ebd8fe4f58c670": "勿动！工单完成后系统自动填充",
            "system_business_order_created_at": "2024-03-27 19:18:54",
            "system_business_order_updated_at": "2024-03-27 19:18:57",
            "system_business_order_receipt_url": "",
            "system_business_order_update_user": "钊钊",
            "36805474-df8e-486b-b3f2-3f07dce2f891": [
                {
                    "oid": "3808433521087744400",
                    "sku": "5221495168374",
                    "spu": "757410742861",
                    "tid": "3808433521087744400",
                    "mode": "sku",
                    "count": 1,
                    "image": "https://img.alicdn.com/bao/uploaded/i1/732202974/O1CN01XF6rvu1Xq8oGc5goA_!!732202974.jpg",
                    "price": 270,
                    "props": "1627207:***********:"
                             "颜色分类:【加粗款承重翻倍】烟雨蓝+腰枕+脚踏+储物袋;"
                             "13746047:119248816:五星脚材质:钢制脚;"
                             "13746149:43772405:扶手类型:固定扶手",
                    "pic_url": "https://img.alicdn.com/bao/uploaded/i1"
                               "/732202974/O1CN01XF6rvu1Xq8oGc5goA_!!732202974.jpg",
                    "pic_path": "https://img.alicdn.com/bao/uploaded/i1"
                                "/732202974/O1CN01XF6rvu1Xq8oGc5goA_!!732202974.jpg",
                    "outer_sku": "KT-蓝-方腿+腰枕+脚踏",
                    "outer_spu": "",
                    "item_title": "电脑椅子舒服久坐电竞椅办公沙发座椅学习靠背书桌宿舍大学生凳子",
                    "short_title": "",
                }
            ],
            "system_business_order_feisuo_creator": "翟美怡",
            "system_business_order_feisuo_updator": "翟美怡",
            "system_business_order_creator_platform": "织布鸟旗舰店:钊钊",
            "system_business_order_current_step_name": "自动提交审批",
            "system_business_order_creator_group_string": "",
            "system_business_order_updator_group_string": "",
            "system_business_order_shop_platform_chinese": "天猫",
            "system_business_order_current_job_assignee_feisuo": "",
            "system_business_order_feisuo_creator_group_string": "",
            "system_business_order_feisuo_updator_group_string": "",
            "system_business_order_current_job_exception_reason": "未知错误",
            "system_business_order_current_job_assignee_platform": "织布鸟旗舰店:钊钊",
            "system_business_order_current_job_assignee_group_feisuo_string": "",
            "system_business_order_current_job_assignee_group_platform_string": "",
        }

    def key_map(self):
        return {
            "tid": "14016f1276f1426c919aead6757248f5",
            "amount": "e705ea1e71ce4ee5ad52d8526a69d7ea",
            "comment": "3b6b34bb0614445fb1b7b3690d28d729",
            "pic_url": "de16a8df58f6403fbf862b2ed077d878",
            "receive_info": "7c2ce25874824bfa91675aa929de4e79",
            "payment_reason": "e43087bbe96d4820a62227d1874e9062",
            "transfer_status": 1,
        }

    def widgets(self):
        return [
            {
                "widget_id": 10,
                "key": "5fd77d6be44e4cd58e483b6ba0899b71",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "5fd77d6be44e4cd58e483b6ba0899b71",
                            "type": "usernick",
                            "title": "买家昵称",
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 0,
                "id": 10,
                "category": 1,
                "widget_meta": [],
                "icon": "ficon-buyer-nick-input",
                "type": "usernick",
                "label": "买家昵称",
                "unique": True,
                "description": "买家昵称，根据订单号支持自动赋值；若无自动赋值，需用户手动输入。淘系平台可获取明文买家昵称，拼多多和抖音获取加密买家昵称",
                "widget_type": "string",
                "required": True,
            },
            {
                "widget_id": 13,
                "key": "14016f1276f1426c919aead6757248f5",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "14016f1276f1426c919aead6757248f5",
                            "type": "order",
                            "title": "子订单ID",
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 1,
                "id": 13,
                "category": 1,
                "widget_meta": [],
                "icon": "ficon-single-trade-select",
                "type": "order",
                "label": "子订单ID",
                "unique": True,
                "description": "订单/子订单，支持订单列表下拉选择或手动输入平台订单/子订单号",
                "widget_type": "table",
                "mode": "child",
                "multiInput": False,
                "required": True,
            },
            {
                "widget_id": 36,
                "key": "518b9e1411f6433c91cde5765f2d7209",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "518b9e1411f6433c91cde5765f2d7209",
                            "type": "string",
                            "title": "原快递单号",
                            "constraints": {"required": False},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 2,
                "id": 36,
                "category": 2,
                "widget_meta": [],
                "ref": ["product"],
                "icon": "ficon-text-input",
                "type": "string",
                "label": "原快递单号",
                "unique": False,
                "description": "单行输入，需要填写者在输入框内根据标题填写相应的内容",
                "widget_type": "string",
                "fieldDescriptors": {"type": "string"},
                "required": False,
            },
            {
                "widget_id": 36,
                "key": "3b6b34bb0614445fb1b7b3690d28d729",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "3b6b34bb0614445fb1b7b3690d28d729",
                            "type": "string",
                            "title": "备注",
                            "constraints": {"required": False},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 3,
                "id": 36,
                "category": 2,
                "widget_meta": [],
                "ref": ["product"],
                "icon": "ficon-text-input",
                "type": "string",
                "label": "备注",
                "unique": False,
                "description": "单行输入，需要填写者在输入框内根据标题填写相应的内容",
                "widget_type": "string",
                "fieldDescriptors": {"type": "string"},
                "required": False,
            },
            {
                "widget_id": 5,
                "key": "e43087bbe96d4820a62227d1874e9062",
                "before": False,
                "schema_value": {},
                "data_schema": {},
                "ref_config": [],
                "order": 4,
                "id": 5,
                "category": 2,
                "widget_meta": [],
                "icon": "ficon-multiple-select",
                "mode": "multiple",
                "type": "select",
                "label": "打款理由",
                "unique": False,
                "expired": True,
                "conditions": [
                    {
                        "label": "包含任一",
                        "ui_type": "select",
                        "operator": "CONTAINS_ANY",
                        "ref_type": "select",
                        "value_type": "ENUM",
                    },
                    {
                        "label": "包含全部",
                        "ui_type": "select",
                        "operator": "CONTAINS_ALL",
                        "ref_type": "select",
                        "value_type": "ENUM",
                    },
                    {
                        "label": "不包含任一",
                        "ui_type": "select",
                        "operator": "DISJOINT",
                        "ref_type": "select",
                        "value_type": "ENUM",
                    },
                ],
                "description": "多选，可以自定义编辑和增删选项，选项视图方式为下拉",
                "options": [
                    {"label": "实物与描述不符", "value": "实物与描述不符"},
                    {"label": "取消退款", "value": "取消退款"},
                    {"label": "色差", "value": "色差"},
                    {"label": "变形", "value": "变形"},
                    {"label": "开焊", "value": "开焊"},
                    {"label": "生锈", "value": "生锈"},
                    {"label": "质量问题", "value": "质量问题"},
                    {"label": "镜面变形", "value": "镜面变形"},
                    {"label": "镜面脱落", "value": "镜面脱落"},
                    {"label": "破损", "value": "破损"},
                    {"label": "漏发配件", "value": "漏发配件"},
                    {"label": "退回运费", "value": "退回运费"},
                    {"label": "好评返现", "value": "好评返现"},
                    {"label": "无理由赔偿", "value": "无理由赔偿"},
                    {"label": "不稳晃动", "value": "不稳晃动"},
                    {"label": "污渍", "value": "污渍"},
                    {"label": "异味", "value": "异味"},
                    {"label": "发霉", "value": "发霉"},
                    {"label": "快递浸泡起鼓", "value": "快递浸泡起鼓"},
                    {"label": "安装不上", "value": "安装不上"},
                    {"label": "补差价", "value": "补差价"},
                    {"label": "运营上错价", "value": "运营上错价"},
                    {"label": "厂家发错货", "value": "厂家发错货"},
                    {"label": "运营上错编码", "value": "运营上错编码"},
                    {"label": "厂家补件不及时", "value": "厂家补件不及时"},
                    {"label": "塌陷", "value": "塌陷"},
                    {"label": "请车费", "value": "请车费"},
                    {"label": "上楼费", "value": "上楼费"},
                    {"label": "运费险", "value": "运费险"},
                    {"label": "弃件", "value": "弃件"},
                    {"label": "货款", "value": "货款"},
                    {"label": "掉漆", "value": "掉漆"},
                ],
                "required": True,
            },
            {
                "widget_id": 38,
                "key": "e705ea1e71ce4ee5ad52d8526a69d7ea",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "e705ea1e71ce4ee5ad52d8526a69d7ea",
                            "type": "number",
                            "title": "打款金额",
                            "constraints": {"required": True},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 5,
                "id": 38,
                "category": 2,
                "widget_meta": [],
                "icon": "ficon-number-input",
                "type": "number",
                "label": "打款金额",
                "unique": False,
                "description": "数字输入，规定填写者输入内容为数字，可进行数值校验",
                "widget_type": "number",
                "fieldDescriptors": {"type": "number"},
                "required": True,
            },
            {
                "widget_id": 25,
                "key": "7c2ce25874824bfa91675aa929de4e79",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "7c2ce25874824bfa91675aa929de4e79",
                            "type": "payment-method",
                            "title": "收款信息",
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 6,
                "id": 25,
                "category": 1,
                "widget_meta": [
                    {"label": "真实姓名", "value": "name"},
                    {"label": "支付宝账号", "value": "account"},
                ],
                "icon": "ficon-payment-info-input",
                "type": "payment-method",
                "label": "收款信息",
                "unique": True,
                "description": "收款信息，适用于打款场景登记消费者收款信息，淘系平台支持通过订单号转账；全平台均支持通过支付宝账号转账。支付宝账号若用于打款，则支付宝账号和真实姓名均需必填。",
                "widget_type": "object",
                "required": True,
            },
            {
                "widget_id": 41,
                "key": "de16a8df58f6403fbf862b2ed077d878",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "de16a8df58f6403fbf862b2ed077d878",
                            "type": "array",
                            "title": "图片上传",
                            "constraints": {"required": True},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 7,
                "id": 41,
                "category": 2,
                "widget_meta": [],
                "icon": "ficon-image-grid",
                "type": "upload",
                "label": "图片上传",
                "unique": False,
                "uploadType": "image",
                "description": "图片上传，可以在表单添加图片文件",
                "widget_type": "table",
                "fieldDescriptors": {"type": "array"},
                "mode": "image",
                "required": True,
            },
            {
                "widget_id": 14,
                "key": "36805474-df8e-486b-b3f2-3f07dce2f891",
                "before": False,
                "schema_value": {},
                "data_schema": {},
                "ref_config": [],
                "order": 8,
                "id": 14,
                "category": 1,
                "widget_meta": [],
                "icon": "ficon-product1",
                "type": "product",
                "label": "商品1",
                "unique": False,
                "conditions": [
                    {
                        "label": "等于任一",
                        "ui_type": "string_any",
                        "operator": "IN",
                        "ref_type": "product",
                        "value_type": "OBJECT",
                    },
                    {
                        "label": "为空",
                        "ui_type": "no_widget",
                        "operator": "NOT_EXIST",
                        "ref_type": "product",
                        "value_type": "OBJECT",
                    },
                ],
                "description": "商品，可自动同步订单内商品，也支持手动选择商品，商品展示商品相关字段信息，包含标题、属性等信息",
                "mode": "sku",
                "required": True,
            },
            {
                "widget_id": 36,
                "key": "9fd9a18b550842fba6dc596c00596bf1",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "9fd9a18b550842fba6dc596c00596bf1",
                            "type": "string",
                            "title": "商品",
                            "constraints": {"required": False},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 9,
                "id": 36,
                "category": 2,
                "widget_meta": [],
                "ref": ["product"],
                "icon": "ficon-text-input",
                "type": "string",
                "label": "商品",
                "unique": False,
                "description": "单行输入，需要填写者在输入框内根据标题填写相应的内容",
                "widget_type": "string",
                "fieldDescriptors": {"type": "string"},
                "required": False,
            },
            {
                "widget_id": 26,
                "key": "********************************",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "********************************",
                            "type": "out-sku-id",
                            "title": "商品编码",
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 10,
                "id": 26,
                "category": 1,
                "widget_meta": [],
                "icon": "ficon-outer-sku-input",
                "type": "out-sku-id",
                "label": "商品编码",
                "limits": {
                    "rules": [
                        {"rule_type": "platform", "rule_value": "TAOBAO"},
                        {"rule_type": "platform", "rule_value": "TMALL"},
                    ],
                    "rule_relation": "OR",
                },
                "unique": True,
                "conditions": [
                    {
                        "label": "日期等于",
                        "ui_type": "date_single",
                        "operator": "DATE_EQ",
                        "ref_type": "order-time",
                        "value_type": "DATETIME",
                    },
                    {
                        "label": "日期早于",
                        "ui_type": "date_single",
                        "operator": "DATE_BEFORE",
                        "ref_type": "order-time",
                        "value_type": "DATETIME",
                    },
                    {
                        "label": "日期晚于",
                        "ui_type": "date_single",
                        "operator": "DATE_AFTER",
                        "ref_type": "order-time",
                        "value_type": "DATETIME",
                    },
                    {
                        "label": "日期属于",
                        "ui_type": "date_double",
                        "operator": "DATE_BETWEEN",
                        "ref_type": "order-time",
                        "value_type": "DATETIME",
                    },
                    {
                        "label": "时间等于",
                        "ui_type": "time_single",
                        "operator": "TIME_EQ",
                        "ref_type": "order-time",
                        "value_type": "DATETIME",
                    },
                    {
                        "label": "时间早于",
                        "ui_type": "time_single",
                        "operator": "TIME_BEFORE",
                        "ref_type": "order-time",
                        "value_type": "DATETIME",
                    },
                    {
                        "label": "时间晚于",
                        "ui_type": "time_single",
                        "operator": "TIME_AFTER",
                        "ref_type": "order-time",
                        "value_type": "DATETIME",
                    },
                    {
                        "label": "时间属于",
                        "ui_type": "time_double",
                        "operator": "TIME_BETWEEN",
                        "ref_type": "order-time",
                        "value_type": "DATETIME",
                    },
                    {
                        "label": "为空",
                        "ui_type": "no_widget",
                        "operator": "NOT_EXIST",
                        "ref_type": "order-time",
                        "value_type": "OBJECT",
                    },
                ],
                "description": "SKU商品编码，根据当前表单中选择的商品，"
                               "自动同步商品编码-SKU维度，商品编码数据源和商品同步，"
                               "若商品数据源选择的是上架商品则编码也取自上架商品。"
                               "注意：若对应数据源未配置商品编码，则获取数据为空。",
                "widget_type": "string",
                "required": True,
            },
            {
                "widget_id": 36,
                "key": "e68691402f834abebcf695a5165bdac4",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "e68691402f834abebcf695a5165bdac4",
                            "type": "string",
                            "title": "发货仓",
                            "constraints": {"required": True},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 11,
                "id": 36,
                "category": 2,
                "widget_meta": [],
                "ref": ["product"],
                "icon": "ficon-text-input",
                "type": "string",
                "label": "发货仓",
                "unique": False,
                "description": "单行输入，需要填写者在输入框内根据标题填写相应的内容",
                "widget_type": "string",
                "fieldDescriptors": {"type": "string"},
                "required": True,
            },
            {
                "widget_id": 36,
                "key": "a4fd69e8f0b14e6db3062d191b4d6c60",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "a4fd69e8f0b14e6db3062d191b4d6c60",
                            "type": "string",
                            "title": "快递公司",
                            "constraints": {"required": True},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 12,
                "id": 36,
                "category": 2,
                "widget_meta": [],
                "ref": ["product"],
                "icon": "ficon-text-input",
                "type": "string",
                "label": "快递公司",
                "unique": False,
                "description": "单行输入，需要填写者在输入框内根据标题填写相应的内容",
                "widget_type": "string",
                "fieldDescriptors": {"type": "string"},
                "required": True,
            },
            {
                "widget_id": 36,
                "key": "dbc8520bdf51496eb6b3462885b55afa",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "dbc8520bdf51496eb6b3462885b55afa",
                            "type": "string",
                            "title": "快递单号",
                            "constraints": {"required": True},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 13,
                "id": 36,
                "category": 2,
                "widget_meta": [],
                "ref": ["product"],
                "icon": "ficon-text-input",
                "type": "string",
                "label": "快递单号",
                "unique": False,
                "description": "单行输入，需要填写者在输入框内根据标题填写相应的内容",
                "widget_type": "string",
                "fieldDescriptors": {"type": "string"},
                "required": True,
            },
            {
                "widget_id": 36,
                "key": "a1a11fc831764256a3c1da887c4e0607",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "a1a11fc831764256a3c1da887c4e0607",
                            "type": "string",
                            "title": "仓库编号",
                            "constraints": {"required": True},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 14,
                "id": 36,
                "category": 2,
                "widget_meta": [],
                "ref": ["product"],
                "icon": "ficon-text-input",
                "type": "string",
                "label": "仓库编号",
                "unique": False,
                "description": "单行输入，需要填写者在输入框内根据标题填写相应的内容",
                "widget_type": "string",
                "fieldDescriptors": {"type": "string"},
                "required": True,
            },
            {
                "widget_id": 36,
                "key": "e24c0a6ab6be4eefbc0ff0b50ccca27b",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "e24c0a6ab6be4eefbc0ff0b50ccca27b",
                            "type": "string",
                            "title": "商户订单号",
                            "constraints": {"required": False},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 15,
                "id": 36,
                "category": 2,
                "widget_meta": [],
                "ref": ["product"],
                "icon": "ficon-text-input",
                "type": "string",
                "label": "商户订单号",
                "unique": False,
                "description": "单行输入，需要填写者在输入框内根据标题填写相应的内容",
                "widget_type": "string",
                "fieldDescriptors": {"type": "string"},
                "required": False,
            },
            {
                "widget_id": 36,
                "key": "eac5bdc874e04498b7ebd8fe4f58c670",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "eac5bdc874e04498b7ebd8fe4f58c670",
                            "type": "string",
                            "title": "支付流水号",
                            "constraints": {"required": False},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 16,
                "id": 36,
                "category": 2,
                "widget_meta": [],
                "ref": ["product"],
                "icon": "ficon-text-input",
                "type": "string",
                "label": "支付流水号",
                "unique": False,
                "description": "单行输入，需要填写者在输入框内根据标题填写相应的内容",
                "widget_type": "string",
                "fieldDescriptors": {"type": "string"},
                "required": False,
            },
            {
                "widget_id": 36,
                "key": "29accf3dc6854a33b367437499f4c7ce",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "29accf3dc6854a33b367437499f4c7ce",
                            "type": "string",
                            "title": "支付人",
                            "constraints": {"required": False},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 17,
                "id": 36,
                "category": 2,
                "widget_meta": [],
                "ref": ["product"],
                "icon": "ficon-text-input",
                "type": "string",
                "label": "支付人",
                "unique": False,
                "description": "单行输入，需要填写者在输入框内根据标题填写相应的内容",
                "widget_type": "string",
                "fieldDescriptors": {"type": "string"},
                "required": False,
            },
        ]

    def arguments(self):
        return [
            {
                "name": "amount",
                "label": "打款金额(输入)",
                "limits": [{"mode": "", "widget_type": "number"}],
                "required": True,
                "data_type": 2,
            },
            {
                "name": "receive_info",
                "label": "收款信息(输入)",
                "limits": [{"mode": "", "widget_type": "payment-method"}],
                "required": True,
                "data_type": 2,
            },
            {
                "name": "payment_reason",
                "label": "付款原因(输入)",
                "limits": [
                    {"mode": "", "widget_type": "string"},
                    {"mode": "single", "widget_type": "select"},
                    {"mode": "multiple", "widget_type": "select"},
                ],
                "required": True,
                "data_type": 2,
            },
            {
                "name": "comment",
                "label": "备注(输入)",
                "limits": [
                    {"mode": "", "widget_type": "textarea"},
                    {"mode": "", "widget_type": "string"},
                    {"mode": "single", "widget_type": "select"},
                    {"mode": "multiple", "widget_type": "select"},
                ],
                "required": False,
                "data_type": 2,
            },
            {
                "name": "pic_url",
                "label": "图片附件(输入)",
                "limits": [{"mode": "image", "widget_type": "upload"}],
                "required": False,
                "data_type": 2,
            },
            {
                "name": "transfer_status",
                "label": "打款状态(输入)",
                "options": [
                    {"type": "text", "label": "待审批", "value": 1},
                    {"type": "text", "label": "待付款", "value": 2},
                ],
                "required": True,
                "data_type": 4,
                "default_value": 1,
            },
            {
                "name": "tid",
                "label": "订单号(输入)",
                "limits": [
                    {"mode": "child", "widget_type": "order"},
                    {"mode": "order", "widget_type": "order"},
                ],
                "options": [],
                "required": True,
                "data_type": 2,
            },
        ]

    def expect(self):
        return {
            "sid": "********",
            "uid": "二团工部",
            "job_id": 335451708,
            "business_order_id": 84704728,
            "register_name": "织布鸟旗舰店:钊钊",
            "approve_timestamp": **********,
            "approve_name": "RPA应用",
            "transfer_type": 1,
            "status": 1,
            "payment_reason": [["破损"]],
            "payment_method": 3,
            "tid_list": [
                {
                    "tid": "3808433521087744400",
                    "oid": "3808433521087744400",
                    "alipay_no": "2024031022001190651423900290",
                }
            ],
            "amount": "38",
            "receive_name": None,
            "receive_account": None,
            "pic_url": [
                {
                    "uid": "image37",
                    "url": "https://prd-robot-processor.oss-cn-zhangjiakou.aliyuncs.com"
                           "/mini_sidebar/upload/image/310/********"
                           "/a4dcc5fc-67f9-418b-843b-9d07c698e6fe_image.png",
                    "name": "image.png",
                    "fileId": "image37",
                    "fileName": "a4dcc5fc-67f9-418b-843b-9d07c698e6fe_image.png",
                    "file_type": "image",
                    "key_in_oss": "mini_sidebar/upload/image/310/********"
                                  "/a4dcc5fc-67f9-418b-843b-9d07c698e6fe_image.png",
                }
            ],
        }

    def testcase(self):
        executor = self.executor()
        with patch(
            "robot_processor.job.alipay.robot_transfer.new_transfer",
            return_value={"success": False},
        ) as mock:
            executor.process()
        new_transfer_body = mock.call_args.args[0]
        expect = self.expect()
        assert new_transfer_body["transfer_type"] == expect["transfer_type"]
        assert new_transfer_body["status"] == expect["status"]
        assert new_transfer_body["payment_reason"] == expect["payment_reason"]
        assert new_transfer_body["tid_list"] == expect["tid_list"]
        assert new_transfer_body["amount"] == expect["amount"]
        assert new_transfer_body["receive_name"] == expect["receive_name"]
        assert new_transfer_body["receive_account"] == expect["receive_account"]
        assert new_transfer_body["pic_url"] == expect["pic_url"]


class TestCase2(CaseMixin):
    def executor(self):
        from robot_processor.job.ems_trace import EmsTraceExecutor

        job = MagicMock()
        order = MagicMock()
        job.business_order = order
        job.raw_step_v2 = {
            "key_map": self.key_map(),
            "task": {"arguments": self.arguments()},
        }
        order.data = self.bo_data()

        return EmsTraceExecutor(job)

    def bo_data(self):
        return {
            "tid": "240321-***************",
            "05fd701a-b3e0-4ea5-a95a-c8d40fa02b86": "南京仓",
            "5c890ffa-**************-5a19ff95f64d": [{"tid": "240321-***************"}],
            "b30dd987-4d6d-4f22-9c14-7b5096a2e023": "*************",
            "bd8bd0b0-6185-4cfb-9f26-43066e2c98b2": "拼多多-邮政-南京",
            "d6b26d9a-861a-40d0-a2ba-c5bed13c9772": [
                {
                    "uid": "*************",
                    "url": "https://prd-robot-processor.oss-cn-zhangjiakou.aliyuncs.com"
                           "/images/2052/4084b023253d0cb5d8775f474697cdbe.png",
                    "name": "*************-image.png",
                    "fileId": "*************",
                    "fileName": "*************-image.png",
                }
            ],
            "d936e94a-3e21-444f-a67f-8dc792404747": [
                {"label": "核实丢件", "value": "核实丢件"}
            ],
        }

    def widgets(self):
        return [
            {
                "widget_id": 13,
                "key": "5c890ffa-**************-5a19ff95f64d",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "5c890ffa-**************-5a19ff95f64d",
                            "type": "order",
                            "title": "订单/子订单",
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 0,
                "id": 13,
                "category": 1,
                "widget_meta": [],
                "icon": "ficon-single-trade-select",
                "type": "order",
                "label": "订单/子订单",
                "unique": True,
                "description": "订单/子订单，支持订单列表下拉选择或手动输入平台订单/子订单号",
                "widget_type": "table",
                "mode": "order",
                "multiInput": False,
                "required": True,
            },
            {
                "widget_id": 42,
                "key": "d936e94a-3e21-444f-a67f-8dc792404747",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "d936e94a-3e21-444f-a67f-8dc792404747",
                            "type": "string",
                            "title": "查件内容",
                            "constraints": {"required": True},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 1,
                "id": 42,
                "category": 2,
                "widget_meta": [],
                "icon": "ficon-radio-tiled",
                "type": "radio-tile",
                "label": "查件内容",
                "unique": False,
                "description": "平铺单选，可以自定义编辑和增删选项，组件选项视图方式为平铺。",
                "widget_type": "enum",
                "fieldDescriptors": {"type": "string"},
                "options": [
                    {"label": "催件", "value": "催件"},
                    {"label": "核实丢件", "value": "核实丢件"},
                    {"label": "改地址", "value": "改地址"},
                    {"label": "查重", "value": "查重"},
                    {"label": "虚假签收", "value": "虚假签收"},
                    {"label": "拦截", "value": "拦截"},
                ],
                "required": True,
            },
            {
                "widget_id": 41,
                "key": "d6b26d9a-861a-40d0-a2ba-c5bed13c9772",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "d6b26d9a-861a-40d0-a2ba-c5bed13c9772",
                            "type": "array",
                            "title": "交易截图",
                            "constraints": {"required": True},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 2,
                "id": 41,
                "category": 2,
                "widget_meta": [],
                "icon": "ficon-image-grid",
                "type": "upload",
                "label": "交易截图",
                "unique": False,
                "uploadType": "image",
                "description": "图片上传，可以在表单添加图片文件",
                "widget_type": "table",
                "fieldDescriptors": {"type": "array"},
                "mode": "image",
                "required": True,
            },
            {
                "widget_id": 21,
                "key": "b30dd987-4d6d-4f22-9c14-7b5096a2e023",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "b30dd987-4d6d-4f22-9c14-7b5096a2e023",
                            "type": "tracking-num",
                            "title": "快递单号",
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 3,
                "id": 21,
                "category": 1,
                "widget_meta": [],
                "icon": "ficon-express-number-input",
                "type": "tracking-num",
                "label": "快递单号",
                "unique": True,
                "description": "快递单号，根据当前表单登记订单号，自动同步平台订单中快递单号",
                "widget_type": "string",
                "required": True,
            },
            {
                "widget_id": 36,
                "key": "bd8bd0b0-6185-4cfb-9f26-43066e2c98b2",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "bd8bd0b0-6185-4cfb-9f26-43066e2c98b2",
                            "type": "string",
                            "title": "快麦快递模板",
                            "constraints": {"required": True},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 4,
                "id": 36,
                "category": 2,
                "widget_meta": [],
                "ref": ["product"],
                "icon": "ficon-text-input",
                "type": "string",
                "label": "快麦快递模板",
                "unique": False,
                "description": "单行输入，需要填写者在输入框内根据标题填写相应的内容",
                "widget_type": "string",
                "fieldDescriptors": {"type": "string"},
                "required": True,
            },
            {
                "widget_id": 36,
                "key": "05fd701a-b3e0-4ea5-a95a-c8d40fa02b86",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "05fd701a-b3e0-4ea5-a95a-c8d40fa02b86",
                            "type": "string",
                            "title": "快麦发货仓",
                            "constraints": {"required": True},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 5,
                "id": 36,
                "category": 2,
                "widget_meta": [],
                "ref": ["product"],
                "icon": "ficon-text-input",
                "type": "string",
                "label": "快麦发货仓",
                "unique": False,
                "description": "单行输入，需要填写者在输入框内根据标题填写相应的内容",
                "widget_type": "string",
                "fieldDescriptors": {"type": "string"},
                "required": True,
            },
            {
                "widget_id": 19,
                "key": "034cd268-7e87-46fa-8406-2d39c426911f",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "034cd268-7e87-46fa-8406-2d39c426911f",
                            "type": "address",
                            "title": "原地址",
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 6,
                "id": 19,
                "category": 1,
                "widget_meta": [
                    {"type": "string", "label": "姓名", "value": "name"},
                    {"type": "string", "label": "手机号", "value": "mobile"},
                    {"type": "string", "label": "详细地址", "value": "address"},
                ],
                "icon": "ficon-delivery-info-input",
                "type": "address",
                "label": "原地址",
                "unique": False,
                "conditions": [
                    {
                        "label": "省份等于",
                        "ui_type": "province_one",
                        "operator": "PROVINCE_EQ",
                        "ref_type": "address",
                        "value_type": "OBJECT",
                    },
                    {
                        "label": "省份等于任一",
                        "ui_type": "province_any",
                        "operator": "PROVINCE_IN",
                        "ref_type": "address",
                        "value_type": "OBJECT",
                    },
                    {
                        "label": "为空",
                        "ui_type": "no_widget",
                        "operator": "NOT_EXIST",
                        "ref_type": "address",
                        "value_type": "OBJECT",
                    },
                ],
                "description": "收货地址，地址组件，需手动输入省市区详细地址、姓名、电话号；淘系、抖音、拼多多平台和聚水潭ERP可在权限范围内登录RPA解密原地址。",
                "widget_type": "object",
                "mode": "new",
                "required": False,
            },
            {
                "widget_id": 19,
                "key": "6bb5242c-eea8-44f4-9619-00460d9ab3ef",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "6bb5242c-eea8-44f4-9619-00460d9ab3ef",
                            "type": "address",
                            "title": "新地址",
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 7,
                "id": 19,
                "category": 1,
                "widget_meta": [
                    {"type": "string", "label": "姓名", "value": "name"},
                    {"type": "string", "label": "手机号", "value": "mobile"},
                    {"type": "string", "label": "详细地址", "value": "address"},
                ],
                "icon": "ficon-delivery-info-input",
                "type": "address",
                "label": "新地址",
                "unique": False,
                "conditions": [
                    {
                        "label": "省份等于",
                        "ui_type": "province_one",
                        "operator": "PROVINCE_EQ",
                        "ref_type": "address",
                        "value_type": "OBJECT",
                    },
                    {
                        "label": "省份等于任一",
                        "ui_type": "province_any",
                        "operator": "PROVINCE_IN",
                        "ref_type": "address",
                        "value_type": "OBJECT",
                    },
                    {
                        "label": "为空",
                        "ui_type": "no_widget",
                        "operator": "NOT_EXIST",
                        "ref_type": "address",
                        "value_type": "OBJECT",
                    },
                ],
                "description": "收货地址，地址组件，需手动输入省市区详细地址、姓名、电话号；淘系、抖音、拼多多平台和聚水潭ERP可在权限范围内登录RPA解密原地址。",
                "widget_type": "object",
                "mode": "new",
                "required": False,
            },
        ]

    def key_map(self):
        return {
            "image": "d6b26d9a-861a-40d0-a2ba-c5bed13c9772",
            "remark": [
                {"text": "", "type": "text"},
                {
                    "key": "d936e94a-3e21-444f-a67f-8dc792404747",
                    "type": "concept",
                    "label": "查件内容",
                    "stepName": "登记",
                },
                {"text": "", "type": "text"},
            ],
            "sender": {"value": "RANDOM_ONLINE"},
            "ems_sid": "1100151223589",
            "mail_info": [
                {"text": "", "type": "text"},
                {
                    "key": "034cd268-7e87-46fa-8406-2d39c426911f",
                    "type": "concept",
                    "label": "原地址",
                    "stepName": "登记",
                },
                {"text": "", "type": "text"},
            ],
            "query_info": "其他",
            "mail_number": "b30dd987-4d6d-4f22-9c14-7b5096a2e023",
        }

    def arguments(self):
        return [
            {
                "name": "mail_number",
                "label": "邮件号（输入）",
                "limits": [
                    {"mode": "", "widget_type": "string"},
                    {"mode": "", "widget_type": "tracking-num"},
                ],
                "options": [],
                "required": True,
                "data_type": 2,
            },
            {
                "desc": "邮政ID为登录RPA客户端邮政账号后展示的SID，将SID复制到该字段。",
                "name": "ems_sid",
                "label": "邮政ID（输入）",
                "options": [],
                "required": True,
                "data_type": 3,
            },
            {
                "desc": "若已创建查件，追加评价时不重复提交邮件信息。",
                "name": "mail_info",
                "label": "邮件信息（输入）",
                "options": [],
                "required": True,
                "data_type": 5,
            },
            {
                "name": "query_info",
                "label": "查件内容（输入）",
                "options": [
                    {"type": "text", "label": "撤回(拦截)", "value": "撤回"},
                    {"type": "text", "label": "改址", "value": "修改信息"},
                    {"type": "text", "label": "物流延误(催件)", "value": "物流延误"},
                    {"type": "text", "label": "无物流", "value": "无物流信息"},
                    {"type": "text", "label": "虚假签收", "value": "虚假签收"},
                    {"type": "text", "label": "破损", "value": "破损"},
                    {"type": "text", "label": "其他", "value": "其他"},
                ],
                "required": True,
                "data_type": 4,
            },
            {
                "desc": "首次发起查件时，为备注内容；非首次查件时，为追加内容。",
                "name": "remark",
                "label": "备注",
                "options": [],
                "required": True,
                "data_type": 5,
            },
            {
                "desc": "可引用前序图片组件，前序图片组件可非必填",
                "name": "image",
                "label": "发送图片（输入）",
                "limits": [{"uploadType": "image", "widget_type": "upload"}],
                "options": [],
                "required": False,
                "data_type": 8,
            },
            {
                "name": "sender",
                "label": "发送账号",
                "options": [
                    {
                        "desc": "随机在线账号：系统随机选择一个RPA登录的邮政账号发送消息。",
                        "type": "text",
                        "label": "随机在线客服",
                        "value": {"value": "RANDOM_ONLINE"},
                        "default": True,
                    },
                    {
                        "desc": "指定账号：填写邮政大客户查件登录的用户名。选择多个账号时，系统将在候选人中随机选择一个在线的账号发送消息。",
                        "type": "text",
                        "label": "指定账号",
                        "value": {"value": "MANUAL_INPUT"},
                    },
                ],
                "required": True,
                "data_type": 6,
            },
        ]

    def expect(self):
        return {
            "sender": "",
            "payload": dict(
                mailNumber="*************",
                mailInfo="",
                queryInfo="其他",
                remark="核实丢件",
                imgPathList=[
                    "https://prd-robot-processor.oss-cn-zhangjiakou.aliyuncs.com"
                    "/images/2052/4084b023253d0cb5d8775f474697cdbe.png"
                ],
            ),
        }

    def testcase(self):
        executor = self.executor()
        with patch(
            "robot_processor.job.ems_trace.MolaClient.post_ems_trace",
            return_value=Err(""),
        ) as mock:
            executor.process()
        expect = self.expect()
        assert mock.call_args.kwargs["sender"] == expect["sender"]
        assert (
            mock.call_args.kwargs["payload"]["mailNumber"]
            == expect["payload"]["mailNumber"]
        )
        assert (
            mock.call_args.kwargs["payload"]["mailInfo"]
            == expect["payload"]["mailInfo"]
        )
        assert (
            mock.call_args.kwargs["payload"]["queryInfo"]
            == expect["payload"]["queryInfo"]
        )
        assert mock.call_args.kwargs["payload"]["remark"] == expect["payload"]["remark"]
        assert (
            mock.call_args.kwargs["payload"]["imgPathList"]
            == expect["payload"]["imgPathList"]
        )


class TestCase3(CaseMixin):
    """bo_id = 84767054"""

    def executor(self):
        from robot_processor.job.qianniu import QianniuExecutor

        job = MagicMock()
        order = MagicMock()
        job.business_order = order
        job.raw_step_v2 = {
            "key_map": self.key_map(),
            "task": {"arguments": self.arguments()},
        }
        order.data = self.bo_data()

        return QianniuExecutor(job)

    def bo_data(self):
        return {
            "oid": "2098775461364601559",
            "tid": "2098775461364601559",
            "system_business_order_end_at": "2024-03-28 17:36:33",
            "system_business_order_status": "已完成",
            "system_business_order_deadline": "2024-03-28 12:02:57",
            "system_business_order_form_name": "图书少发、漏发、错发、严重破损污渍",
            "system_business_order_shop_nick": "中公教育旗舰店",
            "system_business_order_created_at": "2024-03-28 12:02:57",
            "system_business_order_updated_at": "2024-03-28 17:36:33",
            "system_business_order_receipt_url": "",
            "system_business_order_update_user": "果酱",
            "2489c0b7-4dc3-4075-8236-c6ce307f9b6c": "tb58657189",
            "8b7a746c-1b4f-4282-a902-11f416776fc8": "买家要赔偿10元",
            "c7b2f0d1-8ba0-4a87-b38e-a3ee8e5f562d": [{"tid": "2098775461364601559"}],
            "system_business_order_feisuo_creator": "金玉良言图书专营店:果酱",
            "system_business_order_feisuo_updator": "中公教育旗舰店:众卿",
            "system_business_order_creator_platform": "中公教育旗舰店:果酱",
            "system_business_order_current_step_name": "发消息11",
            "system_business_order_creator_group_string": "",
            "system_business_order_updator_group_string": "",
            "system_business_order_shop_platform_chinese": "天猫",
            "system_business_order_current_job_assignee_feisuo": "",
            "system_business_order_feisuo_creator_group_string": "",
            "system_business_order_feisuo_updator_group_string": "",
            "system_business_order_current_job_exception_reason": "未知错误",
            "system_business_order_current_job_assignee_platform": "中公教育旗舰店:果酱",
            "system_business_order_current_job_assignee_group_feisuo_string": "",
            "system_business_order_current_job_assignee_group_platform_string": "",
        }

    def key_map(self):
        return {
            "branch": {
                "single": {
                    "sender": {"value": "RANDOM_ONLINE"},
                    "content": [{"text": "这边帮您登记核实，预计24小时内给您回复。", "type": "text"}],
                    "usernick": {
                        "key": "2489c0b7-4dc3-4075-8236-c6ce307f9b6c",
                        "type": "usernick",
                        "field": None,
                        "multi_row": False,
                        "widget_type": "string",
                    },
                    "send_channel": {"value": "CHAT_CLIENT"},
                },
                "send_method": "same_send_same",
                "branch_status": "single",
                "same_send_same": {
                    "multiple": [
                        {
                            "id": "996b36b1-85f3-4b0e-8241-93c71b711a9c",
                            "name": "规则1",
                            "next": "",
                            "skip": False,
                            "type": "NORMAL",
                            "order": 1,
                            "enable": True,
                            "condition_group": {
                                "relation": "AND",
                                "condition_list": [{"data": {}, "type": "single"}],
                            },
                        },
                        {
                            "id": "d7fcb0e9-ef31-43d2-a72e-6d27f9585431",
                            "name": "兜底规则",
                            "next": "",
                            "skip": False,
                            "type": "DEFAULT",
                            "order": 9999,
                            "enable": True,
                        },
                    ]
                },
                "different_send_same": {},
            }
        }

    def arguments(self):
        return [
            {
                "name": "branch",
                "label": "发送账号(输入)",
                "commons": [
                    {
                        "desc": None,
                        "name": "usernick",
                        "label": "买家昵称(输入)",
                        "limits": [{"mode": "", "widget_type": "usernick"}],
                        "options": None,
                        "required": True,
                        "data_type": 2,
                    }
                ],
                "options": [
                    {"tips": "单消息发送", "label": "单消息规则配置", "value": "single"},
                    {"tips": "多消息发送", "label": "多消息规则配置", "value": "multiple"},
                ],
                "required": True,
                "arguments": [
                    {
                        "name": "content",
                        "label": "发送内容",
                        "options": [],
                        "required": True,
                        "data_type": 5,
                    },
                    {
                        "desc": "根据平台规则及避免账号风控，仅支持发送一张图片",
                        "name": "image",
                        "label": "发送图片",
                        "limits": [
                            {"uploadType": "image", "widget_type": "upload"},
                            {"widget_type": "receipt_url"},
                        ],
                        "options": [],
                        "required": False,
                        "data_type": 8,
                    },
                ],
                "data_type": 10,
                "optionals": [
                    {
                        "name": "sender",
                        "label": "发送账号",
                        "options": [
                            {
                                "desc": "随机在线账号：系统随机选择一个RPA登录的千牛账号发送消息。",
                                "type": "text",
                                "label": "随机在线客服",
                                "value": {"value": "RANDOM_ONLINE"},
                                "default": True,
                                "extra_source": None,
                            },
                            {
                                "desc": "",
                                "type": "text",
                                "label": "任务创建人",
                                "value": {"value": "CREATOR"},
                                "extra_source": None,
                            },
                            {
                                "desc": "指定账号：支持选择当前店铺平台子账号或平台子账号组。选择多个账号时，系统将在候选人中随机选择一个在线的账号发送消息。",
                                "type": "text",
                                "label": "指定账号",
                                "value": {
                                    "extra": {"details": [], "assignee_groups": []},
                                    "value": "FIXED",
                                },
                                "extra_source": "select_user",
                            },
                            {
                                "desc": "",
                                "type": "text",
                                "label": "最近更新人",
                                "value": {"value": "UPDATER"},
                                "extra_source": None,
                            },
                            {
                                "desc": "由最近接待的客服帐号发送给买家消息，需要订阅乐语助人机器人才可使用",
                                "type": "text",
                                "label": "最近接待人",
                                "value": {"value": "LAST_RECEPTION"},
                                "extra_source": None,
                            },
                        ],
                        "required": True,
                        "condition": [
                            {"value": "different_send_same", "depend": "send_method"}
                        ],
                        "data_type": 6,
                    },
                    {
                        "desc": None,
                        "name": "send_channel",
                        "label": "发送渠道",
                        "options": [
                            {
                                "desc": "",
                                "type": "text",
                                "label": "RPA客户端",
                                "value": {"value": "RPA_CLIENT"},
                                "default": True,
                                "extra_source": None,
                            },
                            {
                                "desc": "",
                                "type": "text",
                                "label": "乐言智能客服客户端",
                                "value": {"value": "CHAT_CLIENT"},
                                "extra_source": None,
                            },
                        ],
                        "required": False,
                        "condition": [
                            {"value": "different_send_same", "depend": "send_method"}
                        ],
                        "data_type": 6,
                    },
                ],
                "send_method": [
                    {
                        "info": "多规则消息由同一账号发送",
                        "tips": "由同一个账号按照多消息的规则匹配来发送消息",
                        "label": "同一账号发送",
                        "value": "same_send_same",
                    },
                    {
                        "info": "多规则消息由不同账号发送",
                        "tips": "在不同的消息规则下可配置不同的账号来发送消息",
                        "label": "不同账号发送",
                        "value": "different_send_same",
                    },
                ],
            }
        ]

    def widgets(self):
        return [
            {
                "widget_id": 10,
                "key": "2489c0b7-4dc3-4075-8236-c6ce307f9b6c",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "2489c0b7-4dc3-4075-8236-c6ce307f9b6c",
                            "type": "usernick",
                            "title": "买家昵称",
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 0,
                "id": 10,
                "category": 1,
                "widget_meta": [],
                "icon": "ficon-buyer-nick-input",
                "type": "usernick",
                "label": "买家昵称",
                "unique": True,
                "description": "买家昵称，根据订单号支持自动赋值；若无自动赋值，需用户手动输入。淘系平台可获取明文买家昵称，拼多多和抖音获取加密买家昵称",
                "widget_type": "string",
                "required": True,
            },
            {
                "widget_id": 13,
                "key": "c7b2f0d1-8ba0-4a87-b38e-a3ee8e5f562d",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "c7b2f0d1-8ba0-4a87-b38e-a3ee8e5f562d",
                            "type": "order",
                            "title": "订单/子订单",
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 1,
                "id": 13,
                "category": 1,
                "widget_meta": [],
                "icon": "ficon-single-trade-select",
                "type": "order",
                "label": "订单/子订单",
                "unique": True,
                "description": "订单/子订单，支持订单列表下拉选择或手动输入平台订单/子订单号",
                "widget_type": "table",
                "mode": "order",
                "multiInput": False,
                "required": True,
            },
            {
                "widget_id": 37,
                "key": "8b7a746c-1b4f-4282-a902-11f416776fc8",
                "before": False,
                "schema_value": {},
                "data_schema": {
                    "fields": [
                        {
                            "name": "8b7a746c-1b4f-4282-a902-11f416776fc8",
                            "type": "string",
                            "title": "买家反馈具体情况",
                            "constraints": {"required": False},
                        }
                    ],
                    "multi_row": False,
                },
                "ref_config": [],
                "order": 2,
                "id": 37,
                "category": 2,
                "widget_meta": [],
                "icon": "ficon-gdpz-Multi-input",
                "type": "textarea",
                "label": "买家反馈具体情况",
                "unique": False,
                "description": "多行输入，需要填写者在输入框内根据标题填写相应的内容，支持换行输入",
                "widget_type": "string",
                "fieldDescriptors": {"type": "string"},
                "required": False,
            },
        ]

    def expect(self):
        return ("这边帮您登记核实，预计24小时内给您回复。", [], [])

    def testcase(self):
        executor = self.executor()
        with patch(
            "robot_processor.job.qianniu.QianniuExecutor.send_message_by_chat_client",
        ) as mock:
            executor.process()
        assert mock.call_args.args == self.expect()
        assert mock.call_args.kwargs == {"contact": "tb58657189", "tid": None}
