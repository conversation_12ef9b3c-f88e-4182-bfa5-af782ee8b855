from leyan_proto.digismart.robot.symbol_table_pb2 import Filter
from robot_processor.symbol_table.models import Value
from robot_processor.symbol_table.services import FilterServicer
from robot_processor.symbol_table.filters import OperandEnum


def test_string_exists():
    operand = OperandEnum.STRING_EXISTS.operand
    filter_ = Filter(conditions=[Filter.Condition(o=[operand.o.pb_value])])

    a = Value.ConstValue(operand.a, None).to_pb()
    filter_.conditions[0].a.MergeFrom(a)
    assert FilterServicer.evaluate(filter_) is False

    a = Value.ConstValue(operand.a, "").to_pb()
    filter_.conditions[0].a.MergeFrom(a)
    assert FilterServicer.evaluate(filter_) is True


def test_string_not_exists():
    operand = OperandEnum.STRING_NOT_EXISTS.operand
    filter_ = Filter(conditions=[Filter.Condition(o=[operand.o.pb_value])])

    a = Value.ConstValue(operand.a, None).to_pb()
    filter_.conditions[0].a.<PERSON><PERSON>(a)
    assert FilterServicer.evaluate(filter_) is True

    a = Value.ConstValue(operand.a, "").to_pb()
    filter_.conditions[0].a.MergeFrom(a)
    assert FilterServicer.evaluate(filter_) is False


def test_datetime_lt():
    operand = OperandEnum.DATETIME_LT.operand
    filter_ = Filter(conditions=[Filter.Condition(o=[operand.o.pb_value])])

    a = Value.ConstValue(operand.a, "2021-01-01 00:00:00").to_pb()
    b = Value.ConstValue(operand.a, "2023-01-01 00:00:00").to_pb()
    filter_.conditions[0].a.MergeFrom(a)
    filter_.conditions[0].b.MergeFrom(b)
    assert FilterServicer.evaluate(filter_) is True

    a = Value.ConstValue(operand.a, "2023-01-01 00:00:00").to_pb()
    b = Value.ConstValue(operand.a, "2021-01-01 00:00:00").to_pb()
    filter_.conditions[0].a.MergeFrom(a)
    filter_.conditions[0].b.MergeFrom(b)
    assert FilterServicer.evaluate(filter_) is False
