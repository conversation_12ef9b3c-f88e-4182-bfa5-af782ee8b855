"""robot_processor.symbol_table.api.get_symbol_operators"""

from robot_processor.symbol_table.api import BizType
from robot_processor.symbol_table.api import GetSymbolOperators
from robot_processor.symbol_table.api import ValueForOperator


def test_string(client):
    type_spec = {"type": "string"}

    response = client.post(
        "/v1/symbol-table/operators",
        json=GetSymbolOperators(
            biz_type=BizType.BO_FORM_VALIDATOR,
            value=ValueForOperator(type_spec=type_spec, var={"path": ""}),
        ).dict(),
    )
    assert response.json["succeed"] is True
    operators = {operator["operator"]: operator for operator in response.json["data"]["operators"]}
    assert operators["eq"]["label"] == "等于"
    assert operators["eq"]["type_spec"]["type"] == "string"


def test_array_string(client):
    type_spec = {"type": "array", "spec": [{"type": "string"}]}
    response = client.post(
        "/v1/symbol-table/operators",
        json=GetSymbolOperators(
            biz_type=BizType.BO_FORM_VALIDATOR,
            value=ValueForOperator(type_spec=type_spec, var={"path": ""}),
        ).dict(),
    )
    assert response.json["succeed"] is True
    operators = {operator["operator"]: operator for operator in response.json["data"]["operators"]}
    assert set(operators.keys()) == {
        "is_not_empty",
        "all",
        "value_recorded",
        "is_empty",
        "any",
    }
