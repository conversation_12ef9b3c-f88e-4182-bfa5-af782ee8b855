from pytest import fixture


from robot_processor.ext import db
from robot_processor.openapi.models import OpenApiCredentials


class RequestMixin:
    def _request_with_sign(self, client, qs: dict, body: dict, app_secret: str):
        from robot_processor.openapi.utils import sign

        qs["sign"] = sign(
            qs["app_key"], app_secret, qs["method"], qs["timestamp"], body
        )
        return client.post("/v1/open", query_string=qs, json=body)

    @fixture
    def credential(self, client):
        credential = OpenApiCredentials()
        credential.org_id = client.org_id
        db.session.add(credential)
        db.session.commit()

        yield credential
