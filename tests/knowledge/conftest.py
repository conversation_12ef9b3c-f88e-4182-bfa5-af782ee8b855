import pytest

from robot_processor.knowledge.enums import KnowledgeBaseRolePrivilege
from robot_processor.knowledge.enums import StaffMold


@pytest.fixture
def mock_admin_role(client, knowledge_base_role_factory, knowledge_base_role_to_staff_mapping_factory):
    admin_role = knowledge_base_role_factory(org_id=client.org_id, privileges=KnowledgeBaseRolePrivilege.members())
    knowledge_base_role_to_staff_mapping_factory.create(
        role_id=admin_role.id, mold=StaffMold.ACCOUNT, tag=str(client.assistant.feisuo_user_id)
    )
    yield admin_role
