import time

from robot_processor.knowledge.models import KnowledgeBase
from robot_processor.knowledge.models import Project


def test_knowledge(
    client, mock_admin_role, knowledge_base_to_shop_mapping_factory, knowledge_base_role_to_staff_mapping_factory
):
    resp = client.get(f"/v1/knowledge/knowledge_bases?channel_id={client.shop.channel_id}")
    assert resp.get_json().get("total") == 0

    resp = client.post(
        "/v1/knowledge/knowledge_bases",
        json={
            "icon": "on sea",
            "name": "仰齐浜",
            "intro": "sea high! o7",
            "channel_id": client.shop.channel_id,
        },
    )
    assert resp.status_code == 200

    resp = client.get(f"/v1/knowledge/knowledge_bases?channel_id={client.shop.channel_id}")
    assert resp.get_json().get("total") == 1
    assert len(resp.get_json().get("data")) != 0
    assert resp.get_json().get("data")[0]["name"] == "仰齐浜"
    assert resp.get_json().get("data")[0]["intro"] == "sea high! o7"

    knowledge_base_id = resp.get_json().get("data")[0]["id"]

    resp = client.delete(
        "/v1/knowledge/knowledge_bases/{}".format(knowledge_base_id),
        json={
            "reason": "为了荣都！",
            "channel_id": client.shop.channel_id,
        },
    )
    assert resp.status_code == 200

    resp = client.get(f"/v1/knowledge/knowledge_bases?channel_id={client.shop.channel_id}")
    assert resp.get_json().get("total") == 0

    resp = client.get(f"/v1/knowledge/rubbishes?channel_id={client.shop.channel_id}")
    assert len(resp.get_json().get("data")) == 1

    resp = client.put(
        f"/v1/knowledge/rubbishes/{knowledge_base_id}",
        json={
            "channel_id": client.shop.channel_id,
        },
    )
    assert resp.get_json().get("success")

    resp = client.get(f"/v1/knowledge/knowledge_bases?channel_id={client.shop.channel_id}")
    assert resp.get_json().get("total") == 1
    assert len(resp.get_json().get("data")) != 0
    assert resp.get_json().get("data")[0]["name"] == "仰齐浜"
    assert resp.get_json().get("data")[0]["intro"] == "sea high! o7"

    resp = client.delete(
        f"/v1/knowledge/knowledge_bases/{knowledge_base_id}?channel_id={client.shop.channel_id}",
        json={
            "reason": "",
            "channel_id": client.shop.channel_id,
        },
    )
    assert resp.status_code == 200


def test_knowledge_without_privileges(
    client, client_contract_expired, mock_admin_role, knowledge_base_to_shop_mapping_factory
):
    resp = client.post(
        "/v1/knowledge/knowledge_bases",
        json={"icon": "sea", "name": "仰浜", "intro": "sea high!", "channel_id": client.shop.channel_id},
    )
    assert resp.status_code == 200

    resp = client.get(f"/v1/knowledge/knowledge_bases?channel_id={client.shop.channel_id}")
    knowledge_base_id = resp.get_json().get("data")[0]["id"]

    kb = KnowledgeBase.query.filter(KnowledgeBase.id == knowledge_base_id).first()
    kb.deleted_at = int(time.time())

    resp = client_contract_expired.put(
        f"/v1/knowledge/rubbishes/{knowledge_base_id}",
        json={
            "channel_id": client_contract_expired.shop.channel_id,
        },
    )
    assert resp.get_json().get("success") is False
    assert resp.get_json().get("message") == "角色无权恢复"


def test_category(
    client,
    mock_admin_role,
    knowledge_base_factory,
):
    knowledge_base = knowledge_base_factory(
        org_id=client.org_id,
        creator_id=client.leyan_assistant.user_id,
        updator_id=client.leyan_assistant.user_id,
    )

    resp = client.get(f"/v1/knowledge/knowledge_bases/{knowledge_base.id}/projects?channel_id={client.shop.channel_id}")
    assert resp.get_json().get("total") == 0

    resp = client.post(
        f"/v1/knowledge/knowledge_bases/{knowledge_base.id}/categories",
        json={
            "name": "册那",
            "knowledge_base_id": knowledge_base.id,
            "channel_id": client.shop.channel_id,
        },
    )

    resp = client.get(f"/v1/knowledge/knowledge_bases/{knowledge_base.id}/projects?channel_id={client.shop.channel_id}")
    assert resp.get_json().get("total") == 1

    resp = client.post(
        f"/v1/knowledge/knowledge_bases/{knowledge_base.id}/categories",
        json={
            "name": "小喇叭",
            "knowledge_base_id": knowledge_base.id,
            "channel_id": client.shop.channel_id,
        },
    )
    resp = client.post(
        f"/v1/knowledge/knowledge_bases/{knowledge_base.id}/categories",
        json={
            "name": "丢雷楼某",
            "knowledge_base_id": knowledge_base.id,
            "channel_id": client.shop.channel_id,
        },
    )

    resp = client.get(f"/v1/knowledge/knowledge_bases/{knowledge_base.id}/projects?channel_id={client.shop.channel_id}")
    assert resp.get_json().get("total") == 3

    projects: list[Project] = (
        Project.query.filter(
            Project.knowledge_base_id == knowledge_base.id,
            Project.org_id == client.org_id,
            Project.parent_id == 0,
            Project.deleted_at.is_(None),
        )
        .order_by(Project.id.asc())
        .all()
    )

    first_project, second_project, third_project = projects[0], projects[1], projects[2]
    assert third_project.prev_id == 0
    assert second_project.prev_id == third_project.id
    assert first_project.next_id == 0

    # 第一个项目移动到第二个项目后面
    resp = client.put(
        f"/v1/knowledge/knowledge_bases/{knowledge_base.id}/projects/{first_project.id}/locations",
        json={"target_id": third_project.id, "action": "MOVE_AFTER", "channel_id": client.shop.channel_id},
    )

    projects: list[Project] = (
        Project.query.filter(
            Project.knowledge_base_id == knowledge_base.id,
            Project.org_id == client.org_id,
            Project.parent_id == 0,
            Project.deleted_at.is_(None),
        )
        .order_by(Project.id.asc())
        .all()
    )

    first_project, second_project, third_project = projects[0], projects[1], projects[2]
    assert third_project.prev_id == 0
    assert third_project.next_id == first_project.id
    assert first_project.prev_id == third_project.id
    assert first_project.next_id == second_project.id
    assert second_project.prev_id == first_project.id
    assert second_project.next_id == 0

    # 在第二个项目里新建一个项目
    resp = client.post(
        f"/v1/knowledge/knowledge_bases/{knowledge_base.id}/categories",
        json={
            "name": "叉烧",
            "knowledge_base_id": knowledge_base.id,
            "parent_id": second_project.id,
            "channel_id": client.shop.channel_id,
        },
    )

    resp = client.get(f"/v1/knowledge/knowledge_bases/{knowledge_base.id}/projects?channel_id={client.shop.channel_id}")
    assert resp.get_json().get("total") == 4

    resp = client.get(
        f"/v1/knowledge/knowledge_bases/{knowledge_base.id}/projects"
        f"?parent_id={second_project.id}&channel_id={client.shop.channel_id}"
    )
    assert resp.get_json().get("total") == 1

    latest_project: Project = (
        Project.query.filter(
            Project.knowledge_base_id == knowledge_base.id,
            Project.org_id == client.org_id,
            Project.deleted_at.is_(None),
        )
        .order_by(Project.id.desc())
        .first()
    )

    resp = client.delete(
        f"/v1/knowledge/knowledge_bases/{knowledge_base.id}/projects/{latest_project.id}",
        json={"reason": "我不吃叉烧", "channel_id": client.shop.channel_id},
    )

    resp = client.get(f"/v1/knowledge/trashes?channel_id={client.shop.channel_id}")
    assert len(resp.get_json().get("data")) == 1

    # 第三个项目移动到第二个项目里面
    resp = client.put(
        f"/v1/knowledge/knowledge_bases/{knowledge_base.id}/projects/{third_project.id}/locations",
        json={"target_id": second_project.id, "action": "ADD_CHILD", "channel_id": client.shop.channel_id},
    )

    projects: list[Project] = (
        Project.query.filter(
            Project.knowledge_base_id == knowledge_base.id,
            Project.org_id == client.org_id,
            Project.deleted_at.is_(None),
        )
        .order_by(Project.id.asc())
        .all()
    )

    first_project, second_project, third_project = projects[0], projects[1], projects[2]
    assert first_project.prev_id == 0
    assert first_project.next_id == second_project.id
    assert second_project.prev_id == first_project.id
    assert second_project.next_id == 0
    assert third_project.parent_id == second_project.id
    assert third_project.prev_id == 0
    assert third_project.next_id == 0

    resp = client.put(
        f"/v1/knowledge/trashes/{latest_project.id}",
        json={
            "move_to_project_id": first_project.id,
            "move_to_knowledge_base_id": first_project.knowledge_base_id,
            "move_action": "MOVE_BEFORE",
            "channel_id": client.shop.channel_id,
        },
    )

    projects: list[Project] = (
        Project.query.filter(
            Project.knowledge_base_id == knowledge_base.id,
            Project.org_id == client.org_id,
            Project.deleted_at.is_(None),
        )
        .order_by(Project.id.asc())
        .all()
    )

    first_project, second_project, third_project, forth_project = projects[0], projects[1], projects[2], projects[3]

    assert forth_project.prev_id == 0
    assert forth_project.next_id == first_project.id
    assert forth_project.parent_id == 0
    assert first_project.prev_id == forth_project.id
    assert first_project.next_id == second_project.id
    assert second_project.prev_id == first_project.id
    assert second_project.next_id == 0
    assert third_project.parent_id == second_project.id
    assert third_project.prev_id == 0
    assert third_project.next_id == 0
