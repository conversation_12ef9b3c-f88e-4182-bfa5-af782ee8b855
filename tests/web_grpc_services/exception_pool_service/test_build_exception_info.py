from robot_processor.web_grpc_services.exception_pool import ExceptionPoolService


class TestExceptionPoolBuildExceptionInfo:
    def test_build(self):
        exception_info_obj = {
            "id": 1,
            "type": "UT",
            "reason": "这是异常原因",
            "suggestion": "这是建议",
        }

        exception_info = ExceptionPoolService.build_exception_info(exception_info_obj)
        assert exception_info.id == 1
        assert exception_info.type == "UT"
        assert exception_info.reason == "这是异常原因"
        assert exception_info.suggestion == "这是建议"
