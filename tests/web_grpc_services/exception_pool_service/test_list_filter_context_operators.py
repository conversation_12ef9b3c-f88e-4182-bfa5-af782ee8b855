from pytest import fixture
from leyan_proto.digismart.robot_web.exception_pool_pb2 import ListFilterContextOperatorsRequest
from leyan_proto.digismart.robot.symbol_table_pb2 import Filter as pb_Filter
from leyan_proto.digismart.robot.symbol_table_pb2 import Value as pb_Value
from robot_types.helper import serialize
from robot_types.model import exception_pool
from robot_processor.utils import struct_wrapper


@fixture(autouse=True)
def setup(client):
    yield


def testcase(mock_service, mock_context):
    request = ListFilterContextOperatorsRequest(
        value=pb_Value(
            type_spec=struct_wrapper(
                serialize(exception_pool.type_spec.FilterContext.properties["business_order_id"])
            ),
            var=pb_Value.Var(path="business_order_id"),
        )
    )
    response = mock_service.ListFilterContextOperators(request, mock_context)
    assert len(response.data.operators) == 2
    assert response.data.operators[0].label == "等于"
    assert response.data.operators[0].operator == pb_Filter.EQ
    assert response.data.operators[0].expect["type"] == "string"
    assert response.data.operators[1].label == "等于任一"
    assert response.data.operators[1].operator == pb_Filter.EQ_ANY
    assert response.data.operators[1].expect["type"] == "array"
