from leyan_proto.digismart.robot_web.exception_pool_pb2 import Job<PERSON><PERSON>

from robot_processor.web_grpc_services.exception_pool import ExceptionPoolService


class TestExceptionPoolBuildJobBrief:
    def test_build(self):
        job_brief_obj = {
            "job_id": 123,
            "step_id": 123,
            "uuid": "a-b-c-d",
            "name": "ut",
            "rpa_name": "ut",
            "type": "AUTO",
        }

        job_brief = ExceptionPoolService.build_job_brief(job_brief_obj)
        assert job_brief.job_id == "123"
        assert job_brief.step_id == "123"
        assert job_brief.uuid == "a-b-c-d"
        assert job_brief.name == "ut"
        assert job_brief.rpa_name == "ut"
        assert job_brief.type == JobBrief.NodeType.AUTO
