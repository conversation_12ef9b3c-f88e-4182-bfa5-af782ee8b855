from dataclasses import dataclass
from unittest.mock import MagicMock

import arrow
from factory.fuzzy import Fu<PERSON><PERSON><PERSON>ger
from leyan_proto.digismart.robot_web.exception_pool_pb2 import (
    ListExceptionPoolRequest,
    PageInfo,
    PageSort,
)
from more_itertools import first
from pytest import fixture, mark

from robot_processor.business_order.exception_rule import ExceptionBusinessOrder
from robot_processor.web_grpc_services.exception_pool import ExceptionPoolService


@dataclass
class MockedPatch:
    service = ExceptionPoolService()
    context = MagicMock()
    request: ListExceptionPoolRequest = None


@mark.order(1)
class TestListExceptionPool:
    mocked_patch = MockedPatch()
    fuzzy_int = FuzzyInteger(1, 1000)

    @fixture(autouse=True)
    def setup_base(self, client):
        yield

    @fixture(autouse=True)
    def setup_grpc_request(self):
        self.mocked_patch.request = ListExceptionPoolRequest(
            page=PageInfo(page=1, per_page=10)
        )
        yield

    @fixture
    def default_exception_bo_obj(self, client):
        return dict(
            org_id=client.shop.org_id,
            sid=client.shop.sid,
            shop_name=client.shop.title,
            shop_platform=client.shop.platform,
            sys_type=2,
            name="ut",
            bo_info={},
            business_order_id=1,
            current_job_id=1,
            current_job_name="",
            current_job_info={},
            reason="",
            exception_info={},
        )

    def test_pagination(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request
        request.page.per_page = self.fuzzy_int.fuzz()

        response = service.ListExceptionPool(request, context)

        # 检查 pagination 保留了下来
        assert response.data.page.page == request.page.page
        assert response.data.page.per_page == request.page.per_page

    @fixture
    def setup_filter_by_shop(self, default_exception_bo_obj, db):
        # 当前店铺的异常工单
        db.session.add(ExceptionBusinessOrder(**default_exception_bo_obj))
        # 非当前店铺的异常工单
        db.session.add(
            ExceptionBusinessOrder(**{**default_exception_bo_obj, "sid": "ut"})
        )
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_shop")
    def test_filter_by_shop(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是当前店铺的
        assert response.data.page.total == 1

    @fixture
    def setup_filter_by_bo_id(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        db.session.add(
            ExceptionBusinessOrder(
                **{**default_exception_bo_obj, "business_order_id": 1}
            )
        )
        # 不符合条件的异常工单
        db.session.add(
            ExceptionBusinessOrder(
                **{**default_exception_bo_obj, "business_order_id": 2}
            )
        )
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_bo_id")
    def test_filter_by_bo_id(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.business_order_id = "1"
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert (
            first(response.data.page_data).business_order_id
            == request.business_order_id
        )

    @fixture
    def setup_filter_by_form_name(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        db.session.add(
            ExceptionBusinessOrder(**{**default_exception_bo_obj, "name": "ut"})
        )
        # 不符合条件的异常工单
        db.session.add(
            ExceptionBusinessOrder(**{**default_exception_bo_obj, "name": "ut2"})
        )
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_form_name")
    def test_filter_by_form_name(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.form_name = "ut"
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert first(response.data.page_data).business_order_name == request.form_name

    @fixture
    def setup_filter_by_reason(self, default_exception_bo_obj, db):
        db.session.add(
            ExceptionBusinessOrder(
                **{
                    **default_exception_bo_obj,
                    "reason": "ut",
                    "exception_info": {"reason": "ut"},
                }
            )
        )
        # 不符合条件的异常工单
        db.session.add(
            ExceptionBusinessOrder(
                **{**default_exception_bo_obj, "reason": "other"}
            )
        )
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_reason")
    def test_filter_by_reason(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.reason = "ut"
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert first(response.data.page_data).exception_info.reason == request.reason

    @fixture
    def setup_filter_by_creator(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        exception_obj = {
            **default_exception_bo_obj,
            "bo_info": {"platform_creator": {"nick": "ut"}},
        }
        db.session.add(ExceptionBusinessOrder(**exception_obj))
        # 不符合条件的异常工单
        db.session.add(ExceptionBusinessOrder(**default_exception_bo_obj))
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_creator")
    def test_filter_by_creator(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.creator = "ut"
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert first(response.data.page_data).creator.nick == request.creator

    @fixture
    def setup_filter_by_creator_user_group(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        exception_obj = {
            **default_exception_bo_obj,
            "bo_info": {"platform_creator_user_group": [{"uuid": "1"}]},
        }
        db.session.add(ExceptionBusinessOrder(**exception_obj))
        # 不符合条件的异常工单
        db.session.add(ExceptionBusinessOrder(**default_exception_bo_obj))
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_creator_user_group")
    def test_filter_by_creator_user_group(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.creator_user_group_uuid.append("1")
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert [
            each.uuid for each in first(response.data.page_data).creator_user_group
        ] == ["1"]

    @fixture
    def setup_filter_by_platform_creator(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        exception_obj = {
            **default_exception_bo_obj,
            "bo_info": {"platform_creator": {"id": 1}},
        }
        db.session.add(ExceptionBusinessOrder(**exception_obj))
        # 不符合条件的异常工单
        db.session.add(ExceptionBusinessOrder(**default_exception_bo_obj))
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_platform_creator")
    def test_filter_by_platform_creator(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.plat_creator_user_id.value = 1
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert first(response.data.page_data).plat_creator.id == 1

    @fixture
    def setup_filter_by_platform_creator_user_group(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        exception_obj = {
            **default_exception_bo_obj,
            "bo_info": {"platform_creator_user_group": [{"uuid": "1"}]},
        }
        db.session.add(ExceptionBusinessOrder(**exception_obj))
        # 不符合条件的异常工单
        db.session.add(ExceptionBusinessOrder(**default_exception_bo_obj))
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_platform_creator_user_group")
    def test_filter_by_platform_creator_user_group(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.plat_creator_user_group_uuid.append("1")
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert [
            each.uuid for each in first(response.data.page_data).plat_creator_user_group
        ] == ["1"]

    @fixture
    def setup_filter_by_leyan_creator(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        exception_obj = {
            **default_exception_bo_obj,
            "bo_info": {"ly_creator": {"id": 1}},
        }
        db.session.add(ExceptionBusinessOrder(**exception_obj))
        # 不符合条件的异常工单
        db.session.add(ExceptionBusinessOrder(**default_exception_bo_obj))
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_leyan_creator")
    def test_filter_by_leyan_creator(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request
        expect = 1

        request.fs_creator_user_id.value = expect
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert first(response.data.page_data).fs_creator.id == expect

    @fixture
    def setup_filter_by_leyan_creator_user_group(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        exception_obj = {
            **default_exception_bo_obj,
            "bo_info": {"ly_creator_user_group": [{"uuid": "1"}]},
        }
        db.session.add(ExceptionBusinessOrder(**exception_obj))
        # 不符合条件的异常工单
        db.session.add(ExceptionBusinessOrder(**default_exception_bo_obj))
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_leyan_creator_user_group")
    def test_filter_by_leyan_creator_user_group(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request
        expect = "1"

        request.fs_creator_user_group_uuid.append(expect)
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert [
            each.uuid for each in first(response.data.page_data).fs_creator_user_group
        ] == [expect]

    @fixture
    def setup_filter_by_current_job_name(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        db.session.add(
            ExceptionBusinessOrder(
                **{
                    **default_exception_bo_obj,
                    "current_job_name": "ut",
                    "current_job_info": {"name": "ut"},
                }
            )
        )
        # 不符合条件的异常工单
        db.session.add(
            ExceptionBusinessOrder(
                **{**default_exception_bo_obj, "current_job_name": "other"}
            )
        )
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_current_job_name")
    def test_filter_by_current_job_name(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.current_job_name = "ut"
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert (
            first(response.data.page_data).current_job_brief.name
            == request.current_job_name
        )

    @fixture
    def setup_filter_by_assignee(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        data = default_exception_bo_obj.copy()
        data["current_job_info"] = {"platform_assignee": {"nick": "ut"}}
        db.session.add(ExceptionBusinessOrder(**data))
        # 不符合条件的异常工单
        db.session.add(ExceptionBusinessOrder(**default_exception_bo_obj))
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_assignee")
    def test_filter_by_assignee(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.assignee_name = "ut"
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert first(response.data.page_data).assignee.nick == request.assignee_name

    @fixture
    def setup_filter_by_assignee_user_group(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        data = default_exception_bo_obj.copy()
        data["current_job_info"] = {"platform_assignee_user_group": [{"uuid": "1"}]}
        db.session.add(ExceptionBusinessOrder(**data))
        db.session.add(ExceptionBusinessOrder(**default_exception_bo_obj))
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_assignee_user_group")
    def test_filter_by_assignee_user_group(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.assignee_user_group_uuid.append("1")
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert [
            each.uuid for each in first(response.data.page_data).assignee_user_group
        ] == request.assignee_user_group_uuid

    @fixture
    def setup_filter_by_platform_assignee(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        data = default_exception_bo_obj.copy()
        data["current_job_info"] = {"platform_assignee": {"id": 1}}
        db.session.add(ExceptionBusinessOrder(**data))
        db.session.add(ExceptionBusinessOrder(**default_exception_bo_obj))
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_platform_assignee")
    def test_filter_by_platform_assignee(self, default_exception_bo_obj):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.plat_assignee_user_id.value = 1
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert (
            first(response.data.page_data).plat_assignee.id
            == request.plat_assignee_user_id.value
        )

    @fixture
    def setup_filter_by_platform_assignee_user_group(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        data = default_exception_bo_obj.copy()
        data["current_job_info"] = {"platform_assignee_user_group": [{"uuid": "1"}]}
        db.session.add(ExceptionBusinessOrder(**data))
        db.session.add(ExceptionBusinessOrder(**default_exception_bo_obj))
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_platform_assignee_user_group")
    def test_filter_by_platform_assignee_user_group(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.plat_assignee_user_group_uuid.append("1")
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert [
            each.uuid
            for each in first(response.data.page_data).plat_assignee_user_group
        ] == request.plat_assignee_user_group_uuid

    @fixture
    def setup_filter_by_leyan_assignee(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        data = default_exception_bo_obj.copy()
        data["current_job_info"] = {"ly_assignee": {"id": 1}}
        db.session.add(ExceptionBusinessOrder(**data))
        db.session.add(ExceptionBusinessOrder(**default_exception_bo_obj))
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_leyan_assignee")
    def test_filter_by_leyan_assignee(self, default_exception_bo_obj):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.fs_assignee_user_id.value = 1
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert (
            first(response.data.page_data).fs_assignee.id
            == request.fs_assignee_user_id.value
        )

    @fixture
    def setup_filter_by_leyan_assignee_user_group(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        data = default_exception_bo_obj.copy()
        data["current_job_info"] = {"ly_assignee_user_group": [{"uuid": "1"}]}
        db.session.add(ExceptionBusinessOrder(**data))
        db.session.add(ExceptionBusinessOrder(**default_exception_bo_obj))
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_leyan_assignee_user_group")
    def test_filter_by_leyan_assignee_user_group(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.fs_assignee_user_group_uuid.append("1")
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert [
            each.uuid for each in first(response.data.page_data).fs_assignee_user_group
        ] == request.fs_assignee_user_group_uuid

    @fixture
    def setup_filter_by_create_time(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        db.session.add(
            ExceptionBusinessOrder(
                **{**default_exception_bo_obj, "bo_created_at": 100}
            )
        )
        # 不符合条件的异常工单
        db.session.add(
            ExceptionBusinessOrder(
                **{**default_exception_bo_obj, "bo_created_at": 200}
            )
        )
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_create_time")
    def test_filter_by_create_time(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.create_begin_time = 0
        request.create_end_time = 150
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert first(response.data.page_data).created_at == 100

    @fixture
    def setup_filter_by_update_time(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        db.session.add(
            ExceptionBusinessOrder(
                **{**default_exception_bo_obj, "updated_at": arrow.now().datetime}
            )
        )
        # 不符合条件的异常工单
        db.session.add(
            ExceptionBusinessOrder(
                **{
                    **default_exception_bo_obj,
                    "updated_at": arrow.now().shift(days=-1).datetime,
                }
            )
        )
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_update_time")
    def test_filter_by_update_time(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.update_begin_time = arrow.now().shift(hours=-1).int_timestamp
        request.update_end_time = arrow.now().shift(hours=1).int_timestamp
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1

    @fixture
    def setup_filter_by_rpa_name(self, default_exception_bo_obj, db):
        # 符合条件的异常工单
        db.session.add(
            ExceptionBusinessOrder(
                **{
                    **default_exception_bo_obj,
                    "current_job_rpa_name": "ut",
                    "current_job_info": {"rpa_name": "ut"},
                }
            )
        )
        # 不符合条件的异常工单
        db.session.add(
            ExceptionBusinessOrder(
                **{
                    **default_exception_bo_obj,
                    "current_job_rpa_name": "other",
                }
            )
        )
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_rpa_name")
    def test_filter_by_rpa_name(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.current_job_rpa_name = "ut"
        response = service.ListExceptionPool(request, context)

        # 数据库里有两条记录，但是只有一条是符合条件的
        assert response.data.page.total == 1
        assert (
            first(response.data.page_data).current_job_brief.rpa_name
            == request.current_job_rpa_name
        )

    @fixture
    def setup_order_by_create(self, default_exception_bo_obj, db):
        db.session.add(
            ExceptionBusinessOrder(
                **{**default_exception_bo_obj, "bo_created_at": 100}
            )
        )
        db.session.add(
            ExceptionBusinessOrder(
                **{**default_exception_bo_obj, "bo_created_at": 200}
            )
        )
        db.session.commit()
        yield

    @mark.usefixtures("setup_order_by_create")
    def test_order_by_create(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.page_sort = PageSort.CREATE_ASC
        response = service.ListExceptionPool(request, context)
        # 按 create asc 排序
        assert response.data.page_data[0].created_at == 100
        assert response.data.page_data[1].created_at == 200

        request.page_sort = PageSort.CREATE_DESC
        response = service.ListExceptionPool(request, context)
        # 按 create desc 排序
        assert response.data.page_data[0].created_at == 200
        assert response.data.page_data[1].created_at == 100

    @fixture
    def setup_order_by_update(self, default_exception_bo_obj, db):
        record1 = ExceptionBusinessOrder(
            **{**default_exception_bo_obj, "updated_at": "2020-01-01 00:00:00"}
        )
        db.session.add(record1)
        record2 = ExceptionBusinessOrder(
            **{**default_exception_bo_obj, "updated_at": "2020-01-02 00:00:00"}
        )
        db.session.add(record2)
        db.session.commit()

    @mark.usefixtures("setup_order_by_update")
    def test_order_by_update(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.page_sort = PageSort.UPDATE_ASC
        response = service.ListExceptionPool(request, context)
        # 按 update asc 排序
        data = response.data.page_data
        assert data[0].updated_at < data[1].updated_at

        request.page_sort = PageSort.UPDATE_DESC
        response = service.ListExceptionPool(request, context)
        # 按 update desc 排序
        data = response.data.page_data
        assert data[0].updated_at > data[1].updated_at

    @mark.usefixtures("setup_order_by_create")
    def test_auto_retry_info(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request
        response = service.ListExceptionPool(request, context)
        assert response.data.page_data[0].will_retry is False
        assert response.data.page_data[1].retry_timestamp == 0
