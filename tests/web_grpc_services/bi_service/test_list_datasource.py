from pytest import fixture
from google.protobuf.empty_pb2 import Empty

from robot_processor.web_grpc_services.bi import BiService

bi_service = BiService()


class TestBiListReportDataSource:
    @fixture(autouse=True)
    def setup(self, client, mock_form):
        yield mock_form.subscribe(client.shop, True)

    def test_list_report_data_type(self, mock_grpc_context):
        request = Empty()
        response = bi_service.ListReportDataType(request, mock_grpc_context)
        # 测试有数据就行
        assert response.data_type_list
