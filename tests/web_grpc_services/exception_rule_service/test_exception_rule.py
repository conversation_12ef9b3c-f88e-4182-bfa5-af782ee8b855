from unittest.mock import MagicMock
from dataclasses import dataclass

from pytest import fixture
from leyan_proto.digismart.robot.exception_rule import api_pb2

from robot_processor.business_order.exception_rule import ExceptionRule
from robot_processor.web_grpc_services.exception_rule import ExceptionRuleService

service = ExceptionRuleService()
context = MagicMock()


@dataclass
class MockedData:
    memo_rule: int = None
    human_rule: int = None


class TestExceptionRule:
    mocked_data = MockedData()

    @fixture(autouse=True)
    def setup_testbed(self, client, db):
        memo_rule = ExceptionRule(
            enabled=True,
            updated_by="ut",
            priority=100,
            scopes=["MEMO"],
            rules=["备注失败"],
            reason="备注失败",
            suggestion="备注失败",
        )
        human_rule = ExceptionRule(
            enabled=True,
            updated_by="ut",
            priority=100,
            scopes=["human"],
            rules=["assignee invalid"],
            reason="执行客服失效",
            suggestion="执行客服失效",
        )
        db.session.add(memo_rule)
        db.session.add(human_rule)
        db.session.flush()
        self.mocked_data.human_rule = human_rule.id
        self.mocked_data.memo_rule = memo_rule.id
        yield

    def test(self):
        # 触发兜底规则
        request = api_pb2.TestExceptionRuleRequest(scope="human", raw_exc="未知错误")
        response = service.TestExceptionRule(request, context)
        assert response.match_pattern == ".*"
        assert response.match_rule.id == 0

        # 触发备注失败规则
        request = api_pb2.TestExceptionRuleRequest(scope="MEMO", raw_exc="备注失败")
        response = service.TestExceptionRule(request, context)
        assert response.match_pattern == "备注失败"
        assert response.match_rule.id == self.mocked_data.memo_rule
        assert response.reason == "备注失败"
        assert response.suggestion == "备注失败"

        # 触发执行客服失效规则
        request = api_pb2.TestExceptionRuleRequest(scope="human", raw_exc="assignee invalid")
        response = service.TestExceptionRule(request, context)
        assert response.match_pattern == "assignee invalid"
        assert response.match_rule.id == self.mocked_data.human_rule
        assert response.reason == "执行客服失效"
        assert response.suggestion == "执行客服失效"
