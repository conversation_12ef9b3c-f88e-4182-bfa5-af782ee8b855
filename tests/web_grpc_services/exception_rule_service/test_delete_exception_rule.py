from unittest.mock import MagicMock, call
from dataclasses import dataclass

from grpc import StatusCode
from pytest import fixture, mark, raises
from leyan_proto.digismart.robot.exception_rule.api_pb2 import (
    DeleteExceptionRuleRequest,
)

from robot_processor.business_order.exception_rule import ExceptionRule
from robot_processor.web_grpc_services.exception_rule import ExceptionRuleService


@dataclass
class MockedPatch:
    service = ExceptionRuleService()
    context = MagicMock()
    context.abort.side_effect = Exception
    request = None


class TestDeleteExceptionRule:
    mocked_patch = MockedPatch()

    @fixture(autouse=True)
    def setup_base(self, client):
        yield

    @fixture(autouse=True)
    def setup_grpc_request(self):
        self.mocked_patch.request = DeleteExceptionRuleRequest()
        yield

    @fixture
    def setup_delete_success(self, db):
        rule = ExceptionRule(updated_by="ut")
        db.session.add(rule)
        db.session.flush()
        self.mocked_patch.request.id = rule.id
        yield

    @mark.usefixtures("setup_delete_success")
    def test_delete_success(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        service.DeleteExceptionRule(request, context)

        exception_rule = ExceptionRule.query.get(request.id)
        assert exception_rule is None

    def test_delete_not_found(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        request.id = 1
        with raises(Exception):
            service.DeleteExceptionRule(request, context)
        context.assert_has_calls(
            calls=[
                call.abort(StatusCode.NOT_FOUND, "cannot find exception rule by id=1")
            ]
        )
