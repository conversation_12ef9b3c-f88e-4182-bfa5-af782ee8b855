from unittest.mock import MagicMock
from dataclasses import dataclass

from pytest import fixture, mark
from leyan_proto.digismart.robot.exception_rule.api_pb2 import (
    CreateExceptionRuleRequest,
)
from leyan_proto.digismart.robot.exception_rule import model_pb2

from robot_processor.business_order.exception_rule import ExceptionRule
from robot_processor.web_grpc_services.exception_rule import ExceptionRuleService


@dataclass
class MockedPatch:
    service = ExceptionRuleService()
    context = MagicMock()
    request = None


class TestCreateExceptionRule:
    mocked_patch = MockedPatch()

    @fixture(autouse=True)
    def setup_base(self, client):
        yield

    @fixture
    def setup_create_exception_rule(self):
        self.mocked_patch.request = CreateExceptionRuleRequest(
            exception_rule=model_pb2.ExceptionRule(
                priority=1,
                enabled=True,
                scopes=["scope 1", "scope 2"],
                rules=["rule 1", "rule 2"],
                extra_config=model_pb2.ExceptionRuleExtraConfig(
                    actions=[
                        model_pb2.ExceptionRuleExtraConfig.Action(
                            name="close", value=True
                        ),
                        model_pb2.ExceptionRuleExtraConfig.Action(
                            name="skip", value=False
                        ),
                        model_pb2.ExceptionRuleExtraConfig.Action(
                            name="retry", value=False
                        ),
                    ],
                    auto_retry=model_pb2.ExceptionAutoRetry.NEED_AUTO_RETRY,
                ),
                reason="fixme",
                suggestion="fixme",
                updated_by="ut",
            )
        )
        yield

    @mark.usefixtures("setup_create_exception_rule")
    def test_create_exception_rule(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        response = service.CreateExceptionRule(request, context)
        exception_rule = ExceptionRule.query.get(response.id)

        assert exception_rule.priority == request.exception_rule.priority
        assert exception_rule.enabled == request.exception_rule.enabled
        assert exception_rule.scopes == request.exception_rule.scopes
        assert exception_rule.rules == request.exception_rule.rules
        assert (
            exception_rule.extra_config["auto_retry"]
            == request.exception_rule.extra_config.auto_retry
        )
        assert exception_rule.reason == request.exception_rule.reason
        assert exception_rule.suggestion == request.exception_rule.suggestion
        assert exception_rule.updated_by == request.exception_rule.updated_by

        action_in_db = {
            action["name"]: action["value"]
            for action in exception_rule.extra_config["actions"]
        }
        for action in request.exception_rule.extra_config.actions:
            assert action_in_db[action.name] == action.value
