from unittest.mock import MagicMock, call
from dataclasses import dataclass

import pytest
from grpc import StatusCode
from leyan_proto.digismart.robot.exception_rule.api_pb2 import (
    UpdateExceptionRuleRequest,
)
from pytest import fixture, mark
from leyan_proto.digismart.robot.exception_rule import model_pb2

from robot_processor.ext import db
from robot_processor.business_order.exception_rule import ExceptionRule
from robot_processor.web_grpc_services.exception_rule import ExceptionRuleService


@dataclass
class MockedPatch:
    service = ExceptionRuleService()
    context = MagicMock()
    context.abort.side_effect = Exception


@dataclass
class MockedData:
    rule_id: int = None


class TestUpdateExceptionRule:
    mocked_patch = MockedPatch()
    mocked_data = MockedData()

    @fixture(autouse=True)
    def setup_base(self, client):
        yield

    @fixture
    def setup_init_exception_rule(self, db):
        rule = ExceptionRule(
            priority=1,
            enabled=False,
            updated_by="",
            scopes=["scope"],
            rules=["rule"],
            extra_config={"actions": [{"name": "close", "value": True}]},
        )
        db.session.add(rule)
        db.session.flush()
        self.mocked_data.rule_id = rule.id
        yield

    @mark.usefixtures("setup_init_exception_rule")
    def test_update_exception_rule(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        exception_rule = ExceptionRule.query.get(self.mocked_data.rule_id)
        exception_rule_pb = service.build_exception_rule_pb(exception_rule)
        # update
        exception_rule_pb.priority = 0
        exception_rule_pb.enabled = False
        exception_rule_pb.extra_config.auto_retry = model_pb2.NEED_AUTO_RETRY
        exception_rule_pb.reason = "reason"
        exception_rule_pb.suggestion = "suggestion"
        exception_rule_pb.updated_by = "ut"
        request = UpdateExceptionRuleRequest(
            id=self.mocked_data.rule_id, exception_rule=exception_rule_pb
        )
        response = service.UpdateExceptionRule(request, context)

        db.session.refresh(exception_rule)
        assert response.id == self.mocked_data.rule_id
        assert exception_rule.priority == 0
        assert exception_rule.enabled is False
        assert exception_rule.extra_config == {
            "actions": [{"name": "close", "value": True}],
            "auto_retry": model_pb2.ExceptionAutoRetry.NEED_AUTO_RETRY,
        }
        assert exception_rule.reason == "reason"
        assert exception_rule.suggestion == "suggestion"
        assert exception_rule.updated_by == "ut"

    def test_update_exception_rule_not_found(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = UpdateExceptionRuleRequest(id=0)
        with pytest.raises(Exception):
            service.UpdateExceptionRule(request, context)
        context.assert_has_calls(
            calls=[
                call.abort(StatusCode.NOT_FOUND, "cannot find exception rule by id=0")
            ]
        )
