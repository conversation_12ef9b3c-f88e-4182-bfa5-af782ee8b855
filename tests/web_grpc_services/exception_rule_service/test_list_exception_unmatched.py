from unittest.mock import MagicMock
from dataclasses import dataclass

from factory.fuzzy import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pytest import fixture, mark
from leyan_proto.digismart.robot.exception_rule.api_pb2 import (
    Pagination,
    ListExceptionUnmatchedRequest,
)

from robot_processor.business_order.exception_rule import ExceptionRuleUnmatched
from robot_processor.web_grpc_services.exception_rule import ExceptionRuleService


@dataclass
class MockedPatch:
    service = ExceptionRuleService()
    context = MagicMock()
    request: ListExceptionUnmatchedRequest = None


class TestListExceptionUnmatched:
    mocked_patch = MockedPatch()
    fuzzy_int = FuzzyInteger(1, 1000)

    @fixture(autouse=True)
    def setup_base(self, client):
        yield

    @fixture(autouse=True)
    def setup_grpc_request(self):
        self.mocked_patch.request = ListExceptionUnmatchedRequest(
            pagination=Pagination(page=1, per_page=10)
        )
        yield

    def test_pagination(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request
        request.pagination.per_page = self.fuzzy_int.fuzz()

        response = service.ListExceptionUnmatched(request, context)

        # 检查 pagination 保留了下来
        assert response.pagination.page == request.pagination.page
        assert response.pagination.per_page == request.pagination.per_page

    @fixture
    def setup_filter_by_scope(self, db):
        db.session.add(
            ExceptionRuleUnmatched(scope="human", raw_exc="", message_digest="")
        )
        db.session.add(
            ExceptionRuleUnmatched(scope="other", raw_exc="", message_digest="")
        )
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_scope")
    def test_filter_by_scope(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        # 筛选出一个符合条件的异常规则
        expect_scope = "human"
        request.scope.value = expect_scope
        response = service.ListExceptionUnmatched(request, context)
        assert response.pagination.total == 1
        for exception in response.exceptions:
            assert expect_scope == exception.scope

        # 没有符合条件的异常规则
        request.scope.value = "匹配不到我"
        response = service.ListExceptionUnmatched(request, context)
        assert response.pagination.total == 0

    @fixture
    def setup_filter_by_raw_exc(self, db):
        db.session.add(ExceptionRuleUnmatched(scope="human", raw_exc="出错了", message_digest=""))
        db.session.commit()
        yield

    @mark.usefixtures("setup_filter_by_raw_exc")
    def test_filter_by_reason(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        # 筛选出一个符合条件的异常规则
        expect_raw_exc = "出错了"
        request.raw_exc.value = expect_raw_exc
        response = service.ListExceptionUnmatched(request, context)
        assert response.pagination.total == 1
        for exception in response.exceptions:
            assert expect_raw_exc in exception.raw_exc

        # 没有符合条件的异常规则
        request.raw_exc.value = "匹配不到我"
        response = service.ListExceptionUnmatched(request, context)
        assert response.pagination.total == 0

    @fixture
    def setup_order_by(self, db):
        db.session.add(ExceptionRuleUnmatched(scope="1", raw_exc="", message_digest=""))
        db.session.add(ExceptionRuleUnmatched(scope="2", raw_exc="", message_digest=""))
        db.session.commit()
        yield

    @mark.usefixtures("setup_order_by")
    def test_order_by(self):
        service = self.mocked_patch.service
        context = self.mocked_patch.context
        request = self.mocked_patch.request

        # 测试正序
        request.order_by.value = "id asc"
        response = service.ListExceptionUnmatched(request, context)
        assert response.pagination.total == 2
        assert response.exceptions[0].scope == "1"
        assert response.exceptions[1].scope == "2"

        # 测试倒序
        request.order_by.value = "id desc"
        response = service.ListExceptionUnmatched(request, context)
        assert response.pagination.total == 2
        assert response.exceptions[0].scope == "2"
        assert response.exceptions[1].scope == "1"
