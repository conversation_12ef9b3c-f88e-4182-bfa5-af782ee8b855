from robot_processor.shop.models import SmartCallInfo
from rpa.smartcall.client import SmartCallClient


def test_start_call(requests_mock):
    requests_mock.post('/smartcall-backend/openapi/put_flow_call',
                       json={"resultCode": 0, "resultMsg": "SUCCESS", "displayMsg": "",
                             "data": {"flowUuid": "eeca26fa-a189-41df-9a23-3771b3265528"}})
    mock_smart_call_info = SmartCallInfo()
    mock_smart_call_info.meta = {"app_secret": "7VixIKmOSENWFSej", "app_key": "appkey_noc", "company_id": "5"}

    param_map = {
        "缺货原因": "缺货原因",
        "商品名称": "商品名称+规格属性",
        "business_order_id": "100",
        "step_id": "200"
    }
    mobile = "***********"
    graph_main_id = "唐狮缺货提醒"

    res = SmartCallClient(shop=None, smartcall_info=mock_smart_call_info).start_call(mobile, graph_main_id, param_map)
    assert res.is_ok()
    assert res.ok_value == {"flowUuid": "eeca26fa-a189-41df-9a23-3771b3265528"}
