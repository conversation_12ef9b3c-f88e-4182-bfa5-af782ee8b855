from rpa.mola import MolaClient


def test_http_error(requests_mock):
    requests_mock.post('/ut', status_code=400)
    result = MolaClient('sid')._post("http://localhost/ut", {}, '')
    assert result.is_err()
    assert result.err_value == "status_code: 400, response: "


def test_biz_error(requests_mock):
    requests_mock.post('/ut', json={'success': False})
    result = MolaClient('sid')._post("http://localhost/ut", {}, '')
    assert result.is_err()
    assert result.err_value == '{"success": false}'


def test_success(requests_mock):
    requests_mock.post('/ut', json={'success': True})
    result = MolaClient('sid', need_retry=True)._post("http://localhost/ut", {}, '')
    assert result.is_ok()
    assert result.ok_value == {"success": True}
