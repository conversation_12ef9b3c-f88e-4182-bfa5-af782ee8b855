from robot_processor.client import item_client


def test_convert_to_old_spu_sku():
    new_sku_dict = {'skuInfo': [
        {'spu_id': '001-WQ', 'sku_id': '001-WQ', 'price': 1.0, 'outer_sku_id': '002-WQ', 'item_title': '001-围裙',
         'props': '红色(11);32', 'pic_url': 'pic', 'quantity': 0}],
        'page_no': 1, 'page_size': 20, 'total_count': 0}
    old_sku_dict = item_client.convert_to_old_spu_sku(new_sku_dict)
    assert '001-WQ' == old_sku_dict.get('spu_id')
    assert '红色(11);32' == old_sku_dict.get('sku_infos')[0].get('props')
    assert '1.0' == str(old_sku_dict.get('sku_infos')[0].get('price'))
    assert '002-WQ' == old_sku_dict.get('sku_infos')[0].get('outer_sku_id')


def test_convert_to_single_spu():
    spu_dict = {'item_detail_infos': [
        {'spu_id': '644950799617', 'item_title': '大号加厚羊角球感统训练蹦蹦球跳跳球儿童玩具幼儿园运动器材女孩',
         'pic_url': 'https://img.alicdn.com/bao/uploaded/i3/923313079'}]}
    spu = item_client.convert_to_single_spu(spu_dict)
    assert '644950799617' == spu.get('spu_id')
