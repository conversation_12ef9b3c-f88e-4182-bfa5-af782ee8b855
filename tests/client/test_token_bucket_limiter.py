from robot_processor.client._token_bucket_limiter import (
    token_bucket_string_msg_handler,
    token_bucket_avro_msg_handler,
    TokenBucketLimiter,
)


def test_token_bucket_string_msg_handler():
    msg = '{"events":[{"app":"MOLA","level":"STORE","store_id":"3803062908","token_bucket_key":"mola:qq:send-to-qq","tokens_per_interval":4,"interval_ms":1000},{"app":"MOLA","level":"STORE","store_id":"3803062908","token_bucket_key":"mola:qq:get-status","tokens_per_interval":4,"interval_ms":60000}]}'  # noqa: E501
    assert TokenBucketLimiter.get_ratelimit_value("mola:qq:send-to-qq:3803062908") is None
    assert TokenBucketLimiter.get_ratelimit_value("mola:qq:get-status:3803062908") is None
    token_bucket_string_msg_handler(None, msg)
    assert TokenBucketLimiter.get_ratelimit_value("mola:qq:send-to-qq:3803062908") == "4/1second"
    assert TokenBucketLimiter.get_ratelimit_value("mola:qq:get-status:3803062908") == "4/60second"


def test_token_bucket_avro_msg_handler():
    msg = {
        "events": [
            {
                "app": "ROBOT",
                "org_id": 0,
                "store_id": "104142144",
                "device_id": "",
                "assistant_nick": "",
                "level": "STORE",
                "token_bucket_key": "erp:common:complete_item",
                "tokens_per_interval": 5,
                "interval_ms": 1000
            },
            {
                "app": "ROBOT",
                "org_id": 0,
                "store_id": "111307611",
                "device_id": "",
                "assistant_nick": "",
                "level": "STORE",
                "token_bucket_key": "erp:common:complete_item",
                "tokens_per_interval": 5,
                "interval_ms": 1000
            }
        ]
    }
    assert TokenBucketLimiter.get_ratelimit_value("erp:common:complete_item:104142144") is None
    assert TokenBucketLimiter.get_ratelimit_value("erp:common:complete_item:111307611") is None
    token_bucket_avro_msg_handler(None, msg)
    assert TokenBucketLimiter.get_ratelimit_value("erp:common:complete_item:104142144") == "5/1second"
    assert TokenBucketLimiter.get_ratelimit_value("erp:common:complete_item:111307611") == "5/1second"


def test_sync_mola_api_config(mocker):
    token_bucket_limiter = TokenBucketLimiter()
    mola_api_features = [
        {
            "id": 53,
            "site": "qq",
            "namespace": "qq",
            "method": "send-to-qq",
            "version": "0.9.10",
            "title": "QQ发送QQ消息",
            "description": "通过用户的QQ号向该用户发送消息。",
            "runtime": {
                "ms": 250,
                "qps": 4,
                "timeout": 40000,
                "interval": 1000,
                "tokensPerInterval": 4
            },
            "created_at": "2022-03-01T09:05:59.354Z",
            "updated_at": "2022-07-12T11:41:05.797Z",
            "deprecated_at": None
        },
        {
            "id": 57,
            "site": "youzheng",
            "namespace": "youzheng-package",
            "method": "trace-package",
            "version": "0.9.10",
            "title": "邮政发起查件",
            "description": "根据邮件号、邮件信息、查件内容来发起查件。",
            "runtime": {
                "ms": 250,
                "qps": 4,
                "timeout": 40000,
                "interval": 1000,
                "tokensPerInterval": 4
            },
            "created_at": "2022-03-01T11:17:37.677Z",
            "updated_at": "2022-07-13T00:07:07.380Z",
            "deprecated_at": None
        }
    ]
    mocked_get_mola_api_features = mocker.patch.object(token_bucket_limiter, '_get_mola_api_features',
                                                       return_value=mola_api_features)
    assert TokenBucketLimiter.get_ratelimit_value("mola:qq:send-to-qq") is None
    assert TokenBucketLimiter.get_ratelimit_value("mola:youzheng-package:trace-package") is None
    token_bucket_limiter.sync_mola_api_config()
    assert TokenBucketLimiter.get_ratelimit_value("mola:qq:send-to-qq") == "4/1second"
    assert TokenBucketLimiter.get_ratelimit_value("mola:youzheng-package:trace-package") == "4/1second"
    token_bucket_limiter.sync_mola_api_config()
    assert mocked_get_mola_api_features.call_count == 1


def test_get_and_set_ratelimit_value():
    assert TokenBucketLimiter.get_ratelimit_value("token_bucket_key") is None
    TokenBucketLimiter.set_ratelimit_value("token_bucket_key", 1, 30000)
    assert TokenBucketLimiter.get_ratelimit_value("token_bucket_key") == "1/30second"
    TokenBucketLimiter.set_ratelimit_value("token_bucket_key", 3, 30000)
    assert TokenBucketLimiter.get_ratelimit_value("token_bucket_key") == "3/30second"


def test_try_acquire_token_for_store(app_context):
    from limits.storage import MemoryStorage
    from limits.strategies import MovingWindowRateLimiter

    token_bucket_limiter = TokenBucketLimiter()
    token_bucket_limiter.storage = MemoryStorage()
    token_bucket_limiter._limiter = MovingWindowRateLimiter(token_bucket_limiter.storage)

    token_bucket_limiter.set_ratelimit_value("token_bucket_key", 1, 1000)
    assert token_bucket_limiter.try_acquire_token_for_store("token_bucket_key", "1")
    assert not token_bucket_limiter.try_acquire_token_for_store("token_bucket_key", "1")
    assert not token_bucket_limiter.try_acquire_token_for_store("token_bucket_key", "2")

    token_bucket_limiter.set_ratelimit_value("token_bucket_key:2", 1, 1000)
    assert token_bucket_limiter.try_acquire_token_for_store("token_bucket_key", "2")
    assert not token_bucket_limiter.try_acquire_token_for_store("token_bucket_key", "2")
