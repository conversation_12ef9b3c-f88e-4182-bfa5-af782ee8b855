from robot_processor.client import taobao_client


def test_http_status_500(requests_mock):
    """http 请求异常的场景"""
    from requests.exceptions import HTTPError

    request_patch = requests_mock.post(taobao_client.api_url, status_code=500)
    result = taobao_client.api_request(method="ut", access_token="", retry_times=2)
    assert result.is_err()
    assert isinstance(result.err(), HTTPError)
    assert request_patch.call_count == 2


def test_business_error(requests_mock):
    """业务异常的场景"""
    request_patch = requests_mock.post(taobao_client.api_url,
                                       json={"error_response": {"code": 1, "msg": "ut need error"}})
    result = taobao_client.api_request(method="ut", access_token="", retry_times=2)

    assert result.is_err()
    top_error = result.err()
    assert top_error.res["code"] == 1
    assert top_error.res["msg"] == "ut need error"
    assert request_patch.called_once


def test_success(requests_mock):
    """成功的场景"""
    request_patch = requests_mock.post(taobao_client.api_url, json={"foo": "bar"})
    result = taobao_client.api_request(method="ut", access_token="")

    assert result.is_ok()
    result = result.ok()
    assert result == {"foo": "bar"}
    assert request_patch.called_once
