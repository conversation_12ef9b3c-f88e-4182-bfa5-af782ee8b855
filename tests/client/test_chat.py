from robot_processor.client import chat_client
from robot_processor.client.conf import app_config as config


def test_send_message(client, requests_mock):
    sender = 'test_sender'
    contact = 'test_contact'
    text = 'test_text'
    image = 'test_image'
    requests_mock.post(f"{config.CHAT_CLIENT_OPEN_GATEWAY_ENDPOINT}/sessions/chat.{sender}/methods/dispatch",
                       status_code=404002, json={})
    res = chat_client.send_message(sender, contact, text, image)
    assert not res.is_ok()
    assert '客户端不在线' == res.err_value
    requests_mock.post(f"{config.CHAT_CLIENT_OPEN_GATEWAY_ENDPOINT}/sessions/chat.{sender}/methods/dispatch",
                       status_code=200, json={})
    res = chat_client.send_message(sender, contact, text, image)
    assert res.is_ok()


def test_get_last_assistant_by_store_buyer(client, requests_mock):
    data = {
        "code": "SUCCESS",
        "lastAssistant": [
            {"assistant_nick": "nick_111", "time_second": 1}
        ]
    }
    requests_mock.post(f"{config.CHAT_CLIENT_SCORE_GRPC_GATEWAY_ENDPOINT}/get_last_assistant_by_store_buyer",
                       status_code=200, json=data)
    res = chat_client.get_last_assistant_by_store_buyer('111', '222', '333')
    assert res.is_ok()
    assert "nick_111" == res.ok_value


def test_get_online_assistants_by_sid(client, requests_mock):
    data = {
        "assistants_by_app": {
            "chat": {
                "assistant_status": [
                    {"assistant_nick": "nick_111"}
                ]
            }
        }
    }
    requests_mock.post(f"{config.CHAT_CLIENT_SESSION_MANAGER_GRPC_GATEWAY_ENDPOINT}/get_online_assistants_by_storeV3",
                       status_code=200, json=data)
    res = chat_client.get_online_assistants_by_sid('111')
    assert res.is_ok()
    assert 1 == len(res.ok_value)
    assert "nick_111" == res.ok_value[0]
