import json
from os import path

import jwt
from flask import current_app
from pytest import fixture

from robot_processor.currents import g
from robot_processor.plugin.api import compute_erp_type, get_access_token
from robot_processor.enums import ErpType
from rpa.erp.kuaimai import KuaimaiClient


def test_make_token(client):
    jwt_key = current_app.config["JWT_KEY"]
    token = client.shop.get_mola_kuaimai_token(jwt_key)

    payload = jwt.decode(token, jwt_key, algorithms="HS256")
    assert payload["shop"]["id"] == client.shop.sid
    assert payload["shop"]["title"] == client.shop.title
    assert payload["shop"]["nickname"] == client.shop.nick


def test_compute_erp_type(mocker, shop_factory, mock_erp_info):
    shop = shop_factory()
    assert compute_erp_type("platform", shop) is None

    source = "erp"
    mock_erp_info.shop_id = shop.id
    mock_erp_info.erp_type = ErpType.JST
    shop.erps = [mock_erp_info]
    erp_type = compute_erp_type(source, shop)
    assert erp_type == ErpType.JST

    mock_erp_info.erp_type = ErpType.KUAIMAI
    shop.erps = [mock_erp_info]
    erp_type = compute_erp_type(source, shop)
    assert erp_type == ErpType.KUAIMAI

    g.shop = shop
    assert get_access_token(erp_type)


@fixture
def mock_logistics_result():
    with open(
        path.join(path.dirname(__file__), "mock/grayerp.get_trades-list.json")
    ) as f:
        return json.load(f)


@fixture
def mock_query_trade_result():
    with open(
        path.join(path.dirname(__file__), "mock/query_trade_result_response.json")
    ) as f:
        return json.load(f)


@fixture
def mock_query_trade_detail_result():
    with open(
        path.join(path.dirname(__file__), "mock/query_trade_detail_result_response.json")
    ) as f:
        return json.load(f)


@fixture
def kuaimai_client():
    yield KuaimaiClient('sid')


def test_after_sale_upload(requests_mock, kuaimai_client):
    sku_list = [{"outer_sku_id": "DS8710-12-43", "qty": 1}]

    requests_mock.post('/namespaces/grayerp/methods/current-create-reissue', exc=TimeoutError())
    res = kuaimai_client.after_sale_upload("", "", "不发货-1234321", sku_list)
    assert not res.is_ok()

    requests_mock.post('/namespaces/grayerp/methods/current-create-reissue', exc=Exception())
    res = kuaimai_client.after_sale_upload("", "", "不发货-1234321", sku_list)
    assert not res.is_ok()

    requests_mock.post('/namespaces/grayerp/methods/current-create-reissue', status_code=401, text='unauthorized')
    res = kuaimai_client.after_sale_upload("wh", 'kd', "tid", sku_list)
    assert not res.is_ok()

    requests_mock.post('/namespaces/grayerp/methods/current-create-reissue', json={})
    res = kuaimai_client.after_sale_upload("wh", 'kd', "tid", sku_list)
    assert not res.is_ok()

    requests_mock.post('/namespaces/grayerp/methods/current-create-reissue', json={'success': False})
    res = kuaimai_client.after_sale_upload("wh", 'kd', "tid", sku_list)
    assert not res.is_ok()

    requests_mock.post('/namespaces/grayerp/methods/current-create-reissue',
                       json={"success": True, "result": {"data": {"list": [{"sid": "123"}]}}})
    res = kuaimai_client.after_sale_upload("wh", 'kd', "tid", sku_list)
    assert res.is_ok()
    assert res.ok_value == '123'


def test_after_sale_logistics(requests_mock, kuaimai_client, mock_logistics_result):
    requests_mock.post('/namespaces/grayerp/methods/get-trades-list', json=mock_logistics_result)
    res = kuaimai_client.after_sale_logistics("3561898630753227")
    assert res.is_ok()
    assert res.ok_value == {
        "logistics_corp": "shunfeng",
        "logistics_order": "SF1342814408421",
        "exp_tpl_id": "branch: sf super cheap"
    }


def test_change_skus(kuaimai_client, requests_mock):
    requests_mock.post('/namespaces/grayerp/methods/original-order-change-sku-list',
                       json={'success': True, 'message': ''})
    data = {"tid": '', "skuList": [{"oid": '3571717768916281', "newSkuID": 'DS2180M-01-43'}]}
    res = kuaimai_client.change_skus(data)
    assert res.is_ok()


def test_change_address(kuaimai_client, requests_mock):
    address = {
        "state": "上海市",
        "city": "上海市",
        "zone": "长宁区",
        "address": "长宁路1号",
        "name": "小熊",
        "mobile": "123",
    }

    # mock success
    requests_mock.post('/namespaces/grayerp/methods/change-address', json={'success': True, 'message': ''})
    res = kuaimai_client.change_address("3561898630753227", address)
    assert res.is_ok()


def test_trade_query(requests_mock, kuaimai_client, mock_query_trade_detail_result, mock_query_trade_result):
    requests_mock.post('/namespaces/grayerp/methods/get-trades-list', json=mock_query_trade_result)
    requests_mock.post('/namespaces/grayerp/methods/get-trades-list-sid', json=mock_query_trade_detail_result)
    res = kuaimai_client.trade_query("2350221121348781075")
    assert "顺丰速运" == res.get("logistics_name")
    assert "2110" == res.get("warehouse_no")
    assert "SF1342528653605" == res.get("logistics_no")
    assert "0" == res.get("pay_amount")
    assert "DS2180M" in res.get("goods_title")
