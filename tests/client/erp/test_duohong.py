from pytest import fixture

from rpa.erp.duohong import DuohongClient

mock_replicate_resp = {
    "success": True,
    "message": "调用服务成功",
    "code": 200,
    "request": {
        "sid": "551782",
        "title": "菜波-测试",
        "site": "duohong",
        "namespace": "duohong-erp",
        "method": "do-copy-trade",
        "payload": {
            "tid": "1516575145066781075"
        }
    },
    "version": "0.7.7",
    "result": {
        "tid": "1516575145066781075_803281",
        "isHDFK": "0"
    }
}

mock_failure_resp = {
    "success": False,
    "message": "调用服务成功",
    "code": 503,
    "request": {
        "sid": "551782",
        "title": "菜波-测试",
        "site": "duohong",
        "namespace": "duohong-erp",
        "method": "do-copy-trade",
        "payload": {
            "tid": "1516575145066781075"
        }
    },
    "version": "0.7.7",
    "error": {
        "id": 13,
        "type": "duohong-erp:502",
        "reason": "[李楠包子铺]多鸿未登录",
        "suggestion": "在RPA客户端登录多鸿后，再点击重试。"
    }
}

mock_address_resp = {
    "success": True,
    "message": "调用服务成功",
    "code": 200,
    "request": {
        "sid": "551782",
        "title": "菜波-测试",
        "site": "duohong",
        "namespace": "duohong-erp",
        "method": "do-get-address",
        "payload": {
            "tid": "1516575145066781075"
        }
    },
    "version": "0.7.7",
    "result": {
        "1516575145066781075": {
            "moFrom": "hpi.ecpf.taobao.Trade",
            "shopName": "菜波数码专营店",
            "buyerNick": "tb031723874",
            "receiverName": "周",
            "receiverState": "上海",
            "receiverCity": "上海市",
            "receiverDistrict": "普陀区",
            "receiverMobile": "15670186000",
            "receiverPhone": "",
            "buyerEmail": "",
            "receiverZip": "000000",
            "receiverAddress": "万里街道新村路"
        }
    }
}

sku_list = [{
    "outer_sku_id": "sku",
    "qty": 2
}]
address = {
    "receiverState": "上海",
    "receiverCity": "上海市",
    "receiverDistrict": "长宁区",
    "detailAddress": "联通大厦18f",
    "receiverMobile": "15670186081",
    "receiverName": "测试-leyan-收货人"
}


def test_convert_list():
    assert '' == DuohongClient.convert_list(None)
    assert 'a' == DuohongClient.convert_list(['a'])
    assert '' == DuohongClient.convert_list([])
    assert 'b' == DuohongClient.convert_list(['b'])


@fixture
def duohong(client):
    return DuohongClient(client.sid)


def test_query_address_fail(duohong, requests_mock):
    requests_mock.post('/namespaces/duohong-erp/methods/do-get-address', status_code=503, json=mock_failure_resp)
    res = duohong.query_address('1234')
    assert not res.is_ok()


def test_query_address_succeed(duohong, requests_mock):
    requests_mock.post('/namespaces/duohong-erp/methods/do-get-address', json=mock_address_resp)
    res = duohong.query_address('1234')
    assert res.is_ok()
    address_data = res.ok()
    assert "周" == address_data.get('name')
    assert "上海" == address_data.get('state')
    assert "上海市" == address_data.get('city')
    assert "普陀区" == address_data.get('district')
    assert "15670186000" == address_data.get('mobile')
    assert "" == address_data.get('phone')
    assert "000000" == address_data.get('zip')
    assert "万里街道新村路" == address_data.get('address')


def test_modify_address_fail(requests_mock, mocker, duohong):
    requests_mock.post('/namespaces/duohong-erp/methods/do-modify-address', status_code=503, json=mock_failure_resp)
    res = duohong.modify_address('1234', address)
    assert not res.is_ok()
    error = res.err()
    assert error.startswith("status_code: 503")


def test_modify_address_succeed(requests_mock, mocker, duohong):
    requests_mock.post('/namespaces/duohong-erp/methods/do-modify-address', json=mock_replicate_resp)
    res = duohong.modify_address('1234', address)
    assert res.is_ok()


def test_modify_sku_fail(requests_mock, duohong):
    requests_mock.post('/namespaces/duohong-erp/methods/do-modify-trade-skus', status_code=503, json=mock_failure_resp)
    res = duohong.modify_sku('1234', sku_list)
    assert not res.is_ok()


def test_modify_skus(requests_mock, duohong):
    requests_mock.post('/namespaces/duohong-erp/methods/do-modify-trade-skus', json=mock_replicate_resp)
    res = duohong.modify_sku('1234', sku_list)
    assert res.is_ok()
    res = duohong.modify_skus({'tid': '1234', "outerSkuIds": [{"skuId": "outer_sku_id", "num": 1}]})
    assert res.is_ok()


def test_after_sale_upload_fail(requests_mock, duohong):
    requests_mock.post('/namespaces/duohong-erp/methods/do-copy-trade', status_code=503, json=mock_failure_resp)
    res = duohong.after_sale_upload('1234', sku_list, address)
    assert not res.is_ok()


def test_after_sale_upload_succeed(requests_mock, duohong):
    requests_mock.post('/namespaces/duohong-erp/methods/do-copy-trade', json=mock_replicate_resp)
    requests_mock.post('/namespaces/duohong-erp/methods/do-modify-trade-skus', json=mock_replicate_resp)
    requests_mock.post('/namespaces/duohong-erp/methods/do-modify-address', json=mock_replicate_resp)
    res = duohong.after_sale_upload('1234', sku_list, address)
    assert res.is_ok()
    assert res.ok() == "1516575145066781075_803281"


def test_after_sale_upload_replicate_fail(requests_mock, duohong):
    requests_mock.post('/namespaces/duohong-erp/methods/do-copy-trade', json=mock_replicate_resp)
    requests_mock.post('/namespaces/duohong-erp/methods/do-modify-address', status_code=503, json=mock_failure_resp)
    res = duohong.after_sale_upload_replicate('1234', address)
    assert not res.is_ok()
    requests_mock.post('/namespaces/duohong-erp/methods/do-modify-address', json=mock_replicate_resp)
