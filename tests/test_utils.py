import pytest

from pydantic import BaseModel, Field, ValidationError
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.utils import (
    flatten, topological_sorting, sort_steps,
    combine_address_with_town,
    readable_enum, omit_none_fields_before_validate,
    filter_none
)


def test_flatten():
    lst = ["七天无理由", "其他", ["单选一", "多选二"]]
    lst1 = flatten(lst)
    assert lst1[2] == "单选一"


def test_topological_sorting():
    """
    graph:
        A -> B -> E |
        |           -> F
        |-> C -> D |

         输出 A B E C D F 或 A C D B E F
    """
    graph = {
        "F": {"D", "E"},
        "E": {"B"},
        "B": {"A"},
        "D": {"C"},
        "C": {"A"}
    }
    result, node_level = topological_sorting(graph)

    assert node_level == {
        "A": 0,
        "B": 1,
        "C": 1,
        "D": 2,
        "E": 2,
        "F": 3
    }
    # 因为按 node level 进行分组了，所以也按 node level 检查
    assert result[0] == "A"
    assert sorted(result[1: 3]) == ["B", "C"]
    assert sorted(result[3: 5]) == ["D", "E"]
    assert result[5] == "F"


def test_sort_step():
    """
    steps:
        A prev: null, next: B, C
        B prev: A, next: E
        C prev: A, next: D
        D prev: C, next: F
        E prev: B, next: F
        F prev: D, E
    """
    from random import shuffle

    class Step:
        def __init__(self):
            from robot_processor.utils import get_nonce

            self.name = ""
            self.step_uuid = get_nonce()
            self.prev_step_ids = []
            self.next_step_ids = []

        def __repr__(self):
            return self.name

    a, b, c, d, e, f = map(lambda _: Step(), range(6))
    a.name = "a"
    b.name = "b"
    c.name = "c"
    d.name = "d"
    e.name = "e"
    f.name = "f"
    a.next_step_ids.extend([b.step_uuid, c.step_uuid])
    b.prev_step_ids.append(a.step_uuid)
    b.next_step_ids.append(e.step_uuid)
    c.prev_step_ids.append(a.step_uuid)
    c.next_step_ids.append(d.step_uuid)
    d.prev_step_ids.append(c.step_uuid)
    d.next_step_ids.append(f.step_uuid)
    e.prev_step_ids.append(b.step_uuid)
    e.next_step_ids.append(f.step_uuid)
    f.prev_step_ids.extend([d.step_uuid, e.step_uuid])
    steps = [a, b, c, d, e, f]
    shuffle(steps)
    assert sort_steps(steps) == [a, b, c, d, e, f]


def test_combine_address_with_town():
    assert ' ' == combine_address_with_town('', '')
    assert ' ' == combine_address_with_town('', None)
    assert ' ' == combine_address_with_town(None, None)
    assert '安亭镇曲惠路' == combine_address_with_town('', '安亭镇曲惠路')
    assert '安亭镇曲惠路' == combine_address_with_town(None, '安亭镇曲惠路')
    assert '安亭镇曲惠路' == combine_address_with_town('安亭镇', '曲惠路')
    assert '安亭镇曲惠路' == combine_address_with_town('安亭镇', '安亭镇曲惠路')


def test_convert_product_widget_data_as_array():
    from robot_processor.utils import convert_product_widget_data_as_array

    product = {"foo": "bar"}
    assert convert_product_widget_data_as_array([product]) == [product]
    assert convert_product_widget_data_as_array(product) == [product]


def test_convert_data_for_product(client, mock_job, mock_business_order, step_factory, mock_form):
    from robot_processor.ext import db
    from robot_processor.utils import convert_data
    from robot_processor.form.form_subscribe import FormSubscribe

    step = step_factory.create(form_id=mock_form.id)
    step.update_raw_step()
    step.raw_step["ui_schema"] = [{"before": False, "type": "product", "key": "foo"}]
    flag_modified(step, "raw_step")
    FormSubscribe(mock_form).subscribe(client.shop, True)
    mock_job.step_uuid = step.step_uuid
    mock_job.step_id = step.id
    mock_job.business_order_id = mock_business_order.id
    mock_business_order.data = {"foo": {"spu": "1"}}
    db.session.commit()

    assert convert_data(job=mock_job) == {"foo": [{"spu": "1"}]}


class TestIsBuyerNickEncrypt:
    def test_encrypt(self, client):
        from robot_processor.utils import is_buyer_nick_encrypt

        assert is_buyer_nick_encrypt("n*")
        assert is_buyer_nick_encrypt("**")

        assert not is_buyer_nick_encrypt("test")


def test_readable_enum():
    from robot_processor.enums import JobStatus
    assert 'JobStatus.SUCCEED' == readable_enum(JobStatus.SUCCEED)


def test_url_unicode_encode():
    from robot_processor.utils import url_encode

    # 转换 path 中的中文
    url = (
        "https://prd-robot-processor.oss-cn-zhangjiakou.aliyuncs.com"
        "/business_order/memo/attachment/3/425920014"
        "/483e4c97-edcb-42b7-8c54-8e6d3f72de10_商品数据源导入规则.xlsx"
    )
    assert (
        url_encode(url) == "https://prd-robot-processor.oss-cn-zhangjiakou.aliyuncs.com"
        "/business_order/memo/attachment/3/425920014/483e4c97-edcb-42b7-8c54-8e6d3f72de10_"
        "%E5%95%86%E5%93%81%E6%95%B0%E6%8D%AE%E6%BA%90%E5%AF%BC%E5%85%A5%E8%A7%84%E5%88%99.xlsx"
    )

    # 转换 query 中的中文
    url = "http://localhost:5000/attachments?foo=转换我"
    assert (
        url_encode(url)
        == "http://localhost:5000/attachments?foo=%E8%BD%AC%E6%8D%A2%E6%88%91"
    )

    # 防御
    url = None
    assert url_encode(url) is None


def test_omit_none_fields():
    class ModelA(BaseModel):
        i: int = Field(default=1)
        i2: int = Field(default=2)

    @omit_none_fields_before_validate("i", "model_a")
    class ModelB(BaseModel):
        i: int = Field(default=1)
        i2: int = Field(default=2)
        model_a: 'ModelA' = Field(default_factory=ModelA)

    ModelB.update_forward_refs(ModelA=ModelA)

    assert ModelA().i == 1
    assert ModelB().i == 1
    assert ModelA(i=2).i == 2
    assert ModelB(i=2).i == 2
    with pytest.raises(ValidationError) as exc:
        ModelA(i=None)
    assert "none is not an allowed value" in str(exc)
    assert ModelB(i=None, model_a=None).i == 1
    with pytest.raises(ValidationError) as exc:
        ModelB(i2=None)
    assert "none is not an allowed value" in str(exc)


def test_filter_none():
    # 非 dict 类型不受影响
    assert filter_none("123") == "123"
    # 过滤 none in dict values
    data = {
        "a": 1,
        "b": None,
        "c": {
            "d": 4,
            "e": None,
            "f": {
                "g": 7,
                "h": None
            }
        },
        "i": [1, 2, None, {"j": 10, "k": None}]
    }
    assert filter_none(data) == {
        "a": 1,
        "c": {
            "d": 4,
            "f": {
                "g": 7
            }
        },
        "i": [1, 2, {"j": 10}]
    }
