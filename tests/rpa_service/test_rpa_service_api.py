import json
from pytest import mark

from lepollo.mock import patch_config

from robot_processor.client.conf import app_config
from robot_processor.enums import JobType
from robot_processor.rpa_service.models import RpaConfig


def test_rpa_list(client):
    resp = client.get('/v1/service/rpa')
    assert resp.json["succeed"] is True
    assert len(resp.json["data"]["rpa"]) > 0
    first_10_rpa = resp.json["data"]["rpa"][:10]
    first_10_rpa_names = [rpa["name"] for rpa in first_10_rpa]

    with patch_config(app_config) as conf:
        conf.RPA_TOP10 = json.dumps(first_10_rpa_names)
        resp = client.get('/v1/service/rpa/recommend')
        assert resp.status_code == 200
        assert resp.json['data']['rpa'] == first_10_rpa


def test_filter_by_tag(client):
    tag = "ERP相关"
    resp = client.get('/v1/service/rpa', query_string=dict(tag="ERP相关"))
    assert resp.json["succeed"] is True
    assert all(map(lambda _rpa: _rpa["tag"] == tag, resp.json["data"]["rpa"]))


def test_tag_list(client):
    resp = client.get('/v1/service/rpa/tags')
    assert resp.status_code == 200
    assert len(set(tag['name'] for tag in resp.json["data"]["tag"])) > 0


def test_subscribe_and_unscribe(client, requests_mock, mock_action_client, rpa):
    from robot_processor.rpa_service.models import RpaContext
    from robot_processor.shop.models import SubscribeInfo
    from robot_processor.rpa_service.models import SubscribeOperator

    mock_rpa = rpa(task=JobType.RPA_CLIENT)
    assert RpaContext.query.filter(RpaContext.org_id == client.org_id).first() is None
    assert SubscribeInfo.query.filter(SubscribeInfo.org_id == client.org_id).first() is None

    mock_subscribe_workflow = requests_mock.post('/rpacontrol/api/feisuo/workflows',
                                                 json={'success': True, 'error': '', 'data': {'workflow_id': 1}})
    mock_enable_workflow = requests_mock.put('/rpacontrol/api/feisuo/workflows/1',
                                             json={'success': True, 'error': '',
                                                   'data': {'workflow_id': 1, 'state': 'ENABLED'}})
    resp = client.post('/v1/service/rpa/subscribe', json={"rpa": [mock_rpa.id]})
    assert resp.json["succeed"] is True
    assert RpaContext.query.filter(RpaContext.org_id == client.org_id).first().enabled
    assert (SubscribeInfo.query.filter(SubscribeInfo.org_id == client.org_id).first().subscribe_operator ==
            SubscribeOperator.SUBSCRIBE)
    assert mock_subscribe_workflow.call_count == 1
    assert mock_enable_workflow.call_count == 1

    resp = client.post('/v1/service/rpa/unsubscribe/dry-run', json={"rpa": [mock_rpa.id]})
    assert resp.json["succeed"] is True
    data = resp.json['data']
    assert 'referred' in data
    assert len(data['referred']) == 1
    assert data['referred'][0]['id'] == mock_rpa.id

    resp = client.post('/v1/service/rpa/unsubscribe', json={"rpa": [mock_rpa.id]})
    assert resp.json["succeed"] is True
    assert (RpaContext.query.filter(RpaContext.org_id == client.org_id, RpaContext.rpa_id == mock_rpa.id)
            .first().enabled is False)
    assert (SubscribeInfo.query.filter(SubscribeInfo.org_id == client.org_id,
                                       SubscribeInfo.subscribe_operator == SubscribeOperator.UNSUBSCRIBE).first()
            is not None)


@mark.order(1)
def test_create_or_update_rpa_configs(client):
    rpa_config = RpaConfig.query.filter(RpaConfig.org_id == client.shop.org_id).first()
    assert rpa_config is None

    resp = client.get("/v1/service/rpa/configs")
    assert resp.json["data"] == {}

    resp = client.get(
        f"/v1/service/rpa/shop_config?sids={client.shop.sid}&category=qianniu_deliver_message",
        headers={"Authentication-Token": "#"}
    )
    assert resp.json["data"] == {}

    resp = client.post("/v1/service/rpa/configs", json={
        "config": {
            "qianniu_deliver_message": {
                "shop_infos": {
                    client.shop.sid: {
                        "sid": client.shop.sid,
                        "platform": client.shop.platform,
                        "deliver_accounts": ["xxxx"],
                        "deliver_account_groups": []
                    }
                },
                "enabled": True
            }
        }
    })

    want = {
        "qianniu_deliver_message": {
            "shop_infos": {
                client.shop.sid: {
                    "sid": client.shop.sid,
                    "platform": client.shop.platform,
                    "deliver_accounts": ["xxxx"],
                    "deliver_account_groups": []
                }
            },
            "extra": None,
            "enabled": True
        },
        "pdd_deliver_message": None,
    }

    assert resp.json["data"] == want

    rpa_config = RpaConfig.query.filter(RpaConfig.org_id == client.shop.org_id).first()
    assert rpa_config is not None
    assert rpa_config.config == want

    resp = client.get("/v1/service/rpa/configs")
    assert resp.json["data"] == want

    resp = client.get(
        f"/v1/service/rpa/shop_config?sids={client.shop.sid}&category=qianniu_deliver_message",
        headers={"Authentication-Token": "#"}
    )
    shop_infos = (want.get("qianniu_deliver_message") or {}).get("shop_infos")
    for sid, shop_info in shop_infos.items():
        shop_info.update({"extra": None})
    assert resp.json["data"] == shop_infos

    resp = client.get(
        f"/v1/service/rpa/shop_config?sids={client.shop.sid}&category=pdd_deliver_message",
        headers={"Authentication-Token": "#"}
    )
    assert resp.json["data"] == {}

    resp = client.post("/v1/service/rpa/configs", json={
        "config": {
            "qianniu_deliver_message": {
                "shop_infos": {
                    client.shop.sid: {
                        "sid": client.shop.sid,
                        "platform": client.shop.platform,
                        "deliver_accounts": ["xxxx"],
                        "deliver_account_groups": []
                    }
                },
                "enabled": False
            }
        }
    })
    assert resp.status_code == 200

    resp = client.get(
        f"/v1/service/rpa/shop_config?sids={client.shop.sid}&category=qianniu_deliver_message",
        headers={"Authentication-Token": "#"}
    )
    assert resp.json["data"] == {}
