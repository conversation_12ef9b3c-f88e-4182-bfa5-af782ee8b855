from pytest import fixture
from faker import Faker
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.db import db
from robot_processor.form.models import WidgetInfo
from robot_processor.form.schemas import RpaDataBinding
from robot_processor.refund.models import TaobaoRefund
from robot_processor.refund.schemas import TaobaoRefundInfo, RefundStatus
from robot_processor.refund.services import update_refund_info


fake = Faker("zh_CN")


@fixture
def mock_taobao_refund():
    refund_info = TaobaoRefundInfo(
        company_name="圆通快递",
        sid="YT123",
        status=RefundStatus.SUCCESS,
        refund_id=fake.pyint(),
    )
    refund = TaobaoRefund()
    refund.refund_id = int(refund_info.refund_id)
    refund.seller_nick = refund.buyer_nick = "ut"
    refund.tid = refund.oid = 123
    refund.jdp_response = {"refund_get_response": {"refund": refund_info.dict()}}
    refund.jdp_created = refund.jdp_modified = fake.date_time()
    refund.status = 'SUCCESS'
    db.session.add(refund)
    db.session.commit()

    yield refund


def test_update_refund_info(mock_business_order, mock_taobao_refund):
    mock_business_order.data["退款信息 key"] = str(mock_taobao_refund.refund_id)
    flag_modified(mock_business_order, "data")
    db.session.commit()

    widget_info_list = [
        WidgetInfo.View.RawStep(
            key="退货快递公司 key",
            type="string",
            option_value=dict(
                data_binding=dict(RpaDataBinding(expression="company_name", level="$"))
            ),
        ),
        WidgetInfo.View.RawStep(
            key="退货快递单号 key",
            type="string",
            option_value=dict(
                data_binding=dict(RpaDataBinding(expression="sid", level="$"))
            ),
        ),
        WidgetInfo.View.RawStep(
            key="退款进度 key",
            type="radio-dropdown",
            option_value=dict(
                data_binding=dict(RpaDataBinding(expression="status", level="$"))
            ),
        ),
    ]
    refund_info = mock_taobao_refund.get_refund_info()

    update_refund_info(
        refund_info, mock_business_order.form_id, "退款信息 key", widget_info_list
    )

    assert mock_business_order.data["退货快递公司 key"] == refund_info.company_name
    assert mock_business_order.data["退货快递单号 key"] == refund_info.sid
    assert mock_business_order.data["退款进度 key"] == [
        {"label": refund_info.status.label, "value": refund_info.status.label}
    ]
