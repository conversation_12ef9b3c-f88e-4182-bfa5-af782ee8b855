from pytest import fixture

from robot_processor.ext import db
from robot_processor.form.system_widget_utils import create_step_0


@fixture
def setup_form(client, mock_form, step_factory,
               mock_widget_collection, widget, widget_info_factory):
    step_0 = create_step_0(mock_form)
    step_0.is_dirty = False
    db.session.add(step_0)
    db.session.flush()
    step_1, step_2 = step_factory.create_batch(2, form=mock_form)
    widget_1 = widget('单行输入')
    widget_2 = widget('订单/子订单')
    step_0.next_step_ids = [step_1.step_uuid]
    step_1.prev_step_ids = [step_0.step_uuid]
    step_1.next_step_ids = [step_2.step_uuid]
    step_2.prev_step_ids = [step_1.step_uuid]
    step_1.widget_collection_id = mock_widget_collection.id
    ui_schema = [
        {
            "widget_id": widget_1.id,
            "option_value": {
                "label": "单行输入"
            }, "order": 0
        },
        {
            "widget_id": widget_2.id,
            "option_value": {
                "mode": "order",
                "label": "订单/子订单",
                "options": "买家最近下单的20个订单",
                "defaultValue": "订单确定时，自动填充"
            }, "order": 1
        }
    ]
    mock_widget_collection.widget_info = [
        widget_info_factory(**widget)
        for widget in ui_schema
    ]
    mock_form.subscribe(client.shop, True)
    db.session.commit()
    step_0.update_raw_step()
    step_1.update_raw_step()
    step_2.update_raw_step()

    yield mock_form.wraps(client.shop), (step_0, step_1, step_2), mock_widget_collection


@fixture
def setup_event_form(client, mock_form, step_factory, mock_widget_collection, widget, widget_info_factory):
    from uuid import uuid4
    from robot_processor.enums import StepType
    step_0 = create_step_0(mock_form)
    step_0.is_dirty = False
    db.session.add(step_0)
    db.session.flush()
    widget_info = widget_info_factory(widget_id=widget("单行输入").id, option_value={"label": "测试"}, key=str(uuid4()))
    mock_widget_collection.widget_info = [widget_info]
    step_event = step_factory.create(
        form=mock_form,
        event="TAOBAO_REFUND",
        event_shortcuts=["REFUND"],
        event_filters={"relation": "and", "conditions": []},
        step_type=StepType.event,
        widget_collection_id=mock_widget_collection.id,
        data={"validation_config": {
            "enabled": True,
            "validations": [
                {
                    "name": "单元测试",
                    "filters": [
                        {
                            "o": ["EQ"],
                            "b": {"type_spec": {"type": "string"}, "qualifier": "const", "const": {"value": "ut"}},
                            "a": {"type_spec": {"type": "string"}, "qualifier": "var", "var": {"path": widget_info.key}}
                        }
                    ],
                    "check_type": 2,
                }
            ]
        }}
    )
    step_0.next_step_ids = [step_event.step_uuid]
    step_event.prev_step_ids = [step_0.step_uuid]
    mock_form.event = "TAOBAO_REFUND"
    mock_form.form_mold = "EVENT"
    mock_form.subscribe(client.shop, True)
    db.session.commit()
    step_0.update_raw_step()
    step_event.update_raw_step()

    yield {
        "form": mock_form.wraps(client.shop),
        "step_0": step_0,
        "step": step_event,
    }
