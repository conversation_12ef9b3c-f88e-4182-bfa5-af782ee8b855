from flask import current_app
from pytest import fixture

from external.event import EventRule, RpaEventType
from robot_processor.business_order.models import BusinessOrder
from robot_processor.db import db
from robot_processor.form.system_widget_utils import create_step_0


@fixture
def request_body():
    yield {
        "event": "taobaoRefundWithoutReturn",
        "data": {
            "shop_name": "诗唯雅",
            "runtime": {
                "token": "",
                "mode": "",
                "execution_id": 0,
                "robot_name": "Tasks",
                "robot_version": "",
                "client_version": "",
                "client_env": "",
                "client_service_port": "",
                "local_schedule_robot_id": ""
            },
            "result": {
                "branch_policy": "备注包含骗子等-人工处理",
                "order_id": "3928462490205177444",
                "order_amount": 71.75,
                "flag_color": "灰色",
                "final_order_remark": "|亲亲，这边看您申请了退款，请问是什么原因呢~",
                "express_number": "78806921739179",
                "delivery_logistics_company": "中通快递",
                "logistics_update": "9小时49分钟",
                "logistics_update_count": 8,
                "history_logistics": "【南京市】 快件已发往 昆明转运中心【南京市】 快件已到达 南京转运中心"
                                     "【常州市】 快件已发往 昆明转运中心【常州市】 快件已到达 常州转运中心"
                                     "【常州市】 快件已发往 常州转运中心"
                                     "【常州市】 常州中心市场二部（0519-83625012,0519-69656999）"
                                     "常州安达（18806125302）已揽收等待揽收中商品已经下单",
                "latest_logistics": "【南京市】 快件已发往 昆明转运中心",
                "after_sale_id": "280155097881174474",
                "refund_reason": "不想要了",
                "refund_amount": "71.75",
                "refund_logistics_number": "",
                "refund_logistics_company": "",
                "refund_history_logistics": "",
                "refund_action": "转人工",
                "reject_reason": "",
                "send_express_group_data": "",
                "express_group_data": "",
                "process_time": "2024-06-13 13:42:31",
                "suggest_manual_follow_up": "",
                "execution_record_status": "",
                "local_execution_record_id": 1,
                "check_content": "",
                "process_result": "成功",
                "fail_reason": ""
            }
        }
    }


@fixture(autouse=True)
def testbed(mock_form, step_factory, mock_widget_collection, widget, widget_info_factory, shop_factory,
            kiosk_org_factory, mocker):
    mocker.patch("robot_processor.ext.action_log_producer")
    shop = shop_factory.create()
    db.session.add(shop)
    db.session.commit()
    db.session.add(kiosk_org_factory.create(id=shop.org_id))
    db.session.commit()
    step_0 = create_step_0(mock_form)
    step_0.is_dirty = False
    db.session.add(step_0)
    db.session.commit()
    step = step_factory.create(form=mock_form)
    step_0.next_step_ids = [step.step_uuid]
    step.prev_step_ids = [step_0.step_uuid]
    step.widget_collection_id = mock_widget_collection.id
    ui_schema = [
        {
            "widget_id": widget("单行输入").id,
            "key": "branch_policy_key",
            "option_value": {"label": "分支策略"},
        },
        {
            "widget_id": widget("单行输入").id,
            "key": "order_id_key",
            "option_value": {"label": "订单编号"},
        },
        {
            "widget_id": widget("数字输入").id,
            "key": "order_amount_key",
            "option_value": {"label": "订单金额"},
        },
        {
            "widget_id": widget("单行输入").id,
            "key": "flag_color_key",
            "option_value": {"label": "插旗颜色"},
        }
    ]
    mock_widget_collection.widget_info = [
        widget_info_factory(**widget_info)
        for widget_info in ui_schema
    ]
    form_shop = mock_form.subscribe(shop, True)
    db.session.commit()
    step_0.update_raw_step()
    step.update_raw_step()
    mock_form.snapshot()

    yield {
        "shop": shop,
        "form": mock_form
    }

    db.session.delete(form_shop)
    db.session.commit()
    db.session.delete(shop)
    db.session.commit()


def test规则关闭(client, testbed, request_body):
    response = client.post(
        "/service/event",
        headers={"Authentication-Token": current_app.config["SERVICE_TOKEN"]},
        json=request_body
    )
    assert response.json["succeed"] is False
    assert response.json["message"] == "未找到对应的规则"


def test淘宝仅退款(client, testbed, request_body):
    db.session.add(EventRule(
        event=RpaEventType.TAOBAO_REFUND_WITHOUT_RETURN,
        shop_key="诗唯雅",
        org_id=int(testbed["shop"].org_id),
        form_id=testbed["form"].id,
        sid=testbed["shop"].sid,
        platform=testbed["shop"].platform,
    ))
    db.session.commit()
    response = client.post(
        "/service/event",
        headers={"Authentication-Token": current_app.config["SERVICE_TOKEN"]},
        json=request_body
    )
    assert response.json["succeed"]

    bo = BusinessOrder.query.filter_by(form_id=testbed["form"].id).one()
    assert bo.data["branch_policy_key"] == "备注包含骗子等-人工处理"
    assert bo.data["order_id_key"] == "3928462490205177444"
    assert bo.data["order_amount_key"] == 71.75
    assert bo.data["flag_color_key"] == "灰色"
