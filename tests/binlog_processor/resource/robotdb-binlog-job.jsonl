{
  "utIdent": "job insert",
  "data": [
    {
      "id": "200702080",
      "created_at": "1681793082",
      "updated_at": "1681793082",
      "status": "INIT",
      "step_id": "3554295",
      "business_order_id": "50778563",
      "message_id": "4312e380f8244e35bec72e4a2e6936c8",
      "exc_info": null,
      "data": "{}",
      "raw_step": null,
      "extra_data": "{}",
      "step_uuid": "db71bbf73bb94808862873c27896b934",
      "assignee": null,
      "process_mark": "UNPROCESSED",
      "assignee_type": "2",
      "deadline_info": "{}",
      "assignee_id": null,
      "archived": null,
      "raw_step_v2": "{}",
      "assignee_user_id": null
    }
  ],
  "database": "robotdb",
  "es": 1681793081000,
  "id": 51308517,
  "isDdl": false,
  "mysqlType": {
    "id": "int(11)",
    "created_at": "int(11)",
    "updated_at": "int(11)",
    "status": "enum('INIT','PENDING','RUNNING','FAILED','SUCCEED')",
    "step_id": "int(11)",
    "business_order_id": "int(11)",
    "message_id": "varchar(128)",
    "exc_info": "text",
    "data": "json",
    "raw_step": "json",
    "extra_data": "json",
    "step_uuid": "varchar(64)",
    "assignee": "varchar(128)",
    "process_mark": "enum('UNPROCESSED','ACCEPT','REJECT','RECALL','CLOSE','REOPEN','SAVE','UPGRADE','ASSIGN','PICK','DELIVER','RETRY','PAUSE','UNPAUSE','DELETE','RECOVER')",
    "assignee_type": "enum('USER','ASSISTANT','RPA','LEYAN','LDAP')",
    "deadline_info": "json",
    "assignee_id": "varchar(32)",
    "archived": "tinyint(1)",
    "raw_step_v2": "json",
    "assignee_user_id": "int(11)"
  },
  "old": null,
  "pkNames": [
    "id"
  ],
  "sql": "",
  "sqlType": {
    "id": 4,
    "created_at": 4,
    "updated_at": 4,
    "status": 4,
    "step_id": 4,
    "business_order_id": 4,
    "message_id": 12,
    "exc_info": -4,
    "data": 12,
    "raw_step": 1111,
    "extra_data": 12,
    "step_uuid": 12,
    "assignee": 12,
    "process_mark": 4,
    "assignee_type": 4,
    "deadline_info": 12,
    "assignee_id": 12,
    "archived": -6,
    "raw_step_v2": 12,
    "assignee_user_id": 4
  },
  "table": "job",
  "ts": 1681793081871,
  "type": "INSERT"
}
// for the readable
{
  "utIdent":  "job update with process mark any to retry",
  "data": [
    {
      "id": "200702080",
      "created_at": "1681793082",
      "updated_at": "1681793083",
      "status": "FAILED",
      "step_id": "3554295",
      "business_order_id": "50778563",
      "message_id": "4312e380f8244e35bec72e4a2e6936c8",
      "exc_info": null,
      "data": "{}",
      "raw_step": null,
      "extra_data": "{}",
      "step_uuid": "db71bbf73bb94808862873c27896b934",
      "assignee": null,
      "process_mark": "RETRY",
      "assignee_type": "2",
      "deadline_info": "{}",
      "assignee_id": null,
      "archived": null,
      "raw_step_v2": "{}",
      "assignee_user_id": null
    }
  ],
  "database": "robotdb",
  "es": 1681793083000,
  "id": 51308532,
  "isDdl": false,
  "mysqlType": {
    "id": "int(11)",
    "created_at": "int(11)",
    "updated_at": "int(11)",
    "status": "enum('INIT','PENDING','RUNNING','FAILED','SUCCEED')",
    "step_id": "int(11)",
    "business_order_id": "int(11)",
    "message_id": "varchar(128)",
    "exc_info": "text",
    "data": "json",
    "raw_step": "json",
    "extra_data": "json",
    "step_uuid": "varchar(64)",
    "assignee": "varchar(128)",
    "process_mark": "enum('UNPROCESSED','ACCEPT','REJECT','RECALL','CLOSE','REOPEN','SAVE','UPGRADE','ASSIGN','PICK','DELIVER','RETRY','PAUSE','UNPAUSE','DELETE','RECOVER')",
    "assignee_type": "enum('USER','ASSISTANT','RPA','LEYAN','LDAP')",
    "deadline_info": "json",
    "assignee_id": "varchar(32)",
    "archived": "tinyint(1)",
    "raw_step_v2": "json",
    "assignee_user_id": "int(11)"
  },
  "old": [
    {
      "updated_at": "1681793082",
      "status": "FAILED"
    }
  ],
  "pkNames": [
    "id"
  ],
  "sql": "",
  "sqlType": {
    "id": 4,
    "created_at": 4,
    "updated_at": 4,
    "status": 4,
    "step_id": 4,
    "business_order_id": 4,
    "message_id": 12,
    "exc_info": -4,
    "data": 12,
    "raw_step": 1111,
    "extra_data": 12,
    "step_uuid": 12,
    "assignee": 12,
    "process_mark": 4,
    "assignee_type": 4,
    "deadline_info": 12,
    "assignee_id": 12,
    "archived": -6,
    "raw_step_v2": 12,
    "assignee_user_id": 4
  },
  "table": "job",
  "ts": 1681793083149,
  "type": "UPDATE"
}
// for the readable
{
  "utIdent":  "job update with status failed",
  "data": [
    {
      "id": "200702080",
      "created_at": "1681793082",
      "updated_at": "1681793083",
      "status": "FAILED",
      "step_id": "3554295",
      "business_order_id": "50778563",
      "message_id": "4312e380f8244e35bec72e4a2e6936c8",
      "exc_info": "whoops!",
      "data": "{}",
      "raw_step": null,
      "extra_data": "{}",
      "step_uuid": "db71bbf73bb94808862873c27896b934",
      "assignee": null,
      "process_mark": "UNPROCESSED",
      "assignee_type": "ASSISTANT",
      "deadline_info": "{}",
      "assignee_id": null,
      "archived": null,
      "raw_step_v2": "{}",
      "assignee_user_id": null
    }
  ],
  "database": "robotdb",
  "es": 1681793083000,
  "id": 51308533,
  "isDdl": false,
  "mysqlType": {
    "id": "int(11)",
    "created_at": "int(11)",
    "updated_at": "int(11)",
    "status": "enum('INIT','PENDING','RUNNING','FAILED','SUCCEED')",
    "step_id": "int(11)",
    "business_order_id": "int(11)",
    "message_id": "varchar(128)",
    "exc_info": "text",
    "data": "json",
    "raw_step": "json",
    "extra_data": "json",
    "step_uuid": "varchar(64)",
    "assignee": "varchar(128)",
    "process_mark": "enum('UNPROCESSED','ACCEPT','REJECT','RECALL','CLOSE','REOPEN','SAVE','UPGRADE','ASSIGN','PICK','DELIVER','RETRY','PAUSE','UNPAUSE','DELETE','RECOVER')",
    "assignee_type": "enum('USER','ASSISTANT','RPA','LEYAN','LDAP')",
    "deadline_info": "json",
    "assignee_id": "varchar(32)",
    "archived": "tinyint(1)",
    "raw_step_v2": "json",
    "assignee_user_id": "int(11)"
  },
  "old": [
    {
      "exc_info": null
    }
  ],
  "pkNames": [
    "id"
  ],
  "sql": "",
  "sqlType": {
    "id": 4,
    "created_at": 4,
    "updated_at": 4,
    "status": 4,
    "step_id": 4,
    "business_order_id": 4,
    "message_id": 12,
    "exc_info": 2005,
    "data": 12,
    "raw_step": 1111,
    "extra_data": 12,
    "step_uuid": 12,
    "assignee": 12,
    "process_mark": 4,
    "assignee_type": 4,
    "deadline_info": 12,
    "assignee_id": 12,
    "archived": -6,
    "raw_step_v2": 12,
    "assignee_user_id": 4
  },
  "table": "job",
  "ts": 1681793083251,
  "type": "UPDATE"
}
// for the readable
{
  "utIdent":  "job update with status from failed to succeed",
  "data": [
    {
      "id": "200702107",
      "created_at": "1681793090",
      "updated_at": "1681793095",
      "status": "SUCCEED",
      "step_id": "5660469",
      "business_order_id": "50778567",
      "message_id": "69ea8dbb70304e628d2bb4fe46ab272b",
      "exc_info": "",
      "data": "{}",
      "raw_step": null,
      "extra_data": "{}",
      "step_uuid": "135a7b00fb2e4f919f7810f6bf33088d",
      "assignee": null,
      "process_mark": "ACCEPT",
      "assignee_type": "2",
      "deadline_info": "{}",
      "assignee_id": null,
      "archived": null,
      "raw_step_v2": "{}",
      "assignee_user_id": null
    }
  ],
  "database": "robotdb",
  "es": 1681793095000,
  "id": 51308678,
  "isDdl": false,
  "mysqlType": {
    "id": "int(11)",
    "created_at": "int(11)",
    "updated_at": "int(11)",
    "status": "enum('INIT','PENDING','RUNNING','FAILED','SUCCEED')",
    "step_id": "int(11)",
    "business_order_id": "int(11)",
    "message_id": "varchar(128)",
    "exc_info": "text",
    "data": "json",
    "raw_step": "json",
    "extra_data": "json",
    "step_uuid": "varchar(64)",
    "assignee": "varchar(128)",
    "process_mark": "enum('UNPROCESSED','ACCEPT','REJECT','RECALL','CLOSE','REOPEN','SAVE','UPGRADE','ASSIGN','PICK','DELIVER','RETRY','PAUSE','UNPAUSE','DELETE','RECOVER')",
    "assignee_type": "enum('USER','ASSISTANT','RPA','LEYAN','LDAP')",
    "deadline_info": "json",
    "assignee_id": "varchar(32)",
    "archived": "tinyint(1)",
    "raw_step_v2": "json",
    "assignee_user_id": "int(11)"
  },
  "old": [
    {
      "updated_at": "1681793090",
      "status": "FAILED",
      "exc_info": null,
      "process_mark": "UNPROCESSED"
    }
  ],
  "pkNames": [
    "id"
  ],
  "sql": "",
  "sqlType": {
    "id": 4,
    "created_at": 4,
    "updated_at": 4,
    "status": 4,
    "step_id": 4,
    "business_order_id": 4,
    "message_id": 12,
    "exc_info": 2005,
    "data": 12,
    "raw_step": 1111,
    "extra_data": 12,
    "step_uuid": 12,
    "assignee": 12,
    "process_mark": 4,
    "assignee_type": 4,
    "deadline_info": 12,
    "assignee_id": 12,
    "archived": -6,
    "raw_step_v2": 12,
    "assignee_user_id": 4
  },
  "table": "job",
  "ts": 1681793095385,
  "type": "UPDATE"
}
