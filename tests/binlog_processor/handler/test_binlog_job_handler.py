from typing import List
from dataclasses import dataclass
from unittest.mock import MagicMock

from pytest import fixture, mark


@dataclass
class MockedData:
    raw_message_list: List[dict] = None
    archived_message: dict = None  # 归档操作的 binlog
    delete_type_message: dict = None  # 删除类型的 binlog


@dataclass
class MockedPatch:
    process_by_exception_ruler: MagicMock = None
    process_for_exception_pool: MagicMock = None


class TestBinlogJobHandler:
    """消费处理 job binlog"""

    mocked_data = MockedData()
    mocked_patch = MockedPatch()

    @fixture(autouse=True)
    def setup_resource(self):
        from io import TextIOWrapper
        from pkg_resources import resource_stream

        resource_package = "tests.binlog_processor.resource"
        with resource_stream(resource_package, "robotdb-binlog-job.jsonl") as stream:
            self.mocked_data.raw_message_list = (
                TextIOWrapper(stream, encoding="utf8")
                .read()
                .split("// for the readable")
            )
        with resource_stream(resource_package, "robotdb-binlog-job-action-archived.json") as stream:
            self.mocked_data.archived_message = TextIOWrapper(stream, encoding="utf8").read()
        with resource_stream(resource_package, "robotdb-binlog-job-type-delete.json") as stream:
            self.mocked_data.delete_type_message = TextIOWrapper(stream, encoding="utf8").read()

        yield

    @fixture
    def setup_patch(self, mocker):
        from robot_processor.business_order.binlog.job_processor import exception_rule

        self.mocked_patch.process_by_exception_ruler = mocker.patch.object(
            exception_rule, "process_by_exception_ruler"
        )
        self.mocked_patch.process_for_exception_pool = mocker.patch.object(
            exception_rule, "process_for_exception_pool"
        )
        yield

    @mark.usefixtures("setup_patch")
    def test_binlog_job_processor(self):
        """仅测试 BinlogMessage 类型不会出错"""
        from robot_processor.business_order.binlog.handler import binlog_job_processor

        for binlog in self.mocked_data.raw_message_list:
            binlog_job_processor(None, binlog)
        message_count = len(self.mocked_data.raw_message_list)

        assert message_count == self.mocked_patch.process_by_exception_ruler.call_count
        assert message_count == self.mocked_patch.process_for_exception_pool.call_count

    @mark.usefixtures("setup_patch")
    def test_binlog_ignore_archive_action(self):
        """测试不处理归档操作"""
        from robot_processor.business_order.binlog.handler import binlog_job_processor

        binlog_job_processor(None, self.mocked_data.archived_message)

        self.mocked_patch.process_by_exception_ruler.assert_not_called()
        self.mocked_patch.process_for_exception_pool.assert_not_called()

    @mark.usefixtures("setup_patch")
    def test_binlog_ignore_type_delete(self):
        """测试不处理删除操作"""
        from robot_processor.business_order.binlog.handler import binlog_job_processor

        binlog_job_processor(None, self.mocked_data.delete_type_message)

        self.mocked_patch.process_by_exception_ruler.assert_not_called()
        self.mocked_patch.process_for_exception_pool.assert_not_called()
