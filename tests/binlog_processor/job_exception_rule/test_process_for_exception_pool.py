import logging
from dataclasses import dataclass
from typing import Dict

import arrow
import sqlalchemy as sa
from cattrs import structure
from flask import current_app
from more_itertools import first
from pytest import fixture
from pytest import mark

from robot_processor.business_order.binlog.job_processor import exception_ruler_keeper
from robot_processor.business_order.binlog.job_processor.exception_rule import process_by_exception_ruler
from robot_processor.business_order.binlog.job_processor.exception_rule import process_for_exception_pool
from robot_processor.business_order.binlog.job_processor.types import BinlogJob
from robot_processor.business_order.binlog.job_processor.types import ExceptionStrategy
from robot_processor.business_order.binlog.types import BinlogMessage
from robot_processor.business_order.exception_rule import ExceptionBusinessOrder
from robot_processor.business_order.models import Job
from robot_processor.ext import db


class MockHandler(logging.Handler):
    records = []

    def emit(self, record):
        self.records.append(record.getMessage())


@dataclass
class MockedData:
    messages: Dict[str, dict] = None

    binlog_message: BinlogMessage[BinlogJob] = None
    binlog_job: BinlogJob = None
    old_info: dict = None

    exception_in_db: int = None
    business_order = None


class TestBinlogJobHandler:
    """消费处理 job binlog"""

    mocked_data = MockedData()

    @fixture(autouse=True)
    def testbed(self, client):
        from tests.testbed import BusinessOrderBuilder
        from tests.testbed import types

        resource = "binlog.job_processor"
        builder = BusinessOrderBuilder(resource, client.shop)
        builder.build()

        self.mocked_data.business_order = builder.business_order
        yield types.BusinessOrderTestbed(client.shop, builder.form, builder.business_order)

    @fixture(autouse=True)
    def setup_logger_capture(self):
        from loguru import logger

        handler = MockHandler(level="DEBUG")
        logger.add(handler)
        self.mocked_data.log_capture = handler
        yield

    @fixture(autouse=True)
    def setup_exception_ruler_keeper(self, client):
        exception_ruler_keeper.init()
        yield

    @fixture(autouse=True)
    def setup_resource(self):
        from io import TextIOWrapper
        from json import loads

        from pkg_resources import resource_stream

        resource_package = "tests.binlog_processor.resource"
        with resource_stream(resource_package, "robotdb-binlog-job.jsonl") as stream:
            self.mocked_data.messages = {
                message["utIdent"]: message
                for message in map(
                    loads,
                    (TextIOWrapper(stream, encoding="utf8").read().split("// for the readable")),
                )
            }
        yield

    @fixture
    def setup_process_job_with_status_init(self):
        message = self.mocked_data.messages["job insert"]
        binlog_message = structure(message, BinlogMessage[BinlogJob])
        self.mocked_data.binlog_message = binlog_message
        self.mocked_data.binlog_job = first(binlog_message.data)
        yield

    @mark.usefixtures("setup_process_job_with_status_init")
    def test_process_job_with_status_init(self):
        """应当跳过"""
        binlog_message = self.mocked_data.binlog_message
        binlog_job = self.mocked_data.binlog_job
        readable_job = binlog_job.as_readable()
        old_info = first(binlog_message.old or [], None)
        process_result = process_by_exception_ruler(readable_job)
        assert process_result is None
        assert ExceptionStrategy.which_strategy(readable_job, old_info, process_result) is ExceptionStrategy.REMOVE

    @fixture
    def setup_remove(self, db):
        message = self.mocked_data.messages["job update with status from failed to succeed"]
        binlog_message = structure(message, BinlogMessage[BinlogJob])
        binlog_job: BinlogJob = first(binlog_message.data)
        self.mocked_data.binlog_message = binlog_message
        self.mocked_data.binlog_job = binlog_job

        job = Job()
        job.id = binlog_job.id
        job._raw_step_v2 = {"step_type": "human"}
        db.session.add(job)
        exception_business_order = self.get_default_exception_business_order()
        exception_business_order.updated_at = arrow.get(binlog_job.created_at).shift(seconds=-1).datetime
        exception_business_order.business_order_id = binlog_job.business_order_id
        exception_business_order.current_job_id = binlog_job.id
        db.session.add(exception_business_order)
        db.session.flush()
        self.mocked_data.exception_in_db = exception_business_order.id
        yield

    @mark.usefixtures("setup_remove")
    def test_remove(self):
        """job status from failed to success"""
        binlog_message = self.mocked_data.binlog_message
        binlog_job = self.mocked_data.binlog_job
        readable_job = binlog_job.as_readable()
        old_info = first(binlog_message.old)
        process_result = process_by_exception_ruler(readable_job)
        assert process_result is None
        assert ExceptionStrategy.which_strategy(readable_job, old_info, process_result) is ExceptionStrategy.REMOVE
        process_for_exception_pool(readable_job, old_info, process_result)
        db.session.expire_all()
        assert ExceptionBusinessOrder.query.get(self.mocked_data.exception_in_db) is None

    @fixture
    def setup_processing(self, testbed, db):
        message = self.mocked_data.messages["job update with process mark any to retry"]
        binlog_message = structure(message, BinlogMessage[BinlogJob])
        binlog_job: BinlogJob = first(binlog_message.data)
        self.mocked_data.binlog_message = binlog_message
        self.mocked_data.binlog_job = binlog_job

        job = db.session.get(Job, testbed.business_order.current_job_id)
        job.id = binlog_job.id
        testbed.business_order.current_job_id = binlog_job.id
        job._raw_step_v2 = {"step_type": "human"}
        exception_business_order = self.get_default_exception_business_order()
        exception_business_order.updated_at = arrow.get(binlog_job.created_at).shift(seconds=-1).datetime
        exception_business_order.business_order_id = binlog_job.business_order_id
        exception_business_order.current_job_id = binlog_job.id
        db.session.add(exception_business_order)
        db.session.flush()
        self.mocked_data.exception_in_db = exception_business_order.id
        yield

    @mark.usefixtures("setup_processing")
    def test_processing(self):
        """异常状态为重试中"""
        binlog_message = self.mocked_data.binlog_message
        binlog_job = self.mocked_data.binlog_job
        readable_job = binlog_job.as_readable()
        old_info = first(binlog_message.old)
        process_result = process_by_exception_ruler(readable_job)
        # assert process_result is None
        assert ExceptionStrategy.which_strategy(readable_job, old_info, process_result) is ExceptionStrategy.PROCESSING
        process_for_exception_pool(readable_job, old_info, process_result)

        db.session.expire_all()
        record = ExceptionBusinessOrder.query.get(self.mocked_data.exception_in_db)
        assert record.is_processing

    @fixture
    def setup_add(self, testbed, db):
        message = self.mocked_data.messages["job update with status failed"]
        binlog_message = structure(message, BinlogMessage[BinlogJob])
        binlog_job: BinlogJob = first(binlog_message.data)
        self.mocked_data.binlog_message = binlog_message
        self.mocked_data.binlog_job = binlog_job

        current_app.config["EXCEPTION_RULE_CONFIG"] = {}
        current_app.config["EXCEPTION_RULE_CONFIG"]["human"] = True

        db.session.execute(sa.text("SET FOREIGN_KEY_CHECKS=0;"))
        job = db.session.get(Job, testbed.business_order.current_job_id)
        testbed.business_order.id = binlog_job.business_order_id
        job.business_order_id = binlog_job.business_order_id
        job.id = binlog_job.id
        testbed.business_order.current_job_id = binlog_job.id
        job._raw_step_v2 = {"step_type": 1, "name": "ut"}
        yield

    @mark.usefixtures("setup_add")
    def test_add(self):
        """测试需要添加到异常池"""
        binlog_message = self.mocked_data.binlog_message
        binlog_job = self.mocked_data.binlog_job
        readable_job = binlog_job.as_readable()
        old_info = first(binlog_message.old)
        process_result = process_by_exception_ruler(readable_job)
        assert process_result is not None
        assert ExceptionStrategy.which_strategy(readable_job, old_info, process_result) is ExceptionStrategy.ADD
        process_for_exception_pool(readable_job, old_info, process_result)
        record = ExceptionBusinessOrder.query.filter_by(
            business_order_id=str(binlog_job.business_order_id),
            current_job_id=str(binlog_job.id),
        ).first()
        assert record is not None

    @fixture
    def setup_re_add(self, testbed, db):
        message = self.mocked_data.messages["job update with status failed"]
        binlog_message = structure(message, BinlogMessage[BinlogJob])
        binlog_job: BinlogJob = first(binlog_message.data)
        self.mocked_data.binlog_message = binlog_message
        self.mocked_data.binlog_job = binlog_job

        current_app.config["EXCEPTION_RULE_CONFIG"] = {}
        current_app.config["EXCEPTION_RULE_CONFIG"]["human"] = True

        db.session.execute(sa.text("SET FOREIGN_KEY_CHECKS=0;"))
        job = db.session.get(Job, testbed.business_order.current_job_id)
        testbed.business_order.id = binlog_job.business_order_id
        job.business_order_id = binlog_job.business_order_id
        job.id = binlog_job.id
        testbed.business_order.current_job_id = binlog_job.id
        job._raw_step_v2 = {"step_type": 1, "name": "ut"}

        # 先把异常加入进异常池
        readable_job = binlog_job.as_readable()
        process_result = process_by_exception_ruler(readable_job)
        old_info = first(binlog_message.old)
        process_for_exception_pool(readable_job, old_info, process_result)

        # 修改状态为处理中
        record = (
            db.session.query(ExceptionBusinessOrder)
            .filter(
                ExceptionBusinessOrder.business_order_id == binlog_job.business_order_id,
                ExceptionBusinessOrder.current_job_id == binlog_job.id,
            )
            .first()
        )
        record.is_processing = True

        yield

    @mark.usefixtures("setup_re_add")
    def test_re_add(self):
        binlog_message = self.mocked_data.binlog_message
        binlog_job = self.mocked_data.binlog_job
        readable_job = binlog_job.as_readable()
        old_info = first(binlog_message.old)
        process_result = process_by_exception_ruler(readable_job)
        assert process_result is not None
        assert ExceptionStrategy.which_strategy(readable_job, old_info, process_result) is ExceptionStrategy.ADD

        record = ExceptionBusinessOrder.query.filter_by(
            business_order_id=str(binlog_job.business_order_id),
            current_job_id=str(binlog_job.id),
        ).first()
        assert record.is_processing
        record.updated_at = arrow.get(readable_job.updated_at).shift(days=-1).datetime
        process_for_exception_pool(readable_job, old_info, process_result)
        db.session.refresh(record)
        assert not record.is_processing

    def get_default_exception_business_order(self):
        record = ExceptionBusinessOrder()
        record.exception_info = {}
        record.reason = ""
        record.updater = ""
        record.is_processing = False
        record.org_id = 1
        record.sid = 1
        record.shop_name = ""
        record.shop_platform = ""
        record.sys_type = 1
        record.name = ""
        record.bo_info = {}
        record.business_order_id = 1
        record.bo_created_at = 0
        record.creator_name = ""
        record.current_job_info = {}
        record.current_job_id = 1
        record.current_job_name = ""
        record.current_job_rpa_name = ""
        record.assignee = ""
        record.assignee_name = ""
        record.task_type = ""
        record.auto_retry_status = ""

        return record
