import json
import typing

import cattrs
from loguru import logger
from pytest import fixture

from tests.factories import WidgetFactory, ShopFactory


@fixture(scope="session")
def mock_shop():
    yield ShopFactory.create(org_id='1134', sid='112610831', title='恩兴家纺旗舰店', platform='TMALL')


@fixture(scope="session")
def mock_widget():
    yield WidgetFactory.create(
        label='工单状态',
        schema={
            "type": "string",
            "label": "工单状态",
            "widget_type": "string"
        },
        category='SYSTEM',
        system_widget_data={
            "key": "system_business_order_status",
            "type": "string"
        }
    )


def test_transform(mock_shop, mock_widget):
    from io import TextIOWrapper
    from pkg_resources import resource_stream

    from robot_processor.business_order.binlog.etl.ck_transform import transform
    from robot_processor.business_order.binlog.etl.types import BinlogBo
    from robot_processor.business_order.binlog.types import BinlogMessage

    with resource_stream("tests.binlog_processor.resource", "robotdb-binlog-bo.json") as stream:
        binlog = TextIOWrapper(stream, encoding="utf8").read()
        binlog_message = typing.cast(
            BinlogMessage[BinlogBo],
            cattrs.structure(json.loads(binlog), BinlogMessage[BinlogBo])
        )
        row = transform(binlog_message.data[0], binlog)
        logger.info("row={}", row)
        assert row['shop_title'] == '恩兴家纺旗舰店'
        assert row['form_string_fields']['system_business_order_status'] == '待受理'
