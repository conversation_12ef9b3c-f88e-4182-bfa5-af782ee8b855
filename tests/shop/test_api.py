from pytest import mark

from robot_processor.client import taobao_client
from robot_processor.enums import ErpType
from robot_processor.ext import db
from robot_processor.shop.models import Shop, TaobaoSubuserGrantRecord


class TestGetShopGrantRecord:
    def test_get_grant_record(self, client, grant_record_factory):
        resp = client.get("/v1/shop/grant-record")
        assert resp.status_code == 404
        assert resp.json["error_display"] == "店铺无授权信息"

        record = grant_record_factory.create()
        client.shop.records = [record]
        db.session.commit()
        resp = client.get("/v1/shop/grant-record")
        assert resp.status_code == 200
        assert len(resp.json) > 0


class TestShopGrantView:
    @mark.order(1)
    def test_get(self, client, requests_mock):
        client.shop.platform = "TAOBAO"
        # 缺失参数
        request_patch = requests_mock.post(
            taobao_client.api_url,
            json={
                "shop": {
                    "sid": "123",
                    "title": "123",
                },
                "subaccounts": [
                    {"user_id": 123}
                ]
            },
        )
        resp = client.get("/v1/shop/grant?user_nick=123")
        assert resp.status_code == 400
        assert 'validation_error' in resp.json

        resp = client.get("/v1/shop/grant?user_nick=123:123&access_token=234&open_id=abc")
        assert resp.status_code == 400
        assert resp.json['reason'] == '子账号无法授权'

        resp = client.get("/v1/shop/grant?user_nick=123&access_token=234&open_id=abcd")
        assert resp.status_code == 400
        assert resp.json['reason'] == '当前店铺未在飞梭应用查询到对应店铺，建议联系店铺销售，先进行后台店铺绑定再授权'

        resp = client.get(f"/v1/shop/grant?user_nick={client.shop.nick}:123&access_token=234&open_id=abcd")
        assert resp.status_code == 400
        assert resp.json['reason'] == '缺失授权记录'
        subuser_record = client.shop.get_recent_subuser_record()
        assert subuser_record and subuser_record.access_token == "234"

        resp = client.get(f"/v1/shop/grant?user_nick={client.shop.nick}&access_token=234&open_id=abc")
        assert resp.status_code == 400
        assert resp.json['reason'] == '用户没有绑定店铺，请联系管理员', '主账号没有绑定到某个飞梭账号上，所以无法获取完整 jwt'
        assert request_patch.call_count == 2, \
            'robot_processor.shop.models.Shop.Utils.get_taobao_shop_info_by_access_token 产生了两次 api 请求'
        assert client.shop.get_recent_record().access_token == "234"

        resp = client.get(f"/v1/shop/grant?user_nick={client.assistant.user_nick}&access_token=234&open_id=abc")
        assert resp.status_code == 200
        assert resp.json['token']
        assert client.shop.records.count() == 1
        subuser_record = client.shop.get_recent_subuser_record()
        assert subuser_record and subuser_record.access_token == "234"
        assert db.session.query(TaobaoSubuserGrantRecord).where(
            TaobaoSubuserGrantRecord.sid == client.shop.sid
        ).count() == 1

    def test_post(self, client, db):
        body = {
            "sid": 'random_sid',
            "seller_id": "random_seller_id",
            "nick": "random_nick",
            "platform": 'TAOBAO',
            "access_token": "xxxxxxxxxx",
            "open_id": "random_open_id",
            "org_id": 1,
            "channel_id": '123455'
        }
        shop = Shop.query.filter_by(sid=body["sid"]).first()
        assert shop is None
        resp = client.post("/v1/shop/grant", json=body)
        assert resp.status_code == 200
        assert resp.json["success"]
        created = Shop.query.filter_by(sid=body["sid"]).first()
        assert created is not None
        db.session.delete(created)


def test_get_shops_by_nick_and_open_id(client):
    client.shop.platform = "TAOBAO"
    # auth 失败
    resp = client.get("/v1/transformer-shops?nick=123")
    assert resp.status_code == 401

    # 既没有提供 nick 也没有提供 open_id
    resp = client.get("/v1/transformer-shops", headers={"Authentication-Token": "#"})
    assert resp.status_code == 400

    # nick 错误
    resp = client.get("/v1/transformer-shops?nick=123", headers={"Authentication-Token": "#"})
    assert resp.status_code == 400

    # nick 正确
    resp = client.get(f"/v1/transformer-shops?nick={client.shop.nick}", headers={"Authentication-Token": "#"})
    assert resp.status_code == 200

    # open_id 正确
    resp2 = client.get(f"/v1/transformer-shops?openid={client.shop.open_id}", headers={"Authentication-Token": "#"})
    assert resp2.status_code == 200
    assert resp.json == resp2.json


def test_service_shop(client):
    resp = client.get("/v1/shops/test", headers={"Authentication-Token": "#"})
    assert resp.status_code == 404

    resp = client.get(f"/v1/shops/{client.shop.sid}", headers={"Authentication-Token": "#"})
    assert resp.status_code == 200


def test_shop_detail(client, mock_grant_record, mini_app_template_factory):
    mini_app_template_factory.create(shop_id=client.shop.id, app_id="3000000050810211")
    client.shop.records.append(mock_grant_record)
    db.session.commit()
    resp = client.get(f"/v1/shops/details/{client.shop.sid}", headers={"Authentication-Token": "#"})

    assert resp.status_code == 200
    assert resp.json["shop"]["app"] == "3000000050810211"


def test_shop_erp_succeed(client, mock_erp_info):
    mock_erp_info.shop_id = client.shop.id
    mock_erp_info.erp_type = ErpType.JST
    db.session.add(mock_erp_info)
    db.session.commit()

    resp = client.get("/v1/shop/erp")
    assert resp.status_code == 200
    assert resp.json == {'erp_type': 'JST'}


@mark.order(1)
def test_qianniu_plugin_login(client):
    resp = client.get(f'/v1/qianniu-plugin/login?user_nick={client.assistant.user_nick}&access_token=123&open_id=abc')
    assert resp.status_code == 200
    assert resp.json['success'], resp.json["msg"]
    assert resp.json['data']
    assert resp.json['data']['token']
    assert resp.json['data']['expireTime']


@mark.order(1)
def test_qianniu_plugin_login_no_shop(client):
    resp = client.get('/v1/qianniu-plugin/login?user_nick=abcdjeklfj:ajfklds&access_token=123&open_id=abc')
    assert resp.status_code == 200
    assert not resp.json['success']
    assert not resp.json['data']
    assert resp.json['msg']
