import random

from pytest import fixture
from result import Err
from result import Ok

from robot_processor.enums import ChannelType
from robot_processor.shop.models import App
from robot_processor.shop.models import Shop
from robot_processor.shop.shop_grant_api import doudian_grant
from rpa.doudian import DoudianOpenAPIClient
from rpa.doudian import schemas as doudian_schemas


@fixture
def doudian_app(mocker):
    mocker.patch("robot_processor.shop.shop_grant_api.current_trace_id", return_value="123")
    yield random.choice([App.PIGEON.value, App.DOUDIAN_XYZ.value])


def test_doudian_grant_with_invalid_code(mocker, doudian_app):
    mocker.patch.object(DoudianOpenAPIClient, "token_create", return_value=Err("Token creation failed"))
    result = doudian_grant("invalid_code", doudian_app)
    assert result.is_err()
    assert "授权失败" in result.err()


def test_doudian_grant_with_unregistered_shop(mocker, doudian_app):
    mocker.patch.object(
        DoudianOpenAPIClient,
        "token_create",
        return_value=Ok(
            doudian_schemas.TokenCreateRes(
                shop_id=111111,
                shop_name="unregistered_shop_name",
                access_token="access_token",
                refresh_token="refresh_token",
                expires_in=3600,
            )
        ),
    )
    result = doudian_grant("valid_code", doudian_app)
    if doudian_app == App.DOUDIAN_XYZ:
        assert result.is_ok()
    else:
        assert result.is_err()
        assert "后台未添加店铺" in result.err()


@fixture
def registered_shop(shop_factory):
    yield shop_factory.create(sid=111, platform=ChannelType.DOUDIAN.name)


def test_doudian_grant_success(mocker, doudian_app, registered_shop):
    mocker.patch.object(
        DoudianOpenAPIClient,
        "token_create",
        return_value=Ok(
            doudian_schemas.TokenCreateRes(
                shop_id=registered_shop.sid,
                shop_name=registered_shop.nick,
                access_token="access_token",
                refresh_token="refresh_token",
                expires_in=3600,
            )
        ),
    )

    result = doudian_grant("valid_code", doudian_app)
    assert result.is_ok()
    assert isinstance(result.ok(), Shop)
