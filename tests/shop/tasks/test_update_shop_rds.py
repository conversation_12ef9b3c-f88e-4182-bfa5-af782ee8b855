import arrow
import pytest
from result import Ok
from faker import Faker

from robot_processor.client import taobao_client
from robot_processor.shop.tasks import update_shop_rds, RDS_NAME
from robot_processor.enums import ShopStatus


faker = Faker()


@pytest.fixture(autouse=True)
def mock_requests(requests_mock):
    pass


@pytest.fixture
def mock_unsubcribe(mocker):
    yield mocker.patch('robot_processor.client.taobao.TaobaoClient.jushita_jdp_user_delete',
                       return_value=Ok({}))


@pytest.fixture
def mock_subscribe(mocker):
    yield mocker.patch('robot_processor.client.taobao.TaobaoClient.jushita_jdp_user_add',
                       return_value=Ok({}))


def test_非淘系店铺(shop_factory):
    shop = shop_factory(platform='其他平台')
    update_shop_rds(shop.channel_id)


def test_店铺删除(shop_factory, mock_unsubcribe):
    shop = shop_factory(deleted=True)
    update_shop_rds(shop.channel_id)
    mock_unsubcribe.assert_called_once()


def test_店铺已关闭(shop_factory, mock_unsubcribe):
    shop = shop_factory(status='DISABLE')
    update_shop_rds(shop.channel_id)
    mock_unsubcribe.assert_called_once()


def test_店铺合同失效(shop_factory, contract_info_factory, mock_unsubcribe):
    shop = shop_factory()
    contract_info_factory.create(end_ts=arrow.now().shift(days=-1).int_timestamp, org_id=shop.org_id)
    update_shop_rds(shop.channel_id)
    mock_unsubcribe.assert_called_once()


def test_已订阅(shop_factory, mock_grant_record, contract_info_factory, requests_mock, mock_subscribe):
    shop = shop_factory.create()
    mock_grant_record.shop_id = shop.id
    contract_info_factory.create(end_ts=arrow.now().shift(days=1).int_timestamp, org_id=shop.org_id)
    requests_mock.post(taobao_client.api_url, json={"total_results": 1})
    update_shop_rds(shop.channel_id)
    mock_subscribe.assert_not_called()


def test_subscribe_success(shop_factory, mock_grant_record, contract_info_factory, requests_mock):
    shop = shop_factory.create()
    shop.seller_id = faker.random_int()
    mock_grant_record.shop_id = shop.id
    contract_info_factory.create(end_ts=arrow.now().shift(days=1).int_timestamp, org_id=shop.org_id)
    patch = requests_mock.post(taobao_client.api_url, json={"total_results": 0})
    update_shop_rds(shop.channel_id)
    assert RDS_NAME in patch.last_request.text
    assert "taobao.jushita.jdp.user.add" in patch.last_request.text


def test_unscribe_success(shop_factory, requests_mock):
    shop = shop_factory.create()
    shop.status = ShopStatus.DISABLE
    patch = requests_mock.register_uri("POST", taobao_client.api_url, json={})
    update_shop_rds(shop.channel_id)
    assert "taobao.jushita.jdp.user.delete" in patch.last_request.text
