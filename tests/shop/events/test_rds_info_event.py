from pytest import fixture
from pytest_mock import MockFixture

from robot_processor.ext import db
from robot_processor.enums import ShopStatus


@fixture
def mock_task(mocker: MockFixture):
    yield mocker.patch("robot_processor.shop.tasks.update_shop_rds.send")


def test_忽略非淘系店铺(app_context, shop_factory, mock_task):
    shop = shop_factory.create()
    shop.platform = "其他平台"
    db.session.commit()
    mock_task.reset_mock()

    shop.status = ShopStatus.ENABLE
    mock_task.assert_not_called()


def test_修改status触发事件(app_context, shop_factory, mock_task):
    shop = shop_factory()
    shop.platform = "TAOBAO"
    db.session.commit()
    mock_task.reset_mock()

    shop.status = ShopStatus.ENABLE
    mock_task.assert_not_called()  # 未提交事务，不触发事件
    db.session.commit()
    mock_task.assert_called_once()
