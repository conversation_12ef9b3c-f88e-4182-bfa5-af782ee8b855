from robot_processor.shop.models import Shop, App


def test_case(client):
    nick = "测试 nick"
    open_id = "测试 open id"
    title = "测试 title"
    platform = "TAOBAO"
    sid = "1234"
    access_token = "测试 access token"

    shop = Shop.Queries.insert_shop_grant_record_for_auth_only(
        nick=nick,
        open_id=open_id,
        title=title,
        platform=platform,
        sid=sid,
        app=App.FEISUO,
        access_token=access_token
    )
    assert shop.id
    assert shop.nick == nick
    assert shop.open_id == open_id
    assert shop.title == title
    assert shop.platform == platform
    assert shop.sid == sid
    assert shop.records.count() == 1
    grant_record = shop.records.first()
    assert grant_record.access_token == access_token
