from pytest import fixture

from robot_processor.database_util import cache_query_count
from robot_processor.form.models import Form


@fixture(autouse=True)
def query_count(mocker):
    from sqlalchemy.orm.query import Query

    patch = mocker.patch.object(Query, "count")
    yield patch


def test_not_cached_query_count(cache, query_count):
    from robot_processor.form.models import Form

    cache_query_count(Form.query, use_cache=False)

    # 未使用缓存
    cache.get.assert_not_called()
    # 未更新缓存
    cache.set.assert_not_called()
    # 查询了数据库
    query_count.assert_called_once()


def test_cached_query_count(cache, query_count):

    query_count.return_value = 1
    assert cache_query_count(Form.query) == 1

    # 缓存未命中
    cache.get.assert_called_once()
    # 更新缓存
    cache.set.assert_called_once()
    # 查询了数据库
    query_count.assert_called_once()

    cache.get.reset_mock()
    cache.set.reset_mock()
    query_count.reset_mock()

    # 再次调用，将使用缓存返回相同结果, 且不查询数据库
    assert cache_query_count(Form.query) == 1
    cache.get.assert_called_once()
    cache.set.assert_not_called()
    query_count.assert_not_called()
