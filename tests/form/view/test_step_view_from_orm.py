from typing import List

from pytest import fixture, mark

from robot_processor.assistant.schema import AccountDetailV2, AssigneeGroup, AssistantV2
from robot_processor.ext import db
from robot_processor.form.models import Step, StepAutoRetry, StepFieldOverride
from robot_processor.enums import (
    StepType, SelectType, UserType, AssignStrategy, AssigneeRule
)
from robot_processor.form.models import WidgetCollection, WidgetInfo


class TestBaseFromORM:
    @fixture(autouse=True)
    def setup(self, client, mock_form):
        mock_form.subscribe(client.shop, True)
        yield

    @fixture
    def testbed_step_human(self, client, mock_form, step_factory, widget_collection_factory, widget_info_factory):
        widget_collection: WidgetCollection = widget_collection_factory()
        widget_info: List[WidgetInfo] = widget_info_factory.create_batch(
            10, widget_collection_id=widget_collection.id, before=False)
        for idx, item in enumerate(widget_info):
            item.order = idx
        step: Step = step_factory(
            form_id=mock_form.id,
            step_uuid="step-uuid-ut",
            step_type=StepType.human,
            is_dirty=True,
            widget_collection_id=widget_collection.id,
            assistants_v2={
                "select_type": SelectType.part.value,
                "details": [
                    {"user_type": UserType.ASSISTANT.value, "user_id": 1, "user_nick": "nick1"},
                    {"user_type": UserType.LEYAN.value, "user_id": 2, "user_nick": "nick2"},
                ],
                "online_only": True,
                "assign_strategy": AssignStrategy.CREATOR.value
            },
            assignee_rule=AssigneeRule.ONLINE.value,
            assignee_groups={"group-uuid-1": 1, "group-uuid-2": 0},
            prev_step_ids=["prev"],
            next_step_ids=["next"]
        )
        step.update_raw_step()
        yield step.wraps(client.shop)

    def test_human_step_view_base_from_orm(self, mock_form, testbed_step_human):
        # 人工步骤
        step = testbed_step_human
        base = Step.View.Base.from_orm(step)

        # 检查基础属性正常
        assert base.id == step.id
        assert base.name == step.name
        assert base.description == step.description
        assert base.step_uuid == step.step_uuid == "step-uuid-ut"
        assert base.step_type == step.step_type == StepType.human
        assert base.form_id == step.form_id == mock_form.id
        assert base.prev_step_ids == step.prev_step_ids == ["prev"]
        assert base.next_step_ids == step.next_step_ids == ["next"]
        assert base.assistants_v2.select_type == SelectType.part
        assert base.assistants_v2.details == [
            AccountDetailV2(user_type=UserType.ASSISTANT, user_id=1, user_nick="nick1"),
            AccountDetailV2(user_type=UserType.LEYAN, user_id=2, user_nick="nick2"),
        ]
        assert base.assistants_v2.assignee_groups == [
            AssigneeGroup(group_uuid="group-uuid-1", enable=1),
            AssigneeGroup(group_uuid="group-uuid-2", enable=0),
        ]
        assert base.assistants_v2.online_only is True
        assert base.assistants_v2.assign_strategy == AssignStrategy.CREATOR
        assert base.assignee_rule == AssigneeRule.ONLINE
        assert base.widget_collection_id == int(step.widget_collection_id)

    @fixture
    @mark.usefixtures("testbed_form_shop")
    def testbed_step_exclusive_gateway(self, client, mock_form, step_factory):
        step: Step = step_factory(
            form_id=mock_form.id,
            step_type=StepType.exclusive_gateway,
            is_dirty=True,
            branch=[{"name": "default", "enable": True, "next": "", "type": "DEFAULT", "order": 1}]
        )
        yield step.wraps(client.shop)

    def test_exclusive_gateway_step_view_base_from_orm(self, testbed_step_exclusive_gateway):
        step = testbed_step_exclusive_gateway

        base = Step.View.Base.from_orm(step)
        assert base.branch == [{"name": "default", "enable": True, "next": "", "type": "DEFAULT", "order": 1}]

    @fixture
    @mark.usefixtures("testbed_form_shop")
    def testbed_step_auto(self, client, mock_form, step_factory):
        step: Step = step_factory(
            form_id=mock_form.id,
            step_type=StepType.auto,
            is_dirty=True,
            key_map={"tid": "widget-info-key"},
            data={"rpa_id": 1}
        )
        step.update_raw_step()
        yield step.wraps(client.shop)

    def test_auto_step_view_base_from_orm(self, testbed_step_auto):
        step = testbed_step_auto

        base = Step.View.Base.from_orm(step)
        assert base.data.rpa_id == 1
        assert base.key_map == {"tid": "widget-info-key"}

    def test_form_editor_view_from_orm(self, testbed_step_human):
        step = testbed_step_human

        view = Step.View.FormEditor.from_orm(step)
        assert view.can_retry is True
        assert view.can_skip is False
        assert view.can_auto_skip_when_fail is False
        assert len(view.ui_schema) == 10

    @fixture
    def mock_step_auto_retry_config(self, client, testbed_step_auto):
        auto_retry = StepAutoRetry()
        auto_retry.step_uuid = testbed_step_auto.step_uuid
        auto_retry.can_retry = True
        auto_retry.update_user = "ut"
        auto_retry.retry_interval = 1
        auto_retry.retry_duration = 2
        auto_retry.retry_times = 3
        db.session.add(auto_retry)
        db.session.commit()
        yield auto_retry

    @mark.usefixtures("mock_step_auto_retry_config")
    def test_form_editor_view_auto_retry_config(self, testbed_step_auto):
        step = testbed_step_auto

        assert step.auto_retry_config is not None
        view = Step.View.FormEditor.from_orm(step)
        assert view.auto_retry_config is None, "step.raw_step 还没有更新 auto_retry_config 信息"

        step.update_raw_step()
        view = Step.View.FormEditor.from_orm(step)
        assert view.auto_retry_config is not None, step.raw_step
        assert view.auto_retry_config.can_retry is True
        assert view.auto_retry_config.retry_interval == 1
        assert view.auto_retry_config.retry_duration == 2
        assert view.auto_retry_config.retry_times == 3

    @fixture
    def mock_step_field_override_config(self, testbed_step_human):
        step = testbed_step_human
        assert step.is_wrapper, "fixture 已经进行过 wraps"

        step.set_step_field_override("assistants_v2", {"select_type": SelectType.all.value})
        step.update_raw_step()
        db.session.commit()
        yield

    @mark.usefixtures("mock_step_field_override_config")
    def test_form_editor_view_field_override(self, testbed_step_human):
        step = testbed_step_human

        view = Step.View.FormEditor.from_orm(step)
        # view 中是 override 的数据
        assert view.assistants_v2.select_type == SelectType.all
        assert view.assistants_v2.assignee_groups == AssistantV2.__fields__["assignee_groups"].default_factory()
        assert not view.assistants_v2.details
        assert view.assistants_v2.online_only == AssistantV2.__fields__["online_only"].default
        # step 中还是原始的数据
        assert step._step.assistants_v2["select_type"] == SelectType.part.value

    @mark.usefixtures("mock_step_field_override_config")
    def test_raw_step_view(self, client, testbed_step_human, mock_form):
        step = testbed_step_human

        view = Step.View.RawStep.from_orm(step)
        assert view.override_config == [StepFieldOverride.View.RawStep(
            channel_id=client.shop.channel_id,
            step_uuid=step.step_uuid,
            form_id=mock_form.id,
            sid=client.shop.sid,
            override_method=StepFieldOverride.OverrideMethod.REPLACE,
            override_field='assistants_v2',
            override_value={'select_type': 1})]
