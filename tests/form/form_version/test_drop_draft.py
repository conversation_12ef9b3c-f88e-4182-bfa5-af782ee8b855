"""
测试删除工单模板的草稿

References:
    robot_processor.form.models.Form.drop_draft_version
"""
from pytest import fixture

from robot_processor.db import in_transaction
from robot_processor.enums import StepType
from robot_processor.form.models import Form, Step, FormVersion
from robot_processor.utils import get_nonce


@fixture
def setup(client):
    with in_transaction() as trx:
        form = Form()
        trx.add(form)
    with in_transaction() as trx:
        # version V0.0.1 steps: [begin, step_a_1, step_b_1]
        begin = Step(
            step_uuid="begin",
            prev_step_ids=[],
            next_step_ids=["step a"],
            name="起始步骤",
            is_dirty=False,
        )
        step_a_1 = Step(
            step_uuid="step a",
            prev_step_ids=["begin"],
            next_step_ids=["step b"],
            name="步骤 A",
            is_dirty=False,
        )
        step_b_1 = Step(
            step_uuid="step b",
            prev_step_ids=["step a"],
            next_step_ids=[],
            name="步骤 B",
            is_dirty=False,
        )
        form.steps.extend([begin, step_a_1, step_b_1])
        trx.flush()
        version_1 = FormVersion(
            version_no="V0.0.1",
            step_id=[begin.id, step_a_1.id, step_b_1.id],
            version_descriptor=get_nonce(),
        )
        form.versions.append(version_1)

        # version V0.0.2 steps: [begin, step_a_2, step_c_2]
        # change:
        #   - Remove step b
        #   - Add step c
        step_b_1.deleted = True
        step_a_2 = Step(
            step_uuid="step a",
            prev_step_ids=["begin"],
            next_step_ids=["step c"],
            name="步骤 A",
            is_dirty=False,
        )
        step_c_2 = Step(
            step_uuid="step c",
            prev_step_ids=["step a"],
            next_step_ids=[],
            name="步骤 C",
            is_dirty=False,
        )
        form.steps.extend([step_a_2, step_c_2])
        trx.flush()
        version_2 = FormVersion(
            version_no="V0.0.2",
            step_id=[begin.id, step_a_2.id, step_c_2.id],
            version_descriptor=get_nonce(),
        )
        form.versions.append(version_2)

    yield {
        "form": form,
        "step begin": begin,
        "step a 1": step_a_1,
        "step a 2": step_a_2,
        "step b 1": step_b_1,
        "step c 2": step_c_2,
        "version 1": version_1,
        "version 2": version_2,
    }


def testcase(setup):
    """在草稿箱状态做很多很多操作，然后丢弃这些操作"""
    latest_version = setup["form"].versions.first()
    assert latest_version.version_no == "V0.0.2"
    backend_steps = {step.step_uuid: step for step in setup["form"].backend_steps.all()}
    assert set(backend_steps) == {"begin", "step a", "step c"}

    """
    Change
        - Remove step a
        - Add step d
    begin -> step d -> step c
    """
    # remove step a
    with in_transaction() as trx:
        setup["form"].steps.filter_by(step_uuid="step a").update({"deleted": True})
        begin_draft = setup["step begin"].clone()
        begin_draft.is_dirty = True
        begin_draft.next_step_ids = ["step c"]
        step_c_draft = setup["step c 2"].clone()
        step_c_draft.is_dirty = True
        step_c_draft.prev_step_ids = ["begin"]
        trx.add_all([begin_draft, step_c_draft])
    # add step d
    with in_transaction() as trx:
        step_d = Step(
            step_uuid="step d",
            prev_step_ids=["begin"],
            next_step_ids=["step c"],
            form_id=setup["form"].id,
            name="步骤 D",
            is_dirty=True,
        )
        begin_draft.next_step_ids = ["step d"]
        step_c_draft.prev_step_ids = ["step d"]
        trx.add_all([step_d, begin_draft, step_c_draft])

    backend_steps = {step.step_uuid: step for step in setup["form"].backend_steps.all()}
    assert set(backend_steps) == {"begin", "step c", "step d"}
    assert setup["step a 1"].deleted, "step a 已经被删除"
    assert setup["step a 2"].deleted, "step a 已经被删除"
    assert setup["step b 1"].deleted, "step b 已经被删除"

    setup["form"].drop_draft_version()
    backend_steps = {step.step_uuid: step for step in setup["form"].backend_steps.all()}
    assert set(backend_steps) == {"begin", "step a", "step c"}
    assert not backend_steps["begin"].is_dirty, "丢弃草稿后，begin 步骤不再是脏数据"
    assert backend_steps["begin"].next_step_ids == ["step a"]

    assert not backend_steps["step a"].is_dirty, "丢弃草稿后，step a 步骤不再是脏数据"
    assert backend_steps["step a"].prev_step_ids == ["begin"]
    assert backend_steps["step a"].next_step_ids == ["step c"]

    assert not backend_steps["step c"].is_dirty, "丢弃草稿后，step c 步骤不再是脏数据"
    assert backend_steps["step c"].prev_step_ids == ["step a"]
    assert backend_steps["step c"].next_step_ids == []

    assert setup["step b 1"].deleted, "和 step b 没有关系"
    assert begin_draft.deleted, "草稿箱内容被丢弃"
    assert step_c_draft.deleted, "草稿箱内容被丢弃"
    assert step_d.deleted, "草稿箱内容被丢弃"


def testcase未发布过(client):
    """未发布过的工单模板，只保留其实步骤，其它的都标记删除"""
    # setup
    with in_transaction() as trx:
        form = Form()
        trx.add(form)
    with in_transaction():
        begin = Step(
            step_uuid="begin",
            step_type=StepType.begin,
            prev_step_ids=[],
            next_step_ids=["step a"],
            name="起始步骤",
            is_dirty=True,
        )
        step_a = Step(
            step_uuid="step a",
            prev_step_ids=["begin"],
            next_step_ids=[],
            name="步骤 A",
            is_dirty=True,
        )
        form.steps.extend([begin, step_a])
    backend_steps = {step.step_uuid: step for step in form.backend_steps.all()}
    assert set(backend_steps) == {"begin", "step a"}

    # call
    form.drop_draft_version()

    # validate
    backend_steps = {step.step_uuid: step for step in form.backend_steps.all()}
    assert set(backend_steps) == {"begin"}
    assert backend_steps["begin"].next_step_ids == []
