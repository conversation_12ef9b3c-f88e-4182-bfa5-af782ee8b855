import random

from pytest import fixture

from robot_processor.enums import FormCategory
from robot_processor.ext import db
from robot_processor.form.api.form_editor_api import ListShopFormRequest
from robot_processor.form.models import FormShop


@fixture
def prepare_data(client, form_factory):
    # test case 中需要检查返回的工单模板数量不为 0
    # 某个场景随机不到为小概率事件，但还是尽量避免一下
    hit_category = {"ALIPAY": False, None: False}
    hit_enabled = {True: False, False: False}

    forms = form_factory.create_batch(10)
    for form in forms:
        form.updated_at = random.randint(1, 1000000)  # 模拟更新时间不规则分布
        category = random.choice(["ALIPAY", None])
        hit_category[category] = True
        form.category = category

        enabled = random.choice([True, False])
        hit_enabled[enabled] = True
        form.subscribe(client.shop, enabled=enabled)
    # 循环完成后，检查下是否有某个场景没有命中
    for category, hit in hit_category.items():
        if not hit:
            form.category = category
    for enabled, hit in hit_enabled.items():
        if not hit:
            form.subscribe(client.shop, enabled=enabled)
    db.session.commit()

    # 再准备一些其他店铺的工单模板，这些工单模板预期不会被搜索到
    other_shop = client.other_shop()
    other_forms = form_factory.create_batch(10)
    for form in other_forms:
        db.session.add(FormShop.new(form=form, shop=other_shop))
    db.session.commit()

    yield


def test_list_shop_form(client, prepare_data):
    # 先测试全部工单模板的场景
    request_param = ListShopFormRequest(form_category="ALL")
    response = client.get('/v1/forms', query_string=request_param.dict())
    assert response.json["success"]
    assert response.json["tags"]
    assert len(response.json["data"]) == 10
    # 测试顺序是按照 updated_at, enabled 倒序
    assert response.json["data"] == sorted(response.json["data"], key=lambda x: (x["updated_at"], x["enabled"]),
                                           reverse=True)

    # 测试根据工单名称查找
    form_obj = random.choice(response.json["data"])
    request_param = ListShopFormRequest(form_category="ALL", form_name=form_obj["name"])
    response = client.get('/v1/forms', query_string=request_param.dict())
    assert response.json["success"]
    assert len(response.json["data"]) > 0
    assert all(map(lambda item: item["name"] == form_obj["name"], response.json["data"]))

    # 测试普通工单模板
    request_param = ListShopFormRequest(form_category=None)
    response = client.get('/v1/forms', query_string=request_param.dict())
    assert response.json["success"]
    assert len(response.json["data"])
    assert all(map(lambda item: item["category"] is None, response.json["data"]))

    # 测试批量打款工单模板
    request_param = ListShopFormRequest(form_category="ALIPAY")
    response = client.get('/v1/forms', query_string=request_param.dict())
    assert response.json["success"]
    assert len(response.json["data"])
    assert all(map(lambda item: item["category"] == FormCategory.ALIPAY.name, response.json["data"]))

    # 测试工单启用状态
    request_param = ListShopFormRequest(form_category="ALL", enabled=True)
    response = client.get('/v1/forms', query_string=request_param.dict())
    assert response.json["success"]
    assert len(response.json["data"])
    assert all(map(lambda item: item["enabled"] is True, response.json["data"]))
    request_param = ListShopFormRequest(form_category="ALL", enabled=False)
    response = client.get('/v1/forms', query_string=request_param.dict())
    assert response.json["success"]
    assert len(response.json["data"])
    assert all(map(lambda item: item["enabled"] is False, response.json["data"]))
