from operator import itemgetter
from itertools import chain

from pydantic_factories import ModelFactory
from pytest import fixture


@fixture
def testbed(
    client,
    form_factory,
    step_factory,
    widget_collection_factory,
    widget_info_factory,
):
    """
    form:
        begin: (human)
        gateway: (exclusive gateway)
        branch_1: (auto)
        branch_2: (human)
        end: (auto)
                         /--> branch_1 --\
    begin --> gateway --|                 | --> end
                         --> branch_2 --/
    """
    from typing import NamedTuple
    from robot_processor.ext import db
    from robot_processor.enums import StepType
    from robot_processor.form.models import Step, Form, FormShop
    from robot_processor.form.models import WidgetInfo

    option_value_factory = type(
        "WidgetOptionValueFactory", (ModelFactory,),
        {"__model__": WidgetInfo.Schema.OptionValue}
    )
    form = form_factory()
    form_shop = FormShop.new(form, client.shop)
    db.session.add(form_shop)
    begin_step = step_factory.create(form_id=form.id, step_type=StepType.begin)

    steps = step_factory.create_batch(5, form_id=form.id)  # type: list[Step]
    begin, gateway, branch_1, branch_2, end = map(itemgetter, range(5))

    begin_step.prev_step_ids = []
    begin_step.next_step_ids = [begin(steps).step_uuid]

    begin(steps).step_type = StepType.human
    begin(steps).is_dirty = True
    widget_collection = widget_collection_factory()
    widget_collection.is_dirty = True
    widget_info = widget_info_factory.create_batch(
        10, widget_collection_id=widget_collection.id, before=False
    )
    for index, item in enumerate(widget_info):
        item.order = index
        item.option_value = option_value_factory.build().dict()
    begin(steps).widget_collection_id = widget_collection.id
    begin(steps).prev_step_ids = [begin_step.step_uuid]
    begin(steps).next_step_ids = [gateway(steps).step_uuid]

    gateway(steps).step_type = StepType.exclusive_gateway
    gateway(steps).is_dirty = True
    gateway(steps).prev_step_ids = [begin(steps).step_uuid]
    gateway(steps).next_step_ids = [
        branch_1(steps).step_uuid,
        branch_2(steps).step_uuid,
    ]

    branch_1(steps).step_type = StepType.auto
    branch_1(steps).is_dirty = True
    branch_1(steps).prev_step_ids = [gateway(steps).step_uuid]
    branch_1(steps).next_step_ids = [end(steps).step_uuid]

    branch_2(steps).step_type = StepType.human
    branch_2(steps).is_dirty = True
    branch_2(steps).prev_step_ids = [gateway(steps).step_uuid]
    branch_2(steps).next_step_ids = [end(steps).step_uuid]
    widget_collection_2 = widget_collection_factory()
    widget_collection_2.is_dirty = True
    widget_info_2 = widget_info_factory.create_batch(
        2, widget_collection_id=widget_collection_2.id, before=False
    )
    refer_begin_step_widget_info = [widget_info[0].clone(), widget_info[1].clone()]
    for item in refer_begin_step_widget_info:
        item.widget_collection_id = widget_collection_2.id
        item.before = True
    for index, item in enumerate(chain(widget_info_2, refer_begin_step_widget_info)):
        item.order = index
        item.option_value = option_value_factory.build().dict()
    branch_2(steps).widget_collection_id = widget_collection_2.id

    end(steps).step_type = StepType.auto
    end(steps).is_dirty = True
    end(steps).prev_step_ids = [branch_1(steps).step_uuid, branch_2(steps).step_uuid]
    db.session.commit()

    yield NamedTuple(
        "Testbed",
        form_shop=FormShop,
        form=Form,
        begin_step=Step,
        begin=Step,
        gateway=Step,
        branch_1=Step,
        branch_2=Step,
        end=Step,
    )(
        form_shop=form_shop,
        form=form,
        begin_step=begin_step,
        begin=begin(steps),
        gateway=gateway(steps),
        branch_1=branch_1(steps),
        branch_2=branch_2(steps),
        end=end(steps),
    )
