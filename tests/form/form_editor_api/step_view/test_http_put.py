from robot_processor.assistant.schema import AssistantV2
from robot_processor.enums import StepType
from robot_processor.form.api.form_editor_api import FormStepEditRequest


def test_put(client, mock_form, testbed_step_human_1, mock_action_client):
    request_body = dict(
        name="修改名字",
        description="ut",
        step_type=StepType.human,
        can_retry=True,
        can_skip=True,
        can_auto_skip_when_fail=True,
        auto_retry_config=FormStepEditRequest.StepAutoRetryConfig(
            retry_duration=1, retry_interval=1000, retry_times=3, can_retry=True
        ).dict(),
        buyer_edit=True,
        buyer_reply=[{"type": "text", "content": "ut"}],
        widget_collection_id=1,
        assistants_v2=AssistantV2().dict(),
    )
    response = client.put(
        "/v1/forms/{}/steps/{}".format(mock_form.id, testbed_step_human_1.id),
        json=request_body,
    )
    assert response.status_code == 200
    assert response.json["success"]
    assert testbed_step_human_1.name == "修改名字"
    mock_action_client.create_action_log_by_kafka.assert_called_once()
