from pydantic_factories import ModelFactory
from pytest import fixture, mark


@fixture(autouse=True)
def setup_form_shop(client, mock_form):
    mock_form.subscribe(client.shop, True)
    yield


@fixture
@mark.usefixtures("setup_form_shop")
def testbed_step_human_1(mock_form, step_factory, widget_collection_factory, widget_info_factory):
    from robot_processor.enums import (
        StepType, SelectType, UserType, AssignStrategy, AssigneeRule
    )
    from robot_processor.form.models import Step
    from robot_processor.form.models import WidgetCollection, WidgetInfo

    widget_collection: WidgetCollection = widget_collection_factory()
    option_value_factory = type(
        "WidgetOptionValueFactory", (ModelFactory,),
        {"__model__": WidgetInfo.Schema.OptionValue}
    )
    widget_info = widget_info_factory.create_batch(
        10, widget_collection_id=widget_collection.id, before=False)
    for idx, item in enumerate(widget_info):
        item.order = idx
        item.option_value = option_value_factory.build().dict()
    step: Step = step_factory(
        form_id=mock_form.id,
        step_type=StepType.human,
        is_dirty=True,
        widget_collection_id=widget_collection.id,
        assistants_v2={
            "select_type": SelectType.part.value,
            "details": [
                {"user_type": UserType.ASSISTANT.value, "user_id": 1, "user_nick": "nick1"},
                {"user_type": UserType.LEYAN.value, "user_id": 2, "user_nick": "nick2"},
            ],
            "online_only": True,
            "assign_strategy": AssignStrategy.CREATOR.value
        },
        assignee_rule=AssigneeRule.ONLINE.value,
        assignee_groups={"group-uuid-1": 1, "group-uuid-2": 0},
    )
    yield step


@fixture
@mark.usefixtures("setup_form_shop")
def testbed_step_exclusive_gateway(mock_form, step_factory):
    from robot_processor.enums import StepType
    from robot_processor.form.models import Step

    step: Step = step_factory(
        form_id=mock_form.id,
        step_type=StepType.exclusive_gateway,
        is_dirty=True,
        branch=[{"name": "default", "enable": True, "next": "", "type": "DEFAULT", "order": 1}]
    )
    yield step


@fixture
@mark.usefixtures("setup_form_shop")
def testbed_step_auto(mock_form, step_factory):
    from robot_processor.enums import StepType
    from robot_processor.form.models import Step

    step: Step = step_factory(
        form_id=mock_form.id,
        step_type=StepType.auto,
        is_dirty=True,
        key_map={"tid": "widget-info-key"},
        data={"rpa_id": 1}
    )
    yield step
