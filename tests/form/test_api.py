from dataclasses import dataclass
from typing import List

import pytest
from pytest import fixture

from robot_processor.enums import (
    <PERSON><PERSON>tat<PERSON>,
    AssigneeRule,
    VisibilityType,
    ProductCode,
    ChannelType,
    ErpType,
    JobType
)
from robot_processor.enums import StepType
from robot_processor.ext import db
from robot_processor.form.models import Form, Step


@dataclass
class FormFixture:
    forms: List[Form]
    steps: List[List[Step]]


@fixture
def string_widget(widget):
    return widget('单行输入')


@fixture
def tid_widget(widget):
    return widget('订单/子订单')


@fixture
def mock_form_with_step(client, form_factory, step_factory):
    _form_1, _form_2 = form_factory.create_batch(2)
    _form_1.subscribe(client.shop, True)
    _form_2.subscribe(client.shop, True)
    form_1_steps = step_factory.create_batch(3, form_id=_form_1.id, step_type=StepType.human, deleted=False)
    form_2_steps = step_factory.create_batch(3, form_id=_form_2.id, step_type=StepType.human, deleted=False)

    _step_1, _step_2, _step_3 = form_1_steps
    _step_1.step_type = _step_1.step_type.human
    _step_1.next_step_ids = [_step_2.step_uuid]
    _step_2.prev_step_ids = [_step_1.step_uuid]
    _step_2.next_step_ids = [_step_3.step_uuid]
    _step_3.prev_step_ids = [_step_2.step_uuid]

    _step_1, _step_2, _step_3 = form_2_steps
    _step_1.step_type = _step_1.step_type.human
    _step_1.next_step_ids = [_step_2.step_uuid]
    _step_2.prev_step_ids = [_step_1.step_uuid]
    _step_2.next_step_ids = [_step_3.step_uuid]
    _step_3.prev_step_ids = [_step_2.step_uuid]

    yield FormFixture(
        forms=[_form_1, _form_2],
        steps=[form_1_steps, form_2_steps]
    )


class TestForm:
    """表单CURD接口测试"""

    @fixture(autouse=True)
    def setup(self, mocker, mock_action_client):
        mocker.patch("dramatiq.pipeline.run")
        mocker.patch('robot_processor.ext.form_producer')
        mocker.patch('robot_processor.ext.robot_form_change_producer')

    def test_complex(self, mocker, client, widget_collection_factory, rpa):
        """ 对步骤，工单的完整性测试 """
        mock_task = rpa(task="JOB_FINISH", org_id=client.org_id)
        # 创建工单
        form_body = {
            "name": "form1",
            "enabled": True,
            "sync_shop_dict": {client.sid: True}
        }
        resp = client.post("/v1/forms", json=form_body)
        form_id = resp.json["data"]["id"]
        # 创建步骤1，人工步骤
        step_1_body = {
            "name": "步骤1",
            "step_type": 1,
            "can_retry": True,
            "assistants_v2": {"select_type": 1},
            "widget_collection_id": widget_collection_factory().id,
            "prev_step_ids": [Form.query.get(form_id).steps.all()[0].step_uuid]
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_1_body)
        assert resp.status_code == 200
        step1 = resp.json["data"]

        # 创建步骤2，自动化任务
        step_2_body = {
            "name": "步骤2",
            "step_type": 2,
            "can_retry": True,
            "prev_step_ids": [step1["step_uuid"]],
            "task_id": mock_task.id,
            "key_map": {"job_status": True}
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_2_body)
        assert resp.status_code == 200
        step2 = resp.json["data"]

        # 创建步骤3，人工任务
        step_3_body = {
            "name": "步骤3",
            "step_type": 1,
            "can_retry": True,
            "assistants_v2": {"select_type": 1},
            "prev_step_ids": [step2["step_uuid"]],
            "widget_collection_id": widget_collection_factory().id,
            "assignee_rule": AssigneeRule.RANDOM.value
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_3_body)
        assert resp.status_code == 200
        step3 = resp.json["data"]

        # 发布工单
        resp = client.put(f"/v1/forms/{form_id}", json=form_body)
        assert resp.status_code == 200

        # 测试分支步骤
        # 先创建组件
        create_wc_body = {
            "form_id": form_id,
            "widgets": [
                {
                    "id": 14,
                    "key": "wk1",
                    "before": False,
                    "option_value": {}
                }
            ]
        }
        resp = client.post("/v1/form-editor/widgets", json=create_wc_body)
        assert resp.status_code == 200
        step_4_body = {
            "name": "步骤4",
            "can_retry": True,
            "step_type": 3,
            "branch": [
                {
                    "name": "分支1",
                    "enable": True,
                    "type": "NORMAL",
                    "order": 1,
                    "condition_group": {
                        "relation": "AND",
                        "condition_list": [
                            {
                                "type": "single",
                                "data": {
                                    "ref": "wk1",
                                    "ref_type": "product",
                                    "value_type": "STRING",
                                    "operator": "IN",
                                    "value": ["spu1", "spu2"]
                                }
                            }
                        ]
                    },
                    "next": step3["step_uuid"]
                },
                {
                    "name": "默认分支",
                    "type": "DEFAULT",
                    "order": 2,
                    "enable": True,
                    "next": step3["step_uuid"]
                }
            ]
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_4_body)
        assert resp.status_code == 200, resp.json
        step4 = resp.json["data"]

        # 创建步骤 5：跳转步骤
        step_5_body = {
            'name': '跳转步骤4',
            'description': '',
            'jump': {
                'target': step4['step_uuid'],
                'limit_enable': True,
                'interval': 600,
                'auto_retry': True,
                'limit_range': 86400,
                'limit_max_times': 1
            },
            'step_type': 7,
            'can_retry': True,
            'task_id': None,
            'prev_step_ids': [step4['step_uuid']],
            'next_step_ids': []
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_5_body)
        assert resp.status_code == 200, resp.json
        step5 = resp.json["data"]

        step = Step.query.filter(Step.id == step5["id"]).first()
        step_jump_config = step.get_auto_jump_config()
        assert step_jump_config.limit_enable
        assert step_jump_config.target == step4['step_uuid']

        step: Step | None = Step.query.filter(Step.id == step5["id"]).first()
        assert step is not None
        assert step.raw_step.get("jump") is not None

        # 1->2->4->3->5 重新组织后保存工单
        step_1_body["next_step_ids"] = [step2["step_uuid"]]
        step_2_body["next_step_ids"] = [step4["step_uuid"]]
        step_2_body["prev_step_ids"] = [step1["step_uuid"]]
        step_4_body["prev_step_ids"] = [step2["step_uuid"]]
        step_4_body["next_step_ids"] = [step3["step_uuid"]]
        step_3_body["prev_step_ids"] = [step4["step_uuid"]]
        step_3_body["next_step_ids"] = [step5["step_uuid"]]
        step_5_body["prev_step_ids"] = [step3["step_uuid"]]
        step_5_body["next_step_ids"] = []
        step_5_body["jump"] = {
            'target': step2["step_uuid"],
            'limit_enable': True,
            'interval': 600,
            'auto_retry': True,
            'limit_range': 86400,
            'limit_max_times': 1
        }
        client.put(f"/v1/forms/{form_id}/steps/{step1['id']}",
                   json=step_1_body)
        client.put(f"/v1/forms/{form_id}/steps/{step2['id']}",
                   json=step_2_body)
        client.put(f"/v1/forms/{form_id}/steps/{step3['id']}",
                   json=step_3_body)
        client.put(f"/v1/forms/{form_id}/steps/{step4['id']}",
                   json=step_4_body)
        client.put(f"/v1/forms/{form_id}/steps/{step5['id']}",
                   json=step_5_body)
        resp = client.put(f"/v1/forms/{form_id}", json=form_body)

        step = Step.query.filter(Step.id == step5["id"]).first()
        step_jump_config = step.get_auto_jump_config()
        assert step_jump_config.limit_enable
        assert step_jump_config.target == step2['step_uuid']
        print(resp.json)

    def test_rpa_param(self, mocker, client, widget_info_factory, mock_widget_collection, string_widget, rpa):
        """ 对步骤，工单的完整性测试 """
        mock_task = rpa(task=JobType.CHANGE_SKU, org_id=client.org_id)
        widget_info_factory.create(
            widget_id=string_widget.id,
            widget_collection_id=mock_widget_collection.id,
            key="sku-widget-key"
        )
        # 创建工单
        resp = client.post("/v1/forms", json={"name": "form1", "enabled": True})
        form_id = resp.json["data"]["id"]
        form = Form.query.get(form_id)
        assert len(form.steps.all()) == 1
        assert form.steps.all()[0].step_type == StepType.begin
        # 创建步骤1，人工步骤
        # 此时已经有系统创建的第一步了
        step_1_body = {
            "name": "步骤1",
            "step_type": 1,
            "can_retry": True,
            "assistants_v2": {"select_type": 1},
            "widget_collection_id": mock_widget_collection.id,
            "prev_step_ids": [form.steps.all()[0].step_uuid]
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_1_body)
        assert resp.status_code == 200
        step1 = resp.json["data"]

        # 创建步骤2，自动化任务
        step_2_body = {
            "name": "步骤2",
            "step_type": 2,
            "can_retry": True,
            "prev_step_ids": [step1["step_uuid"]],
            "task_id": mock_task.id,
            "key_map": {
                "new_outer_id": "not exist widget",
                "tid": "not exist widget key"
            }
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_2_body)
        assert resp.status_code == 200
        step2 = resp.json["data"]
        step_1_body["next_step_ids"] = [step2["step_uuid"]]

        client.put(f"/v1/forms/{form_id}/steps/{step1['id']}", json=step_1_body)
        # 发布工单
        resp = client.put(f"/v1/forms/{form_id}", json={"name": "form1", "enabled": True})
        assert resp.status_code == 400
        errors = resp.json["data"]
        assert {'参数[新SKU ID(输入)]引用的组件不存在', '参数[订单号(输入)]引用的组件不存在'} == set(
            e['error_display'] for e in errors)

    def test_rpa_param_1(self, client, widget_info_factory, mock_widget_collection, string_widget, tid_widget, rpa):
        widget_info_factory.create(
            widget_id=string_widget.id,
            widget_collection_id=mock_widget_collection.id,
            key="sku-widget-key",
        )
        widget_info_factory.create(
            widget_id=tid_widget.id,
            widget_collection_id=mock_widget_collection.id,
            key="tid-widget-key"
        )
        # 创建工单
        resp = client.post("/v1/forms", json={"name": "form1", "enabled": True})
        form_id = resp.json["data"]["id"]
        # 创建步骤1，人工步骤
        step_1_body = {
            "name": "步骤1",
            "widget_collection_id": mock_widget_collection.id,
            "step_type": 1,
            "can_retry": True,
            "assistants_v2": {"select_type": 1},
            "prev_step_ids": [Form.query.get(form_id).steps.all()[0].step_uuid]
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_1_body)
        assert resp.status_code == 200
        step1 = resp.json["data"]

        change_sku = rpa(task=JobType.CHANGE_SKU, org_id=client.org_id)
        # 创建步骤2，自动化任务
        step_2_body = {
            "name": "步骤2",
            "step_type": 2,
            "can_retry": True,
            "prev_step_ids": [step1["step_uuid"]],
            "task_id": change_sku.id,
            "key_map": {
                "new_outer_id": "sku-widget-key",
                "tid": "tid-widget-key"
            }
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_2_body)
        assert resp.status_code == 200
        step2 = resp.json["data"]
        step_1_body["next_step_ids"] = [step2["step_uuid"]]

        client.put(f"/v1/forms/{form_id}/steps/{step1['id']}", json=step_1_body)
        # 发布工单
        resp = client.put(f"/v1/forms/{form_id}", json={"name": "form1", "enabled": True})
        assert resp.status_code == 200, resp.json

    def test_rpa_param_2(self, client, widget_info_factory, mock_widget_collection, string_widget, rpa):
        """测试富文本中引用的组件不存在"""
        widget_info_factory.create(
            key='remark-widget-key',
            widget_id=string_widget.id,
            widget_collection_id=mock_widget_collection.id)
        # 创建工单
        resp = client.post("/v1/forms", json={"name": "form1", "enabled": True})
        form_id = resp.json["data"]["id"]
        # 创建步骤1，人工步骤
        step_1_body = {
            "name": "步骤1",
            "step_type": 1,
            "can_retry": True,
            "assistants_v2": {"select_type": 1},
            "widget_collection_id": mock_widget_collection.id,
            "prev_step_ids": [Form.query.get(form_id).steps.all()[0].step_uuid]
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_1_body)
        assert resp.status_code == 200
        step1 = resp.json["data"]

        # 创建步骤2，自动化任务
        mock_task = rpa(task=JobType.YTO_DING, org_id=client.org_id)
        step_2_body = {
            "name": "步骤2",
            "step_type": 2,
            "can_retry": True,
            "prev_step_ids": [step1["step_uuid"]],
            "task_id": mock_task.id,
            "key_map": {
                "yto_id": "fake_yto_id",
                "send_chatroom": "fake_chatroom",
                "sender": "RANDOM_ONLINE",
                "content": [
                    {"key": "non-exist-widget-key",
                     "type": "concept",
                     "label": "concept备注"}
                ]
            }
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_2_body)
        assert resp.status_code == 200
        step2 = resp.json["data"]
        step_1_body["next_step_ids"] = [step2["step_uuid"]]

        client.put(f"/v1/forms/{form_id}/steps/{step1['id']}", json=step_1_body)
        # 发布工单
        resp = client.put(f"/v1/forms/{form_id}", json={
            "name": "form1",
            "enabled": True
        })
        assert resp.status_code == 400
        """TODO(<EMAIL>): label is better than name"""
        error = resp.json["data"][0]
        assert "concept备注" in error["error_display"]
        assert "不存在" in error["error_display"]

        step_2_body['key_map']['content'][0]['key'] = 'remark-widget-key'
        resp = client.put(f"/v1/forms/{form_id}/steps/{step2['id']}", json=step_2_body)
        assert resp.status_code == 200
        # 发布工单
        resp = client.put(f"/v1/forms/{form_id}", json={"name": "form1", "enabled": True})
        assert resp.status_code == 200, resp.json

    def test_assignee_rule_legality(self, client, widget_collection_factory, rpa):
        """ 对步骤，工单的完整性测试 """
        # 选择 JOB_FINISH 是因为它的参数要求最简单
        mock_task = rpa(task=JobType.JOB_FINISH, org_id=client.org_id)
        # 创建工单
        form_body = {
            "name": "form1",
            "enabled": True
        }
        resp = client.post("/v1/forms", json=form_body)
        form_id = resp.json["data"]["id"]

        # 创建步骤1，人工步骤
        step_1_body = {
            "name": "步骤1",
            "step_type": StepType.human.value,
            "can_retry": True,
            "assistants_v2": {"select_type": 1},
            "widget_collection_id": widget_collection_factory().id,
            "prev_step_ids": [Form.query.get(form_id).steps.all()[0].step_uuid],
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_1_body)
        assert resp.status_code == 200
        step1 = resp.json["data"]

        # 创建步骤2，自动化任务
        step_2_body = {
            "name": "步骤2",
            "step_type": StepType.auto.value,
            "can_retry": True,
            "prev_step_ids": [step1["step_uuid"]],
            "task_id": mock_task.id,
            "key_map": {"job_status": True}
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_2_body)
        assert resp.status_code == 200
        step2 = resp.json["data"]

        # 创建步骤3，人工任务，上一步是非人工步骤，允许设置分派规则
        step_3_body = {
            "name": "步骤3",
            "step_type": 1,
            "can_retry": True,
            "assistants_v2": {"select_type": 1},
            "prev_step_ids": [step2["step_uuid"]],
            "assignee_rule": AssigneeRule.MANUAL.value,
            "widget_collection_id": widget_collection_factory().id
        }
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_3_body)
        assert resp.status_code == 400
        assert "上一步有非人工步骤，不允许选择手动分派逻辑" in resp.json["msg"]

        # 创建步骤3，人工任务
        step_3_body["assignee_rule"] = AssigneeRule.RANDOM.value
        resp = client.post(f"/v1/forms/{form_id}/steps", json=step_3_body)
        assert resp.status_code == 200
        step3 = resp.json["data"]

        # 修改step1,step2
        step_1_body["next_step_ids"] = [step2["step_uuid"]]
        step_2_body["next_step_ids"] = [step3["step_uuid"]]
        resp = client.put(f"/v1/forms/{form_id}/steps/{step1['id']}", json=step_1_body)
        assert resp.status_code == 200

        resp = client.put(f"/v1/forms/{form_id}/steps/{step2['id']}", json=step_2_body)
        assert resp.status_code == 200
        # 重新发布工单
        resp = client.put(f"/v1/forms/{form_id}", json=form_body)
        assert resp.status_code == 200


class TestStep:

    def test_put_step_line(self, client, mock_form, mock_step):
        mock_form.subscribe(client.shop, True)
        mock_form.steps = [mock_step]
        db.session.commit()

        data = {
            "name": "123",
            "step_type": StepType.human.value,
            "can_retry": True,
            "edit_type": 2
        }

        resp = client.put(
            f"/v1/forms/{mock_form.id}/steps/{mock_step.id}",
            json=data
        )
        assert resp.status_code == 200

    def test_put_step_failed(self, client, mock_form, mock_step):
        mock_form.subscribe(client.shop, True)
        mock_form.steps = [mock_step]
        db.session.commit()

        data = {
            "name": "123",
            "step_type": StepType.human.value,
            "can_retry": False,
            "assistants_v2": {
                "select_type": 2,
                "details": [
                    {
                        "user_id": 123,
                        "user_type": 3,
                        "user_nick": "123",
                        "phone": "18817338404"
                    }
                ]
            },
            "buyer_edit": True
        }
        # 缺少买家回复话术
        resp = client.put(
            f"/v1/forms/{mock_form.id}/steps/{mock_step.id}",
            json=data
        )
        assert resp.status_code == 400
        data["buyer_reply"] = [{"type": "text", "text": "123"}]
        data["status"] = JobStatus.PENDING.value
        data["widget_collection_id"] = 1
        resp = client.put(
            f"/v1/forms/{mock_form.id}/steps/{mock_step.id}",
            json=data
        )
        assert resp.status_code == 200

    def test_error_check_widget_mapping(self, client, mock_form, mock_widget_collection, string_widget, mock_step, rpa):
        mock_form.subscribe(client.shop, True)
        mock_task = rpa(task=JobType.ALIPAY, org_id=client.org_id)
        mock_step.widget_collection_id = mock_widget_collection.id
        mock_step.form_id = mock_form.id
        mock_step.is_dirty = False
        ui_schema = [
            {
                "key": "85a39438-d97d-45c7-84b7-ccccc91473b9",
                "widget_id": 2,
                "option_value": {
                    "label": "单行输入"
                }, "order": 0
            },
            {
                "key": "cca3ac80-7905-48ad-8f70-b05d78cc48d3",
                "widget_id": string_widget.id,
                "order": 1
            }
        ]
        widget_info = mock_widget_collection.new(ui_schema)
        db.session.add(widget_info)
        for info in widget_info.widget_info:
            info.widget_collection_id = mock_widget_collection.id
        db.session.commit()
        data = {
            "name": "test",
            "key_map": {
                "tid": "cca3ac80-7905-48ad-8f70-b05d78cc48d3",
                "url": "85a39438-d97d-45c7-84b7-ccccc91473b9"
            },
            "rpa_id": mock_task.id,
            "buyer_edit": True,
        }
        resp = client.post(f"/v1/forms/{mock_form.id}/steps", json=data)
        assert resp.status_code == 400

    def test_pass_check_widget_mapping(self, client, mock_form, mock_widget_collection, string_widget, mock_step, rpa):
        mock_form.subscribe(client.shop, True)
        mock_task = rpa(task=JobType.ALIPAY, org_id=client.org_id)
        mock_step.widget_collection_id = mock_widget_collection.id
        mock_step.form_id = mock_form.id
        mock_step.is_dirty = False
        mock_step.deleted = False
        mock_step.name = "hh"
        mock_widget_collection.ui_schema = [
            {
                "key": "85a39438-d97d-45c7-84b7-ccccc91473b9",
                "widget_id": 2,
                "option_value": {
                    "label": "单行输入"
                }
            },
            {
                "key": "cca3ac80-7905-48ad-8f70-b05d78cc48d3",
                "widget_id": string_widget.id,
            }
        ]
        db.session.commit()
        data = {
            "name": "hh",
            "key_map": {
                "tid": "cca3ac80-7905-48ad-8f70-b05d78cc48d3",
                "url": "85a39438-d97d-45c7-84b7-ccccc91473b9"
            },
            "rpa_id": mock_task.id
        }
        resp = client.post(f"/v1/forms/{mock_form.id}/steps", json=data)
        assert resp.status_code == 400

    def test_old_post_step(self, client, mock_form):
        mock_form.subscribe(client.shop, True)
        db.session.commit()

        data = {
            "name": "开始步骤",
            "step_type": 1,
            "buyer_edit": True,
        }

        resp = client.post(
            f"/v1/forms/{mock_form.id}/steps",
            json=data
        )
        assert resp.status_code == 400
        ret = resp.json
        assert not ret['success']

        data.pop("buyer_edit")
        resp = client.post(
            f"/v1/forms/{mock_form.id}/steps",
            json=data
        )
        ret = resp.json
        assert resp.status_code == 200, resp.json
        assert ret['success']
        assert ret['data']['name'] == '开始步骤'

    def test_post_steps(self, client, mock_form):
        mock_form.subscribe(client.shop, True)
        db.session.commit()

        data = {
            'steps': [
                {
                    "name": "开始步骤",
                    "assistants_v2": {
                        "select_type": 1
                    },
                    "step_type": 1,
                    "widget_collection_id": 12345,
                },
                {
                    "name": "步骤二",
                    "assistants_v2": {
                        "select_type": 1
                    },
                    "step_type": 1,
                    "buyer_edit": True
                }
            ]
        }

        resp = client.post(
            f"/v1/forms/{mock_form.id}/steps",
            json=data
        )
        assert resp.status_code == 400
        ret = resp.json
        assert not ret['success']

        data['steps'][1].pop('buyer_edit')

        resp = client.post(
            f"/v1/forms/{mock_form.id}/steps",
            json=data
        )
        ret = resp.json
        assert resp.status_code == 200
        assert ret['success']
        steps_name = [step['name'] for step in ret['data']]
        assert steps_name == ['开始步骤', '步骤二']

    def test_put_steps(self, client, mock_form, mock_step):
        mock_form.subscribe(client.shop, True)
        mock_form.steps = [mock_step]
        db.session.commit()
        data = {
            'steps': [
                {
                    "id": mock_step.id,
                    "name": "123",
                    "step_type": StepType.human.value,
                    "assistants_v2": {
                        "select_type": 1
                    },
                    "widget_collection_id": 12345,
                }
            ]
        }

        resp = client.put(
            f"/v1/forms/{mock_form.id}/steps",
            json=data
        )
        ret = resp.json
        assert resp.status_code == 200, ret
        assert ret['success']
        steps_name = [step['name'] for step in ret['data']]
        assert steps_name == ['123']

    def test_delete_steps(self, client, mock_form, mock_step):
        mock_form.subscribe(client.shop, True)
        mock_form.steps = [mock_step]
        db.session.commit()
        data = {
            "step_ids": [mock_step.id]
        }
        resp = client.delete(
            f"/v1/forms/{mock_form.id}/steps",
            json=data
        )
        assert resp.json['succeed']
        resp = client.get(
            f"/v1/forms/{mock_form.id}/steps/{mock_step.id}",
            json=data
        )
        assert resp.json['succeed']
        assert resp.json['data']['deleted']

    def test_delete_step_line(self, client, mock_form_with_step, mock_action_client):
        mock_form = mock_form_with_step.forms[0]
        mock_form.steps.update({'is_dirty': False, 'deleted': False})
        db.session.commit()
        _step_1, _step_2, _step_3 = mock_form.steps.all()
        resp = client.delete(f"/v1/forms/{mock_form.id}/steps/{_step_2.id}")
        assert resp.status_code == 200
        assert _step_2.deleted is True
        _new_step1 = mock_form.steps.filter_by(step_uuid=_step_1.step_uuid).order_by(Step.id.desc()).first()
        assert _new_step1.id > _step_1.id
        assert _new_step1.is_dirty is True
        _new_step3 = mock_form.steps.filter_by(step_uuid=_step_3.step_uuid).order_by(Step.id.desc()).first()
        assert _new_step3.id > _step_3.id
        assert _new_step3.is_dirty is True
        mock_action_client.create_action_log_by_kafka.assert_called_once()


class TestTask:

    def test_get_tasks(self, client, rpa):
        subscribed_rpa = rpa(task=JobType.JOB_FINISH, org_id=client.org_id)
        unsubscribed_rpa = rpa(task=JobType.ALIPAY)

        resp = client.get("/v1/tasks")
        assert resp.status_code == 200
        tasks = resp.json["data"]
        assert len(tasks) >= 2
        subscribed_task_dict = next(filter(lambda item: item["id"] == subscribed_rpa.id, tasks))
        assert subscribed_task_dict["subscribe_info"]["has_subscribed"] is True
        unsubscribed_task_dict = next(filter(lambda item: item["id"] == unsubscribed_rpa.id, tasks))
        assert unsubscribed_task_dict["subscribe_info"]["has_subscribed"] is False


def test_get_group_forms(client, mocker, mock_form, mock_step, step_factory, form_factory):
    mock_form.subscribe(client.shop, True)
    mock_step.form_id = mock_form.id
    mock_step.assignee_groups = {"uuid": 1}
    step_factory.create(form_id=mock_form.id, assignee_groups={"uuid": 1})
    f = form_factory.create()
    f.subscribe(client.shop, True)
    step_factory.create(form=f, assignee_groups={"uuid2": 1})
    db.session.commit()
    resp = client.get("/v1/groups/uuid1/forms")
    assert resp.status_code == 200
    assert not resp.json["count"]

    resp = client.get("/v1/groups/uuid/forms")
    assert resp.status_code == 200
    assert resp.json["count"] == 1


def test_form_templates(client_unauthorized, mock_form_template, mock_erp_info):
    client, _, shop = client_unauthorized.login()
    shop.contract.product_code = ProductCode.FS_001
    mock_form_template.visibility_level = VisibilityType.ORG
    mock_form_template.code = 1
    db.session.commit()

    resp = client.get("/v1/form-templates")

    assert resp.status_code == 200
    assert len(resp.json["templates"]) == 1

    mock_form_template.visibility_level = VisibilityType.PLATFORM
    mock_form_template.visibility_type = [ChannelType.TAOBAO.name, ChannelType.TMALL.name]
    shop.platform = ChannelType.TAOBAO.name
    db.session.commit()
    resp = client.get("/v1/form-templates")

    assert resp.status_code == 200
    assert len(resp.json["templates"]) == 1

    mock_form_template.visibility_level = VisibilityType.PLATFORM
    mock_form_template.visibility_type = [ChannelType.TAOBAO.name, ChannelType.TMALL.name]
    shop.platform = ChannelType.DOUDIAN.name
    db.session.commit()
    resp = client.get("/v1/form-templates")

    assert resp.status_code == 200
    assert len(resp.json["templates"]) == 0

    mock_form_template.visibility_level = VisibilityType.ERP
    mock_form_template.visibility_type = [ErpType.KUAIMAI.name]
    shop.platform = ChannelType.DOUDIAN.name
    mock_erp_info.erp_type = ErpType.KUAIMAI
    mock_erp_info.shop_id = shop.id
    db.session.commit()
    resp = client.get("/v1/form-templates")

    assert resp.status_code == 200
    assert len(resp.json["templates"]) == 1


@pytest.fixture
def mock_robot_form_change_producer(mocker):
    p = mocker.patch("robot_processor.ext.robot_form_change_producer")
    yield p


def test_form_publish_from_template(client, mock_form_template, mock_erp_info, mock_robot_form_change_producer):
    data = dict(
        form_template_id=mock_form_template.id,
        name="test",
        enabled=True,
    )
    resp = client.post("/v1/form-templates", json=data)
    assert "form_id" in resp.json
    mock_robot_form_change_producer.assert_called_once()
    assert mock_robot_form_change_producer.call_args.args[0]["sid"] == client.sid
