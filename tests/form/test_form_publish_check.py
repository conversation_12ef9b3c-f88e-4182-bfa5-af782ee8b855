from robot_processor.business_order.condition.condition import (
    BranchDict,
    ConditionGroupDict,
    ConditionDict,
    ConditionDataDict,
)
from robot_processor.enums import StepType, AssigneeRule, DataType
from robot_processor.form.form_publish_check import FormPublish<PERSON>heck, FormPublishCheckWidgetInfo
from robot_processor.form.models import Form, Step, WidgetRef


def test_check_begin_step(client, step_factory, mock_form: Form):
    step1, step2 = step_factory.build_batch(
        2, form_id=mock_form.id, is_dirty=False
    )
    step1.name = 'step'
    step1.prev_step_ids = ['111']
    step2.name = 'step1'
    step2.prev_step_ids = ['222']
    form_publish_check = FormPublishCheck(step=[step1, step2], widget_info=[])
    publish_check_descriptions = form_publish_check.FlowCheck.check_begin(form_publish_check)
    assert 1 == len(publish_check_descriptions)
    assert "缺少开始节点" == publish_check_descriptions[0].display_info.error_display

    step3, step4 = step_factory.build_batch(
        2, form_id=mock_form.id, is_dirty=False
    )
    step3.name = 'step3'
    step3.id = 1
    step4.name = 'step4'
    step4.id = 2
    form_publish_check = FormPublishCheck(step=[step3, step4], widget_info=[])
    publish_check_descriptions = form_publish_check.FlowCheck.check_begin(form_publish_check)
    assert 1 == len(publish_check_descriptions)
    assert "只能有一个开始节点，请检查多余的步骤：【step4】" == publish_check_descriptions[0].display_info.error_display

    step5, step6 = step_factory.build_batch(
        2, form_id=mock_form.id, is_dirty=False
    )
    step5.name = 'step5'
    step5.id = 3
    step6.name = 'step6'
    step6.prev_step_ids = [step5.step_uuid]
    step6.id = 4
    form_publish_check = FormPublishCheck(step=[step5, step6], widget_info=[])
    publish_check_descriptions = form_publish_check.FlowCheck.check_begin(form_publish_check)
    assert 0 == len(publish_check_descriptions)


def test_check_reachable(client, step_factory, mock_form: Form):
    step1, step2 = step_factory.build_batch(
        2, form_id=mock_form.id, is_dirty=False
    )
    step1.name = 'step1'
    step1.step_uuid = '11111'
    step2.name = 'step2'
    step2.step_uuid = '22222'
    step1.step_type = StepType.auto
    step2.step_type = StepType.auto
    step1.next_step_ids = ['111']
    step2.prev_step_ids = ['222']
    form_publish_check = FormPublishCheck(step=[step1, step2], widget_info=[])
    publish_check_descriptions = form_publish_check.FlowCheck.check_reachable(form_publish_check)
    assert 2 == len(publish_check_descriptions)
    assert '后续步骤不存在[111]' == publish_check_descriptions[0].display_info.error_display
    assert '步骤不可达' == publish_check_descriptions[1].display_info.error_display


def test_check_assignee_rule(client, step_factory, mock_form: Form):
    step1, step2 = step_factory.build_batch(
        2, form_id=mock_form.id, is_dirty=False
    )
    step1.name = 'step1'
    step1.step_uuid = '11111'
    step2.name = 'step2'
    step2.step_uuid = '22222'
    step1.next_step_ids = ['22222']
    step2.prev_step_ids = ['11111']
    step1.step_type = StepType.auto
    step2.step_type = StepType.auto
    step2.assignee_rule = AssigneeRule.MANUAL
    form_publish_check = FormPublishCheck(step=[step1, step2], widget_info=[])
    publish_check_descriptions = form_publish_check.FlowCheck.check_assignee_rule(form_publish_check)
    assert 1 == len(publish_check_descriptions)
    assert '不允许设置手动分派逻辑' == publish_check_descriptions[0].display_info.error_display


def test_check_check_assistant(client, mock_step: Step):
    mock_step.step_type = StepType.human
    form_publish_check = FormPublishCheck(step=[mock_step], widget_info=[])
    publish_check_descriptions = form_publish_check.StepCheck.check_assistant(form_publish_check, mock_step)
    assert 1 == len(publish_check_descriptions)
    assert '缺少客服信息' == publish_check_descriptions[0].display_info.error_display


def test_check_widget_collection(client, mock_step: Step):
    mock_step.step_type = StepType.human
    form_publish_check = FormPublishCheck(step=[mock_step], widget_info=[])
    publish_check_descriptions = form_publish_check.StepCheck.check_widget_collection(form_publish_check, mock_step)
    assert 1 == len(publish_check_descriptions)
    assert '缺少表单信息' == publish_check_descriptions[0].display_info.error_display


def test_check_widget_collection_refer_widget_info(client, step_factory, mock_form: Form):
    step1, step2 = step_factory.build_batch(
        2, form_id=mock_form.id, is_dirty=False
    )
    widget_info = [
        FormPublishCheckWidgetInfo(widget_info_uuid='w1111', widget_collection_id=1, is_ref=False),
        FormPublishCheckWidgetInfo(widget_info_uuid='w2222', widget_collection_id=2, is_ref=True)
    ]

    step1.name = 'step1'
    step1.step_uuid = '11111'
    step2.name = 'step2'
    step2.step_uuid = '22222'
    step1.next_step_ids = ['22222']
    step2.prev_step_ids = ['11111']
    step2.widget_collection_id = '2'
    step2.step_type = StepType.human

    form_publish_check = FormPublishCheck(step=[step1, step2], widget_info=widget_info)
    publish_check_descriptions = \
        form_publish_check.StepCheck.check_widget_collection_refer_widget_info(form_publish_check, step2)
    assert 1 == len(publish_check_descriptions)
    assert '表单引用的组件不存在' == publish_check_descriptions[0].display_info.error_display


def test_check_task(client, mock_step: Step, mock_form: Form, rpa_context_factory, rpa):
    mock_form.subscribe(client.shop, True)
    smartcall_rpa = rpa(task='SMARTCALL')
    rpa_context = rpa_context_factory.create(org_id=client.org_id)
    rpa_context.rpa_id = smartcall_rpa.id

    mock_step.step_type = StepType.auto
    mock_step.form_id = mock_form.id

    mock_step.data = {"rpa_id": 2}
    form_publish_check = FormPublishCheck(step=[mock_step], widget_info=[])
    publish_check_descriptions = form_publish_check.StepCheck.check_task(form_publish_check, mock_step, mock_step.task)
    assert 1 == len(publish_check_descriptions)
    assert '缺少任务信息' == publish_check_descriptions[0].display_info.error_display

    mock_step.data = {"rpa_id": smartcall_rpa.id}
    form_publish_check = FormPublishCheck(step=[mock_step], widget_info=[])
    publish_check_descriptions = form_publish_check.StepCheck.check_task(form_publish_check, mock_step, mock_step.task)
    assert 0 == len(publish_check_descriptions)


def test_check_task_arguments_required(client, mock_step: Step, mock_form: Form, rpa_context_factory, rpa):
    mock_form.subscribe(client.shop, True)
    memo_rpa = rpa(task='MEMO')
    rpa_context = rpa_context_factory.create(org_id=client.org_id)
    rpa_context.rpa_id = memo_rpa.id
    mock_step.step_type = StepType.auto
    mock_step.form_id = mock_form.id

    mock_step.data = {"rpa_id": memo_rpa.id}
    form_publish_check = FormPublishCheck(step=[mock_step], widget_info=[])
    publish_check_descriptions = form_publish_check.StepCheck.check_task_arguments(form_publish_check,
                                                                                   mock_step, mock_step.task)
    assert 5 == len(publish_check_descriptions)
    assert '请输入【订单备注(输入)】' == publish_check_descriptions[0].display_info.error_display


def test_check_task_arguments_branch_error(client, mock_step, mock_form, rpa_context_factory, rpa):
    mock_form.subscribe(client.shop, True)
    send_qq_rpa = rpa(task='SEND_QQ_GROUP')
    rpa_context = rpa_context_factory.create(org_id=client.org_id)
    rpa_context.rpa_id = send_qq_rpa.id
    mock_step.step_type = StepType.auto
    mock_step.form_id = mock_form.id

    mock_step.key_map = {"branch": {"single": {"content": [{"text": " 拦截退回 ", "type": "text"}],
                                               "qq_group": " 菲棉 - 中通查件群 2.3"},
                                    "send_method": "same_send_same",
                                    "branch_status": "single",
                                    "same_send_same":
                                        {"multiple": [{"id": "de4d3c7b-c1f0-4a3d-9535-936c5e3c90b4",
                                                       "name": " 规则 1", "next": "", "skip": False, "type": "NORMAL",
                                                       "order": 1, "enable": True,
                                                       "condition_group": {"relation": "AND",
                                                                           "condition_list": [{"data": {},
                                                                                               "type": "single"}]}},
                                                      {"id": "acdb2e21-b31c-42af-89fd-ba1aec69cab6", "name": "兜底规则",
                                                       "next": "", "skip": False, "type": "DEFAULT", "order": 9999,
                                                       "enable": True}]}, "different_send_different": {}}}

    mock_step.data = {"rpa_id": send_qq_rpa.id}
    form_publish_check = FormPublishCheck(step=[mock_step], widget_info=[])
    publish_check_descriptions = form_publish_check.StepCheck.check_task_arguments(form_publish_check,
                                                                                   mock_step, mock_step.task)
    assert 1 == len(publish_check_descriptions)
    assert '请输入【发送账号QQ(输入)】' == publish_check_descriptions[0].display_info.error_display


def test_check_task_arguments_branch_success(client, mock_step, mock_form, rpa_context_factory, rpa):
    mock_form.subscribe(client.shop, True)
    send_qq_rpa = rpa(task='SEND_QQ_GROUP')
    rpa_context = rpa_context_factory.create(org_id=client.org_id)
    rpa_context.rpa_id = send_qq_rpa.id
    mock_step.step_type = StepType.auto
    mock_step.form_id = mock_form.id

    mock_step.key_map = {"branch": {"single": {"content": [{"text": " 拦截退回 ", "type": "text"}],
                                               "qq_group": " 菲棉 - 中通查件群 2.3", "qq_id": "12334"},
                                    "send_method": "same_send_same",
                                    "branch_status": "single", "same_send_same":
                                        {"multiple": [{"id": "de4d3c7b-c1f0-4a3d-9535-936c5e3c90b4",
                                                       "name": " 规则 1", "next": "", "skip": False, "type": "NORMAL",
                                                       "order": 1, "enable": True,
                                                       "condition_group": {"relation": "AND",
                                                                           "condition_list": [{"data": {},
                                                                                               "type": "single"}]}},
                                                      {"id": "acdb2e21-b31c-42af-89fd-ba1aec69cab6", "name": "兜底规则",
                                                       "next": "", "skip": False, "type": "DEFAULT", "order": 9999,
                                                       "enable": True}]}, "different_send_different": {}}}

    mock_step.data = {"rpa_id": send_qq_rpa.id}
    form_publish_check = FormPublishCheck(step=[mock_step], widget_info=[])
    publish_check_descriptions = form_publish_check.StepCheck.check_task_arguments(form_publish_check,
                                                                                   mock_step, mock_step.task)
    assert 0 == len(publish_check_descriptions)


def test_check_branch(client, mock_step, mock_form, rpa_context_factory, rpa):
    mock_form.subscribe(client.shop, True)
    smartcall_rpa = rpa(task='SMARTCALL')
    rpa_context = rpa_context_factory.create(org_id=client.org_id)
    rpa_context.rpa_id = smartcall_rpa.id
    mock_step.step_type = StepType.exclusive_gateway
    mock_step.form_id = mock_form.id
    mock_step.data = {"rpa_id": smartcall_rpa.id}

    form_publish_check = FormPublishCheck(step=[mock_step], widget_info=[])
    publish_check_descriptions = form_publish_check.StepCheck.check_branch(form_publish_check, mock_step)
    assert 1 == len(publish_check_descriptions)
    assert '缺少分支' == publish_check_descriptions[0].display_info.error_display

    mock_step.branch = [{"id": "a1831fda-5614-4a6d-992a-713b469e5262", "name": "分支 - 1",
                         "next": "ac4d65faa18b41acba08da78fd1c88ff", "type": "NORMAL", "order": 1,
                         "enable": True, "condition_group": {"relation": "AND",
                                                             "condition_list": [{"data": {
                                                               "ref": "62ae61e2f45842cb99369bc77a032bc6",
                                                               "value": 500, "operator": "LT",
                                                               "ref_type": "number", "value_type": "NUMBER"},
                                                               "type": "single"}]}},
                        {"id": "2bc6be62-8ca2-4e57-82f5-52450bc9c4fc", "name": "默认分支",
                         "next": "97aa6f52cd0a465d91f13f8d1f427f58", "type": "DEFAULT", "order": 9999, "enable": True}]
    form_publish_check = FormPublishCheck(step=[mock_step], widget_info=[])
    publish_check_descriptions = form_publish_check.StepCheck.check_branch(form_publish_check, mock_step)
    assert 2 == len(publish_check_descriptions)
    assert '分支[分支 - 1]后续步骤不匹配' == publish_check_descriptions[0].display_info.error_display


class Test检查分支条件引用的枚举值是否有效:
    def test跳过默认分支(self, mocker):
        branch_list = [BranchDict(type='DEFAULT', id="1", name="默认分支")]
        check = FormPublishCheck(step=[], widget_info=[])
        result = check.CommonCheck.check_branch_condition_enum_options_refer(
            check, branch_list
        )
        assert not result

    def test跳过没有使用枚举的分支条件(self, mocker):
        branch_list = [
            BranchDict(
                type="NORMAL",
                id="1",
                name="分支1",
                condition_group=ConditionGroupDict(
                    relation="AND",
                    condition_list=[
                        # 仅判断 value_type=ENUM 的
                        ConditionDict(data=ConditionDataDict(value_type="STRING"))
                    ]
                )
            )
        ]
        check = FormPublishCheck(step=[], widget_info=[])
        result = check.CommonCheck.check_branch_condition_enum_options_refer(
            check, branch_list
        )
        assert not result

    def test无需判断的操作符的分支条件(self, mocker):
        branch_list = [
            BranchDict(
                type="NORMAL",
                id="1",
                name="分支1",
                condition_group=ConditionGroupDict(
                    relation="AND",
                    condition_list=[
                        # 会跳过 operator=EXISTS 的
                        ConditionDict(data=ConditionDataDict(
                            value_type="ENUM",
                            operator="EXISTS",
                        ))
                    ]
                )
            )
        ]
        check = FormPublishCheck(step=[], widget_info=[])
        result = check.CommonCheck.check_branch_condition_enum_options_refer(
            check, branch_list
        )
        assert not result

    def test有失效的候选项(self, mocker):
        branch_list = [
            BranchDict(
                type="NORMAL",
                id="1",
                name="分支1",
                condition_group=ConditionGroupDict(
                    relation="AND",
                    condition_list=[
                        # 会跳过 operator=EXISTS 的
                        ConditionDict(data=ConditionDataDict(
                            ref_type="radio-tile",
                            ref="radio-tile-widget-info-key",
                            value_type="ENUM",
                            value=[["1"], ["2"]],
                            operator="MATCH_ANY",
                        ))
                    ]
                )
            )
        ]
        check = FormPublishCheck(step=[], widget_info=[
            FormPublishCheckWidgetInfo(
                widget_info_uuid=WidgetRef(
                    key="radio-tile-widget-info-key",
                    type="radio-tile",
                    widget_type="enum",
                    field=None
                ),
                widget_collection_id=1,
                is_ref=False,
                option_value=dict(
                    # value=2 的失效了，预期要检查出来
                    options=[dict(label="1", value="1"), dict(label="3", value="3")]
                )
            )
        ])
        result = check.CommonCheck.check_branch_condition_enum_options_refer(
            check, branch_list
        )
        assert len(result) == 1

        branch_dict, invalid_option = result[0]
        assert invalid_option == "2"
        assert branch_dict["name"] == "分支1"


class Test检查自动化任务引用的组件是否有效:
    def build_case_1_step(self):
        step = Step()
        step.prev_step_ids = []
        step.key_map = dict(trade_no="trade-widget-info-key")
        step.step_type = StepType.human
        return step

    def test_case_1_data_type_select(self):
        step = self.build_case_1_step()
        task_argument = dict(data_type=DataType.SELECT.value, name="trade_no", label="订单号")
        check = FormPublishCheck(step=[step], widget_info=[])
        result = check.TaskArgumentCheck.check_refer(
            check, step, step.key_map, task_argument
        )
        assert result.is_err()
        check_description = result.err()
        assert len(check_description) == 1
        err_desc = check_description[0]
        assert err_desc.metadata.concept_key == "trade-widget-info-key"

    def build_case_2_step(self):
        step = Step()
        step.prev_step_ids = []
        step.key_map = dict(content=[
            dict(type="concept", label="订单号", key="trade-widget-info-key")
        ])
        step.step_type = StepType.auto
        return step

    def test_case_2_data_type_input_textarea(self):
        step = self.build_case_2_step()
        task_argument = dict(
            data_type=DataType.INPUT_TEXTAREA.value,
            name="content",
            label="发送内容"
        )
        check = FormPublishCheck(step=[step], widget_info=[])
        result = check.TaskArgumentCheck.check_refer(
            check, step, step.key_map, task_argument
        )
        assert result.is_err()
        check_description = result.err()
        assert len(check_description) == 1
        err_desc = check_description[0]
        assert err_desc.metadata.concept_key == "trade-widget-info-key"

    def build_case_3_step_single(self):
        step = Step()
        step.prev_step_ids = []
        step.key_map = dict(
            branch=dict(
                branch_status="single",
                single=dict(
                    content=[
                        dict(type="concept", label="订单号", key="trade-widget-info-key")
                    ]
                )
            )
        )
        step.step_type = StepType.auto
        return step

    def build_case_3_step_same_send_same(self):
        step = Step()
        step.prev_step_ids = []
        step.key_map = dict(
            branch=dict(
                branch_status="multiple",
                send_method="same_send_same",
                same_send_same=dict(
                    multiple=[
                        dict(
                           content=[
                               dict(type="concept",
                                    label="订单号",
                                    key="trade-widget-info-key")
                           ]
                        )
                    ]
                )
            )
        )
        step.step_type = StepType.auto
        return step

    def build_case_3_step_different_send_different(self):
        step = Step()
        step.prev_step_ids = []
        step.key_map = dict(
            branch=dict(
                branch_status="multiple",
                send_method="different_send_different",
                different_send_different=dict(
                    multiple=[
                        dict(
                           content=[
                               dict(type="concept",
                                    label="订单号",
                                    key="trade-widget-info-key")
                           ]
                        )
                    ]
                )
            )
        )
        step.step_type = StepType.auto
        return step

    def test_case_3_data_type_msg_rules(self):
        # 单消息规则
        step_single = self.build_case_3_step_single()
        task_argument = dict(
            data_type=DataType.MSG_RULES.value,
            name="branch",
            label="发消息规则",
            arguments=[
                dict(
                    name="content",
                    label="发送内容",
                    data_type=DataType.INPUT_TEXTAREA.value
                )
            ]
        )
        check = FormPublishCheck(step=[step_single], widget_info=[])
        result = check.TaskArgumentCheck.check_refer(
            check, step_single, step_single.key_map, task_argument
        )
        assert result.is_err()
        check_description = result.err()
        assert len(check_description) == 1
        err_desc = check_description[0]
        assert err_desc.metadata.concept_key == "trade-widget-info-key"
        # 单账号规则
        step_same = self.build_case_3_step_same_send_same()
        check = FormPublishCheck(step=[step_same], widget_info=[])
        result = check.TaskArgumentCheck.check_refer(
            check, step_same, step_same.key_map, task_argument
        )
        assert result.is_err()
        check_description = result.err()
        assert len(check_description) == 1
        err_desc = check_description[0]
        assert err_desc.metadata.concept_key == "trade-widget-info-key"
        # 多账号规则
        step_different = self.build_case_3_step_different_send_different()
        check = FormPublishCheck(step=[step_different], widget_info=[])
        result = check.TaskArgumentCheck.check_refer(
            check, step_different, step_different.key_map, task_argument
        )
        assert result.is_err()
        check_description = result.err()
        assert len(check_description) == 1
        err_desc = check_description[0]
        assert err_desc.metadata.concept_key == "trade-widget-info-key"
