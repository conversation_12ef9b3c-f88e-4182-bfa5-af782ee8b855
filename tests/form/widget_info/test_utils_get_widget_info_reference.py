from robot_processor.form.models import WidgetInfo, WidgetRef


RawStep = WidgetInfo.View.RawStep


def test_get_widget_info_reference_when_before_is_not_true():
    # Create a mock widget_info object
    widget_info = RawStep(
        before=False, key="", type="string", widget_type="string"
    )

    # Call the method under test
    result = WidgetInfo.Utils.get_widget_info_reference(widget_info)

    # Assert that the result is None
    assert result is None


def test_get_widget_info_reference_when_before_widget_ref_exists_in_option_value():
    # Create a mock widget_info object
    widget_info = RawStep(
        before=True,
        key="",
        type="string",
        widget_type="string",
        option_value={
            "before_widget_ref": {
                "key": "test_key",
                "type": "string",
                "widget_type": "string",
            }
        },
    )

    # Call the method under test
    result = WidgetInfo.Utils.get_widget_info_reference(widget_info)

    # Assert that the result is a WidgetRef object with the expected attributes
    assert isinstance(result, WidgetRef)
    assert result.key == "test_key"
    assert result.type == "string"
    assert result.widget_type == "string"


def test_get_widget_info_reference_when_before_widget_ref_does_not_exist_in_option_value():
    # Create a mock widget_info object
    widget_info = RawStep(
        before=True,
        key="test_key",
        type="string",
        widget_type="string",
        option_value={},
    )
    # Call the method under test
    result = WidgetInfo.Utils.get_widget_info_reference(widget_info)

    # Assert that the result is a WidgetRef object with the expected attributes
    assert isinstance(result, WidgetRef)
    assert result.key == "test_key"
    assert result.type == "string"
    assert result.widget_type == "string"
    assert result.multi_row is None
    assert result.field is None
