from pytest import fixture

from robot_processor.form.models import WidgetInfo, WidgetRef


@fixture
def widget_info_string():
    return WidgetInfo.View.RawStep(
        key="这是一个单行文本组件的 widget key",
        type="string",
        widget_type="string",
        option_value=dict(
            label="这是一个单行文本组件的 label",
        ),
    )


def test_简单组件(widget_info_string):
    paths = WidgetInfo.Utils.flatten_widget_info_tree_paths(widget_info_string)
    assert len(paths) == 1
    path = paths[0]
    assert path.depth == 1
    assert path.root == path.leaf
    assert path.root.key == path.leaf.key == "这是一个单行文本组件的 widget key"
    assert path.as_widget_ref() == WidgetRef(
        key="这是一个单行文本组件的 widget key",
        type="string",
        widget_type="string",
        multi_row=False,
        field=None,
    )
    assert path.label_list == ["这是一个单行文本组件的 label"]
    assert path.widget_type_list == ["string"]


@fixture
def widget_info_table():
    return WidgetInfo.View.RawStep(
        key="快递信息组件 widget key",
        type="table",
        widget_type="table",
        option_value=dict(
            label="快递信息",
            fields=[
                dict(
                    key="快递公司组件 widget key",
                    type="string",
                    widget_type="string",
                    option_value=dict(
                        label="快递公司",
                    ),
                ),
                dict(
                    key="快递单号 widget key",
                    type="table",
                    widget_type="table",
                    option_value=dict(
                        label="快递单号列表",
                        fields=[
                            dict(
                                key="快递单号 widget key",
                                type="string",
                                widget_type="string",
                                option_value=dict(
                                    label="快递单号",
                                ),
                            ),
                        ],
                    ),
                ),
            ],
        ),
    )


def test_Table嵌套(widget_info_table):
    """嵌套的容器会为每一个叶子节点生成一个 WidgetRef"""
    paths = WidgetInfo.Utils.paths = WidgetInfo.Utils.flatten_widget_info_tree_paths(
        widget_info_table
    )
    assert len(paths) == 2
    path_logistics_company = paths[0]
    path_logistics_no = paths[1]

    assert path_logistics_company.depth == 2
    assert path_logistics_company.root.key == "快递信息组件 widget key"
    assert path_logistics_company.leaf.key == "快递公司组件 widget key"
    assert path_logistics_company.as_widget_ref() == WidgetRef(
        key="快递信息组件 widget key",
        type="table",
        widget_type="table",
        multi_row=False,
        field=WidgetRef(
            key="快递公司组件 widget key",
            type="string",
            widget_type="string",
            multi_row=False,
            field=None,
        ),
    )

    assert path_logistics_no.depth == 3
    assert path_logistics_no.root.key == "快递信息组件 widget key"
    assert path_logistics_no.leaf.key == "快递单号 widget key"
    assert path_logistics_no.as_widget_ref() == WidgetRef(
        key="快递信息组件 widget key",
        type="table",
        widget_type="table",
        multi_row=False,
        field=WidgetRef(
            key="快递单号 widget key",
            type="table",
            widget_type="table",
            multi_row=False,
            field=WidgetRef(
                key="快递单号 widget key",
                type="string",
                widget_type="string",
                multi_row=False,
                field=None,
            ),
        ),
    )
