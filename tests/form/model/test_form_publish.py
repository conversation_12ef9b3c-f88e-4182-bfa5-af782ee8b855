from pytest import fixture
from pytest_mock import MockFixture

from robot_processor.ext import db


class TestFormPublish:
    @fixture(autouse=True)
    def setup(
        self,
        client,
        mock_form,
        mock_step,
        mock_widget_info,
        mock_widget_collection,
    ):
        mock_form.subscribe(client.shop, True)
        mock_step.is_dirty = True
        mock_step.form = mock_form
        mock_widget_info.option_value = {"label": "ut"}
        mock_widget_info.widget_collection = mock_widget_collection
        mock_step.step_type = "human"
        mock_step.widget_collection = mock_widget_collection
        db.session.commit()
        yield

    @fixture
    def mock_kafka_producer(self, mocker: MockFixture):
        from robot_processor.ext import robot_form_change_producer

        patch = mocker.patch(
            "robot_processor.ext.robot_form_change_producer",
            autospec=robot_form_change_producer,
        )
        yield patch

    def test_widget_collection_publish(self, client, mock_form, mock_kafka_producer):
        mock_form.wraps(client.shop).publish()
        mock_kafka_producer.assert_called()
