from pytest import fixture, mark

from robot_processor.ext import db
from robot_processor.form.models import FormVersion, Form


class TestForm__snapshot:
    @fixture
    def testbed_form_version(self, client, mock_form):
        form_version = FormVersion()
        form_version.form = mock_form
        form_version.step_id = []
        form_version.job_road = {}
        form_version.version_no = "V0.0.1"
        form_version.version_descriptor = "HEAD"
        form_version.updator = "ut"
        db.session.add(form_version)
        db.session.commit()
        yield form_version

    @mark.usefixtures("testbed_form_version")
    def test_update(self, mock_form):
        last_version = mock_form.versions.first()
        assert last_version.meta == {}

        new_version = mock_form.snapshot()
        assert last_version == new_version
        assert new_version.updator == mock_form.update_user
        assert new_version.meta == Form.View.FormVersionMeta.from_orm(mock_form).dict()
        assert new_version.version_no == "V0.0.1"
        assert new_version.version_descriptor == "HEAD"

    @mark.usefixtures("testbed_form_version")
    def test_new(self, mock_form, testbed_form_version):
        # 修改 step_id，可以让 snapshot 走到 NEW 的分支策略上
        testbed_form_version.step_id = [1]
        db.session.commit()
        last_version = testbed_form_version

        new_version = mock_form.snapshot()
        assert new_version != last_version
        assert new_version.updator == mock_form.update_user
        assert new_version.version_no == "V0.0.2"
        assert new_version.version_descriptor == "HEAD"
        assert new_version.meta == Form.View.FormVersionMeta.from_orm(mock_form).dict()

        # 不会修改 last version 的信息
        assert last_version.meta == {}
        assert last_version.version_no == "V0.0.1"
        assert last_version.version_descriptor != "HEAD"
