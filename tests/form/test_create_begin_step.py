from robot_processor.form.system_widget_utils import create_step_0
from robot_processor.assistant.schema import AssistantV2
from robot_processor.ext import db


def test_create_begin_step(
    mocker, mock_form,
):
    owners = {
        'details': [
            {
                "user_type": 4,
                "user_id": 1,
                "user_nick": "user"
            }
        ],
        'online_only': False,
        'select_type': 2,
        'leyan_accounts': [
            {
                "user_type": 4,
                "user_id": 1,
                "user_nick": "user"
            }
        ],
        'assign_strategy': 1,
        'assignee_groups': [],
        'channel_accounts': [],
    }

    begin_step = create_step_0(mock_form, owners=owners)
    db.session.add(begin_step)
    db.session.commit()

    assert begin_step.assistants_v2 == {
        'details': [],
        'online_only': False,
        'select_type': 1,
        'leyan_accounts': [],
        'assign_strategy': 1,
        'assignee_groups': [],
        'channel_accounts': [],
        'owners': AssistantV2.parse(owners).dict()
    }

    assert begin_step.get_assistants_v2() == AssistantV2.parse({
        'details': [],
        'online_only': False,
        'select_type': 1,
        'leyan_accounts': [],
        'assign_strategy': 1,
        'assignee_groups': [],
        'channel_accounts': [],
    })

    assert mock_form.get_owners() == AssistantV2.parse(owners)
