"""测试数据范围筛选-查询数目"""
from pytest import fixture
from google.protobuf import struct_pb2
from leyan_proto.digismart.robot.bi.generate_pb2 import (
    DataRangeGroups,
    DataRange, DataStatistics,
)

from robot_processor.bi.dashboard import ReportPanel
from robot_processor.bi.generate_schema.dimension.system import BO_ID, QUERY_LIMIT
from robot_processor.bi.dataset.etl.pipeline import get_raw_sql
from robot_processor.bi.dataset.etl.extract import dataset_from_database


expect_raw_sql = """SELECT count(transformed_business_order.id) / CAST((SELECT count(transformed_business_order.id) AS count_1 
FROM transformed_business_order 
WHERE transformed_business_order.form_id IN (604652) AND transformed_business_order.deleted IS NOT true) AS NUMERIC) AS "工单ID-占比" 
FROM transformed_business_order 
WHERE transformed_business_order.form_id IN (604652) AND transformed_business_order.deleted IS NOT true
 LIMIT 22"""  # noqa


class TestDataTypeRatio:
    @fixture
    def mock_report_panel(self):
        panel = ReportPanel()
        panel.statistics = [DataStatistics(
            dimension=BO_ID,
            operator=DataStatistics.OPERATOR_RATIO)]
        panel.data_range_groups = DataRangeGroups(
            data_range_groups=[
                DataRangeGroups.DataRangeGroup(
                    data_ranges=[
                        DataRange(
                            dimension=QUERY_LIMIT,
                            operator=DataRange.OPERATOR_EQUAL,
                            value=struct_pb2.Value(number_value=22),
                        )
                    ]
                )
            ]
        )
        default_data_source = panel.data_source
        default_data_source.form_ids.append(604652)
        panel.data_source = default_data_source

        yield panel

    def test_ratio(self, mock_report_panel):
        df = mock_report_panel.build_statement().and_then(dataset_from_database).unwrap()
        assert df.shape[0] == 1
        assert get_raw_sql(mock_report_panel).unwrap_or(None) == expect_raw_sql
