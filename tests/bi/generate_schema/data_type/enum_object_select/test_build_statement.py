"""测试 DATA_TYPE_ENUM_OBJECT_SELECT 在生成 SQL 的表现
"""
from google.protobuf.json_format import ParseDict
from google.protobuf import struct_pb2
from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimensionDisplay, DataRange

from robot_processor.bi.generate_schema.data_type import ReportDataType
from robot_processor.form.types import WidgetDataType


class CaseMixin:
    def get_sql(self, statement):
        return str(statement.compile(compile_kwargs={"literal_binds": True}))


class TestDataTypeEnumObjectSelect(CaseMixin):
    def get_dimension_widget_select(self):
        from robot_processor.bi.generate_schema.dimension.custom import (
            EnumObjectSelectBuilder,
        )

        return EnumObjectSelectBuilder(
            widget_type="select",
            sync_widget_key=None,
            widget_info_list=[
                {"option_value": {"label": "ut"}, "key": "unittest-key"},
                {"option_value": {"label": "ut"}, "key": "unittest-other-key"},
            ],
            widget_data_type=WidgetDataType.ENUM
        ).build_select()

    def get_dimension_display_widget_select(self):
        return DataDimensionDisplay(dimension=self.get_dimension_widget_select())

    def test_select_field(self):
        dimension_display = self.get_dimension_display_widget_select()
        agent = ReportDataType.auto_init_dimension_display(dimension_display)
        # 根据 dimension 的 DataType 匹配到了正确的 ReportDataTypeAgent
        assert (
            str(agent)
            == "<ReportDataType[DimensionDisplay]:DATA_TYPE_ENUM_OBJECT_SELECT>"
        )
        assert (
            self.get_sql(agent.get_select_field())
            == """coalesce(json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-key"')), json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-other-key"')))"""  # noqa
        )

    def get_data_range_equal_single_level(self):
        dimension = self.get_dimension_widget_select()

        return DataRange(
            dimension=dimension,
            operator=DataRange.OPERATOR_EQUAL,
            value=ParseDict({"label": "", "value": ["ut"]}, struct_pb2.Value()),
        )

    def get_data_range_equal_multi_level(self):
        dimension = self.get_dimension_widget_select()

        return DataRange(
            dimension=dimension,
            operator=DataRange.OPERATOR_EQUAL,
            value=ParseDict({"label": "", "value": ["其他问题", "补发"]}, struct_pb2.Value()),
        )

    def test_where_clause(self):
        # 单层级的场景
        data_range = self.get_data_range_equal_single_level()
        agent = ReportDataType.auto_init_data_range(data_range)
        # 根据 dimension 的 DataType 匹配到了正确的 ReportDataTypeAgent
        assert str(agent) == "<ReportDataType[DataRange]:DATA_TYPE_ENUM_OBJECT_SELECT>"
        assert (
            self.get_sql(agent.get_where_clause())
            == """json_unquote(json_extract(coalesce(json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-key"')), json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-other-key"'))), '$[0].value')) = 'ut' OR json_contains(json_extract(coalesce(json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-key"')), json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-other-key"'))), '$[*][0]'), json_object('value', 'ut'))"""  # noqa
        )

        # 多层级的场景
        data_range = self.get_data_range_equal_multi_level()
        agent = ReportDataType.auto_init_data_range(data_range)
        assert (
            self.get_sql(agent.get_where_clause())
            == """json_unquote(json_extract(coalesce(json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-key"')), json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-other-key"'))), '$[0].value')) = '其他问题' AND json_unquote(json_extract(coalesce(json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-key"')), json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-other-key"'))), '$[1].value')) = '补发' OR json_contains(json_extract(coalesce(json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-key"')), json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-other-key"'))), '$[*][0]'), json_object('value', '其他问题')) AND json_contains(json_extract(coalesce(json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-key"')), json_unquote(json_extract(transformed_business_order.raw_data, '$."unittest-other-key"'))), '$[*][1]'), json_object('value', '补发'))"""  # noqa
        )
