"""测试 DATA_TYPE_ENUM_OBJECT_SELECT 在 etl 的表现"""
import pandas as pd
from pytest import fixture

from robot_processor.form.types import WidgetDataType
from leyan_proto.digismart.robot_web.bi_pb2 import \
    ListReportDataDimensionResponse

DimensionInfo = ListReportDataDimensionResponse.ReportDataDimensionInfo

class CaseMixin:
    @property
    def form_id_for_test(self):
        return 1

    def get_panel(self):
        from leyan_proto.digismart.robot.bi.report_pb2 import ReportDataSource
        from robot_processor.bi.dashboard import ReportPanel

        panel = ReportPanel()
        panel.mode = ReportPanel.PanelMode.GENERATE
        panel.data_source = ReportDataSource(form_ids=[self.form_id_for_test])

        return panel

    def get_dimension_widget_select(self):
        from robot_processor.bi.generate_schema.dimension.custom import (
            EnumObjectSelectBuilder,
        )

        return EnumObjectSelectBuilder(
            widget_type="select",
            sync_widget_key=None,
            widget_info_list=[
                {"option_value": {"label": "ut"}, "key": "unittest-key"},
                {"option_value": {"label": "ut"}, "key": "unittest-other-key"},
            ],
            widget_data_type=WidgetDataType.ENUM
        ).build_select()


class TestDataTypeEnumObjectSelect(CaseMixin):
    @fixture
    def setup_single_choice_bo(self, client):
        """测试单选的场景"""
        from robot_processor.bi.database import Session, ReportBusinessOrder

        with Session() as session, session.begin():
            session.add(
                ReportBusinessOrder(
                    form_id=self.form_id_for_test,
                    data={"unittest-key": [{"label": "补发", "value": "补发"}]},
                )
            )
            session.add(
                ReportBusinessOrder(
                    form_id=self.form_id_for_test,
                    data={
                        "unittest-other-key": [
                            {"label": "其他问题", "value": "其他问题"},
                            {"label": "仅退款", "value": "仅退款"},
                        ]
                    },
                )
            )
        yield

    @fixture
    def mock_panel(self):
        from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimensionDisplay

        dimension = self.get_dimension_widget_select()

        panel = self.get_panel()
        data = dimension.dimension if isinstance(dimension, DimensionInfo) else dimension
        panel.dimensions = [DataDimensionDisplay(dimension=data)]
        yield panel

    def test_single_choice_select(self, setup_single_choice_bo, mock_panel):
        """将复杂类型转换为可读类型"""
        from robot_processor.bi.dataset.etl.pipeline import panel_dataset_from_database

        df: pd.DataFrame = panel_dataset_from_database(mock_panel).unwrap()
        assert set(map(tuple, df.values.tolist())) == set(
            map(tuple, [["补发"], ["其他问题>仅退款"]])
        )

    def test_single_choice_where_clause(self):
        ...


class TestCasePruning(CaseMixin):
    """测试剪枝下拉选择组件"""

    @fixture
    def setup_bo(self, client):
        from robot_processor.bi.database import Session, ReportBusinessOrder

        with Session() as session, session.begin():
            session.add(
                ReportBusinessOrder(
                    form_id=self.form_id_for_test,
                    data={
                        "level-3": [
                            [
                                {"label": "第一级", "value": "第一级"},
                                {"label": "第二级", "value": "第二级"},
                                {"label": "第三级", "value": "第三级"},
                            ],
                            [
                                {"label": "第1级", "value": "第1级"},
                                {"label": "第2级", "value": "第2级"},
                                {"label": "第3级", "value": "第3级"},
                            ],
                        ]
                    },
                )
            )
            session.add(
                ReportBusinessOrder(
                    form_id=self.form_id_for_test,
                    data={
                        "level-3": [
                            [
                                {"label": "第一级-1", "value": "第一级-1"},
                                {"label": "第二级", "value": "第二级"},
                                {"label": "第三级", "value": "第三级"},
                            ],
                            [
                                {"label": "第1级-1", "value": "第1级-1"},
                                {"label": "第2级", "value": "第2级"},
                                {"label": "第3级", "value": "第3级"},
                            ],
                        ]
                    },
                )
            )

        yield

    def get_specified_level_dimension(self, level):
        from robot_processor.bi.generate_schema.dimension.custom import (
            EnumObjectSelectBuilder,
        )

        dimensions = EnumObjectSelectBuilder(
            widget_type="select",
            sync_widget_key=None,
            widget_info_list=[
                {"option_value": {"label": "level-3", "level": 3}, "key": "level-3"}
            ],
            widget_data_type=WidgetDataType.ENUM
        ).build_specified_level_select()
        return dimensions[level - 1]

    @fixture
    def mock_panel_level_1(self):
        from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimensionDisplay

        dimension = self.get_specified_level_dimension(1)
        panel = self.get_panel()
        panel.dimensions = [DataDimensionDisplay(dimension=dimension.dimension)]

        yield panel

    def test_select_level_1(self, setup_bo, mock_panel_level_1):
        from robot_processor.bi.dataset.etl.pipeline import panel_dataset_from_database

        df: pd.DataFrame = panel_dataset_from_database(mock_panel_level_1).unwrap()
        # 目前对多选支持不友好，升级 Mysql8.0 版本后，以下断言应该成功
        # assert sorted(df.values.tolist(), key=sorted) == sorted(
        #     [["第1级"], ["第1级-1"], ["第一级"], ["第一级-1"]], key=sorted
        # )
        assert sorted(df.values.tolist(), key=sorted) == sorted(
            [["第一级"], ["第一级-1"]], key=sorted
        )

    def test_where_level_1(self, setup_bo, mock_panel_level_1):
        from google.protobuf.struct_pb2 import Struct, Value
        from leyan_proto.digismart.robot.bi.generate_pb2 import (
            DataRangeGroups,
            DataRange,
        )
        from robot_processor.bi.dataset.etl.pipeline import panel_dataset_from_database

        value = Struct()
        value["label"] = ""
        value["value"] = ["第一级"]
        mock_panel_level_1.data_range_groups = DataRangeGroups(
            data_range_groups=[
                DataRangeGroups.DataRangeGroup(
                    relation=DataRange.RELATION_AND,
                    data_ranges=[
                        DataRange(
                            dimension=self.get_specified_level_dimension(1).dimension,
                            operator=DataRange.OPERATOR_EQUAL,
                            value=Value(struct_value=value),
                        )
                    ],
                )
            ],
            relation=DataRange.RELATION_AND,
        )
        df: pd.DataFrame = panel_dataset_from_database(mock_panel_level_1).unwrap()
        assert df.values.tolist() == [["第一级"]]

    @fixture
    def mock_panel_level_2(self):
        from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimensionDisplay

        dimension = self.get_specified_level_dimension(2)
        panel = self.get_panel()
        panel.dimensions = [DataDimensionDisplay(dimension=dimension.dimension)]

        yield panel

    def test_select_level_2(self, setup_bo, mock_panel_level_2):
        from robot_processor.bi.dataset.etl.pipeline import panel_dataset_from_database

        df: pd.DataFrame = panel_dataset_from_database(mock_panel_level_2).unwrap()
        assert set(map(tuple, df.values.tolist())) == set(
            map(
                tuple,
                [
                    ["第一级>第二级"],
                    ["第一级-1>第二级"],
                ],
            )
        )

    def test_where_level_2(self, setup_bo, mock_panel_level_2):
        from google.protobuf.struct_pb2 import Struct, Value
        from leyan_proto.digismart.robot.bi.generate_pb2 import (
            DataRangeGroups,
            DataRange,
        )
        from robot_processor.bi.dataset.etl.pipeline import panel_dataset_from_database

        value = Struct()
        value["label"] = ""
        value["value"] = ["第一级", "第二级"]
        mock_panel_level_2.data_range_groups = DataRangeGroups(
            data_range_groups=[
                DataRangeGroups.DataRangeGroup(
                    relation=DataRange.RELATION_AND,
                    data_ranges=[
                        DataRange(
                            dimension=self.get_specified_level_dimension(2).dimension,
                            operator=DataRange.OPERATOR_EQUAL,
                            value=Value(struct_value=value),
                        )
                    ],
                )
            ],
            relation=DataRange.RELATION_AND,
        )
        df: pd.DataFrame = panel_dataset_from_database(mock_panel_level_2).unwrap()
        assert df.values.tolist() == [["第一级>第二级"]]
