"""测试 DATA_TYPE_DATE 在生成 SQL 的表现
"""
from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimensionDisplay

from robot_processor.bi.generate_schema.data_type import ReportDataType
from robot_processor.form.types import WidgetDataType


class CaseMixin:
    def get_panel(self):
        from robot_processor.bi.dashboard import ReportPanel

        panel = ReportPanel()
        panel.mode = ReportPanel.PanelMode.GENERATE

        return panel

    def get_sql(self, statement):
        return str(statement.compile(compile_kwargs={"literal_binds": True}))


class TestDataTypeDate(CaseMixin):
    def get_dimension_create_time(self):
        from robot_processor.bi.generate_schema.dimension.system import (
            BO_CREATE_TIME_DATE,
        )

        return DataDimensionDisplay(dimension=BO_CREATE_TIME_DATE.dimension)

    def test_created_at_in_select(self):
        """case: 工单创建时间"""

        dimension_display = self.get_dimension_create_time()
        agent = ReportDataType.auto_init_dimension_display(dimension_display)
        # 根据 dimension 的 DataType 匹配到了正确的 ReportDataTypeAgent
        assert str(agent) == "<ReportDataType[DimensionDisplay]:DATA_TYPE_DATE>"
        assert (
            self.get_sql(agent.get_select_field())
            == "date_format(from_unixtime(transformed_business_order.created_at), '%Y-%m-%d')"
        )

    def get_dimension_update_time(self):
        from robot_processor.bi.generate_schema.dimension.system import (
            BO_UPDATE_TIME_DATE,
        )

        return DataDimensionDisplay(dimension=BO_UPDATE_TIME_DATE.dimension)

    def test_updated_at_in_select(self):
        """case: 工单最近更新时间"""

        dimension_display = self.get_dimension_update_time()
        agent = ReportDataType.auto_init_dimension_display(dimension_display)
        # 根据 dimension 的 DataType 匹配到了正确的 ReportDataTypeAgent
        assert str(agent) == "<ReportDataType[DimensionDisplay]:DATA_TYPE_DATE>"
        assert (
            self.get_sql(agent.get_select_field())
            == "date_format(from_unixtime(transformed_business_order.updated_at), '%Y-%m-%d')"
        )

    def get_dimension_widget_datetime(self):
        from robot_processor.bi.generate_schema.dimension.custom import DatetimeBuilder

        return DataDimensionDisplay(
            dimension=DatetimeBuilder(
                "payment-time",
                None,
                [
                    {"option_value": {"label": "ut"}, "key": "unittest-key"},
                    {"option_value": {"label": "ut"}, "key": "other-unittest-key"},
                ],
                widget_data_type=WidgetDataType.DATETIME
            ).build_date()
        )

    def test_custom_widget_datetime_in_select(self):
        """case: 自定义组件-日期时间"""
        dimension_display = self.get_dimension_widget_datetime()
        agent = ReportDataType.auto_init_dimension_display(dimension_display)
        # 根据 dimension 的 DataType 匹配到了正确的 ReportDataTypeAgent
        assert str(agent) == "<ReportDataType[DimensionDisplay]:DATA_TYPE_DATE>"
        assert (
            self.get_sql(agent.get_select_field())
            == "date_format(coalesce(json_unquote(json_extract(transformed_business_order.raw_data, '$.\"unittest-key\"')), json_unquote(json_extract(transformed_business_order.raw_data, '$.\"other-unittest-key\"'))), '%Y-%m-%d')"  # noqa F811
        )
