from robot_processor.bi.dataset.etl.load import dataset_to_echarts_source
from robot_processor.bi.dataset.etl.extract import dataset_from_database


def test_dataset_to_echarts_source(sql):
    from google.protobuf.json_format import ParseDict
    from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimension
    from leyan_proto.digismart.robot.bi.report_pb2 import ReportPanel

    dataset = dataset_from_database(sql).unwrap()
    drill_down_status = ReportPanel.DrillDownStatus(drill_path=["状态", "创建时间（月）", "创建人"])
    sort_rules = [(DataDimension(title="数量"), False)]

    # 默认按第一维度钻取
    assert dataset_to_echarts_source(dataset, values=["数量"]) == [
        ["状态", "数量"],
        ["已关闭", 3],
        ["已完成", 2587],
        ["异常中", 9],
        ["进行中", 31],
    ]
    # 增加排序规则，按照数量降序排列
    assert dataset_to_echarts_source(dataset, sort_rules=sort_rules, values=["数量"]) == [
        ["状态", "数量"],
        ["已完成", 2587],
        ["进行中", 31],
        ["异常中", 9],
        ["已关闭", 3],
    ]

    # 按第二维度钻取
    drill_filter = ParseDict(
        {"dimension": "状态", "value": "已完成"},
        ReportPanel.DrillDownStatus.Filters()
    )
    drill_down_status.drill_filters.append(drill_filter)
    assert dataset_to_echarts_source(
        dataset,
        drill_down_status=drill_down_status,
        values=["数量"]
    ) == [
               ['创建时间（月）', '数量'],
               ['2022-05', 60],
               ['2022-06', 235],
               ['2022-07', 449],
               ['2022-08', 484],
               ['2022-09', 270],
               ['2022-10', 167],
               ['2022-11', 168],
               ['2022-12', 112],
               ['2023-01', 23],
               ['2023-02', 79],
               ['2023-03', 157],
               ['2023-04', 139],
               ['2023-05', 123],
               ['2023-06', 121]
           ]
    # 按第三维度钻取
    drill_filter = ParseDict(
        {"dimension": "创建时间（月）", "value": "2022-05"},
        ReportPanel.DrillDownStatus.Filters()
    )
    drill_down_status.drill_filters.append(drill_filter)
    assert dataset_to_echarts_source(dataset, drill_down_status=drill_down_status, values=["数量"]) == [
        ['创建人', '数量'],
        ['达芙妮亿俏专卖店:售后5', 11],
        ['达芙妮亿俏专卖店:梦之', 49]
    ]


def test_echarts_percent():
    import pandas as pd
    from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimension
    df = pd.DataFrame({
        "创建时间（月）": ["2022-05", "2022-06", "2022-07"],
        "数量": [60, 235, 449],
        "占比": [0.08064516129032258, 0.31586021505376344, 0.603494623655914]
    })
    dimension = DataDimension(title="占比")
    assert dataset_to_echarts_source(df, values=["数量", "占比"], percent_rules=[dimension]) == [
        ['创建时间（月）', '数量', '占比'],
        ['2022-05', 60, '8.06%'],
        ['2022-06', 235, '31.59%'],
        ['2022-07', 449, '60.35%']
    ]
