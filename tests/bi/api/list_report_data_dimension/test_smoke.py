from leyan_proto.digismart.robot.bi import report_pb2
from leyan_proto.digismart.robot_web import bi_pb2
from pytest import mark, fixture

from robot_processor.bi.dashboard import ReportPanel
from robot_processor.utils import message_to_dict
from robot_processor.web_grpc_services.bi import BiService

bi_service = BiService()


@fixture
def mock_report_panel(testbed, db, mock_dashboard):
    panel = ReportPanel()
    panel.name = "ut"
    panel.dashboard_id = mock_dashboard.id
    panel.data_source = report_pb2.ReportDataSource(form_ids=[testbed.form.id])
    db.session.add(panel)
    db.session.commit()
    return panel


@fixture
def testbed(client):
    from tests.testbed import FormBuilder, types

    resource = "bi.default"
    testbed_builder = FormBuilder(resource, client.shop)
    testbed_builder.build()

    yield types.FormTestbed(shop=client.shop, form=testbed_builder.form)


@mark.smoke
def test_list_report_data_dimension(mock_grpc_context, mock_report_panel):
    request = bi_pb2.GetReportPanelRequest()
    request.panel_id.value = mock_report_panel.id
    response = bi_service.ListReportDataDimension(request, mock_grpc_context)

    mock_grpc_context.assert_not_called()
    dimension_list = message_to_dict(response)["dimension_list"]
    assert len(dimension_list) > 0
