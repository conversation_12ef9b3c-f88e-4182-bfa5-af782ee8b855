from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimensionDisplay
from leyan_proto.digismart.robot_web import bi_pb2
from pytest import mark

from robot_processor.bi.generate_schema.dimension.system import BO_ID
from robot_processor.utils import message_to_dict
from robot_processor.web_grpc_services.bi import BiService

bi_service = BiService()


@mark.smoke
def test_generate_report_dataset(mock_grpc_context, client, db):
    # 设计模式
    request = bi_pb2.GenerateReportDatasetRequest()
    request.data_source.form_ids.append(604652)
    request.generate_schema.dimensions.append(DataDimensionDisplay(dimension=BO_ID))
    response = bi_service.GenerateReportDataset(request, mock_grpc_context)

    mock_grpc_context.assert_not_called()
    dataset = message_to_dict(response.dataset)
    # 有数据
    assert len(dataset["source"]) > 1

    # SQL 模式
    request = bi_pb2.GenerateReportDatasetRequest()
    request.sql_schema.raw_sql = (
        "select count(*) as 工单数量 from transformed_business_order"
    )
    response = bi_service.GenerateReportDataset(request, mock_grpc_context)
    dataset = message_to_dict(response.dataset)
    assert dataset["source"][0][0] == "工单数量"
    assert int(dataset["source"][1][0]) > 0
