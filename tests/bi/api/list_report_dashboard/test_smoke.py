from google.protobuf.empty_pb2 import Empty
from more_itertools import first
from pytest import mark

from robot_processor.web_grpc_services.bi import BiService

bi_service = BiService()


@mark.smoke
def test_list_report_dashboard(mock_grpc_context, mock_dashboard):
    request = Empty()
    response = bi_service.ListReportDashboard(request, mock_grpc_context)

    mock_grpc_context.assert_not_called()

    assert len(response.dashboard_list) > 0
    assert first(response.dashboard_list).id.value == mock_dashboard.id
