from leyan_proto.digismart.robot_web import bi_pb2
from pytest import mark

from robot_processor.bi.dashboard import ReportPanel
from robot_processor.web_grpc_services.bi import BiService

bi_service = BiService()


@mark.smoke
def test_create_report_panel(mock_grpc_context, mock_dashboard):
    request = bi_pb2.PutReportPanelRequest()
    request.dashboard_id.value = mock_dashboard.id
    response = bi_service.CreateReportPanel(request, mock_grpc_context)

    mock_grpc_context.assert_not_called()
    assert ReportPanel.query.get(response.panel.id.value) is not None
