from pytest import fixture


@fixture(scope="session", autouse=True)
def init_database():
    """
    为单元测试初始化数据库，数据库和 robot 的数据库是独立的，并且提前准备了测试用的数据
    数据库的地址由配置 `BI_DATABASE_CONNECTION_URI` 提供
    数据库的测试数据在 `tests.bi.resource` 中维护

    """
    import os
    import pandas as pd
    import requests
    from pkg_resources import resource_stream
    from sqlalchemy.future import Engine, create_engine
    from sqlalchemy.exc import OperationalError
    from robot_processor.app import app  # noqa
    from robot_processor.bi.database import ReportBusinessOrder, Session
    from robot_processor.bi.database.models import Base

    test_db_url = os.environ.get('TEST_DB_URL', 'http://test-db/db')
    # 初始化数据库
    engine: Engine = Session.kw["bind"]
    try:
        Base.metadata.drop_all(bind=engine)
        use_testdb = False
        res = {}
    except OperationalError:  # localhost 数据库不可用，推测在 ci/cd 环境下
        res = requests.post(test_db_url, json={'engine': 'mysql-5.7'}).json()
        url = f"mysql+pymysql://{res['user']}:{res['password']}@{res['host']}:{res['port']}/{res['name']}?charset=utf8mb4&autocommit=true"  # noqa
        engine = create_engine(url)
        Session.configure(bind=engine)
        Base.metadata.drop_all(bind=engine)
        use_testdb = True

    Base.metadata.create_all(bind=engine)

    # 准备测试数据
    resource_package = "tests.bi.resource"
    with resource_stream(resource_package, "质量问题测试数据.csv") as stream:
        df = pd.read_csv(stream)
        df.replace({"(null)": None}, inplace=True)
        with Session() as session, session.begin():
            df.to_sql(
                ReportBusinessOrder.__tablename__,
                engine,
                index=False,
                if_exists="replace",
                index_label="id",
            )

    yield
    if use_testdb:
        requests.delete(f"{test_db_url}?id={res['id']}").raise_for_status()
