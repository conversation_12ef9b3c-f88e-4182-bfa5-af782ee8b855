from base64 import b64encode
from io import Bytes<PERSON>
from unittest.mock import <PERSON>M<PERSON>

from flask import current_app
from pytest import fixture, mark
from werkzeug.datastructures import FileStorage


class TestOssUploadFileApi:
    api_path = "/service/files"

    @property
    def mock_file(self):
        return FileStorage(
            stream=BytesIO(b"unittest"), filename="ut.bin", content_type="text/plain"
        )

    @property
    def mock_file_base64(self):
        return b64encode(b"unittest").decode()

    @fixture(autouse=True)
    def mock_oss(self, mocker):
        from external.oss import oss_client

        oss_result = MagicMock()
        oss_result.status = 200
        mocker.patch.object(oss_client.bucket, "put_object", return_value=oss_result)
        yield

    @fixture(autouse=True)
    def setup_context(self):
        current_app.config["OSS_BIZ_BASE_URL"] = "http://ut/"
        yield

    @fixture
    def setup_upload_with_public_policy(self, client):
        current_app.config["OSS_BIZ_POLICY"] = {
            "ut": {"bucket": "ut",
                   "auth_type": "shop",
                   "object_key_type": "business-order-data",
                   "acl": "public-read"}
        }
        yield

    @mark.order(1)
    @mark.usefixtures("setup_upload_with_public_policy")
    def test_upload_with_public_policy(self, client):
        """公开策略上传的文件，会返回文件的 uri"""
        response = client.post(
            self.api_path, query_string={"biz_key": "ut"}, data={"file": self.mock_file}
        )

        assert response.status_code == 200
        assert response.json["url"] == (
            "http://ut/org/{}/store/{}/16802231b09f155b7a42a5dcaba33a74_ut.bin".format(
                client.org_id, client.sid
            )
        )

    @mark.order(1)
    @mark.usefixtures("setup_upload_with_public_policy")
    def test_upload_with_json_format(self, client):
        """使用 json 方式上传文件，文件通过 base64 编码"""
        response = client.post(
            self.api_path,
            query_string={"biz_key": "ut", "format": "json"},
            json={"file": self.mock_file_base64},
        )
        assert response.status_code == 200
        assert response.json["url"] == (
            "http://ut/org/{}/store/{}/16802231b09f155b7a42a5dcaba33a74__.png".format(
                client.org_id, client.sid
            )
        )
