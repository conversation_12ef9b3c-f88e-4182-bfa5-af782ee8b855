from sqlalchemy.orm.attributes import flag_modified

from robot_processor.ext import db
from robot_processor.business_order.condition.condition import ConditionGroup
from robot_processor.business_order.job_wrappers.wrapper import get_job_executor
from robot_processor.business_order.models import BusinessOrder, Job
from robot_processor.job.job_model_wrapper import JobArguments


def test_branch(mocker, shop_factory, mock_business_order: BusinessOrder, mock_job, mock_step, mock_form):
    shop = shop_factory.create(platform='TMALL')
    mock_step.form_id = mock_form.id
    mock_form.subscribe(shop, True)
    mock_job.id = 3
    mock_job.step_id = mock_step.id
    mock_job.step_uuid = mock_step.step_uuid
    mock_job.business_order_id = mock_business_order.id
    mock_business_order.sid = shop.sid
    mock_business_order.job_history = [1, 2, 3]
    mock_business_order.job_road = [[1, 2, 3, -1, 4], [1, 2, 3, -1, 5]]
    mock_business_order.data["f504c40e-1f8f-47e3-afbe-4396cbb4841d"] = "name1"
    branch = [
        {
            "name": "分支4.1",
            "next": "4b0eecf1d7934efe946424d142296a64",
            "enable": True,
            "type": "NORMAL",
            "order": 5,
            "condition_group": {
                "relation": "AND",
                "condition_list": [
                    {
                        "data": {
                            "ref": "f504c40e-1f8f-47e3-afbe-4396cbb4841d",
                            "value": [
                                "name1",
                                "name2"
                            ],
                            "operator": "IN",
                            "ref_type": "product",
                            "value_type": "STRING"
                        },
                        "type": "single"
                    }
                ]
            }
        },
        {
            "name": "分支5.1",
            "next": "1289cdc38c4f496e86bbc8851b501c85",
            "enable": True,
            "type": "NORMAL",
            "order": 2,
            "condition_group": {
                "relation": "AND",
                "condition_list": [
                    {
                        "data": {
                            "ref": "f504c40e-1f8f-47e3-afbe-4396cbb4841d",
                            "value": [
                                "name3",
                                "name4"
                            ],
                            "operator": "IN",
                            "ref_type": "product",
                            "value_type": "STRING"
                        },
                        "type": "single"
                    }
                ]
            }
        }
    ]
    mock_step.branch = branch
    mock_step.step_type = 3
    flag_modified(mock_business_order, "data")
    flag_modified(mock_business_order, "job_history")
    job4 = Job(id=4, business_order_id=mock_business_order.id,
               step_uuid="4b0eecf1d7934efe946424d142296a64")
    job5 = Job(id=5, business_order_id=mock_business_order.id,
               step_uuid="1289cdc38c4f496e86bbc8851b501c85")
    db.session.add(job4)
    db.session.add(job5)
    db.session.commit()
    gw = get_job_executor(mock_job.id)
    assert gw is not None
    gw.run_job()


def test_conditions_with_time_widget(mock_form_version, shop_factory):
    shop = shop_factory.create(platform='TMALL')
    bo = BusinessOrder()
    bo.shop = shop
    bo.form_version = mock_form_version
    bo.data = {
        "f504c40e-1f8f-47e3-afbe-4396cbb4841d": [
            {
                "f3d2e11a-4e57-4349-a1fc-01b238f4b078": "12:30:00"
            }
        ],
        "f0273ca1-d7ee-4b11-8b89-e7eab8b35714": "bb"
    }
    job = Job()
    job.business_order = bo
    job._raw_ui_schema = {
        "ui_schema": [
            {
                "key": "f504c40e-1f8f-47e3-afbe-4396cbb4841d",
                "type": "table",
                "option_value": {
                    "fields": [
                        {
                            "key": "f3d2e11a-4e57-4349-a1fc-01b238f4b078",
                            "type": "time"
                        }
                    ]
                },
                "data_schema": {
                    "multi_row": True
                }
            },
            {
                "key": "f0273ca1-d7ee-4b11-8b89-e7eab8b35714",
                "type": "string"
            }
        ]
    }
    condition_group = {
        "relation": "AND",
        "condition_list": [
            {
                "data": {
                    "ref": {
                        "type": "table",
                        "key": "f504c40e-1f8f-47e3-afbe-4396cbb4841d",
                        "field": "f3d2e11a-4e57-4349-a1fc-01b238f4b078",
                        "multi_row": False
                    },
                    "value": [
                        "12:00:00",
                        "13:00:00"
                    ],
                    "operator": "TIME_BETWEEN",
                    "ref_type": "table",
                    "value_type": "time"
                },
                "type": "single"
            },
            {
                "data": {
                    "ref": "f0273ca1-d7ee-4b11-8b89-e7eab8b35714",
                    "value": [
                        "aa",
                        "bb",
                    ],
                    "operator": "MATCH_ANY",
                    "ref_type": "string",
                    "value_type": "STRING"
                },
                "type": "single"
            }
        ]
    }

    assert ConditionGroup.build_condition_group(
        JobArguments.extract_from_job_orm(job),
        condition_group
    ).exec()
