from unittest.mock import PropertyMock

import pytest
import requests
from oss2.http import Response
from oss2.models import PutObjectResult
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.business_order.models import BusinessOrder, BusinessOrderStatus
from robot_processor.business_order.tasks import replace_file_with_oss
from robot_processor.enums import StepType
from robot_processor.ext import db


class TestUpload:
    @pytest.fixture
    def mock_data(self, client, mocker, step_factory, mock_form, job_factory, business_order_factory):
        def create(ui_schema, bo_data):
            mock_form.shop = client.shop
            bo = business_order_factory.create(form_id=mock_form.id, status=BusinessOrderStatus.RUNNING, sid=client.sid,
                                               deleted=False, data=bo_data)
            step = step_factory(step_type=StepType.human, form_id=mock_form.id)
            step.update_raw_step()
            step.raw_step["ui_schema"].extend(ui_schema)
            flag_modified(step, "raw_step")

            job = job_factory.create(step_id=step.id, business_order_id=bo.id,
                                     step_uuid=step.step_uuid)
            requests_response = requests.Response()
            requests_response.status_code = 200
            mocker.patch(
                "oss2.Bucket.put_object",
                return_value=PutObjectResult(Response(requests_response))
            )
            mocker.patch(
                "requests.get", return_value=requests_response
            )
            db.session.flush()
            bo.jobs.append(job)
            bo.current_job_id = job.id
            db.session.commit()
            return bo
        return create

    def test_bo_replace_image(self, mocker, mock_data):
        data = {
            "test_image": [{
                "url": "https://i.alicdn.com/taobao.jpg",
                "fileName": "taobao",
            }]
        }
        ui_schema = [
            {
                "key": "test_image",
                "type": "upload",
                "uploadType": "image",
            }
        ]
        bo = mock_data(ui_schema, data)

        results = BusinessManager.replace_upload_widget(bo)
        mocker.patch(
            "requests.Response.content", new_callable=PropertyMock,
            return_value=b'012345JFIF'
        )
        replace_file_with_oss(bo.id, results)
        bo = BusinessOrder.query.get(bo.id)
        assert bo.data['test_image'][0]['url'].startswith('https://test.oss.com')

    def test_bo_replace_video(self, mocker, client, mock_data):
        ui_schema = [
            {
                "key": "test_video",
                "type": "upload",
                "uploadType": "video",
            }
        ]
        data = {
            "test_video": [{
                "url": "https://v.alicdn.com/taobao.wmv",
                "fileName": "taobao.wmv",
            }]
        }
        bo = mock_data(ui_schema, data)
        results = BusinessManager.replace_upload_widget(bo)
        mocker.patch(
            "requests.Response.content", new_callable=PropertyMock,
            return_value=b'test'
        )
        replace_file_with_oss(bo.id, results)
        bo = BusinessOrder.query.get(bo.id)
        assert bo.data['test_video'][0]['url'].startswith('https://test.oss.com')
