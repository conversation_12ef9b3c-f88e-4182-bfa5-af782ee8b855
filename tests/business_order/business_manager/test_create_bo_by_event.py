from uuid import uuid4

from pytest import fixture
from pytest import mark

widget_keymap = {
    "售后单号": str(uuid4()),
    "售后状态": str(uuid4()),
    "售后类型": str(uuid4()),
    "售后原因": str(uuid4()),
    "退款金额": str(uuid4()),
    "实付金额": str(uuid4()),
    "实付金额与退款金额差值": str(uuid4()),
    "订单编号": str(uuid4()),
    "退货商品": str(uuid4()),
}


@fixture
def setup(
    client,
    mock_form,
    step_factory,
    widget_info_factory,
    widget_collection_factory,
    widget,
    setup_event_config,
):
    from robot_processor.db import db
    from robot_processor.enums import StepType
    from robot_processor.form.event.models import EventType
    from robot_processor.form.models import FormMold

    mock_form.form_mold = FormMold.EVENT
    begin_step = step_factory.create(step_type=StepType.begin, form_id=mock_form.id)
    event_step = step_factory.create(
        form_id=mock_form.id,
        prev_step_ids=[begin_step.step_uuid],
        step_type=StepType.event,
        event=EventType.PDD_REFUND,
        is_dirty=True,
    )
    begin_step.next_step_ids = [event_step.step_uuid]
    mock_form.subscribe(client.shop, True)

    widget_collection = widget_collection_factory.create()
    event_step.widget_collection_id = widget_collection.id
    widget_info_factory.create(
        widget_id=widget("单行输入").id,
        widget_collection_id=widget_collection.id,
        key=widget_keymap["售后单号"],
        option_value=dict(label="售后单号"),
    )
    widget_info_factory.create(
        widget_id=widget("单行输入").id,
        widget_collection_id=widget_collection.id,
        key=widget_keymap["售后状态"],
        option_value=dict(label="售后状态"),
    )
    widget_info_factory.create(
        widget_id=widget("单行输入").id,
        widget_collection_id=widget_collection.id,
        key=widget_keymap["售后类型"],
        option_value=dict(label="售后类型"),
    )
    widget_info_factory.create(
        widget_id=widget("单行输入").id,
        widget_collection_id=widget_collection.id,
        key=widget_keymap["售后原因"],
        option_value=dict(label="售后原因"),
    )
    widget_info_factory.create(
        widget_id=widget("数字输入").id,
        widget_collection_id=widget_collection.id,
        key=widget_keymap["退款金额"],
        option_value=dict(label="退款金额"),
    )
    widget_info_factory.create(
        widget_id=widget("数字输入").id,
        widget_collection_id=widget_collection.id,
        key=widget_keymap["实付金额"],
        option_value=dict(label="实付金额"),
    )
    widget_info_factory.create(
        widget_id=widget("数字输入").id,
        widget_collection_id=widget_collection.id,
        key=widget_keymap["实付金额与退款金额差值"],
        option_value=dict(label="实付金额与退款金额差值"),
    )
    widget_info_factory.create(
        widget_id=widget("单行输入").id,
        widget_collection_id=widget_collection.id,
        key=widget_keymap["订单编号"],
        option_value=dict(label="订单编号"),
    )
    widget_info_factory.create(
        widget_id=widget("商品").id,
        widget_collection_id=widget_collection.id,
        key=widget_keymap["退货商品"],
        option_value=dict(label="退货商品"),
    )
    db.session.commit()
    event_step.compatible_set_symbols_old(widget_collection.id)
    mock_form.wraps(client.shop).publish()
    mock_form.wraps(client.shop).trigger_on_publish("ut")

    yield {
        "form": mock_form,
        "step": event_step,
        "shop": client.shop,
    }


@mark.usefixtures("mock_action_client")
def test_pdd_refund(setup):
    from robot_processor.business_order.business_order_manager import BusinessManager
    from robot_processor.db import db
    from robot_processor.form.event.models import EventConfig
    from robot_processor.form.event.models import EventType
    from robot_processor.form.event.pdd_refund import PddRefundInfo

    event = EventConfig.get_by_id(EventType.PDD_REFUND)
    data = PddRefundInfo(
        refund_id=1,
        after_sales_status=2,
        after_sales_status_zh="买家申请退款，待商家处理",
        after_sales_type=2,
        after_sales_type_zh="仅退款",
        refund_remark="",
        after_sale_reason="商品质量问题",
        refund_created_time="2021-09-10 00:00:00",
        refund_amount=100,
        refund_operator_role=1,
        refund_operator_role_zh="用户",
        refund_shipping_name=None,
        refund_tracking_number=None,
        goods_id="3667632512403898551",
        goods_name="【测试商品】测试商品",
        goods_number=1,
        goods_price=100,
        out_goods_id="OUT GOODS ID",
        outer_id="OUTER ID",
        sku_id="SKU ID",
        order_sn="111111111111111111111",
        pay_amount=109.99,
        shipping_status=1,
        shipping_status_zh="已发货",
        logistics_id="shunfeng",
        logistics_name="顺丰",
        logistics_no="SF123456",
        pay_and_refund_amount_diff=9.99,
        fs_trade_no=[{"tid": "111111111111111111111"}],
        fs_product_list=[
            {
                "TID": "111111111111111111111",
                "OID": None,
                "PICTURE": "https://img.pddpic.com/goods/images/2021-09-10/3f6b4b7d-6e8e-4c1e-9b5c-1b3f1b4b0c6d.jpg",
                "TITLE": "【测试商品】测试商品",
                "DESCRIPTION": "",
                "SPU": "3667632512403898551",
                "SKU": "SKU ID",
                "SPU_OUTER": "OUT GOODS ID",
                "SKU_OUTER": "OUTER ID",
                "PRICE": 100,
                "INVENTORY": None,
                "PAYMENT": None,
                "COUNT": 1,
                "SHORT_TITLE": "",
                "COMBINE": "SINGLE",
            }
        ],
    ).dict()

    created = BusinessManager.create_bo_by_event(event, setup["shop"], data)
    assert len(created) == 1 and created[0].is_ok()
    bo = created[0].unwrap()
    assert bo.data[widget_keymap["售后单号"]] == "1"
    assert bo.data[widget_keymap["售后状态"]] == "买家申请退款，待商家处理"
    assert bo.data[widget_keymap["售后类型"]] == "仅退款"
    assert bo.data[widget_keymap["售后原因"]] == "商品质量问题"
    assert bo.data[widget_keymap["退款金额"]] == 100
    assert bo.data[widget_keymap["实付金额"]] == 109.99
    assert bo.data[widget_keymap["实付金额与退款金额差值"]] == 9.99
    assert bo.data[widget_keymap["退货商品"]] == [
        {
            "COMBINE": "SINGLE",
            "TID": "111111111111111111111",
            "OID": None,
            "PICTURE": "https://img.pddpic.com/goods/images/2021-09-10/3f6b4b7d-6e8e-4c1e-9b5c-1b3f1b4b0c6d.jpg",
            "TITLE": "【测试商品】测试商品",
            "DESCRIPTION": "",
            "SPU": "3667632512403898551",
            "SKU": "SKU ID",
            "SKU_NAME": None,
            "SPU_OUTER": "OUT GOODS ID",
            "SKU_OUTER": "OUTER ID",
            "PRICE": 100.0,
            "INVENTORY": None,
            "PAYMENT": None,
            "COUNT": 1,
            "SHORT_TITLE": "",
        }
    ]

    # shortcut
    setup["step"].event_shortcut = "REFUND_SHIPPED"
    db.session.commit()
    created = BusinessManager.create_bo_by_event(event, setup["shop"], data)
    assert len(created) == 1 and created[0].is_ok()

    setup["step"].event_shortcut = "REFUND_WITH_RETURN_SHIPPED"
    db.session.commit()
    created = BusinessManager.create_bo_by_event(event, setup["shop"], data)
    assert len(created) == 0, "预期被 shortcut 拦截"
