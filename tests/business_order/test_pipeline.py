#  Copyright 2023 Leyantech Ltd. All Rights Reserved.
"""
测试工单的流转 不关心具体的RPA类型
"""
import random

import pytest
from sqlalchemy import func

from robot_processor.business_order.models import Job
from robot_processor.business_order.tasks import execute_job
from robot_processor.enums import (
    JobStatus,
    StepType,
    AssigneeRule,
    BusinessOrderStatus,
    JobType,
)
from robot_processor.ext import db

# 这里的 task 类型是随意选取的，不影响测试结果
test_task_type = JobType.ALIPAY


@pytest.fixture(autouse=True)
def setup_test_job_executor(mocker):
    from robot_processor.job.alipay import AlipayExecutor
    mocker.patch.object(AlipayExecutor, 'process', return_value=(JobStatus.SUCCEED, None))


@pytest.fixture
def mock_human_step(mock_form, human_step_factory):
    def do_create(**kwargs):
        return human_step_factory.create(
            form_id=mock_form.id, assignee_rule=AssigneeRule.RANDOM,
            assistants_v2={
                "select_type": 2,
                "channle_accounts": [
                    {"user_id": 3333, "user_type": 4}
                ]
            }, **kwargs
        )
    return do_create


@pytest.fixture
def mock_auto_step(mock_form, auto_step_factory, rpa):
    def do_create(**kwargs):
        task = rpa(task=test_task_type, org_id=mock_form.get_org_id())
        return auto_step_factory.create(form_id=mock_form.id, data={"rpa_id": task.id}, **kwargs)
    return do_create


@pytest.fixture
def mock_gateway_step(mock_form, exclusive_step_factory):
    def do_create(**kwargs):
        return exclusive_step_factory.create(form_id=mock_form.id, **kwargs)
    return do_create


@pytest.fixture
def bo_without_gateway(client, mock_form, business_order_factory, mock_human_step, mock_auto_step):
    def do_create(step_count=2):
        mock_form.subscribe(client.shop, True)
        begin = mock_human_step()
        prev = begin
        for i in range(step_count):
            step_type = random.choice([StepType.human, StepType.auto])
            if step_type == StepType.human:
                cur = mock_human_step()
            else:
                cur = mock_auto_step()
            prev.next_step_ids = [cur.step_uuid]
            cur.prev_step_ids = [prev.step_uuid]
            prev = cur
        version = mock_form.snapshot()
        bo = business_order_factory.create(
            form_id=mock_form.id,
            sid=client.shop.sid,
            creator_user_id=client.assistant.user_id,
            creator_type=client.assistant.user_type,
            data={}, form_version_id=version.id)
        bo.init_bo_jobs()
        db.session.commit()
        return bo
    return do_create


def test_single_run_without_gateway(bo_without_gateway):
    bo = bo_without_gateway(step_count=0)
    cur_job = bo.job_history[0]
    execute_job(bo.job_history[0])
    assert cur_job == bo.current_job_id
    execute_job(bo.job_history[0], chain=False)

    assert bo.current_job.is_success()
    assert bo.is_completed()


def test_chain_run_without_gateway(bo_without_gateway):
    # 自动步骤和人工步骤的随意组合 不包含多分支
    bo = bo_without_gateway()
    execute_job(bo.job_history[0], chain=True)
    job_history = bo.job_history
    jobs = Job.query.filter(Job.id.in_(job_history[1:])).order_by(func.field(Job.id, *job_history[1:])).all()
    for job in jobs:
        if job.is_human() or job.is_auto():
            # 人工步骤分派后或者自动任务回重新放回队列 在本次pipeline中会停在这类job上
            break_point = job
            assert bo.current_job_id == break_point.id
            assert not bo.current_job.is_success()
            break
    else:
        assert bo.current_job.is_success()


def test_chain_run_with_closed_business_order(bo_without_gateway):
    bo = bo_without_gateway()
    bo.status = BusinessOrderStatus.CLOSE
    current_job = bo.current_job_id
    execute_job(bo.job_history[0], chain=True)
    assert bo.current_job_id == current_job


def test_chain_run_with_gateway(client, mock_form,
                                mock_human_step, mock_auto_step, mock_gateway_step, mock_action_client,
                                mock_buyer_server_get_buyer_nick_by_open_uid, business_order_factory):
    #          / 3  \
    #     1 - 2      5
    #          \ 4 /
    # 自动步骤和人工步骤的随意组合 不包含多分支
    mock_form.subscribe(client.shop, True)
    mock_form.steps.delete()
    begin = mock_human_step()
    gateway = mock_gateway_step()
    branch1, branch2 = mock_auto_step(), mock_auto_step()
    end = mock_auto_step()
    gateway.branch = [
        {
            "id": "576aec94-2863-4fe0-a8b4-93a96d2f376f",
            "name": "分支-1",
            "next": branch1.step_uuid,
            "type": "NORMAL",
            "order": 1,
            "enable": True,
            "condition_group": {
                "relation": "AND",
                "condition_list": [
                    {
                        "data": {
                            "ref": "test_widget",
                            "value": ["AAA"],
                            "operator": "MATCH_ANY",
                            "ref_type": "string",
                            "value_type": "STRING"
                        },
                        "type": "single"
                    }
                ]
            }
        }, {
            "id": "fc62a804-1674-4cb2-8b33-f76a7395c4c3",
            "name": "默认分支",
            "next": branch2.step_uuid,
            "type": "DEFAULT",
            "order": 9999,
            "enable": True
        }
    ]
    begin.next_step_ids = [gateway.step_uuid]
    gateway.prev_step_ids = [begin.step_uuid]
    gateway.next_step_ids = [branch1.step_uuid, branch2.step_uuid]
    branch1.prev_step_ids = [gateway.step_uuid]
    branch1.next_step_ids = [end.step_uuid]
    branch2.prev_step_ids = [gateway.step_uuid]
    branch2.next_step_ids = [end.step_uuid]
    end.prev_step_ids = [branch1.step_uuid, branch2.step_uuid]
    end.next_step_ids = []
    version = mock_form.snapshot()
    bo = business_order_factory.create(form_version_id=version.id,
                                       form_id=mock_form.id,
                                       sid=client.shop.sid,
                                       creator_user_id=client.assistant.user_id,
                                       creator_type=client.assistant.user_type,
                                       data={"test_widget": "AAA"})
    job = bo.init_bo_jobs()
    assert job is not None
    db.session.commit()
    execute_job(job.id, chain=True)
    assert bo.current_job.step_id == branch1.id  # 走入分支1
    execute_job(bo.current_job_id, chain=True)
    assert bo.current_job.step_id == end.id
    execute_job(bo.current_job_id, chain=True)
    assert bo.is_completed()
