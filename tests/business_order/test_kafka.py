from robot_processor.business_order.kafka import update_assignee
from robot_processor.business_order.tasks import handle_assignee_status
from robot_processor.enums import BusinessOrderStatus, JobStatus, Creator


class TestKafkaHandler:
    def test_assignee_invalid(self, client, mocker, business_order_factory, mock_form_with_step):
        mocker.patch("robot_processor.business_order.models.Job.job_name")
        mocker.patch("robot_processor.business_order.models.Job.task_type")

        form = mock_form_with_step.forms[0]
        bos = business_order_factory.create_batch(
            3, form_id=form.id, sid=client.sid, status=BusinessOrderStatus.RUNNING,
            form_version_id=form.versions.first().id)
        for bo in bos:
            bo.init_bo_jobs()
        job = bos[0].all_jobs()[0]
        bos[0].current_job_id = job.id
        job.status = JobStatus.PENDING
        job.assignee_user_id = client.assistant.user_id
        job.assignee_type = Creator(client.assistant.user_type)
        record = {
            "shop_assignees": [

                {
                    "platform": "TAOBAO",
                    "sid": client.shop.sid,
                    "action": "DELETE",
                    "leyan_account_id": 1,
                    "channel_account_ids": [client.assistant.user_id],
                }
            ]
        }
        results = update_assignee(None, record)

        for sid, action, users in results:
            handle_assignee_status(sid, action, users)

        record = {
            "shop_assignees": [
                {
                    "platform": "TAOBAO",
                    "sid": client.shop.sid,
                    "action": "ENABLE",
                    "leyan_account_id": 1,
                    "channel_account_ids": [client.assistant.user_id],
                }
            ]
        }
        results = update_assignee(None, record)

        for sid, action, users in results:
            handle_assignee_status(sid, action, users)
