#  Copyright 2023 Leyantech Ltd. All Rights Reserved.
import random

from robot_processor.ext import db


def test_get_bo_version(client, mock_business_orders):
    """测试获取工单实例的版本"""
    bo = random.choice(mock_business_orders.business_orders)
    assert bo.form_version_id, "工单实例绑定了版本"
    response = client.get(f"/v1/business_orders/{bo.id}/version")
    assert response.json["succeed"]

    assert set(response.json["data"]["step_id"]) == {
        step.id for step in bo.form.job_steps
    }, "预期的步骤ID为工单已发布的步骤"


def test_bo_version_default(client, mock_business_orders):
    """测试工单实例没有版本时，根据工单创建一个空的工单版本"""
    bo = random.choice(mock_business_orders.business_orders)
    # 清空工单的版本绑定关系，模拟一个没有版本的工单
    bo.form_version_id = None
    db.session.commit()

    response = client.get(f"/v1/business_orders/{bo.id}/version")
    assert response.json["succeed"]
    assert set(response.json["data"]["step_id"]) == {
        job.step_id for job in bo.jobs
    }, "因为没有绑定工单模板版本，预期根据工单已创建的步骤来生成一个空的工单版本"
    assert response.json["data"]["version_descriptor"] == ""
