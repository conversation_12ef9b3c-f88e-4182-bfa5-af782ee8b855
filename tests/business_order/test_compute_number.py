import json

from pytest import fixture

from robot_processor.enums import StepType
from robot_processor.business_order.utils.compute_number import WidgetTitleItem, WidgetTitle, \
    WidgetName, WidgetDataType, WidgetType, get_number_widget_values
from robot_processor.form.models import Form, Step, Widget, WidgetInfo, WidgetCollection
from robot_processor.business_order.models import BusinessOrder


@fixture
def mock_data(
    client, form_factory, step_factory, business_order_factory,
    widget_factory, widget_info_factory, widget_collection_factory
):
    widget_1: Widget = widget_factory.create(
        category="BASIC",
        schema=json.loads('{"icon": "ficon-number-input", "type": "number", "label": "数值", "unique": false, "description": "数字输入，规定填写者输入内容为数字，可进行数值校验", "widget_type": "number", "fieldDescriptors": {"type": "number"}}'),  # noqa
        options=json.loads('{"max": {"type": "number", "title": "最大值", "x-index": 7, "x-component": "NumberPicker", "x-decorator": "FormItem", "x-reactions": {"fulfill": {"schema": {"x-hidden": "{{$deps[0]}}"}}, "dependencies": ["isCalculate"]}, "x-component-props": {"placeholder": "最大值"}}, "min": {"type": "number", "title": "最小值", "x-index": 6, "x-component": "NumberPicker", "x-decorator": "FormItem", "x-reactions": {"fulfill": {"schema": {"x-hidden": "{{$deps[0]}}"}}, "dependencies": ["isCalculate"]}, "x-component-props": {"placeholder": "请设置最小值"}, "x-decorator-props": {"tooltip": "注意：当数字组件用于支付宝批量打款时，平台限制最小金额为1元"}}, "unit": {"type": "string", "title": "单位符", "x-index": 3, "x-component": "UnitPicker", "x-decorator": "FormItem", "x-component-props": {"units": ["元", "%", "mm", "cm", "m", "英寸", "斤", "公斤", "个", "件", "套"], "placeholder": "请选择单位符"}}, "label": {"type": "string", "title": "标题", "x-index": 1, "x-component": "Input", "x-decorator": "FormItem", "x-validator": [{"required": true}, {"max": 20}], "x-component-props": {"placeholder": "请设置标题"}}, "required": {"enum": [{"label": "必填", "value": true}, {"label": "非必填", "value": false}], "type": "boolean", "title": "是否必填", "isData": true, "default": false, "x-index": 9, "x-component": "RadioGroup", "x-decorator": "FormItem", "x-reactions": {"fulfill": {"state": {"value": false}, "schema": {"x-hidden": "{{$deps[0]}}"}}, "dependencies": ["isCalculate"]}}, "checkType": {"enum": [{"label": "仅重复提示", "value": 1}, {"label": "重复提示且不允许继续创建工单", "value": 2}], "type": "string", "title": "校验方式", "default": 1, "x-index": 12, "x-component": "RadioGroup", "x-decorator": "FormItem", "x-reactions": {"fulfill": {"state": {"visible": "{{$deps[0]}}"}}, "dependencies": ["valueUnique"]}}, "expression": {"type": "string", "title": "计算公式", "x-index": 10, "x-component": "CalculateNumber", "x-decorator": "FormItem", "x-reactions": {"fulfill": {"schema": {"x-visible": "{{$deps[0]}}"}}, "dependencies": ["isCalculate"]}, "x-component-props": "{{getCalculateNumberProps()}}"}, "isCalculate": {"enum": [{"label": "是", "value": true}, {"label": "否", "value": false}], "type": "boolean", "title": "是否计算数值", "default": false, "x-index": 4, "x-component": "RadioGroup", "x-decorator": "FormItem"}, "layoutWidth": {"type": "number", "title": "组件列宽占比(%)", "default": 1, "x-index": 13, "x-component": "RadioGroup", "x-decorator": "FormItem", "x-validator": [{"required": false}], "x-component-props": {"options": [{"label": "25", "value": 0.25}, {"label": "50", "value": 0.5}, {"label": "75", "value": 0.75}, {"label": "100", "value": 1}], "optionType": "button"}}, "placeholder": {"type": "string", "title": "未填写时提示", "x-index": 2, "x-component": "Input", "x-decorator": "FormItem", "x-validator": [{"max": 20}], "x-component-props": {"placeholder": "请设置未填写时提示"}}, "valueUnique": {"type": "boolean", "title": "值唯一", "default": true, "x-index": 11, "x-component": "Switch", "x-decorator": "FormItem", "x-decorator-props": {"tooltip": "开启值唯一校验后，当登记工单时出现该组件填写了重复的值后，将进行下方您选择的校验方式进行校验"}}, "defaultValue": {"type": "number", "title": "默认值", "x-index": 8, "x-component": "NumberPicker", "x-decorator": "FormItem", "x-reactions": {"fulfill": {"schema": {"x-hidden": "{{$deps[3]}}", "x-component-props.max": "{{$deps[2]}}", "x-component-props.min": "{{$deps[1]}}", "x-component-props.precision": "{{$deps[0]}}"}}, "dependencies": ["decimalDigits", "min", "max", "isCalculate"]}, "x-component-props": {"placeholder": "请设置默认值"}}, "decimalDigits": {"type": "number", "title": "小数位数", "default": 0, "x-index": 5, "x-component": "NumberPicker", "x-decorator": "FormItem", "x-reactions": {"fulfill": {"schema": {"x-hidden": "{{$deps[0]}}"}}, "dependencies": ["isCalculate"]}, "x-component-props": {"max": 2, "min": 0, "precision": 0}}}'),  # noqa
        label="数字输入",
        data_schema_template=json.loads('{"fields": [{"name": "<field_id>", "type": "table", "title": "复合组件", "constraints": {"required": false}}], "multi_row": true}	'),  # noqa
    )
    widget_2: Widget = widget_factory.create(
        category="ENHANCE",
        schema=json.loads('{"icon": "ficon-text-input", "type": "table", "label": "复合组件", "description": "复合组件，可以支持用户自定义多列多行数据，类似表单样式", "widget_type": "table", "fieldDescriptors": {"type": "table"}}'),  # noqa
        options=json.loads('{"label": {"type": "string", "title": "标题", "isData": false, "x-index": 1, "x-component": "Input", "x-decorator": "FormItem", "x-validator": [{"required": true}, {"max": 20}], "x-component-props": {"placeholder": "请设置标题"}}, "fields": {"type": "array", "title": "子组件配置", "x-index": 3, "required": true, "x-component": "TableComponentConfig", "x-decorator": "FormItem", "x-reactions": {"fulfill": {"schema": {"x-component-props.widgetName": "{{$deps[0]}}"}}, "dependencies": ["label"]}, "x-validator": [{"array_validator": true}], "x-component-props": {"maxChildren": 20}, "x-decorator-props": {"tooltip": "您可在左侧拖入或下方点击添加可选组件，目前仅支持除评分组件之外的基础组件，最多添加20个"}}, "multiRow": {"enum": [{"label": "允许", "value": true}, {"label": "不允许", "value": false}], "type": "boolean", "title": "是否允许添加多行数据", "default": true, "x-index": 2, "x-component": "RadioGroup", "x-decorator": "FormItem", "x-validator": [{"required": true}]}, "required": {"enum": [{"label": "必填", "value": true}, {"label": "非必填", "value": false}], "type": "boolean", "title": "是否必填", "isData": true, "default": true, "x-index": 7, "x-display": "hidden", "x-component": "RadioGroup", "x-decorator": "FormItem"}, "layoutWidth": {"type": "number", "title": "组件列宽占比(%)", "default": 1, "x-index": 8, "x-component": "RadioGroup", "x-decorator": "FormItem", "x-validator": [{"required": false}], "x-component-props": {"options": [{"label": "25", "value": 0.25}, {"label": "50", "value": 0.5}, {"label": "75", "value": 0.75}, {"label": "100", "value": 1}], "optionType": "button"}}}'),  # noqa
        label="复合组件",
        data_schema_template=json.loads('{"fields": [{"name": "<field_id>", "type": "number", "title": "数字输入", "constraints": {"required": false}}], "multi_row": false}'),  # noqa
    )

    widget_collection_1: WidgetCollection = widget_collection_factory.create()
    widget_collection_2: WidgetCollection = widget_collection_factory.create()

    form: Form = form_factory.create()
    form.subscribe(client.shop, True)
    step_1: Step = step_factory.create(
        form_id=form.id,
        step_type=StepType.begin,
        is_dirty=False,
    )
    step_2: Step = step_factory.create(
        name="步骤名称2",
        form_id=form.id,
        step_type=StepType.human,
        is_dirty=False,
        widget_collection_id=widget_collection_1.id,
    )
    step_3: Step = step_factory.create(
        name="步骤名称3",
        form_id=form.id,
        step_type=StepType.human,
        is_dirty=False,
        widget_collection_id=widget_collection_2.id,
    )

    step_2_widget_1: WidgetInfo = widget_info_factory.create(  # noqa
        widget_collection_id=widget_collection_1.id,
        widget_id=widget_1.id,
        key="960ee6bb-9573-4c3a-bb94-12e2d5afa0e8",
        option_value=json.loads('{"icon": "ficon-number-input", "label": "数值", "required": false, "isCalculate": false, "layoutWidth": 1, "valueUnique": false, "widget_type": "number", "componentType": "number", "decimalDigits": null}'), # noqa
        data_schema=json.loads('{"title": "数值", "fields": [{"name": "960ee6bb-9573-4c3a-bb94-12e2d5afa0e8", "type": "number", "title": "数值"}]}'),  # noqa
    )
    step_2_widget_2: WidgetInfo = widget_info_factory.create(  # noqa
        widget_collection_id=widget_collection_1.id,
        widget_id=widget_2.id,
        key="5d9ae515-ada2-4d4c-b87b-938f33893a8f",
        option_value=json.loads('{"icon": "ficon-text-input", "label": "复合组件", "fields": [{"id": 59, "key": "92171163-cc38-4a67-b6bd-c4f6f9a299fe", "type": "number", "before": false, "data_schema": {"title": "数值", "fields": [{"name": "92171163-cc38-4a67-b6bd-c4f6f9a299fe", "type": "number", "title": "数值"}]}, "widget_type": "number", "option_value": {"icon": "ficon-number-input", "label": "数值", "before": false, "required": false, "isCalculate": false, "valueUnique": false, "widget_type": "number", "componentType": "number", "decimalDigits": null}}], "multiRow": true, "required": true, "minLength": 1, "layoutWidth": 1, "widget_type": "table", "componentType": "table", "rowLayoutMode": "table", "useDatasource": false}'.replace('"id": 52', '"id": {}'.format(widget_2.id)).replace('"id": 59', '"id": {}'.format(widget_1.id))),  # noqa
        data_schema=json.loads('{"title": "复合组件", "fields": [{"name": "92171163-cc38-4a67-b6bd-c4f6f9a299fe", "type": "number", "title": "数值"}]}'),  # noqa
    )

    step_3_widget_1: WidgetInfo = widget_info_factory.create(  # noqa
        widget_collection_id=widget_collection_2.id,
        widget_id=widget_1.id,
        key="c2b4434c-0244-4c62-8929-bd3601d69b22",
        option_value=json.loads('{"icon": "ficon-number-input", "label": "数值", "required": false, "isCalculate": false, "layoutWidth": 1, "valueUnique": false, "widget_type": "number", "componentType": "number", "decimalDigits": null}'),  # noqa
        data_schema=json.loads('{"title": "数值", "fields": [{"name": "c2b4434c-0244-4c62-8929-bd3601d69b22", "type": "number", "title": "数值"}]}'),  # noqa
    )
    step_3_widget_2: WidgetInfo = widget_info_factory.create(  # noqa
        widget_collection_id=widget_collection_2.id,
        widget_id=widget_2.id,
        key="3b50374f-fbb0-47ce-8534-7b116bdb2425",
        option_value=json.loads('{"icon": "ficon-text-input", "label": "复合组件", "fields": [{"id": 52, "key": "e85db638-3fc2-4bfd-b9ff-cfffa3d5e7b0", "type": "table", "before": false, "data_schema": {"title": "复合组件", "fields": [{"name": "a9f504f7-5512-4caf-a761-18f351b1d9da", "type": "table", "title": "复合组件", "fields": [{"name": "613dce89-f725-4015-910b-03da3c492a23", "type": "table", "title": "复合组件", "fields": [{"name": "8a40d0bc-5c22-49af-9b16-31869f54638c", "type": "table", "title": "复合组件", "fields": [{"name": "afa63ceb-02f7-4bb8-8894-9df6ec5da15b", "type": "number", "title": "数值"}]}]}]}]}, "widget_type": "table", "option_value": {"icon": "ficon-text-input", "label": "复合组件", "before": false, "fields": [{"id": 52, "key": "a9f504f7-5512-4caf-a761-18f351b1d9da", "type": "table", "before": false, "data_schema": {"title": "复合组件", "fields": [{"name": "613dce89-f725-4015-910b-03da3c492a23", "type": "table", "title": "复合组件", "fields": [{"name": "8a40d0bc-5c22-49af-9b16-31869f54638c", "type": "table", "title": "复合组件", "fields": [{"name": "afa63ceb-02f7-4bb8-8894-9df6ec5da15b", "type": "number", "title": "数值"}]}]}]}, "widget_type": "table", "option_value": {"icon": "ficon-text-input", "label": "复合组件", "before": false, "fields": [{"id": 52, "key": "613dce89-f725-4015-910b-03da3c492a23", "type": "table", "before": false, "data_schema": {"title": "复合组件", "fields": [{"name": "8a40d0bc-5c22-49af-9b16-31869f54638c", "type": "table", "title": "复合组件", "fields": [{"name": "afa63ceb-02f7-4bb8-8894-9df6ec5da15b", "type": "number", "title": "数值"}]}]}, "widget_type": "table", "option_value": {"icon": "ficon-text-input", "label": "复合组件", "before": false, "fields": [{"id": 52, "key": "8a40d0bc-5c22-49af-9b16-31869f54638c", "type": "table", "before": false, "data_schema": {"title": "复合组件", "fields": [{"name": "afa63ceb-02f7-4bb8-8894-9df6ec5da15b", "type": "number", "title": "数值"}]}, "widget_type": "table", "option_value": {"icon": "ficon-text-input", "label": "复合组件", "before": false, "fields": [{"id": 59, "key": "afa63ceb-02f7-4bb8-8894-9df6ec5da15b", "type": "number", "before": false, "data_schema": {"title": "数值", "fields": [{"name": "afa63ceb-02f7-4bb8-8894-9df6ec5da15b", "type": "number", "title": "数值"}]}, "widget_type": "number", "option_value": {"icon": "ficon-number-input", "label": "数值", "before": false, "required": false, "isCalculate": false, "valueUnique": false, "widget_type": "number", "componentType": "number", "decimalDigits": null}}], "multiRow": true, "required": true, "minLength": 1, "widget_type": "table", "componentType": "table", "rowLayoutMode": "table", "useDatasource": false}}], "multiRow": true, "required": true, "minLength": 1, "widget_type": "table", "componentType": "table", "rowLayoutMode": "table", "useDatasource": false}}], "multiRow": true, "required": true, "minLength": 1, "widget_type": "table", "componentType": "table", "rowLayoutMode": "table", "useDatasource": false}}], "multiRow": true, "required": true, "minLength": 1, "widget_type": "table", "componentType": "table", "rowLayoutMode": "table", "useDatasource": false}}], "multiRow": true, "required": true, "minLength": 1, "layoutWidth": 1, "widget_type": "table", "componentType": "table", "rowLayoutMode": "table", "useDatasource": false}'.replace('"id": 52', '"id": {}'.format(widget_2.id)).replace('"id": 59', '"id": {}'.format(widget_1.id))),  # noqa
        data_schema=json.loads('{"title": "复合组件", "fields": [{"name": "e85db638-3fc2-4bfd-b9ff-cfffa3d5e7b0", "type": "table", "title": "复合组件", "fields": [{"name": "a9f504f7-5512-4caf-a761-18f351b1d9da", "type": "table", "title": "复合组件", "fields": [{"name": "613dce89-f725-4015-910b-03da3c492a23", "type": "table", "title": "复合组件", "fields": [{"name": "8a40d0bc-5c22-49af-9b16-31869f54638c", "type": "table", "title": "复合组件", "fields": [{"name": "afa63ceb-02f7-4bb8-8894-9df6ec5da15b", "type": "number", "title": "数值"}]}]}]}]}]}'),  # noqa
    )

    step_1.prev_step_ids = []
    step_1.next_step_ids = [step_2.step_uuid]
    step_1.update_raw_step()
    step_2.prev_step_ids = [step_1.step_uuid]
    step_2.next_step_ids = [step_3.step_uuid]
    step_2.update_raw_step()
    step_3.prev_step_ids = [step_2.step_uuid]
    step_3.next_step_ids = []
    step_3.update_raw_step()

    bo_1: BusinessOrder = business_order_factory.create(  # noqa
        sid=client.sid,
        form_id=form.id,
        data=json.loads('{"flag": "", "system_business_order_end_at": "", "system_business_order_status": "待提交", "system_business_order_deadline": "2024-11-12 17:42:55", "system_business_order_form_name": "39 - 测试数值计算 1 ", "system_business_order_shop_nick": "开了个铺", "system_business_order_created_at": "2024-11-12 17:42:55", "system_business_order_updated_at": "2024-11-12 17:43:49", "system_business_order_receipt_url": "", "system_business_order_update_user": "神谷润", "5d9ae515-ada2-4d4c-b87b-938f33893a8f": [{"92171163-cc38-4a67-b6bd-c4f6f9a299fe": 44}, {"92171163-cc38-4a67-b6bd-c4f6f9a299fe": 1}, {"92171163-cc38-4a67-b6bd-c4f6f9a299fe": 241}], "93c96a42-477c-4149-9989-01d9308fe732": [{"8139f037-e281-48fc-b410-97e6efa36f07": [{"77005feb-8fd0-4098-889f-679f656c23cb": [{"07b3c2b7-69cf-474f-9042-110299bea6e7": [{"b8d35440-5c2c-45ee-8ebd-8f835208e590": "213"}]}]}]}, {"8139f037-e281-48fc-b410-97e6efa36f07": [{"77005feb-8fd0-4098-889f-679f656c23cb": [{"07b3c2b7-69cf-474f-9042-110299bea6e7": [{"b8d35440-5c2c-45ee-8ebd-8f835208e590": "-1"}]}]}]}, {"8139f037-e281-48fc-b410-97e6efa36f07": [{"77005feb-8fd0-4098-889f-679f656c23cb": [{"07b3c2b7-69cf-474f-9042-110299bea6e7": [{"b8d35440-5c2c-45ee-8ebd-8f835208e590": "23123123"}]}]}]}], "960ee6bb-9573-4c3a-bb94-12e2d5afa0e8": 12, "system_business_order_feisuo_creator": "神谷润", "system_business_order_feisuo_updator": "神谷润", "system_business_order_creator_platform": "神谷润", "system_business_order_current_step_name": "步骤名称3", "system_business_order_creator_group_string": "", "system_business_order_updator_group_string": "", "system_business_order_shop_platform_chinese": "淘宝", "system_business_order_current_job_assignee_feisuo": "", "system_business_order_feisuo_creator_group_string": "", "system_business_order_feisuo_updator_group_string": "", "system_business_order_current_job_exception_reason": "", "system_business_order_current_job_assignee_platform": "zhilei", "system_business_order_current_job_assignee_group_feisuo_string": "0211文婷乐言账号测试组,乐言账号组,开了个铺-czl,0310乐言操作日志,乐言禁用组", "system_business_order_current_job_assignee_group_platform_string": "0211文婷乐言账号测试组,乐言账号组,开了个铺-czl,0310乐言操作日志,乐言禁用组"}	'),  # noqa
    )
    bo_2: BusinessOrder = business_order_factory.create(  # noqa
        sid=client.sid,
        form_id=form.id,
        data=json.loads('{"flag": "", "system_business_order_end_at": "", "system_business_order_status": "待提交", "system_business_order_deadline": "2024-11-12 18:00:34", "system_business_order_form_name": "39 - 测试数值计算 1 ", "system_business_order_shop_nick": "开了个铺", "system_business_order_created_at": "2024-11-12 18:00:34", "system_business_order_updated_at": "2024-11-12 18:01:37", "system_business_order_receipt_url": "", "system_business_order_update_user": "神谷润", "3b50374f-fbb0-47ce-8534-7b116bdb2425": [{"e85db638-3fc2-4bfd-b9ff-cfffa3d5e7b0": [{"a9f504f7-5512-4caf-a761-18f351b1d9da": [{"613dce89-f725-4015-910b-03da3c492a23": [{"8a40d0bc-5c22-49af-9b16-31869f54638c": [{"afa63ceb-02f7-4bb8-8894-9df6ec5da15b": 213123123}]}]}, {"613dce89-f725-4015-910b-03da3c492a23": [{"8a40d0bc-5c22-49af-9b16-31869f54638c": [{"afa63ceb-02f7-4bb8-8894-9df6ec5da15b": 12}]}]}]}]}, {"e85db638-3fc2-4bfd-b9ff-cfffa3d5e7b0": [{"a9f504f7-5512-4caf-a761-18f351b1d9da": [{"613dce89-f725-4015-910b-03da3c492a23": [{"8a40d0bc-5c22-49af-9b16-31869f54638c": [{"afa63ceb-02f7-4bb8-8894-9df6ec5da15b": 21321312}, {"afa63ceb-02f7-4bb8-8894-9df6ec5da15b": 12312312}]}]}]}, {"a9f504f7-5512-4caf-a761-18f351b1d9da": [{"613dce89-f725-4015-910b-03da3c492a23": [{"8a40d0bc-5c22-49af-9b16-31869f54638c": [{"afa63ceb-02f7-4bb8-8894-9df6ec5da15b": 21312321}]}]}]}]}], "5d9ae515-ada2-4d4c-b87b-938f33893a8f": [{"92171163-cc38-4a67-b6bd-c4f6f9a299fe": ***********}, {"92171163-cc38-4a67-b6bd-c4f6f9a299fe": 21}, {"92171163-cc38-4a67-b6bd-c4f6f9a299fe": -213123}], "960ee6bb-9573-4c3a-bb94-12e2d5afa0e8": 213, "system_business_order_feisuo_creator": "神谷润", "system_business_order_feisuo_updator": "神谷润", "system_business_order_creator_platform": "神谷润", "system_business_order_current_step_name": "步骤名称3", "system_business_order_creator_group_string": "", "system_business_order_updator_group_string": "", "system_business_order_shop_platform_chinese": "淘宝", "system_business_order_current_job_assignee_feisuo": "", "system_business_order_feisuo_creator_group_string": "", "system_business_order_feisuo_updator_group_string": "", "system_business_order_current_job_exception_reason": "", "system_business_order_current_job_assignee_platform": "guicong.zhang", "system_business_order_current_job_assignee_group_feisuo_string": "", "system_business_order_current_job_assignee_group_platform_string": ""}'),  # noqa
    )

    yield form


def test_process():
    data = {
        "level_1": [
            {
                "level_2": [
                    {
                        "level_3": [
                            {
                                "level_4": [
                                    {
                                        "level_5": 11
                                    },
                                    {
                                        "level_5": 22
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "level_2": [
                    {
                        "level_3": [
                            {
                                "level_4": [
                                    {
                                        "level_5": 33
                                    },
                                    {
                                        "level_5": None
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "level_2": []
            }
        ]
    }

    widget_title = WidgetTitle([
        WidgetTitleItem(
            widget_name=WidgetName(
                widget_label="层级 1",
                widget_type=WidgetType("table"),
                widget_data_type=WidgetDataType.TABLE
            ),
            widget_infos=[
                WidgetInfo(key="level_1")
            ]
        ),
        WidgetTitleItem(
            widget_name=WidgetName(
                widget_label="层级 2",
                widget_type=WidgetType("table"),
                widget_data_type=WidgetDataType.TABLE
            ),
            widget_infos=[
                WidgetInfo(key="level_2")
            ]
        ),
        WidgetTitleItem(
            widget_name=WidgetName(
                widget_label="层级 3",
                widget_type=WidgetType("table"),
                widget_data_type=WidgetDataType.TABLE
            ),
            widget_infos=[
                WidgetInfo(key="level_3")
            ]
        ),
        WidgetTitleItem(
            widget_name=WidgetName(
                widget_label="层级 4",
                widget_type=WidgetType("table"),
                widget_data_type=WidgetDataType.TABLE
            ),
            widget_infos=[
                WidgetInfo(key="level_4")
            ]
        ),
        WidgetTitleItem(
            widget_name=WidgetName(
                widget_label="层级 5",
                widget_type=WidgetType("number"),
                widget_data_type=WidgetDataType.NUMBER
            ),
            widget_infos=[
                WidgetInfo(key="level_5")
            ]
        )
    ])

    values = get_number_widget_values(widget_title, data)

    assert values == [11, 22, 33]


def test_statistics(client, mock_data):
    resp = client.post(
        "/v1/task-center/business-orders/statistics",
        json={
            "form_id": [0]
        }
    )
    assert resp.json == {
        "success": False,
        "data": {},
        "reason": "未能查询到任一工单"
    }

    form = mock_data

    resp = client.post(
        "/v1/task-center/business-orders/statistics",
        json={
            "form_id": [form.id]
        }
    )

    assert resp.status_code == 200
    assert resp.json == {
        "success": True,
        "data": {
            "步骤名称2_数值": {
                "count": "2",
                "sum": "225.00",
                "mean": "112.50"
            },
            "步骤名称2_复合组件_数值": {
                "count": "6",
                "sum": "***********.00",
                "mean": "3553518082.67"
            },
            "步骤名称3_复合组件_复合组件_复合组件_复合组件_复合组件_数值": {
                "count": "5",
                "sum": "268069080.00",
                "mean": "53613816.00"
            }
        }
    }
