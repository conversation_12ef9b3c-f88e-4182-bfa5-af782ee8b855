from robot_processor.form.models import WidgetRef
from robot_processor.form.models import WidgetInfo
from robot_processor.business_order.models import BoWrapper


class Vanilla:
    @classmethod
    def get_data(cls):
        return {
            "oid": "527335201575309302",
            "tid": "527335201575309302",
            "1638d789-3e78-4681-96fd-7e98c4b366ff": [[{"label": "绿", "value": "绿"}]],
            "188cac8b-1112-4866-aa43-20b145afbbe5": "0.00",
            "26a04295-3107-46c1-bd52-f58ec1fcd310": "123",
            "2822464d-327c-47fb-8795-2b4e4e3bf098": [
                {
                    "37b633c0-faa5-4411-b645-649048d3c64b": "01-a",
                    "68381e6a-11ee-4460-af7e-4a65e04a1b77": [
                        {
                            "f2fadd89-a595-4ef9-ae7d-853bf090eca2": "申通",
                            "f91ef74f-76c6-4115-8525-70ed0d6ce848": "ST28726",
                        },
                        {
                            "f2fadd89-a595-4ef9-ae7d-853bf090eca2": "邮政",
                            "f91ef74f-76c6-4115-8525-70ed0d6ce848": "YZ67676",
                        },
                    ],
                    "ffc354d8-2d1b-470f-bc5e-dbd7d94d8a66": "123",
                },
                {
                    "37b633c0-faa5-4411-b645-649048d3c64b": "02-a",
                    "68381e6a-11ee-4460-af7e-4a65e04a1b77": [
                        {
                            "f2fadd89-a595-4ef9-ae7d-853bf090eca2": "申通",
                            "f91ef74f-76c6-4115-8525-70ed0d6ce848": "ST0000",
                        }
                    ],
                    "ffc354d8-2d1b-470f-bc5e-dbd7d94d8a66": "123",
                },
                {
                    "37b633c0-faa5-4411-b645-649048d3c64b": "03-a",
                    "68381e6a-11ee-4460-af7e-4a65e04a1b77": [
                        {
                            "f2fadd89-a595-4ef9-ae7d-853bf090eca2": "顺丰",
                            "f91ef74f-76c6-4115-8525-70ed0d6ce848": "SF1212",
                        }
                    ],
                    "ffc354d8-2d1b-470f-bc5e-dbd7d94d8a66": "123",
                },
            ],
            "29984543-dbb7-4958-aaa3-9ff18726f5d7": [{"tid": "527335201575309302"}],
            "33c7e301-294f-4db1-b0ad-ca0cf8e911b0": [{"label": "C", "value": "C"}],
            "3ab56be5-6f0c-4855-8dc1-f92d4a784936": "25.80",
            "3f15137b-6f6f-4025-8c4b-6a700a40f3e1": "2019-07-12 21:32:19",
            "72722346-90a3-45c6-a96c-037fa86a6f63": {
                "city": "东莞市",
                "name": "黄**",
                "state": "广东省",
                "mobile": "134********",
                "address": "厚*镇宝塘社区濠丰**广场vivo旁边中国**店",
            },
            "b5fc2028-042e-4fe2-897f-944e6a76f618": "hsf凤凤78894698",
            "c2a8f248-cf78-4e43-923a-aa479a821362": [
                {
                    "oid": "527335201575309302",
                    "sku": "4122306060130",
                    "spu": "594881173309",
                    "tid": "527335201575309302",
                    "mode": "sku",
                    "price": 27.8,
                    "props": "颜色分类:卡其黄T恤;尺码:均码",
                    "outer_sku": "4122306060130",
                    "item_title": "薄款透视宽松短袖t恤女+高腰花苞短裤阔腿裤新款夏装两件套套装",
                }
            ],
            "d5c569cc-be91-46da-93be-93377c5a9739": [
                {
                    "0bf53e92-7ceb-4a00-83f4-b30db544fe2a": [],
                    "26880fe0-147e-424b-89d5-91e177948a5f": [],
                    "27a5447f-d3fd-4de8-b17a-45e919927b13": "测试",
                    "3aede8cb-5cdb-4c45-989f-42b380f86b5d": "2023/08/29 15:43:53",
                    "4abd1b97-1d75-4e72-b17a-b08ffff94a9a": "1",
                    "4d12f04a-0e95-4219-9b46-5c5b6ce3853b": [
                        [{"label": "赵", "value": "赵"}]
                    ],
                    "bd91eab4-af0a-439d-966c-02a069fd20b2": 3,
                    "d57b25e8-b55c-430a-8a1c-7cff1d6862df": [],
                },
                {
                    "0bf53e92-7ceb-4a00-83f4-b30db544fe2a": [],
                    "26880fe0-147e-424b-89d5-91e177948a5f": [],
                    "27a5447f-d3fd-4de8-b17a-45e919927b13": "测试",
                    "3aede8cb-5cdb-4c45-989f-42b380f86b5d": "2023/08/29 15:43:53",
                    "4abd1b97-1d75-4e72-b17a-b08ffff94a9a": "1",
                    "4d12f04a-0e95-4219-9b46-5c5b6ce3853b": [
                        [{"label": "赵", "value": "赵"}]
                    ],
                    "bd91eab4-af0a-439d-966c-02a069fd20b2": 3,
                    "d57b25e8-b55c-430a-8a1c-7cff1d6862df": [],
                },
            ],
            "d8fcc64d-c33c-4c8c-a211-83706389ceba": {
                "sku_list": [
                    {
                        "sku": "4767367422532",
                        "spu": "639659416556",
                        "_key": "4767367422532",
                        "count": 30,
                        "price": 5,
                        "props": "适用手机型号:华为nova5;颜色分类:白色",
                        "sku_id": "4767367422532",
                        "source": "platform",
                        "pic_url": "https://img.alicdn.com/bao/uploaded/i1/1052101482"
                        "/TB26oEPeVXXXXcpXXXXXXXXXXXX_!!1052101482.jpg",
                        "quantity": 1,
                        "sku_name": "",
                        "outer_sku": "codeseller123",
                        "spu_title": "不能用的mp4",
                        "cost_price": 0,
                        "item_title": "不能用的mp4",
                        "supplier_id": "",
                        "outer_sku_id": "codeseller123",
                        "supplier_name": "",
                        "outer_sku_id_real": "",
                    }
                ],
                "after_sales_type": "non_original",
            },
        }

    @classmethod
    def get_widget_form(cls):
        return WidgetForm(
            {
                "1638d789-3e78-4681-96fd-7e98c4b366ff": WidgetInfo.View.RawStep(
                    key="1638d789-3e78-4681-96fd-7e98c4b366ff",
                    type="select-tile",
                    option_value=dict(label="多选平铺"),
                ),
                "188cac8b-1112-4866-aa43-20b145afbbe5": WidgetInfo.View.RawStep(
                    key="188cac8b-1112-4866-aa43-20b145afbbe5",
                    type="received-amount",
                    option_value=dict(label="实收金额"),
                ),
                "26a04295-3107-46c1-bd52-f58ec1fcd310": WidgetInfo.View.RawStep(
                    key="26a04295-3107-46c1-bd52-f58ec1fcd310",
                    type="out-sku-id",
                    option_value=dict(label="商品编码"),
                ),
                "2822464d-327c-47fb-8795-2b4e4e3bf098": WidgetInfo.View.RawStep(
                    key="2822464d-327c-47fb-8795-2b4e4e3bf098",
                    type="table",
                    widget_type="table",
                    data_schema=WidgetInfo.Schema.DataSchema(
                        multi_row=True,
                        fields=[
                            WidgetInfo.Schema.DataSchemaField(
                                name="37b633c0-faa5-4411-b645-649048d3c64b",
                                type="string",
                                title="内部单号",
                            ),
                            WidgetInfo.Schema.DataSchemaField(
                                name="68381e6a-11ee-4460-af7e-4a65e04a1b77",
                                type="table",
                                title="快递信息",
                                fields=[
                                    WidgetInfo.Schema.DataSchemaField(
                                        name="f2fadd89-a595-4ef9-ae7d-853bf090eca2",
                                        type="string",
                                        title="快递公司",
                                    ),
                                    WidgetInfo.Schema.DataSchemaField(
                                        name="f91ef74f-76c6-4115-8525-70ed0d6ce848",
                                        type="string",
                                        title="快递单号",
                                    ),
                                ],
                            ),
                            WidgetInfo.Schema.DataSchemaField(
                                name="ffc354d8-2d1b-470f-bc5e-dbd7d94d8a66",
                                type="string",
                                title="订单号",
                            ),
                        ],
                    ),
                    option_value=dict(
                        label="复合组件",
                        fields=[
                            WidgetInfo.View.RawStep(
                                key="37b633c0-faa5-4411-b645-649048d3c64b",
                                type="string",
                                option_value=dict(label="内部单号"),
                            ).dict(),
                            WidgetInfo.View.RawStep(
                                key="68381e6a-11ee-4460-af7e-4a65e04a1b77",
                                type="table",
                                widget_type="table",
                                option_value=dict(
                                    label="快递信息",
                                    fields=[
                                        WidgetInfo.View.RawStep(
                                            key="f2fadd89-a595-4ef9-ae7d-853bf090eca2",
                                            type="string",
                                            option_value=dict(label="快递公司"),
                                        ).dict(),
                                        WidgetInfo.View.RawStep(
                                            key="f91ef74f-76c6-4115-8525-70ed0d6ce848",
                                            type="string",
                                            option_value=dict(label="快递单号"),
                                        ).dict(),
                                    ],
                                ),
                            ).dict(),
                            WidgetInfo.View.RawStep(
                                key="ffc354d8-2d1b-470f-bc5e-dbd7d94d8a66",
                                type="string",
                                option_value=dict(label="订单号"),
                            ).dict(),
                        ],
                    ),
                ),
                "ffc354d8-2d1b-470f-bc5e-dbd7d94d8a66": WidgetInfo.View.RawStep(
                    key="ffc354d8-2d1b-470f-bc5e-dbd7d94d8a66",
                    type="string",
                    option_value=dict(lable="订单号"),
                ),
                "37b633c0-faa5-4411-b645-649048d3c64b": WidgetInfo.View.RawStep(
                    key="37b633c0-faa5-4411-b645-649048d3c64b",
                    type="string",
                    option_value=dict(label="内部单号"),
                ),
                "68381e6a-11ee-4460-af7e-4a65e04a1b77": WidgetInfo.View.RawStep(
                    key="68381e6a-11ee-4460-af7e-4a65e04a1b77",
                    type="table",
                    widget_type="table",
                    data_schema=WidgetInfo.Schema.DataSchema(
                        multi_row=True,
                        fields=[
                            WidgetInfo.Schema.DataSchemaField(
                                name="f2fadd89-a595-4ef9-ae7d-853bf090eca2",
                                type="string",
                                title="快递公司",
                            ),
                            WidgetInfo.Schema.DataSchemaField(
                                name="f91ef74f-76c6-4115-8525-70ed0d6ce848",
                                type="string",
                                title="快递单号",
                            ),
                        ],
                    ),
                    option_value=dict(
                        label="快递信息",
                        fields=[
                            WidgetInfo.View.RawStep(
                                key="f2fadd89-a595-4ef9-ae7d-853bf090eca2",
                                type="string",
                                option_value=dict(label="快递公司"),
                            ).dict(),
                            WidgetInfo.View.RawStep(
                                key="f91ef74f-76c6-4115-8525-70ed0d6ce848",
                                type="string",
                                option_value=dict(label="快递单号"),
                            ).dict(),
                        ],
                    ),
                ),
                "f2fadd89-a595-4ef9-ae7d-853bf090eca2": WidgetInfo.View.RawStep(
                    key="f2fadd89-a595-4ef9-ae7d-853bf090eca2",
                    type="string",
                    option_value=dict(label="快递公司"),
                ),
                "f91ef74f-76c6-4115-8525-70ed0d6ce848": WidgetInfo.View.RawStep(
                    key="f91ef74f-76c6-4115-8525-70ed0d6ce848",
                    type="string",
                    option_value=dict(label="快递单号"),
                ),
                "29984543-dbb7-4958-aaa3-9ff18726f5d7": WidgetInfo.View.RawStep(
                    key="29984543-dbb7-4958-aaa3-9ff18726f5d7",
                    type="order",
                    option_value=dict(label="订单/子订单"),
                ),
                "33c7e301-294f-4db1-b0ad-ca0cf8e911b0": WidgetInfo.View.RawStep(
                    key="33c7e301-294f-4db1-b0ad-ca0cf8e911b0",
                    type="radio-tile",
                    option_value=dict(label="单选平铺"),
                ),
                "3ab56be5-6f0c-4855-8dc1-f92d4a784936": WidgetInfo.View.RawStep(
                    key="3ab56be5-6f0c-4855-8dc1-f92d4a784936",
                    type="payment",
                    option_value=dict(label="实付金额"),
                ),
                "3f15137b-6f6f-4025-8c4b-6a700a40f3e1": WidgetInfo.View.RawStep(
                    key="3f15137b-6f6f-4025-8c4b-6a700a40f3e1",
                    type="order-time",
                    option_value=dict(label="下单时间"),
                ),
                "72722346-90a3-45c6-a96c-037fa86a6f63": WidgetInfo.View.RawStep(
                    key="72722346-90a3-45c6-a96c-037fa86a6f63",
                    type="address",
                    option_value=dict(label="收货地址"),
                ),
                "c2a8f248-cf78-4e43-923a-aa479a821362": WidgetInfo.View.RawStep(
                    key="c2a8f248-cf78-4e43-923a-aa479a821362",
                    type="product",
                    option_value=dict(label="商品"),
                ),
                "d5c569cc-be91-46da-93be-93377c5a9739": WidgetInfo.View.RawStep(
                    key="d5c569cc-be91-46da-93be-93377c5a9739",
                    type="table",
                    widget_type="table",
                    option_value=dict(
                        label="复合组件",
                        fields=[
                            WidgetInfo.View.RawStep(
                                key="0bf53e92-7ceb-4a00-83f4-b30db544fe2a",
                                type="radio-tile",
                                option_value=dict(label="单选平铺"),
                            ).dict(),
                            WidgetInfo.View.RawStep(
                                key="26880fe0-147e-424b-89d5-91e177948a5f",
                                type="radio-dropdown",
                                option_value=dict(label="单选下拉"),
                            ).dict(),
                            WidgetInfo.View.RawStep(
                                key="4d12f04a-0e95-4219-9b46-5c5b6ce3853b",
                                type="select-tile",
                                option_value=dict(label="多选平铺"),
                            ).dict(),
                            WidgetInfo.View.RawStep(
                                key="d57b25e8-b55c-430a-8a1c-7cff1d6862df",
                                type="select-dropdown",
                                option_value=dict(label="多选下拉"),
                            ).dict(),
                            WidgetInfo.View.RawStep(
                                key="d8fcc64d-c33c-4c8c-a211-83706389ceba",
                                type="reissue-product",
                                option_value=dict(label="补发商品"),
                            ).dict(),
                            WidgetInfo.View.RawStep(
                                key="27a5447f-d3fd-4de8-b17a-45e919927b13",
                                type="string",
                                option_value=dict(label="单行输入"),
                            ).dict(),
                            WidgetInfo.View.RawStep(
                                key="4abd1b97-1d75-4e72-b17a-b08ffff94a9a",
                                type="textarea",
                                option_value=dict(label="多行输入"),
                            ).dict(),
                            WidgetInfo.View.RawStep(
                                key="bd91eab4-af0a-439d-966c-02a069fd20b2",
                                type="number",
                                option_value=dict(label="数值"),
                            ).dict(),
                            WidgetInfo.View.RawStep(
                                key="3aede8cb-5cdb-4c45-989f-42b380f86b5d",
                                type="datetime",
                                option_value=dict(label="日期时间"),
                            ).dict(),
                        ],
                    ),
                    data_schema=WidgetInfo.Schema.DataSchema(
                        multi_row=True,
                        fields=[
                            WidgetInfo.Schema.DataSchemaField(
                                name="0bf53e92-7ceb-4a00-83f4-b30db544fe2a",
                                type="radio-tile",
                                title="单选平铺",
                            ),
                            WidgetInfo.Schema.DataSchemaField(
                                name="26880fe0-147e-424b-89d5-91e177948a5f",
                                type="radio-dropdown",
                                title="单选下拉",
                            ),
                            WidgetInfo.Schema.DataSchemaField(
                                name="4d12f04a-0e95-4219-9b46-5c5b6ce3853b",
                                type="select-tile",
                                title="多选平铺",
                            ),
                            WidgetInfo.Schema.DataSchemaField(
                                name="d57b25e8-b55c-430a-8a1c-7cff1d6862df",
                                type="select-dropdown",
                                title="多选下拉",
                            ),
                            WidgetInfo.Schema.DataSchemaField(
                                name="d8fcc64d-c33c-4c8c-a211-83706389ceba",
                                type="reissue-product",
                                title="补发商品",
                            ),
                            WidgetInfo.Schema.DataSchemaField(
                                name="27a5447f-d3fd-4de8-b17a-45e919927b13",
                                type="string",
                                title="单行输入",
                            ),
                            WidgetInfo.Schema.DataSchemaField(
                                name="4abd1b97-1d75-4e72-b17a-b08ffff94a9a",
                                type="textarea",
                                title="多行输入",
                            ),
                            WidgetInfo.Schema.DataSchemaField(
                                name="bd91eab4-af0a-439d-966c-02a069fd20b2",
                                type="number",
                                title="数值",
                            ),
                            WidgetInfo.Schema.DataSchemaField(
                                name="3aede8cb-5cdb-4c45-989f-42b380f86b5d",
                                type="datetime",
                                title="日期时间",
                            ),
                        ],
                    ),
                ),
                "27a5447f-d3fd-4de8-b17a-45e919927b13": WidgetInfo.View.RawStep(
                    key="27a5447f-d3fd-4de8-b17a-45e919927b13",
                    type="string",
                    option_value=dict(label="单行输入"),
                ),
                "4abd1b97-1d75-4e72-b17a-b08ffff94a9a": WidgetInfo.View.RawStep(
                    key="4abd1b97-1d75-4e72-b17a-b08ffff94a9a",
                    type="textarea",
                    option_value=dict(label="多行输入"),
                ),
                "bd91eab4-af0a-439d-966c-02a069fd20b2": WidgetInfo.View.RawStep(
                    key="bd91eab4-af0a-439d-966c-02a069fd20b2",
                    type="number",
                    option_value=dict(label="数值"),
                ),
                "3aede8cb-5cdb-4c45-989f-42b380f86b5d": WidgetInfo.View.RawStep(
                    key="3aede8cb-5cdb-4c45-989f-42b380f86b5d",
                    type="datetime",
                    option_value=dict(label="日期时间"),
                ),
                "0bf53e92-7ceb-4a00-83f4-b30db544fe2a": WidgetInfo.View.RawStep(
                    key="0bf53e92-7ceb-4a00-83f4-b30db544fe2a",
                    type="radio-tile",
                    option_value=dict(label="单选平铺"),
                ),
                "26880fe0-147e-424b-89d5-91e177948a5f": WidgetInfo.View.RawStep(
                    key="26880fe0-147e-424b-89d5-91e177948a5f",
                    type="radio-dropdown",
                    option_value=dict(label="单选下拉"),
                ),
                "4d12f04a-0e95-4219-9b46-5c5b6ce3853b": WidgetInfo.View.RawStep(
                    key="4d12f04a-0e95-4219-9b46-5c5b6ce3853b",
                    type="select-tile",
                    option_value=dict(label="多选平铺"),
                ),
                "d57b25e8-b55c-430a-8a1c-7cff1d6862df": WidgetInfo.View.RawStep(
                    key="d57b25e8-b55c-430a-8a1c-7cff1d6862df",
                    type="select-dropdown",
                    option_value=dict(label="多选下拉"),
                ),
                "d8fcc64d-c33c-4c8c-a211-83706389ceba": WidgetInfo.View.RawStep(
                    key="d8fcc64d-c33c-4c8c-a211-83706389ceba",
                    type="reissue-product",
                    option_value=dict(label="补发商品"),
                ),
            }
        )

    @classmethod
    def get_widget_ref_table_table(cls):
        """复合组件"""
        return WidgetRef(
            key="2822464d-327c-47fb-8795-2b4e4e3bf098",
            type="table",
            multi_row=True,
            field=None,
        )

    @classmethod
    def get_widget_ref_table_logistics(cls):
        """复合组件-快递信息"""
        widget_ref = cls.get_widget_ref_table_table()
        widget_ref.field = WidgetRef(
            key="68381e6a-11ee-4460-af7e-4a65e04a1b77",
            type="table",
            multi_row=True,
            field=None,
        )
        return widget_ref

    @classmethod
    def get_widget_ref_column_logistics_company(cls):
        """复合组件-快递信息-快递公司"""
        widget_ref = cls.get_widget_ref_table_logistics()
        widget_ref.field.field = WidgetRef(
            key="f2fadd89-a595-4ef9-ae7d-853bf090eca2",
            type="string",
            widget_type="string",
        )
        return widget_ref

    @classmethod
    def get_widget_ref_column_logistics_no(cls):
        """复合组件-快递信息-快递单号"""
        widget_ref = cls.get_widget_ref_table_logistics()
        widget_ref.field.field = WidgetRef(
            key="f91ef74f-76c6-4115-8525-70ed0d6ce848",
            type="string",
            widget_type="string",
        )
        return widget_ref

    @classmethod
    def get_widget_ref_column_order(cls):
        """复合组件-内部单号"""
        widget_ref = cls.get_widget_ref_table_table()
        widget_ref.field = WidgetRef(
            key="37b633c0-faa5-4411-b645-649048d3c64b",
            type="string",
            widget_type="string",
        )
        return widget_ref


class WidgetForm(BoWrapper.WidgetForm):
    def __init__(self, widget_map):
        self._widget_map = widget_map
