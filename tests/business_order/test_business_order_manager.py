import pytest
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.business_order.mini_app.schema import BuyerBusinessOrderSchema
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.schema import BusinessOrderSchema
from robot_processor.currents import g
from robot_processor.enums import Creator
from robot_processor.ext import db
from robot_processor.form.models import FormShop
from rpa.n8n.api import N8NBusinessOrderSchema


def test_merge_tid_oid_uid_from_data(
        client, mocker, mock_business_order: BusinessOrder, mock_form, mock_step,
        widget_info_factory, widget_collection_factory, widget):
    mock_business_order.uid = None
    mock_business_order.data = {
        "f504c40e-1f8f-47e3-afbe-4396cbb4841d": {
            "tid": "tid",
            "oid": "oid"
        },
        "7e45b942-5d53-4944-afc5-5df91fdaaab0": {
            "sku_id": "sku_id"
        }, "fb4f332a-01f4-46da-89bc-e4558b2dfd50": {
            "usernick": "usernick"
        }
    }
    wc = widget_collection_factory.create()
    mock_step.form_id = mock_form.id
    mock_step.widget_collection_id = wc.id
    mock_step.prev_step_ids = []
    product_widget = widget('商品')
    order_widget = widget('订单/子订单')
    usernick_widget = widget('买家昵称')
    widget_info_factory.create(widget_id=order_widget.id, key='f504c40e-1f8f-47e3-afbe-4396cbb4841d',
                               widget_collection_id=wc.id)
    widget_info_factory.create(widget_id=usernick_widget.id, key='fb4f332a-01f4-46da-89bc-e4558b2dfd50',
                               widget_collection_id=wc.id)
    widget_info_factory.create(widget_id=product_widget.id, key='7e45b942-5d53-4944-afc5-5df91fdaaab0',
                               widget_collection_id=wc.id)

    db.session.commit()
    BusinessManager.merge_tid_oid_uid_from_data(mock_business_order)
    assert mock_business_order.uid == "usernick"
    assert mock_business_order.data["tid"] == "tid"
    assert mock_business_order.data["oid"] == "oid"

    mock_business_order.uid = None
    mock_business_order.data = {}
    flag_modified(mock_business_order, "data")
    db.session.commit()

    BusinessManager.merge_tid_oid_uid_from_data(mock_business_order)
    assert mock_business_order.uid is None
    assert mock_business_order.data.get("tid") is None
    assert mock_business_order.data.get("oid") is None


def test_old_fill_business_order(client, mock_form):
    g.org_sids = [client.sid]
    mock_form.subscribe(client.shop, True)
    body = BuyerBusinessOrderSchema(
        sid=client.shop.sid,
        mid="188",
        form_id=mock_form.id,
        uid="uid"
    )
    error, bo = BusinessManager.fill_business_order(body)
    assert error is None
    assert bo.sid == client.shop.sid
    assert bo.mid == "188"
    assert bo.aid == "uid"
    assert bo.creator_type == Creator.USER


def test_business_order_deleted_not_enable_status(client, mock_form):
    """
    工单删除和未启用的情况，优先提示工单删除
    """
    g.org_sids = [client.sid]
    mock_form.subscribe(client.shop, True)
    db.session.commit()

    deleted_msg = ("该工单已失效,建议您咨询客服", '抱歉，当前工单模板及任务已被删除，无法创建')
    enabled_msg = ("该类型工单未启用", )

    # 工单删除，工单未启用
    mock_form.form_subscribe.unsubscribe(client.shop, reserved=False)
    assert mock_form.wraps(client.shop).status == FormShop.Status.DELETED, "预期工单状态为删除"

    body = BuyerBusinessOrderSchema(
        sid=client.shop.sid,
        mid="188",
        form_id=mock_form.id,
        uid="uid"
    )
    _, msg = body.check()
    assert msg in deleted_msg

    _, msg = N8NBusinessOrderSchema(
        sid=client.shop.sid,
        mid="188",
        form_id=mock_form.id,
        uid="uid"
    ).check()
    assert msg in deleted_msg

    error, bo = BusinessManager.fill_business_order(body)
    assert error.get('reason') in deleted_msg

    # 工单未删除，工单未启用
    mock_form.subscribe(client.shop, enabled=False)
    assert mock_form.wraps(client.shop).status == FormShop.Status.DISABLED, "预期工单状态为未启用"

    body = BuyerBusinessOrderSchema(
        sid=client.shop.sid,
        mid="188",
        form_id=mock_form.id,
        uid="uid"
    )
    _, msg = body.check()
    assert msg in enabled_msg

    _, msg = N8NBusinessOrderSchema(
        sid=client.shop.sid,
        mid="188",
        form_id=mock_form.id,
        uid="uid"
    ).check()
    assert msg in enabled_msg

    error, bo = BusinessManager.fill_business_order(body)
    assert error.get('reason') in enabled_msg


@pytest.fixture
def mock_form_subscribe(client, mock_form):
    mock_form.subscribe(client.shop, True)
    db.session.commit()


def test_fill_business_order_invalid_org_id(client, mock_form_subscribe):
    body = BusinessOrderSchema(user_nick="un1", creator_user_id=111, sid=client.shop.sid, mid="188", form_id=111)
    g.org_sids = []
    error, bo = BusinessManager.fill_business_order(body)
    assert "非法店铺id" == error['reason']


def test_fill_business_order_invalid_username(client, mock_form_subscribe, mock_form):
    g.org_sids = [client.shop.sid]
    body = BusinessOrderSchema(user_nick="un1", creator_user_id=111, sid=client.shop.sid, mid="188",
                               form_id=mock_form.id)
    error, bo = BusinessManager.fill_business_order(body)
    assert error
    assert "获取用户名失败" == error['reason']


def test_fill_business_order_success(client, mock_form_subscribe, mock_form):
    g.org_sids = [client.shop.sid]
    body = BusinessOrderSchema(user_nick=client.assistant.user_id, creator_user_id=client.assistant.user_id,
                               sid=client.shop.sid, mid="188", form_id=mock_form.id)
    error, bo = BusinessManager.fill_business_order(body)
    assert error is None
    assert bo.form_id == mock_form.id
    assert bo.sid == client.shop.sid
    assert bo.mid == "123"
    assert bo.aid == "org1_shop1:user1"
    assert bo.creator_type == client.assistant.user_type
    assert bo.creator_user_id == client.assistant.user_id
