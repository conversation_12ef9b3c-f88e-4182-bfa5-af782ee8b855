from functools import cached_property

from _pytest.fixtures import fixture
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.business_order.events import record_bo_update
from robot_processor.business_order.models import BusinessOrderSystemAttributes
from robot_processor.ext import db


def test_set_feisuo_updator_by(client, mock_business_order):
    mock_business_order.sid = client.shop.sid
    mock_business_order.set_updator_by_nick(client.assistant.user_nick)
    assert mock_business_order.update_user == client.assistant.user_nick
    assert mock_business_order.feisuo_updator == client.assistant.get_bound_leyan_user().user_nick


@fixture
def sqlalchemy_event_listen():
    from sqlalchemy.event import contains
    from sqlalchemy.event import listens_for
    from sqlalchemy.event import remove

    from robot_processor.business_order.events import record_bo_update
    from robot_processor.business_order.models import BusinessOrder

    if not contains(BusinessOrder, "before_update", record_bo_update):
        listens_for(BusinessOrder, "before_update")(record_bo_update)

        def defer():
            remove(BusinessOrder, "before_update", record_bo_update)

    else:

        def defer():
            pass

    yield
    defer()


def test_bo_update_system_fields(client, mock_business_order, mock_form, sqlalchemy_event_listen):
    mock_business_order.form = mock_form
    mock_business_order.data["system_business_order_status"] = "RUNNING"
    flag_modified(mock_business_order, "data")
    db.session.add(mock_business_order)
    db.session.commit()
    # 修改工单状态
    mock_business_order.status = "FAILED"
    db.session.add(mock_business_order)
    db.session.commit()
    # 预期系统组件也被更改了
    assert mock_business_order.data["system_business_order_status"] == "FAILED"


def test_bo_system_attribute_extracting(client, mock_business_order, mock_form):
    # 增加 BusinessOrderSystemAttributes 的测试覆盖率
    mock_business_order.form = mock_form
    attributes = BusinessOrderSystemAttributes(mock_business_order)
    for attr_name in dir(BusinessOrderSystemAttributes):
        if attr_name.startswith("_"):
            continue
        attr_value = getattr(BusinessOrderSystemAttributes, attr_name)
        if isinstance(attr_value, (property, cached_property)):
            try:
                getattr(attributes, attr_name)
            except AttributeError:
                pass


# mock loguru.logger.error
@fixture
def mock_error_log(mocker):
    p = mocker.patch("robot_processor.business_order.events.logger.error")
    yield p


def test_每一个系统字段的获取(client, mock_business_orders, mock_error_log):
    # 下面是全部的系统字段,来源是category=4的widget
    bo = mock_business_orders.business_orders[0]
    bo.data = dict().fromkeys(
        [
            "system_business_order_end_at",
            "system_business_order_status",
            "system_business_order_deadline",
            "system_business_order_shop_nick",
            "system_business_order_created_at",
            "system_business_order_updated_at",
            "system_business_order_receipt_url",
            "system_business_order_update_user",
            "system_business_order_feisuo_creator",
            "system_business_order_feisuo_updator",
            "system_business_order_creator_platform",
            "system_business_order_current_step_name",
            "system_business_order_creator_group_string",
            "system_business_order_updator_group_string",
            "system_business_order_shop_platform_chinese",
            "system_business_order_current_job_assignee_feisuo",
            "system_business_order_feisuo_creator_group_string",
            "system_business_order_feisuo_updator_group_string",
            "system_business_order_current_job_assignee_platform",
            "system_business_order_current_job_assignee_group_feisuo_string",
            "system_business_order_current_job_assignee_group_platform_string",
        ]
    )
    flag_modified(bo, "data")
    db.session.add(bo)
    db.session.commit()
    record_bo_update(None, None, bo)
    # 出错时会打印错误日志
    assert mock_error_log.call_count == 0
