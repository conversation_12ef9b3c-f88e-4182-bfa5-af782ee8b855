import json
from pytest import fixture

from robot_processor.enums import StepType, JobType
from robot_processor.types.exception_rule import rule_scope_fallback
from robot_processor.business_order.exception_rule import ExceptionRuler, ExceptionRule


class TestExceptionRuler:
    @fixture(autouse=True)
    def setup_Exception_rules(self, db):
        db.session.add(
            ExceptionRule(
                priority=100,
                enabled=True,
                scopes=["human"],
                rules=["候选客服的状态为非有效"],
                reason="工单的处理客服已被禁用",
                suggestion="建议您指派给其他候选客服",
                updated_by="ut",
            )
        )
        db.session.add(
            ExceptionRule(
                priority=100,
                enabled=True,
                scopes=[],
                rules=["TimeoutError"],
                reason="系统请求超时",
                suggestion="请重试该任务，重新请求执行任务",
                updated_by="ut",
            )
        )
        db.session.add(
            ExceptionRule(
                priority=100,
                enabled=True,
                scopes=[JobType.CHANGE_ADDRESS.value, JobType.SEND_MESSAGE.value],
                rules=[r"客户端未登录, shop=(?P<shop>\w+),?.*"],
                reason="[{{shop}}]客户端未登录",
                suggestion="登录客户端后重试",
                updated_by="ut",
            )
        )
        db.session.add(
            ExceptionRule(
                priority=100,
                enabled=True,
                scopes=[JobType.SEND_MESSAGE.value],
                rules=[
                    (
                        r"mola error\. "
                        r"namespace=qianniu, "
                        r"method=send-message, "
                        r"request=.*(\"contact\":\s*\"(?P<contact>[^\"]+)\"),?.*"
                        r"runtime=.*(\"subUserNick\":\s*\"(?P<user>[^\"]+)\"),?.*"
                        r"error=.*not in conversation"
                    )
                ],
                reason="买家[{{contact}}]不在客服[{{user}}]的对话列表内",
                suggestion="确认买家信息后，再点击重试。",
                updated_by="ut",
            )
        )
        # 兜底规则
        db.session.add(
            ExceptionRule(
                priority=0,
                enabled=True,
                scopes=[rule_scope_fallback],
                rules=[".*"],
                reason="未知错误",
                suggestion="请联系飞梭客服处理",
                updated_by="ut",
            )
        )

    def test_handle(self):
        ruler = ExceptionRuler.load_from_orm(ExceptionRule.query)

        # 不满足执行条件
        result = ruler.process("候选客服的状态为非有效", StepType.human, None)
        assert result.render_result.reason == "工单的处理客服已被禁用"
        assert result.render_result.suggestion == "建议您指派给其他候选客服"

        # erp api
        exc = ruler.process("TimeoutError(HTTPConnectionPool: Read timed out. (read timeout=1))", StepType.human, None)
        assert exc.render_result.reason == "系统请求超时"
        assert exc.render_result.suggestion == "请重试该任务，重新请求执行任务"

        # 简单的 rpa 客户端
        exc = ruler.process("客户端未登录, shop=测试店铺", StepType.auto, JobType.CHANGE_ADDRESS.value)
        assert exc.render_result.reason == "[测试店铺]客户端未登录"
        assert exc.render_result.suggestion == "登录客户端后重试"

        # 复杂一些的 rpa 客户端
        rpa_res = {
            "success": False,
            "request": {
                "sid": "61891485",
                "title": "带着狗狗泡妞",
                "site": "taobao",
                "namespace": "qianniu",
                "method": "send-message",
                "query": {},
                "payload": {
                    "contact": "这个不是吧",
                    "count": 100,
                    "buyer_open_uid": "AAEwp3E6ARvSHbj0w8",
                    "ccode": "785406918.1-.1#11001@cntaobao",
                },
            },
            "_error": {
                "name": "ServiceError",
                "message": "imsdk.invoke() 出错",
                "stack": "ServiceError: imsdk.invoke() 出错\n    at er.invoke ...",
                "value": {
                    "err": {
                        "code": 9,
                        "subcode": 1,
                        "result": {"hasMore": 1, "msgs": []},
                        "passthrough": {
                            "code": '{"domain":1,"code":400200003,"scope":"IM"}',
                            "detail": '{"developer_message":"not in conversation",'
                            '"reason":"not in conversation",'
                            '"extra_info":"00510a94&6315","scope":"IM"}',
                            "nameInvoke": "ListPreviousMsgs",
                        },
                    },
                    "cmd": "im.singlemsg.GetRemoteHisMsg",
                    "params": {
                        "cid": {"ccode": "785406918.1-421487242.1#11001@cntaobao"},
                        "gohistory": 1,
                        "count": 100,
                    },
                    "timeout": 8333.333333333334,
                    "retry": 0,
                },
                "date": "2023-04-12T10:43:50.490Z",
            },
            "runtime": {
                "authInfo": {
                    "shopID": 61891485,
                    "shopName": "沐风之南家具",
                    "userID": "421487242",
                    "userNick": "带着狗狗泡妞:妹妹",
                    "subUserID": "2214736580688",
                    "subUserNick": "带着狗狗泡妞:妹妹",
                },
                "timeout": 40000,
                "execTime": 323,
                "startTime": "2023/4/12下午6:43:50",
                "endTime": "2023/4/12下午6:43:50",
                "loadTime": "2023/4/12下午5:04:26",
                "href": "alires:///WebUI/chatnewmsg/recent.html?debug=true",
            },
        }
        rpa_tmpl = (
            "mola error. "
            "namespace={namespace}, method={method}, "
            "request={request}, runtime={runtime}, error={error}"
        )
        exc_info = rpa_tmpl.format(
            namespace=rpa_res["request"]["namespace"],
            method=rpa_res["request"]["method"],
            request=json.dumps(rpa_res["request"], ensure_ascii=False),
            runtime=json.dumps(rpa_res["runtime"], ensure_ascii=False),
            error=json.dumps(rpa_res["_error"], ensure_ascii=False),
        )
        exc = ruler.process(exc_info, StepType.auto, JobType.SEND_MESSAGE.value)
        assert exc.render_result.reason == "买家[这个不是吧]不在客服[带着狗狗泡妞:妹妹]的对话列表内"
        assert exc.render_result.suggestion == "确认买家信息后，再点击重试。"

        # 兜底
        exc = ruler.process("随机错误", StepType.human, None)
        assert exc.render_result.reason == "未知错误"
        assert exc.render_result.suggestion == "请联系飞梭客服处理"
