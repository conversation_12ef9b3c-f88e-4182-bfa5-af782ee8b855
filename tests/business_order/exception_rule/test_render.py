"""robot_processor.business_order.Exception.models.ExceptionRule.render
"""

from pytest import fixture

from robot_processor.business_order.exception_rule import ExceptionRuler
from robot_processor.business_order.exception_rule import ExceptionRule
from robot_processor.types.exception_rule import ExcInfo


class TestRender:
    @fixture(autouse=True)
    def setup(self, client):
        yield

    @fixture
    def setup_render_with_context(self, db):
        rule = ExceptionRule(
            enabled=True,
            rules=[r"no valid rpa client, shop=(?P<店铺名>\w+)"],
            reason="[{{店铺名}}]客户端未登录",
            suggestion="登录[{{店铺名}}]客户端后重试",
            updated_by="ut"
        )
        db.session.add(rule)
        yield

    def test_render_with_context(self, setup_render_with_context):
        """正则匹配的，返回带上下文的文案"""
        ruler = ExceptionRuler.load_from_orm(ExceptionRule.query)

        exc_info = ExcInfo(scope="", raw="no valid rpa client, shop=测试店铺")
        match_result = ruler.match(exc_info)
        render = ruler.render(exc_info, match_result)
        assert render.reason == "[测试店铺]客户端未登录"
        assert render.suggestion == "登录[测试店铺]客户端后重试"
