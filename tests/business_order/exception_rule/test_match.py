"""robot_processor.business_order.exceptional.models.ExceptionRule.match
"""
from dataclasses import dataclass

from pytest import fixture

from robot_processor.business_order.exception_rule import ExceptionRule, ExceptionRuler
from robot_processor.types.exception_rule import ExcInfo, rule_scope_fallback


@dataclass()
class MockedData:
    rule: ExceptionRule = None
    ruler: ExceptionRuler = None


class TestMatch:
    mocked_data = MockedData()

    @fixture(autouse=True)
    def setup(self, client):
        yield

    @fixture
    def setup_ignore_disabled_rule(self):
        rule = ExceptionRule(
            id=1, enabled=False, rules=[r".*"], scopes=[""],
            priority=100, extra_config={}, reason="", suggestion=""
        )
        self.mocked_data.ruler = ExceptionRuler.load_from_orm([rule])
        self.mocked_data.rule = rule
        yield

    def test_ignore_disabled_rule(self, setup_ignore_disabled_rule):
        """测试不使用未开启的规则"""
        ruler = self.mocked_data.ruler
        match_result = ruler.match(ExcInfo(scope="", raw="any message"))
        # 命中了兜底规则，忽略了 enabled=False 的规则
        assert ruler.check_is_fallback_rule(match_result)

    @fixture
    def setup_ignore_scoped_rule(self):
        rule = ExceptionRule(
            id=100, enabled=True, scopes=["specified"], rules=[r".*"],
            priority=100, extra_config={}, reason="", suggestion=""
        )
        self.mocked_data.ruler = ExceptionRuler.load_from_orm([rule])
        self.mocked_data.rule = rule
        yield

    def test_ignore_scoped_rule(self, setup_ignore_scoped_rule):
        """测试不使用非当前 scope 的规则"""
        ruler = self.mocked_data.ruler
        match_result = ruler.match(ExcInfo(scope="other", raw="any message"))
        # 命中了兜底规则，因为 scope 不匹配
        assert ruler.check_is_fallback_rule(match_result)

    @fixture
    def setup_match_any_rule(self):
        rules = [
            ExceptionRule(
                id=1, enabled=True, scopes=["human"], rules=["assignee invalid"],
                priority=100, extra_config={}, reason="", suggestion=""
            ),
            ExceptionRule(
                id=2, enabled=True, scopes=["human"], rules=["assignee not found"],
                priority=100, extra_config={}, reason="", suggestion=""
            ),
            ExceptionRule(
                id=3, enabled=True, scopes=["SEND_MESSAGE"], rules=["no valid client"],
                priority=100, extra_config={}, reason="", suggestion=""
            ),
            ExceptionRule(
                id=4, enabled=True, scopes=[], rules=["any scope can match"],
                priority=100, extra_config={}, reason="", suggestion=""
            ),
            ExceptionRule(
                id=5,
                priority=10,
                enabled=True,
                scopes=[rule_scope_fallback],
                rules=["KeyError"],
                extra_config={},
                reason="",
                suggestion=""
            ),
            ExceptionRule(
                id=6,
                priority=9,
                enabled=True,
                scopes=[rule_scope_fallback],
                rules=[".*"],
                extra_config={},
                reason="",
                suggestion=""
            ),
        ]
        ruler = ExceptionRuler.load_from_orm(rules)
        self.mocked_data.ruler = ruler
        yield

    def test_match_any_rule(self, setup_match_any_rule):
        """测试满足任一规则"""
        ruler = self.mocked_data.ruler

        # 测试命中指定规则
        match_result = ruler.match(
            ExcInfo(scope="human", raw="any message contain assignee invalid keyword")
        )
        assert match_result.match_rule.id == 1

        match_result = ruler.match(
            ExcInfo(scope="human", raw="any message assignee not found woo")
        )
        assert match_result.match_rule.id == 2

        match_result = ruler.match(
            ExcInfo(
                scope="SEND_MESSAGE",
                raw="something wrong, maybe there has no valid client.",
            )
        )
        assert match_result.match_rule.id == 3

        match_result = ruler.match(
            ExcInfo(scope="some scope", raw="try any scope can match.")
        )
        assert match_result.match_rule.id == 4

        # 测试兜底规则
        match_result = ruler.match(
            ExcInfo(scope="any scope", raw="KeyError: 'key' not in data")
        )
        assert match_result.match_rule.id == 5
        assert ruler.check_is_fallback_rule(match_result)

        match_result = ruler.match(
            ExcInfo(scope="AFTER_SALE_UPLOAD", raw="ValueError: find a bug!")
        )
        assert match_result.match_rule.id == 6
        assert ruler.check_is_fallback_rule(match_result)
