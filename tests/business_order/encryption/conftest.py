import typing as t

import pytest

from robot_processor.business_order.seller.schema import BusinessOrderDetail
from robot_processor.enums import BusinessOrder<PERSON>tatus, Creator, WidgetCategory
from robot_processor.ext import db
from robot_processor.form.models import WidgetInfo
from robot_processor.task_center.getter import BusinessOrderBrief, ShopBrief
from robot_processor.utils import ts2date
from tests.factories import (BusinessOrderFactory, FormFactory, JobFactory,
                             StepFactory, WidgetCollectionFactory,
                             WidgetFactory, WidgetInfoFactory)


@pytest.fixture
def mock_address_widget():
    yield WidgetFactory.create(
        label="收货地址",
        code=4,
        category=WidgetCategory.ONLINE_RETAIL,
        widget_meta=[{"label": "姓名", "value": "name"}, {"label": "手机号",
                                                        "value": "mobile"}, {"label": "详细地址", "value": "address"}],
        schema={
            "icon": "ficon-delivery-info-input",
            "type": "address",
            "label": "收货地址",
            "unique": False,
            "conditions": [
                {"label": "省份等于", "ui_type": "province_one", "operator": "PROVINCE_EQ",
                    "ref_type": "address", "value_type": "OBJECT"},
                {"label": "省份等于任一", "ui_type": "province_any", "operator": "PROVINCE_IN",
                    "ref_type": "address", "value_type": "OBJECT"},
                {"label": "为空", "ui_type": "no_widget",
                 "operator": "NOT_EXIST", "ref_type": "address",
                 "value_type": "OBJECT"}
            ],
            "description": "收货地址，地址组件，需手动输入省市区详细地址、姓名、电话号；淘系、抖音、拼多多平台和聚水潭ERP可在权限范围内登录RPA解密原地址。"
        },
        options={
            "mode": {
                "enum": [
                    {
                        "label": "仅用于展示原地址",
                        "value": "old"
                    },
                    {
                        "label": "用于填写新地址",
                        "value": "new"
                    }
                ],
                "type": "string",
                "title": "组件形式",
                "default": "old",
                "x-index": 2,
                "x-component": "RadioGroup",
                "x-decorator": "FormItem"
            },
            "label": {
                "type": "string",
                "title": "标题",
                "x-index": 1,
                "x-component": "Input",
                "x-decorator": "FormItem",
                "x-validator": [
                    {
                        "required": True
                    },
                    {
                        "max": 20
                    }
                ],
                "x-component-props": {
                    "placeholder": "请设置标题"
                }
            },
            "required": {
                "enum": [
                    {
                        "label": "必填",
                        "value": True
                    },
                    {
                        "label": "非必填",
                        "value": False
                    }
                ],
                "type": "boolean",
                "title": "是否必填",
                "default": False,
                "x-index": 3,
                "x-component": "RadioGroup",
                "x-decorator": "FormItem"
            },
            "layoutWidth": {
                "type": "number",
                "title": "组件列宽占比(%)",
                "default": 1,
                "x-index": 4,
                "x-component": "RadioGroup",
                "x-decorator": "FormItem",
                "x-validator": [
                    {
                        "required": False
                    }
                ],
                "x-component-props": {
                    "options": [
                        {
                            "label": "25",
                            "value": 0.25
                        },
                        {
                            "label": "50",
                            "value": 0.5
                        },
                        {
                            "label": "75",
                            "value": 0.75
                        },
                        {
                            "label": "100",
                            "value": 1
                        }
                    ],
                    "optionType": "button"
                }
            }
        }
    )


@pytest.fixture
def mock_address_steps(mock_address_widget):
    collections = WidgetCollectionFactory.create_batch(2)
    widget_infos = [WidgetInfoFactory.create(
        widget_collection_id=x.id,
        widget_id=mock_address_widget.id
    ) for x in collections]
    steps = []
    for widget_info in widget_infos:
        steps.append(
            StepFactory.create(
                widget_collection_id=widget_info.widget_collection_id,
            )
        )
    yield steps


@pytest.fixture
def mock_address_info():
    yield {
        "state": "黑龙江省",
        "city": "齐齐哈尔市",
        "town": "五龙街道",
        "zone": "龙沙区",
        "address": "大大街111号",
        "name": "建设",
        "mobile": "***********-1234"
    }


@pytest.fixture
def mock_address_bo(mock_address_steps, mock_address_info):
    jobs = []
    form = FormFactory.create()
    bo = BusinessOrderFactory.create(
        form_id=form.id
    )
    bo_data = {}
    for step in mock_address_steps:
        jobs.append(
            JobFactory.create(
                step_id=step.id,
                business_order_id=bo.id
            )
        )
        widget_info = WidgetInfo.query.filter(
            WidgetInfo.widget_collection_id == step.widget_collection_id
        ).first()
        widget_info = t.cast(WidgetInfo, widget_info)
        bo_data[widget_info.key] = mock_address_info
    bo.data = bo_data
    db.session.commit()
    yield bo


@pytest.fixture
def mock_bo_brief(mock_address_bo):
    yield BusinessOrderBrief(
        id=mock_address_bo.id,
        name=mock_address_bo.form.name,
        description=mock_address_bo.form.description,
        status=BusinessOrderStatus(mock_address_bo.status),
        from_type=mock_address_bo.from_type,
        form_category=mock_address_bo.form.category,
        form_deleted=False,
        form_version_id=mock_address_bo.form_version_id,
        data=mock_address_bo.data,
        extra_data=mock_address_bo.extra_data,
        aid=mock_address_bo.aid,
        platform_creator=mock_address_bo.aid,
        feisuo_creator=mock_address_bo.feisuo_creator,
        creator_group=[],
        platform_creator_group=[],
        feisuo_creator_group=[],
        mid=mock_address_bo.mid,
        uid=mock_address_bo.uid,
        buyer_open_uid=mock_address_bo.buyer_open_uid,
        creator_type=Creator(mock_address_bo.creator_type),
        creator_user_id=mock_address_bo.creator_user_id,
        form_id=mock_address_bo.form_id,
        sid=mock_address_bo.sid,
        created_at=ts2date(mock_address_bo.created_at),
        update_user=mock_address_bo.update_user,
        platform_updator=mock_address_bo.update_user,
        feisuo_updator=mock_address_bo.feisuo_updator,
        updator_group=[],
        platform_updator_group=[],
        feisuo_updator_group=[],
        update_action="UNPROCESSED",
        update_reason=None,
        updated_at=ts2date(mock_address_bo.updated_at),
        current_job=None,
        current_jobs=[],
        actions={},
        shop_info=ShopBrief(sid=None, title=None, nick=None, platform="TAOBAO"),
        flag=mock_address_bo.flag
    )


@pytest.fixture
def mock_bo_detail(mock_address_bo):
    yield BusinessOrderDetail(
        id=mock_address_bo.id,
        form_id=mock_address_bo.form.id,
        name=mock_address_bo.form.name,
        created_at=ts2date(mock_address_bo.created_at),
        created_by=mock_address_bo.aid,
        updated_at=ts2date(mock_address_bo.updated_at),
        update_user=mock_address_bo.update_user,
        status=mock_address_bo.status.value,
        uid=mock_address_bo.uid,
        data=mock_address_bo.data,
        actions={},
        current_job=None,
        next_job=None,
        job_history=[],
        job_transition=[],
        shop={"platform": "TMALL"},
        deadline_info=None,
        tags=None,
        version={}
    )


@pytest.fixture
def mock_whitelist_enabled(mocker):
    mocker.patch("robot_processor.business_order.encryption.address_widget.AddressWidgetMask.need_mask",
                 return_value=True)
