import pytest


@pytest.fixture
def client_with_testing_flag_off():
    from robot_processor.app import app
    with app.app_context():
        original_testing_flag = app.testing
        app.testing = False
        yield app.test_client()
        app.testing = original_testing_flag


def test_error_handler(client_with_testing_flag_off):
    from robot_processor.app import app

    app._got_first_request = False
    @app.get('/v1/test_error_handler')
    def test_error_handler():
        raise Exception('test error')

    resp = client_with_testing_flag_off.get('/v1/test_error_handler')
    assert resp.status_code == 500
    body = resp.json
    assert '追踪码' in body.pop('error_display')
    assert body == {'data': None, 'error': 'unhandled error', 'succeed': False}
