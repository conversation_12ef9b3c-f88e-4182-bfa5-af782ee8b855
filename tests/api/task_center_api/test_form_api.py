from pytest import fixture

from robot_processor.ext import db
from robot_processor.form.models import FormShop


@fixture
def setup_data(client, shop_factory, form_factory):
    shops = shop_factory.create_batch(5, org_id=client.shop.org_id)
    forms = form_factory.create_batch(5)
    for shop, form in zip(shops, forms):
        form.subscribe(shop, True)
    db.session.commit()
    return shops, forms


def test_filter_form_names(client, setup_data):
    shops, forms = setup_data
    resp = client.get("/v1/task-center/forms/name", query_string={"sid": ",".join([shop.sid for shop in shops])})
    assert sorted([form.name for form in forms]) == sorted(resp.json["data"])


def test_filter_form_ids(client, setup_data):
    shops, forms = setup_data
    resp = client.get("/v1/task-center/forms/ids", query_string={"sid": ",".join([shop.sid for shop in shops])})
    form_dict = resp.json["data"]
    for form in forms:
        assert form_dict[form.name] == [form.id]


def test_filter_forms(client, setup_data, business_order_factory):
    shops, forms = setup_data
    resp = client.post(
        "/v1/task-center/forms", json={"shops": [{"sid": shop.sid, "platform": shop.platform} for shop in shops]}
    )
    form_shops = FormShop.query.filter(FormShop.sid.in_([shop.sid for shop in shops])).all()
    shop_channel_id_to_shop_mapping = {shop.channel_id: shop for shop in shops if shop.channel_id is not None}
    form_id_to_info_mapping: dict[int, list[dict]] = {}
    for form_shop in form_shops:
        shop = shop_channel_id_to_shop_mapping.get(form_shop.channel_id)
        if shop is None:
            continue
        if origin_info := form_id_to_info_mapping.get(form_shop.form_id):
            new_info = origin_info + [
                {
                    "status": form_shop.status,
                    "sid": shop.sid,
                    "platform": shop.platform,
                    "title": shop.title,
                }
            ]
        else:
            new_info = [
                {
                    "status": form_shop.status,
                    "sid": shop.sid,
                    "platform": shop.platform,
                    "title": shop.title,
                }
            ]
        form_id_to_info_mapping.update({form_shop.form_id: new_info})

    assert resp.json == {
        "forms": [
            {
                "form_id": form.id,
                "form_name": form.name,
                "form_info": form_id_to_info_mapping.get(form.id) or [],
                "form_version": "STASH",
            }
            for form in forms
        ]
    }

    form = forms[0]
    shop = shops[0]
    for form_shop in form_shops:
        if form_shop.form_id == form.id:
            form_shop.status = FormShop.Status.RESERVED
    db.session.commit()

    resp = client.post(
        "/v1/task-center/forms", json={"shops": [{"sid": shop.sid, "platform": shop.platform} for shop in shops]}
    )
    assert len(resp.json.get("forms")) == len(forms)

    business_order_factory.create(form_id=form.id, sid=shop.sid, deleted=False)
    db.session.commit()

    resp = client.post(
        "/v1/task-center/forms", json={"shops": [{"sid": shop.sid, "platform": shop.platform} for shop in shops]}
    )
    assert len(resp.json.get("forms")) == len(forms)
