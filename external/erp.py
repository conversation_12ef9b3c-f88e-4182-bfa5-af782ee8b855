from flask import Blueprint, jsonify

from external.schema import GetErpParam
from robot_processor.decorators import service_required
from robot_processor.shop.models import ErpInfo, Shop
from robot_processor.validator import validate

erp_api = Blueprint("service-erp", __name__, url_prefix="/v1/erp")


@erp_api.post("/get_meta")
@service_required
@validate
def get_erp_meta(body: GetErpParam):
    """
    rpa中控获取erp授权信息
    """
    stmt = ErpInfo.query.join(
        Shop, Shop.id == ErpInfo.shop_id
    ).filter(
        Shop.sid == body.sid,
        Shop.platform == body.platform.name,
        ErpInfo.erp_type == body.erp_type.name,
        ErpInfo.token.isnot(None)
    )
    if body.title:
        stmt = stmt.filter(Shop.title == body.title)
    erp_info = stmt.first()
    if not erp_info:
        return jsonify(
            success=False,
            data=None,
            error="No erp info found"
        )
    return jsonify(
        success=True,
        data={
            "token": erp_info.token,
            "meta": erp_info.meta
        },
        error=None
    )
