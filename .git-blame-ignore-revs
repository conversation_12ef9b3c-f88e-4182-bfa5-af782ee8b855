# This file contains SHAs of changes that should be ignored when e.g.
# running `git blame` on a file. Do not edit any of the existing commits.
#
# Use this file by running:
#
#     git blame --ignore-revs-file=.git-blame-ignore-revs <file>
#
# or by configuring `blame.ignoreRevsFile`:
#     git config blame.ignoreRevsFile .git-blame-ignore-revs
# The latter ought to also work with IDEs such as IntelliJ.


# bump sqlalemy
dd6beb62a3c7cb19d55856c25c618e43686248ba


# fix types 相关的 commits
97fc495955440c9c1ffb1d93589233d2abaf54c0
c1be9d1164c9ca4489b0e6af5f2f4938311fcfe5
774e44204d6b3628bbdbde1d1cceaf3b4ec7c96d
a44fb4105c62e43bbf76f93adad268532b41c83c
052ef6afd60b80f6661c0bfe5e2e98e0bcf9eed9
9c217f4a5e8924cac3c35b06927186f63e759958
1c6c8f806b83655297ce8138608b933a91184c6e
6ae569919f317cbdebca8142a38e0bd89d462324
cefcac53533c812d701f01190ef40184aa888b71
4e3628264f4511cd48b057d9be1c5a13b077631c
404f2cd1ccca83e164ba2ba552cd1a2dcbab30db
621725d40349546b859493a146cd94cc515114a0
0b98b294a41d00552acb873c16ddeb133826ea5b
5b4eb16bf13254e49b307a429c77c62729926304
e2263d0d7aaac71820ed59d4da50d68f9638b73e
af29eeba627f7ba2d08dbf1d3876ebdc2c406355
d5a421862aa0f173e17c3285096d8319557305ea
199fc52a440076b1fbfa70b17fb58dc9571f7609
9cd62a1d213ab07ab99414039a6adc09effecddd
acda117bec0ec24f79bcbd7a19e3a174efbadfb5
cab10f983662b2d57d7a344b83d06c3854920a2d
0689563c4c29ae17ad8d3bcd110014ad41afad01
c46e3e7d9b945e42ea68450f3150d45268be45f8
e312fdea9bc2cc0395ff95d287f6f6ece96aa9b5
2657d0ccb3a14e817c40b179e46750deb3692c38
15bfc3c2449dccb1efa56ebae239a9f31d426033
376b1df2a64121d049f2d0d32f7dec12dd6e00ae
07780df62d198eb627c3b006a47c36704f299dc7
cf1d8a89960e6fdd262bd15d34882b70ae6c6d2d
1994eb192e636fea4027aa60f8804c2b2afaa550
613cafb5f0420e9ef05e9e437586fbdb216cc321
0f6c3755e328163bed45b96b40d7cc5c8d1799d7
91d78d04a27cae7fc9af1a24bae503fe00fcd285
f8fc0d635d5ca33788d25daa20d5331811f6baea
720d9d01801ade1afd79744d5e6e973badf31441
1b1cad1f1ee06a5558a2f2058f1a04b4df10a9a5
199f485db34b794a3c8abb9b019142facf07eab1
bddbf9cdaafd0a1a1821c4d8465a09f06343d318
d37932bb0ed7f3aa66caeddb51507490380b6cc9
60ee9822db9ac0f88999240682b1791497b115c0
139cc3f5cf8fe19e60518c50d2c2efb6f0246bce
ad8ef034ad4f595fe8f8d0977d44bfa2f38cd2ca
0dc85fd998573bb0cd7204b0709c69bc742e4571
57e2e7cfa8d0a235335e872473328d848941d0ba
1b2478b9700235d8c00b1bdf99b6b9f7c43c5e9f
daf45136bd712a1da504b0a1fc978f347ca4c203
9ed34b769c61f0daffb7b058e954266103f51800
083adeb3f4f35ba2fb8807f113e557c6731c0d7b
e099f113495a00a4050501c56ae879cfd9f605b4
feba202c4027360b88763ee785d7822d72334c5e
38a8db300b4a1cd2f5b15769907720c0d77ed64c
70692067a470eef57eb396fa0b43fc3326d13c98
fe8fa284b3b3e11e877b7f1031121556cd98f7ce
28736f516ba310e6b8e5e84567bb735e2d89520f
5892a96e7dd9e7aaacc69eb6f1e36dfa403df469
386bb5067e435c35435f2e142de3bb1eb88ecedc
6b38f9ebe8a55acf5976345db8a4ca633efd5f85
9184a98b1d99a2bd31701fcb77f9fb0fb6de4dca
9e8ca199dc810c6ab65376a1f3013659a808a5b7
417825b0a51b46ea27d551e931688db2188065ea
4dd9473d96b421677ed7b65ff34c7e80e5b82d73
7d918f5a2d57357d344209f6f486a42e1d2bb043
049f2c31ec03a5090847486ecd8851168cab3605
2fb8cfcda8a6269a235a637fe2e39ac8d4ec0919
9dca0914e5a38b22ef553484024268f8e31eeda9
5d7416301efbd060f0baa0fc38e974d97e28343d
d6d7a9422b77bc2f38c9dae647bb62d14a58def5
87a9fdd86a9aac57e6a040c9b1dc56ed83dcf38e
d72e93cd3281ceb173f91791e30568dbd54e0baa
2726f2879dfb7cf99dc93f84755f27faccc7a248
fadffbe7ecf38c5f394b6080df2f8fbbc594224e
510621859649182d5f656f6079d1f4cad009ea01
e326f7371ad4c0d9c75f893ed3dbe98dd9773cf9
8e89b8af6158e0e04b84c4f333aa1fa683838988
42b935aa8489c9c998d6353354ebc288584cadc4
4ca68d3b4c269e3f66c050f8217185e0cf776ffe
1ca06514806bbda9ebfe8ac84f3e263bc3061a20
3b15ab38e9b5febca3fbe603a290957704231a3d
35ba9c321ddfe336d2e2c2df81fff64ff3efda61
e342bcd4be480da7c6302acf5786f4f78c9e3b05
44c1bf7ebde6dd8b75c49538d30bfccc744b0447
8283edffcc993028331e720bf70b4af05933a2a1
a1bbe61ef5c0d8ac8d3745fc73fd0304df59eb6a
998d489e3bffe838ed90b9fbb83e89c426dc95f8
ac782997df259ba9adc133baf5b1e18daab939ba
f677dc100de96610e38f3808ab75763fbb29c62e
b747eb95f23a8d33a7b8b11e08abb7748aac9814
c1a14eaba0546320131bf52096b6e206c4ad3d5a
47b9589d8f70173fa5bcde29c2dfcfa32ca7e529
8cd921e759b4020518471579ae5439c7e9d088a8
ac427381f799dba5cbd9ee606955a5c7ff82de2b
2318b577266b3de936eef60f7112246e124d2ed3
523b57ed3d09a40a8896b8b46612f1857cb9cc7a
