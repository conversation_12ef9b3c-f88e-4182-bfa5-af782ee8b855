"""存储 String -> String 的转换函数"""
from robot_processor.fs_type import basic as basic_fs_type, compose as compose_fs_type
from robot_processor.utils import unwrap_optional


def jst_logistics_info_copied_string_extractor(
    logistics_info_str: basic_fs_type.String,
) -> compose_fs_type.Table[basic_fs_type.String]:
    """雷度定制需求，需要从聚水潭复制过来的特定格式的多组物流信息字符串中抽取出来快递单号信息

    Args:
        logistics_info_str: 物流信息字符串，格式为：`物流公司,物流单号\n物流公司,物流单号\n物流公司,物流单号`
        超星韵达专用,463237976829977 超星韵达专用,463237977049807 超星韵达专用,463237977050986

    Returns:

    """
    logistics_no_list = list(
        map(
            lambda each_logistics_info_str: each_logistics_info_str.split(",")[-1],
            logistics_info_str.unwrap().split(),
        )
    )

    return unwrap_optional(
        compose_fs_type.Table[basic_fs_type.String]
        .from_python_value(logistics_no_list)
        .unwrap()
    )
