""" 管易云 erp"""
import time
from typing import Optional

from loguru import logger
from result import Result, Err, Ok

from robot_processor.enums import ErpType
from robot_processor.shop.models import Shop, ErpInfo
from rpa.conf import rpa_config as config
from rpa.mola import MolaClient


class GuanyiyunClient:
    def __init__(self, shop: Shop, erp_info: Optional["ErpInfo"] = None):
        """
          Raises:
              AssertionError
        """
        if not erp_info:
            erp_info = shop.erps.filter(
                ErpInfo.erp_type == ErpType.GUANYIYUN
            ).order_by(
                ErpInfo.id.desc()
            ).first()

        assert erp_info, f"店铺 {shop.sid} 无ERP授权信息"
        self.co_id = erp_info.meta.get("co_id")
        self.shop = shop

    def get_erp_tid(self, platform_code, is_approve) -> Result[str, str]:
        namespace = 'guanyi-erp'
        method = 'get-trades-list'
        erp_tid: Optional[str] = None
        date_types = config.RPA_GUANYIYUN_DATETYPE.split(',')
        sleep_time = config.RPA_GUANYIYUN_SLEEPTIME
        mola_client = MolaClient(self.co_id)  # type: ignore[arg-type]
        try:
            for date_type in date_types:
                query_params = {
                    "platformCode": platform_code,
                    "dateType": date_type,
                }
                # 获取原订单
                result = mola_client.call_namespace_method(namespace, method, query_params)
                match result:
                    case Ok(result_json):
                        logger.info(f"get_erp_tid result_json:{result_json}")
                        is_ok = result_json.get("success")
                        rows = result_json.get("result", {}).get("rows", [])
                        if is_ok and len(rows) > 0:
                            rows = [row for row in rows if row.get("approve") == is_approve]
                            trade_index = -1 if is_approve else 0
                            if len(rows) > 0:
                                erp_tid = str(rows[trade_index].get("code")).replace('SO', '', 1)
                        if erp_tid is not None:
                            break
                        time.sleep(sleep_time)
                    case Err():
                        return result
        except BaseException as exception:
            return Err(str(exception))
        if erp_tid is not None:
            return Ok(erp_tid)
        else:
            return Err("未找到符合条件的订单")

    def after_sale_approve(self, tid: str) -> Result[str, str]:
        namespace = 'guanyi-erp'
        method = 'approve-trade'
        erp_tid_res = self.get_erp_tid(tid, False)
        match erp_tid_res:
            case Err(error_message):
                return Err(f"订单查询失败:{tid} {error_message}")
            case Ok(erp_tid):
                mola_res = MolaClient(self.shop.sid).call_namespace_method(namespace, method, {"tid": f'{erp_tid}'})
                return mola_res.map(lambda _: erp_tid)

    def after_sale_upload(self, business_data: dict) -> Result[str, str]:
        tid = business_data.get("tid")
        match erp_tid_res := self.get_erp_tid(tid, True):
            case Err():
                return erp_tid_res
            case Ok(erp_tid):
                if not erp_tid:
                    return Err(f"订单查询失败:{tid} {erp_tid}")
                else:
                    business_data["tid"] = erp_tid
                    mola_res = MolaClient(self.shop.sid).call_namespace_method('guanyi-erp', 'create-reissue',
                                                                               business_data)
                    match mola_res:
                        case Err():
                            return mola_res
                        case Ok(body):
                            # 将补发单号回传。
                            result = body.get('result', {}).get('id', '')
                            return Ok(result)

    def get_trade_logistics_info(self, tid: str) -> Result[list, str]:
        # 获取原订单
        mola_res = MolaClient(self.shop.sid).call_namespace_method('guanyi-erp', 'get-trade-logistics-info',
                                                                   {"tid": tid})
        logger.info(f"get_logistics result_json:{mola_res}")
        match mola_res:
            case Err():
                return mola_res
            case Ok(body):
                try:
                    result = body.get("result")
                    if result is None or len(result) == 0:
                        return Err("尚未获取到物流单号")
                    return Ok(result)
                except Exception as e:
                    return Err(str(e))
