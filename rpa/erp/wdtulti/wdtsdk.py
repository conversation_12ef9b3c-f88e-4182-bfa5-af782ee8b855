import hashlib
import json
import time
import urllib.parse
import urllib.request


class WdtErpException(Exception):
    def __init__(self, message, code):
        super().__init__(message, code)


class Pager:
    __pageSize = 0
    __pageNo = 0
    __calcTotal = False

    def __init__(self, pageSize, pageNo=0, calcTotal=False):
        self.__pageSize = pageSize
        self.__pageNo = pageNo
        self.__calcTotal = calcTotal

    def getPageSize(self):
        return self.__pageSize

    def getPageNo(self):
        return self.__pageNo

    def getCalcTotal(self):
        return self.__calcTotal


class WdtErpClient:
    __url = ""
    __sid = ""
    __key = ""
    __secret = ""
    __salt = ""
    __version = "1.0"
    __multi_tenant_mode = False

    def __init__(
        self, sid: str, appkey: str, secret: str, url="http://wdt.wangdian.cn"
    ):
        if not url.endswith("openapi"):
            if url[-1] == "/":
                url = url + "openapi"
            else:
                url = url + "/openapi"

        self.__url = url
        self.__sid = sid
        self.__key = appkey

        arr = secret.split(":")
        self.__secret = arr[0]
        self.__salt = arr[1]

    def __make_sign(self, request: dict):
        request = dict(sorted(request.items()))

        sign = self.__secret
        for key in request:
            if key == "sign":
                continue
            sign = sign + key + str(request[key])

        sign = sign + self.__secret
        sign = hashlib.md5(sign.encode()).hexdigest()
        return sign

    def __build_query(self, request):
        params = ""
        for key in request:
            params = params + key + "=" + str(request[key]) + "&"
        return params

    def __execute(self, method, pager: Pager, args):
        req = {
            "sid": self.__sid,
            "key": self.__key,
            "salt": self.__salt,
            "method": method,
            "timestamp": int(time.time()) - 1325347200,
            "v": self.__version,
        }

        if pager is not None:
            req["page_size"] = pager.getPageSize()
            req["page_no"] = pager.getPageNo()
            req["calc_total"] = int(pager.getCalcTotal())

        data_wrapper = [args]
        req["body"] = json.dumps(data_wrapper)

        req["sign"] = self.__make_sign(req)

        del req["body"]

        headers = {"content-Type": "application/json"}
        if self.__multi_tenant_mode:
            headers["connection"] = "close"

        service_url = self.__url + "?" + self.__build_query(req)

        # resp = requests.post(self.__url, params=req, data=json.dumps(dataWrapper), headers=headers)
        data = bytes(json.dumps(data_wrapper), encoding="utf-8")
        req = urllib.request.Request(service_url, data=data, headers=headers)  # type: ignore[assignment]

        resp = urllib.request.urlopen(req)  # type: ignore[arg-type]
        resp_data = json.loads(resp.read().decode("utf-8"))

        # respData = json.loads(resp.text)

        if resp_data is None:
            raise WdtErpException("invalid response: {resp}", 0)

        if resp_data["status"] > 0:
            raise WdtErpException(str(resp_data["message"]), resp_data["status"])

        return resp_data

    def page_call(self, method, pager: Pager, args):
        resp = self.__execute(method, pager, args)

        if pager.getCalcTotal():
            return resp["data"]

        return resp

    def call(self, method, args):
        resp = self.__execute(method, None, args)  # type: ignore[arg-type]
        return resp["data"]

    def __str__(self):
        return f"WdtErpClient(sid={self.__sid})"
