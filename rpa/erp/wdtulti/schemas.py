import enum
from datetime import datetime
from typing import List
from typing import Optional

import arrow
from pydantic import BaseModel
from pydantic import Field
from pydantic import root_validator
from pydantic import validator

from robot_processor.job.job_model_wrapper import SKU
from robot_processor.utils import make_fields_optional


class OrderStatus(enum.Enum):
    线下退款 = 4
    已取消 = 5
    待转预订单 = 6
    待转已完成 = 7
    未付款 = 10
    待尾款 = 12
    等未付 = 15
    延时审核 = 16
    预订单前处理 = 19
    审核前处理 = 20
    自流转待发货 = 21
    异常订单 = 23
    换货预订单 = 24
    待处理预订单 = 25
    待分配预订单 = 27
    待客审 = 30
    待财审 = 35
    已审核 = 55
    已发货 = 95
    成本确认 = 96
    已过账 = 101
    已完成 = 110

    @classmethod
    def get_chinese_by_num(cls, value):
        # 通过value获取中文
        return cls._value2member_map_[int(value)].name


def get_gift_type_zh(value: int):
    return {
        0: "非赠品",
        1: "自动赠送",
        2: "手工赠送",
        4: "周期购赠送",
        8: "平台赠送",
        32: "阶梯满赠",
        64: "CRM追加赠送",
        65: "主品",
        128: "主品",
    }[value]


@make_fields_optional
class WdtultiOrderDetailModel(BaseModel):
    """
    旺店通订单详情
    """

    actual_num: str
    adjust: str
    api_goods_id: str
    api_goods_name: str
    api_spec_id: str
    api_spec_name: str
    barcode: str
    bind_oid: str
    cid: int
    commission: str
    created: str
    created_date: str
    delivery_term: int
    discount: str
    flag: int
    from_mask: int
    gift_type: int
    goods_id: int
    goods_name: str
    goods_no: str
    goods_type: int
    guarantee_mode: int
    img_url: str
    invoice_content: str
    is_consigned: str  # 本来是bool 强转
    is_received: int
    modified: str
    modified_date: str
    num: str
    order_price: str
    paid: str
    pay_status: int
    platform_id: int
    platform_status: int
    price: str
    print_suite_mode: int
    prop1: str
    prop2: str
    rec_id: int
    refund_num: str
    refund_status: int
    remark: str
    share_amount: str
    share_post_price: str
    share_price: str
    spec_code: str
    spec_id: int
    spec_name: str
    spec_no: str
    src_oid: str
    src_tid: str
    stock_state: int
    suite_amount: str
    suite_discount: str
    suite_id: int
    suite_name: str
    suite_no: str
    suite_num: str
    tax_rate: str
    trade_id: int
    weight: str

    chinese_gift_type: str

    @property
    def good_info(self):
        """组合商品的名称与数量"""
        return f"名称: {self.goods_name} 数量: {int(float(self.num or '0'))} \n"


@make_fields_optional
class WdtultiOrderModel(BaseModel):
    """
    旺店通旗舰版交易
    """

    bad_reason: int
    buyer_message: str
    cancel_reason: str
    check_time: str
    checker_name: str
    checkouter_name: str
    cod_amount: str
    commission: str
    created: str
    cs_remark: str
    currency: str
    customer_id: int
    customer_no: str
    customer_type: int
    delay_to_time: str
    delivery_term: int
    detail_list: List[WdtultiOrderDetailModel]
    discount: str
    estimate_consign_time: str
    ext_cod_fee: str
    fchecker_name: str
    fenxiao_nick: str
    fenxiao_type: int
    flag_name: str
    freeze_reason: str
    gift_mask: int
    goods_amount: str
    goods_cost: str
    goods_count: str
    goods_type_count: str
    id_card_type: int
    invoice_content: str
    invoice_id: int
    invoice_title: str
    invoice_type: int
    is_sealed: str  # 本来是bool 强转
    large_type: int
    logistics_code: str
    logistics_id: int
    logistics_name: str
    logistics_no: str
    logistics_type_name: str
    modified: str
    other_amount: str
    other_cost: str
    package_id: int
    package_name: str
    paid: str
    pay_time: str
    platform_id: int
    post_amount: str
    post_cost: str
    print_remark: str
    profit: str
    raw_goods_count: str
    raw_goods_type_count: int
    receivable: float
    receiver_area: str
    receiver_city: int
    receiver_district: int
    receiver_dtb: str
    receiver_province: int
    receiver_ring: str
    receiver_zip: str
    refund_status: int
    remark_flag: int
    revert_reason: str
    salesman_name: str
    shop_id: int
    shop_name: str
    shop_no: str
    shop_platform_id: int
    src_tids: str
    trade_id: int
    trade_no: str
    trade_status: str  # 返回的是int，但是为了方便合并改成了str
    trade_type: int  # 订单类型
    warehouse_no: str  # 仓库编号 不一定返回
    warehouse_type: int
    weight: str
    trade_mask: int
    trade_from: int
    consign_time: str

    # 以下为自定义属性
    detail_paid: str  # 订单明细的实付金额
    chinese_status: str  # 订单状态的中文
    all_goods_info: str  # 订单中的所有商品名称与数量，复合组件用
    warehouse_name: str
    provider_name: str
    goods_no: str  # 复合字段
    goods_name: str  # 复合字段
    spec_no: str  # 复合字段
    spec_name: str  # 复合字段
    is_split: bool  # 是否是拆单
    is_multi_packages: bool  # 是否是多包裹

    @staticmethod
    def is_convertible_to_int(s: str) -> bool:
        try:
            int(s)
            return True
        except ValueError:
            return False

    def __init__(self, **data):
        super().__init__(**data)
        self.detail_paid = str(sum([float(detail.paid) for detail in self.detail_list]))
        self.chinese_status = ",".join(
            OrderStatus.get_chinese_by_num(trade_status) for trade_status in self.trade_status.split(",")
        )
        rows = ""
        for detail in self.detail_list:
            rows += detail.good_info
        self.all_goods_info = rows
        if self.consign_time and WdtultiOrderModel.is_convertible_to_int(self.consign_time):
            self.consign_time = arrow.get(int(self.consign_time), tzinfo="Asia/Shanghai").format("YYYY-MM-DD HH:mm:ss")
        for detail in self.detail_list:
            detail.chinese_gift_type = get_gift_type_zh(detail.gift_type)

    @staticmethod
    def merge_orders(orders: List["WdtultiOrderModel"]):
        """合并订单"""
        # 商品级别的属性直接extend到detail_list里面，在外面合并
        merged_detail_list = []
        for order in orders:
            if order.detail_list:
                merged_detail_list.extend(order.detail_list)
        result = orders[0]
        result.detail_list = merged_detail_list
        # 订单级别的字段在这里合并
        result.detail_paid = str(sum([float(order.detail_paid) for order in orders if order.detail_paid]))
        result.warehouse_no = ",".join([order.warehouse_no for order in orders if order.warehouse_no])
        result.logistics_name = ",".join([order.logistics_name for order in orders if order.logistics_name])
        result.logistics_no = ",".join([order.logistics_no for order in orders if order.logistics_no])
        result.chinese_status = ",".join([order.chinese_status for order in orders if order.chinese_status])
        return result

    def __repr__(self):
        goods_info = "\n".join([f"名称：{detail.goods_name} 数量{detail.num}" for detail in self.detail_list])
        status_name = OrderStatus.get_chinese_by_num(self.trade_status)
        return (
            f"订单状态 {status_name} 物流公司 {self.logistics_name}\n 运单号 {self.logistics_no or '无运单号'}\n 发货商品"
            f" {goods_info}\n"
        )

    __str__ = __repr__


@make_fields_optional
class WdtultiOrderResp(BaseModel):
    order: List[WdtultiOrderModel]
    total_count: int


class TradeQueryParams(BaseModel):
    """订单查询

    References:
        https://open.wangdian.cn/qjb/open/apidoc/doc?path=sales.TradeQuery.queryWithDetail
    """

    start_time: str | None = None  # 修改起始时间
    end_time: str | None = None  # 修改结束时间
    warehouse_no: str | None = None  # 仓库编号
    status: str | None = None  # 订单状态
    trade_no: str | None = None  # 订单号
    shop_no: str | None = None  # 店铺编号
    logistics_no: str | None = None  # 物流单号
    src_tid: str | None = None  # 原始单号, 多个原始单号之间使用英文逗号分隔


class WarehouseModel(BaseModel):
    address: Optional[str]
    city: Optional[str]
    contact: Optional[str]
    created: Optional[str]
    district: Optional[str]
    is_disabled: Optional[bool]
    mobile: Optional[str]
    modified: Optional[int]
    name: Optional[str]
    prop1: Optional[str]
    prop2: Optional[str]
    province: Optional[str]
    remark: Optional[str]
    sub_type: Optional[int]
    telno: Optional[str]
    type: Optional[int]
    warehouse_id: Optional[int]
    warehouse_no: str
    zip: Optional[str]


class Pager:
    __page_size = 0
    __page_no = 0
    __calc_total = False

    def __init__(self, page_size=0, page_no=0, calc_total=False):
        self.__page_size = page_size
        self.__page_no = page_no
        self.__calc_total = calc_total

    def get_page_size(self):
        return self.__page_size

    def get_page_no(self):
        return self.__page_no

    def get_calc_total(self):
        return self.__calc_total

    def get_next(self):
        return Pager(page_size=self.get_page_size(), page_no=self.get_page_no() + 1, calc_total=self.get_calc_total())


class UltiPushDiscount(BaseModel):
    tid: str
    oid: str
    amount: float = 0
    detail: str


class UltiPushOrder(BaseModel):
    tid: str
    oid: str
    status: int
    refund_status: int
    goods_id: str
    spec_id: str
    goods_no: str = ""
    spec_no: str
    goods_name: str = ""
    spec_name: str = ""
    num: int = 0
    price: float
    discount: float = 0
    share_discount: float = 0
    total_amount: float = 0
    adjust_amount: float = 0
    refund_amount: float = 0
    remark: str = ""


class UltiPushTrade(BaseModel):
    tid: str
    process_status: int = 10
    trade_status: int = 30
    refund_status: int = 0
    pay_status: int = 2
    order_count: int = 0
    goods_count: int = 0
    pay_method: int = 1
    end_time: str
    buyer_nick: str
    receiver_name: str
    receiver_area: str
    receiver_address: str
    receiver_mobile: str
    post_amount: float = 0
    other_amount: float = 0
    discount: float = 0
    receivable: float = 0
    invoice_type: int = 0
    logistics_type: str = "-1"
    delivery_term: int
    remark: str
    remark_flag: int = 0
    # is_auto_wms: bool = False
    warehouse_no: str = ""
    trade_time: str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    pay_time: str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")


class TradePushResp(BaseModel):
    class Data(BaseModel):
        new_count: int
        chg_count: int


class UltiTradeQueryResp(BaseModel):
    class Response(BaseModel):
        class Data(BaseModel):
            class OrderInfo(BaseModel):
                logistics_no: str
                logistics_name: str

            order: List[OrderInfo]

        code: int


class GoodsDetail(BaseModel):
    merchant_no: str  # 商家编码
    num: int
    type: Optional[int] = 1
    price: Optional[float] = None


class TradeInfo(BaseModel):
    trade_no: str
    receiver_name: Optional[str] = None
    mobile: Optional[str] = None
    telno: Optional[str] = None  # 在原单加密的情况下，提供telno作为新的补发手机号，不然会报错“电话号码不能包含*”
    receiver_address: Optional[str] = None
    warehouse_no: Optional[str] = None
    print_remark: Optional[str] = None
    # direct_paid: Optional[float] = None
    post_amount: Optional[float] = None
    direct_paid: Optional[float] = None
    # 2023/09/16 之后不添加这个参数某些地址会在旺店通那边解析不通过
    is_ignore_warning: Optional[bool] = True
    logistics_code: Optional[str]
    trade_type: Optional[str] = None
    trade_label: Optional[str] = None


class ReissueOrder(BaseModel):
    trade_info: TradeInfo
    goods_detail_list: List[GoodsDetail]


class RefundOrder(BaseModel):
    oid: str
    spec_no: str
    refund_num: int


class Refund(BaseModel):
    tid: str
    type: int
    mode: int
    shop_no: str
    refund_no: Optional[str] = None
    warehouse_no: Optional[str] = None
    remark: Optional[str] = None
    logistics_no: Optional[str] = None
    logistics_name: Optional[str] = None
    no_duplicate_return_logistics_no: Optional[bool] = True
    flag_name: Optional[str] = None
    flag: Optional[bool] = False
    refund_reason: Optional[str] = None


class SwapGoods(BaseModel):
    merchant_no: str  # 商家编码
    num: int
    type: Optional[int] = 1
    price: Optional[float] = None
    amount: Optional[float] = None


class SwapOrder(BaseModel):
    warehouse_no: Optional[str] = None
    post_amount: Optional[float] = None
    logistics_code: Optional[str] = None
    receiver_address: Optional[str] = None
    receiver_name: Optional[str] = None
    mobile: Optional[str] = None
    print_remark: Optional[str] = None
    is_ignore_warning: Optional[bool] = True


class SwapOrderRequest(BaseModel):
    refund: Refund
    refund_order_list: List[RefundOrder]
    swap_order: SwapOrder
    swap_goods_list: List[SwapGoods]


@make_fields_optional
class SwapOrderResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        refund_id: int
        refund_no: str

    status: int
    message: str
    data: Data


class ReissueOrderResp(BaseModel):
    class Data(BaseModel):
        trade_no: str

    status: int
    message: Optional[str]
    data: Data


@make_fields_optional
class QueryShop(BaseModel):
    shop_id: int
    shop_name: str
    account_id: str
    shop_no: str


class QueryShopResp(BaseModel):
    class Data(BaseModel):
        total_count: Optional[int]
        details: List[QueryShop]

    status: int
    message: Optional[str]
    data: Data


class Provider(BaseModel):
    address: Optional[str]
    arrive_cycle_days: Optional[int]
    contact: Optional[str]
    created: Optional[str]
    deleted: Optional[int]
    email: Optional[str]
    fax: Optional[str]
    follower_name: Optional[str]
    is_disabled: Optional[bool]
    mobile: Optional[str]
    modified: Optional[str]
    provider_id: Optional[int]
    provider_name: Optional[str]
    provider_no: Optional[str]
    qq: Optional[str]
    remark: Optional[str]
    telno: Optional[str]
    wangwang: Optional[str]
    website: Optional[str]
    zip: Optional[str]


class ProviderGoodsInfo(BaseModel):
    avaliable_num: Optional[str]
    base_unit_name: Optional[str]
    brand_name: Optional[str]
    class_name: Optional[str]
    discount: Optional[str]
    follower_id: Optional[int]
    goods_name: Optional[str]
    goods_no: Optional[str]
    img_url: Optional[str]
    is_master: Optional[bool]
    last_price: Optional[str]
    last_purchase_time: Optional[str]
    last_second_price: Optional[str]
    lowest_price: Optional[str]
    min_purchase_num: Optional[str]
    num_14days: Optional[str]
    num_7days: Optional[str]
    num_all: Optional[str]
    num_month: Optional[str]
    order_num: Optional[str]
    origin: Optional[str]
    price: Optional[str]
    prop1: Optional[str]
    prop2: Optional[str]
    prop3: Optional[str]
    prop4: Optional[str]
    prop5: Optional[str]
    prop6: Optional[str]
    provider_goods_no: Optional[str]
    provider_no: Optional[str]
    provider_type: Optional[int]
    purchase_arrive_num: Optional[str]
    purchase_cycle_day: Optional[str]
    purchase_num: Optional[str]
    remark: Optional[str]
    retail_price: Optional[str]
    sending_num: Optional[str]
    short_name: Optional[str]
    spec_code: Optional[str]
    spec_name: Optional[str]
    spec_no: Optional[str]
    stock_num: Optional[str]
    subscribe_num: Optional[str]
    tax_rate: Optional[str]
    to_purchase_num: Optional[str]
    today_num: Optional[str]
    unit_name: Optional[str]
    unit_ratio: Optional[str]
    yesterday_num: Optional[str]


def get_skus_from_wdtulti_trades(origin_trade: WdtultiOrderModel, tid: str | None = None) -> List[SKU]:
    """
    解析旺店通旗舰版订单中的sku信息。(需要注意，这个方法可能不会获取到所有的商品)

    该函数会将该订单中的组合装相同的商品，转换成一个组合装商品，然后返回。
    其余单品则是会一一转化、并添加。

    如果填写了 tid，则会根据 tid 来过滤一下，排除非该 tid 的商品。（主要针对合并单）
    """
    skus = []
    suite_nos = []
    for good in origin_trade.detail_list or []:
        if tid is not None and good.src_tid != tid:
            continue
        if not good.suite_no:
            # 如果 suite_no 为空，则说明他是一个单品，直接将其添加到需要输出的商品列表中。
            sku = SKU(
                spu_id=good.goods_no,
                sku_id=good.spec_no,
                outer_sku_id=good.spec_no,
                qty=int(float(good.num)) if good.num is not None else None,
                pic=None,
                price=float(good.paid) if good.paid is not None else None,
                type=1,
                outer_spu_id=None,
                source="erp",
            )
            skus.append(sku)
        else:
            # 如果 suite_no 不为空，则表示该商品来源于一个组合装。
            # 如果该组合装已经添加，则跳过该商品。
            if good.suite_no in suite_nos:
                continue

            # 如果该组合装还未被添加，则将组合装的信息填入，并加入组合装列表进行记录，便于检测该组合装是否已经被添加。
            suite_nos.append(good.suite_no)
            sku = SKU(
                spu_id=str(good.suite_id),
                sku_id=good.suite_no,
                outer_sku_id=good.suite_no,
                qty=int(float(good.suite_num)) if good.suite_num is not None else None,
                pic=None,
                price=float(good.suite_amount) if good.suite_amount is not None else None,
                type=2,
                outer_spu_id=None,
                source="erp",
            )
            skus.append(sku)
    return skus


class StockQueryResp(BaseModel):
    class Data(BaseModel):
        spec_no: str = Field(description="商家编码")
        warehouse_no: str = Field(description="仓库编码")
        warehouse_name: str = Field(description="仓库名称")
        stock_num: int = Field(description="库存量")
        available_send_stock: int = Field(description="可发库存")

        @validator("stock_num", pre=True)
        def check_stock_num(cls, v):
            try:
                return int(float(v))
            except:  # noqa pylint: disable=bare-except
                return v

        @validator("available_send_stock", pre=True)
        def check_available_send_stock(cls, v):
            try:
                return int(float(v))
            except:  # noqa pylint: disable=bare-except
                return v

    data: List[Data]


class WdtultiOrderModelOutput(BaseModel):
    orders: List[WdtultiOrderModel]
    count: int


class ChangeRemarkResp(BaseModel):
    class Data(BaseModel):
        class Error(BaseModel):
            trade_no: str
            message: str

        message: str | None
        error: Optional[list[Error]]

    data: Data


class ChangeRemarkReq(BaseModel):
    class Data(BaseModel):
        trade_no: str
        cs_remark: str
        is_super_add: bool = False

    data: List[Data]


class QueryTradeLogReq(BaseModel):
    trade_no: str


@make_fields_optional
class QueryTradeLogResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        @make_fields_optional
        class Detail(BaseModel):
            created: str
            shortname: str
            type: int
            readable_type: str

        detail_list: List[Detail]

    data: Data


class LogisticDetail(BaseModel):
    logistics_no: str
    logistics_name: str


class LogisticInfo(BaseModel):
    total_count: Optional[int]
    details: List[LogisticDetail]


class QueryLogisticResp(BaseModel):
    status: int
    data: LogisticInfo


@make_fields_optional
class StockinRefundReturnLogisticsPackage(BaseModel):
    rec_id: int
    logistics_id: int
    logistics_name: str
    logistics_no: str
    operator_name: str
    created: str
    refund_remark: str
    stockin_time: str
    is_disabled: bool
    flag_name: str
    refund_no: str
    refund_status: str
    refund_stockin_status: int
    stockin_status: int
    check_time: str
    pre_stockin_no: str
    pre_stockin_status: int
    stockin_operator_name: str

    readable_refund_status: str
    readable_refund_stockin_status: str
    readable_stockin_status: str
    readable_pre_stockin_status: str

    @root_validator(pre=True)
    def generate_all_readable_str(cls, values):
        refund_status = values.get("refund_status")
        refund_status_map = {
            10: "已取消",
            20: "待审核",
            30: "已同意",
            40: "已拒绝",
            50: "待财审",
            60: "待收货",
            70: "部分到货",
            80: "待结算",
            90: "已完成",
        }
        if refund_status is not None:
            values["readable_refund_status"] = refund_status_map.get(refund_status)

        refund_stockin_status = values.get("refund_stockin_status")
        refund_stockin_status_map = {0: "未入库", 1: "待入库", 2: "部分入库", 3: "全部入库", 4: "终止入库(停止等待)"}
        if refund_stockin_status is not None:
            values["readable_refund_stockin_status"] = refund_stockin_status_map.get(refund_stockin_status)

        stockin_status = values.get("stockin_status")
        stockin_status_map = {
            -2: "待处理",
            10: "已取消",
            20: "编辑中",
            30: "待审核",
            37: "待质检",
            40: "质检待确认",
            80: "已完成",
        }
        if stockin_status is not None:
            values["readable_stockin_status"] = stockin_status_map.get(stockin_status)

        pre_stockin_status = values.get("pre_stockin_status")
        pre_stockin_status_map = {
            -2: "待处理",
            10: "已取消",
            20: "编辑中",
            30: "待审核",
            37: "待质检",
            40: "质检待确认",
            80: "已完成",
        }
        if pre_stockin_status is not None:
            values["readable_pre_stockin_status"] = pre_stockin_status_map.get(pre_stockin_status)

        return values


@make_fields_optional
class StockinRefundReturnLogisticsPackageData(BaseModel):
    total_count: int
    record: List[StockinRefundReturnLogisticsPackage]


@make_fields_optional
class StockinRefundReturnLogisticsPackageQueryResp(BaseModel):
    status: int
    data: StockinRefundReturnLogisticsPackageData


@make_fields_optional
class LogisticsDetail(BaseModel):
    calc_weight: str
    height: str
    length: str
    logistics_id: int
    logistics_name: str
    logistics_no: str
    package_name: str
    postage: str
    rec_id: int
    remark: str
    stockout_id: int
    volume: str
    weight: str
    width: str


@make_fields_optional
class StockoutOrderDetail(BaseModel):
    barcode: str
    base_unit_id: int
    batch_id: int
    brand_name: str
    brand_no: str
    class_name: str
    cost_price: str
    created_date: str
    discount: str
    from_mask: int
    gift_type: int
    good_prop1: str
    good_prop2: str
    good_prop3: str
    good_prop4: str
    good_prop5: str
    good_prop6: str
    goods_amount: str
    goods_count: str
    goods_id: int
    goods_name: str
    goods_no: str
    goods_type: int
    is_examined: int
    is_package: bool
    market_price: str
    modified_date: str
    num: str
    num2: str
    paid: str
    platform_id: int
    position_details_list: List[LogisticsDetail]
    position_id: int
    prop1: str
    prop2: str
    prop3: str
    prop4: str
    prop5: str
    prop6: str
    rec_id: int
    refund_status: str
    remark: str
    sale_order_id: int
    scan_type: int
    sell_price: str
    share_amount: str
    share_post_amount: str
    share_price: str
    spec_code: str
    spec_id: int
    spec_name: str
    spec_no: str
    src_oid: str
    src_order_detail_id: int
    src_order_type: int
    src_tid: str
    stockout_id: int
    suite_no: str
    tax_rate: str
    total_amount: str
    unit_id: int
    unit_name: str
    unit_ratio: str
    weight: str


@make_fields_optional
class StockoutDetail(BaseModel):
    bad_reason: int
    batch_no: str
    block_reason: int
    buyer_message: str
    calc_post_cost: str
    cod_amount: str
    consign_status: int
    consign_time: str
    consigner_name: str
    created: str
    created_date: str
    cs_remark: str
    currency: str
    custom_type: int
    customer_id: int
    customer_no: str
    delivery_term: int
    details_list: List[StockoutOrderDetail]  # 商品
    discount: str
    employee_no: str
    error_info: str
    examiner_name: str
    fenxiao_nick: str
    flag_name: str
    goods_count: str
    goods_total_amount: str
    goods_total_cost: str
    goods_type_count: int
    id_card_type: int
    invoice_content: str
    invoice_id: int
    invoice_title: str
    invoice_type: int
    logistics_code: str
    logistics_id: int
    logistics_list: List[LogisticsDetail]  # 物流
    logistics_name: str
    logistics_no: str
    logistics_print_status: int
    logistics_type: int
    modified: str
    operator_id: int
    order_no: str
    order_type: int
    outer_no: str
    package_fee: str
    packager_name: str
    paid: str
    pay_time: str
    pick_group_name: str
    picker_name: str
    picklist_no: str
    picklist_seq: int
    platform_id: int
    post_amount: str
    post_fee: str
    print_remark: str
    printer_name: str
    receivable: str
    receiver_area: str
    receiver_city: int
    receiver_country: int
    receiver_district: int
    receiver_dtb: str
    receiver_province: int
    receiver_zip: str
    refund_status: int
    remark: str
    sendbill_template_id: int
    seq_no: int
    shop_id: int
    shop_name: str
    shop_no: str
    shop_platform_id: int
    shop_remark: str
    src_order_id: int
    src_order_no: str
    src_trade_no: str
    status: int
    stock_check_time: str
    stockout_id: int
    sub_platform_id: int
    tax: str
    tax_rate: str
    trade_from: int
    trade_id: int
    trade_label: str
    trade_no: str
    trade_status: int
    trade_time: str
    trade_type: int
    warehouse_id: int
    warehouse_mapping_code: str
    warehouse_name: str
    warehouse_no: str
    warehouse_type: int
    weight: str


class WDTUltiShop(BaseModel):
    shop_id: int
    shop_name: str
    shop_no: str
    platform_id: int
    sub_platform_id: int
    is_disabled: bool


class GetShopsResponseData(BaseModel):
    total_count: int
    details: list[WDTUltiShop]


class GetShopsResponse(BaseModel):
    status: int
    data: GetShopsResponseData


class DetailListItem(BaseModel):
    rec_id: int
    refund_id: int
    platform_id: int
    oid: str
    tid: str
    trade_no: str
    checked_cost_price: float
    num: float
    price: float
    original_price: float
    refund_num: float
    total_amount: float
    refund_amount: float
    is_guarantee: bool
    goods_no: str
    goods_name: str
    spec_name: str
    spec_no: str
    spec_code: str
    barcode: str
    stockin_num: float
    remark: str
    api_goods_name: str
    sales_trade_id: int
    api_spec_name: str
    trade_order_id: int
    modified: int
    goods_id: Optional[str]
    spec_id: Optional[str]
    sys_goods_id: int
    sys_spec_id: int
    discount: float
    paid: float
    suite_id: int
    suite_no: str
    suite_name: str
    suite_num: float
    created: str
    modified_date: str
    raw_refund_no: str
    raw_refund_nos: str


@make_fields_optional
class RefundQueryOrder(BaseModel):
    warehouse_type: int
    provider_refund_no: str
    detail_list: List[DetailListItem]
    refund_no: str
    bad_reason: int
    return_telno: Optional[str]
    type: int
    trade_id: int
    modified: int
    shop_no: str
    created: int
    trade_no_list: str
    return_logistics_no: str
    return_goods_amount: float
    modified_date: str
    shop_platform_id: int
    refund_id: int
    shop_id: int
    src_tids: str
    actual_refund_amount: float
    refund_time: str
    receive_amount: float
    pay_id: str
    status: int
    check_time: str
    return_mask_info: str
    tmp_data: Optional[int]
    tid_list: str
    remark: str
    sub_platform_id: int
    stockin_status: int
    flag_name: str
    return_goods_count: float
    receiver_telno: str
    receiver_name: str
    refund_reason: str
    return_warehouse_id: int
    note_count: int
    from_type: int
    raw_refund_nos: str
    consign_mode: int
    guarantee_refund_amount: float
    return_logistics_name: str
    settle_time: str
    reason_id: int
    buyer_nick: str
    operator_name: str
    revert_reason: int
    return_warehouse_no: str
    direct_refund_amount: float
    platform_id: str
    sync_return: Optional[bool]
    customer_name: str
    reason_name: str
    customer_id: int
    return_mask: Optional[int]
    revert_reason_name: str

    readable_type: str  # 非API提供
    readable_stockin_status: str  # 非API提供
    readable_status: str  # 非API提供
    readable_process_status: str  # 非API提供

    @root_validator(pre=True)
    def generate_all_readable_str(cls, values):
        _type = values.get("type")
        type_map = {1: "售前退款", 2: "退货", 3: "换货", 4: "退款不退货", 6: "保价退款"}
        values["readable_type"] = type_map.get(_type) or ""

        stockin_status = values.get("stockin_status")
        stockin_status_map = {0: "无需入库", 1: "待入库", 2: "部分入库", 3: "全部入库", 4: "终止入库"}
        values["readable_stockin_status"] = stockin_status_map.get(stockin_status) or ""

        status = values.get("status")
        status_map = {
            10: "已取消",
            20: "待审核",
            30: "已审核",
            40: "已推送",
            80: "已结算",
            81: "待回传",
            85: "待过账",
            86: "已过账",
            87: "成本确认",
            90: "已完成",
        }
        values["readable_status"] = status_map.get(status) or ""

        process_status = values.get("process_status")
        process_status_map = {0: "待递交", 15: "已提交", 25: "待回传", 35: "已回传"}
        values["readable_process_status"] = process_status_map.get(process_status) or ""
        return values


@make_fields_optional
class RefundQueryResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        total_count: int
        order: List[RefundQueryOrder]

    message: str
    status: int
    data: Data


@make_fields_optional
class RawRefundQueryOrder(BaseModel):
    tid: str
    shop_id: int
    shop_no: str
    shop_name: str


@make_fields_optional
class RawRefundQueryResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        total_count: int
        order: List[RawRefundQueryOrder]

    message: str
    status: int
    data: Data


class RefundOrderDetail(BaseModel):
    oid: str
    price: str
    refund_order_id: int
    refund_order_stockin_num: str
    spec_no: str
    stockin_num: str
    stockin_order_detail_id: int


@make_fields_optional
class StockinOrderDetail(BaseModel):
    adjust_num: str
    base_unit_id: int
    base_unit_name: str
    batch_id: int
    batch_no: str
    brand_name: str
    brand_no: str
    checked_cost_price: str
    created: str
    defect: bool
    expect_num: str
    expire_date: str
    goods_id: int
    goods_name: str
    goods_no: str
    goods_unit: str
    logistics_id: int
    logistics_name: str
    logistics_no: str
    modified: str
    num: str
    num2: str
    org_stockin_detail_id: int
    position_id: int
    position_no: str
    production_date: str
    prop1: str
    prop2: str
    prop3: str
    prop4: str
    prop5: str
    prop6: str
    rec_id: int
    refund_amount: str
    refund_detail_id: str
    refund_order_detail_list: List[RefundOrderDetail]
    remark: str
    right_num: str
    spec_code: str
    spec_id: int
    spec_name: str
    spec_no: str
    src_order_id: int
    src_order_type: int
    stockin_id: int
    stockin_num: str
    total_cost: str
    unit_id: int
    unit_ratio: str
    validity_days: int
    warehouse_id: int

    num_int: int

    @root_validator(pre=True)
    def pre(cls, values):
        if values.get("num") is not None:
            num_int = int(values["num"])
            values["num_int"] = num_int
        return values


@make_fields_optional
class StockinOrder(BaseModel):
    actual_refund_amount: str
    adjust_num: str
    attach_type: int
    check_operator_id: int
    check_operator_name: str
    check_time: str
    created: str
    created_time: str
    customer_name: str
    customer_no: str
    details_list: List[StockinOrderDetail]
    fenxiao_nick: str
    flag_id: int
    flag_name: str
    goods_count: str
    goods_type_count: int
    logistics_id: int
    logistics_name: str
    logistics_no: str
    modified: str
    nick_name: str
    note_count: int
    operator_id: int
    operator_name: str
    order_no: str
    process_status: int
    prop3: str
    reason: str
    reason_id: int
    refund_amount: str
    refund_no: str
    remark: str
    seq_no: str
    shop_id: int
    shop_name: str
    shop_no: str
    shop_platform_id: int
    shop_remark: str
    src_order_id: int
    src_order_no: str
    src_order_type: int
    status: int
    stockin_id: int
    sub_platform_id: int
    tid_list: str
    total_goods_stockin_num: str
    total_price: str
    trade_no_list: str
    warehouse_id: int
    warehouse_name: str
    warehouse_no: str

    readable_process_status: str  # 非API提供
    readable_status: str  # 非API提供

    @root_validator(pre=True)
    def generate_all_readable_str(cls, values):
        process_status = values.get("process_status")
        process_status_map = {
            10: "已取消",
            20: "待审核",
            30: "已同意",
            40: "已拒绝",
            50: "待财审",
            60: "待收货",
            63: "待推送",
            64: "推送失败",
            65: "委外待收货",
            69: "待收货(已结算)",
            70: "部分到货",
            71: "部分到货(已结算)",
            80: "待结算",
            90: "已完成",
        }
        values["readable_process_status"] = process_status_map.get(process_status) or ""
        status = values.get("status")
        status_map = {10: "已取消", 20: "编辑中", 30: "待客审", 60: "待结算", 80: "已完成"}
        values["readable_status"] = status_map.get(status) or ""
        return values


@make_fields_optional
class StockinRefundQueryResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        order: List[StockinOrder]
        total_count: int

    data: Data
    message: str
    status: int


@make_fields_optional
class Spec(BaseModel):
    spec_id: str
    spec_no: str = Field(description="代表单品（SKU）所有属性的唯一编号，用于系统单品的区分")
    spec_name: str = Field(description="规格名称")
    retail_price: float = Field(description="零售价")
    img_url: str = Field(description="图片url")
    deleted: int = Field(description="0代表未删除  大于0代表已删除")


@make_fields_optional
class Goods(BaseModel):
    goods_id: int
    goods_no: str = Field(description="代表SPU所有属性的唯一编号，用于系统货品的区分")
    goods_name: str = Field(description="货品名称")
    short_name: str = Field(description="货品简称")
    spec_list: list[Spec]


@make_fields_optional
class QuerySkuResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):

        goods_list: list[Goods]

    data: Data


@make_fields_optional
class Suite(BaseModel):
    @make_fields_optional
    class SpecForSuite(BaseModel):
        suite_id: int
        spec_no: str = Field(description="商家编码")
        spec_name: str = Field(description="规格名称")
        num: float = Field(description="数量")
        deleted: int = Field(description="删除后自增生成的id值")

    suite_id: int
    suite_no: str = Field(description="组合装商家编码")
    suite_name: str = Field(description="组合装名称")
    short_name: str = Field(description="组合装简称")
    retail_price: float = Field(description="零售价")
    detail_list: list[SpecForSuite]
    deleted: int = Field(description="大于0表示已删除，删除后自增生成的id值")


@make_fields_optional
class QuerySuiteResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        suite_list: list[Suite]

    data: Data


@make_fields_optional
class QueryStockResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        @make_fields_optional
        class Detail(BaseModel):
            spec_no: str
            stock_num: int
            warehouse_name: str
            available_send_stock: int

            @validator("stock_num", pre=True)
            def check_stock_num(cls, v):
                try:
                    return int(float(v))
                except:  # noqa pylint: disable=bare-except
                    return v

            @validator("available_send_stock", pre=True)
            def check_available_send_stock(cls, v):
                try:
                    return int(float(v))
                except:  # noqa pylint: disable=bare-except
                    return v

        total_count: int
        detail_list: list[Detail]

    data: Data
