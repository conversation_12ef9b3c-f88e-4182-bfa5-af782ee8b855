import hashlib
import json
import time
import urllib
from datetime import datetime
from datetime import timed<PERSON><PERSON>
from typing import Any
from typing import List
from typing import Optional
from typing import Type
from typing import TypeVar

from loguru import logger
from pydantic import BaseModel
from result import Err
from result import Ok
from result import Result

from robot_processor.client_mixins import Session
from robot_processor.enums import ErpType
from robot_processor.error.client_request import WdtRequestError
from robot_processor.shop.models import ErpInfo
from robot_processor.shop.schema import WDTUltiGrantMeta
from robot_processor.utils import ts2date
from rpa.conf import rpa_config as config
from rpa.erp.wdtulti import WarehouseModel
from rpa.erp.wdtulti import WdtultiOrderResp
from rpa.erp.wdtulti.schemas import ChangeRemarkReq
from rpa.erp.wdtulti.schemas import ChangeRemarkResp
from rpa.erp.wdtulti.schemas import GetShopsResponse
from rpa.erp.wdtulti.schemas import Pager
from rpa.erp.wdtulti.schemas import Provider
from rpa.erp.wdtulti.schemas import ProviderGoodsInfo
from rpa.erp.wdtulti.schemas import QueryLogisticResp
from rpa.erp.wdtulti.schemas import QueryShop
from rpa.erp.wdtulti.schemas import QueryShopResp
from rpa.erp.wdtulti.schemas import QuerySkuResp
from rpa.erp.wdtulti.schemas import QueryStockResp
from rpa.erp.wdtulti.schemas import QuerySuiteResp
from rpa.erp.wdtulti.schemas import QueryTradeLogReq
from rpa.erp.wdtulti.schemas import QueryTradeLogResp
from rpa.erp.wdtulti.schemas import RawRefundQueryResp
from rpa.erp.wdtulti.schemas import RefundQueryResp
from rpa.erp.wdtulti.schemas import ReissueOrder
from rpa.erp.wdtulti.schemas import ReissueOrderResp
from rpa.erp.wdtulti.schemas import StockinRefundQueryResp
from rpa.erp.wdtulti.schemas import StockinRefundReturnLogisticsPackageQueryResp
from rpa.erp.wdtulti.schemas import StockoutDetail
from rpa.erp.wdtulti.schemas import StockQueryResp
from rpa.erp.wdtulti.schemas import SwapOrderRequest
from rpa.erp.wdtulti.schemas import SwapOrderResp
from rpa.erp.wdtulti.schemas import TradePushResp
from rpa.erp.wdtulti.schemas import TradeQueryParams
from rpa.erp.wdtulti.schemas import UltiPushDiscount
from rpa.erp.wdtulti.schemas import UltiPushOrder
from rpa.erp.wdtulti.schemas import UltiPushTrade
from rpa.erp.wdtulti.schemas import WDTUltiShop

RespT = TypeVar("RespT", bound=BaseModel)


class WdtQmRateLimitError(WdtRequestError):
    pass


class WdtRateLimitError(WdtRequestError):
    pass


# todo fixme 写的太乱了，逐步拆分到WdtUltiOpenAPIClient（旺店通自己的开放平台接口）和WdtUltiQM（奇门接口）
class WdtUltiClient:
    session = Session()

    def __init__(self, sid: str):
        """
        Raises:
            AssertionError
        """
        erp_info = ErpInfo.get_by_sid(sid, ErpType.WDTULTI)
        assert erp_info, f"店铺 {sid} 无旺店通旗舰版授权信息"
        self.sid = erp_info.meta["sid"]
        self.wdt_appkey = erp_info.meta["wdt_appkey"]
        self.wdt_secret = erp_info.meta["wdt_secret"]
        self.wdt_salt = erp_info.meta["wdt_salt"]
        self.after_sale_shop_no = erp_info.meta["after_sale_shop_no"]

    @property
    def endpoint(self):
        return config.WDTULTI_ENDPOINT

    @property
    def logistics_type_kv(self):
        return config.WDTULTI_LOGISTICS_TYPE

    @property
    def target_app_key(self):
        return config.WDTULTI_TARGET_APP_KEY

    @property
    def app_key(self):
        return config.WDTULTI_APP_KEY

    @property
    def app_secret(self):
        return config.WDTULTI_APP_SECRET

    @property
    def wdt3_customer_id(self):
        return config.WDTULTI_STG_WDT3_CUSTOMER_ID

    @property
    def request_timeout(self) -> int:
        return config.WDTULTI_REQUEST_TIMEOUT

    def _to_single_str(self, result, obj):  # todo：代码优化 @lixun.wang
        if isinstance(obj, dict):
            obj_sort = self.sorted_params(obj)
            for k in obj_sort.keys():
                if isinstance(obj_sort[k], str) and WdtUltiClient._check_json_format(obj_sort[k]):
                    result.append(k)
                    self._to_single_str(result, json.loads(obj_sort[k]))
                elif isinstance(obj_sort[k], list) or isinstance(obj_sort[k], dict):
                    result.append(k)
                    self._to_single_str(result, obj_sort[k])
                else:
                    if str(obj_sort[k]) == 0.0 or str(obj_sort[k]) == 1.0:
                        result.append(f"{k}0")
                    else:
                        result.append(f"{k}{str(obj_sort[k])}")
        elif isinstance(obj, list):
            for objc in obj:
                if (
                    isinstance(objc, UltiPushDiscount)
                    or isinstance(objc, UltiPushOrder)
                    or isinstance(objc, UltiPushTrade)
                ):
                    self._to_single_str(result, objc.dict())
                else:
                    self._to_single_str(result, objc)
        else:
            if not obj:
                result.append("false")
            else:
                result.append(str(obj))

    @staticmethod
    def _check_json_format(raw_msg):
        """
        用于判断一个字符串是否符合Json格式
        :param self:
        :return:
        """
        if isinstance(raw_msg, str):  # 首先判断变量是否为字符串
            try:
                json.loads(raw_msg)
            except ValueError:
                return False
            return True
        else:
            return False

    def _build_app_params_trade_push(self, method, discount, order, trade):
        params = {
            "datetime": ts2date(time.time()),
            "discountList": discount,
            "rawTradeList": trade,
            "rawTradeOrderList": order,
            "shopNo": self.after_sale_shop_no,
            "wdt_appkey": self.wdt_appkey,
            "wdt_salt": self.wdt_salt,
            "method": method,
        }
        params["wdt_sign"] = self._wdt_sign(params)
        return params

    def _build_app_params_trade_query(self, method, data):
        params = {
            "datetime": ts2date(time.time()),
            "method": method,
            "pager": '{"page_no":1,"page_size":10}',
            "params": f'{{"src_tid":"{data["src_tid"]}"}}',
            "wdt_appkey": self.wdt_appkey,
            "wdt_salt": self.wdt_salt,
        }
        params["wdt_sign"] = self._wdt_sign(params)
        return params

    def _build_app_params_warehouse_query(self, method, data):
        params = {
            "datetime": ts2date(time.time()),
            "method": method,
            "pager": '{"page_no":1,"page_size":10}',
            "params": f'{{"warehouse_no":"{data["warehouse_no"]}"}}',
            "wdt_appkey": self.wdt_appkey,
            "wdt_salt": self.wdt_salt,
        }
        params["wdt_sign"] = self._wdt_sign(params)
        return params

    def sorted_params(self, params):
        sorted_params = {}
        for key in sorted(params):
            sorted_params[key] = params[key]
        return sorted_params

    def _wdt_sign(self, params):
        single_str_list: list[str] = []
        self._to_single_str(single_str_list, params)
        single_str = "".join(single_str_list)
        single_str = self.wdt_secret + single_str + self.wdt_secret
        sign_str = hashlib.md5(single_str.encode("utf-8")).hexdigest()
        return sign_str

    def _sign(self, params):
        params = self.sorted_params(params)
        sign_str = str().join([f"{k}{v}" for k, v in params.items()])
        sign_str = self.app_secret + sign_str + self.app_secret
        sign = hashlib.md5(sign_str.encode("utf-8")).hexdigest().upper()
        return sign

    def _request(self, method, trade_data, app_params):
        """
        Raises:
            AssertionError
            WdtRequestError
        """
        sys_params = {
            "app_key": self.app_key,
            "format": "json",
            "method": method,
            "partner_id": "top-sdk-java-20201014",
            "sign_method": "md5",
            "target_app_key": self.target_app_key,
            "timestamp": ts2date(time.time()),
            "v": "2.0",
        }

        app_params["wdt3_customer_id"] = self.wdt3_customer_id if self.wdt3_customer_id else self.sid

        params = sys_params.copy()
        params.update(app_params)

        sign_str = self._sign(params)
        params["sign"] = sign_str

        full_url = (
            f"{self.endpoint}?app_key={params['app_key']}"
            f"&method={params['method']}&v=2.0&sign={params['sign']}"
            f"&timestamp={params['timestamp']}"
            f"&target_app_key={params['target_app_key']}"
            f"&partner_id={params['partner_id']}&"
            f"format={params['format']}"
            f"&sign_method={params['sign_method']}"
        )

        logger.info(f"wdtulti full_url:{full_url}")
        resp = self.session.post(full_url, data=app_params, timeout=self.request_timeout)

        return self._resp(method=method, params=params, data=trade_data, resp=resp)

    def _resp(self, method, params, data, resp):
        resp_json = resp.json()
        logger.info(f"wdtulti resp_json:{resp_json}")
        if "code" in resp_json and int(resp_json["code"]) != 0:
            raise WdtRequestError(req=(method, params, data), res=resp_json, message=resp_json["message"])
        #   处理异常
        #   {"response": {
        #     "code": 15,
        #     "flag": "failure",
        #     "message": "Remote service error",
        #     "request_id": "15t07anbi7d27",
        #     "sub_code": "server.app-error",
        #     "sub_message": "超过每分钟最大调用频率限制,请稍后重试"
        #   }}
        if int(resp_json.get("response", {}).get("code", 0)) != 0:
            raise WdtRequestError(
                req=(method, params, data),
                res=resp_json,
                message=resp_json.get("response", {}).get("message", "")
                + resp_json.get("response", {}).get("sub_message", ""),
            )
        return resp_json

    def trade_push(self, push_discount, push_order, push_trade: UltiPushTrade) -> TradePushResp:
        discount_list = []
        order_list = []
        trade_list = []
        # 转换物流公司为物流公司编码，待前端下拉支持kv接口后移除
        if self.logistics_type_kv:
            data_json = json.loads(self.logistics_type_kv)
            for k in data_json:
                if push_trade.logistics_type == data_json[k]:
                    push_trade.logistics_type = k
        for d in push_discount:
            discount_list.append(self.sorted_params(d.dict()))
        for o in push_order:
            order_list.append(self.sorted_params(o.dict()))
        trade_list.append(self.sorted_params(push_trade.dict()))

        method = "wdt.sales.rawtrade.pushself"
        app_params = self._build_app_params_trade_push(
            method,
            discount=json.dumps(discount_list, ensure_ascii=False),
            order=json.dumps(order_list, ensure_ascii=False),
            trade=json.dumps(trade_list, ensure_ascii=False),
        )

        resp_json = self._request(method, trade_data=json.dumps(trade_list, ensure_ascii=False), app_params=app_params)

        if int(resp_json.get("response", {}).get("data", {}).get("new_count", 0)) < 1:
            raise WdtRequestError(req=(method, app_params, trade_list), res=resp_json, message=resp_json["response"])
        return resp_json

    def after_sale_upload(self, distinct_list, order_list, trade) -> Result[None, str]:
        """售后上传
        Raises:
            AssertionError
            WdtRequestError
        """
        try:
            self.trade_push(distinct_list, order_list, trade)
        except Exception as e:
            return Err(str(e))

        return Ok(None)

    def trade_query(self, wdt_tid: str | list, logistics_no: Optional[str] = None) -> dict:
        method = "wdt.sales.tradequery.querywithdetail"
        if isinstance(wdt_tid, list):
            wdt_tid = wdt_tid[0].get("tid")
        data = {"src_tid": wdt_tid}
        if logistics_no:
            data["logistics_no"] = logistics_no
        app_params = self._build_app_params_trade_query(method, data)
        return self._request(
            method,
            data,
            app_params,
        )

    def warehouse_query(self, warehouse_no: str) -> dict:
        method = "wdt.setting.warehouse.querywarehouse"
        data = {"warehouse_no": warehouse_no}
        app_params = self._build_app_params_warehouse_query(method, data)
        return self._request(
            method,
            data,
            app_params,
        )

    def after_sale_logistics(self, wdt_tid: str) -> Result[dict, str]:
        """售后上传
        Raises:
            AssertionError
            WdtRequestError
        """
        try:
            resp_json = self.trade_query(wdt_tid)
            if resp_json["response"]["data"]["total_count"] == 0:
                return Err(f"订单：{wdt_tid}，没有找到")
            logistics_no = resp_json["response"]["data"]["order"][0]["logistics_no"]
            logistics_name = resp_json["response"]["data"]["order"][0]["logistics_name"]
            wh_no = resp_json["response"]["data"]["order"][0].get("warehouse_no")
            wh_name = ""
            if wh_no:
                wh_resp_json = self.warehouse_query(wh_no)
                logger.info(f'"wh_resp_json:" {wh_resp_json}')
                # details 可能为空
                # {'response': {'data': {'details': [], 'total_count': 0}, 'message': 'OK', 'status': 0}}
                if int(wh_resp_json["response"]["status"]) == 0:
                    if wh_resp_details := wh_resp_json["response"]["data"]["details"]:
                        wh_name = wh_resp_details[0]["name"]
            logistics = dict(
                logistics_no=str(logistics_no),
                logistics_name=str(logistics_name),
                warehouse_no=str(wh_no),
                warehouse_name=str(wh_name),
            )
            return Ok(logistics)
        except (AssertionError, WdtRequestError) as e:
            return Err(str(e))


class WdtUltiOpenPlatformAPIClient:
    session = Session()

    def __init__(self, meta: str | dict):
        """
        Raises:
            AssertionError
        """
        if isinstance(meta, str):
            self.meta: WDTUltiGrantMeta = WDTUltiGrantMeta.parse_raw(meta)
        else:
            self.meta = WDTUltiGrantMeta.parse_obj(meta)
        self.sid = self.meta.sid
        self.wdt_appkey = self.meta.wdt_appkey
        self.wdt_secret = self.meta.wdt_secret
        self.wdt_salt = self.meta.wdt_salt

    @property
    def endpoint(self):
        return config.WDTULTI_OPENAPI_ENDPOINT

    @property
    def app_key(self):
        return config.WDTULTI_APP_KEY

    @property
    def app_secret(self):
        return config.WDTULTI_APP_SECRET

    @property
    def request_timeout(self) -> int:
        return config.WDTULTI_REQUEST_TIMEOUT

    def sign(self, params):
        sorted_params = dict(sorted(params.items()))
        sign_str = self.wdt_secret
        for key in sorted_params:
            sign_str = sign_str + key + str(sorted_params[key])
        sign_str = sign_str + self.wdt_secret
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        return sign

    @staticmethod
    def build_query(request):
        params = ""
        for key in request:
            params = params + key + "=" + str(request[key]) + "&"
        return params

    def send(self, method: str, pager: Pager | None, params: list[dict[str, Any]]) -> dict[str, Any]:
        """
        Raises:
            AssertionError
            WdtRequestError
        """
        all_params = {
            "sid": self.sid,
            "key": self.wdt_appkey,
            "salt": self.wdt_salt,
            "method": method,
            "timestamp": int(time.time()) - 1325347200,
            "v": "2.0",
        }
        if pager is not None:
            all_params["page_size"] = pager.get_page_size()
            all_params["page_no"] = pager.get_page_no()
            all_params["calc_total"] = int(pager.get_calc_total())
        all_params["body"] = json.dumps(params)
        sign = self.sign(all_params)
        all_params["sign"] = sign
        del all_params["body"]
        service_url = self.endpoint + "?" + self.build_query(all_params)
        data = bytes(json.dumps(params), encoding="utf-8")
        headers = {"content-Type": "application/json"}
        logger.info(f"request url: {service_url} data: {data!r} headers: {headers}")

        resp = self.session.post(service_url, data=data, headers=headers)
        logger.info(f"resp: {resp.text}")
        resp_json = resp.json()
        if int(resp_json.get("status", "-1")) != 0:
            raise WdtRequestError(req=(method, params), res=resp_json, message=resp_json["message"])
        return resp_json

    def get_shops(self) -> list[WDTUltiShop]:
        shoplist: list[WDTUltiShop] = []
        page_no = 0
        while True:
            resp = self.send("setting.Shop.queryShop", Pager(200, page_no, True), [{}])
            parsed_response: GetShopsResponse = GetShopsResponse.parse_obj(resp)
            if len(parsed_response.data.details) == 0:
                break
            shoplist = shoplist + parsed_response.data.details
            page_no += 1
        return shoplist

    def check_grant_is_valid(self) -> bool:
        try:
            resp = self.send("sales.TradeQuery.queryWithDetail", Pager(10, 0, True), [{"trade_no": "1"}])
            if resp.get("status") == 0:
                return True
            else:
                return False
        except Exception as e:
            logger.error("旺店通旗舰版授权不可用 {}", e)
            return False


class WdtUltiOpenAPIClient:
    session = Session()

    def __init__(self, sid: str):
        """
        Raises:
            AssertionError
        """
        erp_info = ErpInfo.get_by_sid(sid, ErpType.WDTULTI)
        assert erp_info, f"店铺 {sid} 无旺店通旗舰版授权信息"
        wdt_appkey = erp_info.meta.get("wdt_appkey")
        if wdt_appkey and wdt_appkey.startswith(config.JST_APP_KEY) and wdt_appkey != config.JST_APP_KEY:
            # 有一些旺店通旗舰版客户的 appkey 是飞梭appkey+字母后缀的形式
            self.wdt_appkey = wdt_appkey
        else:
            self.wdt_appkey = config.JST_APP_KEY
        self.sid = erp_info.meta["sid"]
        self.wdt_secret = erp_info.meta["wdt_secret"]
        self.wdt_salt = erp_info.meta["wdt_salt"]

    @property
    def endpoint(self):
        return config.WDTULTI_OPENAPI_ENDPOINT

    @property
    def app_key(self):
        return config.WDTULTI_APP_KEY

    @property
    def app_secret(self):
        return config.WDTULTI_APP_SECRET

    @property
    def request_timeout(self) -> int:
        return config.WDTULTI_REQUEST_TIMEOUT

    def sign(self, params):
        sorted_params = dict(sorted(params.items()))
        sign_str = self.wdt_secret
        for key in sorted_params:
            sign_str = sign_str + key + str(sorted_params[key])
        sign_str = sign_str + self.wdt_secret
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        return sign

    def build_query(self, request):
        params = ""
        for key in request:
            params = params + key + "=" + str(request[key]) + "&"
        return params

    def _request(self, method: str, pager: Optional[Pager], params, resp_class: Type[RespT]) -> RespT:
        """
        Raises:
            AssertionError
            WdtRequestError
        """
        all_params = {
            "sid": self.sid,
            "key": self.wdt_appkey,
            "salt": self.wdt_salt,
            "method": method,
            "timestamp": int(time.time()) - 1325347200,
            "v": "2.0",
        }
        if pager is not None:
            all_params["page_size"] = pager.get_page_size()
            all_params["page_no"] = pager.get_page_no()
            all_params["calc_total"] = int(pager.get_calc_total())
        all_params["body"] = json.dumps(params)
        sign = self.sign(all_params)
        all_params["sign"] = sign
        del all_params["body"]
        service_url = self.endpoint + "?" + self.build_query(all_params)
        data = bytes(json.dumps(params), encoding="utf-8")
        headers = {"content-Type": "application/json"}
        logger.info(f"request url: {service_url} data: {data!r} headers: {headers}")

        resp = self.session.post(service_url, data=data, headers=headers)
        logger.info(f"resp: {resp.text}")
        resp_json = resp.json()
        if int(resp_json.get("status", "-1")) != 0:
            if resp_json["message"] == "超过每分钟最大调用频率限制,请稍后重试":
                raise WdtRateLimitError(req=(method, params), res=resp_json, message=resp_json["message"])
            raise WdtRequestError(req=(method, params), res=resp_json, message=resp_json["message"])
        return resp_class.parse_obj(resp_json)

    def reissue_order(self, reissue_order: ReissueOrder) -> ReissueOrderResp:
        # 如果最终金额没有显示在创建的补发单中，可能是客户的旺店通旗舰版客户端版本太低，需要升级到1.4.7.3
        json_order = reissue_order.dict(exclude_none=True)
        return self._request(
            "sales.TradeManual.reissueOrder",
            None,
            [json_order["trade_info"], json_order["goods_detail_list"]],
            ReissueOrderResp,
        )

    def swap_order(self, request: SwapOrderRequest) -> SwapOrderResp:
        json_request = request.dict(exclude_none=True)
        return self._request(
            "aftersales.refund.Refund.createOrder",
            None,
            [
                json_request["refund"],
                json_request["refund_order_list"],
                json_request["swap_order"],
                json_request["swap_goods_list"],
            ],
            SwapOrderResp,
        )

    def all_shops(self) -> list[QueryShop]:
        shop_list: list[QueryShop] = []
        page_no = 0
        while True:
            pager = Pager(200, page_no, True)
            resp = self._request("setting.Shop.queryShop", pager, [{}], QueryShopResp)
            if len(resp.data.details) == 0:
                break
            shop_list = shop_list + resp.data.details
            page_no += 1
        return shop_list

    def change_cs_remark(self, req: ChangeRemarkReq) -> ChangeRemarkResp:
        return self._request("sales.TradeEdit.modifyRemark", None, [req.dict()], ChangeRemarkResp)

    def query_skus_by_sku_id(self, sku_id: str, page_no: int = 0, page_size: int = 10):
        return self._request(
            "goods.Goods.queryWithSpec", Pager(page_size, page_no), [{"spec_no": sku_id}], QuerySkuResp
        )

    def query_skus_by_spu_id(self, spu_id: str, page_no: int = 0, page_size: int = 10):
        return self._request(
            "goods.Goods.queryWithSpec", Pager(page_size, page_no), [{"goods_no": spu_id}], QuerySkuResp
        )

    def query_combine_skus_by_combine_sku_id(self, suite_no: str, page_no: int = 0, page_size: int = 10):
        return self._request("goods.Suite.search", Pager(page_size, page_no), [{"suite_no": suite_no}], QuerySuiteResp)

    def query_sku_stocks(
        self, sku_ids: list[str], warehouse_no: str | None = None, page_no: int = 0, page_size: int = 100
    ):
        params = {"spec_nos": sku_ids}
        if warehouse_no:
            params.update({"warehouse_no": warehouse_no})  # type: ignore[dict-item]
        return self._request("wms.StockSpec.search2", Pager(page_size, page_no), [params], QueryStockResp)

    def query_trade_log(self, req: QueryTradeLogReq) -> QueryTradeLogResp:
        return self._request("sales.TradeQuery.getLog", None, [req.dict()], QueryTradeLogResp)

    @classmethod
    def trade_log_type_to_str(cls, type: int) -> str:
        map = {
            1: "下单或建单",
            2: "付款",
            3: "递交",
            6: "拦截出库",
            12: "修改仓库",
            16: "修改标记",
            20: "填写物流单号",
            21: "订单拆分",
            30: "驳回到客审",
            44: "进入财审",
            45: "订单审核",
            46: "财务审核",
            50: "历史订单导入",
            69: "修改包装",
            80: "客户打款",
            90: "修改打印状态",
            91: "打单",
            100: "验货",
            101: "打包",
            102: "称重",
            105: "发货",
            106: "包裹分拣",
            120: "取消拦截",
            123: "归档",
            124: "反归档",
            132: "撤销发货 / 拣货放回",
            133: "拣货",
            134: "登记检视员",
            135: "物流同步",
            140: "翱向发货单翱向建单时间",
            141: "翱向发货单实际落库时间",
            142: "翱向发货单递交",
            143: "翱向补赠赠品",
            155: "热敏获取物流单号",
            160: "清除订单拦截",
            165: "销售出库签入",
            166: "销售出库签出",
            300: "推送 / 取消外部wms入 / 出库单",
            305: "回收物流单号",
            310: "打印小票日志",
            330: "档口锁定",
            340: "订单回告",
            345: "不记录绩效",
            350: "删除出库单货区",
            360: "重选出库单货区",
            365: "订单撤销出库发货",
            366: "解析主单客服备注到子单",
            1000: "无具体意义",
            1001: "更换货品",
            1002: "添加货品",
            1003: "删除货品",
            1004: "修改货品金额",
            1005: "修改收件人信息",
            1006: "修改物流",
            1007: "修改仓库",
            1008: "修改打印备注",
            1009: "修改客服备注",
            1010: "冻结订单",
            1011: "解冻订单",
            1012: "清除异常",
            1013: "退款 / 子订单退款",
            1014: "订单审核失败",
            1015: "未付款递交",
            1016: "下载原始单",
            1020: "递交后处理",
            1021: "重试物流",
            1031: "分单",
            1032: "取消分单",
            1041: "放行",
            1042: "退回",
            1051: "历史订单导入转完成",
            1060: "极速直发",
            1061: "生成出库单",
            1062: "原始单发货变更触发发货",
            1063: "原始单变更触发发货失败",
            1099: "未付款转移",
            1100: "委外推单",
            1101: "获取面单",
        }
        return map.get(type) or "未知"

    def get_logistic_by_name(self, logistics_name) -> QueryLogisticResp:
        return self._request(
            "setting.Logistics.queryLogistics",
            Pager(1000, 0, True),
            [{"logistics_name": logistics_name}],
            QueryLogisticResp,
        )

    def stockin_refund_return_logistics_package_query(
        self,
        logistics_no,
    ) -> StockinRefundReturnLogisticsPackageQueryResp:
        return self._request(
            "wms.stockin.Refund.returnLogisticsPackageQuery",
            Pager(100, 0, True),
            [{"logistics_no": logistics_no}],
            StockinRefundReturnLogisticsPackageQueryResp,
        )


class WdtUltiQM:
    """旺店通旗舰版奇门接口"""

    session = Session()

    def __init__(self, sid=None, credential=None, erp_info=None):
        if credential:
            erp_info = ErpInfo(meta=credential.dict())
        else:
            erp_info = erp_info or ErpInfo.get_by_sid(sid, ErpType.WDTULTI)
        assert erp_info, f"店铺 {sid} 无旺店通旗舰版授权信息"
        wdt_app_key = erp_info.meta.get("wdt_appkey")
        if wdt_app_key and wdt_app_key.startswith(config.JST_APP_KEY) and wdt_app_key != config.JST_APP_KEY:
            # 有一些旺店通旗舰版客户的 appkey 是飞梭appkey+字母后缀的形式
            self.wdt_app_key = wdt_app_key
        else:
            self.wdt_app_key = config.JST_APP_KEY
        self.app_key = config.JST_APP_KEY
        self.target_app_key = config.WDTULTI_TARGET_APP_KEY
        self.app_secret = config.JST_APP_SECRET
        self.sid = erp_info.meta.get("sid")
        self.wdt_salt = erp_info.meta.get("wdt_salt")
        self.wdt_secret = erp_info.meta.get("wdt_secret")

    def _get_sign_wdt_fc(self, form_data: dict, wdt_fc_secret: str):
        """
        旺店通旗舰版 wdt_sign 生成
        """
        form_data = self._wdt_fc_sort_params(form_data)
        sign_str = self._wdt_fc_format_data(form_data)
        sign_str = wdt_fc_secret + sign_str + wdt_fc_secret
        sign = hashlib.md5()
        sign.update(sign_str.encode("utf-8"))
        return sign.hexdigest().lower()

    def _wdt_fc_sort_params(self, form_data):
        """
        旺店通旗舰版 参数排序
        """
        form_dict = dict()
        for x in sorted(form_data.keys(), reverse=False):
            v = form_data.get(x)
            if isinstance(v, dict):
                r = self._wdt_fc_sort_params(v)
                form_dict.update({x: r})
            else:
                form_dict.update({x: v})
        return form_dict

    def _wdt_fc_format_data(self, form_data):
        """
        旺店通旗舰版生成wdt_sign进行格式转换
        """
        sign_str = ""
        for k, v in form_data.items():
            sign_str += str(k)
            if isinstance(v, dict):
                sign_str += self._wdt_fc_format_data(v)
            else:
                sign_str += str(v)
        return sign_str

    def _get_sign_qm(self, form_data: dict):
        """
        奇门 sign 生成
        """
        form_dict = dict()
        #   对数所有请求参数按照键名进行正序排序
        for x in sorted(form_data.keys(), reverse=False):
            form_dict.update({x: form_data.get(x)})

        #   循环对每个键值进行处理
        sign_str = ""
        for k, v in form_dict.items():
            sign_str += str(k) + str(v)
        sign_str = self.app_secret + sign_str + self.app_secret
        #   md5 32位大写加密
        sign = hashlib.md5()
        sign.update(sign_str.encode("utf-8"))
        return sign.hexdigest().upper()

    def _execute_qm(self, relative_url, s_id, params: dict):
        params = params.copy()
        wdt_salt = self.wdt_salt
        wdt_secret = self.wdt_secret
        params.update({"wdt_appkey": self.wdt_app_key})
        params.update({"datetime": self._get_time_stamp()[1]})
        params.update({"method": relative_url})
        params.update({"wdt_salt": wdt_salt})
        wdt_sign = self._get_sign_wdt_fc(params, wdt_secret)

        params.update({"wdt_sign": wdt_sign})
        params.update({"app_key": self.app_key})
        params.update({"timestamp": self._get_time_stamp()[1]})
        params.update({"method": relative_url})
        params.update({"target_app_key": self.target_app_key})
        params.update({"format": "json"})
        params.update({"sign_method": "md5"})
        params.update({"v": "2.0"})
        params.update({"wdt3_customer_id": s_id})
        sign = self._get_sign_qm(params)
        params.update({"sign": sign})
        body = json.dumps({"pager": params.get("pager"), "params": params.get("params")})
        qm_response = self._do_post(params, body)

        response_json = qm_response.json()["response"]
        if response_json.get("sub_message") == "超过每分钟最大调用频率限制,请稍后重试":
            raise WdtQmRateLimitError((relative_url, self.sid, params), qm_response.json(), "超过每分钟最大调用频率限制,请稍后重试")
        return qm_response

    def _do_post(self, params, body):
        params_str = urllib.parse.urlencode(params)
        url = config.WDTULTI_ENDPOINT + "?" + params_str
        logger.info(f"请求参数 {url}")
        res = self.session.post(url, data=body)
        logger.info(json.dumps(res.json(), ensure_ascii=False, indent=2))
        return res

    @staticmethod
    def _get_time_stamp():
        start_time = (datetime.now() + timedelta(days=-1)).strftime("%Y-%m-%d %H:%M:%S")
        end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        return [start_time, end_time]

    def get_sent_orders_by_time(self, page_no, page_size, start_time, end_time):
        params = {
            "pager": {"page_no": page_no, "page_size": page_size},
            "params": {"start_time": start_time, "end_time": end_time, "status": 95},
        }
        response = self._execute_qm("wdt.sales.tradequery.querywithdetail", self.sid, params)
        return WdtultiOrderResp(**response.json()["response"]["data"])

    def trade_query(self, params: TradeQueryParams, pager: Pager):
        data = dict(
            pager=dict(page_no=pager.get_page_no(), page_size=pager.get_page_size()),
            params=params.dict(exclude_none=True),
        )
        response = self._execute_qm("wdt.sales.tradequery.querywithdetail", self.sid, data)
        return WdtultiOrderResp(**response.json()["response"]["data"])

    def history_trade_query(self, params: TradeQueryParams, pager: Pager):
        data = dict(
            pager=dict(page_no=pager.get_page_no(), page_size=pager.get_page_size()),
            params=params.dict(exclude_none=True),
        )
        response = self._execute_qm("wdt.sales.tradequery.queryhistorywithdetail", self.sid, data)
        return WdtultiOrderResp(**response.json()["response"]["data"])

    def get_orders(self, src_tid=None, trade_no=None, logistics_no=None, check_history=True) -> WdtultiOrderResp:
        """
        查询订单管理API文档
        https://open.wangdian.cn/qjb/open/apidoc/doc?path=sales.TradeQuery.queryWithDetail
        归档订单：https://open.wangdian.cn/qjb/open/apidoc/doc?path=sales.TradeQuery.queryHistoryWithDetail
        :param src_tid: 订单号
        """

        def check_src_tid_by_detail_list(o, src_tid):
            for detail in o.detail_list:
                if detail.src_tid.startswith(src_tid):
                    return True
            return False

        def filter_same_trade_no(orders):
            """过滤掉同一个订单号的订单"""
            trade_no_set = set()
            filtered_orders = []  # noqa
            for order in orders:
                if order.trade_no not in trade_no_set:
                    trade_no_set.add(order.trade_no)
                    filtered_orders.append(order)
            return filtered_orders

        page_no, page_size = 1, 100
        params: dict = {
            "pager": {"page_no": page_no, "page_size": page_size},
            "params": {},
        }
        if logistics_no:
            params["params"]["logistics_no"] = logistics_no
        elif trade_no:
            params["params"]["trade_no"] = trade_no
        elif src_tid:
            params["params"]["src_tid"] = src_tid
        else:
            raise Exception("订单号和旺店通订单号不能同时为空")

        def wdt_order():
            qm_response = self._execute_qm("wdt.sales.tradequery.querywithdetail", self.sid, params)
            response_json = qm_response.json()["response"]
            wdt_order_resp = WdtultiOrderResp(**response_json["data"])
            return wdt_order_resp

        def wdt_history_order():
            # 归档订单查询，无法仅通过 logistics_no 来调用。
            if trade_no:
                params["params"]["trade_no"] = trade_no
            elif src_tid:
                params["params"]["src_tid"] = src_tid
            else:
                return WdtultiOrderResp(order=[], total_count=0)

            qm_response = self._execute_qm("wdt.sales.tradequery.queryhistorywithdetail", self.sid, params)
            response_json = qm_response.json()["response"]
            wdt_order_resp = WdtultiOrderResp(**response_json["data"])
            return wdt_order_resp

        # 初始化查询；先通过普通接口查询，如果没有查询到再通过归档接口查询
        use_history = False
        wdt_order_res = wdt_order()
        if not wdt_order_res.order and check_history:
            logger.info(f"旺店通归档订单查询，订单号：{src_tid} {trade_no}")
            use_history = True
            wdt_order_res = wdt_history_order()
        # 如果订单数量超过100，继续查询
        wdt_order_total_count = wdt_order_res.total_count or 0
        while wdt_order_total_count > page_no * page_size:
            page_no += 1
            params["pager"]["page_no"] = page_no
            append_wdt_order_res = wdt_order() if not use_history else wdt_history_order()
            wdt_order_res.order.extend(append_wdt_order_res.order)

        # 如果仅仅指定了订单号，要过滤掉所有不以该订单号开头的数据，
        if src_tid and not trade_no:
            filtered_orders = [o for o in (wdt_order_res.order or []) if check_src_tid_by_detail_list(o, src_tid)]
            wdt_order_res.order = filtered_orders
        wdt_order_res.order = filter_same_trade_no(wdt_order_res.order)
        return wdt_order_res

    def get_stockout_order_by_trade_no(self, trade_no):
        params: dict = {"pager": {"page_no": 1, "page_size": 100}, "params": {"src_order_no": trade_no}}
        resp = self._execute_qm("wdt.wms.stockout.sales.querywithdetail", self.sid, params)
        stockout_orders = [StockoutDetail(**i) for i in resp.json()["response"]["data"]["order"]]
        return stockout_orders

    def get_warehouse_name(self, warehouse_no) -> str | None:
        params = {"pager": {"page_no": 1, "page_size": 100}, "params": {"warehouse_no": warehouse_no}}
        response = self._execute_qm("wdt.setting.warehouse.querywarehouse", self.sid, params)
        details = response.json()["response"]["data"]["details"]
        if not details:
            return None
        else:
            return WarehouseModel(**details[0]).name or ""

    def all_warehouse_query(self) -> List[WarehouseModel]:
        warehouses: List[WarehouseModel] = []
        page_no = 1
        while True:
            params = {"pager": {"page_no": page_no, "page_size": 100}, "params": {}}
            response = self._execute_qm("wdt.setting.warehouse.querywarehouse", self.sid, params)
            details = response.json()["response"]["data"]["details"]
            if len(details) == 0:
                break
            warehouses.extend([WarehouseModel(**detail) for detail in details])
            page_no = page_no + 1
        return warehouses

    def get_provider_goods_info(self, params) -> List[ProviderGoodsInfo]:
        params = {"pager": {"page_no": 1, "page_size": 1}, "params": params}
        response = self._execute_qm("wdt.purchase.providergoods.querydetail", self.sid, params)
        return [ProviderGoodsInfo(**i) for i in response.json()["response"]["data"]["details"]]

    def get_provider_details(self, provider_no=None) -> List[Provider]:
        params = {}

        if provider_no:
            params["provider_no"] = provider_no
        params = {"pager": {"page_no": 1, "page_size": 100}, "params": params}
        response = self._execute_qm("wdt.setting.purchaseprovider.querydetail", self.sid, params)
        details = response.json()["response"]["data"]["details"]
        return [Provider(**detail) for detail in details]

    def get_provider_name(self, spec_no=None):
        """获取供应商名称"""
        good_info = self.get_provider_goods_info({"spec_no": spec_no})[0]
        provider_info = self.get_provider_details(good_info.provider_no)[0]
        return provider_info.provider_name

    def stock_query(self, spec_no_list: List[str], warehouse_no: str | None = None) -> StockQueryResp:
        """

        References:
            https://open.wangdian.cn/qjb/open/apidoc/doc?path=wms.StockSpec.search
        """
        params: dict[str, Any] = {
            "params": {"spec_nos": spec_no_list},
            "pager": {"page_no": 1, "page_size": 100},
        }
        if warehouse_no:
            params["params"]["warehouse_no"] = warehouse_no
        response = self._execute_qm("wdt.wms.stockspec.search", self.sid, params)
        response_json = response.json()["response"]
        return StockQueryResp.parse_obj(response_json)

    def refund_query_by_logistics_no(self, logistics_no: str) -> RefundQueryResp:
        # logistics_no为退回物流单号.
        params: dict[str, Any] = {
            "params": {"return_logistics_no": logistics_no},
            "pager": {"page_no": 1, "page_size": 200},
        }
        response = self._execute_qm("wdt.aftersales.refund.refund.search", self.sid, params)
        response_json = response.json()["response"]
        return RefundQueryResp.parse_obj(response_json)

    def rawrefund_query_by_refund_no(self, refund_no: str, start_time: str, end_time: str) -> RawRefundQueryResp:
        # src_refund_no为平台退换单号.
        params: dict[str, Any] = {
            "params": {"refund_no": refund_no, "start_time": start_time, "end_time": end_time},
            "pager": {"page_no": 1, "page_size": 200},
        }
        response = self._execute_qm("wdt.aftersales.refund.rawrefund.search", self.sid, params)
        response_json = response.json()["response"]
        return RawRefundQueryResp.parse_obj(response_json)

    def stockin_refund_query_by_refund_no(self, refund_no) -> StockinRefundQueryResp:
        params: dict[str, Any] = {
            "params": {"refund_no": refund_no},
            "pager": {"page_no": 1, "page_size": 200},
        }
        response = self._execute_qm("wdt.wms.stockin.refund.querywithdetail", self.sid, params)
        response_json = response.json()["response"]
        return StockinRefundQueryResp.parse_obj(response_json)
