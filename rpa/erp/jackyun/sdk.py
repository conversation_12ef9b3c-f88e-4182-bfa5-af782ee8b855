import hashlib
import hmac
import json
import time
from typing import Type
from typing import TypeVar
from urllib.parse import quote

import arrow
from loguru import logger
from pydantic import BaseModel

from robot_processor.client_mixins import Session
from robot_processor.enums import ErpType
from robot_processor.error.client_request import JackyunRequestError
from robot_processor.shop.models import ErpInfo
from robot_processor.shop.schema import JackyunGrantMeta
from robot_processor.utils import ts2date
from rpa.conf import rpa_config as conf
from rpa.erp.jackyun.schemas import AftersaleUploadReq
from rpa.erp.jackyun.schemas import AftersaleUploadResp
from rpa.erp.jackyun.schemas import GetCustomColumnConfigInfoReq
from rpa.erp.jackyun.schemas import GetCustomColumnConfigInfoResp
from rpa.erp.jackyun.schemas import GoodsListResp
from rpa.erp.jackyun.schemas import JackyunShop
from rpa.erp.jackyun.schemas import LogisticsGetResp
from rpa.erp.jackyun.schemas import OrderListResp
from rpa.erp.jackyun.schemas import OrderUpdateLogisticsInfoReq
from rpa.erp.jackyun.schemas import OrderUpdateLogisticsInfoResp
from rpa.erp.jackyun.schemas import QueryChannelResp
from rpa.erp.jackyun.schemas import QueryCombineResp
from rpa.erp.jackyun.schemas import QueryShopResp
from rpa.erp.jackyun.schemas import ReturnChangeListResp
from rpa.erp.jackyun.schemas import ReturnChangeUpdateCustomFieldsReq
from rpa.erp.jackyun.schemas import ReturnChangeUpdateCustomFieldsResp
from rpa.erp.jackyun.schemas import ReturnChangeUpdateReq
from rpa.erp.jackyun.schemas import ReturnChangeUpdateResp

RespT = TypeVar("RespT", bound=BaseModel)


class JackyunOpenPlatformAPIClient:
    session = Session()

    def __init__(self, meta: str | dict):
        if isinstance(meta, str):
            self.meta: JackyunGrantMeta = JackyunGrantMeta.parse_raw(meta)
        else:
            self.meta = JackyunGrantMeta.parse_obj(meta)
        self.app_key = self.meta.app_key
        self.app_secret = self.meta.app_secret
        self.app_token = self.meta.app_token

    @staticmethod
    def generate_signature(params: dict, app_secret: str) -> str:
        sign_str: str = app_secret
        for key in sorted(params):
            print(key, params.get(key))
            sign_str += key + str(params.get(key))
        sign_str += app_secret
        return hashlib.md5(sign_str.lower().encode("utf-8")).hexdigest()

    def send(self, method: str, data: dict) -> dict:
        original_biz_content = json.dumps(data, separators=(",", ":"))
        params = {
            "appkey": self.app_key,
            "contenttype": "json",
            "method": method,
            "timestamp": arrow.now().format("YYYY-MM-DD HH:mm:ss"),
            "version": "v1.0",
            "bizcontent": original_biz_content,
        }
        sign = self.generate_signature(params, self.app_secret)
        params.update({"sign": sign})
        params.update({"bizcontent": quote(original_biz_content, encoding="utf-8")})
        if self.app_token:
            params.update({"token": self.app_token})
        post_data = "&".join([key + "=" + params[key] for key in sorted(params)])
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        resp = self.session.post(
            "https://open.jackyun.com/open/openapi/do",
            data=post_data,
            headers=headers,
        )
        logger.info(f"resp: {resp.text}")
        resp_json = resp.json()
        return resp_json

    def get_shops(self) -> list[JackyunShop]:
        # 目前抽取了几个客户，都没有订阅该接口，看下是否需要调整。
        method = "erp.sales.get"
        page_no = 0
        page_size = 50
        shops: list[JackyunShop] = []
        resp = self.send(method, {"pageIndex": page_no, "pageSize": page_size})
        page_no += 1
        parsed_resp: QueryShopResp = QueryShopResp.parse_obj(resp)
        if parsed_resp.code != "200":
            return []
        shops.extend(parsed_resp.result.data)
        return shops

    def check_grant_is_valid(self) -> bool:
        # 目前抽取了几个客户，都没有订阅该接口，看下是否需要调整。
        try:
            resp = self.send(
                "omsapi-business.order.get",
                {
                    "pageIndex": 1,
                    "pageSize": 1,
                },
            )
            if resp.get("code") == 200:
                return True
            else:
                return False
        except Exception as e:
            logger.error("吉客云授权不可用 {}", e)
            return False


class JackyunQmSDK:
    session = Session()

    def __init__(self, sid=None, credential=None, erp_info=None):
        """
        Args:
            sid (str): shop.sid
            credential (Credential.Jackyun | None): 吉客云授权信息
            erp_info (ErpInfo | None):
        Raises:
            AssertionError
        """
        if credential:
            self.erp_info = ErpInfo(meta=credential.dict())
        else:
            erp_info = erp_info or ErpInfo.get_by_sid(sid, ErpType.JACKYUN)
            assert erp_info, f"店铺 {sid} 无吉客云授权信息"
            self.erp_info = erp_info

    @property
    def app_key(self):
        return self.erp_info.meta["app_key"]

    @property
    def app_secret(self):
        return self.erp_info.meta["app_secret"]

    @property
    def app_token(self):
        return self.erp_info.meta.get("app_token")

    @property
    def customer_id(self):
        return self.erp_info.meta["customer_id"]

    @staticmethod
    def sorted_params(params):
        sorted_params = {}
        for key in sorted(params):
            sorted_params[key] = params[key]
        return sorted_params

    def _sign(self, params):
        sign_str = "".join([f"{k}{v}" for k, v in params.items()])
        secret_key = conf.JACKYUN_APP_SECRET.encode("utf-8")
        hmac_sha256 = hmac.new(secret_key, sign_str.encode("utf-8"), digestmod=hashlib.sha256)
        hmac_hexdigest = hmac_sha256.hexdigest()
        return hmac_hexdigest.upper()

    def _request(self, method, data: dict, resp_class: Type[RespT]) -> RespT:
        """
        Raises:
            JackyunRequestError
        """
        timestamp = ts2date(time.time())
        params = {
            "method": method,
            "app_key": conf.JACKYUN_APP_KEY,
            "timestamp": timestamp,
            "target_app_key": conf.JACKYUN_TARGET_APP_KEY,
            "format": "json",
            "v": 2.0,
            "sign_method": "hmac-sha256",
            "jkymethod": method,
            "jkysign": "jkysign",
            "jkytimestamp": timestamp,
            "jkyversion": "1.0",
            "jkyappkey": self.app_key,
            "jkycustomerid": self.customer_id,
            "content": json.dumps(data, separators=(",", ":")),
        }
        params = JackyunQmSDK.sorted_params(params)
        sign_str = self._sign(params)
        params["sign"] = sign_str
        logger.info(f"method: {method}, sign: {sign_str}, data: {data}")
        resp = self.session.post(conf.JACKYUN_QM_ENDPOINT, params=params, timeout=conf.JACKYUN_REQUEST_TIME)
        logger.info(f"resp: {resp.text}")
        resp_json = resp.json()
        if resp_json["response"]["jackyunFlag"] != "success":
            raise JackyunRequestError(
                req=(method, params), res=resp_json, message=resp_json["response"]["jackyunMessage"]
            )
        return resp_class.parse_obj(resp_json)

    def order_list(self, tid) -> OrderListResp:
        data = {
            "sourceTradeNos": tid,
            "fields": "goodsDetail.goodsNo,goodsDetail.goodsName,"
            "goodsDetail.specName,goodsDetail.sellCount,"
            "goodsDetail.sellPrice,goodsDetail.isFit,"
            "goodsDetail.isGift,goodsDetail.customerPrice,"
            "goodsDetail.tradeGoodsNo,goodsDetail.sourceSubtradeNo,"
            "flagIds,flagNames,tradeStatusExplain,logisticCode,shopcode,"
            "tradeType,mainPostid,logisticName,warehouseName,warehouseCode,"
            "shopId,onlineTradeNo,packageWeight,"
            "assemblyGoodsDetail,sellerMemo,tradeNo,"
            "goodsDetail.platCode,goodsDetail.platGoodsId,"
            "goodsDetail.platSkuId,tradeId,sourceAfterNo,"
            "goodsDetail.barcode,assemblyGoodsDetail,"
            "goodsDetail.divideSellTotal",
        }
        return self._request("jackyun.tradenotsensitiveinfos.list.get", data, OrderListResp)

    def order_list_by_main_post_id(self, main_post_id) -> OrderListResp:
        data = {
            "mainPostId": main_post_id,
            "fields": "goodsDetail.goodsNo,goodsDetail.goodsName,"
            "goodsDetail.specName,goodsDetail.sellCount,"
            "goodsDetail.sellPrice,goodsDetail.isFit,"
            "goodsDetail.isGift,goodsDetail.customerPrice,"
            "goodsDetail.tradeGoodsNo,goodsDetail.sourceSubtradeNo,"
            "flagIds,flagNames,tradeStatusExplain,logisticCode,shopcode,"
            "tradeType,mainPostid,logisticName,warehouseName,warehouseCode,"
            "shopId,onlineTradeNo,packageWeight,"
            "assemblyGoodsDetail,sellerMemo,tradeNo,"
            "goodsDetail.platCode,goodsDetail.platGoodsId,"
            "goodsDetail.platSkuId,tradeId,sourceAfterNo,"
            "goodsDetail.barcode,assemblyGoodsDetail,"
            "goodsDetail.divideSellTotal",
        }
        return self._request("jackyun.tradenotsensitiveinfos.list.get", data, OrderListResp)


class JackyunSDK:
    session = Session()

    def __init__(self, sid: str):
        """
        Args:
            sid (str): shop.sid
        Raises:
            AssertionError
        """
        erp_info = ErpInfo.get_by_sid(sid, ErpType.JACKYUN)
        assert erp_info, f"店铺 {sid} 无吉客云授权信息"
        self.erp_info = erp_info

    @property
    def app_key(self):
        return self.erp_info.meta["app_key"]

    @property
    def app_secret(self):
        return self.erp_info.meta["app_secret"]

    @property
    def app_token(self):
        return self.erp_info.meta.get("app_token")

    def _sign(self, params: dict):
        """
        吉客云签名
        """
        sign_str: str = self.app_secret
        for key in sorted(params):
            print(key, params.get(key))
            sign_str += key + str(params.get(key))
        sign_str += self.app_secret
        return hashlib.md5(sign_str.lower().encode("utf-8")).hexdigest()

    def _request(self, method: str, data: dict, resp_class: Type[RespT]) -> RespT:
        """
        Raises:
            JackyunRequestError
        """
        params = dict()
        params["appkey"] = self.app_key
        params["bizcontent"] = json.dumps(data, separators=(",", ":"))
        params["contenttype"] = "json"
        params["method"] = method
        params["timestamp"] = arrow.now().format("YYYY-MM-DD HH:mm:ss")
        params["version"] = "v1.0"
        sign = self._sign(params)
        params.update({"sign": sign})
        params.update({"bizcontent": quote(params["bizcontent"], encoding="utf-8")})
        if self.app_token:
            params.update({"token": self.app_token})
        post_data = "&".join([key + "=" + params[key] for key in sorted(params)])
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        resp = self.session.post("https://open.jackyun.com/open/openapi/do", data=post_data, headers=headers)
        logger.info(f"resp: {resp.text}")
        resp_json = resp.json()
        if resp_json["code"] != 200:
            raise JackyunRequestError(req=(method, params, data), res=resp_json, message=resp_json["msg"])
        return resp_class.parse_obj(resp_json)

    def returnchange_list(self, data: dict) -> ReturnChangeListResp:
        return self._request("ass.returnchange.fullinfoget", data, ReturnChangeListResp)

    def returnchange_update(self, req: ReturnChangeUpdateReq):
        return self._request(
            "ass-business.returnchange.update.plat", req.dict(exclude_none=True), ReturnChangeUpdateResp
        )

    def aftersale_upload(self, req: AftersaleUploadReq) -> AftersaleUploadResp:
        return self._request("ass-business.returnchange.create", req.dict(exclude_none=True), AftersaleUploadResp)

    def goods_list_by_goods_no(self, goods_no: str):
        data = {"pageIndex": 0, "pageSize": 100, "goodsNo": goods_no}
        return self._request("erp.storage.goodslist", data, GoodsListResp)

    def query_channel_by_code(self, code: str):
        data = {"pageIndex": 0, "pageSize": 100, "code": code}
        return self._request("erp.sales.get", data, QueryChannelResp)

    def query_combine_skus_by_combine_sku_ids(
        self,
        combine_sku_ids: list[str],
        page_index: int = 0,
        page_size: int = 100,
    ):
        request_combine_sku_ids = ",".join(combine_sku_ids)
        data = {
            "goodsNo": request_combine_sku_ids,
            "pageIndex": page_index,
            "pageSize": page_size,
        }
        return self._request("erp-goods.goods.listgoodspackage", data, QueryCombineResp)

    def query_skus_by_sku_id(self, goods_no: str, page_index: int = 0, page_size: int = 100):
        data = {
            "pageIndex": page_index,
            "pageSize": page_size,
            "goodsNo": goods_no,
        }
        return self._request(
            "erp.storage.goodslist",
            data,
            GoodsListResp,
        )

    def returnchange_update_customfields(
        self,
        req: ReturnChangeUpdateCustomFieldsReq,
    ) -> ReturnChangeUpdateCustomFieldsResp:
        return self._request(
            "ass-business.returnchange.update.customfields.plat",
            req.dict(exclude_none=True),
            ReturnChangeUpdateCustomFieldsResp,
        )

    def others_get_customcolumnconfiginfo(self, req: GetCustomColumnConfigInfoReq) -> GetCustomColumnConfigInfoResp:
        return self._request(
            "ass-business.others.getcustomcolumnconfiginfo", req.dict(exclude_none=True), GetCustomColumnConfigInfoResp
        )

    def order_update_logsitics_info(self, req: OrderUpdateLogisticsInfoReq) -> OrderUpdateLogisticsInfoResp:
        return self._request(
            "wms.order.logistic-info-update", req.dict(exclude_none=True), OrderUpdateLogisticsInfoResp
        )

    def logistics_get(self):
        return self._request("erp.logistic.get", dict(pageSize=200), LogisticsGetResp)
