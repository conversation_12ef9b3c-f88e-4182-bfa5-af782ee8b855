from loguru import logger

from rpa.erp.jst.sdk import JstSDK


def get_wms_co_name_from_cache(result, wms_co_id) -> str | None:
    if result.datas:
        for wms_info in result.datas:
            if wms_info.wms_co_id == wms_co_id:
                return wms_info.name
    return None


def complete_wms_co_name_by_wms_co_id(
    jst_sdk: JstSDK,
    org_id: str,
    wms_co_id: int | None
) -> str:
    # 补充分仓名称
    if wms_co_id in [0, None]:
        return "未设定发货仓库"

    try:
        result = jst_sdk.get_wms_info_by_org_id(org_id)
    except Exception as e:
        logger.warning(f"租户 {org_id} 获取聚水潭分仓信息失败 {e}")
        return ""
    else:
        # 先从缓存拿
        if wms_name := get_wms_co_name_from_cache(result, wms_co_id):
            return wms_name
        # 缓存拿不到的话，刷新缓存再拿
        else:
            result = jst_sdk.get_wms_info_by_org_id(org_id, refresh_cache=True)
            # !!如果最后还找不到，说明商家没有拿主账号授权聚水潭，导致我们只能拿到部分的分仓信息
            return get_wms_co_name_from_cache(result, wms_co_id) or "未知分仓"


def is_sku_match(sku_id: str | None, another_sku_id: str | None) -> bool:
    """聚水潭对于 sku_id 不区分大小写，部分商家在商品管理中和在订单中使用的 sku_id 可能不一致

    References:
        https://git.leyantech.com/digismart/robot-processor/-/merge_requests/4363
    """
    from robot_processor.utils import unwrap_optional
    if any([sku_id is None, another_sku_id is None]):
        return False
    return unwrap_optional(sku_id).lower() == unwrap_optional(another_sku_id).lower()
