from datetime import datetime
from typing import List
from typing import Optional

import arrow
from pydantic import BaseModel
from pydantic import Field
from pydantic import root_validator
from pydantic import validator

from robot_processor.enums import WdtTradeStatus
from robot_processor.job.job_model_wrapper import SKU
from robot_processor.utils import make_fields_optional


class PushOrder(BaseModel):
    oid: str
    num: float
    price: float
    status: int
    refund_status: int
    goods_id: str
    spec_id: str
    goods_no: str = ""
    spec_no: str
    goods_name: str = ""
    spec_name: str = ""
    adjust_amount: float = 0
    discount: float = 0
    share_discount: float = 0
    remark: str = ""
    cid: Optional[str]


class PushTrade(BaseModel):
    tid: str
    trade_status: int
    pay_status: int
    delivery_term: int
    buyer_nick: str
    receiver_name: str
    receiver_province: str
    receiver_city: str
    receiver_district: str
    receiver_address: str
    receiver_mobile: str
    receiver_telno: str = ""
    receiver_zip: str = ""
    post_amount: float = 0
    cod_amount: float = 0
    ext_cod_fee: float = 0
    other_amount: float = 0
    paid: float = 0
    seller_memo: str
    logistics_type: str = "-1"
    order_list: List[PushOrder]
    trade_time: str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    pay_time: str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    warehouse_no: Optional[str]


class SalesSpec(BaseModel):
    spec_no: str
    num: str
    is_suite: str = "0"
    remark: Optional[str]
    original_price: Optional[float]


class SalesTrade(BaseModel):
    trade_no: str
    receiver_name: Optional[str]
    receiver_mobile: Optional[str]
    receiver_address: Optional[str]
    receiver_province: Optional[str]
    receiver_city: Optional[str]
    receiver_district: Optional[str]
    is_check: int = 1
    is_cover: int = 0
    warehouse_no: Optional[str]
    tags: Optional[str]
    logistics_code: Optional[str]
    cs_remark: Optional[str]
    spec_list: List[SalesSpec]
    buyer_message: Optional[str]
    operator_no: Optional[str]
    post_amount: Optional[str]
    print_remark: Optional[str]
    custom_trade_type_name: Optional[str]  # 自定义订单类型


class SalesTradeReplenishResp(BaseModel):
    code: int
    message: str
    trade_no: Optional[str]


class BatchInfo(BaseModel):
    batch_no: str
    batch_id: str
    position_no: str
    position_goods_count: int


@make_fields_optional
class GoodsItem(BaseModel):
    actual_num: str
    adjust: str
    api_goods_name: str
    api_spec_name: str
    barcode: str
    base_unit_id: int
    bind_oid: str
    cid: int
    class_name: str
    commission: str
    created: str
    delivery_term: int
    discount: str
    flag: int
    from_mask: int
    gift_type: int
    goods_id: int
    goods_name: str
    goods_no: str
    goods_type: int
    guarantee_mode: str
    invoice_content: str
    invoice_type: int
    is_consigned: str
    is_master: str
    is_print_suite: str
    is_received: str
    is_zero_cost: str
    large_type: int
    modified: str
    num: str
    order_price: str
    paid: str
    pay_id: str
    pay_status: str
    platform_goods_id: str
    platform_id: int
    platform_spec_id: str
    price: str
    prop2: str = ""
    rec_id: int
    refund_num: str
    refund_status: int
    remark: str
    share_amount: str
    share_amount2: str
    share_post: str
    share_price: str
    single_spec_no: str
    spec_code: str
    spec_id: int
    spec_name: str
    spec_no: str
    src_oid: str
    src_tid: str
    stock_reserved: str
    suite_amount: str
    suite_discount: str
    suite_id: int
    suite_name: str
    suite_no: str
    suite_num: str
    tax_rate: str
    tc_order_id: str
    trade_id: int
    unit_name: str
    weight: str
    # 旺店通货品退款状态的可读性名称，不在文档中，是基于 refund_status 来生成的。
    refund_status_name: str
    batch_list: List[BatchInfo] = []
    brand_name: str
    num_number: int
    order_price_number: float
    share_amount_number: float
    share_price_number: float
    paid_number: float

    @root_validator(pre=True)
    def pre(cls, values):
        refund_status = values.get("refund_status")
        refund_status_map = {
            0: "无退款",
            1: "取消退款",
            2: "已申请退款",
            3: "等待退货",
            4: "等待收货",
            5: "退款成功",
            6: "已关闭",
        }
        values["refund_status_name"] = refund_status_map.get(refund_status) or ""

        num = values.get("num")
        num_number = int(float(num)) if num else 0
        values["num_number"] = num_number

        order_price = values.get("order_price")
        order_price_number = float(order_price) if order_price else 0
        values["order_price_number"] = order_price_number

        share_amount = values.get("share_amount")
        share_amount_number = float(share_amount) if share_amount else 0
        values["share_amount_number"] = share_amount_number

        share_price = values.get("share_price")
        share_price_number = float(share_price) if share_price else 0
        values["share_price_number"] = share_price_number

        paid_number = values.get("paid")
        paid_number = float(paid_number) if paid_number else 0
        values["paid_number"] = paid_number
        return values


@make_fields_optional
class Trade(BaseModel):
    bad_reason: int
    buyer_message: str
    buyer_message_count: str
    buyer_nick: str
    cancel_reason: str
    check_step: str
    checker_id: int
    checker_name: str
    checkouter_id: int
    checkouter_name: str
    cod_amount: str
    commission: str
    consign_status: int
    created: str
    cs_remark: str
    cs_remark_change_count: str
    cs_remark_count: str
    currency: str
    customer_id: str
    customer_name: str
    customer_no: str
    customer_type: str
    dap_amount: str
    delay_to_time: str
    delivery_term: int
    discount: str
    discount_change: str
    ext_cod_fee: str
    fchecker_id: int
    fchecker_name: str
    fenxiao_nick: str
    fenxiao_tid: str
    fenxiao_type: int
    flag_id: str
    flag_name: str
    freeze_reason: int
    freeze_reason_info: str
    fullname: str
    gift_mask: str
    goods_amount: str
    goods_cost: str
    goods_count: str
    goods_list: List[GoodsItem]
    goods_type_count: int
    id_card: str
    id_card_type: int
    invoice_content: str
    invoice_id: int
    invoice_title: str
    invoice_type: int
    is_prev_notify: str
    is_sealed: str
    is_unpayment_sms: str
    large_type: str
    logistics_code: str
    logistics_id: int
    logistics_name: str
    logistics_no: str
    logistics_template_id: str
    logistics_type: int
    modified: str
    note_count: str
    other_amount: str
    other_cost: str
    package_id: str
    paid: str
    pay_account: str
    pay_time: str
    pi_amount: str
    platform_id: int
    post_amount: str
    post_cost: str
    pre_charge_time: str
    print_remark: str
    profit: str
    raw_goods_count: str
    raw_goods_type_count: int
    receivable: float
    receiver_address: str
    receiver_area: str
    receiver_city: int
    receiver_country: str
    receiver_district: int
    receiver_dtb: str
    receiver_mobile: str
    receiver_name: str
    receiver_province: str
    receiver_ring: str
    receiver_telno: str
    receiver_zip: str
    refund_status: int
    remark_flag: int
    reserve: str
    revert_reason: str
    sales_score: str
    salesman_id: int
    sendbill_template_id: str
    shop_id: str
    shop_name: str
    shop_no: str
    shop_platform_id: str
    shop_remark: str
    single_spec_no: str
    split_from_trade_id: str
    split_package_num: str
    src_tids: str
    stockout_no: str
    tax: str
    tax_rate: str
    to_deliver_time: str
    trade_from: int
    trade_id: int
    trade_mask: str
    trade_no: str
    trade_prepay: str
    trade_status: int
    trade_type: int
    unmerge_mask: str
    version_id: int
    volume: str
    warehouse_id: str
    warehouse_no: str
    warehouse_type: int
    weight: str
    trade_time: str  # 下单时间

    # 额外添加的字段
    trade_status_zh: str
    has_split_order: str
    warehouse_name: str
    all_goods_info: str  # spu名称+数量复合输出
    is_archived: bool = False
    consign_status_zh: str
    consign_time: str
    is_split: bool
    is_multi_packages: bool
    goods_item_count: int
    to_deliver_time_datetime: datetime
    trade_time_datetime: datetime

    def __init__(self, **data):
        super().__init__(**data)
        if self.trade_status is not None and WdtTradeStatus.has_value(self.trade_status):
            self.trade_status_zh = WdtTradeStatus(self.trade_status).label
        self.has_split_order = "是" if self.split_from_trade_id != "0" else "否"
        self.is_split = self.split_from_trade_id != "0"
        self.goods_item_count = 0
        if self.goods_list:
            for item in self.goods_list:
                self.goods_item_count += int(float(item.num or "0"))
            self.all_goods_info = ",".join(
                [f"名称：{g.goods_name} 数量：{int(float(g.num or '0'))}" for g in self.goods_list]
            )
        else:
            self.all_goods_info = ""

        self.consign_status_zh = Trade.get_consign_status_zh(self.consign_status)
        if self.to_deliver_time:
            if self.to_deliver_time == "任意时间":
                self.to_deliver_time_datetime = None  # type: ignore[assignment]
            else:
                self.to_deliver_time_datetime = arrow.get(self.to_deliver_time, tzinfo="Asia/Shanghai").datetime
        if self.trade_time and self.trade_time != "0000-00-00 00:00:00":
            self.trade_time_datetime = arrow.get(self.trade_time, tzinfo="Asia/Shanghai").datetime

    @classmethod
    def get_consign_status_zh(cls, consign_status):
        if consign_status == 0:
            return "无出库"
        status_mapping = {
            1: "验货",
            2: "称重",
            4: "出库",
            8: "物流同步",
            16: "分拣",
            32: "档口",
            64: "拣货",
            128: "供销回传成功",
            256: "供销回传失败",
        }
        statuses = []
        for code, description in status_mapping.items():
            if consign_status & code:
                statuses.append(description)
        return ",".join(statuses)

    @classmethod
    def get_origin_trade_with_all_goods(cls, trades):
        """
        将所有的商品信息都加入到最老的原始单中。
        :param trades:
        :return:
        """
        need_merge_fields = [
            "logistics_name",
            "logistics_no",
            "warehouse_no",
        ]
        trade = trades[0].copy()
        trade.paid = str(sum([float(t.paid) for t in trades]))
        for field in need_merge_fields:
            setattr(trade, field, ",".join([getattr(t, field) for t in trades]))
        trade.goods_list = []
        for t in trades:
            trade.goods_list.extend(t.goods_list)
        return trade

    @classmethod
    def merge_trades(cls, trades):
        need_merge_fields = [
            "logistics_name",
            "logistics_no",
            "warehouse_no",
        ]
        trade = trades[0].copy()
        trade.paid = str(sum([float(t.paid) for t in trades]))
        for field in need_merge_fields:
            setattr(trade, field, ",".join([getattr(t, field) for t in trades]))
        trade.goods_list = []
        # 在合并商品列表时，如果是相同的spec_no，数量累加即可，不要添加多个同spec_no的GoodsItem
        spec_no_to_good_detail = {}
        for t in trades:
            for good_item in t.goods_list:
                if good_item.spec_no not in spec_no_to_good_detail:
                    spec_no_to_good_detail[good_item.spec_no] = good_item
                else:
                    spec_no_to_good_detail[good_item.spec_no].num = int(float(good_item.num)) + int(
                        float(spec_no_to_good_detail[good_item.spec_no].num)
                    )
        trade.goods_list = list(spec_no_to_good_detail.values())
        return trade

    def __getattribute__(self, item):
        merged_fields = ["goods_no", "goods_name", "spec_no", "spec_name"]

        if item in merged_fields:
            return ",".join([getattr(g, item) for g in self.goods_list])
        else:
            return super().__getattribute__(item)


class Warehouse(BaseModel):
    address: str
    api_key: str
    api_object_id: str
    city: str
    cod_logistics_id: str
    contact: str
    coordinates_x: str
    coordinates_y: str
    created: str
    district: str
    division_id: str
    ext_warehouse_no: str
    flag: str
    is_defect: int
    is_disabled: int
    is_outer_goods: str
    is_outer_stock: str
    match_warehouse_id: str
    mobile: str
    modified: str
    name: str
    picker_num: str
    priority: str
    prop1: str
    prop2: str
    province: str
    remark: str
    shop_id: str
    sub_type: str
    tag: str
    telno: str
    type: int
    warehouse_id: str
    warehouse_no: str
    warehouse_type: str
    zip: str


class Shop(BaseModel):
    account_id: str
    shop_id: int
    account_nick: str
    shop_no: str
    shop_name: str
    platform_id: int


@make_fields_optional
class ShopQueryResp(BaseModel):
    @make_fields_optional
    class Response(BaseModel):
        errorcode: int
        message: str
        code: int
        shoplist: List[Shop]

    response: Response


@make_fields_optional
class WarehouseQueryResp(BaseModel):
    @make_fields_optional
    class Response(BaseModel):
        errorcode: int
        message: str
        total_count: int
        warehouses: List[Warehouse]
        code: int
        sub_code: str
        sub_message: str
        request_id: str

    response: Response


@make_fields_optional
class TradePushResp(BaseModel):
    @make_fields_optional
    class Response(BaseModel):
        errorcode: int
        message: str
        new_count: int
        chg_count: int

    response: Response


@make_fields_optional
class TradeQueryResp(BaseModel):
    @make_fields_optional
    class Response(BaseModel):
        errorcode: int
        message: str
        total_count: int
        trades: List[Trade]
        code: int
        sub_code: str
        sub_message: str
        request_id: str

    response: Response


def get_skus_from_wdt_trades(origin_trade: Trade, tid: str | None = None) -> List[SKU]:
    """
    如果填写了 tid，则会根据 tid 来过滤一下，排除非该 tid 的商品。（主要针对合并单）
    """
    sku_mapping: dict[str, SKU] = {}
    suite_mapping: dict[str, SKU] = {}
    for good in origin_trade.goods_list:
        if tid is not None and good.src_tid != tid:
            continue
        if not good.suite_no:
            if sku := sku_mapping.get(good.spec_no):
                if sku.qty is None:
                    sku.qty = int(float(good.num)) if good.num is not None else None
                else:
                    current_sku_qty = int(float(good.num)) if good.num is not None else 0
                    sku.qty = sku.qty + current_sku_qty
                sku_mapping.update({good.spec_no: sku})
            else:
                sku_mapping.update(
                    {
                        good.spec_no: SKU(
                            spu_id=good.goods_no,
                            sku_id=good.spec_no,
                            outer_sku_id=good.spec_no,
                            qty=int(float(good.num)) if good.num is not None else None,
                            pic=None,
                            price=float(good.price) if good.price is not None else None,
                            outer_spu_id=None,
                            source="erp",
                            type=1,
                        )
                    }
                )
        else:
            if good.suite_no in suite_mapping:
                continue
            suite_mapping.update(
                {
                    good.suite_no: SKU(
                        spu_id=str(good.suite_id),
                        sku_id=good.suite_no,
                        outer_sku_id=good.suite_no,
                        qty=int(float(good.suite_num)) if good.suite_num is not None else None,
                        pic=None,
                        price=float(good.suite_amount) if good.suite_amount is not None else None,
                        type=2,
                        outer_spu_id=None,
                        source="erp",
                    )
                }
            )
    return list(sku_mapping.values()) + list(suite_mapping.values())


@make_fields_optional
class RefundOrder(BaseModel):
    order_id: int
    refund_id: int
    oid: str
    tid: str
    src_no: str
    process_status: int
    sales_tid: str
    order_num: int
    price: float
    cost_price: float
    original_price: float
    discount: float
    paid: float
    refund_num: int
    refund_order_amount: float
    total_amount: float
    spec_no: str
    goods_name: str
    goods_no: str
    spec_name: str
    spec_code: str
    suite_no: str
    suite_name: str
    suite_num: int
    stockin_num: int
    remark: str

    @root_validator(pre=True)
    def parse_string_numbers(cls, values):
        for field in ["order_num", "refund_num", "suite_num", "stockin_num"]:
            if field in values and isinstance(values[field], str):
                values[field] = int(float(values[field]))
        return values


@make_fields_optional
class Refund(BaseModel):
    refund_id: int
    refund_no: str
    api_outer_no: str
    platform_id: int
    src_no: str
    type: str
    process_status: int
    status: int
    return_logistics_name: str
    return_logistics_no: str
    shop_name: str
    tid: str
    shop_id: str
    stockin_pre_no: str
    refund_order_list: List[RefundOrder]
    goods_amount: float
    refund_amount: float

    process_status_zh: str
    status_zh: str

    @root_validator(pre=True)
    def generate_all_readable_str(cls, values):
        status = values.get("status")
        status_map = {
            0: "未处理",
            1: "取消",
            2: "申请中",
            3: "待退货",
            4: "待收货",
            5: "成功",
            10: "换货待处理",
            20: "待买家退货",
            30: "买家已退货，待收货",
            40: "待买家修改",
            50: "待发出换货商品",
            55: "待买家收货",
            60: "换货待处理",
            70: "换货关闭",
            80: "换货成功",
        }
        values["status_zh"] = status_map.get(status) or ""

        process_status = values.get("process_status")
        process_status_map = {
            5: "补款",
            10: "已取消",
            20: "待审核",
            30: "已同意",
            40: "已拒绝",
            50: "待财审",
            60: "待收货",
            63: "待推送",
            64: "推送失败",
            65: "委外待收货",
            69: "待收货(已结算)",
            70: "部分到货",
            71: "部分到货(已结算)",
            80: "待结算",
            90: "已完成",
        }
        values["process_status_zh"] = process_status_map.get(process_status) or ""
        return values


@make_fields_optional
class RefundQueryResp(BaseModel):
    @make_fields_optional
    class Response(BaseModel):
        errorcode: int
        message: str
        refunds: List[Refund]

    response: Response


@make_fields_optional
class StockinPreOrderDetail(BaseModel):
    spec_no: str
    goods_count: int
    discount: float
    cost_price: float
    src_price: float
    tax_price: float
    tax_amount: float
    total_cost: float
    remark: str
    goods_name: str
    goods_no: str
    spec_name: str
    spec_code: str
    brand_name: str
    brand_no: str

    @root_validator(pre=True)
    def parse_string_numbers(cls, values):
        for field in ["goods_count"]:
            if field in values and isinstance(values[field], str):
                values[field] = int(float(values[field]))
        return values


@make_fields_optional
class StockinPreOrder(BaseModel):
    outer_no: str
    stockin_no: str
    stockin_pre_no: str
    status: str
    src_order_no: str
    logistics_name: str
    logistics_no: str
    warehouse_no: str
    name: str
    b_stockin: str
    shortname: str
    goods_count: int
    goods_type_count: int
    details_list: List[StockinPreOrderDetail]

    @root_validator(pre=True)
    def parse_string_numbers(cls, values):
        for field in ["goods_count"]:
            if field in values and isinstance(values[field], str):
                values[field] = int(float(values[field]))
        return values


@make_fields_optional
class StockinPreOrderQueryResp(BaseModel):
    @make_fields_optional
    class Response(BaseModel):
        code: int
        message: str
        total_count: int
        stockin_pre_order_list: List[StockinPreOrder]

    response: Response


@make_fields_optional
class StockinDetail(BaseModel):
    rec_id: int
    stockin_id: int
    src_order_detail_id: int
    spec_no: str
    goods_count: float
    num: float
    goods_no: str
    goods_name: str
    price: float

    diff: int  # 计算得出


@make_fields_optional
class Stockin(BaseModel):
    stockin_id: int
    order_no: str
    warehouse_no: str
    warehouse_name: str
    shop_no: str
    shop_remark: str
    src_order_no: str
    process_status: int
    status: int
    status_str: str
    stockin_time: str
    created_time: str
    associated_time: str
    remark: str
    trade_no: str
    trade_type: str
    order_type: int
    tid: str
    oid: str
    details_list: List[StockinDetail]

    status_readable_str: str
    readable_process_status: str
    diff_sum: int

    @root_validator(pre=True)
    def generate_all_readable_str(cls, values):
        status = values.get("status")
        status_map = {"10": "已取消", "20": "编辑中", "30": "待审核", "60": "待结算", "80": "已完成"}
        values["status_readable_str"] = status_map.get(status) or ""

        process_status = values.get("process_status")
        process_status_map = {
            10: "已取消",
            20: "待审核",
            30: "已同意",
            40: "已拒绝",
            50: "待财审",
            60: "待收货",
            63: "待推送",
            64: "推送失败",
            65: "委外待收货",
            69: "待收货(已结算)",
            70: "部分到货",
            71: "部分到货(已结算)",
            80: "待结算",
            90: "已完成",
        }
        values["readable_process_status"] = process_status_map.get(process_status) or ""
        return values


@make_fields_optional
class StockinOrderQueryRefundResp(BaseModel):
    class Response(BaseModel):
        errorcode: int
        message: str
        stockin_list: List[Stockin]

    response: Response


@make_fields_optional
class Stockout(BaseModel):
    @make_fields_optional
    class Details(BaseModel):
        @make_fields_optional
        class Position(BaseModel):
            batch_id: str
            batch_no: str
            position_goods_count: str
            stockout_order_detail_id: str
            position_no: str

        rec_id: int
        spec_no: str
        batch_id: str
        batch_no: str
        print_batch_no: str
        goods_count: float
        position_list: List[Position]

    stockout_id: int
    order_no: str
    src_order_no: str
    warehouse_no: str
    consign_time: str
    trade_status: int
    details_list: List[Details]


@make_fields_optional
class StockoutOrderQueryTradeResp(BaseModel):
    @make_fields_optional
    class Response(BaseModel):
        code: int
        message: str
        total_count: int
        stockout_list: List[Stockout]

    response: Response


@make_fields_optional
class SuiteQueryResp(BaseModel):
    @make_fields_optional
    class Response(BaseModel):
        @make_fields_optional
        class Suite(BaseModel):
            suite_id: int
            suite_no: str

        code: int
        message: str
        total_count: int
        suites: List[Suite]

    response: Response


@make_fields_optional
class TradeLogDetail(BaseModel):
    rec_id: int
    trade_id: int
    type: int
    data: int
    fullname: str
    message: str
    created: str

    readable_type_str: str

    @root_validator(pre=True)
    def generate_all_readable_str(cls, values):
        _type = int(values.get("type"))
        type_map = {
            1: "下单",
            2: "付款",
            3: "递交",
            4: "关闭",
            5: "部分关闭",
            6: "拦截出库",
            7: "退款",
            8: "部分退款",
            9: "订单审核",
            10: "订单强制审核不进财审",
            11: "订单强制审核进财审",
            12: "订单快速审核不进财审",
            13: "订单快速审核进财审",
            14: "订单审核不进财审",
            15: "订单审核进财审",
            16: "订单开发票",
            17: "平台更换货品",
            18: "更换规格",
            19: "更换货品",
            20: "修改物流",
            30: "订单驳回",
            33: "预订单转审核",
            35: "订单清楚驳回",
            45: "订单财务审核",
            46: "财务审核原因",
            49: "修改打印备注",
            50: "历史订单导入",
            51: "平台优惠金额发生变化",
            80: "客户打款,交易完成",
            90: "清除打印",
            91: "标记打印",
            100: "已验货",
            101: "已打包",
            102: "已称重",
            103: "已出库",
            104: "发货中",
            105: "已发货",
            107: "买家留言变更",
            108: "打印小票",
            120: "撤销出库",
            121: "驳回验货",
            122: "驳回称重",
            123: "归档操作",
            124: "反归档",
            133: "登记拣货员",
            134: "登记监视员",
            135: "预同步物流",
            140: "物流同步",
            141: "物流重新同步",
            142: "取消同步",
            143: "设置为同步成功",
            144: "供销端给分销端物流同步失败",
            145: "供销端发货重算邮费",
            150: "手工分配订单货品出库顺序",
            151: "取消手工分配货品出库顺序",
            155: "热敏获取物流单号",
            160: "销售出库单取消拦截",
            161: "刷新货品名称",
            162: "修改组合装打印方式",
            163: "销售出库明细的冻结，解冻操作",
            165: "销售出库单签入",
            166: "销售出库单签出",
            167: "销售出库单导入邮资",
            168: "拦截带有赠品的订单",
            169: "选择仓库,订单转入",
            170: "订单审核优先占用库存",
            171: "订单审核释放占用的库存",
            172: "强制恢复已经推送成功的委外出库单但是系统是已取消状态的",
            173: "自动审核出错",
            174: "订单审核刷新三级地址",
            175: "订单审核刷新原始订单地址到系统订单",
            178: "修改订单的证件号码",
            179: "子订单拆分",
            180: "从客服备注刷新收货人信息",
            200: "回写标旗和备注到平台",
            202: "通过激活时间拆分",
            203: "设置打印备注",
            204: "分配分拣员",
            205: "汇总其他分摊邮费",
            206: "定制订单自动换货",
            207: "延时审核转审核",
            208: "等未付转审核",
            210: "添加多物流单号",
            211: "更新多物流单号",
            212: "删除多物流单号",
            213: "o2o专用",
            214: "多级审核驳回",
            215: "激活时间更新失败",
            216: "激活时间更新",
            217: "平台更改规格名称",
            218: "订单校验",
            219: "虚拟仓直接发货",
            220: "平台换货系统自动更新",
            221: "回收物流单号",
            225: "订单平台异常",
            226: "阿里大药房取消订单失败",
            227: "供销端预订单预拆分",
            228: "新核单卡冻结订单",
            229: "收件人证件号变更",
            230: "批量补发订单",
            231: "修改发件人",
            232: "匹配爆款策略指定包装",
            250: "供销结算调整",
            300: "撤销委外出库单",
            301: "自动取消委外出库单",
            302: "WMS回传包材信息报错",
            303: "WMS回传邮资变更",
            304: "供销回传信息记录(货品金额、佣金、邮资)",
            310: "转入特殊单",
            311: "转出特殊单",
            314: "自动拆分异常",
            317: "PDA缺货标记拦截",
            318: "PDA协同拣货",
            319: "自定义绩效登记",
            320: "退换备注模板内容回传平台，如果超长记录在这个表中，否则记录在sales_refund_log中",
            321: "解除子母单绑定关系",
            322: "分销商昵称变更",
            323: "送货时间更新",
            324: "系统下载订单",
            325: "天猫优仓自流转改为非自流转",
            326: "物流拦截",
            328: "打印京东工业品配送单",
            329: "通过序列号批量出库功能导入序列号",
            331: "订单商品已标记为无需发货，订单自动取消发货",
            332: "订单货品均是虚拟货品，分拣单删除",
            333: "子订单退款失败",
            334: "订单转定制加工",
        }
        values["readable_type_str"] = type_map.get(_type)
        return values


@make_fields_optional
class TradeLogsQueryResp(BaseModel):
    class Response(BaseModel):
        errorcode: str
        message: str
        total_count: int
        logs_list: List[TradeLogDetail]

    response: Response


class StockQueryResp(BaseModel):
    class Response(BaseModel):
        class Stock(BaseModel):
            spec_no: str = Field(description="商家编码")
            warehouse_no: str = Field(description="仓库编码")
            warehouse_name: str = Field(description="仓库名称")
            stock_num: int = Field(description="库存量")
            avaliable_num: int = Field(description="可发库存")

            @validator("stock_num", pre=True)
            def check_stock_num(cls, v):
                try:
                    return int(float(v))
                except:  # noqa pylint: disable=bare-except
                    return v

            @validator("avaliable_num", pre=True)
            def check_avaliable_num(cls, v):
                try:
                    return int(float(v))
                except:  # noqa pylint: disable=bare-except
                    return v

        stocks: List[Stock]

    response: Response


class WdtTradeItem(BaseModel):
    goods_no: str
    spec_no: str
    goods_name: str


class WdtTradeInfo(Trade):
    pass


class WdtTradeOutput(BaseModel):
    orders: List[WdtTradeInfo]
    count: int


class Logistics(BaseModel):
    logistics_id: int
    logistics_no: str
    logistics_name: str
    logistics_type: int
    province: Optional[str]
    city: Optional[str]
    district: Optional[str]
    contact: Optional[str]
    telno: Optional[str]
    mobile: Optional[str]
    address: Optional[str]


class LogisticsResponse(BaseModel):
    code: int
    message: str
    logistics_list: List[Logistics]


class TradeUnfileResp(BaseModel):
    code: int
    message: str


class GetShopsResponse(BaseModel):
    class WDTShop(BaseModel):
        shop_id: str
        shop_name: str
        shop_no: str
        platform_id: str
        sub_platform_id: str
        is_disabled: str

    shoplist: list[WDTShop]
    code: int
    total_count: int


@make_fields_optional
class Spec(BaseModel):
    spec_id: str
    spec_no: str = Field(description="代表单品（SKU）所有属性的唯一编号，用于系统单品的区分")
    spec_name: str = Field(description="规格名称")
    retail_price: float = Field(description="零售价")
    img_url: str = Field(description="图片url")
    deleted: int = Field(description="0代表未删除  大于0代表已删除")

    tax_code: str  # 税务编码
    tax_rate: float  # 税率


@make_fields_optional
class QuerySkuResp(BaseModel):
    @make_fields_optional
    class Goods(BaseModel):
        goods_id: int
        goods_no: str = Field(description="代表SPU所有属性的唯一编号，用于系统货品的区分")
        goods_name: str = Field(description="货品名称")
        short_name: str = Field(description="货品简称")
        spec_list: list[Spec]
        brand_name: str = Field(description="品牌名称")

    goods_list: list[Goods]


@make_fields_optional
class QuerySuiteResp(BaseModel):
    @make_fields_optional
    class Suite(BaseModel):
        @make_fields_optional
        class SpecForSuite(BaseModel):
            suite_id: int
            spec_no: str
            spec_name: str
            num: float = Field(description="单品数量")
            deleted: int = Field(description="删除状态,和goods_suite一致(0:正常 1:已删除) 2:特殊处理,该明细对应的单品库存变化时不进行库存同步")

        suite_id: int
        suite_no: str = Field(description="代表组合装商品所有属性的唯一编码，用于系统组合装商品的区分")
        suite_name: str = Field(description="组合装名称")
        short_name: str = Field(description="组合装简称")
        retail_price: float = Field(description="零售价")
        specs_list: list[SpecForSuite]
        deleted: str = Field(description="删除状态  未删除=0   已删除＞0")
        brand_name: str = Field(description="品牌名称")

    suites: list[Suite]


@make_fields_optional
class StockRefundLogistics(BaseModel):
    logistics_type: str
    logistics_name: str
    logistics_no: str
    weight: float
    input_operator: str
    refund_operator: str
    is_in_count: str
    in_time: str
    modified: str
    created: str
    refund_flag: str


@make_fields_optional
class QueryStockRefundLogisticsResp(BaseModel):
    stock_refund_logistics: List[StockRefundLogistics]
    message: str
    code: int
    total_count: int


class StockinRefundDetail(BaseModel):
    spec_no: str
    stockin_num: int
    stockin_price: float


class StockinRefundPushReq(BaseModel):
    refund_no: str
    outer_no: str
    warehouse_no: Optional[str]
    is_check: int
    remark: Optional[str]
    detail_list: List[StockinRefundDetail]


@make_fields_optional
class StockinRefundPushResp(BaseModel):
    code: int
    message: str


@make_fields_optional
class LogisticsMultiQueryResp(BaseModel):
    @make_fields_optional
    class Logistics(BaseModel):
        logistics_no: str
        logistics_id: int
        logistics_name: str

    logistics_multi_list: List[Logistics]
    code: int
    message: str
    total_count: int
