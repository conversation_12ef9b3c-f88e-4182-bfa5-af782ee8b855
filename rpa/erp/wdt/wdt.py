import hashlib
import json
import time
from typing import TYPE_CHECKING
from typing import Any
from typing import Dict
from typing import Optional
from typing import Type
from typing import TypeVar
from typing import Union

import jwt
from dramatiq import Retry
from loguru import logger
from pydantic import BaseModel
from result import Err
from result import Ok
from result import Result

from robot_processor.client import token_bucket_limiter
from robot_processor.client_mixins import Session
from robot_processor.enums import ErpType
from robot_processor.error.client_request import WdtRequestError
from robot_processor.shop.models import ErpInfo
from robot_processor.shop.schema import WDTGrantMeta
from robot_processor.utils import ts2date
from rpa.conf import rpa_config as conf
from rpa.erp.wdt.schemas import GetShopsResponse
from rpa.erp.wdt.schemas import LogisticsMultiQueryResp
from rpa.erp.wdt.schemas import LogisticsResponse
from rpa.erp.wdt.schemas import PushTrade
from rpa.erp.wdt.schemas import QuerySkuResp
from rpa.erp.wdt.schemas import QueryStockRefundLogisticsResp
from rpa.erp.wdt.schemas import QuerySuiteResp
from rpa.erp.wdt.schemas import RefundQueryResp
from rpa.erp.wdt.schemas import SalesTrade
from rpa.erp.wdt.schemas import SalesTradeReplenishResp
from rpa.erp.wdt.schemas import Shop
from rpa.erp.wdt.schemas import ShopQueryResp
from rpa.erp.wdt.schemas import StockinOrderQueryRefundResp
from rpa.erp.wdt.schemas import StockinPreOrderQueryResp
from rpa.erp.wdt.schemas import StockinRefundPushReq
from rpa.erp.wdt.schemas import StockinRefundPushResp
from rpa.erp.wdt.schemas import StockoutOrderQueryTradeResp
from rpa.erp.wdt.schemas import StockQueryResp
from rpa.erp.wdt.schemas import SuiteQueryResp
from rpa.erp.wdt.schemas import TradeLogsQueryResp
from rpa.erp.wdt.schemas import TradePushResp
from rpa.erp.wdt.schemas import TradeQueryResp
from rpa.erp.wdt.schemas import TradeUnfileResp
from rpa.erp.wdt.schemas import WarehouseQueryResp

RespT = TypeVar("RespT", bound=BaseModel)

if TYPE_CHECKING:
    from robot_processor.form.symbol_table import Credential  # noqa: F401


class WdtRateLimitError(WdtRequestError):
    pass


class WdtOpenAPIClient:
    session = Session()

    def __init__(self, sid=None, credential=None):
        """
        Args:
            sid (str | None): shop.sid
            credential (Credential.WDT | None): 旺店通授权信息
        Raises:
            AssertionError
        """
        if credential:
            self.erp_info = ErpInfo(meta=credential.dict())
        else:
            erp_info = ErpInfo.get_by_sid(sid, ErpType.WDT)
            assert erp_info, f"店铺 {sid} 无旺店通授权信息"
            self.erp_info = erp_info
        assert self.sid is not None

    @property
    def sid(self):
        return self.erp_info.meta["sid"]

    @property
    def app_key(self):
        return self.erp_info.meta["app_key"]

    @property
    def app_secret(self):
        return self.erp_info.meta["app_secret"]

    def _sorted_params(self, params: dict):
        key_val = {}
        for i in sorted(params):
            key_val[i] = params[i]
        return key_val

    def _sign(self, params: dict):
        sign_str = ""
        for key in params:
            val = str(params[key])
            key = str("{:02}".format(len(key))) + "-" + key
            val = str("{:04}".format(len(val))) + "-" + val
            sign_str += "{}:{};".format(key, val)

        sign_str = sign_str[:-1] + self.app_secret
        sign = hashlib.md5((sign_str).encode("utf-8")).hexdigest()

        return sign

    def _request(self, relative_url: str, data: dict, resp_class: Type[RespT]) -> RespT:
        """
        Raises:
            WdtRequestError
        """

        params = {"appkey": self.app_key, "sid": self.sid, "timestamp": str(int(time.time()))}
        params.update(data)
        params = self._sorted_params(params)
        sign = self._sign(params)
        params["sign"] = sign
        headers = {"Content-Type": "application/x-www-form-urlencoded;"}
        resp = self.session.post(
            url=conf.WDT_OPENAPI_ENDPOINT + relative_url,
            params=params,
            headers=headers,
            timeout=conf.WDT_REQUEST_TIMEOUT,
        )

        logger.info(f"wdt request url: {relative_url} params: {params} resp: {resp.text}")
        try:
            resp_json = resp.json()
        except Exception as e:
            logger.warning(f"wdt response json error: {resp.text}")
            raise e
        if int(resp_json.get("code", "-1")) != 0:
            raise WdtRequestError(req=(relative_url, params), res=resp_json, message=resp_json["message"])
        return resp_class.parse_obj(resp_json)

    def _amend_sales_trade(self, trade: SalesTrade) -> SalesTrade:
        """
        修正一些字段格式，兼容旺店通
        """
        if trade.receiver_address:
            # 地址不能带换行符
            # https://redmine.leyantech.com/issues/595778 https://redmine.leyantech.com/issues/604530
            trade.receiver_address = trade.receiver_address.replace("\n", " ").replace(r"\n", "")
        return trade

    def sales_trade_replenish(self, trade: SalesTrade) -> SalesTradeReplenishResp:
        # 如果源订单已完成但还是报错订单未审核，检查地址中的字符串是不是有换行符
        trade = self._amend_sales_trade(trade)
        return self._request(
            "sales_trade_replenish.php",
            {"trade_list": json.dumps(trade.dict(), ensure_ascii=False)},
            SalesTradeReplenishResp,
        )

    def trade_unfile(self, trade_no: str):
        data = {"trade_no": trade_no}
        return self._request("trade_unfile.php", data, TradeUnfileResp)

    def get_logistic(self) -> LogisticsResponse:
        return self._request("logistics.php", {}, LogisticsResponse)

    def query_skus_by_sku_id(self, sku_id: str, page_no: int = 0, page_size: int = 100):
        return self._request(
            "goods_query.php",
            {
                "spec_no": sku_id,
                "page_no": page_no,
                "page_size": page_size,
            },
            QuerySkuResp,
        )

    def query_skus_by_spu_id(self, spu_id: str, page_no: int = 0, page_size: int = 100):
        return self._request(
            "goods_query.php",
            {
                "goods_no": spu_id,
                "page_no": page_no,
                "page_size": page_size,
            },
            QuerySkuResp,
        )

    def query_combine_skus_by_combine_sku_id(self, combine_sku_id: str, page_no: int = 0, page_size: int = 100):
        return self._request(
            "suites_query.php",
            {
                "suite_no": combine_sku_id,
                "page_no": page_no,
                "page_size": page_size,
            },
            QuerySuiteResp,
        )

    def query_stock_refund_logistics(self, logistics_no: str):
        return self._request(
            "stock_refund_logistics_query.php",
            {
                "logistics_no": logistics_no,
            },
            QueryStockRefundLogisticsResp,
        )

    def stock_refund_push(self, req: StockinRefundPushReq):
        return self._request(
            "stockin_refund_push.php",
            {"stockin_refund_info": json.dumps(req.dict(exclude_none=True), ensure_ascii=False)},
            StockinRefundPushResp,
        )

    def logistics_multi_query(self, trade_no: str):
        return self._request("logistics_multi_query.php", {"trade_no": trade_no}, LogisticsMultiQueryResp)


class WDTOpenPlatformAPIClient:
    session = Session()

    def __init__(self, meta: str | dict):
        if isinstance(meta, str):
            self.meta: WDTGrantMeta = WDTGrantMeta.parse_raw(meta)
        else:
            self.meta = WDTGrantMeta.parse_obj(meta)

    @property
    def sid(self):
        return self.meta.sid

    @property
    def app_key(self):
        return self.meta.app_key

    @property
    def app_secret(self):
        return self.meta.app_secret

    @staticmethod
    def sorted_params(params: dict):
        key_val = {}
        for i in sorted(params):
            key_val[i] = params[i]
        return key_val

    def generate_sign(self, params: dict):
        sign_str = ""
        for key in params:
            val = str(params[key])
            key = str("{:02}".format(len(key))) + "-" + key
            val = str("{:04}".format(len(val))) + "-" + val
            sign_str += "{}:{};".format(key, val)

        sign_str = sign_str[:-1] + self.app_secret
        sign = hashlib.md5(sign_str.encode("utf-8")).hexdigest()

        return sign

    def send(self, relative_url: str, data: dict) -> dict[str, Any]:
        """
        Raises:
            WdtRequestError
        """

        params = {"appkey": self.app_key, "sid": self.sid, "timestamp": str(int(time.time()))}
        params.update(data)
        params = self.sorted_params(params)
        sign = self.generate_sign(params)
        params["sign"] = sign
        headers = {"Content-Type": "application/x-www-form-urlencoded;"}
        resp = self.session.post(
            url=conf.WDT_OPENAPI_ENDPOINT + relative_url,
            params=params,
            headers=headers,
            timeout=conf.WDT_REQUEST_TIMEOUT,
        )

        logger.info(f"wdt request url: {relative_url} params: {params} resp: {resp.text}")
        try:
            resp_json = resp.json()
        except Exception as e:
            logger.warning(f"wdt response json error: {resp.text}")
            raise e
        if int(resp_json.get("code", "-1")) != 0:
            raise WdtRequestError(req=(relative_url, params), res=resp_json, message=resp_json["message"])
        return resp_json

    def get_shops(self) -> list[GetShopsResponse.WDTShop]:
        shop_list: list[GetShopsResponse.WDTShop] = []
        page_no = 0
        while True:
            resp = self.send("shop.php", {"page_no": page_no})
            parsed_response: GetShopsResponse = GetShopsResponse.parse_obj(resp)
            if len(parsed_response.shoplist) == 0:
                break
            shop_list = shop_list + parsed_response.shoplist
            page_no += 1
        return shop_list

    def check_grant_is_valid(self) -> bool:
        try:
            resp = self.send("trade_query.php", {"trade_no": "1"})
            if resp.get("code") == 0:
                return True
            else:
                return False
        except Exception as e:
            logger.error("旺店通企业版授权不可用 {}", e)
            return False


class WdtClient:
    session = Session()

    def __init__(self, sid=None, credential=None, erp_info=None):
        """
        Args:
            sid (str | None): shop.sid
            credential (Credential.WDT | None): 旺店通授权信息
            erp_info (ErpInfo | None):
        Raises:
            AssertionError
        """
        from dataclasses import asdict
        from dataclasses import is_dataclass

        if credential:
            if is_dataclass(credential):
                self.erp_info = ErpInfo(meta=asdict(credential))
            else:
                self.erp_info = ErpInfo(meta=credential.dict())
        else:
            erp_info = erp_info or ErpInfo.get_by_sid(sid, ErpType.WDT)
            assert erp_info, f"店铺 {sid} 无旺店通授权信息"
            self.erp_info = erp_info
        assert self.sid is not None
        self.after_sale_shop_no = self.erp_info.meta["after_sale_shop_no"]
        assert self.after_sale_shop_no is not None

    @property
    def sid(self):
        return self.erp_info.meta["sid"]

    @staticmethod
    def sorted_params(params):
        sorted_params = {}
        for key in sorted(params):
            sorted_params[key] = params[key]
        return sorted_params

    def make_header(self):
        token = self._make_token
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + token,
        }
        return headers

    @property
    def _make_token(self):
        payload = {"shop": {"id": self.sid}}
        return jwt.encode(payload, conf.WDT_JWT_KEY, algorithm="HS256")

    def _sign(self, params):
        sign_str = conf.WDT_APP_SECRET + "".join([f"{k}{v}" for k, v in params.items()]) + conf.WDT_APP_SECRET

        return hashlib.md5(sign_str.encode("utf-8")).hexdigest().upper()

    def _request(self, method, data: dict, resp_class: Type[RespT]) -> RespT:
        """
        Raises:
            AssertionError
            WdtRequestError
        """

        params = {
            "method": method,
            "app_key": conf.WDT_APP_KEY,
            "timestamp": ts2date(time.time()),
            "sid": self.sid,
            "target_app_key": conf.WDT_TARGET_APP_KEY,
            "format": "json",
            "v": 2.0,
            "sign_method": "md5",
        }
        params.update(data)
        params = WdtClient.sorted_params(params)
        sign_str = self._sign(params)
        params["sign"] = sign_str
        logger.info(f"method: {method}, sign: {sign_str}, data: {data}")
        resp = self.session.post(conf.WDT_ENDPOINT, params=params, timeout=conf.WDT_REQUEST_TIMEOUT)
        logger.info(f"resp: {resp.text}")
        resp_json = resp.json()
        # 旺店通限流: https://open.wangdian.cn/qyb/open/guide?path=guide_cwmsm
        if resp_json["response"].get("sub_code") == "1012":
            raise WdtRateLimitError(req=(method, params, data), res=resp_json, message=resp_json["response"]["message"])
        if resp_json["response"].get("errorcode"):
            errorcode = int(resp_json["response"].get("errorcode"))
            if errorcode != 0:
                raise WdtRequestError(
                    req=(method, params, data), res=resp_json, message=resp_json["response"]["message"]
                )
        elif resp_json["response"].get("code"):
            code = int(resp_json["response"].get("code"))
            if code != 0:
                raise WdtRequestError(
                    req=(method, params, data), res=resp_json, message=resp_json["response"]["message"]
                )
        if int(resp_json["response"].get("errorcode", -1)) != 0:
            raise WdtRequestError(req=(method, params, data), res=resp_json, message=resp_json["response"]["message"])

        return resp_class.parse_obj(resp_json)

    def trade_push(self, push_trade: PushTrade) -> TradePushResp:
        return self._request(
            "wdt.trade.push",
            {
                "shop_no": self.after_sale_shop_no,
                "trade_list": json.dumps([push_trade.dict()], ensure_ascii=False),
                "switch": 0,
            },
            TradePushResp,
        )

    def after_sale_upload(self, data: PushTrade, check_rate_limit=True) -> Result[None, str]:
        """售后上传
        Raises:
            AssertionError
            WdtRequestError
        """
        if check_rate_limit:
            token_bucket_key = "erp:wdt:after_sale_upload"
            if not token_bucket_limiter.try_acquire_token_for_store(token_bucket_key, self.sid):
                raise Retry(message=f"{token_bucket_key}:{self.sid} is rate limited", delay=3000)

        # 转换物流公司为物流公司编码，待前端下拉支持kv接口后移除
        if conf.WDT_LOGISTICS_TYPE:
            data_json = json.loads(conf.WDT_LOGISTICS_TYPE)
            for k in data_json:
                if data.logistics_type == data_json[k]:
                    data.logistics_type = k
        try:
            self.trade_push(data)
        except TimeoutError as e:
            return Err(str(e))
        except (AssertionError, WdtRequestError) as e:
            return Err(str(e))

        return Ok(None)

    def get_sent_orders_by_time(
        self, page_no, page_size, start_time, end_time, shop_nos: str | None = None
    ) -> (TradeQueryResp):
        params = {
            "start_time": start_time,
            "end_time": end_time,
            "status": 95,
            "page_size": page_size,
            "page_no": page_no,
        }
        if shop_nos:
            params["shop_nos"] = shop_nos
        return self._request("wdt.trade.query", params, TradeQueryResp)

    def trade_query(
        self,
        wdt_tid: Union[str, list, None] = None,
        logistics_no: Optional[str] = None,
        trade_no: Optional[str] = None,
        check_rate_limit=False,
        blocking=False,
    ) -> TradeQueryResp:
        """
        wdt_tid: 平台订单号
        logistics_no: 快递单号
        trade_no: 系统订单号，wdt_tid和trade_no同时存在，优先使用trade_no
        """

        def filter_same_trade_no(orders):
            """去重"""
            new_orders = []
            trade_no_list = []
            for order in orders:
                if order.trade_no not in trade_no_list:
                    new_orders.append(order)
                    trade_no_list.append(order.trade_no)
            return new_orders

        if check_rate_limit:
            token_bucket_key = "erp:wdt:trade"
            if not blocking:
                acquired = token_bucket_limiter.try_acquire_token_for_store(token_bucket_key, self.sid)
            else:
                acquired = self._blocking_acquire(token_bucket_key, self.sid)
            if not acquired:
                raise Retry(message=f"{token_bucket_key}:{self.sid} is rate limited", delay=3000)
        req_data: Dict[str, Any]
        if trade_no:
            req_data = {"trade_no": trade_no}
        elif logistics_no:
            req_data = {"logistics_no": logistics_no}
        else:
            if isinstance(wdt_tid, list):
                wdt_tid = wdt_tid[0].get("tid")
            req_data = {"src_tid": wdt_tid}

        result = self._request("wdt.trade.query", req_data, TradeQueryResp)
        # 如果返回的结果中没有网店销售订单，说明最老的订单被归档了，需要merge归档订单进来
        if not [i for i in result.response.trades if i.trade_type == 1]:
            history_result = self._request("wdt.his.trade.query", req_data, TradeQueryResp)
            for trade in history_result.response.trades:
                trade.is_archived = True
            result.response.trades += history_result.response.trades
        result.response.trades = filter_same_trade_no(result.response.trades)
        return result

    def shop_query(self) -> ShopQueryResp:
        data = {"page_size": 100}
        return self._request("wdt.shop.query", data, ShopQueryResp)

    def all_shops(self) -> list[Shop]:
        shop_list: list[Shop] = []
        page_no = 0
        while True:
            data = {"page_size": 100, "page_no": page_no}
            resp = self._request("wdt.shop.query", data, ShopQueryResp)
            if len(resp.response.shoplist) == 0:
                break
            shop_list = shop_list + resp.response.shoplist
            page_no += 1
        return shop_list

    def warehouse_query(self, warehouse_no: Optional[str] = None) -> WarehouseQueryResp:
        data: Dict[str, Any] = {"page_size": 100}
        if warehouse_no:
            data["warehouse_no"] = warehouse_no
        return self._request("wdt.warehouse.query", data, WarehouseQueryResp)

    def after_sale_logistics(self, wdt_tid: str) -> Result[dict, str]:
        """售后上传
        Raises:
            AssertionError
            WdtRequestError
        """
        try:
            resp = self.trade_query(wdt_tid)
            logger.info(f'"after_sale_logistics resp_json:" {resp.response}')
            if resp.response.total_count == 0 or not resp.response.trades:
                return Err(f"订单<{wdt_tid}>为审核或订单不存在")
            first_trade = resp.response.trades[0]
            logistics_no = first_trade.logistics_no
            logistics_name = first_trade.logistics_name
            warehouse_no = first_trade.warehouse_no
            receivable = first_trade.receivable  # 应收
            paid = first_trade.paid  # 已付
            wh_name = ""
            if warehouse_no:
                wh_resp = self.warehouse_query(warehouse_no)
                logger.info(f'"wh_resp:" {wh_resp}')
                if wh_resp.response.errorcode == 0 and wh_resp.response.warehouses:
                    wh_name = wh_resp.response.warehouses[0].name
            return Ok(
                {
                    "logistics_no": str(logistics_no),
                    "logistics_name": str(logistics_name),
                    "warehouse_no": str(warehouse_no),
                    "receivable": receivable,
                    "paid": str(paid),
                    "warehouse_name": str(wh_name),
                }
            )
        except (AssertionError, WdtRequestError) as e:
            return Err(str(e))

    def stock_query(self, spec_no: str, warehouse_no=None) -> StockQueryResp:
        """
        References:
            https://open.wangdian.cn/qyb/open/apidoc/doc?path=stock_query.php
        """
        data = {"spec_no": spec_no}
        if warehouse_no:
            data["warehouse_no"] = warehouse_no
        return self._request("wdt.stock.query", data, StockQueryResp)

    def refund_query_by_src_refund_no(self, src_refund_no: str) -> RefundQueryResp:
        data = {"src_refund_no": src_refund_no}
        return self._request("wdt.refund.query", data, RefundQueryResp)

    def refund_query_by_logistics_no(self, logistics_no: str):
        # logistics_no为退回物流单号.
        data = {"logistics_no": logistics_no}
        return self._request("wdt.refund.query", data, RefundQueryResp)

    def refund_query_by_trade_no(self, trade_no: str):
        # trade_no为系统订单号.
        data = {"trade_no": trade_no}
        return self._request("wdt.refund.query", data, RefundQueryResp)

    def stockin_order_query_refund(self, src_order_no: str) -> StockinOrderQueryRefundResp:
        # src_order_no为系统退款单号
        data = {"src_order_no": src_order_no}
        return self._request("wdt.stockin.order.query.refund", data, StockinOrderQueryRefundResp)

    def stockin_order_query_refund_by_logistics_no(self, logistics_no: str):
        data = {"logistics_no": logistics_no}
        return self._request("wdt.stockin.order.query.refund", data, StockinOrderQueryRefundResp)

    def stockout_order_query_trade(self, src_order_no: str) -> StockoutOrderQueryTradeResp:
        # src_order_no为系统单号
        data = {"src_order_no": src_order_no}
        return self._request("wdt.stockout.order.query.trade", data, StockoutOrderQueryTradeResp)

    def suites_query(self, suite_no: str) -> SuiteQueryResp:
        data = {"suite_no": suite_no}
        return self._request("wdt.suites.query", data, SuiteQueryResp)

    def trade_logs(self, src_tid: str | None = None, trade_no: str | None = None):
        data: dict[str, Any] = {"page_size": 100, "page_no": 0}
        if trade_no:
            data["trade_nos"] = trade_no
        else:
            data["src_tids"] = src_tid
        return self._request("wdt.trade.logs.query", data, TradeLogsQueryResp)

    def stockin_pre_order_query(self, stockin_pre_no: str):
        data: dict[str, Any] = {"stockin_pre_no": stockin_pre_no}
        return self._request("wdt.stockin.pre.order.query", data, StockinPreOrderQueryResp)

    def _blocking_acquire(self, key: str, sid: str) -> bool:
        eta = time.time() + 10
        acquired = False
        while time.time() < eta:
            acquired = token_bucket_limiter.try_acquire_token_for_store(key, sid)
            if acquired:
                break
            time.sleep(0.3)
        return acquired
