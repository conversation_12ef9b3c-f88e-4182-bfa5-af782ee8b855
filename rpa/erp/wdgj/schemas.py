from typing import List

from pydantic import BaseModel
from pydantic.fields import Field

from robot_processor.utils import make_fields_optional


@make_fields_optional
class WdgjGoodsInOrder(BaseModel):
    PlatSkuID: str
    ShareTradeCost: str
    ShareTradeFavorable: str
    authorid: str
    authorname: str
    barcode: str
    bfit: str
    bgift: str
    goodscost: str
    goodscount: str
    goodsid: str
    goodsmoney: str
    goodsname: str
    goodsno: str
    goodsremark: str
    platgoodsid: str
    positionsname: str
    price: str
    recid: str
    relationno: str
    remark: str
    sharemoney: str
    sn: str
    specid: str
    specname: str
    unit: str


@make_fields_optional
class WdgjOrder(BaseModel):
    PrintPos: str
    adr: str
    alltotal: str
    appendremark: str
    bSplitOrMerge: str
    binvoice: str
    cancelreason: str
    chargeid: str
    chargetype: str
    chkoperator: str
    chktime: str
    city: str
    commissionvalue: str
    confirmoperator: str
    confirmtime: str
    country: str
    couponvalue: str
    currencyrate: str
    currencytype: str
    customerid: str
    customerremark: str
    favourabletotal: str
    flagname: str
    freezereason: str
    fxtradeno: str
    goodscost: str
    goodslist: List[WdgjGoodsInOrder]
    goodstotal: str
    goodsweight: str
    invoicepayeraddr: str
    invoicepayerbankno: str
    invoicepayerphone: str
    invoicepayerregno: str
    invoicetitle: str
    logisticid: str
    logisticlistno: str
    logisticname: str
    ordertime: str
    othercost: str
    packagedweight: str
    packagename: str
    packageoperator: str
    picker: str
    postage: str
    postagetotal: str
    postid: str
    predate: str
    pricedis: str
    pricespec: str
    providerid: str
    providername: str
    providerno: str
    province: str
    rcvtotal: str
    regoperator: str
    regtime: str
    remark: str
    reserved1: str
    reserved2: str
    reserved3: str
    reserved4: str
    seller: str
    shopid: str
    shopname: str
    shopno: str
    shoptype: str
    sndoperator: str
    sndtime: str
    sndto: str
    taxvalue: str
    tel: str
    totalprofit: str
    town: str
    tradefrom: str
    tradeid: str
    tradenick: str
    tradeno: str
    tradeno2: str
    tradestatus: str
    tradetime: str
    tradetype: str
    type: str
    warehouseid: str
    warehousename: str
    warehouseno: str
    zip: str


@make_fields_optional
class QueryTradesResp(BaseModel):
    class Response(BaseModel):
        datalist: List[WdgjOrder]
        returncode: str
        returninfo: str

    response: Response


class WdgjSpec(BaseModel):
    specid: str
    speccode: str
    specname: str


@make_fields_optional
class WdgjGoods(BaseModel):
    goodsid: str
    goodsno: str
    classname: str
    goodsname: str
    goodsname2: str
    pricedetail: str
    pricemember: str
    barcode: str
    bgift: str
    purchaser: str
    speclist: List[WdgjSpec]
    goodscount: str


@make_fields_optional
class QueryGoodsResp(BaseModel):
    class Response(BaseModel):
        datalist: List[WdgjGoods]
        returncode: str
        returninfo: str

    response: Response


@make_fields_optional
class ShopInfo(BaseModel):
    shopid: str = Field(description="店铺ID")
    shopname: str = Field(description="店铺名")
    shoptype: str = Field(description="店铺类型")
    shopno: str | None = Field(description="店铺编号")
    warehouseid: str | None = Field(description="仓库ID")
    warehousename: str | None = Field(description="仓库名")
    warehouseno: str | None = Field(description="仓库编码")
    bblockup: str = Field(description="是否停用 True/False")
    billstyle: str | None = Field(description="发货单模板")
    linkman: str = Field(description="联系人")
    chargetype: str | None = Field(description="结算方式")
    adr: str | None = Field(description="详细地址")
    country: str | None = Field(description="国家")
    province: str = Field(description="州省")
    city: str = Field(description="区市")
    town: str = Field(description="区县")
    zip: str | None = Field(description="邮编")
    tel: str = Field(description="电话")
    email: str | None = Field(description="邮箱")
    website: str | None = Field(description="网址")


@make_fields_optional
class QueryShopResponse(BaseModel):
    @make_fields_optional
    class Response(BaseModel):
        returncode: str = Field(description="0 成功 -1 失败 -2系统存在异常")
        returninfo: str = Field(description="成功返条数，失败返回错误信息")
        datalist: list[ShopInfo] = Field(description="店铺列表")

    response: Response

@make_fields_optional
class WdgjFitGoods(BaseModel):
    goodsid: str
    goodsno: str
    goodsname: str
    goodsname2: str
    pricedetail: str
    goodslist: List[WdgjGoods]


@make_fields_optional
class QueryFitGoodsResp(BaseModel):
    class Response(BaseModel):
        datalist: List[WdgjFitGoods]
        returncode: str
        returninfo: str

    response: Response
