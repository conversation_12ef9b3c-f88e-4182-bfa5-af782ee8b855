from datetime import datetime

from loguru import logger


def format_timestamp(timestamp: str | int | None, is_millis: bool = True) -> str:
    if not timestamp:
        return ""
    try:
        if is_millis:
            unix_timestamp = int(timestamp) // 1000
        else:
            unix_timestamp = int(timestamp)
        return datetime.fromtimestamp(
            unix_timestamp
        ).strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        logger.error("时间解析失败: {}", e)
        return ""
