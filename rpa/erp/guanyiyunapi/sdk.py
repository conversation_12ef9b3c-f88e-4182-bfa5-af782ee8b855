import hashlib
import json
import time
from typing import Dict
from typing import List
from typing import Type
from typing import TypeVar
from urllib.parse import quote_plus

from loguru import logger
from pydantic import BaseModel

from robot_processor.client_mixins import Session
from robot_processor.enums import ErpType
from robot_processor.error.client_request import GyyRequestError
from robot_processor.shop.models import ErpInfo
from robot_processor.shop.schema import GuanYiYunGrantMeta
from robot_processor.utils import ts2date
from rpa.conf import rpa_config as conf
from rpa.erp.guanyiyunapi.schemas import AftersaleUploadReq
from rpa.erp.guanyiyunapi.schemas import AftersaleUploadResponse
from rpa.erp.guanyiyunapi.schemas import GetShopsResponse
from rpa.erp.guanyiyunapi.schemas import GetWarehousesRequest
from rpa.erp.guanyiyunapi.schemas import GetWarehousesResponse
from rpa.erp.guanyiyunapi.schemas import GyyErpTradeDetailGetResponse
from rpa.erp.guanyiyunapi.schemas import GyyErpTradeGetResponse
from rpa.erp.guanyiyunapi.schemas import GyyErpWarehouseGetResponse
from rpa.erp.guanyiyunapi.schemas import GyyOrder
from rpa.erp.guanyiyunapi.schemas import GyyShop
from rpa.erp.guanyiyunapi.schemas import GyyWarehouse
from rpa.erp.guanyiyunapi.schemas import TradeReturnAddRequest
from rpa.erp.guanyiyunapi.schemas import TradeReturnAddResponse
from rpa.erp.guanyiyunapi.schemas import TradeReturnApproveResponse
from rpa.erp.guanyiyunapi.schemas import TradeReturnGetRequest
from rpa.erp.guanyiyunapi.schemas import TradeReturnGetResponse

RespT = TypeVar("RespT", bound=BaseModel)


def quote_data(data):
    if isinstance(data, dict):
        return {quote_data(k): quote_data(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [quote_data(i) for i in data]
    elif isinstance(data, str):
        return quote_plus(data)
    else:
        return data


class GyyOpenPlatformAPIClient:
    session = Session()

    def __init__(self, meta: str | dict):
        if isinstance(meta, str):
            self.meta: GuanYiYunGrantMeta = GuanYiYunGrantMeta.parse_raw(meta)
        else:
            self.meta = GuanYiYunGrantMeta.parse_obj(meta)
        self.session_key = self.meta.session_key
        self.app_key = self.meta.co_id
        self.app_secret = self.meta.app_secret

    @staticmethod
    def generate_signature(params: dict, secret: str) -> str:
        """
        管易云签名
        """
        params_json = json.dumps(params, ensure_ascii=False, separators=(",", ":"))
        sign_str = secret + params_json + secret
        hash_object = hashlib.md5()
        hash_object.update(sign_str.encode("utf-8"))
        return hash_object.hexdigest().upper()

    def send(self, method: str, data: dict) -> dict:
        param = {
            "appkey": self.app_key,
            "sessionkey": self.session_key,
            "method": method,
        }
        param.update(data)
        sign = self.generate_signature(param, self.app_secret)

        encoded_param = quote_data(param)
        encoded_param.update({"sign": sign})
        logger.info(f"req: {encoded_param}")
        headers = {"Content-Type": "application/json"}
        resp = self.session.post(
            conf.GYY_OPENAPI_ENDPOINT, json=encoded_param, headers=headers, timeout=conf.GYY_REQUEST_TIMEOUT
        )
        logger.info(f"resp: {resp.text}")
        resp_json = resp.json()
        return resp_json

    def get_shops(self) -> list[GyyShop]:
        method = "gy.erp.shop.get"
        page_no = 1
        page_size = 100
        shops: list[GyyShop] = []
        resp = self.send(method, {"page": page_no, "size": page_size})
        parsed_resp: GetShopsResponse = GetShopsResponse.parse_obj(resp)
        if not parsed_resp.success:
            return []
        shops.extend(parsed_resp.shops)
        return shops

    def get_shop_by_code(self, shop_code: str) -> list[GyyShop]:
        method = "gy.erp.shop.get"
        page_no = 1
        page_size = 100
        shops: list[GyyShop] = []
        resp = self.send(method, {"page": page_no, "size": page_size,
                                  "code": shop_code})
        parsed_resp: GetShopsResponse = GetShopsResponse.parse_obj(resp)
        if not parsed_resp.success:
            return []
        shops.extend(parsed_resp.shops)
        return shops

    def check_grant_is_valid(self) -> bool:
        try:
            resp = self.send("gy.erp.trade.get", {"platform_code": "1"})
            return bool(resp.get("success"))
        except Exception as e:
            logger.error("管易云授权不可用 {}", e)
            return False


class GyyQmSDK:
    session = Session()

    def __init__(self, sid: str | None = None, erp_info: ErpInfo | None = None):
        if erp_info is None and sid is not None:
            erp_info = ErpInfo.get_by_sid(sid, ErpType.GUANYIYUN)
        assert erp_info, f"店铺 {sid} 无管易云授权信息"
        self.erp_info = erp_info

    @property
    def session_key(self):
        return self.erp_info.meta["session_key"]

    @staticmethod
    def sorted_params(params):
        sorted_params = {}
        for key in sorted(params):
            sorted_params[key] = params[key]
        return sorted_params

    def _sign(self, params):
        sign_str = conf.GYY_APP_SECRET + "".join([f"{k}{v}" for k, v in params.items()]) + conf.GYY_APP_SECRET
        return hashlib.md5(sign_str.encode("utf-8")).hexdigest().upper()

    def _request(self, method, data: dict, resp_class: Type[RespT]) -> RespT:
        """
        Raises:
            GyyRequestError
        """

        params = {
            "method": method,
            "app_key": conf.GYY_APP_KEY,
            "timestamp": ts2date(time.time()),
            "target_app_key": conf.GYY_TARGET_APP_KEY,
            "format": "json",
            "sessionkey": self.session_key,
            "v": 2.0,
            "sign_method": "md5",
        }
        params.update(data)
        params = GyyQmSDK.sorted_params(params)
        sign_str = self._sign(params)
        params["sign"] = sign_str
        logger.info(f"method: {method}, sign: {sign_str}, data: {data}")
        resp = self.session.post(conf.GYY_QM_ENDPOINT, params=params, timeout=conf.GYY_REQUEST_TIMEOUT)
        logger.info(f"resp: {resp.text}")
        resp_json = resp.json()
        if resp_json["response"]["success"] != "true":
            raise GyyRequestError(req=(method, params, data), res=resp_json, message=resp_json["response"]["errorDesc"])
        return resp_class.parse_obj(resp_json)

    def query_trade(self, platform_code: str) -> GyyErpTradeGetResponse:
        return self._request(
            method="gy.erp.trade.get", data={"platform_code": platform_code}, resp_class=GyyErpTradeGetResponse
        )

    def query_history_trade(self, platform_code: str) -> GyyErpTradeGetResponse:
        return self._request(
            method="gy.erp.trade.history.get", data={"platform_code": platform_code}, resp_class=GyyErpTradeGetResponse
        )

    def query_history_trade_detail(self, code: str) -> GyyErpTradeDetailGetResponse:
        return self._request(
            method="gy.erp.trade.history.detail.get", data={"code": code}, resp_class=GyyErpTradeDetailGetResponse
        )

    def query_trade_detail(self, code: str) -> GyyErpTradeDetailGetResponse:
        return self._request(
            method="gy.erp.trade.detail.get", data={"code": code}, resp_class=GyyErpTradeDetailGetResponse
        )

    def query_trade_by_tid(self, tid: str) -> List[GyyOrder]:
        resp = self.query_trade(tid)
        history_resp = self.query_history_trade(tid)
        orders: List[GyyOrder] = []
        if resp.response.orders:
            for order in resp.response.orders:
                detail_resp = self.query_trade_detail(order.code)
                orders.append(detail_resp.response.orderDetail)
        if history_resp.response.orders:
            for order in history_resp.response.orders:
                detail_resp = self.query_history_trade_detail(order.code)
                orders.append(detail_resp.response.orderDetail)
        return orders

    def query_trade_by_code(self, code: str) -> GyyOrder | None:
        resp = self.query_trade_detail(code)
        if resp.response.orderDetail:
            return resp.response.orderDetail
        history_resp = self.query_history_trade_detail(code)
        if history_resp.response.orderDetail:
            return history_resp.response.orderDetail
        return None


class GyySDK:
    session = Session()

    def __init__(self, sid: str):
        """
        Args:
            sid (str): shop.sid
        Raises:
            AssertionError
        """
        erp_info = ErpInfo.get_by_sid(sid, ErpType.GUANYIYUN)
        assert erp_info, f"店铺 {sid} 无管易云授权信息"
        self.erp_info = erp_info

    @property
    def session_key(self):
        return self.erp_info.meta["session_key"]

    @property
    def app_secret(self):
        return self.erp_info.meta["app_secret"]

    @property
    def app_key(self):
        return self.erp_info.meta["co_id"]

    def _sign(self, params: Dict, secret: str):
        """
        管易云签名
        """
        params_json = json.dumps(params, ensure_ascii=False, separators=(",", ":"))
        sign_str = secret + params_json + secret
        hash_object = hashlib.md5()
        hash_object.update(sign_str.encode("utf-8"))
        return hash_object.hexdigest().upper()

    def _request(self, method: str, data: dict, resp_class: Type[RespT]) -> RespT:
        """
        Raises:
            GyyRequestError
        """
        param = {
            "appkey": self.app_key,
            "sessionkey": self.session_key,
            "method": method,
        }
        param.update(data)
        sign = self._sign(param, self.app_secret)

        encoded_param = quote_data(param)
        encoded_param.update({"sign": sign})
        logger.info(f"req: {encoded_param}")
        headers = {"Content-Type": "application/json"}
        resp = self.session.post(
            conf.GYY_OPENAPI_ENDPOINT, json=encoded_param, headers=headers, timeout=conf.GYY_REQUEST_TIMEOUT
        )
        logger.info(f"resp: {resp.text}")
        resp_json = resp.json()
        if not resp_json["success"]:
            raise GyyRequestError(req=(method, encoded_param, data), res=resp_json, message=resp_json["errorDesc"])
        return resp_class.parse_obj(resp_json)

    def all_warehouses(self):
        return self._request(
            method="gy.erp.warehouse.get", data={"page_size": 100}, resp_class=GyyErpWarehouseGetResponse
        )

    def get_warehouse_by_name(self, name: str) -> str | None:
        resp = self.all_warehouses()
        warehouses = [warehouse for warehouse in resp.response.warehouses if warehouse.name == name]
        if warehouses:
            return warehouses[0]
        return None

    def aftersale_upload(self, req: AftersaleUploadReq) -> AftersaleUploadResponse:
        data = req.dict(exclude_none=True)
        return self._request(method="gy.erp.trade.add", data=data, resp_class=AftersaleUploadResponse)

    def trade_return_add(self, request: TradeReturnAddRequest) -> TradeReturnAddResponse:
        """退货单新增"""
        data = request.dict(exclude_none=True)
        return self._request("gy.erp.trade.return.add", data=data, resp_class=TradeReturnAddResponse)

    def trade_return_get(self, request: TradeReturnGetRequest) -> TradeReturnGetResponse:
        """退货单查询"""
        data = request.dict(exclude_none=True)
        return self._request("gy.erp.trade.return.get", data=data, resp_class=TradeReturnGetResponse)

    def trade_return_approve(self, code: int) -> TradeReturnApproveResponse:
        return self._request(
            "gy.erp.trade.return.order.dobatchagree", data={"ids": [code]}, resp_class=TradeReturnApproveResponse
        )

    def get_warehouses(self, request: GetWarehousesRequest) -> GetWarehousesResponse:
        data = request.dict(exclude_none=True)
        return self._request("gy.erp.warehouse.get", data=data, resp_class=GetWarehousesResponse)

    def get_all_warehouses(self) -> list[GyyWarehouse]:
        page_no = 1
        page_size = 100
        warehouses: list[GyyWarehouse] = []
        while True:
            try:
                req = GetWarehousesRequest(
                    page_no=page_no,
                    page_size=page_size,
                )
                resp = self.get_warehouses(req)
                if not resp.success:
                    break
                if not resp.total:
                    break
                if not resp.warehouses:
                    break
                warehouses.extend(resp.warehouses)
                if page_no * page_size >= resp.total:
                    break
                page_no += 1
            except Exception as e:
                logger.error("查询仓库失败", e)
                break

        return warehouses
