from enum import StrEnum

from result import Ok, Err, Result

from robot_processor.client_mixins import Session
from rpa.conf import rpa_config
from rpa.mola import MolaClient


class TPType(StrEnum):
    ALL_TRADE = "全部"
    NORMAL_TRADE = "普通订单"
    OFFLINE_TRADE = "线下订单"
    AFTER_SALES_TRADE = "售后订单"


class WanliniuClient:
    session = Session()
    namespace = 'wanliniu-erp'

    def __init__(self, sid: str):
        self.mola = MolaClient(sid)

    @staticmethod
    def convert_list(value):
        if not value:
            return ''
        if isinstance(value, list) and len(value) > 0:
            return value[0]
        if isinstance(value, list):
            return ''
        else:
            return value

    @staticmethod
    def _make_upload_request(shop_name: str, buyer_name: str,
                             address: dict, sku_list: list, tid="",
                             remark="", warehouse="", logistics_corp="",
                             sales_man="") -> dict:
        # reference https://leyan.yuque.com/digismart/fed/sfcycg#F1pJA
        data = {
            "shopName": WanliniuClient.convert_list(shop_name),
            "showBuyer": buyer_name,
            "tid": WanliniuClient.convert_list(tid),
            "receiverAddress": address,
            "skuList": [
                {
                    "skuId": sku["outer_sku_id"],
                    "itemNum": str(sku["qty"])
                } for sku in sku_list
            ],
            "remark": remark,
            "storeHouseName": WanliniuClient.convert_list(warehouse),
            "courierCompany": WanliniuClient.convert_list(logistics_corp),
            "salesman": WanliniuClient.convert_list(sales_man),
        }
        return data

    def after_sale_upload(self, shop_name, buyer_name: str,
                          address: dict, sku_list: list, tid="",
                          remark="", warehouse="", logistics_corp="",
                          sales_man="") -> Result[str, str]:
        data = self._make_upload_request(shop_name, buyer_name, address,
                                         sku_list, tid=tid, remark=remark,
                                         warehouse=warehouse,
                                         logistics_corp=logistics_corp,
                                         sales_man=sales_man)
        mola_res = self.mola.call_namespace_method(self.namespace, "create-trade", data)
        match mola_res:
            case Err():
                return mola_res
            case Ok(body):
                entity_states = body.get('result', {}).get("entityStates", [])
                for key in entity_states:
                    entity_state = entity_states[key]
                    if "salercpt_no" in entity_state:
                        return Ok(entity_state["salercpt_no"])
                return Err("未返回万里牛单号")

    def after_sale_logistics(self, salercpt_no: str) -> Result[dict, str]:
        mola_res = self.mola.call_namespace_method(self.namespace, "create-tracking-number",
                                                   {"salercptNo": salercpt_no})
        match mola_res:
            case Err():
                return mola_res
            case Ok(body):
                entity_states = body.get('result', {}).get("entityStates", [])
                for key in entity_states:
                    entity_state = entity_states[key]
                    if "expressNo" in entity_state:
                        return Ok({
                            'logistics_corp': entity_state.get('expressName'),
                            'logistics_order': entity_state.get('expressNo')})
                return Err("快递单创建失败：单号未返回")

    def trade_query(self, tid: str, tp_type: str) -> Result[tuple[dict, dict], str]:
        mola_res = self.mola.call_namespace_method(self.namespace, "get-orders-list", {"tp_tid": tid})
        match mola_res:
            case Err():
                return mola_res
            case Ok(body):
                trade_data = body.get('result', {}).get('data', {})
                if not trade_data or 'data' not in trade_data:
                    return Err("没有找到符合订单类型或状态的订单")
                after_sales_trade_type = rpa_config.WANLINIU_AFTER_SALES_TRADE_TYPE or ['51']
                offline_trade_type = rpa_config.WANLINIU_OFFLINE_TRADE_TYPE or ['50']
                trade_type = rpa_config.WANLINIU_TRADE_TYPE or (after_sales_trade_type + offline_trade_type)
                match tp_type:
                    case TPType.NORMAL_TRADE:
                        trades = [
                            trade for trade in trade_data.get('data')
                            if str(trade.get("tp_type")) not in trade_type
                        ]
                    case TPType.AFTER_SALES_TRADE:
                        trades = [
                            trade for trade in trade_data.get('data')
                            if str(trade.get("tp_type")) in after_sales_trade_type
                        ]
                    case TPType.OFFLINE_TRADE:
                        trades = [
                            trade for trade in trade_data.get('data')
                            if str(trade.get("tp_type")) in offline_trade_type
                        ]
                    case TPType.ALL_TRADE:
                        trades = [trade for trade in trade_data.get('data')]
                    case _:
                        return Err("当前订单类型不支持")
                if not trades:
                    return Err("没有找到符合订单类型或状态的订单")

                trade_info = max(trades, key=lambda t: t.get("trade_modified_time") or t.get("trade_create_time"))
                return Ok((
                    {
                        "pay_amount": f"{trade_info.get('realPayment')}",
                        "logistics_name": trade_info.get("delivery_name"),
                        "logistics_no": trade_info.get("express_uid"),
                        "warehouse_no": trade_info.get("storage_name"),
                    },
                    {
                        "tradeUid": trade_info.get("salercpt_uid"),
                        "storageUid": trade_info.get("storage_uid"),
                        "shopUid": trade_info.get("sys_shop"),
                        "salercpt_no": trade_info.get("salercpt_no"),
                    }
                ))

    def sku_of_trade_query(self, query_param: dict) -> Result[dict, str]:
        mola_res = self.mola.call_namespace_method(self.namespace, "get-order-sku-list", data=query_param)
        match mola_res:
            case Err():
                return mola_res
            case Ok(body):
                skus = body.get('result', {}).get('data', [])
                goods_title_list = [sku.get('goods_name') for sku in skus if
                                    (sku.get('tp_tid') or sku.get('tp_tid_snapshot')) and sku.get('goods_name')]
                if not goods_title_list:
                    return Err("商品未查到")
                else:
                    goods_title = ";".join(list(set(goods_title_list)))
                    return Ok({'goods_title': goods_title})

    def trade_with_sku_query(self, tid: str, tp_type="") -> Result[dict, str]:
        tp_type = tp_type or rpa_config.WANLINIU_TRADE_DEFAULT_TYPE
        match trade_query_res := self.trade_query(tid, tp_type):
            case Err():
                return trade_query_res
            case Ok((trade_info, sku_query_param)):
                match sku_res := self.sku_of_trade_query(sku_query_param):
                    case Err():
                        return sku_res
                    case Ok(sku_info):
                        trade_info.update(sku_info)
                        return Ok(trade_info)
        return Err("未知异常")
