import base64
import hashlib
from json import dumps as json_dumps

from loguru import logger
from result import Err, Ok
from requests import HTTPError, Request, Response
import arrow
from lepollo import ApolloConfig, get_config
from requests_opentracing import SessionTracing

from robot_processor.utils import request_to_log, response_to_log
from rpa.yto.schema import InterceptResponse, TrackQueryResponse, CredentialProtocol

__all__ = ["YtoSdk"]


class YtoSdk:
    """圆通开放平台 SDK

    SDK 仅提供对圆通开放平台的接口封装，不处理业务逻辑
    """

    def __init__(self, credential: CredentialProtocol, config=None):
        """Initialize SDK with partner id.

        Args:
            credential (CredentialProtocol): 客户授权信息
            config (ApolloConfig | None): 圆通开放平台的配置信息
        """
        self.credential = credential
        self.config = config or yto_config
        self.session = SessionTracing()

    def intercept_report(self, waybill_no, wanted_desc):
        """拦截件退回上报

        通过上报接口可以上报派件前的所有在途运单，支持“拦截退回”类型的拦截服务。

        Args:
            waybill_no (str): 运单号
            wanted_desc (str): 问题描述
        Returns:
            Ok[InterceptResponse] | Err[SDKError]
        References:
            https://open.yto.net.cn/interfaceDocument/menu295/submenu309
        """
        method, version = "wanted_report_adapter", "v1"
        if self.credential.partner_key == "K75774379":
            api_path = "/wanted_report_adapter/v1/AYL0Kt/K75774379"
        else:
            api_path = "/wanted_report_adapter/v1/tuuzc5/" + self.credential.partner_key
        param = dict(waybillNo=waybill_no, wantedDesc=wanted_desc)
        return self._do_signed_request(api_path, param, method, version).map(
            lambda raw: InterceptResponse(**raw)
        )

    def intercept_cancel(self, waybill_no, deal_remark):
        """拦截件取消

        通过取消接口具体可以取消商家自己通过开放平台拦截件上报接口下发生成的拦截件
        拦截件状态是“有效”状态的可以取消成功，已经完结、失效、取消的均不允许取消

        Args:
            waybill_no (str): 运单号码
            deal_remark (str): 取消描述
        Returns:
            Ok[InterceptResponse] | Err[SDKError]: 成功时返回拦截件取消结果，失败时返回错误信息
        References:
            https://open.yto.net.cn/interfaceDocument/menu295/submenu310
        """
        method, version = "wanted_cancel_adapter", "v1"
        if self.credential.partner_key == "K75774379":
            api_path = "/wanted_cancel_adapter/v1/AYL0Kt/K75774379"
        else:
            api_path = "/wanted_cancel_adapter/v1/tuuzc5/" + self.credential.partner_key
        param = dict(waybillNo=waybill_no, dealRemark=deal_remark)
        return self._do_signed_request(api_path, param, method, version).map(
            lambda raw: InterceptResponse(**raw)
        )

    def track_query(self, number):
        """物流轨迹查询

        Args:
            number (str): 圆通快递单号

        Returns:
            Ok[TrackQueryResponse.Success | TrackQueryResponse.Failed] | Err[SDKError]: 成功时返回轨迹信息，失败时返回错误信息

        References:
            https://open.yto.net.cn/interfaceDocument/menu251/submenu258
        """
        method, version = "track_query_adapter", "v1"
        if self.credential.partner_key == "K75774379":
            api_path = "/track_query_adapter/v1/AYL0Kt/K75774379"
        else:
            api_path = "/track_query_adapter/v1/tuuzc5/" + self.credential.partner_key
        param = dict(number=number)
        return (
            self._do_signed_request(api_path, param, method, version)
            .map(TrackQueryResponse.validate)
            .map(lambda resp: resp.__root__)
        )

    def _do_signed_request(self, api_path, param, method, version):
        """对请求的业务参数进行签名，并发送请求。因每个接口的返回数据结构不同，业务异常由调用方处理。

        Args:
            api_path (str): 接口路径
            param (dict): 业务参数
            method (str): 由接口提供（控制台-接口管理）
            version (str): 由接口提供（控制台-接口管理）

        Returns:
            Ok[dict] | Err[SDKServerError]: HTTP 请求成功时，返回响应体；失败时返回 SdKServerError

        References:
            [签名规则](https://open.yto.net.cn/interfaceDocument/menu295/submenu308)
        """
        partner_id = self.credential.partner_id
        param_str = json_dumps(param, ensure_ascii=False, sort_keys=True)
        sign_str = base64.b64encode(
            hashlib.md5(f"{param_str}{method}{version}{partner_id}".encode("utf-8")).digest()
        ).decode("utf-8")
        request = Request(
            method="POST",
            url=self.config.app_endpoint + api_path,
            json=dict(
                param=param_str,
                sign=sign_str,
                timestamp=int(arrow.now().float_timestamp * 1000),
                format="JSON",
            ),
        )

        # 处理 HTTP 异常
        try:
            response: Response = self.session.send(
                request.prepare(),
                timeout=self.config.request_timeout,
            )
        except HTTPError as e:
            error = SDKServerError.wrap_requests_error(e, request, None)
            logger.opt(exception=e).warning(error.to_log())
            return Err(error)
        if not response.ok:
            error = SDKServerError(request, response)
            logger.opt(exception=error).warning(error.to_log())
            return Err(error)

        logger.info(
            "圆通开放平台请求: request: {}, response: {}".format(
                request_to_log(request), response_to_log(response)
            )
        )
        return Ok(response.json())


class YTOConfig(ApolloConfig):
    __namespace__ = "client"

    @property
    def app_endpoint(self):
        return self.get_str(
            "yto.app_endpoint",
            "https://openapi.yto.net.cn:11443/open"
        )

    @property
    def request_timeout(self):
        return self.get_int("yto.request_timeout", 3)


yto_config = get_config(config_class=YTOConfig)


class SDKError(Exception):
    def to_log(self):
        """在日志中记录的信息"""
        raise NotImplementedError

    def to_user_tips(self):
        """给用户看的错误提示信息"""
        raise NotImplementedError


class SDKServerError(SDKError):
    def __init__(self, request, response, *args):
        """SDK 服务端异常

        Args:
            request (Request): 请求参数
            response (Response | None): 响应参数
        """
        super().__init__(*args)
        self.request = request
        self.response = response

    @classmethod
    def wrap_requests_error(cls, error, request, response):
        """处理 requests 库抛出的异常

        Args:
            error (HTTPError): requests 库抛出的异常
            request (Request): requests 库的请求对象
            response (Response | None): requests 库的响应对象
        """
        try:
            raise cls(request, response, *error.args) from error
        except SDKServerError as error:
            return error

    def to_log(self):
        from robot_processor.utils import request_to_log, response_to_log

        tmpl = "圆通开放平台请求失败\n\t请求参数: {request}\n\t返回信息: {response}"
        request = request_to_log(self.request)
        response = response_to_log(self.response) if self.response is not None else "<无响应>"

        return tmpl.format(request=request, response=response)

    def to_user_tips(self):
        return "圆通开放平台异常，请联系飞梭对接群服务人员或店铺实施工程师"


class SDKBizError(SDKError):
    def __init__(self, message, request_data, response_data):
        """

        Args:
            message (str): 错误信息
            request_data (dict): 接口请求的业务参数
            response_data (dict): 接口响应数据
        """
        super().__init__(message)
        self.message = message
        self.request_data = request_data
        self.response_data = response_data

    def to_log(self):
        tmpl = "圆通开放平台业务失败\n\t请求参数: {request}\n\t返回信息: {response}"
        request = json_dumps(self.request_data, ensure_ascii=True)
        response = json_dumps(self.response_data, ensure_ascii=False)

        return tmpl.format(request=request, response=response)

    def to_user_tips(self):
        return self.message
