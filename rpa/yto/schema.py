from enum import StrEnum, IntEnum
from typing import Protocol
from pydantic import BaseModel


class YTOResponse(BaseModel):
    class Config:
        extra = "allow"


class InterceptResponse(YTOResponse):
    """拦截接口返回数据"""

    class StatusCode(IntEnum):
        SUCCEED = 0
        BIZ_ERROR = 1
        SERVER_ERROR = -1

    statusCode: int | None
    statusMessage: str | None


class TrackingInfo(YTOResponse):
    """物流轨迹信息"""

    class InfoContent(StrEnum):
        GOT = "GOT", "已揽收"
        ARRIVAL = "ARRIVAL", "已收入"
        DEPARTURE = "DEPARTURE", "已发出"
        SENT_SCAN = "SENT_SCAN", "派件"
        INBOUND = "INBOUND", "自提柜入柜"
        SIGNED = "SIGNED", "签收成功"
        FAILED = "FAILED", "签收失败"
        FORWARDING = "FORWARDING", "转寄"
        TMS_RETURN = "TMS_RETURN", "退回"
        AIRSEND = "AIRSEND", "航空发货"
        AIRPICK = "AIRPICK", "航空提货"

        def __new__(cls, value, *args):
            self = str.__new__(cls, value)
            self._value_ = value
            return self

        def __init__(self, _, label):
            self.label = label

        def is_intercepted(self):
            """是否被拦截"""
            return self is TrackingInfo.InfoContent.TMS_RETURN

        def is_finished(self):
            """物流是否完结"""
            return self in [
                TrackingInfo.InfoContent.INBOUND,
                TrackingInfo.InfoContent.SIGNED,
            ]

    waybill_No: str  # 运单号
    upload_Time: str  # 走件产生时间 yyyy-MM-dd HH:mm:ss
    infoContent: InfoContent  # 物流状态
    processInfo: str  # 物流信息 e.g.: 您的快件被【浙江省金华市义乌市上溪镇】揽收，揽收人: xxx (xxxxxxxxx)
    city: str | None  # 当前操作城市
    district: str | None  # 当前操作区或者县
    weight: str | None  # 重量，单位：kg

    def is_signed(self):
        return self.infoContent == self.InfoContent.SIGNED

    def is_returned(self):
        return self.infoContent == self.InfoContent.TMS_RETURN


class TrackQueryResponse(YTOResponse):
    class Success(YTOResponse):
        __root__: list[TrackingInfo]

    class Failed(YTOResponse):
        code: str
        message: str

    __root__: Success | Failed


class CredentialProtocol(Protocol):
    partner_key: str  # 客户编码
    partner_id: str  # 客户密钥


class InterceptStatusPushMessage(YTOResponse):
    """圆通推送的拦截件结果状态"""

    class ResultCode(StrEnum):
        CANCELED = "1", "拦截件已取消"
        TIMEOUT_FAILED = "2", "超时拦截失败"
        RETURNED = "3", "已做退回/更址扫描操作"
        SIGNED = "4", "快件已签收"
        UNAUTHORIZED_SITE_SIGN = "5", "非指定网点签收，实物拦截失败"

        def __new__(cls, value, *args):
            self = str.__new__(cls, value)
            self._value_ = value
            return self

        def __init__(self, _, label):
            self.label = label

    class Data(YTOResponse):
        waybillNo: str
        result: str
        # 结果编码：1.拦截件已取消；2.超时拦截失败；3.已做退回/更址扫描操作；4.快件已签收；5.非指定网点签收，实物拦截失败。
        resultCode: str

    # 状态码
    data: Data
    # 客户渠道
    channel: str
