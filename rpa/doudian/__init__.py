"""doudian相关的"""
from .client import DoudianRpaClient, DoudianOpenAPIClient, OpenAPIConfig
from rpa.conf import rpa_config


# 原来的 doudian_client, 使用 rpa 实现
doudian_rpa_client = DoudianRpaClient()
# 保留一个引用
doudian_client = doudian_rpa_client
# 使用抖店开放平台的 client
doudian_openapi_pigeon_client = DoudianOpenAPIClient(OpenAPIConfig(
    app_key=rpa_config.DOUDIAN_OPEN_API_APP_KEY,
    app_secret=rpa_config.DOUDIAN_OPEN_API_APP_SECRET,
))
doudian_openapi_doudian_xyz_client = DoudianOpenAPIClient(OpenAPIConfig(
    app_key=rpa_config.DOUDIAN_XYZ_APP_KEY,
    app_secret=rpa_config.DOUDIAN_XYZ_APP_SECRET,
))
