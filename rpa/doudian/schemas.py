from typing import Optional, Literal
from json import loads as json_loads

from pydantic import BaseModel, Field, validator


class Location(BaseModel):
    name: Optional[str]
    id: Optional[str]
    has_child: Optional[bool]


class PostAddr(BaseModel):
    province: Location
    city: Location
    town: Location
    street: Location
    detail: str


class ReceiveInfo(BaseModel):
    post_receiver: Optional[str]
    post_tel: Optional[str]
    post_addr: PostAddr
    can_view: Optional[int]
    post_tel_type: Optional[int]
    expire_time: Optional[int]
    is_show_edit_address: Optional[bool]
    can_postpone: Optional[bool]
    extension_number: Optional[str]
    post_tel_mask: Optional[str]
    address_tag: Optional[str]
    user_account_infos: Optional[dict]
    ui_type: Optional[str]
    buyer_tel_info: Optional[dict]

    def to_standard_output(self):
        # 前端处理的时候必须有town，不一定要有district
        return {
            'name': self.post_receiver,
            'mobile': self.post_tel,
            'state': self.post_addr.province.name,
            'city': self.post_addr.city.name,
            'zone': self.post_addr.town.name,
            'district': self.post_addr.town.name,
            'town': self.post_addr.street.name,
            'address':  self.post_addr.detail,
        }


class ReceiveDetail(BaseModel):
    verify_type: Optional[str]
    verify_params: Optional[dict]
    is_send: Optional[int]
    receive_info: Optional[ReceiveInfo]
    pre_receive_info: Optional[dict]
    nick_name: Optional[str]

    # 飞梭需要的额外字段
    is_success: Optional[bool]
    error_msg: str = ""


class PigeonTokenExchangeParam(BaseModel):
    token: str = Field(description="飞鸽提交的用户 token")


class LoginParam(BaseModel):
    customer_service_name: str
    shop_id: str


class PigeonTokenExchangeRes(BaseModel):
    login_param: LoginParam

    @validator("login_param", pre=True)
    def parse_login_param(cls, v):
        if isinstance(v, str):
            return json_loads(v)
        return v


class TokenCreateParam(BaseModel):
    code: str = Field(description="飞鸽提交的用户 token")
    grant_type: Literal["authorization_code"] = "authorization_code"


class TokenCreateRes(BaseModel):
    access_token: str = Field(description="用于调用API的access_token 过期时间为expires_in值 "
                                          "可通过refresh_token刷新获取新的access_token "
                                          "过期时间仍为expires_in值")
    expires_in: int = Field(description="access_token接口调用凭证超时时间，单位（秒）")
    shop_id: int = Field(description="店铺ID")
    shop_name: str = Field(description="店铺名称")
    refresh_token: str = Field(description="用户刷新access_token")


class TokenRefreshParam(BaseModel):
    refresh_token: str
    grant_type: Literal["refresh_token"] = "refresh_token"


class TokenRefreshRes(BaseModel):
    access_token: str
    expires_in: int
    refresh_token: str
    scope: Optional[str] = None
    shop_id: int
    shop_name: str
    authority_id: Optional[str] = None
    auth_subject_type: Optional[str] = None
