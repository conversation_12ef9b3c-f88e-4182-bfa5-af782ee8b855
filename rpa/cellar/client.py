from loguru import logger
from result import Result, Ok, Err

from robot_processor.client_mixins import Session
from robot_processor.utils import response_to_log
from rpa.conf import cellar_config


class CellarClient:
    session = Session()

    def _request(self, url, data) -> Result[None, str]:
        logger.info(f"cellar request url={url}, data={data}")
        try:
            resp = self.session.post(url, json=data,
                                     headers={
                                         "Authorization": "Bearer " + cellar_config.CELLAR_CLIENT_TOKEN
                                     },
                                     timeout=cellar_config.CELLAR_CLIENT_REQUEST_TIMEOUT)
        except Exception as e:
            return Err(str(e))
        if not resp.ok:
            # {"statusCode":400,"message":"sid can not be blank"}
            # {"statusCode":200}
            resp_json = resp.json()
            logger.info(f"cellar response code={resp.status_code}, data={resp_json}")
            return Err(response_to_log(resp))
        else:
            return Ok(None)

    def send_message(self, platform: str, tid: str, sid: str, assistant_fetch_strategy: int,
                     assistant: str, text: str, pic_url: str, extra_data=None) -> Result[None, str]:
        """
        发消息

        :param assistant_fetch_strategy: 发送账号: 1: 随机在线客服 2: 指定账号 3: 最近接待人
        :param pic_url: 图片地址
        """
        url = cellar_config.CELLAR_CLIENT_ENDPOINT + "/send_msg/{}/tid/{}/message".format(platform, tid)
        data = {
            "sid": sid,
            "assistant_fetch_strategy": assistant_fetch_strategy,
            "assistant": assistant,
            "text": text,
            "pic_url": pic_url,
        }
        if extra_data:
            data.update(extra_data)
        return self._request(url, data)
