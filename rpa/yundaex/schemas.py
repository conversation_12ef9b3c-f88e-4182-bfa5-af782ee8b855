import arrow
from pydantic import BaseModel, Field

from robot_processor.utils import make_fields_optional


class BaseResponse(BaseModel):
    success: bool
    reasonCode: str
    msg: str


class InterceptInvokeExpressInte(BaseModel):
    """退回件-发起拦截"""

    # 运单号
    shipId: str
    # 始发网点ID（发件网点）
    startSiteCode: str | None = None
    # 拦截类型(1.客户退回)
    inteType: str = "1"
    # 发件人地址
    senderAddress: str | None = None
    # 拦截发起人姓名
    interceptSponsorName: str
    # 拦截发起时间
    interceptCreateTime: str = Field(default_factory=lambda: arrow.now().format("YYYY-MM-DD HH:mm:ss"))
    # 备注
    remark: str | None = None

    class Response(BaseResponse):
        @make_fields_optional
        class Data(BaseModel):
            shipId: str
            canIntercept: int
            unblockableReason: int | None

            can_intercept: bool
            unblockable_reason: str | None

            def __init__(self, **data):
                super().__init__(**data)
                self.can_intercept = {1: True, 2: False}[self.canIntercept]
                self.unblockable_reason = {
                    1: "运单号不规范不准确",
                    2: "运单录单信息错误",
                    3: "始发站点错误",
                    4: "已派件",
                    5: "消费者已签收",
                    6: "其他(已存在当前渠道录入拦截件)",
                    7: "已存在非当前渠道录入拦截件",
                }.get(self.unblockableReason, self.unblockableReason)  # type: ignore[arg-type]

        data: Data


class NotifyExpressInterceptionRealStatus(BaseModel):
    """退回件-拦截结果查询"""

    # 运单号
    shipId: str

    class Response(BaseResponse):
        @make_fields_optional
        class Data(BaseModel):
            realInterceptStatus: int
            real_intercept_status: str

            def __init__(self, **data):
                super().__init__(**data)
                self.real_intercept_status = {
                    2: "拦截中",
                    3: "拦截失败",
                    4: "拦截成功",
                    5: "拦截终止",
                    6: "已完成拦截",
                    7: "拦截异常"
                }.get(self.realInterceptStatus, self.realInterceptStatus)  # type: ignore[arg-type]

        data: Data


class CancelIntercept(BaseModel):
    """退回件 - 取消拦截"""

    # 运单号
    shipId: str

    class Response(BaseResponse):
        pass
