from typing import TYPE_CHECKING

from loguru import logger
from requests.auth import AuthBase
from requests_opentracing import SessionTracing
from result import Ok, Err

from robot_processor.error.client_request import YundaexError
from robot_processor.utils import response_to_log, request_to_log
from rpa.yundaex.conf import yundaex_config
from rpa.yundaex.schemas import InterceptInvokeExpressInte, NotifyExpressInterceptionRealStatus, CancelIntercept

if TYPE_CHECKING:
    from robot_processor.shop.models import Shop


class YundaexClient:
    """韵达快递开放平台

    References:
        http://open.yundaex.com/api/apiDoc
    """

    def __init__(self, shop=None, auth_account=None):
        self.session = SessionTracing()
        self.auth: YundaexAuth | None = None
        if shop:
            self.auth = YundaexAuth.get_by_shop(shop, auth_account)

    def send_request(self, path: str, data: dict):
        url = yundaex_config.app_endpoint + path
        response = self.session.post(url, json=data, auth=self.auth)
        logger.info("韵达开放平台请求 request: {}, response: {}".format(
            request_to_log(response.request),
            response_to_log(response)
        ))
        if response.status_code != 200:
            return Err(YundaexError(request_to_log(response.request), response_to_log(response)))
        try:
            response_data = response.json()
            if response_data["success"]:
                return Ok(response_data)
            else:
                error = YundaexError(request_to_log(response.request), response_to_log(response), response_data["msg"])
                return Err(error)
        except Exception as e:
            return Err(e)

    def intercept_invokeExpressInte(
        self, request: InterceptInvokeExpressInte
    ) -> Ok[InterceptInvokeExpressInte.Response] | Err[YundaexError | Exception]:
        """退回件-指令下发"""
        path = "/tmm/outerapi/intercept/invokeExpressInte"
        data = request.dict(exclude_none=True)
        return self.send_request(path, data).map(InterceptInvokeExpressInte.Response.parse_obj)

    def intercept_notifyExpressInterceptionRealStatus(
        self, request: NotifyExpressInterceptionRealStatus
    ) -> Ok[NotifyExpressInterceptionRealStatus.Response] | Err[YundaexError | Exception]:
        """退回件 - 拦截结果查询"""
        path = "/tmm/outerapi/intercept/notifyExpressInterceptionRealStatus"
        data = request.dict()
        return self.send_request(path, data).map(NotifyExpressInterceptionRealStatus.Response.parse_obj)

    def intercept_cancelIntercept(
        self, request: CancelIntercept
    ) -> Ok[CancelIntercept] | Err[YundaexError | Exception]:
        """退回件 - 取消拦截"""
        path = "/tmm/outerapi/intercept/cancelIntercept"
        data = request.dict()
        return self.send_request(path, data).map(CancelIntercept.Response.parse_obj)


class YundaexAuth(AuthBase):
    def __init__(self, partner_id, secret):
        self.partner_id = partner_id
        self.secret = secret

    def __call__(self, r):
        from hashlib import md5

        body = r.body.decode("utf-8")
        body = {
            "partnerId": self.partner_id,
            "dataDigest": md5((body + self.secret).encode("utf-8")).hexdigest(),
            "data": body,
        }
        r.prepare_body(body, None)
        r.headers["Content-Type"] = "application/x-www-form-urlencoded"
        return r

    @classmethod
    def get_by_shop(cls, shop: "Shop", auth_account: str | None = None):
        from robot_processor.enums import AuthType
        from robot_processor.shop.auth_manager import Credentials

        auth = Credentials.get_by_shop_auth_type(shop, AuthType.YUNDAEX, auth_account)
        if auth is None:
            raise ValueError("未找到店铺的韵达快递授权信息")
        return cls(auth.auth_extra_data["partnerId"], auth.auth_extra_data["secret"])

    def __repr__(self):
        return f'YundaexAuth(partner_id="{self.partner_id}")'
