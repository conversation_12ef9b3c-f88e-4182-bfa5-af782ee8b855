import requests
from dramatiq import Retry

from robot_processor.client import token_bucket_limiter
from robot_processor.client_mixins import Session
from robot_processor.client.conf import app_config


class N8nClient:
    session = Session()

    def create_order(self, sid: str, flow_uuid: str, mode: str, bo_detail: dict,
                     check_rate_limit=True) -> requests.Response:
        """
        作为一个RPA执行，当执行到N8N的RPA时，将当前任务以及工单实例的数据发到N8N, 等回调结果
        POST /webhook/${uuid-with-hyphenation}

        {
          "business_order": {
            "id": 1,
            "created_at": 0, //创建时间
            "status": 1,
            "steps": [] // 步骤数据，包含不同阶段采集的表单数据，均为示例, 按照实际工单模型制定字段
          }
        }
        rt : {"success": true, "msg": "exc info"}
        """
        if check_rate_limit:
            token_bucket_key = 'mola:vip:vip'
            if not token_bucket_limiter.try_acquire_token_for_store(token_bucket_key, sid):
                raise Retry(message=f'{token_bucket_key}:{sid} is rate limited', delay=3000)
        url = f"{app_config.N8N_ENDPOINT}/events/dispatch/{flow_uuid}?version=1&mode={mode}"
        resp = self.session.post(url, json=bo_detail)
        resp.raise_for_status()
        return resp
