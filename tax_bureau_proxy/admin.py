from flask import render_template_string, jsonify, redirect, url_for

from tax_bureau_proxy import app, cache

admin_template = """
<!DOCTYPE html>
<html>
<head>
    <title>静态资源管理</title>
    <style>
        body { font-family: Arial, sans-serif; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f4f4f4; }
        .actions a { margin-right: 10px; }
    </style>
</head>
<body>
    <h1>静态资源管理</h1>
    <h2>已缓存的静态资源</h2>
    <table>
        <thead>
            <tr>
                <th>文件地址</th>
                <th>Size (bytes)</th>
                <th>Response Headers</th>
                <th>创建时间</th>
                <th>最近访问时间</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for static_resource, item in items %}
            <tr>
                <td>{{ static_resource }}</td>
                <td>{{ item['size'] }}</td>
                <td>
                {% for header in item['response_headers'] %}
                <br>{{header}}
                {% endfor %}
                </td>
                <td>{{ item['created_at'] }}</td>
                <td>{{ item['recent_use_at'] }}</td>
                <td class="actions">
                    <a href="{{ url_for('view_cache', static_resource=static_resource) }}">View</a>
                    <a href="{{ url_for('delete_cache', static_resource=static_resource) }}"
                      onclick="return confirm('Delete this cache?')"
                      >
                      Delete
                    </a>
                    <a href="{{ url_for('refresh_cache', static_resource=static_resource) }}">Refresh</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
"""


@app.route("/proxy/admin")
def admin_page():
    """
    Admin 页面：查看缓存列表。
    """
    items = []
    for key in cache:
        value = cache.get(key)
        items.append(
            (
                key.replace("static:", ""),
                {
                    "size": len(value["content"]) if value else 0,
                    "response_headers": [
                        f"{key}: {val}"
                        for key, val in value["response_headers"].items()
                    ],
                    "created_at": value["created_at"],
                    "recent_use_at": value["recent_use_at"],
                },
            )
        )
    return render_template_string(admin_template, items=items)


@app.route("/proxy/admin/view/<path:static_resource>")
def view_cache(static_resource):
    """
    查看某个缓存项的详细信息。
    """
    cache_key = f"static:{static_resource}"
    if cache_key in cache:
        cached_data = cache[cache_key]
        return jsonify(
            {
                "cache_key": cache_key,
                "etag": cached_data.get("etag"),
                "last_modified": cached_data.get("last_modified"),
                "response_headers": cached_data.get("response_headers"),
                "content_preview": cached_data["content"][:100].decode(errors="replace")
                + "..."
                if cached_data.get("content")
                else "No content",
            }
        )
    return "Cache key not found", 404


@app.route("/proxy/admin/delete/<path:static_resource>")
def delete_cache(static_resource):
    """
    删除某个缓存项。
    """
    cache_key = f"static:{static_resource}"
    if cache_key in cache:
        cache.delete(cache_key)
        return redirect(url_for("admin_page"))
    return "Cache key not found", 404


@app.route("/proxy/admin/refresh/<path:static_resource>")
def refresh_cache(static_resource):
    """
    手动刷新某个缓存项。
    """
    from tax_bureau_proxy import proxy_static_with_cache
    cache_key = f"static:{static_resource}"
    if cache_key in cache:
        static_resource = cache_key.replace("static:", "")

        # 获取缓存的条件请求头（如果有）
        try:
            delete_cache(static_resource)
            proxy_static_with_cache(static_resource)
            return redirect(url_for("admin_page"))
        except Exception as e:
            return f"Failed to refresh cache: {e}", 500
    return "Cache key not found", 404
