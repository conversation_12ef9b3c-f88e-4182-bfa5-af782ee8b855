from typing import Self

from pydantic import BaseModel, Field

from robot_processor.buyer_table.models import BuyerTable
from robot_processor.form.models import Form, FormShop
from robot_processor.buyer_table.utils import check_form_is_valid


class FormIds(BaseModel):
    form_ids: list[str]


class SavePageLayout(BaseModel):
    ui_schema: dict


class Icon(BaseModel):
    type: int = Field(title="图标类型: 1、 系统图标 2、自定义图标")
    color: dict | None = Field({}, title="颜色")
    url: str = Field(title="图标链接")


class SaveBuyerTable(BaseModel):
    form_id: str = Field(title="关联工单id")
    table_name: str = Field(title="关联表单名称")
    description: str | None = Field("", title="备注", max_length=500)
    icon: Icon = Field(title="表单图标")
    enabled: bool | None = Field(title="表单图标")


class BuyerTableDetail(BaseModel):
    """获取表单详情"""
    id: int = Field(title="id")
    form_id: str | None = Field("", title="关联工单id")
    form_name: str | None = Field("", title="关联工单名称")
    name: str | None = Field("", title="关联工单名称")
    is_valid: bool = Field(title="关联工单是否生效")
    invalid_reason: list[str] | None = Field(title="失效理由")
    tags: list | None = Field([], title="标签")
    table_name: str = Field(title="表单名称")
    description: str | None = Field("", title="备注")
    icon: Icon = Field(title="表单图标")
    created_at: int = Field(title="创建的时间戳")
    updated_at: int = Field(title="更新的时间戳")
    updater: str = Field(title="更新人")
    enabled: bool = Field(title="表单是否禁用")
    sort: int = Field(title="排序")

    @classmethod
    def generate(cls, table_info: BuyerTable.View, form: Form | None, form_shop: FormShop | None) -> Self:
        is_valid, invalid_reason = check_form_is_valid(form, form_shop)
        return cls(
            id=table_info.id,
            form_id=table_info.form_id,
            form_name=form.name if form is not None else "",
            name=form.name if form is not None else "",
            is_valid=is_valid,
            invalid_reason=[invalid_reason] if invalid_reason != "" else [],
            tags=form.tags if form is not None else [],
            table_name=table_info.name,
            description=table_info.description,
            icon=Icon(
                type=table_info.icon.get("type") or "",
                color=table_info.icon.get("color") or "",
                url=table_info.icon.get("url") or "",
            ),
            created_at=table_info.created_at,
            updated_at=table_info.updated_at,
            updater=table_info.updater.get("user_nick") or "",
            enabled=table_info.enabled,
            sort=table_info.sort,
        )


class FormBrief(BaseModel):
    form_id: str = Field(title="关联工单id")
    name: str = Field(title="关联工单名称")
    description: str | None = Field("", title="备注")
    tags: list | None = Field([], title="标签")
    is_valid: bool = Field(title="是否生效")
    invalid_reason: list[str] | None = Field(title="失效理由")
    is_bound: bool = Field(True, title="是否被关联")

    @classmethod
    def generate(cls, form: Form, form_shop: FormShop | None) -> Self:
        is_valid, invalid_reason = check_form_is_valid(form, form_shop, True)
        return cls(
            form_id=form.id,
            name=form.name,
            description=form.description,
            tags=form.tags,
            is_valid=is_valid,
            invalid_reason=[invalid_reason] if invalid_reason != "" else [],
        )


class BuyerTableSort(BaseModel):
    class Sort(BaseModel):
        id: int = Field(title="表单id")
        sort: int = Field(title="顺序")

    sorts: list[Sort]
