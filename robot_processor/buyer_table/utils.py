from loguru import logger

from robot_processor.form.models import Form, FormShop, FormMold
from robot_processor.buyer_table.enums import InvalidReason


def check_form_is_valid(
    form: Form | None,
    form_shop: FormShop | None,
    check_form_can_bound: bool = False
) -> tuple[bool, str]:
    is_valid: bool = True
    invalid_reason: str = ""

    if form is None:
        is_valid = False
        invalid_reason = InvalidReason.NOT_BOUND_FORM
    elif form_shop is None:
        is_valid = False
        logger.error(f"发现未绑定 shop 的工单模板：{form.id}")
        invalid_reason = InvalidReason.FORM_DELETED
    elif form_shop.status == FormShop.Status.DISABLED:
        is_valid = False
        invalid_reason = InvalidReason.FORM_DISABLED
    elif form_shop.status in [FormShop.Status.DELETED, FormShop.Status.RESERVED]:
        is_valid = False
        invalid_reason = InvalidReason.FORM_DELETED
    elif check_form_can_bound:
        if form.form_mold != FormMold.BUYER:
            is_valid = False
            invalid_reason = InvalidReason.INVALID_TYPE
        else:
            # 基于 form_version 来判断工单模板是否存在过发布。
            # 比较老的工单模板或者部分异常情况下，没有 form_version。
            # 如果出现 case，我们这边再处理与排查。
            if form.versions.count() == 0:
                is_valid = False
                invalid_reason = InvalidReason.FORM_NOT_PUBLISHED

    return is_valid, invalid_reason
