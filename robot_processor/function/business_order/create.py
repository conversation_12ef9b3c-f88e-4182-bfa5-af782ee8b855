from decimal import Decimal

import arrow
from robot_types.func.business_order.vanilla import CreateBusinessOrderFromDataProjectionABC
from robot_types.helper import data_projection


class CreateBusinessOrderFromDataProjection(CreateBusinessOrderFromDataProjectionABC):
    def _validate_parameters(self):
        from robot_processor.enums import ShopStatus
        from robot_processor.ext import db
        from robot_processor.form.models import Form
        from robot_processor.shop.models import Shop

        if not self.is_form_id_set():
            raise ValueError("未选择工单模板")
        if not self.is_shop_set():
            raise ValueError("未选择店铺")
        shop: Shop | None = Shop.query.filter_by(sid=self.shop.sid, platform=self.shop.platform).first()
        if not shop:
            raise ValueError(f"店铺信息不可用 {self.shop}")
        if shop.deleted:
            raise PermissionError("店铺已关闭")
        if shop.status != ShopStatus.ENABLE:
            raise PermissionError("店铺已禁用")
        self.shop_instant = shop
        form = db.session.get(Form, self.form_id)
        if form is None:
            raise ValueError("工单模板未找到")
        if not Form.Queries.is_subscribed(form.id, shop):
            raise PermissionError(f"店铺 {shop.title}@{shop.sid} 的工单模板[{form.name}]未启用")
        self.form_instant = form

    def _execute(self):
        from robot_processor.assistant.schema import AccountDetailV2
        from robot_processor.enums import Creator, FromType
        from robot_processor.business_order.business_order_manager import BusinessManager
        from robot_processor.utils import raise_exception
        shop = self.shop_instant
        form = self.form_instant
        creator = AccountDetailV2(user_type=Creator.RPA, user_id=0, user_nick="RPA")
        business_manager = BusinessManager(form, shop, creator, FromType.KAFKA_TRADE_EVENT)
        data = data_projection.rule_based.apply_projection(self.data, self.projection)
        created = business_manager.pipeline_create_order(data).unwrap_or_else(raise_exception)
        res = self.Return(id=Decimal(created.id), created_at=arrow.get(created.created_at).naive)
        return res
