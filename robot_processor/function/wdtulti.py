"""旺店通旗舰版"""
from result import as_result

from robot_processor.form.symbol_table import Value, TypeSpec, NamedTypeSpec, \
    WdtultiGoods, WdtultiTradeInfo
from robot_processor.function.base import FnBase, FnArgs, FnArg, FnSignature

__all__ = ["GetTradeInfo"]


class GetTradeInfo(FnBase):
    """获取订单信息

    fn get_trade_info(platform_trade_id: str, credential: dict) -> string
    """

    class Args(FnArgs):
        # 平台订单号
        PLATFORM_TRADE_ID = FnArg(
            name="platform_trade_id",
            type_spec=TypeSpec(type="string"),
        )
        # 旺店通系统单号，默认单号为JY开头
        TRADE_NO = FnArg(
            name="trade_no",
            type_spec=TypeSpec(type="string"),
        )
        CREDENTIAL = FnArg(
            name="credential",
            type_spec=NamedTypeSpec.CREDENTIAL_WDTULTI.type_spec
        )
        LOGISTICS_NO = FnArg(
            name="logisitcs_no",
            type_spec=TypeSpec(type="string"),

        )
        # 订单类别
        ORDER_TYPE = FnArg(
            name="order_type",
            type_spec=TypeSpec(type="array", spec=[TypeSpec(type="string")])
        )

    Signature = FnSignature(
        name="wdtulti.get_trade_info",
        args=Args,
        rtype=TypeSpec(type="array",
                       spec=[NamedTypeSpec.WDTULTI_TRADE_INFO.type_spec]),
    )

    # @classmethod
    # def get_order_type_value(cls, ot: str):
    #    match ot:
    #        case "全部":
    #            return 0
    #        case "网店销售":
    #            return 1
    #        case "订单补发":
    #           return 7

    @as_result(TypeError, ValueError)
    def call(self) -> Value:
        from rpa.erp.wdtulti import WdtUltiQM

        credential = self.get_arg_value_or_none(
            self.Args.CREDENTIAL, rtype=NamedTypeSpec.CREDENTIAL_WDTULTI.model
        ).unwrap()
        if not credential:
            raise ValueError("未找到店铺的旺店通旗舰版授权信息")
        platform_tid = self.get_arg_value_or_none(
            self.Args.PLATFORM_TRADE_ID, rtype=str
        ).unwrap()
        trade_no = self.get_arg_value_or_none(self.Args.TRADE_NO,
                                              rtype=str).unwrap()
        logisitcs_no = self.get_arg_value_or_none(self.Args.LOGISTICS_NO,
                                                  rtype=str).unwrap()
        if not any([platform_tid, trade_no, logisitcs_no]):
            raise ValueError("平台订单号和旺店通系统单号和快递单号不能同时为空")
        order_types = self.get_arg_value_or_none(
            self.Args.ORDER_TYPE, rtype=list).unwrap()
        order_type: str = "全部"
        if order_types:
            order_type = order_types[0]

        wdt_client = WdtUltiQM(credential=credential)
        wdt_response = wdt_client.get_orders(src_tid=platform_tid,
                                             trade_no=trade_no,
                                             logistics_no=logisitcs_no)
        if order_type == "普通订单":
            wdt_response.order = [order for order in wdt_response.order if
                                  order.trade_from not in [6]]
        elif order_type == "补发订单":
            wdt_response.order = [order for order in wdt_response.order if
                                  order.trade_from == 6]
        output_trade_list = []
        for trade in wdt_response.order:
            trade_warehouse_no_list = list(set(trade.warehouse_no.split(",")))
            trade_warehouse_name_list = []
            for warehouse_no in trade_warehouse_no_list:
                warehouse_name = wdt_client.get_warehouse_name(
                    warehouse_no=warehouse_no)
                if warehouse_name:
                    trade_warehouse_name_list.append(warehouse_name)
            trade.warehouse_name = ",".join(trade_warehouse_name_list)
            goods_list = []
            for detail in trade.detail_list:
                goods_list.append(WdtultiGoods(goods_name=detail.goods_name,
                                               goods_no=detail.goods_no,
                                               spec_name=detail.spec_name,
                                               spec_no=detail.spec_no,
                                               num=int(float(detail.num))))
            output_trade = WdtultiTradeInfo(
                trade_no=trade.trade_no,
                chinese_status=trade.chinese_status,
                logistics_name=trade.logistics_name,
                logistics_no=trade.logistics_no,
                paid=float(trade.paid),
                warehouse_no=trade.warehouse_no,
                warehouse_name=trade.warehouse_name,
                goods_list=goods_list)
            output_trade_list.append(output_trade)
        wdtulti_trades = [
            NamedTypeSpec.WDTULTI_TRADE_INFO.model.validate(out.dict()).dict()
            for out in output_trade_list
        ]

        return self.wrap_return(wdtulti_trades)
