from typing import Any
from decimal import Decimal

from result import as_result
from robot_types.model import credential

from robot_processor.form import named_type_spec
from robot_processor.form.symbol_table import TypeSpec
from robot_processor.function.base import FnBase, FnArgs, FnArg, FnSignature
from rpa.erp.wln.schemas import Order as WlnOrder


class GetTradeInfo(FnBase):
    class Args(FnArgs):
        # 平台订单号
        TID = FnArg(name="tid", type_spec=TypeSpec(type="string"))
        # 过滤条件参数
        EXPRESS_NO = FnArg(name="express_no", type_spec=TypeSpec(type="string"))
        TRADE_TYPE = FnArg(
            name="trade_type",
            type_spec=TypeSpec(type="array", spec=[TypeSpec(type="number")])
        )
        TRADE_STATUS = FnArg(
            name="trade_status",
            type_spec=TypeSpec(type="array", spec=[TypeSpec(type="number")])
        )
        # 授权信息
        CREDENTIAL = FnArg(name="credential", type_spec=named_type_spec.Credential.WLN.type_spec)

    Signature = FnSignature(
        name="wln.get_trade_info",
        args=Args,
        rtype=TypeSpec(type="array", spec=[named_type_spec.WLN.WLN_TRADE_INFO.type_spec]),
    )

    @as_result(TypeError, ValueError)
    def call(self):
        from rpa.erp.wln import WlnClient

        wln_credential = self.get_arg_value(
            self.Args.CREDENTIAL, rtype=named_type_spec.Credential.WLN.model
        ).expect("缺少授权信息")

        tid: str = self.get_arg_value_or_none(self.Args.TID, rtype=str).unwrap() or ""
        express_no: str = self.get_arg_value_or_none(self.Args.EXPRESS_NO, rtype=str).unwrap() or ""
        trade_types: list[int] = self.get_arg_value_or_none(self.Args.TRADE_TYPE, rtype=list).unwrap() or []
        trade_status: list[int] | None = self.get_arg_value_or_none(self.Args.TRADE_STATUS, rtype=list).unwrap() or []

        # 查询条件
        if not tid:
            raise ValueError("查询条件不能为空")

        # 筛选条件
        query_params: dict[str, Any] = {}
        query_extend: dict[str, Any] = {}
        if express_no:
            query_extend["express_no"] = express_no
        if query_extend:
            query_params["query_extend"] = query_extend
        if trade_status:
            query_params["trade_status"] = ",".join([str(i) for i in trade_status])

        wln_client = WlnClient(credential.WLN(wln_credential.app_key, wln_credential.app_secret))
        wln_raw_orders: list[WlnOrder] | None = wln_client.query_trades(
            tids=[tid],
            **query_params
        ).data
        if wln_raw_orders is None:
            raise ValueError("查询订单失败")

        # 根据订单类型去过滤
        if trade_types:
            wln_raw_orders = [
                wln_raw_order
                for wln_raw_order in wln_raw_orders
                if wln_raw_order.trade_type in trade_types
            ]

        wln_orders = [
            named_type_spec.WLN.WLN_TRADE_INFO.model(
                tp_tid=wln_raw_order.tp_tid,
                trade_no=wln_raw_order.trade_no,
                buyer_account=wln_raw_order.buyer_account,
                buyer_mobile=wln_raw_order.buyer_mobile,
                buyer=wln_raw_order.buyer,
                buyer_msg=wln_raw_order.buyer_msg,
                logistic_name=wln_raw_order.logistic_name,
                express_code=wln_raw_order.express_code,
                flag=wln_raw_order.flag_zh,
                split_trade=(True if wln_raw_order.split_trade == 1 else False),
                has_refund=(True if wln_raw_order.has_refund == 1 else False),
                trade_type=wln_raw_order.trade_type_zh,
                storage_name=wln_raw_order.storage_name,
                process_status=wln_raw_order.process_status_zh,
                paid_fee=wln_raw_order.paid_fee,
                real_payment=wln_raw_order.real_payment,
                orders=[
                    named_type_spec.WLN.WLN_ITEM.model(
                        tp_oid=item.tp_oid,
                        item_name=item.item_name,
                        is_package=(True if item.is_package == 1 else False),
                        oln_item_name=item.oln_item_name,
                        item_platform_url=item.item_platform_url,
                        item_image_url=item.item_image_url,
                        oln_item_id=item.oln_item_id,
                        oln_item_code=item.oln_item_code,
                        oln_sku_id=item.oln_sku_id,
                        oln_sku_name=item.oln_sku_name,
                        sku_code=item.sku_code,
                        oln_status=item.oln_status_zh,
                        order_id=item.order_id,
                        payment=float(Decimal(item.payment)) if item.payment else None,
                        price=float(Decimal(item.price)) if item.price else None,
                        remark=item.remark,
                        size=float(Decimal(item.size)) if item.size else None,
                        inventory_status=item.inventory_status,
                        bar_code=item.bar_code,
                        discounted_unit_price=item.discounted_unit_price,
                        receivable=float(Decimal(item.receivable)) if item.receivable else None,
                    )
                    for item in (wln_raw_order.orders or [])
                ]
            ).dict()
            for wln_raw_order in wln_raw_orders
        ]
        return self.wrap_return(wln_orders)
