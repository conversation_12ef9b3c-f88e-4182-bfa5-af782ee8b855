"""旺店通企业版"""
from result import as_result

from robot_processor.form.symbol_table import Value, TypeSpec, NamedTypeSpec
from robot_processor.function.base import FnBase, FnArgs, FnArg, FnSignature

__all__ = ["GetTradeInfo"]

from rpa.erp.wdt import WdtOpenAPIClient


class GetTradeInfo(FnBase):
    """获取订单信息

    fn get_trade_info(platform_trade_id: str, credential: dict) -> string
    """

    class Args(FnArgs):
        # 平台订单号
        PLATFORM_TRADE_ID = FnArg(
            name="platform_trade_id",
            type_spec=TypeSpec(type="string"),
        )
        # 旺店通系统单号，默认单号为JY开头
        TRADE_NO = FnArg(
            name="trade_no",
            type_spec=TypeSpec(type="string"),
        )
        LOGISTICS_NO = FnArg(
            name="logisitcs_no",
            type_spec=TypeSpec(type="string"),

        )
        # 订单类别
        ORDER_TYPE = FnArg(
            name="order_type",
            type_spec=TypeSpec(type="array", spec=[TypeSpec(type="string")])
        )
        CREDENTIAL = FnArg(
            name="credential", type_spec=NamedTypeSpec.CREDENTIAL_WDT.type_spec
        )

    Signature = FnSignature(
        name="wdt.get_trade_info",
        args=Args,
        rtype=TypeSpec(type="array",
                       spec=[NamedTypeSpec.WDT_TRADE_INFO.type_spec]),
    )

    @classmethod
    def get_order_type_value(cls, ot: str):
        match ot:
            case "全部":
                return 0
            case "网店销售":
                return 1
            case "订单补发":
                return 7

    @as_result(TypeError, ValueError)
    def call(self) -> Value:
        from rpa.erp.wdt import WdtClient

        credential = self.get_arg_value_or_none(
            self.Args.CREDENTIAL, rtype=NamedTypeSpec.CREDENTIAL_WDT.model
        ).unwrap()
        if not credential:
            raise ValueError("未找到店铺的旺店通授权信息")
        platform_tid = self.get_arg_value_or_none(
            self.Args.PLATFORM_TRADE_ID, rtype=str
        ).unwrap()
        trade_no = self.get_arg_value_or_none(self.Args.TRADE_NO,
                                              rtype=str).unwrap()
        logisitcs_no = self.get_arg_value_or_none(self.Args.LOGISTICS_NO,
                                                  rtype=str).unwrap()
        if not any([platform_tid, trade_no, logisitcs_no]):
            raise ValueError("平台订单号和旺店通系统单号和快递单号不能同时为空")

        order_types = self.get_arg_value_or_none(
            self.Args.ORDER_TYPE, rtype=list).unwrap()
        order_type: str = "全部"
        if order_types:
            order_type = order_types[0]
        order_type_value = GetTradeInfo.get_order_type_value(order_type)
        wdt_client = WdtClient(credential=credential)
        wdt_open_api_client = WdtOpenAPIClient(credential=credential)
        wdt_response = wdt_client.trade_query(wdt_tid=platform_tid,
                                              logistics_no=logisitcs_no,
                                              trade_no=trade_no).response
        if order_type_value > 0:
            wdt_response.trades = [trade for trade in wdt_response.trades if
                                   trade.order_type == order_type_value]
        for trade in wdt_response.trades:
            trade_warehouse_no_list = list(set(trade.warehouse_no.split(",")))
            trade_warehouse_name_list = []
            for warehouse_no in trade_warehouse_no_list:
                warehouse_response = wdt_client.warehouse_query(
                    warehouse_no=warehouse_no).response
                if warehouse_response.errorcode == 0 and warehouse_response.warehouses:
                    trade_warehouse_name_list.append(
                        warehouse_response.warehouses[0].name)
            trade.warehouse_name = ",".join(trade_warehouse_name_list)
            for goods in trade.goods_list:
                brand_name = ""
                if goods.suite_no:
                    suite_resp = wdt_open_api_client.query_combine_skus_by_combine_sku_id(
                        goods.suite_no)
                    if suite_resp.suites:
                        brand_name = suite_resp.suites[0].brand_name
                else:
                    sku_resp = wdt_open_api_client.query_skus_by_sku_id(
                        goods.spec_no)
                    if sku_resp.goods_list:
                        brand_name = sku_resp.goods_list[0].brand_name
                goods.brand_name = brand_name
            stockout_resp = WdtClient(
                credential=credential).stockout_order_query_trade(
                trade.trade_no)
            if stockout_resp.response.stockout_list:
                trade.consign_time = stockout_resp.response.stockout_list[
                    0].consign_time

        wdt_trades = [
            NamedTypeSpec.WDT_TRADE_INFO.model.validate(trade.dict()).dict()
            for trade in wdt_response.trades
        ]

        return self.wrap_return(wdt_trades)
