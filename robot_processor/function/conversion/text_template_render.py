from typing import Any

from robot_types.core import Symbol
from robot_types.core import TypeSpec
from robot_types.core import Value
from robot_types.func import conversion
from robot_types.helper import ValueResolver
from robot_types.helper import deserialize
from robot_types.helper.form_composer import FormComposer
from robot_types.helper.predefined import BizType
from robot_types.model import component


class TextTemplateRender(conversion.render.TextTemplateRenderABC):
    """关联 DataType.TEXT_TEMPLATE 类型，用于维护组件在富文本中的默认渲染规则"""

    def get_symbol_table_and_scope(self):
        if self.is_mode_set():
            symbol_table = BizType(self.mode).provide_symbol_table()
            return symbol_table, symbol_table.root_scope
        else:
            form_symbol_table: FormComposer.SymbolTableComposer = self.symbol_table  # type: ignore[has-type]
            return form_symbol_table, form_symbol_table.current_scope

    def _validate_parameters(self):
        from robot_processor.business_order.models import get_form_composer

        super()._validate_parameters()
        if not self.is_context_set():
            raise ValueError("context is not set")
        if not self.is_template_set():
            raise ValueError("template not set")
        if not self.is_mode_set():
            if not self.is_form_version_id_set():
                raise ValueError("form_version_id not set")
            if not self.is_current_step_id_set():
                raise ValueError("current_step_id not set")
            form_composer = get_form_composer(int(self.form_version_id))
            form_composer.set_current(int(self.current_step_id))
            self.symbol_table = form_composer.symbol_table_wrapper
        self.text_template_resolver = TextTemplateResolver()

    def _execute(self):
        from robot_processor.utils import raise_exception

        text = ""
        value_resolver = ValueResolver(context=self.context)
        for segment in self.template:
            segment_value = deserialize(segment, Value)
            value = segment_value.with_resolver(value_resolver).resolve().unwrap_or_else(raise_exception)
            if value is None:
                continue
            if segment_value.qualifier == "var":
                symbol_table, scope = self.get_symbol_table_and_scope()
                segment_symbol = segment_value.var.resolve_symbol(symbol_table, scope)
                text += self.to_string(segment_symbol, value)
            else:
                text += self.text_template_resolver.serialize(value)
        return text

    def to_string(self, symbol: Symbol, val: Any):
        # 大部分组件值转换为文本的方法都由 TextTemplateResolver 实现
        # 但是 TextTemplateResolver 都是基于 value 实现的，没有 value 对应的组件信息
        # 因此需要对 value 进行预处理，如将容器组件的值由 key:value 转换成 label:value 的形式
        def pre_process(_symbol, _val):
            from robot_types.helper.data_projection.label_based import convert_to_label_map

            match _symbol, _val:
                case Symbol(type_spec=TypeSpec(type="collection")), dict():
                    return convert_to_label_map(_val, _symbol.children)
                case Symbol(type_spec=TypeSpec(type="array")), list():
                    return [pre_process(_symbol.children[0], _item) for _item in _val]
                case Symbol(type_spec=TypeSpec(type="boolean")), _:
                    return {
                        True: _symbol.render_config.get("trueLabel", "是"),
                        False: _symbol.render_config.get("falseLabel", "否"),
                    }[_val]

            return _val

        val = pre_process(symbol, val)

        return self.text_template_resolver.serialize(val)


class TextTemplateResolver:
    def __init__(self):
        from cattrs import Converter

        self.converter = Converter(unstructure_fallback_factory=lambda _: str)
        self.serialize = self.converter.unstructure
        self._register_serialize_hook()

    def _register_serialize_hook(self):
        from datetime import date
        from datetime import datetime
        from datetime import time
        from decimal import Decimal

        @self.converter.register_unstructure_hook
        def datetime_hook(val: datetime) -> str:
            return val.strftime("%Y-%m-%d %H:%M:%S")

        @self.converter.register_unstructure_hook
        def date_hook(val: date) -> str:
            return val.strftime("%Y-%m-%d")

        @self.converter.register_unstructure_hook
        def time_hook(val: time) -> str:
            return val.strftime("%H:%M:%S")

        @self.converter.register_unstructure_hook
        def decimal_hook(val: Decimal) -> str:
            return str(val)

        @self.converter.register_unstructure_hook
        def select_hook(val: component.Select):
            return "/".join([level.label for level in val.levels])

        @self.converter.register_unstructure_hook
        def select_multi_hook(val: component.SelectMulti):
            options = ["/".join([level.label for level in option.levels]) for option in val.options]
            return " ".join(options)

        @self.converter.register_unstructure_hook
        def list_hook(val: list):
            return " ".join([self.serialize(item) for item in val])

        @self.converter.register_unstructure_hook
        def dict_hook(val: dict):
            return " ".join([f"{label}:{value}" for label, value in val.items()])
