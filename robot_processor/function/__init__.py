from robot_processor.function.equal import FnEqual
from robot_processor.function import wdt
from robot_processor.function import jst
from robot_processor.function import yto
from robot_processor.function import platform_order
from robot_processor.function import jackyun
from robot_processor.function import wln
from robot_processor.function import wdtulti
from robot_processor.function import baisheng

function_mapper = {
    FnEqual.Signature.name: FnEqual,
    wdt.GetTradeInfo.Signature.name: wdt.GetTradeInfo,
    jst.GetTradeInfo.Signature.name: jst.GetTradeInfo,
    jst.GetOutTradeInfo.Signature.name: jst.GetOutTradeInfo,
    yto.InterceptReport.Signature.name: yto.InterceptReport,
    platform_order.GetPlatformOrderInfo.Signature.name:
        platform_order.GetPlatformOrderInfo,
    jackyun.GetTradeInfo.Signature.name: jackyun.GetTradeInfo,
    wln.GetTradeInfo.Signature.name: wln.GetTradeInfo,
    wdtulti.GetTradeInfo.Signature.name: wdtulti.GetTradeInfo,
    baisheng.GetTradeInfo.Signature.name: baisheng.GetTradeInfo,
}


def _auto_loader():
    from pkgutil import walk_packages
    from importlib import import_module

    for _path in walk_packages(__path__, __name__ + "."):
        module = import_module(_path.name)
        for cls_name in vars(module):
            if hasattr(vars(module)[cls_name], "signature"):
                signature = getattr(vars(module)[cls_name], "signature")
                function_mapper[signature.name] = vars(module)[cls_name]


_auto_loader()
