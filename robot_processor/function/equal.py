import datetime as builtin_dt

from result import Result, Ok, do

from robot_processor.form.symbol_table import Value, TypeSpec
from robot_processor.function.base import (
    GenericFnBase,
    GenericFnArgs,
    GenericFnSignature,
    FnBase,
    FnArgs,
    FnArg,
    FnSignature,
)


class FnEqual(GenericFnBase):
    """等于运算符

    fn equal(lhs: T, rhs: T) -> boolean
    where {
        T: string | number | datetime | date | time
    }
    """

    class Args(GenericFnArgs):
        LHS = "lhs"
        RHS = "rhs"

    Signature = GenericFnSignature(
        name="comparison.equal", args=Args, rtype=TypeSpec(type="boolean")
    )

    def call(self) -> Result[Value, Exception]:
        fn_equal: FnBase
        match (
            self.get_arg_type_spec(self.Args.LHS),
            self.get_arg_type_spec(self.Args.RHS),
        ):
            case Ok(TypeSpec(type="string")), Ok(TypeSpec(type="string")):
                fn_equal = FnEqual_string(*self.args)
            case Ok(TypeSpec(type="number")), Ok(TypeSpec(type="number")):
                fn_equal = FnEqual_number(*self.args)
            case Ok(TypeSpec(type="datetime")), Ok(TypeSpec(type="date")):
                fn_equal = FnEqual_datetime_date(*self.args)
            case Ok(TypeSpec(type="datetime")), Ok(TypeSpec(type="time")):
                fn_equal = FnEqual_datetime_time(*self.args)
            case Ok(TypeSpec(type="date")), Ok(TypeSpec(type="date")):
                fn_equal = FnEqual_date(*self.args)
            case Ok(TypeSpec(type="time")), Ok(TypeSpec(type="time")):
                fn_equal = FnEqual_time(*self.args)
            case _ as unmatch:
                not_implemented_error = self.not_implemented_error(unmatch)
                return not_implemented_error

        return fn_equal.context(self._context).call()


class FnEqual_string(FnBase):
    """等于运算符

    fn equal(lhs: string, rhs: string) -> boolean
    """

    class Args(FnArgs):
        LHS = FnArg(type_spec=TypeSpec(type="string"), name="lhs")
        RHS = FnArg(type_spec=TypeSpec(type="string"), name="rhs")

    Signature = FnSignature(
        name="comparison.equal_string", args=Args, rtype=TypeSpec(type="boolean")
    )

    def call(self):
        return do(
            Ok(self.wrap_return(lhs == rhs))
            for lhs in self.get_arg_value(self.Args.LHS, rtype=str)
            for rhs in self.get_arg_value(self.Args.RHS, rtype=str)
        )


class FnEqual_number(FnBase):
    """等于运算符

    fn equal(lhs: number, rhs: number) -> boolean
    """

    class Args(FnArgs):
        LHS = FnArg(type_spec=TypeSpec(type="number"), name="lhs")
        RHS = FnArg(type_spec=TypeSpec(type="number"), name="rhs")

    Signature = FnSignature(
        name="comparison.equal_number", args=Args, rtype=TypeSpec(type="boolean")
    )

    def call(self):
        return do(
            Ok(self.wrap_return(lhs == rhs))
            for lhs in self.get_arg_value(self.Args.LHS, rtype=float)
            for rhs in self.get_arg_value(self.Args.RHS, rtype=float)
        )


class FnEqual_datetime_date(FnBase):
    """等于运算符

    fn equal(lhs: datetime, rhs: date) -> boolean
    """

    class Args(FnArgs):
        LHS = FnArg(type_spec=TypeSpec(type="datetime"), name="lhs")
        RHS = FnArg(type_spec=TypeSpec(type="date"), name="rhs")

    Signature = FnSignature(
        name="comparison.equal_datetime_date", args=Args, rtype=TypeSpec(type="boolean")
    )

    def call(self):
        return do(
            Ok(self.wrap_return(lhs.date() == rhs))
            for lhs in self.get_arg_value(self.Args.LHS, rtype=builtin_dt.datetime)
            for rhs in self.get_arg_value(self.Args.RHS, rtype=builtin_dt.date)
        )


class FnEqual_datetime_time(FnBase):
    """等于运算符

    fn equal(lhs: datetime, rhs: time) -> boolean
    """

    class Args(FnArgs):
        LHS = FnArg(type_spec=TypeSpec(type="datetime"), name="lhs")
        RHS = FnArg(type_spec=TypeSpec(type="time"), name="rhs")

    Signature = FnSignature(
        name="comparison.equal_datetime_time", args=Args, rtype=TypeSpec(type="boolean")
    )

    def call(self):
        return do(
            Ok(self.wrap_return(lhs.time() == rhs))
            for lhs in self.get_arg_value(self.Args.LHS, rtype=builtin_dt.datetime)
            for rhs in self.get_arg_value(self.Args.RHS, rtype=builtin_dt.time)
        )


class FnEqual_date(FnBase):
    """等于运算符

    fn equal(lhs: date, rhs: date) -> boolean
    """

    class Args(FnArgs):
        LHS = FnArg(type_spec=TypeSpec(type="date"), name="lhs")
        RHS = FnArg(type_spec=TypeSpec(type="date"), name="rhs")

    Signature = FnSignature(
        name="comparison.equal_date", args=Args, rtype=TypeSpec(type="boolean")
    )

    def call(self):
        return do(
            Ok(self.wrap_return(lhs == rhs))
            for lhs in self.get_arg_value(self.Args.LHS, rtype=builtin_dt.date)
            for rhs in self.get_arg_value(self.Args.RHS, rtype=builtin_dt.date)
        )


class FnEqual_time(FnBase):
    """等于运算符

    fn equal(lhs: time, rhs: time) -> boolean
    """

    class Args(FnArgs):
        LHS = FnArg(type_spec=TypeSpec(type="time"), name="lhs")
        RHS = FnArg(type_spec=TypeSpec(type="time"), name="rhs")

    Signature = FnSignature(
        name="comparison.equal_time", args=Args, rtype=TypeSpec(type="boolean")
    )

    def call(self):
        return do(
            Ok(self.wrap_return(lhs == rhs))
            for lhs in self.get_arg_value(self.Args.LHS, rtype=builtin_dt.time)
            for rhs in self.get_arg_value(self.Args.RHS, rtype=builtin_dt.time)
        )
