"""百胜订单组件"""
from result import as_result

from robot_processor.form.symbol_table import Value, TypeSpec, NamedTypeSpec, BaishengOrder
from robot_processor.function.base import FnBase, FnArgs, FnArg, FnSignature

__all__ = ["GetTradeInfo"]


class GetTradeInfo(FnBase):
    """获取百胜订单信息

    fn get_trade_info(platform_trade_id: str, credential: dict) -> string
    """

    class Args(FnArgs):
        # 平台订单号
        PLATFORM_TRADE_ID = FnArg(
            name="platform_trade_id",
            type_spec=TypeSpec(type="string"),
        )
        # 百胜系统单号
        ORDER_SN = FnArg(
            name="order_sn",
            type_spec=TypeSpec(type="string"),
        )
        # 授权信息
        CREDENTIAL = FnArg(
            name="credential",
            type_spec=NamedTypeSpec.CREDENTIAL_BAISHENG.type_spec
        )
        # 快递单号
        LOGISTICS_NO = FnArg(
            name="logistics_no",
            type_spec=TypeSpec(type="string"),
        )
        # 订单状态
        ORDER_STATUS = FnArg(
            name="order_status",
            type_spec=TypeSpec(type="array", spec=[TypeSpec(type="string")])
        )
    Signature = FnSignature(
        name="baisheng.get_trade_info",
        args=Args,
        rtype=TypeSpec(type="array",
                       spec=[NamedTypeSpec.BAISHENG_TRADE_INFO.type_spec]),
    )

    @as_result(TypeError, ValueError)
    def call(self) -> Value:
        from rpa.erp.baisheng import BaiShengClient

        credential = self.get_arg_value_or_none(
            self.Args.CREDENTIAL, rtype=NamedTypeSpec.CREDENTIAL_BAISHENG.model
        ).unwrap()
        if not credential:
            raise ValueError("未找到店铺的百胜授权信息")
        platform_tid = self.get_arg_value_or_none(
            self.Args.PLATFORM_TRADE_ID, rtype=str
        ).unwrap()
        order_sn = self.get_arg_value_or_none(self.Args.ORDER_SN,
                                              rtype=str).unwrap()
        logistics_no = self.get_arg_value_or_none(self.Args.LOGISTICS_NO,
                                                  rtype=str).unwrap()
        if not any([platform_tid, order_sn, logistics_no]):
            raise ValueError("平台订单号、百胜系统单号和快递单号不能同时为空")

        order_statues = self.get_arg_value_or_none(
            self.Args.ORDER_STATUS, rtype=list).unwrap()
        order_status: str = "全部"
        if order_statues:
            order_status = order_statues[0]

        bs_client = BaiShengClient(credential=credential)
        # 选择调用方法
        if order_sn:
            resp = bs_client.get_order_list_by_order_sn(order_sn)
        elif logistics_no:
            resp = bs_client.get_order_list_by_shipping_sn(logistics_no)
        elif platform_tid:
            resp = bs_client.get_order_list(platform_tid)
        if not resp.ok:
            raise ValueError("访问百胜API失败")

        resp_obj = resp.json()
        if resp_obj.get("status") == "api-success" \
                and resp_obj.get("message") == "success":
            orders = resp_obj.get("data").get("orderListGets")
        else:
            raise ValueError("访问百胜API失败")

        trades = []
        for order in orders:
            # 先将五个字段转为 bool，再转为 BaishengOrder
            for key in ["is_copy", "is_split", "is_split_new", "is_combine", "is_combine_new"]:
                if key in order:
                    order[key] = bool(order[key])
            trades.append(BaishengOrder.parse_obj(order))
        if order_status != "全部":
            trades = [trade for trade in trades if
                      trade.readable_order_status == order_status]
        baisheng_trades = [
            NamedTypeSpec.BAISHENG_TRADE_INFO.model.validate(out.dict()).dict()
            for out in trades
        ]
        return self.wrap_return(baisheng_trades)
