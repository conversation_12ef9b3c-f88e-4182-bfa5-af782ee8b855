"""聚水潭"""

from result import as_result

from robot_processor.error.client_request import JstRequestError
from robot_processor.form import named_type_spec
from robot_processor.form.symbol_table import TypeSpec
from robot_processor.function.base import FnArg
from robot_processor.function.base import FnArgs
from robot_processor.function.base import FnBase
from robot_processor.function.base import FnSignature
from rpa.erp.jst import JstBaseOrder
from rpa.erp.jst import OpenOrdersOutSimpleQuery
from rpa.erp.jst import OutOrder


class GetTradeInfo(FnBase):
    """获取订单信息

    fn get_trade_info()
    """

    class Args(FnArgs):
        # 平台订单号
        SO_ID = FnArg(name="so_id", type_spec=TypeSpec(type="string"))
        # 内部单号
        O_ID = FnArg(name="o_id", type_spec=TypeSpec(type="string"))
        CREDENTIAL = FnArg(name="credential", type_spec=named_type_spec.Credential.JST.type_spec)

    Signature = FnSignature(
        name="jst.get_trade_info",
        args=Args,
        rtype=TypeSpec(type="array", spec=[named_type_spec.JST.JST_TRADE_INFO.type_spec]),
    )

    @as_result(TypeError, ValueError, JstRequestError)
    def call(self):
        from rpa.erp.jst import JstQmSDK

        credential = self.get_arg_value(self.Args.CREDENTIAL, rtype=named_type_spec.Credential.JST.model).expect(
            "缺少授权信息"
        )
        so_id = self.get_arg_value_or_none(self.Args.SO_ID, rtype=str).unwrap() or ""
        o_id = self.get_arg_value_or_none(self.Args.O_ID, rtype=str).unwrap() or ""
        query_params = {}
        if so_id:
            query_params["so_ids"] = so_id
        elif o_id:
            query_params["o_ids"] = o_id
        else:
            raise ValueError("查询条件不能为空")
        jst = JstQmSDK(credential=credential)
        jst_raw_orders = jst.query_orders(query_params).orders
        # 如果没有查询到订单信息，则尝试查询一下归档订单。
        if len(jst_raw_orders) == 0:
            query_params.update({"archive": "true"})
            jst_raw_orders = jst.query_orders(query_params).orders

        for order in jst_raw_orders:
            self.process_order(order)
        jst_orders = [
            named_type_spec.JST.JST_TRADE_INFO.model.validate(jst_raw_order).dict() for jst_raw_order in jst_raw_orders
        ]
        return self.wrap_return(jst_orders)

    def process_order(self, order: JstBaseOrder):
        from robot_processor.enums import JstTradeStatus
        from rpa.erp.jst import JstNewSDK
        from rpa.erp.jst import JstQmSDK
        from rpa.erp.jst import JstSDK

        credential = self.get_arg_value(self.Args.CREDENTIAL, rtype=named_type_spec.Credential.JST.model).unwrap()
        sdk = JstSDK(credential=credential)
        new_sdk = JstNewSDK(credential=credential)
        qm = JstQmSDK(credential=credential)

        try:
            order.f_weight_float = float(order.f_weight)
        except ValueError:
            pass
        # 订单状态
        order.status = JstTradeStatus.get_readable_name(order.status)
        # 供销商
        if order.drp_co_id_to:
            for info in new_sdk.supplier_name_query(order.drp_co_id_to).data.supplier_vos:
                if info.supplier_co_id == str(order.drp_co_id_to):
                    order.supplier_name = info.co_name
                    break
            else:
                order.supplier_name = "-"
        else:
            order.supplier_name = "-"
        # 分销商
        if order.drp_co_id_to:
            for drp_info in sdk.get_distributor_info_by_org_id(credential.co_id, [order.drp_co_id_to]).datas:
                if drp_info.drp_co_id == order.drp_co_id_to:
                    order.drp_co_name = drp_info.name
                    break
        else:
            order.drp_co_name = "-"

        # 仓库
        if order.wms_co_id == 0:
            order.wms_co_name = "未设定发货仓库"
        else:
            for wms_info in qm.get_wms_info_by_org_id(credential.co_id, False).datas:
                if wms_info.wms_co_id == order.wms_co_id:
                    order.wms_co_name = wms_info.name
                    break
            else:
                order.wms_co_name = "未知分仓"
        # 是否拆分单
        order.has_split_order = "是" if "SPLIT" in order.order_from else "否"
        # 批次
        order.wave_id = new_sdk.get_wave_id(order.o_id) or "-"
        # 拣货员
        order.picker_name = new_sdk.get_picker_name(int(order.o_id)) or ""
        # 过滤换掉的商品
        order.items = [item for item in order.items if item.item_status != "Replaced"]
        # 补充商品信息
        single_items, combined_items = [], []
        for item in order.items:
            if item.sku_type == "combine":
                combined_items.append(item)
            else:
                single_items.append(item)
        sku_info_map = {}
        if single_items:
            sku_info_map.update(
                {
                    sku_info["sku_id"]: sku_info
                    for sku_info in sdk.sku_query([item.sku_id for item in single_items]).skus
                }
            )
        if combined_items:
            sku_info_map.update(
                {
                    sku_info["sku_id"]: sku_info
                    for sku_info in sdk.combine_sku_query([item.sku_id for item in combined_items]).skus
                }
            )
        for item in order.items:
            if item.sku_id in sku_info_map:
                sku_info = sku_info_map[item.sku_id]
                item.erp_name = sku_info["name"]
                item.erp_short_name = sku_info["short_name"]
                item.supplier_name = sku_info["supplier_name"] or "-"
                item.category = sku_info["category"]


class GetOutTradeInfo(FnBase):
    """获取出库单信息"""

    class Args(FnArgs):
        # 平台订单号
        SO_ID = FnArg(name="so_id", type_spec=TypeSpec(type="string"))
        # 内部单号
        O_ID = FnArg(name="o_id", type_spec=TypeSpec(type="string"))
        CREDENTIAL = FnArg(name="credential", type_spec=named_type_spec.Credential.JST.type_spec)
        # 物流单号
        LOGISTICS_NO = FnArg(name="logistics_no", type_spec=TypeSpec(type="string"))
        # 订单类别
        ORDER_TYPE = FnArg(name="order_type", type_spec=TypeSpec(type="array", spec=[TypeSpec(type="string")]))
        # 订单状态
        ORDER_STATUS = FnArg(name="order_status", type_spec=TypeSpec(type="array", spec=[TypeSpec(type="string")]))

    Signature = FnSignature(
        name="jst.get_out_trade_info",
        args=Args,
        rtype=TypeSpec(type="array", spec=[named_type_spec.JST.JST_TRADE_INFO.type_spec]),
    )

    @as_result(Exception)
    def call(self):
        from rpa.erp.jst import JstNewSDK
        from rpa.erp.jst import JstQmSDK

        so_id = self.get_arg_value_or_none(self.Args.SO_ID, rtype=str).unwrap() or ""
        o_id = self.get_arg_value_or_none(self.Args.O_ID, rtype=str).unwrap() or ""
        credential = self.get_arg_value(self.Args.CREDENTIAL, rtype=named_type_spec.Credential.JST.model).unwrap()
        qm_client = JstQmSDK(credential=credential)
        open_client = JstNewSDK(credential=credential)

        if so_id:
            raw_orders = self._query_so_id(so_id, qm_client, open_client)
            if logistics_no := self.get_arg_value_or_none(self.Args.LOGISTICS_NO, rtype=str).unwrap():
                raw_orders = [order for order in raw_orders if order.l_id == logistics_no]
            if order_type := self.get_arg_value_or_none(self.Args.ORDER_TYPE, rtype=list).unwrap():
                raw_orders = [order for order in raw_orders if order.order_type in order_type]
            if order_status := self.get_arg_value_or_none(self.Args.ORDER_STATUS, rtype=list).unwrap():
                raw_orders = [order for order in raw_orders if order.readable_status in order_status]
        elif o_id:
            raw_orders = self._query_o_id(o_id, qm_client, open_client)
        else:
            raise ValueError("查询条件不能为空")
        from rpa.erp.jst import JstQmSDK

        credential = self.get_arg_value(self.Args.CREDENTIAL, rtype=named_type_spec.Credential.JST.model).unwrap()
        for order in raw_orders:
            if order.wms_co_id == 0:
                order.wms_co_name = "未设定发货仓库"
            else:
                for wms_info in JstQmSDK(credential=credential).get_wms_info_by_org_id(credential.co_id, False).datas:
                    if wms_info.wms_co_id == order.wms_co_id:
                        order.wms_co_name = wms_info.name
                        break
                else:
                    order.wms_co_name = "未知分仓"

        return self.wrap_return([self._process_order(order) for order in raw_orders])

    @staticmethod
    def _process_order(raw_order: OutOrder):
        model: type[named_type_spec._model.jst.JstOutOrderInfo] = named_type_spec.JST.JST_OUT_ORDER_INFO.model
        return model(
            so_id=raw_order.so_id,
            o_id=raw_order.o_id,
            io_id=raw_order.io_id,
            status=raw_order.readable_status,
            order_type=raw_order.order_type,
            logistics_company=raw_order.logistics_company,
            l_id=raw_order.l_id,
            labels=raw_order.labels,
            f_weight=raw_order.f_weight,
            paid_amount=raw_order.paid_amount,
            items=[
                named_type_spec._model.jst.JstItem(
                    name=item.name,
                    qty=item.qty,
                    sku_id=item.sku_id,
                    i_id=item.i_id,
                    shop_sku_id=None,
                    shop_i_id=None,
                    erp_name=None,
                    erp_short_name=None,
                    properties_value=item.properties_value,
                    category=None,
                    price=None,
                    supplier_name=None,
                )
                for item in raw_order.items
            ],
            wms_co_name=raw_order.wms_co_name,
        )

    def _query_so_id(self, so_id: str, qm_client, open_client):
        from robot_processor.plugin.trade_utils import ErpTradeManager

        params = OpenOrdersOutSimpleQuery(so_ids=[so_id])
        response = ErpTradeManager.query_out_orders_jst(qm_client, open_client, params)
        return response.datas

    def _query_o_id(self, o_id: str, qm_client, open_client):
        from robot_processor.plugin.trade_utils import ErpTradeManager

        split_o_ids = [
            action.remark
            for action in open_client.order_action_query(o_id).data.datas  # type: ignore[union-attr]
            if action.name == "被拆分"
        ]
        if split_o_ids:
            params = OpenOrdersOutSimpleQuery(o_ids=split_o_ids)
        else:
            params = OpenOrdersOutSimpleQuery(o_ids=[o_id])
        response = ErpTradeManager.query_out_orders_jst(qm_client, open_client, params)
        return response.datas
