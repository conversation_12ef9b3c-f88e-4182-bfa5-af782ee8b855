from flask import Blueprint, jsonify, render_template
from result import Ok
from pydantic import BaseModel

from robot_processor.fs_type.base import FsType, FsTypeLiteral
from robot_processor.validator import validate
from robot_processor.utils import unwrap_optional

router = Blueprint("openapi-page", __name__)


@router.get("/openapi/doc/<path:file>")
def openapi_index(file):
    match file:
        case "guide-docs/register":
            return render_template("openapi/guide-docs/register.html")
        case "guide-docs/how-to-use-api":
            return render_template("openapi/guide-docs/how-to-use-api.html")
        case "guide-docs/how-to-signature":
            return render_template("openapi/guide-docs/how-to-signature.html")
        case "api-docs/fs/order/create":
            return render_template("openapi/api-docs/fs.order.create.html")
        case "api-docs/fs-type/schema":
            return render_template("openapi/api-docs/fs-type-schema.html")
        case _:
            return render_template("openapi/guide-docs/register.html")


class FsTypeJsonSchemaQuery(BaseModel):
    app_key: str
    form_id: int
    widget_key: str


@router.get("/openapi/doc/fs-type-schema.json")
@validate
def fs_type_json_schema(query: FsTypeJsonSchemaQuery):
    from robot_processor.db import db
    from robot_processor.form.models import Form, Step

    form = db.session.get(Form, query.form_id)
    if form is None:
        return jsonify(dict())
    step_id = unwrap_optional(form.versions.first()).step_id
    steps = db.session.query(Step).filter(Step.id.in_(step_id)).all()
    ui_schema = []
    for step in steps:
        ui_schema.extend(Step.Utils.raw_ui_schema(step))
    widget_info_obj = next(filter(lambda w: w["key"] == query.widget_key, ui_schema), None)
    if widget_info_obj:
        fs_type_literal = FsTypeLiteral(widget_info_obj["option_value"]["widget_type"])
        widget_literal = widget_info_obj["type"]
        match FsType.get_fs_type_by_literal(fs_type_literal, widget_literal):
            case Ok(fs_type): return jsonify(fs_type.schema())

    return jsonify(dict())
