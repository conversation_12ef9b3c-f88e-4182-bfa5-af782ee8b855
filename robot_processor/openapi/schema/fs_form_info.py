from functools import wraps
from typing import Literal
from typing import cast

from pydantic import BaseModel
from robot_types.core import Symbol
from robot_types.core import TypeSpec


class Query(BaseModel):
    form_id: int
    form_version_id: int | None
    form_version_no: str | None


class JsonSchema(BaseModel):
    type: Literal["string", "number", "boolean", "object", "array", "any"]
    title: str | None = None
    format: Literal["date-time", "date", "time"] | None = None

    @classmethod
    def from_type_spec(cls, type_spec: TypeSpec):
        jsonschema: JsonSchema | JsonSchemaArray | JsonSchemaObject
        match type_spec.type:
            case "array":
                items = JsonSchema.from_type_spec(type_spec.items) if type_spec.items is not None else None
                jsonschema = JsonSchemaArray(items=items)
            case "collection":
                properties = (
                    {
                        prop_name: JsonSchema.from_type_spec(prop_type_spec)
                        for prop_name, prop_type_spec in type_spec.properties.items()
                    }
                    if type_spec.properties is not None
                    else None
                )
                jsonschema = JsonSchemaObject(properties=properties)
            case _:
                type_ = cast(
                    Literal["string", "number", "boolean", "object", "array"],
                    {"collection": "object", "datetime": "string", "date": "string", "time": "string"}.get(
                        type_spec.type, type_spec.type
                    ),
                )
                format_ = {"datetime": "date-time", "date": "date", "time": "time"}.get(type_spec.type, None)
                jsonschema = JsonSchema(type=type_, format=format_)
        return jsonschema

    @classmethod
    def from_symbol(cls, symbol: Symbol):
        jsonschema = cls.from_type_spec(symbol.type_spec)
        jsonschema.title = symbol.label
        # 递归处理 children, from_type_spec 已经构建了完整层级的 jsonschema, 这里只是通过 symbol children 更新 title 信息
        match jsonschema:
            case JsonSchemaArray():
                if symbol.items:
                    jsonschema.items = cls.from_symbol(symbol.items)
            case JsonSchemaObject():
                if symbol.properties is not None and jsonschema.properties is not None:
                    for key in set(symbol.properties.keys()).intersection(jsonschema.properties.keys()):
                        jsonschema.properties[key] = cls.from_symbol(symbol.properties[key])

        return jsonschema

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        kwargs["exclude_none"] = True
        return super().dict(**kwargs)


class JsonSchemaObject(JsonSchema):
    type: Literal["object"] = "object"
    properties: dict[str, JsonSchema] | None


class JsonSchemaArray(JsonSchema):
    type: Literal["array"] = "array"
    items: JsonSchema | None


class FormInfo(BaseModel):
    form_id: int
    form_version_id: int
    form_version_no: str
    form_name: str
    schemas: dict[str, JsonSchemaObject]
