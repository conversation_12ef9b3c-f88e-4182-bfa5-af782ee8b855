from enum import Enum
from typing import Generic
from typing import NamedTuple
from typing import TypeVar

from pydantic import BaseModel

T = TypeVar("T")
CodeDescription = NamedTuple("CodeDescription", [("code", str), ("description", str)])


class ResponseCode(CodeDescription, Enum):
    SUCCESS = 0, "成功"
    UNKNOWN = 1, "未知错误"

    # InvalidArgument
    INVALID_ARGUMENT = 3000, "请求参数错误"
    INVALID_ARGUMENT_MISSING_REQUIRED_PARAM = 3001, "缺少必选参数"
    INVALID_ARGUMENT_MISSING_SIGNATURE = 3002, "缺少签名参数"
    INVALID_ARGUMENT_APP_KEY = 3003, "认证失败，请求使用的 app_key 不存在"
    INVALID_ARGUMENT_SIGNATURE = 3004, "无效签名"
    INVALID_ARGUMENT_METHOD = 3005, "无效方法"
    # AccessControl
    ACCESS_CONTROL = 3100, "访问控制错误"
    ACCESS_CONTROL_API_CALL_LIMITED = 3101, "触发限流"
    # BusinessOrder
    BUSINESS_ORDER = 3200, "工单操作失败"
    BUSINESS_ORDER_CREATE_FAILED = 3201, "工单创建失败"
    FETCH_RATE_FAILED = 3202, "获取评价失败"
    FETCH_VOC_ASK_LIST_FAILED = 3203, "获取问大家失败"
    BUSINESS_ORDER_NOT_FOUND = 3204, "工单不存在"
    BUSINESS_ORDER_CANNOT_OPERATE = 3205, "工单不允许操作"
    # FormInfo
    FORM_INVALID = 3301, "工单模板不存在"
    FORM_VERSION_INVALID = 3302, "工单模板版本不存在"


class ErrorDetail(BaseModel):
    loc: list[str] | None
    msg: str


class Response(Generic[T], BaseModel):
    code: str
    # 请求唯一标识
    request_id: str
    # 当 succeed 时，返回业务数据
    result: T | None
    # 当 not succeed 时，需要给到错误原因，以及错误详情
    message: str | None = None
    details: list[ErrorDetail] | None = None

    @classmethod
    def Success(cls, result: T | dict):
        return cls(code=ResponseCode.SUCCESS.code, request_id=cls.get_request_id(), result=result)

    @classmethod
    def Failed(cls, code: ResponseCode, details=None, result=None):
        return cls(
            code=code.code,
            request_id=cls.get_request_id(),
            message=code.description,
            details=details,
            result=result,
        )

    @classmethod
    def get_request_id(cls):
        from leyan_tracing import get_tracer

        if (tracer := get_tracer()) and (span := tracer.active_span):
            return "{:x}".format(span.trace_id)
        else:
            return ""
