from pydantic import BaseModel
from pydantic import Field


class Request(BaseModel):
    business_order_id: int
    ignore_deleted: bool = Field(default=True)


class BusinessOrderView(BaseModel):
    id: int
    form_id: int
    form_version_id: int
    form_version_no: str
    shop_sid: str
    shop_platform: str
    shop_title: str
    shop_nick: str
    status: str
    deleted: bool
    created_at: str
    updated_at: str
    data: dict


class Response(BaseModel):
    data: BusinessOrderView
