from enum import StrEnum
from typing import Any
from typing import Callable

from flask import Response as FlaskResponse

from ._common import ErrorDetail
from ._common import Response
from ._common import ResponseCode

# fmt: off
__all__ = [
    "Response", "ResponseCode", "ErrorDetail",
    "Method"
]
# fmt: on


class Method(StrEnum):
    fs_order_create = "fs.order.create"  # "创建工单"
    fs_tmall_rate = "fs.tmall.rate"  # "获取天猫评价"
    fs_panel_data = "fs.panel.data"  # "获取panel数据"
    fs_voc_ask_list = "fs.voc.ask.list"  # "获取问大家列表"
    fs_form_info = "fs.form.info"  # 工单模板详情
    fs_order_list = "fs.order.list"  # 工单列表
    fs_order_info = "fs.order.info"  # 工单详情
    fs_order_update = "fs.order.update"  # 更新工单

    def __init__(self, _):
        self._bind_func_name = None

    def register(self, func: Callable[[Any], FlaskResponse]):
        self._bind_func_name = func.__name__
        return func

    def dispatch(self, executor) -> FlaskResponse:
        return getattr(executor, self._bind_func_name)()  # type: ignore
