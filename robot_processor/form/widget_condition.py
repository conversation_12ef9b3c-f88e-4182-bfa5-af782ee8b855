import enum
import operator as builtin_operator
from collections.abc import Callable
from datetime import date
from datetime import datetime
from datetime import time
from typing import Any
from typing import cast

import arrow
from beartype import beartype
from loguru import logger
from more_itertools import first

from robot_processor.form.errors import FormError
from robot_processor.utils import readable_enum


def check_if_satisfy_condition(value_type: "ValueType", operator: "Operator", src, target):
    readable_value_type = readable_enum(value_type)
    readable_operator = readable_enum(operator)

    try:
        op_fn = ValueType.operator_function(value_type, operator)
    except KeyError as err:
        raise FormError(f"未找到 {readable_value_type} {readable_operator} 操作符信息 {err=}")
    log_repr = f"{op_fn.__name__}({src=}, {target=})"

    # 左值为 None 的情况一定是不满足条件的
    if src is None and operator not in [
        Operator.EXIST,
        Operator.NOT_EXIST,
        Operator.IS_EMPTY,
        Operator.IS_NOT_EMPTY,
    ]:
        log_repr = f"{op_fn.__name__}({src=}, {target=}): False"
        logger.info(f"condition exec: {log_repr}")
        return False

    try:
        src = _format_src_or_target(value_type, src)
        target = _format_src_or_target(value_type, target)
        result = op_fn(src, target)
        log_repr = f"{op_fn.__name__}({src=}, {target=}): {result}"
    except Exception as err:
        raise FormError(f"条件计算出现未知异常 {log_repr} {err=}")
    logger.info(f"condition exec: {log_repr}")
    return result


def _format_src_or_target(value_type: "ValueType", src_or_target):
    # 单选和多选要统一处理成多选的数据结构
    if (
        value_type == ValueType.ENUM
        and isinstance(src_or_target, list)
        and not isinstance(first(src_or_target, []), list)
    ):
        src_or_target = [src_or_target]

    if isinstance(src_or_target, list):
        return [ValueType.value_format(value_type, item) for item in src_or_target]
    else:
        return ValueType.value_format(value_type, src_or_target)


class Operator(enum.Enum):
    EQ = enum.auto()
    NE = enum.auto()
    IN = enum.auto()
    CONTAINS_ANY = enum.auto()
    CONTAINS_ALL = enum.auto()
    NOT_CONTAINS_ANY = enum.auto()
    DISJOINT = enum.auto()
    LT = enum.auto()
    LTE = enum.auto()
    GT = enum.auto()
    GTE = enum.auto()
    BETWEEN = enum.auto()
    EXIST = enum.auto()
    NOT_EXIST = enum.auto()
    DATETIME_EQ = enum.auto()
    DATETIME_BEFORE = enum.auto()
    DATETIME_AFTER = enum.auto()
    DATETIME_BETWEEN = enum.auto()
    DATE_EQ = enum.auto()
    DATE_BEFORE = enum.auto()
    DATE_AFTER = enum.auto()
    DATE_BETWEEN = enum.auto()
    TIME_EQ = enum.auto()
    TIME_BEFORE = enum.auto()
    TIME_AFTER = enum.auto()
    TIME_BETWEEN = enum.auto()
    PROVINCE_EQ = enum.auto()
    PROVINCE_IN = enum.auto()
    MATCH_ANY = enum.auto()
    NOT_MATCH_ANY = enum.auto()
    MATCH_ALL = enum.auto()
    EXACTLY_MATCH_ANY = enum.auto()
    IS_EMPTY = enum.auto()
    IS_NOT_EMPTY = enum.auto()
    IS_TRUE = enum.auto()
    IS_FALSE = enum.auto()
    ANY_IS_TRUE = enum.auto()
    ANY_IS_FALSE = enum.auto()
    ALL_IS_TRUE = enum.auto()
    ALL_IS_FALSE = enum.auto()

    @classmethod
    def valid_operator(cls):
        return [k for k, v in Operator.__members__.items()]


class ValueType(enum.Enum):
    STRING = enum.auto()
    NUMBER = enum.auto()
    ENUM = enum.auto()
    DATETIME = enum.auto()
    DATE = enum.auto()
    TIME = enum.auto()
    OBJECT = enum.auto()  # 未改造的电商组件
    TABLE = enum.auto()
    BOOLEAN = enum.auto()
    ARRAY = enum.auto()
    COLLECTION = enum.auto()

    @classmethod
    def operator_function(cls, value_type: "ValueType", operator_: Operator):
        Mapper = dict[ValueType, dict[Operator, Callable[[Any, Any], bool]]]
        return cast(
            Mapper,
            {
                ValueType.STRING: {
                    Operator.EQ: string_eq,
                    Operator.IN: string_in,
                    Operator.MATCH_ANY: string_match_any,
                    Operator.NOT_MATCH_ANY: string_not_match_any,
                    Operator.MATCH_ALL: string_match_all,
                    Operator.NOT_CONTAINS_ANY: string_not_contains_any,
                    Operator.EXACTLY_MATCH_ANY: string_exactly_match_any,
                    Operator.CONTAINS_ANY: table_string_contains_any,
                    Operator.CONTAINS_ALL: table_string_contains_all,
                    Operator.EXIST: op_exists,
                    Operator.NOT_EXIST: op_not_exists,
                },
                ValueType.NUMBER: {
                    Operator.EQ: number_eq,
                    Operator.NE: number_ne,
                    Operator.GT: number_gt,
                    Operator.GTE: number_gte,
                    Operator.LT: number_lt,
                    Operator.LTE: number_lte,
                    Operator.BETWEEN: number_between,
                    Operator.EXIST: op_exists,
                    Operator.NOT_EXIST: op_not_exists,
                },
                ValueType.ENUM: {
                    Operator.IN: enum_contains_any,  # 新版组件不再支持的操作符
                    Operator.CONTAINS_ANY: enum_contains_any,
                    Operator.CONTAINS_ALL: enum_contains_all,
                    Operator.DISJOINT: enum_disjoint,
                    Operator.EXIST: enum_exists,
                    Operator.NOT_EXIST: enum_not_exists,
                },
                ValueType.DATETIME: {
                    Operator.DATE_EQ: datetime_date_eq,
                    Operator.DATE_BEFORE: datetime_date_before,
                    Operator.DATE_AFTER: datetime_date_after,
                    Operator.DATE_BETWEEN: datetime_date_between,
                    Operator.DATETIME_BEFORE: datetime_before,
                    Operator.DATETIME_AFTER: datetime_after,
                    Operator.DATETIME_BETWEEN: datetime_between,
                    Operator.TIME_EQ: datetime_time_eq,
                    Operator.TIME_BEFORE: datetime_time_before,
                    Operator.TIME_AFTER: datetime_time_after,
                    Operator.TIME_BETWEEN: datetime_time_between,
                    Operator.EXIST: op_exists,
                    Operator.NOT_EXIST: op_not_exists,
                },
                ValueType.DATE: {
                    Operator.DATE_EQ: date_eq,
                    Operator.DATE_BEFORE: date_before,
                    Operator.DATE_AFTER: date_after,
                    Operator.DATE_BETWEEN: date_between,
                    Operator.EXIST: op_exists,
                    Operator.NOT_EXIST: op_not_exists,
                },
                ValueType.TIME: {
                    Operator.TIME_EQ: time_eq,
                    Operator.TIME_BEFORE: time_before,
                    Operator.TIME_AFTER: time_after,
                    Operator.TIME_BETWEEN: time_between,
                    Operator.EXIST: op_exists,
                    Operator.NOT_EXIST: op_not_exists,
                },
                # FIXME 需要尽快改造，现在只是正好几个组件的操作符互不冲突
                ValueType.OBJECT: {
                    Operator.PROVINCE_EQ: address_province_eq,
                    Operator.PROVINCE_IN: address_province_in,
                    Operator.IN: product_in,
                    Operator.CONTAINS_ANY: order_question_any,
                    Operator.CONTAINS_ALL: order_question_all,
                    Operator.DISJOINT: order_question_disjoint,
                    Operator.EXIST: object_exists,
                    Operator.NOT_EXIST: object_not_exists,
                },
                ValueType.TABLE: {
                    Operator.IS_EMPTY: table_is_empty,
                    Operator.IS_NOT_EMPTY: table_is_not_empty,
                },
                ValueType.BOOLEAN: {
                    Operator.NOT_EXIST: op_not_exists,
                    Operator.EXIST: op_exists,
                    Operator.IS_TRUE: boolean_is_true,
                    Operator.IS_FALSE: boolean_is_false,
                    Operator.ANY_IS_TRUE: boolean_any_is_true,
                    Operator.ANY_IS_FALSE: boolean_any_is_false,
                    Operator.ALL_IS_TRUE: boolean_all_is_true,
                    Operator.ALL_IS_FALSE: boolean_all_is_false,
                },
                ValueType.ARRAY: {
                    Operator.IS_EMPTY: table_is_empty,
                    Operator.IS_NOT_EMPTY: table_is_not_empty,
                },
                ValueType.COLLECTION: {
                    Operator.EXIST: object_exists,
                    Operator.NOT_EXIST: object_not_exists,
                },
            },
        )[value_type][operator_]

    @classmethod
    def value_format(cls, value_type: "ValueType", src_or_target):
        if src_or_target is None:
            return src_or_target
        if value_type == ValueType.NUMBER:
            return round(float(src_or_target), 2)
        if value_type == ValueType.DATETIME:
            return cls._parse_arrow(src_or_target).datetime
        if value_type == ValueType.DATE:
            return cls._parse_arrow(src_or_target).date()
        if value_type == ValueType.TIME:
            return cls._parse_arrow(src_or_target).time()
        if value_type == ValueType.ENUM:
            assert isinstance(src_or_target, list), f"期望类型不匹配 {type(src_or_target)}"
            return cast(
                list[str],
                [item.get("value") if isinstance(item, dict) else item for item in src_or_target],
            )

        return src_or_target

    @classmethod
    def _parse_arrow(cls, datetime_str):
        return arrow.get(datetime_str, ["YYYY-MM-DD HH:mm:ss", "YYYY-MM-DD", "H:m:s"])


def op_exists(src, target=None):
    if src is None:
        return False
    if isinstance(src, str) and src == "":
        return False
    return True


def op_not_exists(src, target=None):
    return not op_exists(src, target)


def object_exists(src, target=None):
    if src is None:
        return False
    elif isinstance(src, (list, dict)) and len(src) == 0:
        return False
    else:
        return True


def object_not_exists(src, target=None):
    return not object_exists(src, target)


@beartype
def string_eq(src: str, target: str) -> bool:
    """等于

    Examples:
        >>> string_eq('AB', 'AB') is True
    """
    return builtin_operator.eq(src, target)


@beartype
def string_in(src: str | list[str], target: list[str]) -> bool:
    """等于任一

    Examples:
        >>> string_in('AB', ['AB', 'BC']) is True
        >>> string_in('AB', ['AC', 'BD']) is False
    """
    if isinstance(src, str):
        return builtin_operator.contains(target, src)
    return any(string_in(item, target) for item in src)


@beartype
def string_match_any(src: str, target: list[str]) -> bool:
    """包含任一

    Examples:
        >>> string_match_any('ABC', ['A', 'D']) is True  # match 'A' in 'ABC'
        >>> string_match_any('ABC', ['D', 'E']) is False
    """
    return any(builtin_operator.contains(src, match) for match in target)


@beartype
def string_not_match_any(src: str, target: list[str]) -> bool:
    """不包含任一

    Examples:
        >>> string_not_match_any('ABC', ['A', 'D']) is False  # match 'A' in 'ABC'
        >>> string_not_match_any('ABC', ['D', 'E']) is True
    """
    return not string_match_any(src, target)


@beartype
def string_match_all(src: str, target: list[str]) -> bool:
    """包含所有

    Examples:
        >>> string_match_all('ABC', ['A', 'B']) is True
        >>> string_match_all('ABC', ['A', 'D']) is False
    """
    return all(builtin_operator.contains(src, match) for match in target)


@beartype
def string_not_contains_any(src: str, target: list[str]):
    """不等于任一"""
    return all(src != match for match in target)


@beartype
def string_exactly_match_any(src: str, target: list[str]) -> bool:
    """等于任一

    Examples:
        >>> string_match_any('ABC', ['ABC', 'DEF']) is True
        >>> string_match_any('ABC', ['AB']) is False
    """
    return any(builtin_operator.eq(src, match) for match in target)


@beartype
def table_string_contains_any(src: list[str], target: list[str]) -> bool:
    """包含任一

    Examples:
        >>> table_string_contains_any(['ABC', 'DEF'], ['ABC', 'BCD']) is True
        >>> table_string_contains_any(['ABC', 'DEF'], ['BCD', 'EFG']) is False
        >>> table_string_contains_any(['ABC', 'DEF'], ['A']) is True
    """
    return any(builtin_operator.contains(src_item, target_item) for src_item in src for target_item in target)


@beartype
def table_string_contains_all(src: list[str], target: list[str]) -> bool:
    """包含全部

    Examples:
        >>> table_string_contains_all(['ABC', 'DEF'], ['ABC', 'DEF']) is True
        >>> table_string_contains_all(['ABC', 'DEF'], ['ABC', 'BCD']) is False
        >>> table_string_contains_all(['ABC', 'DEF'], ['A', 'E']) is True
    """
    return all(any(builtin_operator.contains(src_item, target_item) for src_item in src) for target_item in target)


NumberT = int | float


@beartype
def number_eq(src: NumberT, target: NumberT) -> bool:
    return builtin_operator.eq(src, target)


@beartype
def number_ne(src: NumberT, target: NumberT) -> bool:
    return not number_eq(src, target)


@beartype
def number_gt(src: NumberT, target: NumberT) -> bool:
    return builtin_operator.gt(src, target)


@beartype
def number_gte(src: NumberT, target: NumberT) -> bool:
    return builtin_operator.ge(src, target)


@beartype
def number_lt(src: NumberT, target: NumberT) -> bool:
    return builtin_operator.lt(src, target)


@beartype
def number_lte(src: NumberT, target: NumberT) -> bool:
    return builtin_operator.le(src, target)


@beartype
def number_between(src: NumberT, target: list[NumberT]) -> bool:
    return number_gte(src, target[0]) and number_lte(src, target[1])


# 单选/多选组件的数据类型
SelectOption = list[list[str]]


@beartype
def enum_contains_any(src: SelectOption, target: SelectOption) -> bool:
    """包含任一

    Examples:
        # 单选
        >>> enum_contains_any([['1', '1']], [['1', '1'], ['3', '2']]) is True
        >>> enum_contains_any([['1', '2']], [['1', '1'], ['3', '2']]) is False
        # 多选
        >>> enum_contains_any([['1', '1'], ['1', '2']], [['1', '1'], ['3', '2']]) is True
        >>> enum_contains_any([['1', '2'], ['1', '3']], [['1', '1'], ['3', '2']]) is True
    """
    return any(builtin_operator.eq(src_item, target_item) for src_item in src for target_item in target)


@beartype
def enum_contains_all(src: SelectOption, target: SelectOption) -> bool:
    """包含全部

    Examples:
        # 单选
        >>> enum_contains_all([['1', '1']], [['1', '1'], ['3', '2']]) is False
        >>> enum_contains_all([['1', '2']], [['1', '1'], ['3', '2']]) is False
        >>> enum_contains_all([['1', '1']], [['1', '1']]) is True
        # 多选
        >>> enum_contains_all([['1', '1'], ['1', '2']], [['1', '1'], ['1', '2']]) is True
        >>> enum_contains_all([['1', '2'], ['1', '3']], [['1', '2'], ['1', '1']]) is False
    """
    return all(builtin_operator.contains(src, target_item) for target_item in target)


@beartype
def enum_disjoint(src: SelectOption, target: SelectOption):
    """不包含任一"""
    return all(builtin_operator.ne(src_item, target_item) for src_item in src for target_item in target)


@beartype
def enum_exists(src, target=None):
    """
    不为空
    前端表示目前 enum 类型的数据，可能会返回 [] 这么一个空列表过来。
    """
    if isinstance(src, list):
        return len(src) > 0
    else:
        return src is not None


@beartype
def enum_not_exists(src, target=None):
    """
    为空
    """
    return not enum_exists(src, target)


@beartype
def datetime_date_eq(src: datetime, target: datetime) -> bool:
    return builtin_operator.eq(src.date(), target.date())


@beartype
def datetime_date_before(src: datetime, target: datetime) -> bool:
    return builtin_operator.lt(src.date(), target.date())


@beartype
def datetime_date_after(src: datetime, target: datetime) -> bool:
    return builtin_operator.gt(src.date(), target.date())


@beartype
def datetime_date_between(src: datetime, target: list[datetime]) -> bool:
    src_date, lower_date, upper_date = src.date(), target[0].date(), target[1].date()
    return builtin_operator.ge(src_date, lower_date) and builtin_operator.le(src_date, upper_date)


@beartype
def datetime_before(src: datetime, target: datetime) -> bool:
    return builtin_operator.lt(src, target)


@beartype
def datetime_after(src: datetime, target: datetime) -> bool:
    return builtin_operator.gt(src, target)


@beartype
def datetime_between(src: datetime, target: list[datetime]) -> bool:
    return builtin_operator.ge(src, target[0]) and builtin_operator.le(src, target[1])


@beartype
def datetime_time_eq(src: datetime, target: datetime) -> bool:
    return builtin_operator.eq(src.time(), target.time())


@beartype
def datetime_time_before(src: datetime, target: datetime) -> bool:
    return builtin_operator.lt(src.time(), target.time())


@beartype
def datetime_time_after(src: datetime, target: datetime) -> bool:
    return builtin_operator.gt(src.time(), target.time())


@beartype
def datetime_time_between(src: datetime, target: list[datetime]) -> bool:
    src_time, lower_time, upper_time = src.time(), target[0].time(), target[1].time()
    return builtin_operator.ge(src_time, lower_time) and builtin_operator.le(src_time, upper_time)


@beartype
def date_eq(src: date, target: date) -> bool:
    return builtin_operator.eq(src, target)


@beartype
def date_before(src: date, target: date) -> bool:
    return builtin_operator.lt(src, target)


@beartype
def date_after(src: date, target: date) -> bool:
    return builtin_operator.gt(src, target)


@beartype
def date_between(src: date, target: list[date]) -> bool:
    return builtin_operator.ge(src, target[0]) and builtin_operator.le(src, target[1])


@beartype
def time_eq(src: time, target: time) -> bool:
    return builtin_operator.eq(src, target)


@beartype
def time_before(src: time, target: time) -> bool:
    return builtin_operator.lt(src, target)


@beartype
def time_after(src: time, target: time) -> bool:
    return builtin_operator.gt(src, target)


@beartype
def time_between(src: time, target: list[time]) -> bool:
    return builtin_operator.ge(src, target[0]) and builtin_operator.le(src, target[1])


def order_question_any(src, target):
    src_list = [s[1].get("value") if isinstance(s[1], dict) else s[1] for s in src if len(s) == 2]
    tar_list = [t[1] for t in target if len(t) == 2]
    return any(x in src_list for x in tar_list)


def order_question_all(src, target):
    src_list = [s[1].get("value") if isinstance(s[1], dict) else s[1] for s in src if len(s) == 2]
    tar_list = [t[1] for t in target if len(t) == 2]
    return all(x in src_list for x in tar_list)


def op_disjoint(src, target):
    for t in target:
        if t in src:
            return False
    return True


def order_question_disjoint(src, target):
    src_list = [s[1].get("value") if isinstance(s[1], dict) else s[1] for s in src if len(s) == 2]
    tar_list = [t[1] for t in target if len(t) == 2]
    return op_disjoint(src_list, tar_list)


def upload_exist(src, target):
    return src is not None and len(src) > 0


def upload_not_exist(src, target):
    return not upload_exist(src, target)


def address_province_eq(src, target):
    if not isinstance(src, dict):
        return False
    state = src.get("state")
    return (state is not None) and (state == target)


def address_province_in(src, target):
    if not isinstance(src, dict):
        return False
    state = src.get("state")
    return (state is not None) and (state in target)


def product_in(src, target):
    """
    商品是否等于任一
    第一版target为手动填，这里判断spu+sku
    """
    if isinstance(src, dict):
        src = [src]
    ids = set()
    for item in src:
        spu = item.get("spu")
        sku = item.get("sku")
        if spu:
            ids.add(spu)
        if sku:
            ids.add(sku)

    return not ids.isdisjoint(target)


def table_is_empty(src, target=None):
    if src is None:
        return True
    elif len(src) == 0:
        return True
    else:
        return False


def table_is_not_empty(src, target=None):
    if src is None:
        return False
    elif len(src) > 0:
        return True
    else:
        return False


def boolean_is_true(src, _=None):
    return src is True


def boolean_is_false(src, _=None):
    return not boolean_is_true(src, _)


def boolean_any_is_true(src, _=None):
    return src and isinstance(src, list) and any(item is True for item in src)


def boolean_any_is_false(src, _=None):
    return src and isinstance(src, list) and any(item is False for item in src)


def boolean_all_is_true(src, _=None):
    return src and isinstance(src, list) and all(item is True for item in src)


def boolean_all_is_false(src, _=None):
    return src and isinstance(src, list) and all(item is False for item in src)
