from pydantic import BaseModel


class DataMapper(BaseModel):
    name: str
    label: str | None
    children: list["DataMapper"] | None = None

    def is_item(self):
        return ("_item_" in self.name.lower()) or (self.name.endswith("_ITEM"))

    def has_item(self):
        return self.children and len(self.children) == 1 and self.children[0].is_item()

    def get_item_mapper(self):
        return self.children[0] if self.children else None


def to_label_map(raw: dict, mappers: list[DataMapper]):

    def to_value(raw_value, data_mapper):
        if isinstance(raw_value, list) and data_mapper.has_item():
            return [to_value(item, data_mapper.get_item_mapper()) for item in raw_value]
        elif isinstance(raw_value, list) and data_mapper.children:
            return [to_value(item, data_mapper) for item in raw_value]
        elif isinstance(raw_value, dict) and data_mapper.children:
            return to_label_map(raw_value, data_mapper.children)
        else:
            return raw_value

    fields = {mapper.name: mapper for mapper in mappers}
    label_map = dict()
    for field in raw:
        if field not in fields:
            continue
        field_mapper = fields[field]
        label = field_mapper.label
        label_map[label] = to_value(raw[field], field_mapper)

    return label_map


def to_field_map(raw: dict, mappers: list[DataMapper]):

    def to_value(raw_value, data_mapper):
        if isinstance(raw_value, list) and data_mapper.has_item():
            return [to_value(item, data_mapper.get_item_mapper()) for item in raw_value]
        elif isinstance(raw_value, list) and data_mapper.children:
            return [to_value(item, data_mapper) for item in raw_value]
        elif isinstance(raw_value, dict) and data_mapper.children:
            return to_field_map(raw_value, data_mapper.children)
        else:
            return raw_value

    fields = {mapper.label: mapper for mapper in mappers}
    field_map = dict()
    for label in raw:
        if label not in fields:
            continue
        field_mapper = fields[label]
        field_map[field_mapper.name] = to_value(raw[label], field_mapper)
    return field_map


DataMapper.update_forward_refs()
