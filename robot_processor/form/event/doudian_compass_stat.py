"""抖店电商罗盘数据"""

import json
from datetime import datetime
from datetime import timedelta
from decimal import Decimal
from functools import wraps
from typing import TYPE_CHECKING

from loguru import logger
from pydantic import BaseModel
from tcron_jobs import runner

from robot_processor.ext import db
from robot_processor.logging import vars as log_vars
from robot_processor.t_cron import wrap_tcron_job
from rpa.conf import rpa_config

if TYPE_CHECKING:
    from robot_processor.form.event.models import ScheduleContext


# @make_fields_optional
class CompassStat(BaseModel):
    # 时间
    stat_time: str
    # 成交金额
    pay_amt: Decimal
    # 成交金额-较上期
    pay_amt_out_period_ratio: Decimal
    # 上期成交金额
    pay_amt_last_value: Decimal
    # 成交金额-同行基准
    pay_amt_benchmark: Decimal
    # 成交金额-首购
    pay_amt_new_usr_pay_amt: Decimal
    # 成交金额-复购
    pay_amt_old_usr_pay_amt: Decimal

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        # 处理 Decimal 类型
        return json.loads(super().json(**kwargs))


def get_scheduled_job_args(kwargs: "ScheduleContext"):
    from robot_processor.client import rpa_client
    from robot_processor.utils import unwrap_optional

    template_name = "抖店店铺数据获取"
    shop_id = kwargs["shop"].id
    org_id = int(unwrap_optional(kwargs["shop"].org_id))
    workflow_id = rpa_client.get_workflow_id(template_name, org_id).data.workflow_id
    return dict(shop_id=shop_id, workflow_id=workflow_id)


@runner.register
@wrap_tcron_job
def poll_doudian_compass_stat(shop_id: int, workflow_id: int) -> str:
    from robot_processor.business_order.business_order_manager import BusinessManager
    from robot_processor.client import rpa_client
    from robot_processor.form.event.models import EventConfig
    from robot_processor.form.event.models import EventType
    from robot_processor.form.event.models import FormEventTCronSchedule
    from robot_processor.shop.models import Shop

    event_type = EventType.DOUDIAN_COMPASS_STAT
    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return "店铺已失效"
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(event_type)

    ack_result = rpa_client.execute_workflow_wait_ack(
        workflow_id,
        None,
        dict(pid=shop.sid, method="get-compass-stat"),
        timeout=rpa_config.pdd_shop_metrics_wait_act_timeout,
    )
    if ack_result.is_err():
        return str(ack_result)
    ack = ack_result.unwrap()
    event = EventConfig.get_by_id(event_type)
    FormEventTCronSchedule.get_by_shop_event(shop, event_type).mark_executed()
    if not ack.success:
        return ack.json()
    output = ack.output[0]["object"]
    if not output["success"]:
        return str(output)
    yesterday = datetime.now().date() - timedelta(days=1)
    stat = CompassStat(
        stat_time=yesterday.strftime("%Y-%m-%d"),
        pay_amt=output["result"]["pay_amt"]["value"]["value"],
        pay_amt_out_period_ratio=output["result"]["pay_amt"]["out_period_ratio"]["value"],
        pay_amt_last_value=output["result"]["pay_amt"]["last_value"]["value"],
        pay_amt_benchmark=output["result"]["pay_amt"]["benchmark"]["value"],
        pay_amt_new_usr_pay_amt=output["result"]["pay_amt"]["new_usr_pay_amt"]["value"],
        pay_amt_old_usr_pay_amt=output["result"]["pay_amt"]["old_usr_pay_amt"]["value"],
    )
    create_result = BusinessManager.create_bo_by_event(event, shop, stat.dict())
    logger.info(f"create bo result: {create_result}")
    return ""
