"""RPA 执行结果创建工单"""
from pydantic import BaseModel, Field

from robot_processor.utils import make_fields_optional


@make_fields_optional
class TaobaoRefund(BaseModel):
    """淘宝退货退款"""

    branch_policy: str = Field(title="分支策略")
    order_id: str = Field(title="订单编号")
    delivery_time: str = Field(title="发货时间")
    order_status: str = Field(title="订单状态")
    delivery_status: str = Field(title="发货状态")
    after_sale_status: str = Field(title="售后状态")
    goods_count: int = Field(title="商品数量")
    order_total_price: float = Field(title="订单总价")
    user_pay: float = Field(title="用户实付")
    delivery_logistics_company: str = Field(title="发货物流公司")
    order_logistics: str = Field(title="订单物流")
    delivery_logistics_sign_city: str = Field(title="发货物流签收市")
    order_remark: str = Field(title="订单备注")
    order_mark: str = Field(title="订单标记")
    after_sale_id: str = Field(title="售后编号")
    refund_explain: str = Field(title="退货说明")
    apply_time: str = Field(title="申请时间")
    goods_title: str = Field(title="商品标题")
    merchant_code: str = Field(title="商家编码")
    goods_sku_id: str = Field(title="商品 sku id")
    goods_spec: str = Field(title="商品规格")
    apply_count: int = Field(title="申请件数")
    refund_amount: float = Field(title="退款金额")
    refund_logistics_company: str = Field(title="退货物流公司")
    refund_logistics_number: str = Field(title="退货物流单号")
    refund_logistics: str = Field(title="退货物流")
    refund_logistics_number_reused: bool = Field(title="退货物流单号多次使用")
    confirm_receipt_agree_refund: bool = Field(title="确认收货,同意退款是否存在")
    send_express_group_data: str = Field(title="发快递群数据")
    agree_return: bool = Field(title="同意退货是否存在")
    refuse_return: bool = Field(title="拒绝退货申请是否存在")
    phone: str = Field(title="手机号")
    local_execution_record_id: int = Field(title="本地执行记录ID")
    apply_customer_service_intervention: bool = Field(title="申请客服介入是否存在")
    contact_address: str = Field(title="联系地址")
    execution_record_status: str = Field(title="执行记录状态")
    suggest_manual_follow_up: str = Field(title="建议人工下一步跟进")
    refund_actions: str = Field(title="退款动作")


@make_fields_optional
class TaobaoRefundWithoutReturn(BaseModel):
    """淘宝仅退款"""

    branch_policy: str = Field(title="分支策略")
    order_id: str = Field(title="订单编号")
    delivery_status: str = Field(title="发货状态")
    order_amount: float = Field(title="订单金额")
    flag_color: str = Field(title="插旗颜色")
    final_order_remark: str = Field(title="订单备注(最终值)")
    express_number: str = Field(title="快递单号")
    delivery_logistics_company: str = Field(title="发货物流公司")
    logistics_update: str = Field(title="物流更新至今")
    logistics_update_count: int = Field(title="物流更新条数")
    history_logistics: str = Field(title="历史物流轨迹")
    latest_logistics: str = Field(title="最新物流轨迹")
    after_sale_id: str = Field(title="售后编号")
    refund_reason: str = Field(title="退款原因")
    refund_amount: float = Field(title="退款金额")
    refund_action: str = Field(title="退款动作")
    reject_reason: str = Field(title="驳回原因")
    send_express_group_data: str = Field(title="发快递群数据")
    express_group_data: str = Field(title="发快递群自定义话术")
    process_time: str = Field(title="处理时间")
    suggest_manual_follow_up: str = Field(title="建议人工下一步跟进")
    execution_record_status: str = Field(title="执行记录状态")
    local_execution_record_id: int = Field(title="本地执行记录ID")
    check_content: str = Field(title="查件内容")
    process_result: str = Field(title="处理结果")
    fail_reason: str = Field(title="失败原因")


@make_fields_optional
class PddRefund(BaseModel):
    """拼多多退货退款"""

    branch_policy: str = Field(title="分支策略")
    order_id: str = Field(title="订单编号")
    delivery_status: str = Field(title="发货状态")
    order_amount: float = Field(title="订单金额")
    flag_color: str = Field(title="插旗颜色")
    final_order_remark: str = Field(title="订单备注(最终值)")
    express_number: str = Field(title="快递单号")
    delivery_logistics_company: str = Field(title="发货物流公司")
    logistics_update: str = Field(title="物流更新至今")
    logistics_update_count: int = Field(title="物流更新条数")
    history_logistics: str = Field(title="历史物流轨迹")
    latest_logistics: str = Field(title="最新物流轨迹")
    after_sale_id: str = Field(title="售后编号")
    refund_reason: str = Field(title="退款原因")
    refund_amount: float = Field(title="退款金额")
    refund_logistics_number: str = Field(title="退货物流单号")
    refund_logistics_company: str = Field(title="退货物流公司")
    refund_history_logistics: str = Field(title="退货历史物流轨迹")
    refund_action: str = Field(title="退款动作")
    reject_reason: str = Field(title="驳回原因")
    send_express_group_data: str = Field(title="发快递群数据")
    express_group_data: str = Field(title="发快递群自定义话术")
    process_time: str = Field(title="处理时间")
    suggest_manual_follow_up: str = Field(title="建议人工下一步跟进")
    execution_record_status: str = Field(title="执行记录状态")
    local_execution_record_id: int = Field(title="本地执行记录ID")
    check_content: str = Field(title="查件内容")
    process_result: str = Field(title="处理结果")
    fail_reason: str = Field(title="失败原因")


@make_fields_optional
class PddRefundWithoutReturn(BaseModel):
    """拼多多仅退款"""

    branch_policy: str = Field(title="分支策略")
    order_id: str = Field(title="订单编号")
    delivery_status: str = Field(title="发货状态")
    order_amount: float = Field(title="订单金额")
    flag_color: str = Field(title="插旗颜色")
    final_order_remark: str = Field(title="订单备注(最终值)")
    delivery_logistics_company: str = Field(title="发货物流公司")
    express_number: str = Field(title="快递单号")
    history_logistics: str = Field(title="历史物流轨迹")
    latest_logistics: str = Field(title="最新物流轨迹")
    logistics_update: str = Field(title="物流更新至今")
    multi_logistics: bool = Field(title="是否存在多段物流")
    first_logistics_company: str = Field(title="一段发货物流公司")
    first_logistics_number: str = Field(title="一段发货物流单号")
    first_logistics: str = Field(title="一段发货物流轨迹")
    second_logistics_company: str = Field(title="二段发货物流公司")
    second_logistics_number: str = Field(title="二段发货物流单号")
    second_logistics: str = Field(title="二段发货物流轨迹")
    group_time: str = Field(title="成团时间")
    after_sale_id: str = Field(title="售后编号")
    refund_amount: float = Field(title="退款金额")
    refund_reason: str = Field(title="退款原因")
    match_strategy: str = Field(title="匹配策略步骤")
    flag_remark: str = Field(title="插旗备注步骤")
    execution_record_status: str = Field(title="执行记录状态")
    fail_reason: str = Field(title="失败原因")
    suggest_manual_follow_up: str = Field(title="建议人工下一步跟进")
    reject_reason: str = Field(title="驳回原因")
    process_result: str = Field(title="处理结果")
    send_message: str = Field(title="发消息步骤")
    express_group_data: str = Field(title="发快递群自定义话术")
    local_execution_record_id: int = Field(title="本地执行记录ID")
    process_time: str = Field(title="处理时间")
    send_express_group_data: str = Field(title="发快递群数据")
    check_content: str = Field(title="查件内容")
    logistics_update_count: int = Field(title="物流更新条数")
    sync_online_table: str = Field(title="同步在线表格步骤")
    refund_action: str = Field(title="退款动作")
    refund_step: str = Field(title="退款步骤")


@make_fields_optional
class DoudianRefund(BaseModel):
    """抖音退货退款"""

    branch_policy: str = Field(title="分支策略")
    order_id: str = Field(title="订单编号")
    user_pay: float = Field(title="用户实付")
    delivery_status: str = Field(title="发货状态")
    order_remark: str = Field(title="订单备注")
    final_order_remark: str = Field(title="订单备注(最终值)")
    order_time: str = Field(title="下单时间")
    delivery_time: str = Field(title="发货时间")
    goods_count: int = Field(title="商品数量")
    express_number: str = Field(title="发货物流单号")
    delivery_log: str = Field(title="发货物流公司")
    delivery_logistics: str = Field(title="订单物流")
    delivery_sign_city: str = Field(title="发货物流签收市")
    after_sale_id: str = Field(title="售后编号")
    apply_time: str = Field(title="申请时间")
    reason: str = Field(title="售后原因")
    status: str = Field(title="售后状态")
    apply_amount: float = Field(title="申请金额")
    apply_count: float = Field(title="申请件数")
    buyer_send_back_count: float = Field(title="买家需寄回数量")
    goods_title: str = Field(title="商品标题")
    goods_spec: str = Field(title="商品规格")
    merchant_code: str = Field(title="商家编码")
    erp_supplier: str = Field(title="ERP供销商")
    erp_warehouse: str = Field(title="ERP发货仓")
    erp_logistics: str = Field(title="ERP快递公司")
    order_mark: str = Field(title="订单标记")
    refund_logistics_number: str = Field(title="退货物流单号")
    refund_logistics_company: str = Field(title="退货物流公司")
    refund_logistics: str = Field(title="退货物流")
    after_sale_label: str = Field(title="售后标签")
    refund_logistics_city: str = Field(title="退货物流发货市")
    refund_logistics_reused: bool = Field(title="退货物流单号多次使用")
    after_sale_remark: str = Field(title="售后备注")
    final_after_sale_remark: str = Field(title="售后备注(最终值)")
    after_sale_description: str = Field(title="售后说明")
    address: str = Field(title="收货地址")
    receiver: str = Field(title="收货人")
    phone: str = Field(title="手机号")
    refund_button_clickable: bool = Field(title="退货退款按钮是否能点击")
    delay_receipt_button_clickable: bool = Field(title="延长收货按钮是否能点击")
    negotiate_after_sale_button_clickable: bool = Field(title="协商修改售后是否能点击")
    refund_action: str = Field(title="退款动作")
    start_process_time: str = Field(title="开始处理时间")
    end_process_time: str = Field(title="处理结束时间")
    log_address: str = Field(title="日志地址")
    expire_time: str = Field(title="到期时间")
    receive_status: str = Field(title="收货状态")
    overview_label: str = Field(title="信息概览标签")
    fail_reason: str = Field(title="失败原因")
    execution_record_status: str = Field(title="执行记录状态")
    local_execution_record_id: int = Field(title="本地执行记录ID")


@make_fields_optional
class DoudianRefundWithoutReturn(BaseModel):
    """抖音仅退款"""

    branch_policy: str = Field(title="分支策略")
    order_id: str = Field(title="订单编号")
    order_amount: float = Field(title="订单金额")
    order_time: str = Field(title="下单时间")
    goods_status: str = Field(title="货物状态")
    order_remark: str = Field(title="订单备注")
    express_number: str = Field(title="物流单号")
    delivery_logistics_company: str = Field(title="发货物流公司")
    erp_logistics_company: str = Field(title="ERP快递公司")
    logistics_update_count: int = Field(title="物流更新条数")
    history_logistics: str = Field(title="历史物流轨迹")
    latest_logistics: str = Field(title="最新物流轨迹")
    logistics_update: str = Field(title="物流更新至今")
    after_sale_id: str = Field(title="售后编号")
    refund_reason: str = Field(title="退款原因")
    refund_amount: float = Field(title="退款金额")
    full_refund: bool = Field(title="是否出现全额退款")
    goods_id: str = Field(title="商品ID")
    goods_title: str = Field(title="商品标题")
    goods_spec: str = Field(title="商品规格")
    merchant_code: str = Field(title="商家编码")
    reject_reason: str = Field(title="拒绝原因")
    suggest_manual_follow_up: str = Field(title="建议人工下一步跟进")
    reject_description: str = Field(title="拒绝描述")
    description: str = Field(title="说明")
    current_situation: str = Field(title="当前情况")
    remaining_time: float = Field(title="剩余时间")
    send_express_group_data: str = Field(title="发快递群数据")
    express_group_data: str = Field(title="发快递群自定义话术")
    consumer_negotiation_result: str = Field(title="与消费者协商结果")
    platform_suggest: str = Field(title="平台建议")
    check_content: str = Field(title="查件内容")
    start_process_time: str = Field(title="开始处理时间")
    end_process_time: str = Field(title="处理结束时间")
    remark: str = Field(title="备注")
    refund_action: str = Field(title="退款动作")
    fail_reason: str = Field(title="失败原因")
    success: bool = Field(title="是否成功")
    local_execution_record_id: int = Field(title="本地执行记录ID")
    execution_record_status: str = Field(title="执行记录状态")


@make_fields_optional
class PddWorkOrder(BaseModel):
    """拼多多工单"""

    branch_policy: str = Field(title="分支策略")
    account: str = Field(title="账号")
    order_id: str = Field(title="订单编号")
    work_order_id: str = Field(title="工单编号")
    order_issue: str = Field(title="订单问题")
    process: str = Field(title="处理进度")
    remaining_time: int = Field(title="剩余时间")
    latest_service_progress: str = Field(title="最新服务进度")
    order_status: str = Field(title="订单状态")
    order_amount: float = Field(title="订单金额")
    after_sale_status: str = Field(title="售后状态")
    after_sale_type: str = Field(title="售后类型")
    refund_amount: float = Field(title="退款金额")
    flag_color: str = Field(title="插旗颜色")
    original_flag_color: str = Field(title="原插旗颜色")
    order_remark: str = Field(title="订单备注")
    original_order_remark: str = Field(title="原订单备注")
    final_order_remark: str = Field(title="订单备注（最终值）")
    receiver_name: str = Field(title="收件人姓名")
    receiver_address: str = Field(title="收件人地址")
    receiver_phone: str = Field(title="收件人手机号")
    goods_title: str = Field(title="商品标题")
    goods_spec: str = Field(title="商品规格")
    goods_count: int = Field(title="商品数量")
    courier_phone: str = Field(title="快递员电话")
    delivery_address: str = Field(title="送达地址")
    encrypted_contact_address: str = Field(title="加密带*联系地址")
    resend_express_company: str = Field(title="补发快递公司")
    resend_express_number: str = Field(title="补发快递单号")
    delivery_logistics_company: str = Field(title="发货物流公司")
    delivery_logistics_number: str = Field(title="发货物流单号")
    delivery_logistics_track: str = Field(title="发货物流轨迹")
    latest_delivery_logistics_track: str = Field(title="最新发货物流轨迹")
    delivery_logistics_update_count: int = Field(title="发货物流更新条数")
    return_logistics_company: str = Field(title="退货物流公司")
    return_logistics_number: str = Field(title="退货物流单号")
    return_logistics_track: str = Field(title="退货物流轨迹")
    latest_return_logistics_track: str = Field(title="最新退货物流轨迹")
    return_logistics_update_count: int = Field(title="退货物流更新条数")
    send_message_content: str = Field(title="发消息内容")
    jst_express_company: str = Field(title="聚水潭快递公司")
    abnormal_network_warning: str = Field(title="异常网点预警_平台提示")
    suggest_manual_follow_up: str = Field(title="建议人工下一步跟进")
    process_result: str = Field(title="处理结果")
    fail_reason: str = Field(title="失败原因")
    work_order_need_process_result: str = Field(title="工单是否需要处理-结果")
    work_order_need_process_reason: str = Field(title="工单是否需要处理-原因/备注")
    get_work_order_info_result: str = Field(title="获取工单信息-结果")
    get_work_order_info_reason: str = Field(title="获取工单信息-原因/备注")
    match_strategy_result: str = Field(title="匹配策略-结果")
    match_strategy_reason: str = Field(title="匹配策略-原因/备注")
    intercept_check_result: str = Field(title="拦截校验-结果")
    intercept_check_reason: str = Field(title="拦截校验-原因/备注")
    execution_action_result: str = Field(title="执行动作-结果")
    execution_action_reason: str = Field(title="执行动作-原因/备注")
    flag_remark_result: str = Field(title="插旗备注-结果")
    flag_remark_reason: str = Field(title="插旗备注-原因/备注")
    send_message_result: str = Field(title="发消息-结果")
    send_message_reason: str = Field(title="发消息-原因/备注")
    local_execution_record_id: int = Field(title="本地执行记录ID")
    process_time: str = Field(title="处理时间")
    end_time: str = Field(title="结束时间")


@make_fields_optional
class PddPlatformRefund(BaseModel):
    """拼多多平台退款"""

    branch_policy: str = Field(title="分支策略")
    process_time: str = Field(title="处理时间")
    order_id: str = Field(title="订单编号")
    order_amount: float = Field(title="订单金额")
    delivery_status: str = Field(title="发货状态")
    refund_reason: str = Field(title="退款原因")
    refund_amount: float = Field(title="退款金额")
    refund_action: str = Field(title="退款动作")
    flag_color: str = Field(title="插旗颜色")
    final_order_remark: str = Field(title="订单备注(最终值)")
    delivery_logistics_company: str = Field(title="发货物流公司")
    express_number: str = Field(title="快递单号")
    history_logistics: str = Field(title="历史物流轨迹")
    latest_logistics: str = Field(title="最新物流轨迹")
    logistics_update: str = Field(title="物流更新至今")
    logistics_update_count: int = Field(title="物流更新条数")
    reject_reason: str = Field(title="驳回原因")
    check_content: str = Field(title="查件内容")
    send_express_group_data: str = Field(title="发快递群数据")
    suggest_manual_follow_up: str = Field(title="建议人工下一步跟进")
    process_result: str = Field(title="处理结果")
    fail_reason: str = Field(title="失败原因")
    after_sale_id: str = Field(title="售后编号")


@make_fields_optional
class TaoFactoryRefund(BaseModel):
    """淘工厂退货退款"""
    process_time: str = Field(title="处理时间")
    shop_name: str = Field(title="店铺名称")
    order_id: str = Field(title="订单编号")
    refund_id: str = Field(title="退款编号")
    express_number: str = Field(title="快递单号")
    refund_reason: str = Field(title="退款原因")
    order_amount: float = Field(title="订单成交金额")
    refund_amount: float = Field(title="退款金额")
    final_order_remark: str = Field(title="订单备注(最终值)")
    delivery_status: str = Field(title="发货状态")
    latest_logistics: str = Field(title="最新物流轨迹")
    logistics_update: str = Field(title="发货物流更新至今")
    logistics_update_count: int = Field(title="发货物流更新条数")
    delivery_logistics_company: str = Field(title="发货物流公司")
    history_logistics: str = Field(title="发货历史物流轨迹")
    return_latest_logistics: str = Field(title="退货最新物流轨迹")
    return_logistics_update: str = Field(title="退货物流更新至今")
    return_logistics_update_count: int = Field(title="退货物流更新条数")
    return_logistics_company: str = Field(title="退货物流公司")
    return_history_logistics: str = Field(title="退货历史物流轨迹")
    final_select_label: str = Field(title="选择标签(最终值)")
    branch_policy: str = Field(title="分支策略")
    message_content: str = Field(title="消息内容")
    message_path: str = Field(title="消息路径")
    refund_actions: str = Field(title="退款动作")
    reject_refund_feedback: str = Field(title="拒绝退款反馈")
    reject_reason: str = Field(title="驳回原因")
    check_content: str = Field(title="查件内容")
    send_express_group_data: str = Field(title="发快递群数据")
    express_group_data: str = Field(title="发快递群自定义话术")
    process_result: str = Field(title="处理结果")
    fail_reason: str = Field(title="失败原因")


@make_fields_optional
class TaoFactoryRefundWithoutReturn(BaseModel):
    """淘工厂仅退款"""
    branch_policy: str = Field(title="分支策略")
    process_time: str = Field(title="处理时间")
    shop_name: str = Field(title="店铺名称")
    order_id: str = Field(title="订单编号")
    refund_id: str = Field(title="退款编号")
    express_number: str = Field(title="快递单号")
    refund_reason: str = Field(title="退款原因")
    order_amount: float = Field(title="订单成交金额")
    refund_amount: float = Field(title="退款金额")
    final_order_remark: str = Field(title="订单备注(最终值)")
    delivery_status: str = Field(title="发货状态")
    latest_logistics: str = Field(title="最新物流轨迹")
    logistics_update: str = Field(title="物流更新至今")
    logistics_update_count: int = Field(title="物流更新条数")
    delivery_logistics_company: str = Field(title="发货物流公司")
    history_logistics: str = Field(title="历史物流轨迹")
    final_select_label: str = Field(title="选择标签(最终值)")
    message_content: str = Field(title="消息内容")
    message_path: str = Field(title="消息路径")
    refund_actions: str = Field(title="退款动作")
    reject_refund_feedback: str = Field(title="拒绝退款反馈")
    reject_reason: str = Field(title="驳回原因")
    check_content: str = Field(title="查件内容")
    express_group_data: str = Field(title="发快递群自定义话术")
    send_express_group_data: str = Field(title="发快递群数据")
    process_result: str = Field(title="处理结果")
    fail_reason: str = Field(title="失败原因")
