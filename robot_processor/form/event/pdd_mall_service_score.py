"""拼多多消费者体验指标"""

import json
from decimal import Decimal
from functools import wraps
from typing import TYPE_CHECKING

from loguru import logger
from pydantic import BaseModel
from tcron_jobs import runner

from robot_processor.ext import db
from robot_processor.logging import vars as log_vars
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.utils import make_fields_optional
from rpa.conf import rpa_config

if TYPE_CHECKING:
    from robot_processor.form.event.models import ScheduleContext


@make_fields_optional
class MallServiceScore(BaseModel):
    readyDate: str  # 上次评估时间
    # 消费者服务体验分
    cstmrServScore: Decimal
    cstmrServScorePpr1w: Decimal
    # 服务态度体验分
    attiLmScore: Decimal
    attiLmScorePpr1w: Decimal
    # 基础服务体验分
    jcfwLmScore: Decimal
    jcfwLmScorePpr1w: Decimal
    # 商品服务体验分
    spLmScore: Decimal
    spLmScorePpr1w: Decimal
    # 发货服务体验分
    fhLmScore: Decimal
    fhLmScorePpr1w: Decimal
    # 物流服务体验分
    wlLmScore: Decimal
    wlLmScorePpr1w: Decimal

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        # 处理 Decimal 类型
        return json.loads(super().json(**kwargs))


def get_scheduled_job_args(kwargs: "ScheduleContext"):
    from robot_processor.client import rpa_client
    from robot_processor.utils import unwrap_optional

    template_name = "拼多多店铺评分数据"
    shop_id = kwargs["shop"].id
    org_id = int(unwrap_optional(kwargs["shop"].org_id))
    workflow_id = rpa_client.get_workflow_id(template_name, org_id).data.workflow_id
    return dict(shop_id=shop_id, workflow_id=workflow_id)


@runner.register
@wrap_tcron_job
def poll_pdd_mail_service_score(shop_id: int, workflow_id: int) -> str:
    from robot_processor.business_order.business_order_manager import \
        BusinessManager
    from robot_processor.client import rpa_client
    from robot_processor.client.rpa_client.schema.pdd_mall_service_score import \
        Output
    from robot_processor.form.event.models import EventConfig
    from robot_processor.form.event.models import EventType
    from robot_processor.form.event.models import FormEventTCronSchedule
    from robot_processor.shop.models import Shop

    event_type = EventType.PDD_MALL_SERVICE_SCORE
    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return "店铺已失效"
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(event_type)

    ack_result = rpa_client.execute_workflow_wait_ack(
        workflow_id,
        None,
        dict(pid=shop.sid, method="mall-service-score"),
        timeout=rpa_config.pdd_shop_metrics_wait_act_timeout,
    )
    if ack_result.is_err():
        return str(ack_result)
    ack = ack_result.unwrap()
    event = EventConfig.get_by_id(event_type)
    FormEventTCronSchedule.get_by_shop_event(shop, event_type).mark_executed()
    if not ack.success:
        return ack.json()
    output: Output = ack.output[0]["object"]
    if not output["success"]:
        return str(output)
    result_items = {item["field"]: item["value"] for item in output["result"]["parsed"]}
    data = MallServiceScore(**result_items)
    create_result = BusinessManager.create_bo_by_event(event, shop, data.dict())
    logger.info(f"create bo result: {create_result}")
    return str(create_result)
