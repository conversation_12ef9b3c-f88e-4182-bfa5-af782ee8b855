import time
from typing import List

import arrow
from loguru import logger
from pydantic import BaseModel
from tcron_jobs import runner

from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.db import db
from robot_processor.enums import ErpType
from robot_processor.error.client_request import JstRequestError
from robot_processor.form.event.models import EventConfig
from robot_processor.form.event.models import EventType
from robot_processor.form.event.models import FormEventTCronSchedule
from robot_processor.logging import vars as log_vars
from robot_processor.shop.auth_manager import get_jst_shop_id_by_shop
from robot_processor.shop.models import ErpInfo
from robot_processor.shop.models import Shop
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.utils import make_fields_optional
from rpa.erp.jst import JstNewSDK
from rpa.erp.jst import JstQmSDK
from rpa.erp.jst import JstSDK
from rpa.erp.jst import schemas as jst_schemas
from rpa.erp.jst.utils import complete_wms_co_name_by_wms_co_id


@make_fields_optional
class OutOrderItem(BaseModel):
    combine_sku_id: str
    i_id: str
    ioi_id: int
    is_gift: bool
    name: str
    oi_id: str
    outer_oi_id: str
    properties_value: str
    qty: int
    raw_so_id: str
    sale_amount: float
    sale_base_price: float
    sale_price: float
    sku_id: str

    supplier_name: str
    batch_no: str


@make_fields_optional
class OutOrder(BaseModel):
    business_staff: str
    buyer_tax_no: str
    co_id: int
    created: str
    drp_co_id_from: str
    free_amount: float
    freight: float
    io_date: str
    io_id: str
    is_cod: bool
    is_print: str
    is_print_express: str
    items: List[OutOrderItem]
    l_id: str
    labels: str
    lc_id: str
    logistics_company: str
    modified: str
    o_id: str
    open_id: str
    order_staff_id: int
    order_staff_name: str
    order_type: str
    paid_amount: float
    pay_amount: float
    pay_date: str
    receiver_city: str
    receiver_country: str
    receiver_district: str
    receiver_state: str
    shop_buyer_id: str
    shop_id: int
    shop_name: str
    so_id: str
    status: str
    stock_enabled: str
    ts: int
    warehouse: str
    weight: float
    wms_co_id: int
    readable_status: str  # 非API提供


def strftime(ts):
    time_struct = time.localtime(ts)
    return time.strftime("%Y-%m-%d %H:%M:%S", time_struct)


@runner.register
@wrap_tcron_job
def poll_jst_out_orders(shop_id: int):
    from robot_processor.plugin.trade_utils import ErpTradeManager

    # 聚水潭出库单
    shop = db.session.get(Shop, shop_id)
    assert shop, "店铺不存在"
    log_vars.Sid.set(shop.sid)
    log_vars.OrgId.set(shop.org_id)
    erp_info = ErpInfo.get_by_sid(shop.sid, ErpType.JST)
    assert erp_info, "店铺未授权"
    event = EventConfig.get_by_id(EventType.JST_OUT_ORDER)
    erp_shop_id = get_jst_shop_id_by_shop(shop)
    event_scheduler = FormEventTCronSchedule.get_by_shop_event(shop, EventType.JST_OUT_ORDER)
    jst_client = JstSDK(shop.sid)
    qm_client = JstQmSDK(shop.sid)
    open_client = JstNewSDK(shop.sid)
    now = arrow.now()
    start_ts = int(event_scheduler.executed_at.timestamp())
    now_ts = int(now.timestamp())
    print(start_ts, now_ts)
    while start_ts <= now_ts:
        next_ts = start_ts + 3600 if start_ts + 3600 <= now_ts else now_ts
        params = jst_schemas.OpenOrdersOutSimpleQuery(
            page_index=1,
            page_size=50,
            modified_begin=strftime(start_ts),
            modified_end=strftime(next_ts),
            shop_id=erp_shop_id,
        )
        has_next = True
        while has_next:
            try:
                resp = ErpTradeManager.query_out_orders_jst(qm_client, open_client, params)
                if len(resp.datas) == 0:
                    break
                for order in resp.datas:
                    for item in order.items:
                        if order.batchs:
                            for batch in order.batchs:
                                if item.sku_id == batch.sku_id:
                                    item.supplier_name = batch.supplier_name
                                    item.batch_no = batch.batch_no
                for data in resp.datas:
                    data.warehouse = data.warehouse or complete_wms_co_name_by_wms_co_id(
                        jst_client,
                        shop.org_id,  # type: ignore[arg-type]
                        data.wms_co_id,
                    )
                for order in resp.datas:
                    BusinessManager.create_bo_by_event(event, shop, OutOrder.parse_obj(order.dict()).dict())
                time.sleep(0.5)
            except JstRequestError as e:
                print(e.message)
                logger.info(e.message)
                # 接口内部已经做过重试，短期无效的话，，一直等着没有意义，这次调度放弃掉
                return
            finally:
                params = params.next_page()
        start_ts = next_ts + 1
    event_scheduler.mark_executed(now.datetime)
