from dataclasses import asdict
from dataclasses import dataclass
from datetime import datetime
from datetime import timedelta
from enum import StrEnum
from enum import auto
from typing import TYPE_CHECKING
from typing import Callable
from typing import Final
from typing import Literal
from typing import TypedDict

from loguru import logger
from pydantic import BaseModel
from robot_types.core import Scope
from robot_types.core import Symbol
from robot_types.core import SymbolTable
from sqlalchemy import JSON
from sqlalchemy import DateTime
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import Integer
from sqlalchemy import String
from sqlalchemy import Text
from sqlalchemy import select
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column

from robot_processor.db import DbBaseModel
from robot_processor.db import db
from robot_processor.db import in_transaction


def register_event_dataschema():
    from robot_processor.form.event import doudian_compass_stat
    from robot_processor.form.event import doudian_staff_stat
    from robot_processor.form.event import kuaimai_trade
    from robot_processor.form.event import pdd_mall_navigator_info
    from robot_processor.form.event import pdd_mall_service_score
    from robot_processor.form.event import pdd_profit_accounting
    from robot_processor.form.event import pdd_sale_quality
    from robot_processor.form.event import pdd_yingxiao_account_report
    from robot_processor.form.event import rpa_execution_result
    from robot_processor.form.event.doudian_collection_timeout import poll_doudian_shop_colection_timeout
    from robot_processor.form.event.doudian_logisitcs_exception import poll_doudian_shop_logisitcs_exception
    from robot_processor.form.event.doudian_refund import poll_doudian_shop_refund
    from robot_processor.form.event.doudian_trade import poll_doudian_shop_trade
    from robot_processor.form.event.jd_asc import poll_jd_pop_shop_asc
    from robot_processor.form.event.jd_pop_refund import poll_jd_pop_shop_refund
    from robot_processor.form.event.jst_out_orders import poll_jst_out_orders
    from robot_processor.form.event.ks_refund import poll_kuaishou_shop_refund
    from robot_processor.form.event.pdd_logistics_exception import poll_pdd_shop_logisitcs_exception
    from robot_processor.form.event.pdd_refund import poll_pdd_shop_refund
    from robot_processor.form.event.pdd_trade import poll_pdd_shop_trade
    from robot_processor.form.event.taobao_chat_history import poll_taobao_shop_chat_history
    from robot_processor.form.event.taobao_refund import poll_taobao_shop_refund
    from robot_processor.form.event.taobao_trade import poll_taobao_shop_trade
    from robot_processor.form.event.wdt_full_split_order import poll_wdt_full_split_order
    from robot_processor.form.event.xiaohongshu_refund import poll_xiaohongshu_shop_refund

    EventType.PDD_REFUND.scheduled_job = poll_pdd_shop_refund
    EventType.DOUDIAN_REFUND.scheduled_job = poll_doudian_shop_refund
    EventType.TAOBAO_REFUND.scheduled_job = poll_taobao_shop_refund
    EventType.JST_OUT_ORDER.scheduled_job = poll_jst_out_orders
    EventType.TAOBAO_TRADE.scheduled_job = poll_taobao_shop_trade
    EventType.DOUDIAN_TRADE.scheduled_job = poll_doudian_shop_trade
    EventType.PDD_TRADE.scheduled_job = poll_pdd_shop_trade
    EventType.RPA_TAO_FACTORY_REFUND_WITHOUT_RETURN.scheduled_job = rpa_execution_result.TaoFactoryRefundWithoutReturn
    EventType.JD_POP_REFUND.scheduled_job = poll_jd_pop_shop_refund
    EventType.JD_POP_ASC.scheduled_job = poll_jd_pop_shop_asc
    EventType.DOUDIAN_COLLECTION_TIMEOUT.scheduled_job = poll_doudian_shop_colection_timeout
    EventType.DOUDIAN_LOGISITCS_EXCEPTION.scheduled_job = poll_doudian_shop_logisitcs_exception
    EventType.PDD_LOGISITCS_EXCEPTION.scheduled_job = poll_pdd_shop_logisitcs_exception
    EventType.WDT_FULL_SPLIT_ORDER_NOTI.scheduled_job = poll_wdt_full_split_order
    EventType.TAOBAO_CHAT_HISTORY.scheduled_job = poll_taobao_shop_chat_history

    # 拼多多消费者体验指标
    EventType.PDD_MALL_SERVICE_SCORE.scheduled_job = pdd_mall_service_score.poll_pdd_mail_service_score
    EventType.PDD_MALL_SERVICE_SCORE.scheduled_job_args = pdd_mall_service_score.get_scheduled_job_args
    # 拼多多售后数据
    EventType.PDD_SALE_QUALITY.scheduled_job = pdd_sale_quality.poll_pdd_sale_quality
    EventType.PDD_SALE_QUALITY.scheduled_job_args = pdd_sale_quality.get_scheduled_job_args
    # 拼多多综合体验星级
    EventType.PDD_MALL_NAVIGATOR_INFO.scheduled_job = pdd_mall_navigator_info.poll_pdd_mall_navigator_info
    EventType.PDD_MALL_NAVIGATOR_INFO.scheduled_job_args = pdd_mall_navigator_info.get_scheduled_job_args
    # 拼多多推广平台财务流水-日账单
    EventType.PDD_YINGXIAO_ACCOUNT_REPORT.scheduled_job = pdd_yingxiao_account_report.poll_pdd_yingxiao_account_report
    EventType.PDD_YINGXIAO_ACCOUNT_REPORT.scheduled_job_args = pdd_yingxiao_account_report.get_scheduled_job_args
    # 拼多多数据中心-交易数据
    EventType.PDD_PROFIT_ACCOUNTING.scheduled_job = pdd_profit_accounting.poll_pdd_profit_accounting
    EventType.PDD_PROFIT_ACCOUNTING.scheduled_job_args = pdd_profit_accounting.get_scheduled_job_args
    # 抖店客服数据
    EventType.DOUDIAN_STAFF_STAT.scheduled_job = doudian_staff_stat.poll_doudian_staff_stat
    EventType.DOUDIAN_STAFF_STAT.scheduled_job_args = doudian_staff_stat.get_scheduled_job_args
    # 抖店电商罗盘数据
    EventType.DOUDIAN_COMPASS_STAT.scheduled_job = doudian_compass_stat.poll_doudian_compass_stat
    EventType.DOUDIAN_COMPASS_STAT.scheduled_job_args = doudian_compass_stat.get_scheduled_job_args

    EventType.KUAISHOU_REFUND.scheduled_job = poll_kuaishou_shop_refund
    EventType.XIAOHONGSHU_REFUND.scheduled_job = poll_xiaohongshu_shop_refund

    EventType.KUAIMAI_TRADE.scheduled_job = kuaimai_trade.poll_kuaimai_trade
    EventType.KUAIMAI_TRADE.scheduled_job_args = kuaimai_trade.get_scheduled_job_args


if TYPE_CHECKING:
    from robot_processor.shop.models import Shop


class ScheduleContext(TypedDict):
    shop: "Shop"
    event: "EventType"


class EventType(StrEnum):
    PDD_REFUND = "PDD_REFUND"  # 拼多多售后单
    DOUDIAN_REFUND = "DOUDIAN_REFUND"  # 抖店售后单
    TAOBAO_REFUND = "TAOBAO_REFUND"  # 淘宝售后单
    JST_OUT_ORDER = "JST_OUT_ORDER"  # 聚水潭出库单变更
    QN_INVOICE_LIST = "QN_INVOICE_LIST"  # 千牛发票列表
    TAOBAO_TRADE = "TAOBAO_TRADE"  # 淘宝订单变更
    DOUDIAN_TRADE = "DOUDIAN_TRADE"  # 抖店订单变更
    PDD_TRADE = "PDD_TRADE"  # 拼多多订单变更
    JD_POP_REFUND = "JD_POP_REFUND"  # 京东POP退款单(仅退款)
    JD_POP_ASC = "JD_POP_ASC"  # 京东POP售后服务单
    DOUDIAN_COLLECTION_TIMEOUT = "DOUDIAN_COLLECTION_TIMEOUT"
    DOUDIAN_LOGISITCS_EXCEPTION = "DOUDIAN_LOGISITCS_EXCEPTION"
    PDD_LOGISITCS_EXCEPTION = "PDD_LOGISITCS_EXCEPTION"
    KUAISHOU_REFUND = "KUAISHOU_REFUND"  # 快手售后单
    WDT_FULL_SPLIT_ORDER_NOTI = "WDT_FULL_SPLIT_ORDER_NOTI"  # 旺店通企业版拆单通知(整单)
    TAOBAO_CHAT_HISTORY = "TAOBAO_CHAT_HISTORY"  # 淘宝聊天记录
    XIAOHONGSHU_REFUND = "XIAOHONGSHU_REFUND"  # 小红书售后单

    # rpa 执行结果创建工单
    RPA_TAOBAO_REFUND = "taobaoRefund"  # 淘宝退货退款
    RPA_TAOBAO_REFUND_WITHOUT_RETURN = "taobaoRefundWithoutReturn"  # 淘宝仅退款
    RPA_PDD_REFUND = "pddRefund"  # 拼多多退货退款
    RPA_PDD_REFUND_WITHOUT_RETURN = "pddRefundWithoutReturn"  # 拼多多仅退款
    RPA_DOUDIAN_REFUND = "douyinRefund"  # 抖店退货退款
    RPA_DOUDIAN_REFUND_WITHOUT_RETURN = "douyinRefundWithoutReturn"  # 抖店仅退款
    RPA_PDD_WORK_ORDER = "pddWorkOrder"  # 拼多多工单
    RPA_PDD_PLATFORM_REFUND = "pddPlatformRefund"  # 拼多多平台退款
    RPA_TAO_FACTORY_REFUND = "taoFactoryRefund"  # 淘工厂退货退款
    RPA_TAO_FACTORY_REFUND_WITHOUT_RETURN = "taoFactoryRefundWithoutReturn"  # 淘工厂仅退款

    # 拼多多店铺评分数据
    PDD_MALL_SERVICE_SCORE = "PDD_MALL_SERVICE_SCORE"  # 拼多多消费者体验指标
    PDD_SALE_QUALITY = "PDD_SALE_QUALITY"  # 拼多多售后数据
    PDD_MALL_NAVIGATOR_INFO = "PDD_MALL_NAVIGATOR_INFO"  # 拼多多综合体验星级
    PDD_YINGXIAO_ACCOUNT_REPORT = "PDD_YINGXIAO_ACCOUNT_REPORT"  # 拼多多推广平台财务流水-日账单
    PDD_PROFIT_ACCOUNTING = "PDD_PROFIT_ACCOUNTING"  # 拼多多利润核算
    DOUDIAN_STAFF_STAT = "DOUDIAN_STAFF_STAT"  # 抖店客服数据
    DOUDIAN_COMPASS_STAT = "DOUDIAN_COMPASS_STAT"  # 抖店电商罗盘数据

    # 快麦订单变更
    KUAIMAI_TRADE = "KUAIMAI_TRADE"

    def __init__(self, value):
        super().__init__(value)  # type: ignore[call-arg]
        self._scheduled_job = None

        def get_scheduled_job_args(kwargs: ScheduleContext):
            return dict(shop_id=kwargs["shop"].id)

        self._scheduled_job_args: Callable[[ScheduleContext], dict] = get_scheduled_job_args

    @property
    def scheduled_job(self):
        """如果事件需要通过 tcron-jobs 来调度，通过 EventType.xxx.scheduled_job 来绑定"""
        return self._scheduled_job

    @scheduled_job.setter
    def scheduled_job(self, fn):
        assert callable(fn), f"EventType.{self.name} assign scheduled_job must be a callable, got {fn}"
        assert self._scheduled_job is None, f"EventType.{self.name} scheduled_job already assigned"
        self._scheduled_job = fn

    @property
    def scheduled_job_args(self):
        """如果事件需要指定调度参数，通过 EventType.xxx.scheduled_job_args 来绑定"""
        return self._scheduled_job_args

    @scheduled_job_args.setter
    def scheduled_job_args(self, fn: Callable[[ScheduleContext], dict]):
        self._scheduled_job_args = fn

    @classmethod
    def safe_init(cls, value):
        try:
            return cls(value)
        except ValueError as e:
            try:
                return cls[value]
            except KeyError:
                raise e

    def which_dimension(self) -> Literal["org", "shop"]:
        """可以指定 Event 的调度纬度是 org / shop，如果没有指定则通过 tcron-job 的参数来判断"""
        if self in {EventType.KUAIMAI_TRADE}:
            return "org"
        return "shop"


@dataclass(frozen=True)
class EventConfig:
    id: EventType
    label: str
    description: str
    icon: str
    symbols: list[Symbol]
    schedule_config: dict

    @classmethod
    def get_by_id(cls, event_type: EventType):
        return event_registry[event_type]

    def get_form_by_shop(self, shop):
        from robot_processor.form.models import Form

        stmt = (
            select(Form)
            .select_from(FormEventSubscription)
            .join(Form, Form.id == FormEventSubscription.form_id)
            .where(FormEventSubscription.shop_id == shop.id)
            .where(FormEventSubscription.event == self.id)
            .where(FormEventSubscription.state == FormEventSubscription.State.ENABLED)
        )
        return db.session.execute(stmt).scalars().all()

    def get_shortcuts(self) -> dict[str, "EventShortcuts"]:
        stmt = select(EventShortcuts).where(EventShortcuts.event == self.id)
        shortcuts = db.session.execute(stmt).scalars().all()
        return {shortcut.value: shortcut for shortcut in shortcuts}

    def get_deprecated_symbols(self):
        from robot_processor.symbol_table.models import Symbol as RobotSymbol

        def to_robot_symbol(symbol: Symbol) -> RobotSymbol:
            return RobotSymbol(
                type_spec=asdict(symbol.type_spec.to_deprecated()),
                name=symbol.name,
                label=symbol.label,
                children=[to_robot_symbol(child) for child in symbol.children] if symbol.children is not None else None,
            )

        return [to_robot_symbol(symbol) for symbol in self.symbols]

    def get_scope(self):
        return Scope(self.id.name, None)

    def get_symbol_table(self):
        scope = self.get_scope()
        symbol_table = SymbolTable([])
        symbol_table.add_scope(scope)
        symbol_table.add_symbols(scope, self.symbols)
        return symbol_table

    def get_schedule_config(self):
        if EventType(self.id).scheduled_job is None:
            return None
        if self.schedule_config:
            return EventScheduleConfig.parse_obj(self.schedule_config)
        else:
            return EventScheduleConfig.default()


def _init_event_registry():
    from dacite import from_dict
    from robot_types import resource

    _event_registry = dict()
    for event_ident in resource.event.registry:
        try:
            event_type = EventType.safe_init(event_ident)
        except ValueError:
            logger.warning(f"robot-processor 未实现 Event: {event_ident}")
            continue
        event_config = resource.event.registry[event_ident].copy()
        event_config["id"] = event_type
        _event_registry[event_type] = from_dict(EventConfig, event_config)
    return _event_registry


# TODO EventType 由代码生成
event_registry: Final[dict[EventType, EventConfig]] = _init_event_registry()


class EventShortcuts(DbBaseModel):
    class Mode(StrEnum):
        CODE = auto()
        FILTER = auto()

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    event: Mapped[EventType] = mapped_column(SQLEnum(EventType, native_enum=False, length=128))
    label: Mapped[str] = mapped_column(String(128))
    description: Mapped[str] = mapped_column(Text(), default="")
    value: Mapped[str] = mapped_column(String(128))
    mode: Mapped[str] = mapped_column(SQLEnum(Mode, native_enum=False, length=128), default=Mode.FILTER)
    filters: Mapped[dict] = mapped_column(JSON(), default=dict)


class FormEventSubscription(DbBaseModel):
    class State(StrEnum):
        ENABLED = auto()
        DISABLED = auto()

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    org_id: Mapped[int]
    shop_id: Mapped[int]
    form_id: Mapped[int]
    step_uuid: Mapped[str] = mapped_column(String(32))
    event: Mapped[EventType] = mapped_column(SQLEnum(EventType, native_enum=False, length=128))
    state: Mapped[State] = mapped_column(SQLEnum(State, native_enum=False, length=128), default=State.ENABLED)

    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)

    @classmethod
    @in_transaction()
    def upsert(cls, shop, form, step, event, enabled: bool):
        state = cls.State.ENABLED if enabled else cls.State.DISABLED
        subscription = (
            db.session.execute(
                select(cls)
                .where(cls.shop_id == shop.id)
                .where(cls.form_id == form.id)
                .where(cls.step_uuid == step.step_uuid)
                .where(cls.event == event)
            )
            .scalars()
            .one_or_none()
        )
        if subscription is None:
            logger.bind(form_id=form.id, sid=shop.sid, org_id=shop.org_id).info("工单第一次初始化")
            subscription = cls(
                org_id=shop.org_id,
                shop_id=shop.id,
                form_id=form.id,
                step_uuid=step.step_uuid,
                event=event,
                state=state,
            )
            db.session.add(subscription)
        if subscription.state != state:
            subscription.state = state
            subscription.updated_at = datetime.now()


class FormEventTCronSchedule(DbBaseModel):
    class State(StrEnum):
        ACTIVE = auto()
        TERMINATED = auto()

    id: Mapped[str] = mapped_column(String(128), primary_key=True)
    org_id: Mapped[int]
    shop_id: Mapped[int]
    job_env: Mapped[str] = mapped_column(String(128))
    job_app: Mapped[str] = mapped_column(String(128))
    job_proc: Mapped[str] = mapped_column(String(128))
    job_name: Mapped[str] = mapped_column(String(128))
    schedule_name: Mapped[str] = mapped_column(String(128))
    argument: Mapped[str] = mapped_column(Text())
    cron_schedule: Mapped[str] = mapped_column(String(128))
    job_timeout: Mapped[int] = mapped_column(Integer, comment="单位: 秒")

    event: Mapped[EventType] = mapped_column(SQLEnum(EventType, native_enum=False, length=128))
    state: Mapped[State] = mapped_column(SQLEnum(State, native_enum=False, length=128))

    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)
    executed_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)

    def __str__(self):
        return (
            "FormEventTCronSchedule("
            f"id={self.id}, org_id={self.org_id}, shop_id={self.shop_id}, "
            f"event={self.event}, state={self.state}, "
            f"job_name={self.job_name}, schedule_name={self.schedule_name}, "
            f"argument={self.argument}, cron_schedule={self.cron_schedule}"
            ")"
        )

    @property
    def schedule_config(self):
        return EventScheduleConfig(schedule_cron=self.cron_schedule, job_timeout=self.job_timeout)

    @classmethod
    def get_by_shop_event(cls, shop, event: EventType):
        return db.session.scalars(
            select(cls).where(cls.shop_id == shop.id, cls.event == event, cls.state == cls.State.ACTIVE)
        ).one()

    @classmethod
    def get_by_org_event(cls, org_id, event: EventType):
        assert event.which_dimension() == "org"
        return FormEventTCronSchedule.query.filter_by(org_id=org_id, event=event, state=cls.State.ACTIVE).one()

    @in_transaction()
    def mark_executed(self, executed_at: datetime | None = None):
        self.executed_at = executed_at or datetime.now()

    def get_cursor_at(self, max_lag: timedelta | None = None, now: datetime | None = None):
        """获取调度任务的 cursor_at，如果 cursor_at 超过 allow_timedelta，则返回 now - max_lag

        Args:
            max_lag: cursor_at 允许的最大“滞后”时间（None 表示不限制）
            now: 当前参考时间，默认使用 datetime.now()
        Return:
            datetime: cursor_at
        """
        cursor_at = self.executed_at or datetime.now()
        if max_lag is None:
            return cursor_at
        return max(cursor_at, (now or datetime.now()) - max_lag)

    @classmethod
    @in_transaction()
    def schedule(cls, shop, event: EventType):
        from robot_processor.t_cron import scheduler

        org_id = int(shop.org_id)
        schedule_dimension = event.which_dimension()
        if schedule_dimension == "org":
            sql = select(cls).where(cls.org_id == org_id, cls.event == event, cls.state == cls.State.ACTIVE)
        else:
            sql = select(cls).where(cls.shop_id == shop.id, cls.event == event, cls.state == cls.State.ACTIVE)

        scheduled_job = db.session.scalars(sql).one_or_none()
        if scheduled_job:
            logger.bind(org_id=org_id, shop_id=shop.id, event=event).info(f"schedule already exists {scheduled_job}")
        else:
            logger.bind(org_id=org_id, shop_id=shop.id, event=event).info("create schedule")
            schedule_config = EventConfig.get_by_id(event).get_schedule_config()
            scheduled_job_args = event.scheduled_job_args(ScheduleContext(shop=shop, event=event))
            response = scheduler.create_schedule(
                event.scheduled_job,
                shop.title,
                schedule_config.schedule_cron,
                schedule_config.job_timeout,
                **scheduled_job_args,
            )
            scheduled_job = cls(
                id=response.schedule.schedule_id,
                org_id=shop.org_id,
                shop_id=shop.id if schedule_dimension == "shop" else 0,
                job_env=response.schedule.job_identifier.env,
                job_app=response.schedule.job_identifier.app,
                job_proc=response.schedule.job_identifier.proc,
                job_name=response.schedule.job_identifier.job_name,
                schedule_name=response.schedule.schedule_name,
                argument=response.schedule.argument,
                cron_schedule=response.schedule.cron_schedule,
                job_timeout=response.schedule.job_timeout.seconds,
                event=event,
                state=cls.State.ACTIVE,
            )
            db.session.add(scheduled_job)
        return scheduled_job

    @in_transaction()
    def stop(self):
        from robot_processor.t_cron import scheduler

        try:
            scheduler.close_schedule(self.id)
        except Exception:  # noqa https://git.leyantech.com/ep/tcron/-/issues/33
            pass
        self.state = self.State.TERMINATED
        self.updated_at = datetime.now()


def sync_schedule_state(org_id, event: EventType | None):
    from robot_processor.shop.models import Shop

    if not event:
        logger.bind(org_id=org_id).warning("ignore schedule by event is None")
        return
    event = EventType(event)
    if event.scheduled_job is None:
        logger.bind(org_id=org_id, event=event).warning("ignore schedule by event not bind scheduled_job")
        return
    subscribed_shop_ids: set[int] = set(
        db.session.scalars(
            select(FormEventSubscription.shop_id)
            .where(FormEventSubscription.org_id == org_id)
            .where(FormEventSubscription.event == event)
            .where(FormEventSubscription.state == FormEventSubscription.State.ENABLED)
        ).all()
    )
    for scheduled in db.session.scalars(
        select(FormEventTCronSchedule)
        .where(FormEventTCronSchedule.org_id == org_id)
        .where(FormEventTCronSchedule.event == event)
        .where(FormEventTCronSchedule.state == FormEventTCronSchedule.State.ACTIVE)
        .where(FormEventTCronSchedule.shop_id.notin_(list(subscribed_shop_ids)))
    ).all():
        logger.bind(org_id=org_id, event=event).info(f"stop schedule {scheduled}")
        scheduled.stop()
    logger.bind(org_id=org_id, event=event).info(f"sync schedule for shop {subscribed_shop_ids}")
    for shop_id in subscribed_shop_ids:
        shop = db.session.get(Shop, shop_id)
        FormEventTCronSchedule.schedule(shop, event)


class EventScheduleConfig(BaseModel):
    # 表达式规则参考 https://docs.temporal.io/workflows#cron-schedules
    schedule_cron: str
    job_timeout: int  # 任务超时时间，单位秒

    @classmethod
    def default(cls):
        return cls(schedule_cron="@every 300s", job_timeout=300)
