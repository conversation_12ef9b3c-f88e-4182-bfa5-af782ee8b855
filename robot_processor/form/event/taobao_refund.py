from datetime import datetime
from typing import Optional

import arrow
from leyan_proto.digismart.trade.dgt_trade_pb2 import Order as pb_Order
from leyan_proto.digismart.trade.dgt_trade_pb2 import TradeInfo
from loguru import logger
from pydantic import BaseModel
from result import Err
from result import Result
from tcron_jobs import runner

from robot_processor.client import taobao_client
from robot_processor.ext import db
from robot_processor.logging import vars as log_vars
from robot_processor.refund.models import TaobaoRefund
from robot_processor.refund.schemas import DisputeType
from robot_processor.refund.schemas import RefundStatus
from robot_processor.refund.schemas import TaobaoRefundInfo as api_TaobaoRefundInfo
from robot_processor.symbol_table.named_typespec.component import ComponentProduct
from robot_processor.symbol_table.named_typespec.component import ComponentProductList
from robot_processor.symbol_table.named_typespec.component import ComponentTradeNo
from robot_processor.symbol_table.named_typespec.component import ComponentTradeNoList
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.types.client.top.top_response import TaobaoLogisticsTraceGet
from robot_processor.types.common import BuyerInfo
from robot_processor.utils import Amount
from robot_processor.utils import amount_diff
from robot_processor.utils import empty_str_as_none
from robot_processor.utils import make_fields_optional
from robot_processor.utils import message_to_dict


@make_fields_optional
class MainOrderAfterSalesInfo(BaseModel):
    """主订单售后信息"""

    refund_id: str
    oid: str
    refund_fee: float
    status_zh: str


def build_main_order_after_sales_info(tid: int):
    refund_list: list[TaobaoRefund] = TaobaoRefund.query.filter_by(tid=tid).all()
    after_sales_list: list[MainOrderAfterSalesInfo] = []
    for refund in refund_list:
        refund_info = refund.get_refund_info()
        after_sales_list.append(
            MainOrderAfterSalesInfo(
                refund_id=str(refund.refund_id),
                oid=str(refund_info.oid),
                refund_fee=refund_info.refund_fee,
                status_zh=refund_info.status.label,
            )
        )
    return after_sales_list


@make_fields_optional
class TaobaoRefundInfo(BaseModel):
    refund_id: str  # 售后单号
    refund_reason: str  # 售后原因
    refund_desc: str  # 售后说明
    refund_fee: float  # 退款金额
    refund_status: str  # 售后状态
    refund_status_zh: str
    refund_created_time: str  # 售后申请时间
    refund_logistics_company: str  # 退货物流公司
    refund_invoice_no: str  # 退货物流单号

    tid: str  # 主订单号
    oid: str  # 子订单号
    buyer_nick: str  # 买家昵称
    buyer_open_uid: str  # 买家open_uid
    order_status: str  # 订单状态
    order_status_zh: str
    order_created: str  # 订单创建时间
    order_price: float  # 订单金额
    order_payment: float  # 实付金额
    goods_status: str  # 商品状态
    goods_status_zh: str
    logistics_company: str  # 物流公司
    invoice_no: str  # 物流单号
    dispute_type: str  # 退款类型
    dispute_type_zh: str
    divide_order_fee: float  # 实付金额
    outer_id: str  # 商品外部商家编码
    agree_refund_apply_time: Optional[str]  # 同意退款时间

    pay_and_refund_amount_diff: float  # 实付金额与退款金额差额
    fs_trade_no: ComponentTradeNoList
    fs_product_list: ComponentProductList
    is_refund_all: bool  # 是否整单退款
    main_order_after_sales_info: list[MainOrderAfterSalesInfo]  # 主订单售后信息
    is_collected: bool  # 是否已揽收
    is_transported: bool  # 是否已经运输
    is_agency_signed: bool  # 是否已代签
    is_signed: bool  # 是否已签收

    desensitization_address: str  # 脱敏收件人信息
    agree_user: str  # 同意退款人
    order_paid: Optional[str]  # 订单支付时间


@runner.register
@wrap_tcron_job
def poll_taobao_shop_refund(shop_id: int) -> int:
    from robot_processor.form.event.models import EventType
    from robot_processor.form.event.models import FormEventTCronSchedule
    from robot_processor.shop.models import Shop

    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return -1
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(EventType.TAOBAO_REFUND)
    event_scheduler = FormEventTCronSchedule.get_by_shop_event(shop, EventType.TAOBAO_REFUND)
    now = arrow.now()
    end_update_at = now.naive
    if now.shift(minutes=-30).naive > event_scheduler.executed_at:
        start_update_at = now.shift(minutes=-30).naive
    else:
        start_update_at = event_scheduler.executed_at
    refund_paginator = get_data_fetcher(
        seller_nick=shop.nick,
        update_time_range=(start_update_at, end_update_at),
    )
    result: list[Result] = []
    for refund in refund_paginator.chain_fetch():
        if refund.jdp_modified > event_scheduler.executed_at:
            event_scheduler.mark_executed(refund.jdp_modified)
        try:
            result.extend(create_bo(shop, refund))
        except Exception as e:
            logger.opt(exception=e).error(f"创建售后单失败 {e}")
            result.append(Err(e))
    if now.naive > event_scheduler.executed_at:
        event_scheduler.mark_executed(now.naive)
    logger.info(f"创建售后单 {len([r for r in result if r.is_ok()])} 条，结果 {result}")
    return refund_paginator.total


def create_bo(shop, refund: TaobaoRefund) -> list[Result]:
    from robot_processor.business_order.business_order_manager import BusinessManager
    from robot_processor.client import trade_client
    from robot_processor.form.event.models import EventConfig
    from robot_processor.form.event.models import EventType

    event = EventConfig.get_by_id(EventType.TAOBAO_REFUND)
    refund_info = refund.get_refund_info()
    log_vars.RefundId.set(refund.refund_id)
    log_vars.Tid.set(refund_info.oid)
    logger.info("处理淘宝售后单")
    trade_info = trade_client.get_trade_by_tid(sid=shop.sid, tid=str(refund.tid))
    if not trade_info.trade_id:
        error = Err(f"无法获取淘宝订单信息 tid:{refund.tid}")
        logger.error(error.err())
        return [error]
    trade_payment = message_to_dict(trade_info)["payment"]
    for order in trade_info.orders:
        if order.oid == str(refund_info.oid):
            current_order = order
            break
    else:
        error = Err(f"无法获取淘宝订单子订单信息 oid:{refund_info.oid}")
        logger.warning(error.err())
        return [error]

    if current_order.HasField("shipping"):
        logistics_company = empty_str_as_none(current_order.shipping.contractor)
        invoice_no = empty_str_as_none(current_order.shipping.tracking_num)
    else:
        logistics_company = invoice_no = None

    main_order_after_sales_info = build_main_order_after_sales_info(refund.tid)
    divide_order_fee = message_to_dict(current_order)["divide_order_fee"]  # proto float 精度问题

    is_collected = False
    is_transported = False
    is_agency_signed = False
    is_signed = False
    main_grant = shop.get_recent_record()
    if invoice_no:
        trace_resp = taobao_client.taobao_logisitcs_trace_get(main_grant.access_token, str(refund.tid))
        if trace_resp.is_ok():
            traces: TaobaoLogisticsTraceGet = trace_resp.unwrap()
            for trace in traces["result"]:
                if "out_sid" not in trace:
                    continue  # type: ignore[unreachable]
                if trace["out_sid"] == invoice_no:
                    is_collected = any(
                        step_info["action"] in ["GOT", "TMS_ACCEPT"] for step_info in trace["trace_list"]
                    )
                    # FIXME(<EMAIL>): 逻辑不严谨，会有逆向物流
                    if is_collected and len(trace["trace_list"]) > 3:
                        is_transported = True
                    is_agency_signed = any(
                        step_info["action"]
                        in [
                            "STA_AGENCY_DELIVERY",
                            "CP_INBOUND",
                            "CW_INBOUND",
                            "GSTA_INBOUND",
                            "GTMS_WAIT_SELF_PICK",
                            "HANDOVERSCAN_SIGNED",
                            "INBOUND",
                            "SH_INBOUND",
                            "SH_MOVE_PACK",
                            "STA_COLLECT_HANDOVER",
                            "STA_DELAY_SIGN",
                            "STA_INBOUND",
                            "STA_MOVE_PACK",
                            "STA_STATION_RETURN",
                            "STA_TOWN_ENTRUCKING",
                            "STA_VILLAGE_IN",
                            "STA_VILLAGER_SIGN",
                            "TMS_BRANCH_END",
                            "TMS_COMMUNICATE",
                            "TMS_INBOUND",
                            "XN_AGENCY_SIGNED",
                            "SH_SIGNED",
                        ]
                        for step_info in trace["trace_list"]
                    )
                    is_signed = any(
                        step_info["action"]
                        in [
                            "CONFIRM_SIGN",
                            "CONFIRM_SIGNED",
                            "CW_SIGN_IN_SUCCESS",
                            "DEPARTURE_SIGNED",
                            "GSTA_SIGN",
                            "GTMS_SIGNED",
                            "RT_SIGNIN_SUCCESS",
                            "SIGNED",
                            "STA_SEND_SIGN",
                            "STA_SIGN",
                            "TMS_SIGN",
                            "X_C_15",
                            "XNSIGN",
                        ]
                        for step_info in trace["trace_list"]
                    )
    refund_messages_resp = taobao_client.refund_messages_get(main_grant.access_token, str(refund.refund_id))
    agree_user = ""
    agree_refund_apply_time = None
    if refund_messages_resp.is_ok():
        refund_message_result = refund_messages_resp.unwrap()
        for refund_message in refund_message_result["refund_messages"]:
            if (
                "主动同意" in refund_message["content"]
                or "退款成功" in refund_message["content"]
                or "主动介入处理" in refund_message["content"]
                or "垫付给买家" in refund_message["content"]
            ):
                agree_user = refund_message["owner_nick"]
                agree_refund_apply_time = refund_message["created"]
    if agree_user == "" and refund_info.status == RefundStatus.SUCCESS:
        if arrow.get(refund_info.modified) <= arrow.get(refund_info.created).shift(seconds=40):
            agree_user = "天猫客服"
            agree_refund_apply_time = refund_info.modified
        elif refund_info.dispute_type == DisputeType.RETURN_GOODS_POSTAGE:
            agree_user = "天猫客服"
            agree_refund_apply_time = refund_info.created
        elif "opRole:xiaoer" in refund_info.attribute:
            agree_user = "天猫客服"
            agree_refund_apply_time = refund_info.created

    data = TaobaoRefundInfo(
        refund_id=refund.refund_id,
        refund_reason=refund_info.reason,
        refund_desc=refund_info.desc,
        refund_fee=refund_info.refund_fee,
        refund_status=refund_info.status.value,
        refund_status_zh=refund_info.status.label,
        refund_created_time=refund_info.created,
        refund_logistics_company=refund_info.company_name,
        refund_invoice_no=refund_info.sid,
        tid=refund.tid,
        oid=refund_info.oid,
        buyer_nick=refund_info.buyer_nick,
        buyer_open_uid=refund_info.buyer_open_uid,
        order_status=refund_info.order_status.value,
        order_status_zh=refund_info.order_status.label,
        order_created=trade_info.created_at,
        order_paid=trade_info.paid_at,
        order_price=message_to_dict(trade_info)["price"],
        order_payment=trade_payment,
        goods_status=refund_info.good_status.value,
        goods_status_zh=refund_info.good_status.label,
        logistics_company=logistics_company,
        invoice_no=invoice_no,
        dispute_type=refund_info.dispute_type.value,
        dispute_type_zh=refund_info.dispute_type.label,
        divide_order_fee=divide_order_fee,
        outer_id=refund_info.outer_id,
        agree_refund_apply_time=agree_refund_apply_time,
        pay_and_refund_amount_diff=float(amount_diff(divide_order_fee, refund_info.refund_fee)),
        fs_trade_no=build_fs_trade_no_list(refund.tid, refund_info.oid),
        fs_product_list=build_fs_product_list(refund_info, current_order),
        is_refund_all=build_is_refund_all(trade_payment, main_order_after_sales_info),
        main_order_after_sales_info=main_order_after_sales_info,
        is_collected=is_collected,
        is_transported=is_transported,
        desensitization_address=build_desensitization_address(trade_info.receiver),
        agree_user=agree_user,
        is_agency_signed=is_agency_signed,
        is_signed=is_signed,
    )
    buyer_info = BuyerInfo(open_uid=refund_info.buyer_open_uid, nick=refund_info.buyer_nick)
    create_result = BusinessManager.create_bo_by_event(event, shop, data.dict(), buyer_info)
    return create_result


def get_data_fetcher(seller_nick, update_time_range: tuple[datetime, datetime]):
    from robot_processor.form.event.paginator import PaginateDataFetcher
    from robot_processor.refund.models import TaobaoRefund

    class TaobaoRefundDataFetcher(PaginateDataFetcher[TaobaoRefund]):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.seller_nick = seller_nick
            self.update_time_range = update_time_range

        def _do_fetch(self, page_no, page_size):
            from sqlalchemy import func
            from sqlalchemy import select
            from sqlalchemy.sql.operators import between_op

            sql = (
                select(TaobaoRefund)
                .select_from(TaobaoRefund)
                .where(
                    TaobaoRefund.seller_nick == self.seller_nick,
                    between_op(TaobaoRefund.jdp_modified, *update_time_range),
                )
                .limit(self.page_size)
                .offset(self.offset)
            )
            if self.total is None:
                self.total = db.session.execute(sql.with_only_columns(func.count())).scalar()
            refund_list = db.session.execute(sql).scalars().all()
            return refund_list

        def after_total_init_hook(self):
            logger.info(
                "淘宝店铺 {}~{} 共有 {} 条售后单".format(
                    self.update_time_range[0].strftime("%Y-%m-%d %H:%M:%S"),
                    self.update_time_range[1].strftime("%Y-%m-%d %H:%M:%S"),
                    self.total,
                )
            )

    return TaobaoRefundDataFetcher()


def build_fs_trade_no_list(tid, oid):
    return ComponentTradeNoList([ComponentTradeNo(tid=tid, oid=oid)])


def build_fs_product_list(refund_info: api_TaobaoRefundInfo, current_order: pb_Order):
    return ComponentProductList(
        [
            ComponentProduct(
                TID=refund_info.tid,
                OID=refund_info.oid,
                PICTURE=current_order.pic_path,
                TITLE=refund_info.title,
                DESCRIPTION=current_order.sku_description,
                SPU=refund_info.num_iid,
                SKU=current_order.sku_id,
                SKU_NAME=refund_info.sku,
                SPU_OUTER=refund_info.outer_id,
                SKU_OUTER=current_order.outer_sku_id,
                PRICE=refund_info.price,
                INVENTORY=None,
                PAYMENT=refund_info.payment,
                COMBINE=None,
                COUNT=refund_info.num,
                SHORT_TITLE="",
                BATCH=[],
            )
        ]
    )


def build_is_refund_all(trade_payment: float, after_sales_info: list[MainOrderAfterSalesInfo]):
    refund_fee_sum = Amount.sum([info.refund_fee for info in after_sales_info if info.status_zh != "退款关闭"]).format(
        precision=2
    )
    return Amount(trade_payment).format(2) == refund_fee_sum


def build_desensitization_address(participant: TradeInfo.Participant):
    if not participant:
        return ""
    desensitization_address = " ".join(
        [
            participant.state or "",
            participant.city or "",
            participant.district or "",
            participant.town or "",
            participant.address or "",
            participant.name or "",
            participant.phone or "",
        ]
    )
    return desensitization_address
