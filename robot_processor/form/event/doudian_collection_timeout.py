from decimal import Decimal

import arrow
import robot_types.helper
import robot_types.model
from leyan_proto.digismart.trade.dgt_dy_trade_pb2 import DyTradeInfo as pb_DyTradeInfo
from loguru import logger
from tcron_jobs import runner

from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.client import doudian_cloud
from robot_processor.client import trade_client
from robot_processor.client.errors import DoudianCloudServiceError
from robot_processor.ext import db
from robot_processor.form.event.models import EventConfig
from robot_processor.form.event.models import EventType
from robot_processor.logging import vars as log_vars
from robot_processor.shop.models import Shop
from robot_processor.t_cron import wrap_tcron_job


@runner.register
@wrap_tcron_job
def poll_doudian_shop_colection_timeout(shop_id: int) -> int:
    from robot_processor.form.event.models import EventType
    from robot_processor.shop.models import Shop

    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return -1
    event = EventConfig.get_by_id(EventType.DOUDIAN_COLLECTION_TIMEOUT)
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(EventType.DOUDIAN_COLLECTION_TIMEOUT)
    now = arrow.now()
    end_update_at = now.naive
    start_update_at = now.shift(days=-2).naive
    start_ts = int(start_update_at.timestamp())
    end_ts = int(end_update_at.timestamp())
    count = 0
    while start_ts < end_ts:
        next_ts = start_ts + 3600 if start_ts + 3600 <= end_ts else end_ts
        page_no = 1
        page_size = 50
        logger.info(f"start check: {start_ts}-{next_ts}")
        while True:
            pb_resp = trade_client.get_period_trade_list_by_page(
                page_no,
                page_size,
                start_ts,
                next_ts,
                platform="DOUDIAN",
                sid=shop.sid,
                seller_nick=shop.nick,
            )
            for trade in pb_resp.trade_info_list:
                if trade.dy_trade_info.order_status == 3 and trade.dy_trade_info.logistics_info:
                    process_one_trade(event, shop, trade.dy_trade_info)
                    count = count + 1
            page_no = page_no + 1
            logger.info(f"check page: {page_no}")
            if not pb_resp.has_more:
                start_ts = next_ts + 1
                break
    return count


def process_one_trade(event: EventType, shop: Shop, trade_info: pb_DyTradeInfo):
    try:
        logistics_info = trade_info.logistics_info[0]
        resp = doudian_cloud.query_traces(
            store_id=shop.sid, logistics_code=logistics_info.company, track_no=logistics_info.tracking_no
        )
        if any("已揽收" in route.state_description for route in resp.data.route__node_list):
            return
        logger.info(f"{resp}")
        ship_time = arrow.get(trade_info.ship_time)
        hours = Decimal(int((arrow.now() - ship_time).seconds / 3600))
        info = robot_types.model.event.doudian.collection_timeout.CollectionTimeout(
            fs_trade_no=[robot_types.model.component.TradeNo(trade_info.order_id)],
            logistics_company=logistics_info.company,
            logistics_no=logistics_info.tracking_no,
            hours=hours,
            receiver=robot_types.model.component.Address(
                name=trade_info.post_receiver,
                mobile=trade_info.post_tel,
                state=trade_info.post_addr.province.name,
                city=trade_info.post_addr.city.name,
                town=trade_info.post_addr.town.name,
                address=trade_info.post_addr.detail,
            ),
        )
        BusinessManager.create_bo_by_event(event, shop, robot_types.helper.serialize(info))
    except DoudianCloudServiceError as e:
        logger.warning(e)
