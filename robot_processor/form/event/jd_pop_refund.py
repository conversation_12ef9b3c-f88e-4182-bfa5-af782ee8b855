from decimal import Decimal

import arrow
import robot_types.helper
import robot_types.model
from dateutil import tz
from loguru import logger
from result import Err
from result import Result
from tcron_jobs import runner

from robot_processor.client import jd_lyzr_client
from robot_processor.client.jd import JdRefund
from robot_processor.db import db
from robot_processor.logging import vars as log_vars
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.utils import safe_datetime


@runner.register
@wrap_tcron_job
def poll_jd_pop_shop_refund(shop_id: int) -> int:
    from robot_processor.form.event.models import EventType
    from robot_processor.form.event.models import FormEventTCronSchedule
    from robot_processor.shop.models import Shop

    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return -1
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(EventType.JD_POP_REFUND)
    event_scheduler = FormEventTCronSchedule.get_by_shop_event(shop, EventType.JD_POP_REFUND)
    now = arrow.now()
    end_update_at = now.int_timestamp
    if now.shift(minutes=-30).naive > event_scheduler.executed_at:
        start_update_at = now.shift(minutes=-30).int_timestamp
    else:
        start_update_at = int(event_scheduler.executed_at.timestamp())
    refund_paginator = get_data_fetcher(
        sid=shop.sid,
        update_time_range=(start_update_at, end_update_at),
    )
    result: list[Result] = []
    for refund in refund_paginator.chain_fetch():
        refund_update_time = arrow.get(refund.modified).naive
        if refund_update_time > event_scheduler.executed_at:
            event_scheduler.mark_executed(refund_update_time)
        try:
            result.extend(create_bo(shop, refund))
        except Exception as e:
            logger.opt(exception=e).error(f"创建售后单失败 {e}")
            result.append(Err(e))
    if now.naive > event_scheduler.executed_at:
        event_scheduler.mark_executed(now.naive)
    logger.info(f"创建退款单 {len([r for r in result if r.is_ok()])} 条，结果 {result}")
    return refund_paginator.total


def create_bo(shop, jd_refund: JdRefund) -> list[Result]:
    from robot_processor.business_order.business_order_manager import BusinessManager
    from robot_processor.form.event.models import EventConfig
    from robot_processor.form.event.models import EventType

    log_vars.RefundId.set(jd_refund.id)
    event = EventConfig.get_by_id(EventType.JD_POP_REFUND)

    logger.info(f"处理京东退款单 {jd_refund.json()}")

    product_list: list[robot_types.model.component.Product] = []
    for sku in jd_refund.skuVoList:
        sku_resp = jd_lyzr_client.pop_sku_get(shop.sid, str(sku.skuId))
        product_list.append(
            robot_types.model.component.Product(
                TID=jd_refund.orderId,
                TITLE=sku_resp.sku.wareTitle,
                DESCRIPTION="",
                SPU=str(sku_resp.sku.wareId),
                SKU=str(sku.skuId),
                SKU_OUTER=sku_resp.sku.outerId,
                PAYMENT=sku_resp.sku.jdPrice / 100.0,
                COUNT=Decimal(sku.skuCount),
            )
        )

    agree_user = ""
    if jd_refund.checkUserName:
        agree_user = jd_refund.checkUserName
    if agree_user == "自动审核":
        agree_user = "系统"

    data = robot_types.model.event.jd.pop_refund.PopRefund(
        tid=[robot_types.model.component.TradeNo(tid=jd_refund.orderId)],
        id=str(jd_refund.id),
        buyerName=jd_refund.buyerName,
        checkTime=safe_datetime(jd_refund.checkTime),
        applyTime=safe_datetime(jd_refund.applyTime),
        applyRefundSum=Decimal(jd_refund.applyRefundSum / 100.0),
        checkUserName=jd_refund.checkUserName,
        checkRemark=jd_refund.checkRemark,
        reason=jd_refund.reason,
        modified=safe_datetime(jd_refund.modified),
        zh_status=convert_refund_status(jd_refund.status),
        zh_order_warehouse_status=convert_order_warehouse_status(jd_refund.orderWarehouseStatus),
        is_refund_all=jd_refund.partRefundType != 1,
        product_list=product_list,
        agree_user=agree_user,
    )
    create_result = BusinessManager.create_bo_by_event(event, shop, robot_types.helper.serialize(data))
    return create_result


def convert_refund_status(status: int) -> str:
    refund_status_map = {
        0: "未审核",
        1: "审核通过",
        2: "审核不通过",
        3: "京东财务审核通过",
        4: "京东财务审核不通过",
        5: "人工审核通过",
        6: "京东拦截并退款",
        7: "青龙拦截成功",
        8: "青龙拦截失败",
        9: "强制关单并退款",
        10: "物流待跟进(线下拦截)",
        11: "用户撤销",
        16: "拒收后退款",
        17: "协商退货退款",
        18: "协商关闭",
        19: "纠纷介入",
        27: "京东承诺拦截",
    }
    return refund_status_map.get(status, str(status))


def convert_order_warehouse_status(order_warehouse_status: int) -> str:
    order_warehouse_status_map = {1: "已出库", 2: "未出库"}
    return order_warehouse_status_map.get(order_warehouse_status, str(order_warehouse_status))


def get_data_fetcher(sid: str, update_time_range: tuple[int, int]):
    from robot_processor.client.jd import JdRefund
    from robot_processor.form.event.paginator import PaginateDataFetcher

    class JdRefundDataFetcher(PaginateDataFetcher[JdRefund]):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.sid = sid
            self.update_time_range = update_time_range
            self.page_size = 50

        def _do_fetch(self, page_no, page_size):
            from robot_processor.client import jd_lyzr_client

            response = jd_lyzr_client.pop_refund_list(
                store_id=self.sid,
                modified_start=arrow.get(self.update_time_range[0], tzinfo=tz.gettz("Asia/Shanghai")).format(
                    "YYYY-MM-DD HH:mm:ss"
                ),
                modified_end=arrow.get(self.update_time_range[1], tzinfo=tz.gettz("Asia/Shanghai")).format(
                    "YYYY-MM-DD " "HH:mm:ss"
                ),
                page_index=page_no,
                page_size=page_size,
            )
            self.total = response.queryResult.totalCount
            return response.queryResult.result

        def after_total_init_hook(self):
            logger.info(
                "京东店铺 {}~{} 共有 {} 条退款单".format(
                    arrow.get(self.update_time_range[0]).format("YYYY-MM-DD HH:mm:ss"),
                    arrow.get(self.update_time_range[1]).format("YYYY-MM-DD HH:mm:ss"),
                    self.total,
                )
            )

    return JdRefundDataFetcher()
