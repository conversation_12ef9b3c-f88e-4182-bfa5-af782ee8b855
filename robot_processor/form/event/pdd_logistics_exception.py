from decimal import Decimal
from typing import List

import arrow
import robot_types.helper
import robot_types.model
from leyan_proto.digismart.trade.dgt_trade_common_pb2 import PddItem
from leyan_proto.digismart.trade.dgt_trade_common_pb2 import PddTradeInfo as pb_PddTradeInfo
from loguru import logger
from tcron_jobs import runner

from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.client import pdd_bridge_client
from robot_processor.client import trade_client
from robot_processor.client.schema import PddRefundStatusCheckPayload
from robot_processor.ext import db
from robot_processor.form.event.models import EventConfig
from robot_processor.form.event.models import EventType
from robot_processor.logging import vars as log_vars
from robot_processor.shop.models import Shop
from robot_processor.symbol_table.named_typespec.component import ComponentProduct
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.trace.pdd_trace import PddTrace
from robot_processor.trace.pdd_trace import PddTraceAction
from robot_processor.utils import get_time_spent_hours
from robot_processor.utils import safe_datetime


@runner.register
@wrap_tcron_job
def poll_pdd_shop_logisitcs_exception(shop_id: int) -> int:
    from robot_processor.form.event.models import EventType
    from robot_processor.shop.models import Shop

    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return -1
    event = EventConfig.get_by_id(EventType.PDD_LOGISITCS_EXCEPTION)

    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(EventType.PDD_LOGISITCS_EXCEPTION)
    now = arrow.now()
    end_update_at = now.naive
    start_update_at = now.shift(days=-7).naive
    start_ts = int(start_update_at.timestamp())
    end_ts = int(end_update_at.timestamp())
    count = 0

    while start_ts < end_ts:
        next_ts = start_ts + 180 if start_ts + 180 <= end_ts else end_ts
        page_no = 1
        page_size = 100
        logger.info(f"start check: {start_ts}-{next_ts}")
        while True:
            pb_resp = trade_client.get_period_trade_list_by_page(
                page_no,
                page_size,
                start_ts,
                next_ts,
                platform="PDD",
                sid=shop.sid,
                seller_nick=shop.nick,
            )
            for trade in pb_resp.trade_info_list:
                if (
                    trade.pdd_trade_info.confirm_status == 1
                    and trade.pdd_trade_info.logistics_info
                    and trade.pdd_trade_info.logistics_info.tracking_number
                ) or (
                    trade.pdd_trade_info.pay_time
                    and not trade.pdd_trade_info.shipping_time
                    and arrow.get(trade.pdd_trade_info.pay_time, tzinfo="Asia/Shanghai").shift(hours=8) < now
                ):
                    # 未发货超时先剪枝掉8小时之内的
                    process_one_logistics(event, shop, trade.pdd_trade_info)
                    count = count + 1
            page_no = page_no + 1
            logger.info(f"check page: {page_no}")
            if not pb_resp.has_more:
                start_ts = next_ts + 1
                break
    return count


def process_one_logistics(event: EventType, shop: Shop, trade_info: pb_PddTradeInfo):
    sent_collection_timeout_hours = Decimal(0)
    paid_collection_timeout_hours = Decimal(0)
    only_collection_stall_timeout_hours = Decimal(0)
    transport_stall_timeout_hours = Decimal(0)
    delivery_stall_timeout_hours = Decimal(0)
    paid_sent_timeout_hours = Decimal(0)
    traces: List[PddTrace] = []
    refund_status_resp = pdd_bridge_client.refund_status_check(
        store_id=shop.sid, payload=PddRefundStatusCheckPayload(order_sns=trade_info.trade_id)
    )
    has_refund = len(refund_status_resp.refund_status_check_response.order_sns_exists_refund) > 0
    pay_time = arrow.get(trade_info.pay_time, tzinfo="Asia/Shanghai").naive

    if trade_info.shipping_time:
        logistics_no = trade_info.logistics_info.tracking_number
        ship_time = arrow.get(trade_info.shipping_time, tzinfo="Asia/Shanghai").naive
        traces = PddTrace.get_traces_by_tracking_number(logistics_no)

        if not PddTraceAction.check_if_traces_valid(traces):
            logger.warning(f"invalid logistics: {logistics_no}")
            return
        # 拼多多物流轨迹在 2025-05-06 12:00:00 时恢复，在此之前已发货且物流轨迹为空的，也认为是无效物流轨迹
        if not traces:
            barrier = arrow.get("2025-05-06 12:00:00", tzinfo="Asia/Shanghai")
            if arrow.get(trade_info.shipping_time, tzinfo="Asia/Shanghai") < barrier:
                logger.warning(f"invalid logistics: {logistics_no}")
                return
            if trade_info.order_status == 3:  # 已签收
                logger.warning(f"invalid logistics: {logistics_no}")
                return

        if PddTraceAction.has_got_action(traces):
            latest_time = arrow.get(traces[0].status_time, tzinfo="Asia/Shanghai").naive
            if PddTraceAction.check_only_collected(traces):
                only_collection_stall_timeout_hours = Decimal(get_time_spent_hours(latest_time))
            elif PddTraceAction.check_in_transport(traces) and not PddTraceAction.has_send_action(traces):
                transport_stall_timeout_hours = Decimal(get_time_spent_hours(latest_time))
            elif PddTraceAction.check_in_send(traces):
                delivery_stall_timeout_hours = Decimal(get_time_spent_hours(latest_time))
            else:
                return
        else:
            sent_collection_timeout_hours = Decimal(get_time_spent_hours(ship_time))
            paid_collection_timeout_hours = Decimal(get_time_spent_hours(pay_time))
    else:
        paid_sent_timeout_hours = Decimal(get_time_spent_hours(pay_time))
    info = robot_types.model.event.pdd.logistics_exception.LogisticsException(
        fs_trade_no=[robot_types.model.component.TradeNo(tid=trade_info.trade_id)],
        logistics_company=trade_info.logistics_info.logistics_name,
        logistics_no=trade_info.logistics_info.tracking_number,
        sent_collection_timeout_hours=sent_collection_timeout_hours,
        paid_collection_timeout=paid_collection_timeout_hours,
        only_collection_stall_timeout_hours=only_collection_stall_timeout_hours,
        transport_stall_timeout_hours=transport_stall_timeout_hours,
        delivery_stall_timeout_hours=delivery_stall_timeout_hours,
        paid_sent_timeout_hours=paid_sent_timeout_hours,
        has_refund=has_refund,
        number_of_logistics_record=Decimal(len(traces)),
        receiver=robot_types.model.component.Address(
            name=trade_info.logistics_info.receiver_name,
            mobile=trade_info.logistics_info.receiver_phone,
            state=trade_info.logistics_info.province,
            city=trade_info.logistics_info.city,
            town=trade_info.logistics_info.town,
            address=trade_info.logistics_info.address,
        ),
        pay_time=safe_datetime(trade_info.pay_time),
        shipping_time=safe_datetime(trade_info.shipping_time),
        order_status_zh=trade_client.to_pdd_order_status_zh(trade_info.order_status),
        fs_product_list=build_fs_product_list(trade_info.trade_id, list(trade_info.item_list)),
    )

    BusinessManager.create_bo_by_event(event, shop, robot_types.helper.serialize(info))


def build_fs_product_list(tid: str, orders: List[PddItem]):
    products = []
    for order in orders:
        products.append(
            ComponentProduct(
                TID=tid,
                OID=None,
                PICTURE=order.goods_img,
                TITLE=order.goods_name,
                DESCRIPTION=order.goods_spec,
                SPU=str(order.goods_id),
                SKU=order.sku_id,
                SKU_NAME=None,
                SPU_OUTER=order.outer_goods_id,
                SKU_OUTER=order.outer_id,
                PRICE=order.goods_price,
                INVENTORY=None,
                PAYMENT=None,
                COMBINE=None,
                COUNT=order.goods_count,
                SHORT_TITLE="",
                BATCH=[],
            )
        )
    return products
