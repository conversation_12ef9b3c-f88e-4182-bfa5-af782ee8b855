from typing import List

import arrow
from leyan_proto.digismart.trade.dgt_dy_trade_pb2 import <PERSON>kuOrder
from pydantic import BaseModel
from tcron_jobs import runner

from robot_processor.business_order.business_order_manager import \
    BusinessManager
from robot_processor.client import trade_client
from robot_processor.ext import db
from robot_processor.form.event.models import EventConfig
from robot_processor.logging import vars as log_vars
from robot_processor.symbol_table.named_typespec.component import \
    ComponentProduct
from robot_processor.symbol_table.named_typespec.component import \
    ComponentProductList
from robot_processor.symbol_table.named_typespec.component import \
    ComponentTradeNo
from robot_processor.symbol_table.named_typespec.component import \
    ComponentTradeNoList
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.utils import make_fields_optional


@make_fields_optional
class DoudianTradeInfo(BaseModel):
    memo: str
    fs_trade_no: ComponentTradeNoList
    fs_product_list: ComponentProductList
    p_id: str  # 订单号


@runner.register
@wrap_tcron_job
def poll_doudian_shop_trade(shop_id: int) -> int:
    from robot_processor.form.event.models import EventType
    from robot_processor.form.event.models import FormEventTCronSchedule
    from robot_processor.shop.models import Shop

    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return -1
    event = EventConfig.get_by_id(EventType.DOUDIAN_TRADE)
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(EventType.DOUDIAN_TRADE)
    event_scheduler = FormEventTCronSchedule.get_by_shop_event(
        shop, EventType.DOUDIAN_TRADE
    )
    now = arrow.now()
    end_update_at = now.naive
    if now.shift(minutes=-30).naive > event_scheduler.executed_at:
        start_update_at = now.shift(minutes=-30).naive
    else:
        start_update_at = event_scheduler.executed_at
    count = 0
    page_no = 1
    page_size = 100
    while True:
        pb_resp = trade_client.get_period_trade_list_by_page(
            page_no,
            page_size,
            int(start_update_at.timestamp()),
            int(end_update_at.timestamp()),
            platform="DOUDIAN",
            sid=shop.sid,
            seller_nick=shop.nick,
        )
        for trade in pb_resp.trade_info_list:
            trade_info = DoudianTradeInfo(
                fs_trade_no=build_fs_trade_no_list(trade.dy_trade_info.order_id),
                memo=trade.dy_trade_info.seller_words,
                fs_product_list=build_fs_product_list(
                    trade.dy_trade_info.order_id, trade.dy_trade_info.sku_order_list
                ),
                p_id=trade.dy_trade_info.order_id,
            )
            BusinessManager.create_bo_by_event(event, shop, trade_info.dict())
            count = count + 1
        page_no = page_no + 1
        if not pb_resp.has_more:
            break
    return count


def build_fs_trade_no_list(tid):
    return ComponentTradeNoList([ComponentTradeNo(tid=tid, oid=None)])


def build_fs_product_list(tid: str, orders: List[SkuOrder]):
    products = []
    for order in orders:
        products.append(
            ComponentProduct(
                TID=tid,
                OID=order.sku_order_id,
                PICTURE=None,
                TITLE=order.product_name,
                DESCRIPTION="",
                SPU=order.product_id,
                SKU=order.sku_id,
                SKU_NAME="",
                SPU_OUTER=order.out_product_id,
                SKU_OUTER=order.out_sku_id,
                PRICE=round(order.goods_price / 100, 2),
                INVENTORY=None,
                PAYMENT=round(order.pay_amount / 100, 2),
                COMBINE=None,
                COUNT=order.item_num,
                SHORT_TITLE="",
                BATCH=[],
            )
        )
    return ComponentProductList(products)
