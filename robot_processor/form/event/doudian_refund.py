from typing import Optional

import arrow
from leyan_proto.digismart.trade.dgt_dy_trade_pb2 import PostAddress
from loguru import logger
from pydantic import BaseModel
from result import Err
from result import Result
from tcron_jobs import runner

from robot_processor.client import doudian_cloud
from robot_processor.client import trade_client
from robot_processor.client.doudian import DoudianAfterSalesOrderInfo
from robot_processor.client.schema import DoudianAfterSale
from robot_processor.db import db
from robot_processor.logging import vars as log_vars
from robot_processor.symbol_table.named_typespec.component import ComponentFile
from robot_processor.symbol_table.named_typespec.component import \
    ComponentFileList
from robot_processor.symbol_table.named_typespec.component import \
    ComponentProduct
from robot_processor.symbol_table.named_typespec.component import \
    ComponentProductList
from robot_processor.symbol_table.named_typespec.component import \
    ComponentTradeNo
from robot_processor.symbol_table.named_typespec.component import \
    ComponentTradeNoList
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.utils import amount_diff
from robot_processor.utils import empty_str_as_none
from robot_processor.utils import make_fields_optional
from robot_processor.utils import message_to_dict

TIME_ZONE = "Asia/Shanghai"


@make_fields_optional
class Logistics(BaseModel):
    logistics_time: str
    logistics_state: str
    company_name: str
    tracking_no: str


@make_fields_optional
class MainOrderAfterSalesInfo(BaseModel):
    """主订单所有的售后信息"""

    after_sales_id: str  # 售后单号
    sku_order_id: str  # 子订单号
    refund_amount: float  # 售后退款金额
    refund_post_amount: float  # 售后退运费金额
    after_sales_status_zh: str
    after_sales_type_zh: str


def build_main_order_after_sales_info(sid: str, tid):
    orders = doudian_cloud.get_aftersale_list(
        store_id=sid, order_id=str(tid), size=100
    ).items
    after_sales_list = [
        MainOrderAfterSalesInfo(
            after_sales_id=order.aftersale_info.aftersale_id,
            sku_order_id=order.order_info.related_order_info[0].sku_order_id,
            refund_amount=round(order.aftersale_info.refund_amount / 100, 2),
            refund_post_amount=round(order.aftersale_info.refund_post_amount / 100, 2),
            after_sales_status_zh=convert_after_sales_status(
                order.aftersale_info.aftersale_status
            ),
            after_sales_type_zh=convert_after_sales_type(
                order.aftersale_info.aftersale_type
            ),
        )
        for order in orders
    ]
    return after_sales_list


@make_fields_optional
class RelatedOrderInfo(BaseModel):
    sku_order_id: str
    order_status_zh: str
    pay_amount: float
    shop_sku_code: str


@make_fields_optional
class DoudianRefundInfo(BaseModel):
    refund_id: str  # 售后单号
    after_sales_type: int  # 售后类型
    after_sales_type_zh: str
    after_sales_status: int  # 售后状态
    after_sales_status_zh: str
    refund_reason: str  # 退款原因
    refund_reason_remark: str  # 退款描述
    refund_total_amount: float  # 售后总金额（含运费）
    refund_apply_time: str  # 申请时间
    refund_logistics_name: str  # 退货物流公司
    refund_tracking_no: str  # 退货物流单号
    refund_apply_role: int  # 售后申请角色
    refund_apply_role_zh: str
    refund_status: int  # 退款状态
    refund_status_zh: str
    refund_refund_time: Optional[str]  # 退款时间

    p_id: str  # 订单号
    order_pay_amount: float  # 主订单实付金额
    s_id: str  # 子订单号
    order_status: int  # 订单状态
    order_status_zh: str
    order_paid: Optional[str]
    got_pkg: int  # 货物状态
    got_pkg_zh: str
    logistics_name: str  # 物流公司
    tracking_no: str  # 物流单号
    pay_amount: float  # 实付金额
    shop_sku_code: str  # 商家sku自定义编码
    order_logistics_info: list[Logistics]  # 正向物流信息
    main_order_after_sales_info: list[MainOrderAfterSalesInfo]  # 主订单所有的售后信息
    related_order_info: list[RelatedOrderInfo]
    is_refund_all: bool  # 是否整单退款
    agree_user: str  # 同意退款人

    evidence: ComponentFileList  # 退款凭证
    pay_and_refund_amount_diff: float
    fs_product_list: ComponentProductList
    fs_trade_no: ComponentTradeNoList
    is_collected: bool  # 是否已揽收
    is_transported: bool  # 是否已经运输
    desensitization_address: str  # 脱敏收件人信息


@runner.register
@wrap_tcron_job
def poll_doudian_shop_refund(shop_id: int) -> int:
    import arrow

    from robot_processor.form.event.models import EventType
    from robot_processor.form.event.models import FormEventTCronSchedule
    from robot_processor.shop.models import Shop

    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return -1
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(EventType.DOUDIAN_REFUND)
    event_scheduler = FormEventTCronSchedule.get_by_shop_event(
        shop, EventType.DOUDIAN_REFUND
    )
    now = arrow.now()
    # 抖店售后单列表接口存在数据同步延迟
    end_update_at = now.shift(seconds=-30).int_timestamp
    if now.shift(minutes=-30).naive > event_scheduler.executed_at:
        start_update_at = now.shift(minutes=-30).int_timestamp
    else:
        start_update_at = int(event_scheduler.executed_at.timestamp())

    refund_paginator = get_data_fetcher(
        shop.sid,
        (start_update_at, end_update_at),
    )
    result: list[Result] = []
    for refund in refund_paginator.chain_fetch():
        refund_update_time = arrow.get(refund.aftersale_info.update_time).naive
        if refund_update_time > event_scheduler.executed_at:
            event_scheduler.mark_executed(refund_update_time)
        try:
            result.extend(create_bo(shop, refund))
        except Exception as e:
            logger.opt(exception=e).error(f"创建售后单失败 {e}")
            result.append(Err(e))
    logger.info(
        f"创建售后单 {len([r for r in result if r.is_ok()])} 条，结果: {result}"
    )
    return refund_paginator.total


def create_bo(shop, refund_list_info: DoudianAfterSale) -> list[Result]:
    from robot_processor.business_order.business_order_manager import \
        BusinessManager
    from robot_processor.form.event.models import EventConfig
    from robot_processor.form.event.models import EventType

    log_vars.RefundId.set(refund_list_info.aftersale_info.aftersale_id)
    date_fmt = "YYYY-MM-DD HH:mm:ss"
    event = EventConfig.get_by_id(EventType.DOUDIAN_REFUND)
    refund_detail_info = doudian_cloud.get_aftersale_detail(
        store_id=shop.sid,
        after_sale_id=int(refund_list_info.aftersale_info.aftersale_id),
        need_operation_record=True,
    )
    after_sales_basic = refund_list_info.aftersale_info
    after_sales = refund_detail_info.process_info.after_sale_info
    logistics_info = refund_detail_info.process_info.logistics_info
    order_info = refund_detail_info.order_info
    # 选取第一个子订单的信息，提供一个快捷入口
    current_order_info = order_info.sku_order_infos[0]
    log_vars.Tid.set(current_order_info.sku_order_id)
    if order_list := trade_client.get_trade_by_tid_and_channel(
        [str(order_info.shop_order_id)], "DOUDIAN"
    ).trade_info_list:
        main_order_info = message_to_dict(order_list[0].dy_trade_info)
        order_pay_amount = round(main_order_info["pay_amount"] / 100, 2)
    else:
        return [Err(f"未找到主订单信息 {order_info.shop_order_id}")]

    # 正向物流
    logistics_list: list[Logistics] = []
    is_collected = True if logistics_info.order else False
    is_transported = (
        True
        if logistics_info.order
        and logistics_info.order[0].logistics_state in [0, 3, 4, 5, 6, 7]
        else False
    )
    for logistics_info in logistics_info.order:
        logistics_list.append(
            Logistics(
                logistics_time=arrow.get(
                    logistics_info.logistics_time, tzinfo=TIME_ZONE
                ).format(date_fmt),
                logistics_state=convert_logistics_state(logistics_info.logistics_state),
                company_name=logistics_info.company_name,
                tracking_no=logistics_info.tracking_no,
            )
        )
    if logistics_list:
        primary_logistics_company = logistics_list[0].company_name
        primary_tracking_no = logistics_list[0].tracking_no
    else:
        primary_logistics_company = primary_tracking_no = None  # type: ignore[assignment]
    main_order_after_sales_info = build_main_order_after_sales_info(
        shop.sid, order_info.shop_order_id
    )

    agree_user = ""
    if refund_detail_info.process_info.record_logs_list:
        for record in refund_detail_info.process_info.record_logs_list:
            if "同意退款" in record.text or "同意退款" in record.action:
                agree_user = record.operator
                break

    logger.info(f"处理抖店售后单 {refund_list_info.json()}")
    data = DoudianRefundInfo(
        refund_id=after_sales.after_sale_id,
        after_sales_status=after_sales.after_sale_status,
        after_sales_status_zh=convert_after_sales_status(after_sales.after_sale_status),
        after_sales_type=after_sales.after_sale_type,
        after_sales_type_zh=convert_after_sales_type(after_sales.after_sale_type),
        refund_reason=after_sales.reason,
        refund_reason_remark=after_sales.reason_remark,
        refund_total_amount=round(after_sales.refund_total_amount / 100, 2),
        refund_apply_time=arrow.get(after_sales.apply_time, tzinfo=TIME_ZONE).format(
            date_fmt
        ),
        refund_refund_time=(
            None
            if not after_sales.refund_time
            else arrow.get(after_sales.refund_time, tzinfo=TIME_ZONE).format(date_fmt)
        ),
        refund_logistics_name=(
            after_sales_basic.return_logistics_company_name
            if after_sales_basic.return_logistics_company_name
            else None
        ),
        refund_tracking_no=(
            after_sales_basic.return_logistics_code
            if after_sales_basic.return_logistics_code
            else None
        ),
        refund_apply_role=after_sales.apply_role,
        refund_apply_role_zh=convert_apply_role(after_sales.apply_role),
        refund_status=after_sales.refund_status,
        refund_status_zh=convert_refund_status(after_sales.refund_status),
        p_id=order_info.shop_order_id,
        order_paid=order_list[0].dy_trade_info.pay_time,
        got_pkg=after_sales.got_pkg,
        got_pkg_zh=convert_got_pkg(after_sales.got_pkg),
        logistics_name=empty_str_as_none(primary_logistics_company),
        tracking_no=empty_str_as_none(primary_tracking_no),
        order_logistics_info=logistics_list,
        s_id=current_order_info.sku_order_id,
        order_status=current_order_info.order_status,
        order_status_zh=convert_order_status_zh(current_order_info.order_status),
        pay_amount=round(current_order_info.pay_amount / 100, 2),
        shop_sku_code=current_order_info.shop_sku_code,
        evidence=ComponentFileList(
            [
                ComponentFile(fileName="", url=file_url)
                for file_url in after_sales.evidence
            ]
        ),
        pay_and_refund_amount_diff=amount_diff(
            round(current_order_info.pay_amount / 100, 2),
            round(after_sales.refund_total_amount / 100, 2),
        ),
        fs_product_list=convert_product_list(order_info),
        fs_trade_no=convert_fs_trade(order_info),
        main_order_after_sales_info=main_order_after_sales_info,
        related_order_info=[
            RelatedOrderInfo(
                sku_order_id=sku_order.sku_order_id,
                order_status_zh=convert_order_status_zh(sku_order.order_status),
                pay_amount=round(sku_order.pay_amount / 100, 2),
                shop_sku_code=sku_order.shop_sku_code,
            )
            for sku_order in order_info.sku_order_infos
        ],
        order_pay_amount=order_pay_amount,
        is_refund_all=build_is_refund_all(
            order_pay_amount, main_order_after_sales_info
        ),
        is_collected=is_collected,
        is_transported=is_transported,
        desensitization_address=build_desensitization_address(
            order_list[0].dy_trade_info.post_addr
        ),
        agree_user=agree_user,
    )
    create_result = BusinessManager.create_bo_by_event(event, shop, data.dict())
    return create_result


def get_data_fetcher(sid: str, update_time_range: tuple[int, int]):
    from robot_processor.client.schema import DoudianAfterSale
    from robot_processor.form.event.paginator import PaginateDataFetcher

    class DoudianRefundDataFetcher(PaginateDataFetcher[DoudianAfterSale]):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.sid = sid
            self.update_time_range = update_time_range

        def _do_fetch(self, page_no, page_size):
            from robot_processor.client import doudian_cloud

            response = doudian_cloud.get_aftersale_list(
                store_id=self.sid,
                update_start_time=self.update_time_range[0],
                update_end_time=self.update_time_range[1],
                page=page_no,
                size=page_size,
            )
            self.total = response.total
            return response.items

        def after_total_init_hook(self):
            logger.info(
                "抖店店铺 {}~{} 共有 {} 条售后单".format(
                    arrow.get(self.update_time_range[0], tzinfo=TIME_ZONE).format(
                        "YYYY-MM-DD HH:mm:ss"
                    ),
                    arrow.get(self.update_time_range[1], tzinfo=TIME_ZONE).format(
                        "YYYY-MM-DD HH:mm:ss"
                    ),
                    self.total,
                )
            )

    return DoudianRefundDataFetcher()


def convert_after_sales_type(after_sales_type: int) -> str:
    after_sales_type_map = {
        0: "售后退货退款",
        1: "售后仅退款",
        2: "发货前退款",
        3: "换货",
        4: "系统取消",
        5: "用户取消",
        6: "价保",
        7: "补寄",
        8: "维修",
    }
    return after_sales_type_map.get(after_sales_type, str(after_sales_type))


def convert_after_sales_status(after_sales_status: int) -> str:
    after_sales_status_map = {
        6: "售后申请",
        7: "售后退货中",
        8: "售后待商家发货",
        11: "售后已发货",
        12: "售后成功",
        13: "售后商家已发货，待用户收货",
        14: "售后用户已收货",
        27: "拒绝售后申请",
        # 发货前商家拒绝售后 or 买家/客服取消售后 or 系统关单都会到达28
        # 但是发货后商家拒绝不会到达28，都是买家/客服取消 or 系统关单
        28: "售后失败",
        29: "售后退货拒绝",
        51: "订单取消成功",
        53: "逆向交易已完成",
    }
    return after_sales_status_map.get(after_sales_status, str(after_sales_status))


def convert_got_pkg(got_pkg: int) -> str:
    got_pkg_map = {
        0: "未收到货",
        1: "已收到货",
    }
    return got_pkg_map.get(got_pkg, str(got_pkg))


def convert_order_status_zh(order_status: int) -> str:
    """reference: https://op.jinritemai.com/docs/guide-docs/205/468"""
    order_status_map = {
        1: "待确认/待支付",
        103: "部分支付",
        105: "已支付",
        2: "备货中（待发货）",
        101: "部分发货",
        3: "已发货",
        4: "已关闭",
        5: "已完成",
        21: "发货前退款完结",
        22: "发货后退款完结",
        39: "收货后退款完结",
    }
    return order_status_map.get(order_status, str(order_status))


def convert_logistics_state(logistics_state: int) -> str:
    logistics_state_map = {
        0: "在途中",
        1: "已揽收",
        2: "疑难/终止揽收",
        3: "已签收",
        4: "退签",
        5: "派件中",
        6: "退回",
        7: "转单",
        8: "取消",
    }
    return logistics_state_map.get(logistics_state, str(logistics_state))


def convert_product_list(order_info: DoudianAfterSalesOrderInfo):
    return ComponentProductList(
        [
            ComponentProduct(
                TID=order_info.shop_order_id,
                OID=sku_order.sku_order_id,
                PICTURE=sku_order.product_image,
                TITLE=sku_order.product_name,
                DESCRIPTION="",
                SPU=sku_order.product_id,
                SKU=sku_order.sku_id,
                SPU_OUTER="",
                SKU_OUTER=sku_order.shop_sku_code,
                SKU_NAME="",
                PRICE=None,
                INVENTORY=None,
                PAYMENT=round(sku_order.sku_pay_amount / 100, 2),
                COMBINE=None,
                COUNT=sku_order.after_sale_item_count,
                SHORT_TITLE="",
            )
            for sku_order in order_info.sku_order_infos
        ]
    )


def convert_fs_trade(order_info: DoudianAfterSalesOrderInfo):
    return ComponentTradeNoList(
        [
            ComponentTradeNo(tid=order_info.shop_order_id, oid=sku_order.sku_order_id)
            for sku_order in order_info.sku_order_infos
        ]
    )


def convert_apply_role(apply_role: int) -> str:
    apply_role_map = {
        1: "买家",
        2: "商家",
        3: "客服",
        4: "系统",
    }
    return apply_role_map.get(apply_role, str(apply_role))


def convert_refund_status(refund_status: int) -> str:
    refund_status_map = {
        1: "待退款",
        2: "退款中",
        3: "退款成功",
        4: "退款失败",
        5: "追缴成功",
    }
    return refund_status_map.get(refund_status, str(refund_status))


def build_is_refund_all(
    order_pay_amount: float, after_sales_list: list[MainOrderAfterSalesInfo]
):
    refund_amount = sum(
        [
            order.refund_amount
            for order in after_sales_list
            if order.after_sales_status_zh not in ["订单取消成功", "售后失败"]
        ]
    )
    refund_post_amount = sum(
        [
            order.refund_post_amount
            for order in after_sales_list
            if order.after_sales_status_zh not in ["订单取消成功", "售后失败"]
        ]
    )
    return refund_amount + refund_post_amount == order_pay_amount


def build_desensitization_address(post_address: PostAddress):
    if not post_address:
        return ""
    desensitization_address = " ".join(
        [
            post_address.province.name or "",
            post_address.city.name or "",
            post_address.town.name or "",
            post_address.street.name or "",
        ]
    )
    return desensitization_address
