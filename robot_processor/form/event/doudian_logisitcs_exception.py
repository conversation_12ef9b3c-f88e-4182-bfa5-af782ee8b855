from decimal import Decimal
from typing import List

import arrow
import robot_types.helper
import robot_types.model
from flask import current_app
from leyan_proto.digismart.trade.dgt_dy_trade_pb2 import DyLogisticsInfo as pb_DyLogisticsInfo
from leyan_proto.digismart.trade.dgt_dy_trade_pb2 import DyTradeInfo as pb_DyTradeInfo
from leyan_proto.digismart.trade.dgt_dy_trade_pb2 import <PERSON><PERSON><PERSON>rder
from loguru import logger
from tcron_jobs import runner

from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.client import doudian_cloud
from robot_processor.client import trade_client
from robot_processor.client.doudian import DoudianTraceAction
from robot_processor.client.errors import DoudianCloudServiceError
from robot_processor.ext import db
from robot_processor.form.event.models import EventConfig
from robot_processor.form.event.models import EventType
from robot_processor.logging import vars as log_vars
from robot_processor.shop.models import Shop
from robot_processor.symbol_table.named_typespec.component import ComponentProduct
from robot_processor.t_cron import wrap_tcron_job
from robot_processor.utils import get_time_spent_hours


@runner.register
@wrap_tcron_job
def poll_doudian_shop_logisitcs_exception(shop_id: int) -> int:
    from robot_processor.form.event.models import EventType
    from robot_processor.shop.models import Shop

    shop = db.session.get(Shop, shop_id)
    if shop is None or shop.deleted:
        return -1
    event = EventConfig.get_by_id(EventType.DOUDIAN_LOGISITCS_EXCEPTION)

    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set(EventType.DOUDIAN_LOGISITCS_EXCEPTION)
    now = arrow.now()
    end_update_at = now.naive
    start_update_at = now.shift(days=-7).naive
    start_ts = int(start_update_at.timestamp())
    end_ts = int(end_update_at.timestamp())
    count = 0
    waybill_sid = shop.sid
    main_willbill_sids = current_app.config.get("DOUDIAN_MAIN_WILLBILL_SIDS", {})
    if shop.org_id in main_willbill_sids:  # 电子面单混用，一般情况下会有一个主店铺
        waybill_sid = main_willbill_sids[shop.org_id]

    while start_ts < end_ts:
        next_ts = start_ts + 180 if start_ts + 180 <= end_ts else end_ts
        page_no = 1
        page_size = 100
        logger.info(f"start check: {start_ts}-{next_ts}")
        while True:
            pb_resp = trade_client.get_period_trade_list_by_page(
                page_no,
                page_size,
                start_ts,
                next_ts,
                platform="DOUDIAN",
                sid=shop.sid,
                seller_nick=shop.nick,
            )
            for trade in pb_resp.trade_info_list:
                if trade.dy_trade_info.order_status in [105, 2, 101]:
                    pay_time = arrow.get(trade.dy_trade_info.pay_time, tzinfo="Asia/Shanghai")
                    paid_sent_timeout_hours = Decimal(int((arrow.now() - pay_time).total_seconds() / 3600))
                    info = robot_types.model.event.doudian.logistics_exception.LogisticsExceptionInfo(
                        fs_trade_no=[robot_types.model.component.TradeNo(trade.dy_trade_info.order_id)],
                        sent_collection_timeout_hours=Decimal(0),
                        paid_collection_timeout=Decimal(0),
                        only_collection_stall_timeout_hours=Decimal(0),
                        transport_stall_timeout_hours=Decimal(0),
                        delivery_stall_timeout_hours=Decimal(0),
                        paid_sent_timeout_hours=paid_sent_timeout_hours,
                        number_of_logistics_record=Decimal(0),
                        receiver=robot_types.model.component.Address(
                            name=trade.dy_trade_info.post_receiver,
                            mobile=trade.dy_trade_info.post_tel,
                            state=trade.dy_trade_info.post_addr.province.name,
                            city=trade.dy_trade_info.post_addr.city.name,
                            town=trade.dy_trade_info.post_addr.town.name,
                            address=trade.dy_trade_info.post_addr.detail,
                        ),
                    )
                    BusinessManager.create_bo_by_event(event, shop, robot_types.helper.serialize(info))
                elif trade.dy_trade_info.order_status == 3 and trade.dy_trade_info.logistics_info:
                    for logistics_info in trade.dy_trade_info.logistics_info:
                        process_one_logistics(event, shop, trade.dy_trade_info, logistics_info, waybill_sid)
                    count = count + 1
            page_no = page_no + 1
            logger.info(f"check page: {page_no}")
            if not pb_resp.has_more:
                start_ts = next_ts + 1
                break
    return count


def process_one_logistics(
    event: EventType, shop: Shop, trade_info: pb_DyTradeInfo, logistics_info: pb_DyLogisticsInfo, waybill_sid: str
):
    try:
        ship_time = arrow.get(logistics_info.ship_time, tzinfo="Asia/Shanghai").naive
        pay_time = arrow.get(trade_info.pay_time, tzinfo="Asia/Shanghai").naive
        only_collection_stall_timeout_hours = Decimal(0)
        sent_collection_timeout_hours = Decimal(0)
        paid_collection_timeout_hours = Decimal(0)
        transport_stall_timeout_hours = Decimal(0)
        delivery_stall_timeout_hours = Decimal(0)
        resp = doudian_cloud.query_traces(
            store_id=waybill_sid, logistics_code=logistics_info.company, track_no=logistics_info.tracking_no
        )
        if resp.data.route__node_list is None:
            logger.warning(resp)
            return

        if DoudianTraceAction.has_got_action(resp.data.route__node_list):
            latest_time = arrow.get(int(resp.data.route__node_list[0].timestamp), tzinfo="Asia/Shanghai").naive
            if DoudianTraceAction.only_got_action(resp.data.route__node_list):
                only_collection_stall_timeout_hours = Decimal(get_time_spent_hours(latest_time))
            elif DoudianTraceAction.in_transporting_state(resp.data.route__node_list):
                transport_stall_timeout_hours = Decimal(get_time_spent_hours(latest_time))
            elif DoudianTraceAction.in_delivering_state(resp.data.route__node_list):
                delivery_stall_timeout_hours = Decimal(get_time_spent_hours(latest_time))
            else:  # 无需处理的分支
                return
        else:
            sent_collection_timeout_hours = Decimal(get_time_spent_hours(ship_time))
            paid_collection_timeout_hours = Decimal(get_time_spent_hours(pay_time))

        info = robot_types.model.event.doudian.logistics_exception.LogisticsExceptionInfo(
            fs_trade_no=[robot_types.model.component.TradeNo(trade_info.order_id)],
            logistics_company=logistics_info.company_name,
            logistics_no=logistics_info.tracking_no,
            sent_collection_timeout_hours=sent_collection_timeout_hours,
            only_collection_stall_timeout_hours=only_collection_stall_timeout_hours,
            paid_collection_timeout=paid_collection_timeout_hours,
            transport_stall_timeout_hours=transport_stall_timeout_hours,
            delivery_stall_timeout_hours=delivery_stall_timeout_hours,
            paid_sent_timeout_hours=Decimal(0),
            number_of_logistics_record=Decimal(len(resp.data.route__node_list)),
            receiver=robot_types.model.component.Address(
                name=trade_info.post_receiver,
                mobile=trade_info.post_tel,
                state=trade_info.post_addr.province.name,
                city=trade_info.post_addr.city.name,
                town=trade_info.post_addr.town.name,
                address=trade_info.post_addr.detail,
            ),
            fs_product_list=build_fs_product_list(trade_info.order_id, list(trade_info.sku_order_list)),
        )
        BusinessManager.create_bo_by_event(event, shop, robot_types.helper.serialize(info))
    except DoudianCloudServiceError as e:
        logger.warning(e)


def build_fs_product_list(tid: str, orders: List[SkuOrder]):
    products = []
    for order in orders:
        products.append(
            ComponentProduct(
                TID=tid,
                OID=order.sku_order_id,
                PICTURE=None,
                TITLE=order.product_name,
                DESCRIPTION="",
                SPU=order.product_id,
                SKU=order.sku_id,
                SKU_NAME="",
                SPU_OUTER=order.out_product_id,
                SKU_OUTER=order.out_sku_id,
                PRICE=round(order.goods_price / 100, 2),
                INVENTORY=None,
                PAYMENT=round(order.pay_amount / 100, 2),
                COMBINE=None,
                COUNT=order.item_num,
                SHORT_TITLE="",
                BATCH=[],
            )
        )
    return products
