from typing import Any

from pydantic import BaseModel, Field
import sqlalchemy as sa
from loguru import logger

from robot_processor.ext import db
from robot_processor.enums import StepType
from robot_processor.form.models import WidgetRef, Step, StepScope, FormVersion, \
    FormSymbol, WidgetInfo, FormSymbolEditableView, Widget, Component
from robot_processor.form.compatible_utils import widget_info_to_editable_view


class StepInfo(BaseModel):
    """
    步骤信息，用于作用域的计算。
    """
    id: int
    step_uuid: str
    prev_step_ids: list[str] = Field(default_factory=list)
    next_step_ids: list[str] = Field(default_factory=list)
    step_type: StepType = StepType.human
    widget_collection_id: int | None = None
    widget_ref: Any = None


class AllPossibleRoadMap(BaseModel):
    """
    所有可能的路线图信息。
    """
    possible_road_map: dict[str, FormVersion.Schema.StepNode] = Field(
        default_factory=dict, description="所有可能的路线图节点内容"
    )
    start_nodes: list[str] = Field(
        default_factory=list, description="所有的开始节点"
    )


class TempStepNodeInfo(BaseModel):
    """
    临时新增的步骤的节点信息。
    """
    temp_step_prevs: list[str] = Field(default_factory=list)
    temp_step_nexts: list[str] = Field(default_factory=list)


def get_step_id_to_step_info_mapping(
    steps: list[Step],
    temp_step_node_info: TempStepNodeInfo | None = None
) -> dict[int, StepInfo]:
    """
    将数据库的步骤转化为所需要的步骤信息。
    如果传递了 temp_step_node_info，则说明需要再生成一个临时步骤，并按照这个数据，找到它的前序、后序步骤，进行数据修改。
    临时步骤默认其 id 为 0，step_uuid 为 TEMP，step_type 为 human。
    """
    step_id_to_step_info_mapping: dict[int, StepInfo] = {}
    for step in steps:
        step_info = StepInfo(
            id=step.id,
            step_uuid=step.step_uuid,
            prev_step_ids=step.prev_step_ids or [],
            next_step_ids=step.next_step_ids or [],
            step_type=step.step_type,
            widget_collection_id=step.widget_collection_id,
            widget_ref=step.data.get("widget_ref") if isinstance(step.data, dict) else None
        )
        if temp_step_node_info:
            # 如果该步骤为临时步骤的“前序”步骤，则将其原“后序”步骤断开，重新链接到临时步骤上。
            if step.step_uuid in temp_step_node_info.temp_step_prevs:
                step_info.next_step_ids = [
                    step_uuid for step_uuid in step.next_step_ids
                    if step_uuid not in temp_step_node_info.temp_step_nexts
                ] + [FormVersion.Schema.StepNode.TEMP]
            # 如果该步骤为临时步骤的“后序”步骤，则将其原“前序”步骤断开，重新链接到临时步骤上。
            if step.step_uuid in temp_step_node_info.temp_step_nexts:
                step_info.prev_step_ids = [
                    step_uuid for step_uuid in step.prev_step_ids
                    if step_uuid not in temp_step_node_info.temp_step_prevs
                ] + [FormVersion.Schema.StepNode.TEMP]
        step_id_to_step_info_mapping.update({
            step.id: step_info
        })
    if temp_step_node_info:
        # 新增临时步骤信息。
        step_id_to_step_info_mapping.update({
            0: StepInfo(
                id=0,
                step_uuid=FormVersion.Schema.StepNode.TEMP,
                prev_step_ids=temp_step_node_info.temp_step_prevs,
                next_step_ids=temp_step_node_info.temp_step_nexts,
            )
        })
    return step_id_to_step_info_mapping


def generate_all_possible_road_map(step_id_to_step_info_mapping: dict[int, StepInfo]) -> AllPossibleRoadMap:
    """
    生成所有可能的路线图。
    """
    start_nodes: list[str] = []
    possible_road_map: dict[str, FormVersion.Schema.StepNode] = {}

    for step_info in step_id_to_step_info_mapping.values():
        if step_info.step_type in [
            StepType.exclusive_gateway,
            StepType.iterate_gw_begin
        ]:
            step_next = FormVersion.Schema.StepNode.BRANCH
        elif len(step_info.next_step_ids) == 0:  # 姑且通过这个方式判断是不是结束节点
            step_next = FormVersion.Schema.StepNode.END
        else:  # 普通一般节点
            step_next = step_info.next_step_ids[0]
        step_node = FormVersion.Schema.StepNode(current=step_info.id, next=step_next)
        possible_road_map[step_info.step_uuid] = step_node
        if len(step_info.prev_step_ids) == 0:  # 姑且通过这个方式判断是不是开始节点
            start_nodes.append(step_info.step_uuid)

    return AllPossibleRoadMap(
        possible_road_map=possible_road_map,
        start_nodes=start_nodes,
    )


def get_all_latest_steps_by_form_id(form_id: int) -> list[Step]:
    """
    根据 form_id 找到这个模板下的所有最新的步骤。
    """
    all_latest_step_ids_query = db.ro_session.query(
        sa.func.max(Step.id),
    ).filter(
        Step.form_id == form_id,
        Step.deleted.is_(False)
    ).group_by(
        Step.step_uuid,
    )
    all_latest_step_ids: list[int] = [step_id for (step_id, ) in all_latest_step_ids_query]
    all_latest_steps: list[Step] = db.ro_session.query(Step).filter(
        Step.id.in_(all_latest_step_ids),
    ).all()
    return all_latest_steps


def calculate_route(
    step_uuid: str,
    step_id_to_step_info_mapping: dict[int, StepInfo],
    memo: dict[str, list[list[FormVersion.Schema.StepNode]]],
    road_map: dict[str, FormVersion.Schema.StepNode],
) -> list[list[FormVersion.Schema.StepNode]]:
    """
    根据 step_uuid 去计算路线。
    """
    if step_uuid in memo:
        return memo[step_uuid]

    node = road_map[step_uuid]
    route: list[list[FormVersion.Schema.StepNode]]

    match node.next:
        case FormVersion.Schema.StepNode.END:
            route = [[node]]
        case FormVersion.Schema.StepNode.BRANCH:
            route = [
                [node, *next_step_route]
                for next_step_uuid in step_id_to_step_info_mapping[node.current].next_step_ids
                for next_step_route in calculate_route(
                    step_uuid=next_step_uuid,
                    step_id_to_step_info_mapping=step_id_to_step_info_mapping,
                    memo=memo,
                    road_map=road_map
                )
            ]
        case _:
            route = [[node, *next_step_route] for next_step_route in calculate_route(
                step_uuid=node.next,
                step_id_to_step_info_mapping=step_id_to_step_info_mapping,
                memo=memo,
                road_map=road_map
            )]

    memo[step_uuid] = route
    return route


def get_start_node_to_all_possible_routes_mapping(
    step_id_to_step_info_mapping: dict[int, StepInfo],
) -> dict[str, list[list[FormVersion.Schema.StepNode]]]:
    """
    获取所有可能的路线信息。
    """
    all_possible_road_map = generate_all_possible_road_map(step_id_to_step_info_mapping)
    memo: dict[str, list[list[FormVersion.Schema.StepNode]]] = {}

    start_node_to_all_possible_routes_mapping: dict[str, list[list[FormVersion.Schema.StepNode]]] = {}
    for start_node in all_possible_road_map.start_nodes:
        routes = calculate_route(
            step_uuid=start_node,
            step_id_to_step_info_mapping=step_id_to_step_info_mapping,
            memo=memo,
            road_map=all_possible_road_map.possible_road_map,
        )
        start_node_to_all_possible_routes_mapping.update({start_node: routes})
    return start_node_to_all_possible_routes_mapping


def get_step_scope_by_single_route(
    step_uuid: str,
    step_infos: list[StepInfo],
    routes: list[list[FormVersion.Schema.StepNode]]
):
    """
    分析该 step_uuid 的 scope 信息。
    """
    def get_step_by_step_uuid(step_uuid_: str) -> StepInfo:
        return next(filter(lambda x: x.step_uuid == step_uuid_, step_infos))

    def get_step_by_step_id(step_id: int) -> StepInfo:
        return next(filter(lambda x: x.id == step_id, step_infos))

    step_info = get_step_by_step_uuid(step_uuid)
    scope_set = set()

    def get_step_scope_in_route(route_: list[FormVersion.Schema.StepNode]) -> tuple[str, ...] | None:
        stack = []
        for step_node in route_:
            current_step = get_step_by_step_id(step_node.current)
            match current_step.step_type:
                case StepType.iterate_gw_begin:
                    stack.append(current_step.step_uuid)
                case StepType.iterate_gw_end:
                    stack.pop()
            if step_node.current == step_info.id:
                return tuple(reversed(stack))
        return None

    for route in routes:
        if next(filter(lambda x: x.current == step_info.id, route), None):
            step_scope = get_step_scope_in_route(route)
            if step_scope is not None:
                scope_set.add(step_scope)

    if len(scope_set) <= 1:
        scope = []
        for each_step_uuid in scope_set.pop():
            each_step = get_step_by_step_uuid(each_step_uuid)
            widget_ref = WidgetRef.parse(each_step.widget_ref, force_parse_str=True)
            scope.append(StepScope(step_uuid=each_step_uuid, widget_ref=widget_ref))
        return scope
    else:
        raise StopIteration(f"step is not unique in routes {scope_set}")


def get_start_node_to_scope_mapping(
    step_uuid: str,
    step_id_to_step_info_mapping: dict[int, StepInfo],
) -> dict[str, list[StepScope]]:
    """
    计算并得出该 step_uuid 的作用域。
    """
    start_node_to_all_possible_routes_mapping = get_start_node_to_all_possible_routes_mapping(
        step_id_to_step_info_mapping
    )

    start_node_to_scope_mapping: dict[str,  list[StepScope]] = {}

    for start_node, routes in start_node_to_all_possible_routes_mapping.items():
        logger.info(f"基于起点 {start_node} 开始计算作用域")
        try:
            scope = get_step_scope_by_single_route(
                step_uuid=step_uuid,
                step_infos=list(step_id_to_step_info_mapping.values()),
                routes=routes,
            )
            start_node_to_scope_mapping.update({start_node: scope})
        except StopIteration:
            continue
        except Exception as e:
            logger.error("检测作用域失败：{}", e)
            continue
    return start_node_to_scope_mapping


def get_step_uuid_to_scopes_mapping(
    steps: list[Step],
    temp_step_node_info: TempStepNodeInfo | None = None
) -> dict[str, dict[str, list[StepScope]] | None]:
    """
    计算出所有步骤的作用域信息。
    temp_step_node_info 为临时步骤的信息。
    """
    step_uuid_to_scopes_mapping: dict[str, dict[str, list[StepScope]] | None] = {}

    step_id_to_step_info_mapping = get_step_id_to_step_info_mapping(
        steps,
        temp_step_node_info=temp_step_node_info,
    )

    for step_info in step_id_to_step_info_mapping.values():
        scope = get_start_node_to_scope_mapping(step_info.step_uuid, step_id_to_step_info_mapping)
        step_uuid_to_scopes_mapping.update({
            step_info.step_uuid: scope
        })

    return step_uuid_to_scopes_mapping


def get_step_uuid_to_form_symbols_mapping(form_id: int, steps: list[Step]) -> dict[str, list[FormSymbol]]:
    """
    获取所有步骤对应的 form_symbol_view。
    """
    step_uuids: list[str] = [step.step_uuid for step in steps]
    step_ids: list[int] = [step.id for step in steps]
    form_symbols: list[FormSymbol] = db.ro_session.query(FormSymbol).filter(
        FormSymbol.form_id == form_id,
        FormSymbol.step_uuid.in_(step_uuids),
        FormSymbol.step_id.in_(step_ids),
    ).all()

    step_uuid_to_form_symbols_mapping: dict[str, list[FormSymbol]] = {}

    for step in steps:
        # 不去管排他网关、遍历网关上的组件信息。（因为之前线上有逻辑问题，遍历网关都会跟着 7 个名为质检日期的组件）
        if step.step_type in [
            StepType.exclusive_gateway,
            StepType.iterate_gw_begin,
            StepType.iterate_gw_end,
        ]:
            continue
        current_step_form_symbols = [form_symbol for form_symbol in form_symbols if form_symbol.step_id == step.id]
        # 没找到 form_symbol 的话，就用 widget_info 去转换出来。
        if len(current_step_form_symbols) == 0 and step.widget_collection_id not in (None, "0"):
            widget_info_list = WidgetInfo.query.filter(
                WidgetInfo.widget_collection_id == step.widget_collection_id
            ).all()
            widget_id_to_widget_mapping = dict((widget.id, widget) for widget in Widget.query)
            component_id_to_component_mapping = dict((component.id, component) for component in Component.query)
            current_step_form_symbol_views = [
                widget_info_to_editable_view(
                    widget_info,
                    widget_id_to_widget_mapping,
                    component_id_to_component_mapping
                ) for widget_info in widget_info_list
            ]
            step_uuid_to_form_symbols_mapping.update({
                step.step_uuid: convert_form_symbol_views(
                    form_id,
                    step.step_uuid,
                    step.id,
                    current_step_form_symbol_views
                )
            })
        else:
            step_uuid_to_form_symbols_mapping.update({step.step_uuid: current_step_form_symbols})

    return step_uuid_to_form_symbols_mapping


def convert_form_symbol_views(
    form_id: int,
    step_uuid: str,
    step_id: int,
    form_symbol_views: list[FormSymbolEditableView]
) -> list[FormSymbol]:
    """
    将 FormSymbolEditableView 转换为 FormSymbol 类型的数据，便于获取 parent 信息。
    """
    def convert(editable_view: FormSymbolEditableView, parent: FormSymbol | None = None):
        form_symbol = FormSymbol(
            form_id=form_id,
            step_uuid=step_uuid,
            step_id=step_id,
            name=editable_view.name,
            type_spec=editable_view.type_spec.dict(),
            options=editable_view.options.dict(),
            component_id=editable_view.component_id,
            render_config=editable_view.render_config,
            parent=parent.name if parent else None,
            parent_path=parent.fullname_in_scope if parent else None,
            redirect=editable_view.redirect
        )
        form_symbols.append(form_symbol)
        if editable_view.children:
            array_item_name = f"{form_symbol.name}_ITEM"
            for child in editable_view.children:
                if editable_view.type_spec.type == "array":
                    child.name = array_item_name
                convert(child, form_symbol)

    form_symbols: list[FormSymbol] = []
    for form_symbol_view in form_symbol_views:
        convert(form_symbol_view)
    return form_symbols


def get_all_iterable_keys(widget_ref: WidgetRef, keys: set[str]) -> set[str]:
    """
    获取所有的进行迭代的组件的 key。
    """
    keys.add(widget_ref.key)
    if isinstance(widget_ref.field, WidgetRef):
        return get_all_iterable_keys(widget_ref.field, keys)
    if isinstance(widget_ref.field, str):
        keys.add(widget_ref.field)
    return keys


def get_scope_widget_keys(scope: list[StepScope]) -> set[str]:
    """
    获取这个作用域所以引用的所有组件的 key。
    """
    scope_key_set: set[str] = set()
    for scope_item in scope:
        scope_key_set.union(get_all_iterable_keys(widget_ref=scope_item.widget_ref, keys=scope_key_set))
    return scope_key_set


def check_is_parent_scope(this_scope: list[StepScope], other_scope: list[StepScope]) -> bool:
    """
    检测 other_scope 是否为 this_scope 的父作用域。
    目前是先检测作用域的步骤是否存在包含关系，然后再检测引用的组件值是否存在包含关系。
    """
    this_scope_step_uuid_set: set[str] = {scope_item.step_uuid for scope_item in this_scope}
    other_scope_step_uuid_set: set[str] = {scope_item.step_uuid for scope_item in other_scope}
    if not other_scope_step_uuid_set.issubset(this_scope_step_uuid_set):
        return False
    this_scope_key_set: set[str] = get_scope_widget_keys([
        scope_item
        for scope_item in this_scope
    ])
    other_scope_key_set: set[str] = get_scope_widget_keys([
        scope_item
        for scope_item in other_scope
    ])
    return other_scope_key_set.issubset(this_scope_key_set)


def get_step_can_call_form_symbols(
    step_uuid: str,
    step_uuid_to_form_symbols_mapping: dict[str, list[FormSymbol]],
    step_uuid_to_scopes_mapping: dict[str, dict[str, list[StepScope]] | None]
) -> dict[str, list[FormSymbolEditableView]]:
    """
    获取步骤可调用的组件。
    """
    this_step_start_node_to_scope_mapping = step_uuid_to_scopes_mapping.get(step_uuid)
    if not this_step_start_node_to_scope_mapping:
        return {}

    logger.info("step uuid: {} 获取到的作用域为 {}".format(step_uuid, this_step_start_node_to_scope_mapping))

    step_uuid_to_form_symbol_views_mapping: dict[str, list[FormSymbolEditableView]] = {}

    # 遍历当前步骤的所有作用域。
    for this_step_start_node, this_step_scope in this_step_start_node_to_scope_mapping.items():
        # 以当前起始节点来看，计算这条路线图内可以用的组件。
        other_step_uuid_to_scope_mapping: dict[str, list[StepScope]] = {}
        for other_step_uuid, other_step_start_node_to_scope_mapping in step_uuid_to_scopes_mapping.items():
            if other_step_start_node_to_scope_mapping is None:
                continue
            # 取到存在于当前起始节点这个路线图的步骤。
            if (scope := other_step_start_node_to_scope_mapping.get(this_step_start_node)) is not None \
                    and other_step_uuid != step_uuid:
                other_step_uuid_to_scope_mapping.update({other_step_uuid: scope})

        related_step_uuids: list[str] = []
        # 遍历所有步骤，进行数据计算。
        for other_step_uuid, other_step_scope in other_step_uuid_to_scope_mapping.items():
            # 如果作用域层数为 0，说明在最外层。
            if len(other_step_scope) == 0:
                related_step_uuids.append(other_step_uuid)
            # 获取共同的作用域的组件的 key，存在则说明他们的作用域存在一定的关联关系。
            # 从该步骤内取出需要查询的步骤可用的组件。
            elif check_is_parent_scope(this_step_scope, other_step_scope):
                related_step_uuids.append(other_step_uuid)

        # 找到当前步骤的作用域所使用到的所有组件的 key。
        this_scope_widget_keys = list(get_scope_widget_keys(this_step_scope))
        this_scope_widget_key_items = ["{}_ITEM".format(key) for key in this_scope_widget_keys]

        for related_step_uuid in related_step_uuids:
            # 找到有关联的步骤上的所有组件。
            form_symbols = step_uuid_to_form_symbols_mapping.get(related_step_uuid) or []
            if len(form_symbols) == 0:
                continue
            # 获取最外层的组件、遍历的对象的组件、遍历的对象的一层子组件。
            filter_form_symbols = [
                form_symbol
                for form_symbol in form_symbols
                if form_symbol.redirect is None
                and (
                    form_symbol.name in this_scope_widget_keys
                    or form_symbol.parent in (this_scope_widget_key_items + this_scope_widget_keys + [None])
                )
            ]
            # 将 form_symbol 进行转换。（子组件会被包含在 children 中，所以可以不用直接在外层输出）
            form_symbol_views = [
                FormSymbolEditableView.from_form_symbol(form_symbol, filter_form_symbols)
                for form_symbol in filter_form_symbols if form_symbol.parent is None
            ]
            step_uuid_to_form_symbol_views_mapping.update({
                related_step_uuid: form_symbol_views
            })

    return step_uuid_to_form_symbol_views_mapping
