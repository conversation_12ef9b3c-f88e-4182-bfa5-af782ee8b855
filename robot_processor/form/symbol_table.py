import datetime as dt
from collections import namedtuple
from functools import wraps
from typing import (
    Any,
    Literal,
    Optional,
    ClassVar,
    overload,
    TypeVar,
    Self,
    TYPE_CHECKING,
)

import arrow
import jmespath.exceptions
from more_itertools import first
from pydantic import BaseModel, Field, validator, root_validator
from result import Result, Ok, Err, do
from leyan_proto.digismart.robot.symbol_table_pb2 import (
    Value as pb_Value,
)

from robot_processor.constants import TIME_ZONE
from robot_processor.utils import (
    list_result_to_result_list,
    jmespath_search,
    make_fields_optional,
    message_to_dict
)

if TYPE_CHECKING:
    from robot_processor.form.models import FormSymbol


class TypeSpec(BaseModel):
    type: Literal[
        "string",
        "number",
        "boolean",
        "datetime",
        "date",
        "time",
        "array",
        "collection",
        "any",
        "T",
    ]
    spec: list["TypeSpec"] | None = None
    name: str | None = None
    typename: str | None = None

    def is_match(self, other):
        """通过检查 type 和 spec 是否匹配来判断是否匹配

        Args:
            other (TypeSpec): 待比较的 TypeSpec

        Returns:
            bool: 是否匹配
        """
        if not isinstance(other, TypeSpec):
            return False
        if self.type in [NamedTypeSpec.ANY.type, NamedTypeSpec.T.type]:
            return True
        if other.type in [NamedTypeSpec.ANY.type, NamedTypeSpec.T.type]:
            return True
        if self.type == other.type == "array":
            return do(
                Ok(self_item_spec.is_match(other_item_spec))
                for self_item_spec in self.get_array_item_spec()
                for other_item_spec in other.get_array_item_spec()
            )
        if self.type == other.type == "collection":
            if self.spec is None:
                return False
            if other.spec is None:
                return False
            self_spec_map = {spec.name: spec for spec in self.spec}
            other_spec_map = {spec.name: spec for spec in other.spec}
            if self_spec_map.keys() != other_spec_map.keys():
                return False
            return all(
                self_spec_map[spec_name].is_match(other_spec_map[spec_name])
                for spec_name in self_spec_map
            )
        else:
            return self.type == other.type

    def get_array_item_spec(self):
        """列表类型的元素类型

        Returns:
            Ok[TypeSpec] | Err[TypeError] | Err[ValueError]

        """
        if self.type != "array":
            return Err(TypeError("当前类型不是 array"))
        if self.spec is None:
            return Err(ValueError("array 类型未指定 spec"))
        return Ok(self.spec[0])

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        kwargs.setdefault("exclude_none", True)
        return super().dict(**kwargs)

    def __str__(self):
        match self.type:
            case "array":
                if not self.spec:
                    return "array<unknown>"
                else:
                    return "array<{}>".format(self.spec[0])
            case "collection":
                if not self.spec:
                    return "collection<string: unknown>"
                col_spec_tmpl = "{}: {}"
                item_str = ", ".join(
                    col_spec_tmpl.format(spec.name, spec) for spec in self.spec
                )
                return "collection<{{{}}}>".format(item_str)
            case _:
                return self.type


@make_fields_optional
class WDTGoods(BaseModel):
    goods_name: str
    goods_no: str
    spec_name: str
    spec_no: str
    num: str
    brand_name: str


@make_fields_optional
class WdtBatch(BaseModel):
    batch_no: str


@make_fields_optional
class WDTTradeInfo(BaseModel):
    trade_no: str
    trade_status_zh: str
    logistics_name: str
    logistics_no: str
    paid: str
    warehouse_no: str
    warehouse_name: str
    goods_list: list[WDTGoods]


@make_fields_optional
class JackyunGoods(BaseModel):
    goodsName: str
    goodsNo: str
    sellCount: str
    specName: str


@make_fields_optional
class JackyunTradeInfo(BaseModel):
    logisticName: str
    mainPostid: str
    sellerMemo: str
    tradeNo: str
    tradeType: str
    tradeId: str
    warehouseName: str
    readable_trade_status: str
    goodsDetail: list[JackyunGoods]


@make_fields_optional
class PlatformOrderInfo(BaseModel):
    order_id: Optional[str]
    spu_title: Optional[str]
    spu_id: Optional[str]
    quantity: Optional[int]
    price: Optional[float]
    sku_description: Optional[str]
    sku_id: Optional[str]
    outer_sku_id: Optional[str]
    pay_amount: Optional[float]
    refund_id: Optional[str]
    logistics_no: Optional[str]
    logistics_company: Optional[str]
    return_logistics_no: Optional[str]
    return_logistics_company: Optional[str]
    refund_reason: Optional[str]
    refund_amount: Optional[float]
    readable_refund_type: Optional[str]
    readable_refund_status: Optional[str]


@make_fields_optional
class WdtultiGoods(BaseModel):
    goods_name: str
    goods_no: str
    spec_name: str
    spec_no: str
    num: int


@make_fields_optional
class WdtultiTradeInfo(BaseModel):
    trade_no: str
    chinese_status: str
    logistics_name: str
    logistics_no: str
    paid: float
    warehouse_no: str
    warehouse_name: str
    goods_list: list[WdtultiGoods]



class Credential:
    class WDT(BaseModel):
        sid: str
        after_sale_shop_no: str
        app_key: str
        app_secret: str

    class Jackyun(BaseModel):
        app_key: str
        app_secret: str
        customer_id: str
        erp_account: str
        app_token: Optional[str]

    class WDTULTI(BaseModel):
        sid: str
        after_sale_shop_no: Optional[str]
        wdt_appkey: str
        wdt_secret: str
        wdt_salt: str


@make_fields_optional
class BaishengOrderDetail(BaseModel):
    goods_sn: str
    sku: str
    goods_id: int
    sku_id: int
    goods_number: int
    goods_price: float
    shop_price: float
    share_price: float
    share_payment: float
    original_order_sn: str
    original_deal_code: str
    sub_deal_code: str
    num_iid: str
    pic_path: str
    goods_name: str
    is_gift: int
    goods_sname: str
    barcode: str
    cbj: float
    brand_name: str

    @validator("cbj", pre=True)
    def set_null(cls, v):
        if v == "":
            return None
        return v


@make_fields_optional
class BaishengOrder(BaseModel):
    order_sn: str
    deal_code: str
    sd_id: int
    order_status: int
    shipping_code: str
    shipping_name: str
    shipping_sn: str
    is_copy: bool
    is_split: bool
    is_split_new: bool
    is_combine: bool
    is_combine_new: bool
    order_msg: str
    seller_msg: str
    seller_flag: int
    fhck: str
    fhckmc: str
    payment: float
    shipping_status: int
    orderDetailGets: list[BaishengOrderDetail]
    readable_shipping_status: str
    readable_order_status: str

    @root_validator(pre=True)
    def generate_all_readable_str(cls, values):
        shipping_status = values.get("shipping_status")
        shipping_status_map = {
            0: "初始",
            1: "预分配缺货处理中",
            2: "已完成预分配",
            3: "已通知配货",
            4: "拣货中(已分配拣货任务)",
            5: "已完成拣货",
            6: "已发货",
            7: "已出库",
            9: "取消"
        }
        values["readable_shipping_status"] = shipping_status_map.get(
            shipping_status)

        order_status = values.get("order_status")
        order_status_map = {
            0: "未确认",
            1: "已确认",
            3: "已作废",
            5: "已完成"
        }
        values["readable_order_status"] = order_status_map.get(order_status)
        return values


class CredentialBaisheng(BaseModel):
    sid: str
    appKey: str
    appSecret: str
    baseUrl: str


class NamedTypeSpec:
    """具名类型"""

    _Value = namedtuple("_Value", ["type_spec", "model"])

    ANY = TypeSpec(type="any")
    T = TypeSpec(type="T")
    ARRAY_ANY = TypeSpec(
        type="array",
        spec=[TypeSpec(type="any")],
    )
    ARRAY_GENERIC = TypeSpec(
        type="array",
        spec=[TypeSpec(type="T")],
    )

    FILE = TypeSpec(
        typename="File",
        type="collection",
        spec=[
            TypeSpec(name="fileName", type="string"),
            TypeSpec(name="url", type="number"),
        ],
    )
    PAYMENT_METHOD = TypeSpec(
        typename="PaymentMethod",
        type="collection",
        spec=[
            TypeSpec(name="payment_method", type="number"),
            TypeSpec(name="receive_account", type="string"),
            TypeSpec(name="receive_name", type="string"),
            TypeSpec(name="tid", type="string"),
            TypeSpec(name="alipay_no", type="string"),
        ],
    )
    ADDRESS = TypeSpec(
        typename="Address",
        type="collection",
        spec=[
            TypeSpec(name="name", type="string"),
            TypeSpec(name="mobile", type="string"),
            TypeSpec(name="state", type="string"),
            TypeSpec(name="city", type="string"),
            TypeSpec(name="zone", type="string"),
            TypeSpec(name="town", type="string"),
            TypeSpec(name="address", type="string"),
        ],
    )
    TRADE_NO = TypeSpec(
        typename="TradeNo",
        type="collection",
        spec=[
            TypeSpec(name="tid", type="string"),
            TypeSpec(name="oid", type="string"),
        ],
    )
    RATE = TypeSpec(
        typename="Rate",
        type="collection",
        spec=[
            TypeSpec(name="id", type="number"),
            TypeSpec(name="label", type="string"),
            TypeSpec(name="type", type="string"),
            TypeSpec(name="value", type="number"),
        ],
    )
    REISSUE_PRODUCT = TypeSpec(
        typename="ReissueProduct",
        type="collection",
        spec=[
            TypeSpec(name="SPU", type="string"),
            TypeSpec(name="SPU_OUTER", type="string"),
            TypeSpec(name="SKU", type="string"),
            TypeSpec(name="SKU_OUTER", type="string"),
            TypeSpec(name="REISSUE_QUANTITY", type="number"),
            TypeSpec(name="TITLE", type="string"),
            TypeSpec(name="DESCRIPTION", type="string"),
            TypeSpec(name="PICTURE", type="string"),
            TypeSpec(name="INVENTORY", type="number"),
        ],
    )
    BATCH_INFO = TypeSpec(
        typename="BatchInfo",
        type="collection",
        spec=[
            TypeSpec(name="BATCH_NO", type="string"),
        ],
    )
    PRODUCT = TypeSpec(
        typename="Product",
        type="collection",
        spec=[
            TypeSpec(name="TID", type="string"),
            TypeSpec(name="OID", type="string"),
            TypeSpec(name="PICTURE", type="string"),
            TypeSpec(name="TITLE", type="string"),
            TypeSpec(name="DESCRIPTION", type="string"),
            TypeSpec(name="SPU", type="string"),
            TypeSpec(name="SKU", type="string"),
            TypeSpec(name="SPU_OUTER", type="string"),
            TypeSpec(name="SKU_OUTER", type="string"),
            TypeSpec(name="PRICE", type="number"),
            TypeSpec(name="INVENTORY", type="number"),
            TypeSpec(name="PAYMENT", type="number"),
            TypeSpec(name="COMBINE", type="string"),
            TypeSpec(name="COUNT", type="number"),
            TypeSpec(name="SHORT_TITLE", type="string"),
            TypeSpec(name="BATCH", type="array",  spec=[BATCH_INFO])
        ],
    )
    SELECT = TypeSpec(
        typename="Select",
        type="array",
        spec=[TypeSpec(type="string")],
    )
    SELECT_MULTI = TypeSpec(
        typename="SelectMulti",
        type="array",
        spec=[
            TypeSpec(
                typename="Select",
                type="array",
                spec=[TypeSpec(type="string")],
            )
        ],
    )
    # 授权 - 旺店通企业版
    CREDENTIAL_WDT = _Value(TypeSpec(
        typename="Credential.WDT",
        type="collection",
        spec=[
            TypeSpec(name="sid", type="string"),
            TypeSpec(name="after_sale_shop_no", type="string"),
            TypeSpec(name="app_key", type="string"),
            TypeSpec(name="app_secret", type="string"),
        ],
    ), Credential.WDT)
    WDT_GOODS = _Value(
        TypeSpec(
            typename="WDT.Goods",
            type="collection",
            spec=[
                TypeSpec(name="goods_name", type="string"),  # 货品(spu)名称
                TypeSpec(name="goods_no", type="string"),  # 货品(spu)编号
                TypeSpec(name="spec_name", type="string"),  # 商家(sku)名称
                TypeSpec(name="spec_no", type="string"),  # 商家(sku)编号
                TypeSpec(name="num", type="number"),  # 数量
                TypeSpec(name="brand_name", type="string"),  # 品牌名称
                TypeSpec(name="order_price", type="number"),  # 成交价
                TypeSpec(name="share_price", type="number"),  # 分摊后价格
                TypeSpec(name="share_amount", type="number")  # 分摊后总价
            ],
        ),
        WDTGoods,
    )
    WDT_TRADE_INFO = _Value(
        TypeSpec(
            typename="WDT.TradeInfo",
            type="collection",
            spec=[
                TypeSpec(name="trade_no", type="string"),  # 旺店通系统单号，默认单号为JY开头
                TypeSpec(name="trade_status_zh", type="string"),  # 订单状态
                TypeSpec(name="logistics_name", type="string"),  # 快递公司
                TypeSpec(name="logistics_no", type="string"),  # 快递单号
                TypeSpec(name="paid", type="number"),  # 实付金额
                TypeSpec(name="warehouse_no", type="string"),  # 仓库编号
                TypeSpec(name="warehouse_name", type="string"),  # 仓库名称
                TypeSpec(name="consign_time", type="string"),  # 发货时间
                TypeSpec(name="shop_name", type="string"),  # 店铺名称
                TypeSpec(
                    name="goods_list", type="array", spec=[WDT_GOODS.type_spec]
                ),  # 货品列表
            ],
        ),
        WDTTradeInfo,
    )
    PLATFORM_ORDER_INFO = _Value(
        TypeSpec(
            typename="PLATFORM.OrderInfo",
            type="collection",
            spec=[
                TypeSpec(name="order_id", type="string"),  # 子订单号
                TypeSpec(name="spu_title", type="string"),  # 商品标题
                TypeSpec(name="spu_id", type="string"),  # 商品ID
                TypeSpec(name="quantity", type="number"),  # 购买数
                TypeSpec(name="price", type="number"),  # 单价
                TypeSpec(name="sku_description", type="string"),  # SKU属性
                TypeSpec(name="sku_id", type="string"),  # SKU ID
                TypeSpec(name="outer_sku_id", type="string"),  # 商家编码
                TypeSpec(name="pay_amount", type="number"),  # 实付金额
                TypeSpec(name="refund_id", type="string"),  # 退款ID
                TypeSpec(name="logistics_no", type="string"),  # 发货运单号
                TypeSpec(name="logistics_company", type="string"),  # 发货快递
                TypeSpec(name="return_logistics_no", type="string"),  # 退货运单号
                TypeSpec(name="return_logistics_company", type="string"),  # 退货快递
                TypeSpec(name="refund_reason", type="string"),  # 退款原因
                TypeSpec(name="refund_amount", type="number"),  # 退款金额
                TypeSpec(name="readable_refund_type", type="string"),  # 退款类型
                TypeSpec(name="readable_refund_status", type="string"),  # 退款状态
            ]
        ),
        PlatformOrderInfo,
    )
    # 授权 - 吉客云
    CREDENTIAL_JACKYUN = _Value(TypeSpec(
        typename="Credential.Jackyun",
        type="collection",
        spec=[
            TypeSpec(name="app_key", type="string"),
            TypeSpec(name="app_secret", type="string"),
            TypeSpec(name="customer_id", type="string"),
            TypeSpec(name="erp_account", type="string"),
            TypeSpec(name="app_token", type="string")
        ],
    ), Credential.Jackyun)
    JACKYUN_GOODS = _Value(
        TypeSpec(
            typename="Jackyun.Goods",
            type="collection",
            spec=[
                TypeSpec(name="goodsName", type="string"),  # 货品名称
                TypeSpec(name="goodsNo", type="string"),  # 货品编号
                TypeSpec(name="specName", type="string"),  # 规格名称
                TypeSpec(name="sellCount", type="string"),  # 数量
            ],
        ),
        JackyunGoods,
    )
    JACKYUN_TRADE_INFO = _Value(
        TypeSpec(
            typename="Jackyun.TradeInfo",
            type="collection",
            spec=[
                TypeSpec(name="logisticName", type="string"),  # 快递公司
                TypeSpec(name="mainPostid", type="string"),  # 快递单号
                TypeSpec(name="sellerMemo", type="string"),  # 客服备注
                TypeSpec(name="tradeNo", type="string"),  # 吉客云订单编号
                TypeSpec(name="tradeType", type="string"),  # 订单类型
                TypeSpec(name="tradeId", type="string"),  # 系统编码
                TypeSpec(name="warehouseName", type="string"),  # 仓库名称
                TypeSpec(name="readable_trade_status", type="string"),  # 订单状态
                TypeSpec(
                    name="goodsDetail", type="array", spec=[JACKYUN_GOODS.type_spec]
                ),  # 货品列表
            ],
        ),
        JackyunTradeInfo,
    )
    # 授权 - 旺店通旗舰版
    CREDENTIAL_WDTULTI = _Value(TypeSpec(
        typename="Credential.WDTULTI",
        type="collection",
        spec=[
            TypeSpec(name="sid", type="string"),
            TypeSpec(name="after_sale_shop_no", type="string"),
            TypeSpec(name="wdt_appkey", type="string"),
            TypeSpec(name="wdt_secret", type="string"),
            TypeSpec(name="wdt_salt", type="string")
        ],
    ), Credential.WDTULTI)
    # 授权 - 百胜
    CREDENTIAL_BAISHENG = _Value(TypeSpec(
        typename="Credential.BAISHENG",
        type="collection",
        spec=[
            TypeSpec(name="sid", type="string"),
            TypeSpec(name="appKey", type="string"),
            TypeSpec(name="appSecret", type="string"),
            TypeSpec(name="baseUrl", type="string"),
        ],
    ), CredentialBaisheng)
    # 百胜货品
    BAISHENG_GOODS = _Value(
        TypeSpec(
            typename="BAISHENG.Goods",
            type="collection",
            spec=[
                TypeSpec(name="goods_sn", type="string"),
                TypeSpec(name="sku", type="string"),
                TypeSpec(name="goods_id", type="number"),
                TypeSpec(name="sku_id", type="number"),
                TypeSpec(name="goods_number", type="number"),
                TypeSpec(name="goods_price", type="number"),
                TypeSpec(name="shop_price", type="number"),
                TypeSpec(name="share_price", type="number"),
                TypeSpec(name="share_payment", type="number"),
                TypeSpec(name="original_order_sn", type="string"),
                TypeSpec(name="original_deal_code", type="string"),
                TypeSpec(name="sub_deal_code", type="string"),
                TypeSpec(name="num_iid", type="string"),
                TypeSpec(name="pic_path", type="string"),
                TypeSpec(name="goods_name", type="string"),
                TypeSpec(name="is_gift", type="number"),
                TypeSpec(name="goods_sname", type="string"),
                TypeSpec(name="barcode", type="string"),
                TypeSpec(name="cbj", type="number"),
                TypeSpec(name="brand_name", type="string"),
            ],
        ),
        BaishengOrderDetail,
    )
    # 百胜订单信息
    BAISHENG_TRADE_INFO = _Value(
        TypeSpec(
            typename="BAISHENG.TradeInfo",
            type="collection",
            spec=[
                TypeSpec(name="order_sn", type="string"),
                TypeSpec(name="deal_code", type="string"),
                TypeSpec(name="sd_id", type="number"),
                TypeSpec(name="order_status", type="number"),
                TypeSpec(name="shipping_code", type="string"),
                TypeSpec(name="shipping_name", type="string"),
                TypeSpec(name="shipping_sn", type="string"),
                TypeSpec(name="is_copy", type="boolean"),
                TypeSpec(name="is_split", type="boolean"),
                TypeSpec(name="is_split_new", type="boolean"),
                TypeSpec(name="is_combine", type="boolean"),
                TypeSpec(name="is_combine_new", type="boolean"),
                TypeSpec(name="order_msg", type="string"),
                TypeSpec(name="seller_msg", type="string"),
                TypeSpec(name="seller_flag", type="number"),
                TypeSpec(name="fhck", type="string"),
                TypeSpec(name="fhckmc", type="string"),
                TypeSpec(name="payment", type="number"),
                TypeSpec(name="shipping_status", type="number"),
                TypeSpec(
                    name="orderDetailGets", type="array", spec=[BAISHENG_GOODS.type_spec]
                ),
                TypeSpec(name="readable_shipping_status", type="string"),
                TypeSpec(name="readable_order_status", type="string"),
            ],
        ),
        BaishengOrder,
    )
    # 旺店通旗舰版货品
    WDTULTI_GOODS = _Value(
        TypeSpec(
            typename="WDTULTI.Goods",
            type="collection",
            spec=[
                TypeSpec(name="goods_name", type="string"),  # 货品(spu)名称
                TypeSpec(name="goods_no", type="string"),  # 货品(spu)编号
                TypeSpec(name="spec_name", type="string"),  # 商家(sku)名称
                TypeSpec(name="spec_no", type="string"),  # 商家(sku)编号
                TypeSpec(name="num", type="number")  # 数量
            ],
        ),
        WdtultiGoods,
    )
    WDTULTI_TRADE_INFO = _Value(
        TypeSpec(
            typename="WDTULTI.TradeInfo",
            type="collection",
            spec=[
                TypeSpec(name="trade_no", type="string"),  # 旺店通系统单号，默认单号为JY开头
                TypeSpec(name="chinese_status", type="string"),  # 订单状态
                TypeSpec(name="logistics_name", type="string"),  # 快递公司
                TypeSpec(name="logistics_no", type="string"),  # 快递单号
                TypeSpec(name="paid", type="number"),  # 实付金额
                TypeSpec(name="warehouse_no", type="string"),  # 仓库编号
                TypeSpec(name="warehouse_name", type="string"),  # 仓库名称
                TypeSpec(
                    name="goods_list", type="array", spec=[
                        WDTULTI_GOODS.type_spec]
                ),  # 货品列表
            ],
        ),
        WdtultiTradeInfo,
    )


class FormSymbolScope(BaseModel):
    """FormSymbol 的作用域"""

    form_id: int
    step_uuid: str
    step_id: int

    if TYPE_CHECKING:
        from robot_processor.form.models import Step

    @classmethod
    def from_step(cls, step: "Step"):
        return cls(form_id=step.form_id, step_uuid=step.step_uuid, step_id=step.id)


class FormSymbolOptions(BaseModel):
    """FormSymbol 的配置项"""

    required: bool | None = None
    defaultValue: Any = None
    enum: list | None = None
    immutable: bool | None = None
    min: Any = None
    max: Any = None

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        kwargs["exclude_none"] = True
        kwargs.setdefault("exclude_unset", True)
        return super().dict(**kwargs)


class FormSymbolEditableView(BaseModel):
    """创建/编辑 FormSymbol 时的数据结构

    创建/编辑 FormSymbol 都是通过 PUT 方式完成的。
    """

    class Config:
        orm_mode = True

    name: str | None = None
    type_spec: TypeSpec
    label: str | None = None
    options: FormSymbolOptions = Field(default_factory=FormSymbolOptions)
    component_id: str | None = None
    render_config: dict = Field(default_factory=dict)
    redirect: str | None = None
    children: list["FormSymbolEditableView"] | None = None

    def __init__(self, **data):
        super().__init__(**data)
        # 优先以显式提供的 label 为准
        if self.label is None:
            self.label = self.render_config.get("label") or ""
        else:
            self.render_config["label"] = self.label

    @classmethod
    def from_form_symbol(cls, symbol: "FormSymbol", sibling: list["FormSymbol"]) -> "FormSymbolEditableView":
        from robot_processor.form.models import FormSymbol

        self = cls.validate(symbol)
        if children := [
            cls.from_form_symbol(child, sibling)
            for child in FormSymbol.sort(
                FormSymbol.util_iter_symbol_children(symbol, sibling)
            )
        ]:
            self.children = children

        return self

    def to_symbol(self):
        from robot_processor.symbol_table.models import Symbol, SymbolOption
        symbol = Symbol(
            type_spec=self.type_spec,
            name=self.name or "",
            label=self.label,
            children=[child.to_symbol() for child in self.children] if self.children is not None else None,
            options=SymbolOption.parse_obj(self.options.dict())
        )
        return symbol

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        kwargs.setdefault("exclude_none", True)
        return super().dict(**kwargs)


class SymbolTable(BaseModel):
    class Config:
        validate_assignment = True

    form_id: int
    version: str
    namespaces: list["SymbolTable.Namespace"] = Field(default_factory=list)

    class Namespace(BaseModel):
        """符号表中的一个命名空间"""

        name: str
        label: str
        symbols: list["SymbolTable.Symbol"] = Field(default_factory=list)
        namespaces: list["SymbolTable.Namespace"] = Field(default_factory=list)

        def append_namespace(self, namespace: "SymbolTable.Namespace"):
            namespaces = [*self.namespaces, namespace]
            self.namespaces = namespaces

    class Symbol(BaseModel):
        """符号表中的一个符号"""

        type_spec: TypeSpec
        name: str
        label: str | None
        children: list["SymbolTable.Symbol"] | None = None

        @classmethod
        def from_form_symbol(
            cls, symbol: "FormSymbol", sibling: list["FormSymbol"]
        ) -> Self:
            """从 FormSymbol 构造一个用于 SymbolTable 中描述的数据结构

            children 是有约束的
                - array 的 children.length == 1
                - collection 的 children.length > 0
                - other 的 children.length == 0
            在系统中把约束检查放到了 Insert 时执行，使用时不再做约束检查

            Args:
                symbol: 原始数据
                sibling: array 和 collection 的 children 信息在 FormSymbol 是同级保存的

            """
            from robot_processor.form.models import FormSymbol

            self = cls(
                type_spec=TypeSpec.validate(symbol.type_spec),
                name=symbol.name,
                label=symbol.label,
            )
            match self.type_spec:
                case TypeSpec(type="array") | TypeSpec(type="collection"):
                    self.children = [
                        cls.from_form_symbol(child, sibling)
                        for child in FormSymbol.util_iter_symbol_children(
                            symbol, sibling
                        )
                    ]

            return self

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        kwargs.setdefault("exclude_unset", True)
        kwargs.setdefault("exclude_none", True)
        return super().dict(**kwargs)


class SymbolTableEditableView(BaseModel):
    """符号表 - 工单模板编辑页视图"""

    form_id: int
    version: str
    steps: list["SymbolTableEditableView.Step"] = Field(default_factory=list)

    class Step(BaseModel):
        step_name: str
        step_uuid: str
        step_id: int
        symbols: list[FormSymbolEditableView] = Field(default_factory=list)

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        kwargs.setdefault("exclude_unset", True)
        kwargs.setdefault("exclude_none", True)
        return super().dict(**kwargs)


def validate_and_cast_value(raw_value: Any, type_spec: TypeSpec, validate_model=None):
    match type_spec.type:
        case "datetime":
            try:
                return Ok(
                    arrow.get(
                        raw_value, "YYYY-MM-DD HH:mm:ss", tzinfo=TIME_ZONE
                    ).datetime
                )
            except Exception as e:
                return Err(e)

        case "date":
            try:
                return Ok(arrow.get(raw_value, "YYYY-MM-DD", tzinfo=TIME_ZONE).date())
            except Exception as e:
                return Err(e)

        case "time":
            try:
                return Ok(arrow.get(raw_value, "HH:mm:ss", tzinfo=TIME_ZONE).time())
            except Exception as e:
                return Err(e)

        case "boolean":
            if not isinstance(raw_value, bool):
                return Err(
                    TypeError(f"期望类型: boolean, 得到错误类型: {type(raw_value).__name__}")
                )
            return Ok(raw_value)

        case "string":
            if not isinstance(raw_value, str):
                return Err(
                    TypeError(f"期望类型: string, 得到错误类型: {type(raw_value).__name__}")
                )
            return Ok(raw_value)

        case "number":
            if not isinstance(raw_value, (int, float)):
                return Err(
                    TypeError(f"期望类型: number, 得到错误类型: {type(raw_value).__name__}")
                )
            return Ok(raw_value)

        case "array":
            if not isinstance(raw_value, list):
                return Err(
                    TypeError(f"期望类型: array, 得到错误类型: {type(raw_value).__name__}")
                )
            if type_spec.spec is None:
                return Ok(raw_value)
            item_type_spec = first(type_spec.spec)
            children_validate_result = [
                validate_and_cast_value(child, item_type_spec) for child in raw_value
            ]
            array_value: Result[
                list[ValueType], Exception
            ] = list_result_to_result_list(children_validate_result)
            return array_value

        case "collection":
            if not isinstance(raw_value, dict):
                return Err(
                    TypeError(f"期望类型: collection, 得到错误类型: {type(raw_value).__name__}")
                )
            if type_spec.spec is None:
                return Ok(raw_value)
            collection_value = {}
            for spec in type_spec.spec:
                if spec.name in raw_value:
                    validate_and_cast_result = validate_and_cast_value(
                        raw_value[spec.name], spec
                    )
                    if validate_and_cast_result.is_err():
                        return validate_and_cast_result
                    else:
                        collection_value[spec.name] = validate_and_cast_result.unwrap()

            if validate_model and issubclass(validate_model, BaseModel):
                try:
                    model_value = validate_model.validate(collection_value)
                    return Ok(model_value)
                except Exception as e:
                    return Err(e)
            else:
                return Ok(collection_value)


ValueType = str | int | float | bool | list | dict | dt.datetime | dt.date | dt.time
T = TypeVar("T")


class Value(BaseModel):
    """在工单/工作流中使用的一个输入信息"""

    class Config:
        underscore_attrs_are_private = True

    _context: Any | None = None

    qualifier: Literal["const", "var", "fn", "extern", "undefined"]
    type_spec: TypeSpec

    const: Optional["ConstValue"] = None
    var: Optional["VarValue"] = None
    fn: Optional["FnValue"] = None

    class ConstValue(BaseModel):
        value: Any

    class VarValue(BaseModel):
        path: str

    class FnValue(BaseModel):
        function: str
        args: list["Arg"]

        class Arg(BaseModel):
            name: str
            value: "Value"

            @property
            def type_spec(self) -> TypeSpec:
                return self.value.type_spec

    NotInitializedError: ClassVar[Exception] = ValueError("变量未初始化")

    def context(self, context: dict[str, Any]):
        self._context = context
        return self

    @overload
    def get(self) -> Result[ValueType, Exception]:
        ...

    @overload
    def get(self, *, rtype: type[T]) -> Result[T, Exception]:
        ...

    def get(self, rtype=None):
        match self.qualifier:
            case "const":
                if self.const is None:
                    return Err(self.NotInitializedError)
                raw_value = self.const.value
            case "var":
                if self.var is None:
                    return Err(self.NotInitializedError)
                if self._context is None:
                    return Err(ValueError("引用变量的值需要提供 context"))
                try:
                    raw_value = jmespath_search(self.var.path, self._context)
                except jmespath.exceptions.JMESPathError as e:
                    return Err(e)
            case _ as qualifier:
                return Err(
                    NotImplementedError(f"qualifier {qualifier} not implemented")
                )
        if raw_value is None:
            return Err(self.NotInitializedError)

        return validate_and_cast_value(raw_value, self.type_spec, rtype)

    def get_var_path(self):
        if self.qualifier != "var":
            return Err(TypeError("当前 Value 不是 var 类型"))
        if self.var is None:
            return Err(self.NotInitializedError)
        return Ok(self.var.path)

    @classmethod
    def build_const(cls, type_spec: TypeSpec, value: ValueType) -> "Value":
        return cls(
            qualifier="const", type_spec=type_spec, const=Value.ConstValue(value=value)
        )

    @classmethod
    def build_undefined(cls, type_spec: TypeSpec) -> "Value":
        return cls(qualifier="undefined", type_spec=type_spec)

    def get_array_item_value_list(self) -> Result[list["Value"], Exception]:
        return do(
            Ok([Value.build_const(item_type_spec, item) for item in array_value])
            for item_type_spec in self.type_spec.get_array_item_spec()
            for array_value in self.get(rtype=list)
        )

    def get_collection_item_value(self, name: str) -> Result["Value", Exception]:
        if self.type_spec.type != "collection":
            return Err(TypeError("当前类型不是 collection"))
        if not self.type_spec.spec:
            return Err(ValueError("collection 类型未指定 spec"))
        if (collection_value_result := self.get(rtype=dict)).is_err():
            return Err(collection_value_result.unwrap_err())
        collection_value = collection_value_result.unwrap()
        if name not in collection_value or collection_value[name] is None:
            return Err(Value.NotInitializedError)

        for spec in self.type_spec.spec:
            if spec.name == name:
                return Ok(Value.build_const(spec, collection_value[name]))
        else:
            return Err(ValueError(f"collection 中没有 {name}"))

    @classmethod
    def from_pb2(cls, pb2_value: pb_Value) -> 'Value':
        dict_value = message_to_dict(pb2_value)
        dict_value.setdefault("qualifier", pb2_value.WhichOneof("value"))
        return cls.validate(dict_value)

    def __str__(self):
        prefix = f"{self.type_spec}"
        match self.qualifier:
            case "const":
                if self.const is None:
                    raw_value = "未初始化"
                else:
                    raw_value = self.const.value
                return f"{prefix} const(value={raw_value})"
            case "var":
                if self.var is None:
                    raw_value = "未初始化"
                else:
                    raw_value = self.var.path
                return f"{prefix} var(path={raw_value})"
            case _:
                return f"{prefix} {self.qualifier}({getattr(self, self.qualifier)})"

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        kwargs.setdefault("exclude_none", True)
        return super().dict(**kwargs)


def _update_forward_refs():
    TypeSpec.update_forward_refs()
    SymbolTable.Symbol.update_forward_refs()
    SymbolTable.Namespace.update_forward_refs()
    SymbolTableEditableView.update_forward_refs()
    Value.FnValue.Arg.update_forward_refs(Value=Value)
    Value.FnValue.update_forward_refs(Arg=Value.FnValue.Arg)
    Value.update_forward_refs(
        ConstValue=Value.ConstValue,
        VarValue=Value.VarValue,
        FnValue=Value.FnValue,
    )


_update_forward_refs()
