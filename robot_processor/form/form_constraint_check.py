import json
from itertools import product
from typing import List
from typing import Optional

from attrs import define, field

from robot_processor.form.models import Step


@define(kw_only=True, frozen=True, slots=True)
class WidgetInfoReferDescription:
    uuid: str = field()
    step_name: str = field()

    refer_by_rpa: Optional[bool] = field(default=None)
    refer_branch_name: Optional[str] = field(default=None)
    refer_display_rule_name: Optional[str] = field(default=None)


def check_widget_info_is_referred(form_id: int, widget_info_uuids: List[str]) -> List[WidgetInfoReferDescription]:
    steps = Step.Queries.steps(form_id=form_id, deleted=False).all()
    refer_description = []
    for widget_info_uuid, each_step in product(widget_info_uuids, steps):
        refer_description.extend(
            _widget_info_referred_by_step(widget_info_uuid, each_step)
        )
    return refer_description


def _widget_info_referred_by_step(widget_info_uuid: str, step: Step) -> List[WidgetInfoReferDescription]:

    refer_description = []
    # 被 RPA 任务引用做参数
    rpa_keymap_values = json.dumps(list((step.key_map or {}).values()))
    if widget_info_uuid in rpa_keymap_values:
        refer_description.append(
            WidgetInfoReferDescription(
                uuid=widget_info_uuid, step_name=step.name, refer_by_rpa=True  # type: ignore[arg-type]
            )
        )
    # 被分支条件引用
    for each_branch in step.branch or []:
        if "condition_group" not in each_branch:
            continue
        if widget_info_uuid in json.dumps(
            each_branch["condition_group"], ensure_ascii=False
        ):
            refer_description.append(
                WidgetInfoReferDescription(
                    uuid=widget_info_uuid,
                    step_name=step.name,  # type: ignore[arg-type]
                    refer_branch_name=each_branch["name"],
                )
            )
    # 被组件显隐规则引用
    for each_display_rule in step.display_rule or []:
        if "conditions" not in each_display_rule:
            continue
        if widget_info_uuid in json.dumps(
            each_display_rule["conditions"], ensure_ascii=False
        ):
            refer_description.append(
                WidgetInfoReferDescription(
                    uuid=widget_info_uuid,
                    step_name=step.name,  # type: ignore[arg-type]
                    refer_display_rule_name=each_display_rule["name"],
                )
            )

    return refer_description
