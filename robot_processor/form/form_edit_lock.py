"""工单模板编辑锁

在编辑工单模板时，需要对工单模板加锁，防止多人同时编辑

"""
from typing import Optional, TypedDict, Protocol

import arrow
from contextlib import contextmanager

from robot_processor.error.base import BizError
from robot_processor.db import no_auto_flush
from robot_processor.form.models import Form, Step


class LockInfo(TypedDict):
    key: str
    nick: str  # 锁当前持有人
    last_updated: int  # 锁相关的工单模板最近更新时间


class FormEditLockError(BizError):
    def __init__(self, lock_info: LockInfo, action):
        self.lock_info = lock_info
        self.action = action
        super().__init__(f"{lock_info.get('nick', '')} 正在编辑此工单模板")


class FormEditLockBrokerProtocol(Protocol):
    def get(self, key: str) -> Optional[LockInfo]:
        pass

    def set(self, key: str, lock_info: LockInfo, timeout: int):
        pass

    def delete(self, key: str):
        pass


class FormEditLock:
    __slots__ = ("user_nick", "form", "broker")
    LOCK_TIMEOUT = 30

    def __init__(
        self, user_nick: str, form: "Form", broker: Optional[FormEditLockBrokerProtocol] = None
    ):
        from robot_processor.ext import cache

        self.user_nick = user_nick
        self.form = form
        self.broker = broker or cache

    @property
    def key(self):
        from robot_processor.constants import EDIT_FORM_PREFIX

        return EDIT_FORM_PREFIX.format(self.form.id)

    def acquire(self):
        lock_info = self.broker.get(self.key)
        # 锁的持有人不是当前用户
        if lock_info and lock_info.get("nick") != self.user_nick:
            raise FormEditLockError(lock_info, "acquire")

        # 锁是可用状态，上锁🔒
        lock_info = lock_info or LockInfo(
            key=self.key, nick=self.user_nick, last_updated=self.get_form_last_updated()
        )
        self.broker.set(self.key, lock_info, self.LOCK_TIMEOUT)
        return lock_info

    def refresh(self):
        lock_info = self.acquire()
        lock_info["last_updated"] = arrow.now().int_timestamp
        self.broker.set(self.key, lock_info, self.LOCK_TIMEOUT)
        return lock_info

    def release(self):
        lock_info = self.broker.get(self.key)
        # 锁的持有人不是当前用户
        if lock_info and lock_info.get("nick") != self.user_nick:
            raise FormEditLockError(lock_info, "release")

        # 释放锁
        self.broker.delete(self.key)
        return lock_info

    @no_auto_flush()
    def get_form_last_updated(self):
        step = self.form.backend_steps.order_by(Step.updated_at.desc()).first()
        step_updated_at = int(step.updated_at if step else 0)
        form_updated_at = int(self.form.updated_at or 0)

        return max(step_updated_at, form_updated_at)

    @contextmanager
    def auto_refresh(self):
        self.acquire()
        yield self
        self.refresh()

    @contextmanager
    def auto_release(self):
        self.acquire()
        try:
            yield self
        finally:
            self.release()
