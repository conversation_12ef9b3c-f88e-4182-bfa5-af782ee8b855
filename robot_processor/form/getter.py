"""
`Step` index:
    - form_id
    - step_uuid + form_id
"""
from dataclasses import dataclass
from typing import Dict

from more_itertools import flatten

from robot_processor.assistant.schema import AssistantV2, default_form_owners_config
from robot_processor.enums import StepType, SelectType
from robot_processor.form.api.widget_schema import WidgetInfoDict


def get_non_ref_widgets_by_form_id(form_id: int) -> Dict[str, WidgetInfoDict]:
    from sqlalchemy.sql.operators import in_op
    from robot_processor.ext import db
    from robot_processor.form.models import Step
    from robot_processor.form.models import WidgetInfo

    with db.session.no_autoflush:
        step_id_subquery = Step.Queries.step_ids(form_id=form_id, is_dirty=False)
        widget_info_list = (
            WidgetInfo.query.join(
                Step, WidgetInfo.widget_collection_id == Step.widget_collection_id
            )
            .filter(
                in_op(Step.id, step_id_subquery),
                WidgetInfo.deleted.isnot(True),
                WidgetInfo.before.isnot(True),
            )
            .all()
        )
        return {obj.key: WidgetInfoDict(**obj.brief()) for obj in widget_info_list}  # type: ignore[typeddict-item]


def get_store_available_forms(org_id, sids, user_id: int, category=None):
    """
    通过sids及用户信息查找用户可访问的工单的模板（第一步配置了该客服）
    """
    from robot_processor.form.models import Form
    from robot_processor.enums import FormMold

    form_query = Form.Queries.by_sids(sids).filter(Form.form_mold != FormMold.BUYER)
    form_query = form_query.filter(Form.Filters.can_create)
    if category:
        form_query = form_query.filter(Form.category == category)
    form_map = dict((form.id, form) for form in form_query)
    all_form_id = list(form_map.keys())
    accessible_forms = filter_form_ids_creatable_by_user(org_id, all_form_id, user_id)
    return [form_map[form_id] for form_id in accessible_forms]


def filter_form_ids_creatable_by_user(org_id, all_form_ids: list[int], user_id: int) -> list[int]:
    """找出可以被指定用户拿来创建工单的模板id列表.

    :param org_id: 租户id
    :param all_form_ids: 待筛选的所有模板id
    :param user_id: 乐言用户 id
    :return: all_form_ids 的子集，且可以被 users 拿来创建工单的模板 id 列表
    """
    from robot_processor.form.models import Step
    from robot_processor.ext import db

    # TODO: 先反查乐言账号绑定的平台账号, 直接对比指派规则里的平台账号就可以了.
    #       这样可以减少查询, 不用把所有 step 的指派信息里的平台账号转成乐言账号了. 后续去除指派规则里的平台用户信息后, 可以下掉这个逻辑
    user = User.from_leyan_user_id(org_id, user_id)

    # 以下三个查询，用于找出和 all_form_id 对应的第一人工步骤
    # 之所以分为三个查询，是因为数据存在冗余现象：一个 step_uuid 存在多个 step_id， 单次查询后再去重容易增加数据库 IO 压力和应用处理时间
    # 第一个查询：为所有带 begin 步骤的 form 找出第一人工步骤的 step_uuid
    begin_steps: list[Step] = (db.session.query(Step).filter(
        Step.form_id.in_(all_form_ids)
    ).filter(
        Step.is_dirty.is_(False)
    ).filter(
        Step.step_type == StepType.begin
    ).all())
    # 取最新的起始步骤。
    form_begin_step_mapping: dict[int, Step] = {}
    for begin_step in begin_steps:
        if (s := form_begin_step_mapping.get(begin_step.form_id)) is None:  # type:ignore[arg-type]
            form_begin_step_mapping.update({begin_step.form_id: begin_step})  # type:ignore[dict-item]
        else:
            if begin_step.id > s.id:
                form_begin_step_mapping.update({begin_step.form_id: begin_step})  # type:ignore[dict-item]

    begin_steps = list(form_begin_step_mapping.values())

    new_first_human_steps = list(flatten(step.next_step_ids or [] for step in begin_steps))
    # 第二个查询：针对 step_uuid 去重, 找出 step_uuid 对应的最新的 step_id
    first_human_step_ids_query = Step.Queries.step_ids(form_id=all_form_ids, is_dirty=False,
                                                       step_uuid=new_first_human_steps, step_type=StepType.human)

    # 第三个查询：使用第二步中得到的 step id 找出各实际第一人工步骤的详细配置信息
    first_human_steps_query = (db.session
                               .query(Step.form_id, Step.assistants_v2, Step.assignee_groups)
                               .filter(Step.id.in_(first_human_step_ids_query)))
    accessible_forms = set()
    for human_step in first_human_steps_query:
        assistant = AssistantV2.from_assistants_and_assignee_group(
            human_step.assistants_v2 or {}, human_step.assignee_groups or {})
        if is_step_accessible_for_user(assistant, user):
            accessible_forms.add(human_step.form_id)

    # FIXME 工单管理员应该是工单模板纬度的配置，不应该放到 Step 上
    # 处理起始步骤存储的工单管理员信息。
    for begin_step in begin_steps:
        owners = AssistantV2.parse(begin_step.assistants_v2.get("owners") or default_form_owners_config())
        if is_step_accessible_for_user(owners, user):
            accessible_forms.add(begin_step.form_id)

    return list(accessible_forms)


def filter_form_ids_visible_by_user(org_id, all_form_ids: list[int], user_id: int):
    """找出可以被指定用户查看的工单的模板id列表.

    :param org_id: 租户id
    :param all_form_ids: 待筛选的所有模板id
    :param user_id: 乐言用户 id
    :return: all_form_ids 的子集，且可以被 users 查看的模板 id 列表
    """
    from collections import defaultdict, namedtuple
    from robot_processor.db import db
    from robot_processor.form.models import FormVersion, Step

    user = User.from_leyan_user_id(org_id, user_id)
    step_ids: list[int] = list({
        step_id
        for step_ids, in db.ro_session.query(FormVersion.step_id).filter(
            FormVersion.form_id.in_(all_form_ids), FormVersion.version_descriptor == "HEAD")
        for step_id in step_ids
    })
    steps = db.ro_session.query(Step.form_id, Step.step_type, Step.assistants_v2, Step.assignee_groups).filter(
        Step.id.in_(step_ids)
    ).all()
    step_info = namedtuple("step_info", ["assistants_v2", "assignee_groups"])
    form_step_map: dict[int, list[step_info]] = defaultdict(list)
    for form_id, step_type, assistants_v2, assignee_groups in steps:
        if step_type == StepType.begin:
            assistants_v2 = assistants_v2.get("owners") or default_form_owners_config()
            step = step_info(assistants_v2, {})
        else:
            step = step_info(assistants_v2, assignee_groups)
        form_step_map[form_id].append(step)

    visible_forms: list[int] = []
    for form_id in form_step_map:
        for step in form_step_map[form_id]:
            assistant = AssistantV2.from_assistants_and_assignee_group(
                step.assistants_v2 or {}, step.assignee_groups or {})
            if is_step_accessible_for_user(assistant, user):
                visible_forms.append(form_id)
                break
    return visible_forms


@dataclass
class User:
    leyan_user: int  # 乐言用户 id
    platform_users: list[int]  # 平台用户 id
    groups: list[str]  # 用户组 id [合并了乐言用户组和平台用户组]

    @classmethod
    def from_leyan_user_id(cls, org_id: int, user_id: int):
        from robot_processor.client import kiosk_client
        from robot_processor.enums import Creator

        platform_user_ids = kiosk_client.get_platform_user_ids_by_leyan_user_id(user_id)
        groups = list(kiosk_client.get_users_groups(
            org_id,
            [(user_id, Creator.LEYAN)] + [
                (platform_user_id, Creator.ASSISTANT) for platform_user_id in platform_user_ids
            ]
        ))
        return cls(user_id, platform_user_ids, groups)


def is_step_accessible_for_user(assistant: AssistantV2, user: User):
    if assistant.select_type == SelectType.all:
        return True
    if {group.group_uuid for group in assistant.assignee_groups if group.enable}.intersection(user.groups):
        return True
    if {account.user_id for account in assistant.channel_accounts}.intersection(user.platform_users):
        return True
    if user.leyan_user in {account.user_id for account in assistant.leyan_accounts}:
        return True
    return False


def is_form_has_transfer_info(form_id) -> bool:
    """通过 rpa=ALIPAY 判断工单模板是否包含打款信息"""
    from robot_processor.ext import db
    from robot_processor.form.models import Form
    from robot_processor.rpa_service.models import Rpa

    if form_id is None:
        return False

    with db.session.no_autoflush:
        if not (form := Form.query.get(form_id)):
            return False
        steps = form.job_steps.all()
        rpa_id_list = [step.rpa_id for step in steps if step.rpa_id]
        rpa_list = Rpa.Queries.by_ids(rpa_id_list)

    return any(rpa.task == "ALIPAY" for rpa in rpa_list)
