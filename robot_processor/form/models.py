import copy
import functools
import inspect
import json
import time
from collections import defaultdict
from collections import named<PERSON>ple
from datetime import datetime
from enum import Enum
from enum import StrEnum
from enum import auto
from functools import partial
from functools import update_wrapper
from itertools import groupby
from typing import TYPE_CHECKING
from typing import Any
from typing import ClassVar
from typing import Dict
from typing import List
from typing import Literal
from typing import Optional
from typing import Self
from typing import Set
from typing import Tuple
from typing import TypedDict
from typing import Union
from typing import cast

import arrow
import sqlalchemy as sa
import sqlalchemy.sql.operators as sa_op
from flask import current_app
from loguru import logger
from more_itertools import first
from pydantic import BaseModel
from pydantic import Field
from pydantic import ValidationError
from pydantic import root_validator
from pydantic import validator
from result import Err
from result import Ok
from result import Result
from robot_extension.util.decorator import classproperty
from robot_extension.util.mixin import LabeledEnum
from robot_types.core import Symbol
from robot_types.core import TypeSpec
from robot_types.helper import serialize
from robot_types.helper.form_composer import FormComposer
from sqlalchemy import Computed
from sqlalchemy import desc
from sqlalchemy import func
from sqlalchemy import select
from sqlalchemy import text
from sqlalchemy.ext.associationproxy import AssociationProxy
from sqlalchemy.ext.associationproxy import association_proxy
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import DynamicMapped
from sqlalchemy.orm import Load
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import Query
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import object_session
from sqlalchemy.orm import relationship
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy.sql import Subquery
from sqlalchemy_utils import ChoiceType

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.assistant.schema import AssistantV2
from robot_processor.client import action_client
from robot_processor.client import risk_control_client
from robot_processor.client.conf import app_config
from robot_processor.constants import ProvinceShortMap
from robot_processor.db import BasicMixin
from robot_processor.db import DbBaseModel
from robot_processor.db import in_transaction
from robot_processor.db import no_auto_flush
from robot_processor.enums import AssigneeRule
from robot_processor.enums import AutoRetryRecordStatus
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import FormMold
from robot_processor.enums import JobStatus
from robot_processor.enums import SelectType
from robot_processor.enums import StepType
from robot_processor.enums import VisibilityType
from robot_processor.enums import WidgetCategory
from robot_processor.error.validate import ValidateError
from robot_processor.ext import cache
from robot_processor.ext import db
from robot_processor.form.api.form_editor_schema import StepNotifyConfig
from robot_processor.form.api.widget_schema import StepWidget
from robot_processor.form.api.widget_schema import WidgetInfoDict
from robot_processor.form.event.models import EventType
from robot_processor.form.schemas import StepAutoJumpConfig
from robot_processor.form.schemas import StepValidationConfig
from robot_processor.form.symbol_table import FormSymbolEditableView
from robot_processor.form.symbol_table import FormSymbolScope
from robot_processor.form.symbol_table import SymbolTable
from robot_processor.form.symbol_table import SymbolTableEditableView
from robot_processor.form.symbol_table import TypeSpec
from robot_processor.form.types import ReportWidgetInfoIdent
from robot_processor.form.types import StepName
from robot_processor.form.types import WidgetDataType
from robot_processor.form.types import WidgetId
from robot_processor.form.types import WidgetInfoLabel
from robot_processor.form.types import WidgetType
from robot_processor.logging import vars as log_vars
from robot_processor.utils import get_nonce
from robot_processor.utils import get_obj_hash
from robot_processor.utils import jmespath_search
from robot_processor.utils import json_length
from robot_processor.utils import omit_none_fields_before_validate
from robot_processor.utils import sort_steps
from robot_processor.utils import unwrap_optional

if TYPE_CHECKING:
    from robot_processor.rpa_service.models import Rpa
    from robot_processor.shop.models import Shop


class FormTimeline(DbBaseModel, BasicMixin):
    """表单的历史数据"""

    __table_args__ = (sa.Index("sid_form_id", "sid", "form_id"),)
    sid: Mapped[str | None] = mapped_column(sa.String(32))
    form_id: Mapped[int | None] = mapped_column(sa.Integer, comment="对应的form id")
    data: Mapped[dict | None] = mapped_column(sa.JSON, comment="dict出来的json")

    deleted: Mapped[bool] = mapped_column(sa.Boolean, default=False)


class StepRetry(DbBaseModel, BasicMixin):
    """是否可以重试的配置"""

    step_uuid: Mapped[str | None] = mapped_column(sa.String(32))
    can_retry: Mapped[bool] = mapped_column(sa.Boolean, default=False)
    update_user: Mapped[str | None] = mapped_column(sa.String(128))

    deleted: Mapped[bool] = mapped_column(sa.Boolean, default=False)


class StepAutoRetry(DbBaseModel, BasicMixin):
    """自动重试的配置"""

    step_uuid: Mapped[str | None] = mapped_column(sa.String(32), index=True, unique=True)
    can_retry: Mapped[bool] = mapped_column(sa.Boolean, default=False)
    update_user: Mapped[str | None] = mapped_column(sa.String(128))
    # 重试次数和重试时间截止时间只会存在一种, 以下单位都是秒
    retry_interval: Mapped[int] = mapped_column(sa.Integer, default=0)
    retry_duration: Mapped[int] = mapped_column(sa.Integer, default=0, comment="重试持续时间")
    retry_times: Mapped[int] = mapped_column(sa.Integer, default=0, comment="重试次数")


class StepExceptionNotify(DbBaseModel, BasicMixin):
    """消息通知的配置"""

    step_uuid: Mapped[str | None] = mapped_column(sa.String(32), index=True, unique=True)
    update_user: Mapped[str | None] = mapped_column(sa.String(128))
    config: Mapped[dict] = mapped_column(sa.JSON, default=dict)  # StepNotifyConfig
    enable: Mapped[bool] = mapped_column(sa.Boolean, default=False, comment="是否启用")
    name: Mapped[str] = mapped_column(sa.String(32), comment="通知名称")


class JobAutoRetryRecord(DbBaseModel, BasicMixin):
    """
    任务自动重试记录
    """

    enable: Mapped[bool] = mapped_column(sa.Boolean, default=False, comment="是否进入自动重试")
    description: Mapped[str | None] = mapped_column(
        sa.String(128), comment="详情，若没有进入自动重试，描述原因 AutoRetryStatus"
    )
    job_id: Mapped[int | None] = mapped_column(sa.Integer, index=True)
    message_id: Mapped[str | None] = mapped_column(sa.String(128), index=True)
    eta: Mapped[int | None] = mapped_column(sa.Integer, comment="重试任务执行时间")
    seq: Mapped[int] = mapped_column(sa.Integer, comment="重试任务执行序号")
    status: Mapped[AutoRetryRecordStatus] = mapped_column(
        ChoiceType(AutoRetryRecordStatus, impl=sa.String(32)),
        nullable=False,
        default=AutoRetryRecordStatus.PENDING,
        comment="重试任务执行状态",
    )
    result: Mapped[dict | None] = mapped_column(sa.JSON, comment="重试任务执行结果")
    first_fail_timestamp: Mapped[int | None] = mapped_column(
        sa.Integer, comment="第一次失败时间,搭配StepAutoRetry.retry_duration使用"
    )


class StepSkip(DbBaseModel, BasicMixin):
    """是否可以跳过的配置"""

    step_uuid: Mapped[str | None] = mapped_column(sa.String(32))
    can_skip: Mapped[bool] = mapped_column(sa.Boolean, default=False)
    update_user: Mapped[str | None] = mapped_column(sa.String(128))

    deleted: Mapped[bool] = mapped_column(sa.Boolean, default=False)


class StepAutoSkip(DbBaseModel, BasicMixin):
    """任务失败是否可以自动跳过的配置"""

    step_uuid: Mapped[str | None] = mapped_column(sa.String(32))
    can_skip: Mapped[bool] = mapped_column(sa.Boolean, default=False)
    update_user: Mapped[str | None] = mapped_column(sa.String(128))

    deleted: Mapped[bool] = mapped_column(sa.Boolean, default=False)


class Step(DbBaseModel, BasicMixin):
    """步骤"""

    __ignore_columns__ = ["uuid", "created_at", "form", "assignee_groups", "raw_step"]

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    created_at: Mapped[int] = mapped_column(sa.Integer, default=time.time, nullable=False)
    updated_at: Mapped[int] = mapped_column(sa.Integer, default=time.time, onupdate=time.time, nullable=False)
    form_id: Mapped[int | None] = mapped_column(sa.Integer, sa.ForeignKey("form.id"))
    form: Mapped["Form"] = relationship(lambda: Form, back_populates="steps")
    name: Mapped[str | None] = mapped_column(sa.String(64), comment="步骤名称")
    description: Mapped[str | None] = mapped_column(sa.TEXT, comment="步骤说明")
    step_type: Mapped[StepType] = mapped_column(
        sa.Enum(StepType),
        default=StepType.auto,
        comment="步骤类型",
    )
    # {"group_uuid_1": 1, "group_uuid_2": 0}  1 enable, 0 disable
    assignee_groups: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="客服组")

    # nick 转 id 新增
    assistants_v2: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="执行客服")

    buyer_edit: Mapped[bool] = mapped_column(sa.Boolean, default=False, comment="是否允许买家填写")
    # 仅当允许买家填写时可编辑
    buyer_reply: Mapped[list] = mapped_column(sa.JSON, default=list, comment="买家引导话术")
    # 步骤完成时，工单的状态
    status: Mapped[JobStatus] = mapped_column(sa.Enum(JobStatus), default=JobStatus.PENDING)
    # 前置节点
    prev_step_ids: Mapped[list] = mapped_column(sa.JSON, default=list)
    # 后置节点
    next_step_ids: Mapped[list] = mapped_column(sa.JSON, default=list)
    # 仅当有task_id时候才有效
    # uid: aaaa
    # url: http://xxxxx.com
    key_map: Mapped[dict] = mapped_column(sa.JSON, default=dict)

    widget_collection_id: Mapped[str | None] = mapped_column(sa.String(32))
    widget_collection: Mapped[Optional["WidgetCollection"]] = relationship(
        "WidgetCollection",
        primaryjoin="foreign(cast(Step.widget_collection_id, Integer))==WidgetCollection.id",
    )
    # 多版本需要一个唯一id，来标示属于同一个step
    step_uuid: Mapped[str] = mapped_column(sa.String(32), default=get_nonce)
    # 是否为脏; form 保存以后，设置为false
    is_dirty: Mapped[bool] = mapped_column(sa.Boolean, default=True)
    # 没有publish之前不能删除

    data: Mapped[dict] = mapped_column(sa.JSON, default=dict)

    branch: Mapped[list] = mapped_column(sa.JSON, default=list)

    # 跳转步骤的相关配置
    jump: Mapped[dict] = mapped_column(sa.JSON, default=dict)

    deleted: Mapped[bool] = mapped_column(sa.Boolean, default=False)
    # 通知信息
    notifier: Mapped[dict] = mapped_column(sa.JSON, default=dict)

    message_digest: Mapped[str | None] = mapped_column(sa.String(32), comment="判断两个工单是否相同")  # 已废弃
    assignee_rule: Mapped[AssigneeRule] = mapped_column(
        sa.Enum(AssigneeRule), default=AssigneeRule.RANDOM, comment="分派规则"
    )
    display_rule: Mapped[list] = mapped_column(sa.JSON, default=list, comment="组件显隐规则")
    raw_step: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="step快照")
    parent: Mapped[str | None] = mapped_column(sa.String(32))

    # 当 step_type=event 时，配置生效
    event: Mapped[EventType | None] = mapped_column(sa.Enum(EventType, native_enum=False, length=128))
    event_shortcuts: Mapped[list | None] = mapped_column(sa.JSON, default=list, comment="事件选择的常用规则")
    event_filters: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="事件配置的自定义过滤条件")

    @property
    def event_shortcut(self):
        if self.event_shortcuts:
            return self.event_shortcuts[0]
        return None

    @event_shortcut.setter
    def event_shortcut(self, new):
        if new:
            self.event_shortcuts = [new]
        else:
            self.event_shortcuts = None

    @property
    def validation_config(self):
        if obj := (self.data or {}).get("validation_config"):
            return StepValidationConfig.parse_obj(obj)
        else:
            return StepValidationConfig.default()

    @validation_config.setter
    def validation_config(self, new: StepValidationConfig | dict):
        data = (self.data or {}).copy()
        if isinstance(new, dict):
            data["validation_config"] = new
        else:
            data["validation_config"] = new.dict()
        self.data = data

    def clone(self):
        dolly = Step()
        dolly.form_id = self.form_id
        dolly.name = self.name
        dolly.description = self.description
        dolly.step_type = self.step_type
        dolly.assignee_groups = self.assignee_groups
        dolly.assistants_v2 = self.assistants_v2
        dolly.buyer_edit = self.buyer_edit
        dolly.buyer_reply = self.buyer_reply
        dolly.status = self.status
        dolly.prev_step_ids = self.prev_step_ids
        dolly.next_step_ids = self.next_step_ids
        dolly.key_map = self.key_map
        dolly.widget_collection_id = self.widget_collection_id
        dolly.step_uuid = self.step_uuid
        dolly.is_dirty = self.is_dirty
        dolly.data = self.data
        dolly.branch = self.branch
        dolly.jump = self.jump
        dolly.deleted = self.deleted
        dolly.notifier = self.notifier
        dolly.assignee_rule = self.assignee_rule
        dolly.display_rule = self.display_rule
        dolly.event = self.event
        dolly.event_shortcuts = self.event_shortcuts
        dolly.event_filters = self.event_filters

        return dolly

    def wraps(self, shop):
        """提供当前 step 在指定 shop 下的 view.

        :param shop: Shop 实例, 且该 shop 应该通过 FormShop 表和当前 form 进行了绑定
        :return: StepWrapper
        """
        assert isinstance(shop, _resolve_shop_model()), "wraps 接受 Shop 的实例，请检查代码错误"
        return StepWrapper(self, self.form.wraps(shop))

    @property
    def is_wrapper(self):
        return not isinstance(self, Step)

    @no_auto_flush()
    def get_latest_published_step(self, need_same_step_type=False):
        """获取当前步骤的最新的已发布的步骤

        Args:
            need_same_step_type:
                如人工步骤修改为自动步骤，就拿不到执行客服的信息
                但是已创建的工单是需要执行客服信息的
                就去找最新的人工步骤并返回
        """
        query = (
            object_session(self)  # type: ignore[union-attr]
            .query(Step)
            .filter_by(
                step_uuid=self.step_uuid,
                form_id=self.form_id,
                is_dirty=False,
                deleted=False,
            )
        )
        if need_same_step_type:
            query = query.filter_by(step_type=self.step_type)

        return query.order_by(Step.id.desc()).first()

    def get_assistants_v2(self, only_get_assignees: bool = True):
        assistants_v2 = copy.deepcopy(self.assistants_v2 or {})
        if only_get_assignees:
            if assistants_v2.get("assignees"):
                assignees = assistants_v2.get("assignees") or {}
            else:
                assignees = assistants_v2
            if assignees.get("select_type") == SelectType.part:
                assignees.setdefault(
                    "assignee_groups",
                    [
                        {"group_uuid": group_uuid, "enable": enable}
                        for group_uuid, enable in (self.assignee_groups or {}).items()
                    ],
                )
            return AssistantV2.parse(assignees)

    def set_assistants_v2(self, value: Union[dict, AssistantV2]):
        # 如果更新了 assignee group，将信息同步到字段上
        if isinstance(value, dict):
            assistant_v2 = AssistantV2.parse(value)
        else:
            assistant_v2 = value
        self.assignee_groups = {
            assignee_group.group_uuid: assignee_group.enable for assignee_group in assistant_v2.assignee_groups
        }
        # 避免覆盖 owners。
        origin_assistants_v2: dict = self.assistants_v2 or {}
        owners = origin_assistants_v2.get("owners") or {}
        current_assistants_v2: dict = assistant_v2.dict()
        self.assistants_v2 = {
            "owners": owners,
            "assignees": current_assistants_v2,
            **current_assistants_v2,
        }

    def set_owners(self, value: dict | AssistantV2) -> None:
        """
        向起始步骤中插入工单管理员信息。
        """
        if not self.is_begin():
            return None
        if isinstance(value, dict):
            assistant_v2 = AssistantV2.parse(value)
        else:
            assistant_v2 = value
        origin_assistants_v2: dict = self.assistants_v2 or {}
        origin_assistants_v2.update({"owners": assistant_v2.dict()})
        self.assistants_v2 = origin_assistants_v2
        flag_modified(self, "assistants_v2")

    # FIXME: 涉及到数据库查询的，不使用 property
    @property
    def can_retry(self):
        retry = StepRetry.query.filter_by(step_uuid=self.step_uuid).first()
        if retry:
            return retry.can_retry
        elif self.step_type == StepType.human:
            # 人工任务默认允许retry
            return True
        return False

    # FIXME: 涉及到数据库查询的，不使用 property
    @can_retry.setter
    def can_retry(self, value):
        if not self.step_uuid:
            self.step_uuid = get_nonce()
        retry = StepRetry.find_or_create(step_uuid=self.step_uuid)
        retry.can_retry = value

    @property
    def can_skip(self):
        skip = StepSkip.query.filter_by(step_uuid=self.step_uuid).first()
        if skip:
            return skip.can_skip
        return False

    @can_skip.setter
    def can_skip(self, value):
        if not self.step_uuid:
            # 创建步骤时未 flush 的 session 的step 还不会赋默认值 导致 retry 的配置失败
            self.step_uuid = get_nonce()
        retry = StepSkip.find_or_create(step_uuid=self.step_uuid)
        retry.can_skip = value

    @property
    def can_auto_skip_when_fail(self):
        skip = StepAutoSkip.query.filter_by(step_uuid=self.step_uuid).first()
        if skip:
            return skip.can_skip
        return False

    @can_auto_skip_when_fail.setter
    def can_auto_skip_when_fail(self, value):
        if not self.step_uuid:
            # 创建步骤时未 flush 的 session 的step 还不会赋默认值 导致 retry 的配置失败
            self.step_uuid = get_nonce()
        retry = StepAutoSkip.find_or_create(step_uuid=self.step_uuid)
        retry.can_skip = value

    @property
    def auto_retry_config(self):
        retry = StepAutoRetry.query.filter_by(step_uuid=self.step_uuid).first()
        if retry:
            return retry.to_dict()
        return None

    @auto_retry_config.setter
    def auto_retry_config(self, value):
        if value is None:
            return
        if not self.step_uuid:
            # 创建步骤时未 flush 的 session 的step 还不会赋默认值 导致 retry 的配置失败
            self.step_uuid = get_nonce()
        retry = StepAutoRetry.find_or_create(step_uuid=self.step_uuid)
        for k, v in value.items():
            setattr(retry, k, v)

    @property
    def notify_configs(self) -> List[StepExceptionNotify]:
        # 暂时只返回异常相关的通知，后续需要补充其他类型的通知
        other_configs = []
        other_configs.extend(StepExceptionNotify.query.filter_by(step_uuid=self.step_uuid).all())
        return other_configs

    @notify_configs.setter
    def notify_configs(self, value: List[dict]):
        configs = [StepNotifyConfig(**i) for i in value]
        # 查询该 step_uuid 所配置的所有通知规则。
        step_notify_configs: list[StepExceptionNotify] = StepExceptionNotify.query.filter(
            StepExceptionNotify.step_uuid == self.step_uuid,
        ).all()
        step_notify_configs_mapping: dict[str, StepExceptionNotify] = {snc.name: snc for snc in step_notify_configs}
        for config in configs:
            notify = step_notify_configs_mapping.get(config.name)
            if notify is None:
                notify = StepExceptionNotify(step_uuid=self.step_uuid, name=config.name)
            else:
                # 如果获取到了配置信息，则将其从 mapping 中移出。
                step_notify_configs_mapping.pop(config.name)
            notify.enable = config.enabled
            notify.config = config.dict()
            db.session.add(notify)
        # 删除冗余的配置。
        for notify_config in step_notify_configs_mapping.values():
            db.session.delete(notify_config)

    def get_notify_configs(self) -> List[dict]:
        res = [i.to_dict() for i in self.notify_configs]
        for i in res:
            i.update(i["config"])
        return res

    def get_auto_jump_config(self) -> StepAutoJumpConfig:
        """
        获取跳转配置。
        :return:
        """
        return StepAutoJumpConfig(**(self.jump or {}))

    @property
    def rpa_context(self):
        from sqlalchemy.orm import Load

        from robot_processor.rpa_service.models import Rpa
        from robot_processor.rpa_service.models import RpaContext

        return (
            RpaContext.query.filter_by(org_id=str(self.form.get_org_id()), rpa_id=self.rpa_id)
            .options(Load(RpaContext).joinedload(RpaContext.rpa).joinedload(Rpa.limits))
            .first()
        )

    @property
    def task(self):
        if rpa_context := self.rpa_context:
            return rpa_context.rpa
        return None

    @property
    def rpa_id(self):
        data = self.data
        if not data:
            return None
        rpa_id = data.get("rpa_id")
        if not rpa_id:
            return None
        return int(rpa_id)

    @property
    def widget_ref(self):
        """
        当前步骤引用的组件，不同类型的步骤会有不同的action
        在遍历网关中：表示要遍历的组件
        """
        widget_ref_obj = (self.data or {}).get("widget_ref")
        if widget_ref_obj is None:
            return
        return WidgetRef.parse(widget_ref_obj)

    @widget_ref.setter
    def widget_ref(self, value):
        if value is not None:
            assert isinstance(
                value, (dict, WidgetRef)
            ), f"不支持的类型, 仅支持 dict 和 WidgetRef, 传入的类型为 {type(value)}"
        if isinstance(value, dict):
            value = WidgetRef.parse_obj(value)

        data = self.data or {}
        if value is None:
            widget_ref_obj = None
        else:
            widget_ref_obj = value.dict()
        data["widget_ref"] = widget_ref_obj
        self.data = data
        flag_modified(self, "data")

    @property
    def coupled_step_uuid(self) -> Optional[str]:
        """
        用于表示成对节点的指向；左节点指向右边节点；右节点指向左节点
        如遍历拆封的coupled_step_uuid指向遍历合并的step_uuid
        """
        _data = self.data or {}
        return _data.get("coupled_step_uuid")

    @coupled_step_uuid.setter
    def coupled_step_uuid(self, value: str):
        if not value:
            return
        _data = self.data or {}
        _data["coupled_step_uuid"] = value
        self.data = _data
        flag_modified(self, "data")

    def set_task_id(self, task_id):
        data = self.data or {}
        if task_id is None:
            data["rpa_id"] = None
        else:
            data["rpa_id"] = int(task_id)
        self.data = data
        flag_modified(self, "data")

    def set_step_type(self, value):
        self.step_type = StepType(value)

    def set_status(self, value):
        self.status = JobStatus(value) if value else None  # type: ignore[assignment]

    def set_symbols(self, symbols: list["FormSymbolEditableView"] | list[Symbol]):
        scope = FormSymbolScope.from_step(self)
        FormSymbol.update_form_scope_symbol(scope, symbols)

    @in_transaction()
    def compatible_set_symbols(self, symbols: list["FormSymbolEditableView"], rewrite_widget_collection: bool):
        from robot_processor.form.compatible_utils import symbol_editable_view_to_widget_info

        self.set_symbols(symbols)
        if rewrite_widget_collection:
            component_map = dict((component.id, component) for component in Component.query)
            widget_map = dict((widget.id, widget) for widget in Widget.query)
            widget_info_list = [
                symbol_editable_view_to_widget_info(symbol, widget_map, component_map) for symbol in symbols
            ]
            widget_collection = WidgetCollection()
            for idx, widget_info in enumerate(widget_info_list):
                widget_info.order = idx
                widget_info.widget_collection = widget_collection
            db.session.add_all([widget_collection, *widget_info_list])
            db.session.flush()
            self.widget_collection_id = widget_collection.id  # type: ignore[assignment]

    def compatible_set_symbols_old(self, widget_collection_id: str):
        from robot_processor.form.compatible_utils import widget_info_to_editable_view

        widget_info_list = WidgetInfo.query.filter(WidgetInfo.widget_collection_id == widget_collection_id).all()
        widget_map = dict((widget.id, widget) for widget in Widget.query)
        component_map = dict((component.id, component) for component in Component.query)
        self.set_symbols(
            [widget_info_to_editable_view(widget_info, widget_map, component_map) for widget_info in widget_info_list]
        )

    def brief(self) -> dict:
        return {
            "id": self.id,
            "name": self.name,
            "step_uuid": self.step_uuid,
            "prev_step_ids": self.prev_step_ids,
            "next_step_ids": self.next_step_ids,
        }

    def to_dict(self, nested=True):
        result = super().to_dict(nested)
        # 看上去前端实际上并不依赖 `updated_user` 这个字段, 可以考虑去除这个 hack
        # https://devi-zoekt.infra.leyantech.com/search?q=updated_user+%28lang%3ATypeScript+or+lang%3AJavaScript%29
        result["updated_user"] = self.form.update_user
        return result

    def is_begin(self):
        return self.step_type == StepType.begin

    def is_human(self):
        return self.step_type == StepType.human

    def is_auto(self):
        return self.step_type == StepType.auto

    def is_gateway(self):
        return self.step_type in (
            StepType.exclusive_gateway,
            StepType.iterate_gw_begin,
            StepType.iterate_gw_end,
        )

    def get_widget_labels(self) -> tuple:
        """
        获取步骤中所有组件的label, 已确保 label 按名称排序.
        """
        labels: list[str] = []
        wc = self.widget_collection
        if not wc:
            return tuple(labels)
        for widget_info in wc.widget_info:
            labels.append(widget_info.label)
        labels.sort()
        return tuple(labels)

    @no_auto_flush()
    def get_ui_schema(self) -> list[dict]:
        """返回步骤的表单配置"""
        ui_schema: list[dict] = []

        if not self.widget_collection:
            return ui_schema

        for widget_info in self.widget_collection.widget_info:
            ui_schema.append(widget_info.brief())

        return ui_schema

    @in_transaction()
    def update(self, update_user: str, **update_data):
        """工单更新"""

        update_data.pop("id", None)  # 防止误传 id
        form_scope: tuple[FormSymbolScope, FormSymbolScope] | None = None
        if self.is_dirty is False:
            # 对于已发布步骤，先复制再修改
            # 不参与复制的字段包括："id", "created_at", "updated_at", "is_dirty", "message_digest", "raw_step"
            step = Step(
                form_id=self.form_id,
                name=self.name,
                step_type=self.step_type,
                description=self.description,
                buyer_edit=self.buyer_edit,
                buyer_reply=self.buyer_reply,
                status=self.status,
                prev_step_ids=self.prev_step_ids,
                next_step_ids=self.next_step_ids,
                key_map=self.key_map,
                widget_collection_id=self.widget_collection_id,
                step_uuid=self.step_uuid,
                data=self.data,
                branch=self.branch,
                jump=self.jump,
                deleted=self.deleted,
                notifier=self.notifier,
                assignee_rule=self.assignee_rule,
                assistants_v2=self.assistants_v2,
                display_rule=self.display_rule,
                event=self.event,
                event_shortcuts=self.event_shortcuts,
                event_filters=self.event_filters,
            )
            db.session.add(step)
            db.session.flush()
            form_scope = (
                FormSymbolScope.from_step(self),
                FormSymbolScope.from_step(step),
            )
        else:
            step = self
        for key, value in update_data.items():
            if key == "step_type":
                step.set_step_type(value)
            elif key == "status":
                step.set_status(value)
            elif key == "task_id":
                step.set_task_id(value)
            elif key == "owners":
                step.set_owners(value)
            elif key == "assistants_v2":
                step.set_assistants_v2(value)
            elif key == "symbols":
                # 先走兼容方案
                # step.set_symbols(value)
                pass
            elif key == "event":
                # 目前前端传递过来的 event 会是 name，而不是 value
                step.event = EventType[value] if value else None
            else:
                setattr(step, key, value)

        if update_data.get("symbols"):
            rewrite_widget_collection = "widget_collection_id" not in update_data
            step.compatible_set_symbols(
                [FormSymbolEditableView.validate(symbol) for symbol in update_data["symbols"]],
                rewrite_widget_collection,
            )
        elif update_data.get("widget_collection_id"):
            step.compatible_set_symbols_old(update_data["widget_collection_id"])
        elif form_scope is not None:
            FormSymbol.update_from_step_copy(*form_scope)

        self.form.step_modified(update_user=update_user)
        return step

    @no_auto_flush()
    def get_task(self):
        from robot_processor.rpa_service.models import Rpa
        from robot_processor.rpa_service.models import RpaContext

        task_info: dict[str, Any] = {}
        if not (rpa_id := (self.data or {}).get("rpa_id")):
            return task_info

        rpa: Optional[Rpa] = Rpa.query.get(rpa_id)
        if not rpa:
            return task_info
        task_info["id"] = rpa.id
        task_info["name"] = rpa.name
        task_info["task_type"] = rpa.task
        task_info["description"] = rpa.description
        task_info["tag"] = rpa.tag
        task_info["arguments"] = rpa.arguments
        task_info["argument"] = rpa.argument
        task_info["context"] = {}
        rpa_context: RpaContext | None = RpaContext.query.filter(
            RpaContext.rpa == rpa,
            RpaContext.org_id == self.form.get_org_id(),
        ).first()
        if rpa_context:
            task_info["context"].update(rpa_context.common)

        return task_info

    @in_transaction()
    @no_auto_flush()
    def update_raw_step(self):
        """更新快照信息"""
        raw_step: Step.View.RawStep = self.View.RawStep.from_orm(self)
        self.raw_step = raw_step.dict()

    @in_transaction()
    def pipeline_update(self, update_user: str, **update_data):
        # 如果 step.is_dirty=False，说明当前步骤已经是已发布状态，需要创建一个新的步骤
        step = self.update(update_user=update_user, **update_data)
        step.update_raw_step()
        return step

    @in_transaction()
    def remove_refer_widget_info_in_widget_collection(self, key_to_remove: List[str], operator_nick: str) -> list[str]:
        """删除其他步骤里引用本步骤的 widget info"""
        if not self.widget_collection:
            return []

        records = self.widget_collection.widget_info.filter(
            WidgetInfo.key.in_(key_to_remove), WidgetInfo.before.is_(True)
        ).all()
        if not records:
            return []

        # 记录一下本步骤引用了的 widget info key
        removed_widget_info_keys = [record.key for record in records]
        widget_collection = self.widget_collection.remove_refer_widget_info(removed_widget_info_keys)
        if self.widget_collection != widget_collection:
            self.pipeline_update(update_user=operator_nick, widget_collection=widget_collection)
        return removed_widget_info_keys

    @in_transaction()
    @no_auto_flush()
    def delete(self):
        self.widget_collection and self.widget_collection.delete()
        Step.query.filter(Step.form_id == self.form_id, Step.step_uuid == self.step_uuid).update({Step.deleted: True})
        StepRetry.query.filter(StepRetry.step_uuid == self.step_uuid).update({StepRetry.deleted: True})

    @classmethod
    def get_step_widgets(cls, step_id: int) -> list[StepWidget]:
        with db.session.no_autoflush:
            return (
                db.session.query(Widget.label, WidgetInfo.key, WidgetInfo.option_value)
                .select_from(Step)
                .join(
                    WidgetInfo,
                    WidgetInfo.widget_collection_id == Step.widget_collection_id,
                )
                .join(Widget, WidgetInfo.widget_id == Widget.id)
                .where(Step.id == step_id)
                .order_by(WidgetInfo.order)
                .all()
            )

    @property
    def component_value_unique_widgets(self):
        widgets: list[dict] = []
        for widget_info_obj in self.raw_step.get("ui_schema", []):
            if widget_info_obj.get("before") is True:
                continue
            if not widget_info_obj.get("option_value", {}).get("valueUnique"):
                continue
            widgets.append(widget_info_obj)
        return widgets

    def get_event_validator(self, shop, form_version_id: int):
        from robot_processor.business_order.business_order_manager import FormValidator

        return FormValidator.init_event_filter(shop=shop, step=self, form_version_id=form_version_id)

    def get_form_validator(self, shop, form_composer):
        from robot_processor.business_order.business_order_manager import FormValidator

        return FormValidator.init_form_validator(shop=shop, form_composer=form_composer, step=self)

    def get_validator_value_unique_dimensions(self, data: dict, shop, form_version_id):
        from robot_processor.business_order.business_order_manager import BusinessManager
        from robot_processor.business_order.models import get_form_composer

        dimensions_tuple: set[tuple[str, str]] = set()
        # 表单校验
        if self.validation_config.enabled:
            form_composer = get_form_composer(form_version_id)
            form_validator = self.get_form_validator(shop=shop, form_composer=form_composer)
            prepare_dimensions = form_validator.prepare_dimensions(data)
            for check_key in prepare_dimensions:
                for value in prepare_dimensions[check_key]:
                    dimensions_tuple.add((check_key, value))
        # 智能建单筛选规则
        if self.step_type == StepType.event:
            form_validator = self.get_event_validator(shop=shop, form_version_id=form_version_id)
            if form_validator.enabled:
                raw_event_data = data.get(BusinessManager.RAW_EVENT_DATA_FIELD, {})
                prepare_dimensions = form_validator.prepare_dimensions(raw_event_data)
                for check_key in prepare_dimensions:
                    for value in prepare_dimensions[check_key]:
                        dimensions_tuple.add((check_key, value))
        dimensions = [{"dimension": key, "dimension_value": value} for key, value in dimensions_tuple]
        return dimensions

    def get_component_value_unique_dimensions(self, data: dict):
        """FIXME 在查询组件可用之前，要保留组件上的值唯一配置（仅用于重复提示）"""
        dimensions_tuple: set[tuple[str, str]] = set()
        for widget_info_obj in self.raw_step.get("ui_schema", []):
            if widget_info_obj.get("before") is True:
                continue
            if not widget_info_obj.get("option_value", {}).get("valueUnique"):
                continue
            if widget_info_obj["key"] not in data:
                continue
            value = data[widget_info_obj["key"]]
            for check_key, value_repr in risk_control_client.widget_value_unique(widget_info_obj, value):
                if isinstance(value_repr, list):
                    for item_value in value_repr:
                        dimensions_tuple.add((check_key, str(item_value)))
                else:
                    dimensions_tuple.add((check_key, str(value_repr)))
        dimensions = [{"dimension": key, "dimension_value": value} for key, value in dimensions_tuple]
        return dimensions

    def get_merged_value_unique_dimensions(self, data, shop, form_version_id):
        dimensions_tuple: set[tuple[str, str]] = set()
        for dimension in self.get_validator_value_unique_dimensions(data, shop, form_version_id):
            dimensions_tuple.add((dimension["dimension"], dimension["dimension_value"]))
        for dimension in self.get_component_value_unique_dimensions(data):
            dimensions_tuple.add((dimension["dimension"], dimension["dimension_value"]))
        dimensions = [{"dimension": key, "dimension_value": value} for key, value in dimensions_tuple]
        return dimensions

    class Utils:
        @staticmethod
        def raw_step_optional(step: "Step") -> dict:
            return step.raw_step or {}

        @staticmethod
        def raw_ui_schema(step: "Step") -> list[dict]:
            """返回步骤的表单配置（从 raw_step 中）"""
            return Step.Utils.raw_step_optional(step).get("ui_schema", [])

        @staticmethod
        def raw_can_retry(step: "Step") -> bool:
            """返回步骤是否可以重试的配置（从 raw_step 中）"""
            return Step.Utils.raw_step_optional(step).get("can_retry", False)

        @staticmethod
        def raw_can_skip(step: "Step") -> bool:
            """返回步骤是否可以跳过的配置（从 raw_step 中）"""
            return Step.Utils.raw_step_optional(step).get("can_skip", False)

        @staticmethod
        def raw_can_auto_skip_when_fail(step: "Step") -> bool:
            """返回步骤是否可以跳过的配置（从 raw_step 中）"""
            return Step.Utils.raw_step_optional(step).get("can_auto_skip_when_fail", False)

        @staticmethod
        def raw_auto_retry_config(step: "Step") -> dict | None:
            """返回步骤的重试配置（从 raw_step 中）"""
            return Step.Utils.raw_step_optional(step).get("auto_retry_config")

        @staticmethod
        def raw_auto_jump_config(step: "Step") -> dict | None:
            """返回步骤的跳转配置（从 raw_step 中）"""
            return Step.Utils.raw_step_optional(step).get("jump")

        @staticmethod
        def raw_task(step: "Step") -> dict:
            """返回步骤的任务配置（从 raw_step 中）"""
            return Step.Utils.raw_step_optional(step).get("task", {})

    class Queries:
        @staticmethod
        def step_ids(
            form_id: list[int] | int | str | None = None,
            deleted: bool | None = None,
            is_dirty: bool | None = None,
            step_uuid: list[str] | str | None = None,
            step_type: StepType | None = None,
        ) -> list[int]:
            """查找符合条件的工单步骤的最新版本 ID.

            Args:
                form_id: 工单 ID
                deleted: 是否标记过软删除，如果为 None，则表示不关心是否标记过软删除
                is_dirty: 是否尚未发布，如果为 None，则表示不关心是否尚未发布；如果为 True，表示只查找尚未发布的步骤；如果为 False，表示只查找已发布的步骤
                step_uuid: 步骤唯一标识, 由于历史原因，一个 step_uuid 可能对应多个 step.id，这个函数会找出最大的那个 id
                step_type: 步骤类型，如 human begin 等

            Returns:
                符合筛选条件的 step ID 列表
            """
            if form_id is None and step_uuid is None:
                logger.error("form_id 和 step_uuid 不能同时为空, 否则容易触发全表扫描")
                return []

            query = Step.query.with_entities(func.max(Step.id).label("id")).group_by(Step.step_uuid)
            if isinstance(form_id, list):
                query = query.filter(Step.form_id.in_(form_id))
            elif form_id is not None:
                query = query.filter(Step.form_id == form_id)

            if deleted is True:
                query = query.filter(Step.deleted.is_(True))
            elif deleted is False:
                query = query.filter(Step.deleted.isnot(True))

            if is_dirty is True:
                query = query.filter(Step.is_dirty.is_(True))
            elif is_dirty is False:
                query = query.filter(Step.is_dirty.isnot(True))

            if isinstance(step_uuid, list):
                query = query.filter(Step.step_uuid.in_(step_uuid))
            elif step_uuid is not None:
                query = query.filter(Step.step_uuid == step_uuid)

            if step_type is not None:
                query = query.filter(Step.step_type == step_type)
            return [s.id for s in query]

        @staticmethod
        def steps(
            form_id: list[int] | int | str,
            deleted: bool | None = None,
            is_dirty: bool | None = None,
            step_uuid: list[str] | str | None = None,
        ) -> Query["Step"]:
            step_ids = Step.Queries.step_ids(form_id=form_id, deleted=deleted, is_dirty=is_dirty, step_uuid=step_uuid)
            return Step.query.filter(Step.id.in_(step_ids))

        @staticmethod
        def by_step_uuid(step_uuid: str):
            return Step.query.filter(Step.step_uuid == step_uuid).order_by(Step.id.desc())

        @staticmethod
        def auto_retry_config(step):
            return StepAutoRetry.query.filter_by(step_uuid=step.step_uuid).first()

    class Schema:
        class StepData(BaseModel):
            rpa_id: Optional[int] = Field(default=None, description="自动化步骤使用的 rpa 任务 id")
            widget_ref: Optional[Any] = Field(default=None, description="步骤引用的组件")
            coupled_step_uuid: Optional[str] = Field(default=None, description="成对节点的指向")

        class AutoRetryConfig(BaseModel):
            class Config:
                orm_mode = True

            can_retry: bool = Field(description="是否可以自动重试")
            retry_interval: int = Field(description="重试间隔")
            retry_duration: int = Field(description="重试时长")
            retry_times: int = Field(description="重试次数")

        class NotifierUnit(StrEnum):
            NONE = "NONE"
            MINUTE = "MINUTE"
            HOUR = "HOUR"
            DAY = "DAY"
            TODAY = "TODAY"

        @omit_none_fields_before_validate("force_task_notifier")
        class Notifier(BaseModel):
            """超时提醒配置"""

            enabled: Optional[bool] = Field(default=False, description="是否开启")
            value: Optional[str] = Field(default=None, description="超时时间")
            unit: Optional["Step.Schema.NotifierUnit"] = Field(default=None, description="超时时间单位")

            deadline_enabled: Optional[bool] = Field(default=False, description="是否开启截止时间")
            deadline_value: Optional[int] = Field(default=None, description="截止时间")
            deadline_unit: Optional[str] = Field(default=None, description="截止时间单位")

            timeout_enabled: Optional[bool] = Field(default=False, description="是否开启超时时间")
            timeout_value: Optional[int] = Field(default=None, description="超时时间")
            timeout_unit: Optional[str] = Field(default=None, description="超时时间单位")

            class ForceConfig(BaseModel):
                """总开关"""

                enabled: bool = Field(default=False)

            force_task_notifier: ForceConfig = Field(default_factory=ForceConfig)

    class View:
        class Base(BaseModel):
            class Config:
                orm_mode = True
                validate_assignment = True

            id: int = Field(description="步骤 id")
            name: str = Field(description="步骤名称")
            description: str = Field(default="", description="步骤描述")
            step_uuid: str = Field(description="步骤 uuid")
            step_type: StepType = Field(default=StepType.auto, description="步骤类型")
            form_id: int = Field(description="工单模板 id")
            next_step_ids: List[str] = Field(default_factory=list, description="后续步骤")
            prev_step_ids: List[str] = Field(default_factory=list, description="前序步骤")
            updated_at: Optional[int] = Field(default=None, description="更新时间")

            is_dirty: Optional[bool] = Field(default=True, description="是否是草稿状态")
            buyer_edit: Optional[bool] = Field(default=False, description="是否允许买家填写")
            buyer_reply: Optional[List[dict]] = Field(default=None, description="买家引导话术")

            # 当 step_type=human 时用到的信息
            assistants_v2: Optional[AssistantV2] = Field(default=None, description="人工步骤的配置")
            assignee_rule: Optional[AssigneeRule] = Field(default=None, description="分派规则")
            assignee_groups: Optional[dict] = Field(default_factory=dict, description="分派组")
            widget_collection_id: Optional[int] = Field(default=None, description="表单")
            display_rule: Optional[List[dict]] = Field(default_factory=list)
            notifier: Optional["Step.Schema.Notifier"] = Field(default=None)

            # step_type=StepType.exclusive_gateway 时用到的信息
            branch: Optional[List[dict]] = Field(default_factory=list, description="排他网关的分支信息")

            # step_type = StepType.jump 时用到的信息
            jump: Optional[dict] = Field(default_factory=dict, description="跳转步骤的配置信息")

            # step_type=StepType.auto 时用到的信息
            key_map: Optional[dict] = Field(default_factory=dict, description="自动化步骤的 key map")
            data: Union["Step.Schema.StepData", dict] = Field(default_factory=dict, description="步骤的一些配置信息")

            # step_type=StepType.event 时用到的信息
            event: str | None
            event_shortcut: str | None
            event_shortcuts: list | None
            event_filters: dict | None

            # 表单校验
            validation_config: StepValidationConfig | None

            @validator("*", pre=True)
            def not_none(cls, v, field):
                if v is None and field.allow_none is False and field.required is False:
                    return field.get_default()
                else:
                    return v

            @classmethod
            def from_orm(cls, step):
                self = super().from_orm(step)
                self.assistants_v2 = step.get_assistants_v2()
                self.event = EventType(self.event).name if self.event else self.event
                return self

        class FormEditor(Base):
            """在工单模板编辑页"""

            ui_schema: List[dict] = Field(default_factory=list, description="人工步骤的表单配置")
            symbols: list[FormSymbolEditableView] = Field(default_factory=list)
            can_retry: Optional[bool] = Field(default=False, description="是否可以重试")
            can_skip: Optional[bool] = Field(default=False, description="是否支持跳过")
            can_auto_skip_when_fail: Optional[bool] = Field(default=False, description="是否支持失败自动跳过")
            auto_retry_config: Optional["Step.Schema.AutoRetryConfig"] = Field(default=None, description="自动重试配置")
            task: Union["Rpa.View.RawStep", dict] = Field(default_factory=dict, description="步骤对应的 rpa 任务信息")

            deleted: bool = Field(default=False, description="是否已删除")

            # 遍历网关
            coupled_step_uuid: Optional[str] = Field(default=None)
            widget_ref: Optional["WidgetRef"] = Field(default=None)
            # 通知配置
            notify_configs: Optional[List[dict]] = Field(default=None)

            @classmethod
            def from_orm(cls, step: Union["Step", "StepWrapper"]) -> "Step.View.FormEditor":
                from robot_processor.form.compatible_utils import use_symbol

                if use_symbol():
                    symbols = FormSymbol.compatible_query_symbol_table_editable_view_by_scope(
                        FormSymbolScope.from_step(step)
                    ).unwrap_or([])
                else:
                    symbols = []
                inherit_view: Step.View.Base = Step.View.Base.from_orm(step)
                return cls(
                    # 继承自 BaseView
                    id=inherit_view.id,
                    name=inherit_view.name,
                    description=inherit_view.description,
                    step_uuid=inherit_view.step_uuid,
                    step_type=inherit_view.step_type,
                    form_id=inherit_view.form_id,
                    next_step_ids=inherit_view.next_step_ids,
                    prev_step_ids=inherit_view.prev_step_ids,
                    updated_at=inherit_view.updated_at,
                    is_dirty=inherit_view.is_dirty,
                    buyer_edit=inherit_view.buyer_edit,
                    buyer_reply=inherit_view.buyer_reply,
                    assistants_v2=inherit_view.assistants_v2,
                    assignee_rule=inherit_view.assignee_rule,
                    assignee_groups=inherit_view.assignee_groups,
                    widget_collection_id=inherit_view.widget_collection_id,
                    display_rule=inherit_view.display_rule,
                    notifier=inherit_view.notifier,
                    branch=inherit_view.branch,
                    key_map=inherit_view.key_map,
                    data=inherit_view.data,
                    event=inherit_view.event,
                    event_shortcut=inherit_view.event_shortcut,
                    event_shortcuts=inherit_view.event_shortcuts,
                    event_filters=inherit_view.event_filters,
                    validation_config=inherit_view.validation_config,
                    # 来自 FormEditorView
                    ui_schema=Step.Utils.raw_ui_schema(step),
                    symbols=symbols,
                    can_retry=Step.Utils.raw_can_retry(step),
                    can_skip=Step.Utils.raw_can_skip(step),
                    can_auto_skip_when_fail=Step.Utils.raw_can_auto_skip_when_fail(step),
                    auto_retry_config=Step.Utils.raw_auto_retry_config(step),
                    jump=inherit_view.jump,
                    task=Step.Utils.raw_task(step),
                    deleted=step.deleted,
                    coupled_step_uuid=step.coupled_step_uuid,
                    widget_ref=step.widget_ref,
                    notify_configs=step.get_notify_configs(),
                )

        class FormVersion(FormEditor):
            """
            在工单模板中展示的字段和 FormEditor 中相同
            但是会修改数据内容从 raw_step 中获取
            """

            @classmethod
            def from_orm(cls, step):
                if not step.raw_step:
                    step.update_raw_step()
                return super().from_orm(step)

        class RawStep(Base):
            """在 raw step 中保存的信息"""

            can_retry: Optional[bool] = Field(default=False, description="是否可以重试")
            can_reject: Optional[bool] = Field(default=False, description="是否支持驳回")
            can_skip: Optional[bool] = Field(default=False, description="是否支持跳过")
            can_auto_skip_when_fail: Optional[bool] = Field(default=False, description="是否支持失败自动跳过")
            auto_retry_config: Optional["Step.Schema.AutoRetryConfig"] = Field(default=None, description="自动重试配置")

            # 人工步骤的一些配置
            ui_schema: Optional[List[dict]] = Field(default_factory=list, description="人工步骤的表单配置")

            # 自动任务的一些配置
            task: Union[Optional["Rpa.View.RawStep"], dict] = Field(
                default_factory=dict, description="步骤对应的 rpa 任务信息"
            )
            override_config: List["StepFieldOverride.View.RawStep"] = Field(
                default_factory=list, description="覆盖字段配置"
            )
            exception_notify_config: Optional[dict] = Field(default=None)

            @classmethod
            def from_orm(cls, step):
                if step.is_wrapper:
                    step = cast(StepWrapper, step)._step  # pylint: disable=protected-access
                inherit_view: Step.View.Base = Step.View.Base.from_orm(step)

                if auto_retry_config_inst := Step.Queries.auto_retry_config(step):
                    auto_retry_config = Step.Schema.AutoRetryConfig.from_orm(auto_retry_config_inst)
                else:
                    auto_retry_config = None

                ui_schema = step.get_ui_schema()
                task = step.get_task()
                override_config = list(
                    map(
                        StepFieldOverride.View.RawStep.from_orm,
                        (StepFieldOverride.query.filter(StepFieldOverride.step_uuid == step.step_uuid).all()),
                    )
                )

                return cls(
                    # 继承自 BaseView
                    id=inherit_view.id,
                    name=inherit_view.name,
                    description=inherit_view.description,
                    step_uuid=inherit_view.step_uuid,
                    step_type=inherit_view.step_type,
                    form_id=inherit_view.form_id,
                    next_step_ids=inherit_view.next_step_ids,
                    prev_step_ids=inherit_view.prev_step_ids,
                    updated_at=inherit_view.updated_at,
                    is_dirty=inherit_view.is_dirty,
                    buyer_edit=inherit_view.buyer_edit,
                    buyer_reply=inherit_view.buyer_reply,
                    assistants_v2=inherit_view.assistants_v2,
                    assignee_rule=inherit_view.assignee_rule,
                    assignee_groups=inherit_view.assignee_groups,
                    widget_collection_id=inherit_view.widget_collection_id,
                    display_rule=inherit_view.display_rule,
                    notifier=inherit_view.notifier,
                    branch=inherit_view.branch,
                    key_map=inherit_view.key_map,
                    data=inherit_view.data,
                    event=inherit_view.event,
                    event_shortcut=inherit_view.event_shortcut,
                    event_shortcuts=inherit_view.event_shortcuts,
                    event_filters=inherit_view.event_filters,
                    validation_config=inherit_view.validation_config,
                    # 来自 RawStepView
                    # 注意，以下 property 都是从数据库里进行查询
                    can_retry=step.can_retry,
                    can_skip=step.can_skip,
                    can_auto_skip_when_fail=step.can_auto_skip_when_fail,
                    # 注意结束
                    auto_retry_config=auto_retry_config,
                    jump=inherit_view.jump,
                    ui_schema=ui_schema,
                    task=task,
                    override_config=override_config,
                    notify_configs=step.get_notify_configs(),
                )

        class Miniapp(FormEditor):
            """千牛侧边栏要求 option value 合并到外面"""

            @validator("ui_schema")
            def flatten_ui_schema(cls, ui_schema):
                """插件栏要求option_value 合并到外面, 保持一层结构"""
                for widget_info in ui_schema:
                    option_value = widget_info.get("option_value", {})
                    widget_info.update(option_value)
                return ui_schema

        class BriefInCreate(BaseModel):
            """在创建工单的页面，需要展示所有的步骤名"""

            class Config:
                orm_mode = True

            id: int = Field(description="步骤 id")
            name: str = Field(description="步骤名称")
            uuid: str = Field(alias="step_uuid", description="步骤 uuid")
            step_type: StepType = Field(default=StepType.auto, description="步骤类型")
            next_step_ids: List[str] = Field(default_factory=list, description="后续步骤")
            prev_step_ids: List[str] = Field(default_factory=list, description="前序步骤")
            assignee_rule: Optional[AssigneeRule] = Field(default=None, description="分派规则")

            @validator("*", pre=True)
            def not_none(cls, v, field):
                if v is None and field.allow_none is False and field.required is False:
                    return field.get_default()
                else:
                    return v


def _resolve_shop_model():
    from robot_processor.shop.models import Shop

    return Shop


class Form(DbBaseModel, BasicMixin):
    """用户创建的表单"""

    __ignore_columns__ = ["uuid", "created_at", "shop", "steps"]

    # 店铺内名称唯一
    name: Mapped[str | None] = mapped_column(sa.String(64), comment="工单名称")
    description: Mapped[str | None] = mapped_column(sa.Text, comment="工单描述")
    update_user: Mapped[str | None] = mapped_column(sa.String(128), comment="最新更新人")
    # 通过是否为空判断，是否为官方
    uuid: Mapped[str] = mapped_column(sa.String(32), default="", comment="官方模版ID")
    # @deprecated 后续使用 form_mold区分，方便扩展
    enable_service_count: Mapped[bool] = mapped_column(sa.Boolean, default=False, comment="买家自助填写开关")

    steps: DynamicMapped[Step] = relationship(Step, lazy="dynamic", back_populates="form")
    # 仅当uuid存在的时候, 官方未投入使用=false
    is_ready: Mapped[bool] = mapped_column(sa.Boolean, default=False)
    can_publish: Mapped[bool] = mapped_column(sa.Boolean, default=True, nullable=False)

    dynamic_schema: Mapped[bool] = mapped_column(sa.Boolean, default=True)
    category: Mapped[str | None] = mapped_column(sa.String(32))
    form_mold: Mapped[FormMold] = mapped_column(
        sa.Enum(FormMold),
        default=FormMold.CUSTOM,
        comment="工单类型,不同的类型工单模板的配置有不同限制",
    )
    co_edit: Mapped[bool] = mapped_column(sa.Boolean, default=True, comment="是否允许协同编辑")
    tags: Mapped[list] = mapped_column(sa.JSON, default=list)
    message_digest: Mapped[str | None] = mapped_column(sa.String(32), comment="判断两个工单是否相同")  # 已废弃
    need_archive: Mapped[bool] = mapped_column(sa.Boolean, default=False, comment="工单是否完结即归档")
    versions: DynamicMapped["FormVersion"] = relationship(
        lambda: FormVersion,
        order_by="desc(FormVersion.id)",
        lazy="dynamic",
        back_populates="form",
    )
    form_shops: DynamicMapped["FormShop"] = relationship(lambda: FormShop, lazy="dynamic", back_populates="form")
    # 这里的 shop 没有过滤掉已删除的
    shops: AssociationProxy[list["Shop"]] = association_proxy("form_shops", "shop")
    # 这里的 shop 是当前可用的
    subscribed_shops: Mapped[list["Shop"]] = relationship(
        _resolve_shop_model,
        secondary="form_shop",
        viewonly=True,
        # Form.form_shops AND Form.Filters.subscribed
        primaryjoin="and_(foreign(Form.id)==form_shop.c.form_id," "form_shop.c.status.in_(['ENABLED','DISABLED']))",
        # FormShop.shop AND Shop.Filters.accessible
        secondaryjoin="and_(foreign(form_shop.c.channel_id)==Shop.channel_id," "Shop.deleted.is_not(True))",
    )
    expect_duration: Mapped[int | None] = mapped_column(sa.Integer, default=0, comment="预计时长，搭配bo.deadline使用")
    # 当 form_mold=event 时，该字段表示事件名
    event: Mapped[EventType | None] = mapped_column(sa.Enum(EventType, native_enum=False, length=128))

    def wraps(self, shop: "Shop") -> "FormWrapper":
        """提供当前 form 在指定 shop 下的 view.

        :param shop: 指定的 shop, 且该 shop 应该通过 FormShop 表和当前form 进行了绑定
        :returns: FormWrapper
        """
        assert isinstance(shop, _resolve_shop_model()), "wraps 接受 Shop 的实例，请检查代码错误"
        if not self.is_wrapper:
            return FormWrapper(cast(Form, self), shop)
        else:
            return FormWrapper(cast(FormWrapper, self)._form, shop)

    @property
    def is_wrapper(self) -> bool:
        return not isinstance(self, Form)

    @property
    def form_subscribe(self):
        from robot_processor.form.form_subscribe import FormSubscribe

        return FormSubscribe(self)

    def subscribe(self, shop: "Shop", enabled: bool):
        return self.form_subscribe.subscribe(shop, enabled)

    def unsubscribe(self, shop: "Shop", reserved: bool):
        self.form_subscribe.unsubscribe(shop, reserved)

    def get_org_id(self):
        return Form.Queries.org_id_by_form_id(self.id)

    def get_deleted(self):
        return len(self.subscribed_shops) == 0

    def get_startup_step(self) -> Step | None:
        # 返回系统步骤的下一步
        system_begin: Step = self.job_steps.filter(Step.step_type == StepType.begin).order_by(Step.id.desc()).first()
        if not system_begin:
            logger.warning(f"该模板没有系统起始步骤 {self.id}")
            return (
                self.job_steps.filter(
                    sa_op.and_(
                        # 人工步骤
                        Step.step_type == StepType.human,
                        # 没有前序步骤
                        json_length(Step.prev_step_ids) == 0,
                    )
                )
                .order_by(Step.id.desc())
                .limit(1)
                .first()
            )
        return (
            Step.query.filter(Step.step_uuid.in_(system_begin.next_step_ids)).order_by(Step.id.desc()).limit(1).first()
        )

    def get_begin_step(self) -> Step | None:
        """
        获取“起始步骤”。
        """
        return (
            Step.query.filter(
                Step.form_id == self.id,
                Step.step_type == StepType.begin,
                Step.deleted.is_(False),
            )
            .order_by(Step.id.desc())
            .first()
        )

    @classmethod
    def query_steps_by_version(cls, form_id: int, form_version: str) -> Result[list[Step], Exception]:
        """获取指定版本的工单步骤列表"""
        if form_version == "STASH":
            backend_step_ids = db.session.query(text("id")).select_from(
                Step.query.with_entities(func.max(Step.id).label("id"))
                .filter(Step.form_id == form_id)
                .filter(Step.deleted.isnot(True))
                .group_by(Step.step_uuid)
                .subquery()
            )
            steps = Step.query.filter(Step.id.in_(backend_step_ids)).all()

        else:
            form_version_info = (
                FormVersion.query.filter(FormVersion.form_id == form_id)
                .filter(FormVersion.version_no == form_version)
                .first()
            )
            if not form_version_info:
                return Err(ValueError(f"工单@{form_id} 版本 {form_version} 不存在"))
            steps = Step.query.filter(Step.id.in_(form_version_info.step_id)).all()

        return Ok(steps)

    def get_owners(self) -> AssistantV2:
        """
        获取该工单模板配置的工单管理员信息。
        """
        begin_step = self.get_begin_step()
        if begin_step is None:
            logger.error(f"工单模板 {self.id} 缺少起始步骤")
            assistants_v2 = {}
        else:
            assistants_v2 = copy.deepcopy(begin_step.assistants_v2 or {})
        return AssistantV2.parse(
            assistants_v2.get("owners")
            or {
                "details": [],
                "online_only": False,
                "select_type": 2,
                "leyan_accounts": [],
                "assign_strategy": 1,
                "assignee_groups": [],
                "channel_accounts": [],
            }
        )

    def brief(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "updated_at": self.updated_at,
            "form_category": self.category,
            "tags": self.tags,
            "steps": [step.brief() for step in self.job_steps],
        }

    def get_form_first_step_widgets_info(self):
        start_up_step = self.get_startup_step()
        widgets_info = start_up_step.widget_collection.widgets_info(True)  # type: ignore[union-attr]
        return {widget["label"]: widget["key"] for widget in widgets_info}

    def get_form_composer_in_draft(self):
        """获取当前草稿箱状态的 FormComposer"""
        from robot_types.helper.form_composer import schemas
        from robot_types.helper.symbol import SymbolDeserializer

        composer = FormComposer(
            meta=schemas.FormMeta(id=self.id, name=self.name, version_id=0, version_no="STASH"),
            steps=[],
        )
        steps = self.backend_steps.all()
        symbols = FormSymbol.query.filter(
            FormSymbol.form_id == self.id,
            FormSymbol.step_id.in_([step.id for step in steps]),
        ).all()
        step_symbols = {
            step_id: SymbolDeserializer.deserialize_flatten(list(grouped_symbols))
            for step_id, grouped_symbols in groupby(symbols, key=lambda x: x.step_id)
        }
        for step in steps:
            composer_step = schemas.Step(
                id=step.id,
                name=step.name,
                step_uuid=step.step_uuid,
                step_type=schemas.StepType(step.step_type),
                symbols=step_symbols.get(step.id, []),
                key_map=step.key_map,
                branch=step.branch,
                jump=step.jump,
                prev_step_ids=step.prev_step_ids,
                next_step_ids=step.next_step_ids,
            )
            composer.steps.append(composer_step)
        return composer

    class Queries:
        __slots__ = ()

        @staticmethod
        def is_subscribed(form_id: int, shop: "Shop") -> bool:
            from sqlalchemy import or_

            from robot_processor.shop.models import Shop

            return db.session.query(
                FormShop.query.filter(
                    or_(
                        FormShop.form_id == form_id,
                        FormShop.sync_merged_form_id == form_id,
                    )
                )
                .join(Shop.Options.from_form_shop)
                .filter(Shop.org_id == shop.org_id)
                .filter(Form.Filters.subscribed)
                .exists()
            ).scalar()

        @staticmethod
        def by_org_id(org_id: str):
            return Form.query.join(Form.form_shops).filter(FormShop.org_id == org_id)

        @staticmethod
        def by_sids(sids: List[str]) -> Query:
            ShopModel = _resolve_shop_model()
            return (
                Form.query.join(Form.form_shops).join(ShopModel.Options.from_form_shop).filter(ShopModel.sid.in_(sids))
            )

        @staticmethod
        def form_shops_by_shop(shop: "Shop") -> Query:
            return FormShop.query.options(Form.Options.joined_load_form).filter(FormShop.shop == shop)

        @staticmethod
        def form_shops_by_form(form: "Form") -> Query:
            return FormShop.query.options(Form.Options.joined_load_shop).filter(FormShop.form == form)

        @staticmethod
        def form_shops_by_sids(sids: List[str]) -> Query:
            ShopModel = _resolve_shop_model()
            return FormShop.query.join(FormShop.shop).filter(ShopModel.sid.in_(sids))

        @staticmethod
        def form_shops_by_org_id(org_id: str) -> Query:
            return FormShop.query.filter(FormShop.org_id == org_id)

        @staticmethod
        def form_shop(form: "Form", shop: "Shop") -> Query["FormShop"]:
            return FormShop.query.filter(FormShop.form == form).filter(FormShop.shop == shop)

        @staticmethod
        def form_shop_by_sync_merged(form: "Form", shop: "Shop") -> Query["FormShop"]:
            """在 business order 迁移前使用"""
            return FormShop.query.filter(FormShop.sync_merged_form_id == form.id).filter(FormShop.shop == shop)

        @staticmethod
        @functools.lru_cache(maxsize=128)
        def org_id_by_form_id(form_id):
            stmt = (
                select(FormShop.org_id)
                .select_from(FormShop)
                .where(FormShop.form_id == form_id)
                .union(select(FormShop.org_id).select_from(FormShop).where(FormShop.sync_merged_form_id == form_id))
            )
            return db.session.execute(stmt).scalars().first()

        @staticmethod
        def subscribed_channel_ids_by_form_ids(
            form_ids: List[int],
        ) -> Dict[int, List[int]]:
            query = (
                db.session.query(
                    FormShop.form_id,
                    func.group_concat(FormShop.channel_id).label("channel_ids"),
                )
                .filter(FormShop.form_id.in_(form_ids))
                .filter(Form.Filters.subscribed)
                .group_by(FormShop.form_id)
            )
            form_channel_ids_map = {
                form_id: list(map(int, channel_ids_str.split(","))) for form_id, channel_ids_str in query.all()
            }
            return form_channel_ids_map

        @staticmethod
        def latest_version_by_form_id(form_id: int) -> Optional["FormVersion"]:
            return (
                FormVersion.query.filter(FormVersion.form_id == form_id)
                .filter(~FormVersion.deleted)
                .order_by(desc(FormVersion.id))
                .first()
            )

        @staticmethod
        def form_shops_by_ids(form_ids: list) -> Query:
            form_query = FormShop.query.filter(FormShop.form_id.in_(form_ids))
            sync_query = FormShop.query.filter(FormShop.sync_merged_form_id.in_(form_ids))
            return form_query.union(sync_query)

    class Options:
        __slots__ = ()

        @classproperty  # noqa
        @staticmethod
        def from_form_shop():
            return FormShop.form

        @classproperty  # noqa
        @staticmethod
        def joined_load_form():
            return Load(FormShop).joinedload(FormShop.form)

        @classproperty  # noqa
        @staticmethod
        def joined_load_shop():
            return Load(FormShop).joinedload(FormShop.shop)

    class Filters:
        __slots__ = ()

        @classproperty  # noqa
        @staticmethod
        def subscribed():
            """店铺已订阅的工单模板"""
            return FormShop.status.in_([FormShop.Status.ENABLED, FormShop.Status.DISABLED])

        @classproperty  # noqa
        @staticmethod
        def can_view_in_report():
            """
            是否可以展示在任务中心/报表的工单模板筛选列表中
            满足以下条件:
                工单模板未删除 ENABLED | DISABLED
                工单模板删除，但是在删除时选择了 “保留工单实例” | RESERVED
            """
            return FormShop.status.in_(
                [
                    FormShop.Status.ENABLED,
                    FormShop.Status.DISABLED,
                    FormShop.Status.RESERVED,
                ]
            )

        @classproperty  # noqa
        @staticmethod
        def can_create():
            """
            是否可以创建工单
            满足以下条件:
                工单模板处于启用状态 ENABLED
            """
            return FormShop.status == FormShop.Status.ENABLED

        @staticmethod
        def by_category(category: Optional[str]):
            """根据工单模板类型筛选工单模板"""
            from sqlalchemy import true

            if category is None:  # 普通工单模板
                return Form.category.is_(None)
            elif category == "ALIPAY":  # 批量打款工单模板
                return Form.category == "ALIPAY"
            else:  # 获取全部工单模板
                return true()

        @staticmethod
        def by_enabled(enabled: bool):
            """根据工单模板启用筛选工单模板"""
            status = FormShop.Status.enabled_by_bool(enabled)
            return FormShop.status == status

    class Schema:
        class RecordLogAction(str, LabeledEnum):
            create = "create", "创建工单模板"
            update = "update", "更新/发布工单模板"
            add = "add", "创建工单步骤"
            edit = "edit", "编辑/修改工单步骤"
            delete = "delete", "删除工单模板/工单步骤"
            sync = "sync", "店铺订阅工单模板"

        class FormActionLog(BaseModel):
            """在工单模板操作日志中保存的信息"""

            model: str = Field(default="forms", description="固定为 forms")
            operator: str = Field(description="操作符")
            object_id: str = Field(description="工单模板 id")
            label: str = Field(description="工单模板名称")
            org_id: str = Field(description="租户 ID")
            sid: str = Field(description="店铺 SID")
            platform: str = Field(description="店铺平台")
            user: str = Field(description="操作人")
            operate_ts: int = Field(description="操作时间戳")
            raw_json: str = Field(description="操作内容")

            @classmethod
            @no_auto_flush()
            def build(
                cls,
                form: "FormWrapper",
                action: "Form.Schema.RecordLogAction",
                user: str,
                operate_ts: Optional[int] = None,
            ):
                assert form.is_wrapper, "form action log 需要 form wrapper"

                raw_info = Form.View.FormActionLog.from_orm(form).dict()
                if action == Form.Schema.RecordLogAction.delete:
                    if form.status == FormShop.Status.DELETED:
                        raw_info["comment"] = "删除工单模板及相关任务"
                    elif form.status == FormShop.Status.RESERVED:
                        raw_info["comment"] = "仅删除工单模板"

                return cls(
                    operator=action.value,
                    object_id=str(form.id),
                    label=form.name,
                    org_id=str(form.org_id),
                    sid=form.sid,
                    platform=form.shop.platform,
                    user=user,
                    operate_ts=operate_ts or arrow.now().int_timestamp,
                    raw_json=json.dumps(raw_info, ensure_ascii=False),
                )

    class View:
        """将 to_dict 方法进行拆分，不同的场景需要不同的字段"""

        @classmethod
        def get_sync_shop(cls, shop: "Shop"):
            return cls.FormEditorList.SyncShop(
                org_id=shop.org_id,
                channel_id=shop.channel_id,
                sid=shop.sid,
                title=shop.title,
                nick=shop.nick,
                platform=shop.platform,
            )

        class Base(BaseModel):  # type: ignore[no-redef] #
            class Config:
                orm_mode = True

            id: int
            name: str = Field(description="工单名称")
            description: str = Field(default="", description="工单描述")
            category: Optional[str] = Field(default=None, description="工单模板类型")
            form_category: Optional[str] = Field(default=None, alias="category", description="forward compatibility")
            tags: List[str] = Field(default_factory=list, description="工单标签")
            form_mold: FormMold = Field(default=FormMold.CUSTOM, description="工单类型")
            can_publish: bool = Field(default=True, description="是否允许发布")
            enable_service_count: bool = Field(default=False, description="买家自助填写开关")
            co_edit: bool = Field(default=True, description="是否允许协同编辑")
            need_archive: bool = Field(default=False, description="是否完结即归档")
            event: EventType | None = Field(default=None, description="事件类型")

            update_user: Optional[str] = Field(default=None, description="最近更新人")
            updated_at: Optional[int] = Field(default=None, description="最近更新时间")

            # forward compatibility
            is_ready: bool = Field(default=True)
            dynamic_schema: bool = Field(default=True)
            form_sync_id: Optional[int] = Field(default=None)

            # 历史数据兼容 将 None 转换成 default
            @validator("*", pre=True)
            def not_none(cls, v, field):
                if v is None and field.allow_none is False and field.required is False:
                    return field.get_default()
                else:
                    return v

        class FormEditorList(Base):  # type: ignore[no-redef] #
            """在店铺的工单模板列表页"""

            class SyncShop(BaseModel):
                """订阅了该工单模板的店铺信息"""

                org_id: int
                channel_id: int
                sid: str

                title: Optional[str]
                nick: Optional[str]
                platform: Optional[str]

            sid: str = Field(description="店铺 id")
            enabled: bool = Field(default=False, description="工单开关")
            sync_shop: List[SyncShop] = Field(default_factory=list)

            @classmethod
            def from_form_shops(cls, form_shop: "FormShop", subscribed_shops: List["Shop"]):
                inherit_view = Form.View.Base.from_orm(form_shop.form)
                sync_shop = list(map(Form.View.get_sync_shop, subscribed_shops))

                return cls(
                    # 继承自 BaseView
                    id=inherit_view.id,
                    name=inherit_view.name,
                    description=inherit_view.description,
                    category=inherit_view.category,
                    tags=inherit_view.tags,
                    form_mold=inherit_view.form_mold,
                    can_publish=inherit_view.can_publish,
                    enable_service_count=inherit_view.enable_service_count,
                    co_edit=inherit_view.co_edit,
                    need_archive=inherit_view.need_archive,
                    update_user=inherit_view.update_user,
                    updated_at=inherit_view.updated_at,
                    # 来自 FormEditorListView
                    sid=form_shop.sid,
                    enabled=bool(form_shop.status),
                    sync_shop=sync_shop,
                )

        class FormEditor(FormEditorList):  # type: ignore[no-redef] #
            """在工单模板编辑页，需要工单模板的完整信息"""

            has_running_business_orders: bool = Field(default=False, description="是否有正在运行的工单实例")
            is_dirty: bool = Field(description="是否是草稿状态")
            version_no: str = Field(default="", description="工单模板版本号")

            steps: List[Step.View.FormEditor] = Field(default_factory=list, description="工单模板步骤")
            expect_duration: Optional[int] = Field(description="工单预期执行时间")
            owner_list: AssistantV2 | None

            @classmethod
            def from_form_shop(cls, form: "Form", shop: "Shop"):
                from robot_processor.business_order.models import BusinessOrder

                backend_steps: List[Step] = form.backend_steps.all()
                steps = [Step.View.FormEditor.from_orm(step) for step in backend_steps]
                is_dirty = any(step.is_dirty for step in backend_steps)
                has_running_business_orders: bool = db.session.query(
                    BusinessOrder.query.filter(BusinessOrder.sid == shop.sid)
                    .filter(BusinessOrder.form_id == form.id)
                    .filter(BusinessOrder.status == BusinessOrderStatus.RUNNING)
                    .exists()
                ).scalar()
                form_shops: List[FormShop] = Form.Queries.form_shops_by_form(form).all()
                shops = [item.shop for item in form_shops if item.status.is_subscribed]
                form_shop: FormShop = first(filter(lambda each: each.shop == shop, form_shops))

                def get_version_no():
                    if is_dirty:
                        return "STASH"
                    elif version := form.versions.first():
                        return version.version_no
                    else:
                        return ""

                inherit_view = Form.View.FormEditorList.from_form_shops(form_shop, shops)

                return cls(
                    # 继承自 FormEditorListView
                    id=inherit_view.id,
                    name=inherit_view.name,
                    description=inherit_view.description,
                    category=inherit_view.category,
                    tags=inherit_view.tags,
                    form_mold=inherit_view.form_mold,
                    can_publish=inherit_view.can_publish,
                    enable_service_count=inherit_view.enable_service_count,
                    co_edit=inherit_view.co_edit,
                    need_archive=inherit_view.need_archive,
                    update_user=inherit_view.update_user,
                    updated_at=inherit_view.updated_at,
                    sid=inherit_view.sid,
                    enabled=inherit_view.enabled,
                    sync_shop=inherit_view.sync_shop,
                    # 来自 FormEditorView
                    expect_duration=form.expect_duration,
                    has_running_business_orders=has_running_business_orders,
                    is_dirty=is_dirty,
                    version_no=get_version_no(),
                    steps=steps,
                    owner_list=form.get_owners(),
                )

        class FormVersionMeta(Base):  # type: ignore[no-redef] #
            """在工单历史版本中保存的信息"""

        class FormActionLog(Base):  # type: ignore[no-redef] #
            """在工单模板操作日志中保存的信息"""

        class FormSchema(BaseModel):
            """在工单创建页，需要工单模板及其步骤的完整信息"""

            id: int
            current_version_id: int | None
            label: str = Field(description="工单模板名称")
            step_history: List[Step.View.BriefInCreate]
            current_step: Step.View.Miniapp
            next_step: Optional[Step.View.BriefInCreate] = Field(
                description="当前步骤的下一步为人工步骤时，" "不为空，需要下一步骤的 assignee_rule 信息"
            )
            # 侧边栏才用到这些信息
            shop_title: Optional[str] = Field(description="店铺名称")
            shop_pic_url: Optional[str] = Field(description="店铺头像")

            @classmethod
            def from_(
                cls,
                *,
                form: "Form",
                current_step: Step,
                steps: List[Step],
                # 侧边栏才用到以下信息
                shop_title: Optional[str] = None,
                shop_pic_url: Optional[str] = None,
            ):
                step_uuid_map = {step.step_uuid: step for step in steps}
                if current_step.next_step_ids:
                    for next_step_uuid in current_step.next_step_ids:
                        next_step = step_uuid_map.get(next_step_uuid)
                        if next_step and next_step.step_type == StepType.human:
                            break
                    else:
                        next_step = None
                else:
                    next_step = None

                current_step_view = Step.View.Miniapp.from_orm(current_step)
                if next_step:
                    next_step_view = Step.View.BriefInCreate.from_orm(next_step)
                else:
                    next_step_view = None
                step_history_view = []
                for step in steps:
                    step_view = Step.View.BriefInCreate.from_orm(step)
                    step_history_view.append(step_view)
                if latest_version := Form.Queries.latest_version_by_form_id(form.id):
                    current_version_id = latest_version.id
                else:
                    current_version_id = None

                return cls(
                    id=form.id,
                    current_version_id=current_version_id,
                    label=form.name,
                    step_history=step_history_view,
                    current_step=current_step_view,
                    next_step=next_step_view,
                    shop_title=shop_title,
                    shop_pic_url=shop_pic_url,
                )

    class Utils:
        @staticmethod
        def reports_widget_type(widget_id, widget_type_map=None):
            """在报表中用于合并的组件类型

            如下拉选择组件分为了 radio 和 select ，但是在报表中应该合并在一列展示
            """
            widget_type_map = widget_type_map or Widget.Queries.widget_type_map()
            # 合并规则的数据结构为 { "原始组件类型": "用于报表合并的组件类型" }
            # 如 { "radio-tile": "select", "radio-dropdown": "select" }
            reports_widget_type_merge_rule = current_app.config.get("REPORTS_WIDGET_TYPE_MERGE_RULE", {})

            # 根据 widget_id 查找组件类型
            widget_type = widget_type_map.get(widget_id, "string")
            # 尝试查找合并规则
            widget_type = reports_widget_type_merge_rule.get(widget_type, widget_type)

            return widget_type

        @staticmethod
        def report_widgets(form_ids: List[int], org_id: str | None = None):
            """用于报表-表单的组件信息

            因为要在报表中使用，所以要包含组件迁移的信息
            """
            steps = (
                Step.query.options(Load(Step).joinedload(Step.widget_collection))
                .options(Load(Step).load_only(Step.id, Step.name, Step.widget_collection_id))
                .filter(Step.form_id.in_(form_ids))
                .filter(Step.widget_collection_id.isnot(None))
                .filter(Step.widget_collection_id != "0")
                .filter(Step.is_dirty.is_(False))
                .all()
            )
            # 按 uuid 分组，组内按照 id 降序排列
            grouped_steps: List[List[Step]] = [
                sorted(group, key=lambda step: step.id, reverse=True)
                for step_uuid, group in groupby(
                    sorted(steps, key=lambda step: step.step_uuid),
                    key=lambda step: step.step_uuid,
                )
            ]
            # 再按 step.name 分组，uuid 分组的结果能保证组内的是相同工单模板
            # 因为存在跨工单模板的报表的场景，此时的合并规则是根据工单步骤名称进行合并
            cross_form_grouped_steps = defaultdict(list)
            # 明细报表渲染的表头参考工单模板最新配置，部分客户想要看到历史数据，由开发通过 widget_collection 来维护
            reserved_widget_info_keys = defaultdict(set)
            for group in grouped_steps:
                # 每个工单模板的步骤取最新一条记录
                step_represent = first(group)
                step_name = StepName(step_represent.name)  # type: ignore[arg-type]
                cross_form_grouped_steps[step_name].append(step_represent)
                if org_id in app_config.page_titles_reserved_keys_whitelist:
                    for each in group:
                        if each.widget_collection and each.widget_collection.changelog:
                            reserved_widget_info_keys[step_name].update(
                                each.widget_collection.changelog.get("reserved_keys", [])
                            )

            widget_type_map = Widget.Queries.widget_type_map()
            widget_infos: Dict[ReportWidgetInfoIdent, List[WidgetInfo]] = dict()

            for step_name in cross_form_grouped_steps:
                step_widget_infos: Dict[
                    Tuple[WidgetInfoLabel, WidgetType, WidgetDataType],
                    Dict[str, WidgetInfo],
                ] = defaultdict(dict)
                group = sorted(
                    cross_form_grouped_steps[step_name],
                    key=lambda step: step.id,
                    reverse=True,
                )
                transfer_rule = WidgetCollection.Utils.merge_changelog_transfer_rule(
                    widget_collections=[step.widget_collection for step in group[::-1]]  # type: ignore[misc]
                )
                reversed_transfer_rule = WidgetCollection.Utils.reverse_changelog_transfer_rule(transfer_rule)
                widget_collection_ids = [step.widget_collection_id for step in group]
                _widget_infos = WidgetInfo.Queries.distinct_by_widget_collection_ids(
                    widget_collection_ids=widget_collection_ids,  # type: ignore[arg-type]
                )
                if reserved_keys := reserved_widget_info_keys[step_name]:
                    _widget_infos.extend(WidgetInfo.Queries.by_keys_published(list(reserved_keys)))
                widget_info_key = {widget_info.key: widget_info for widget_info in _widget_infos}
                for widget_info in _widget_infos:
                    # 在跨工单模板的相同名称步骤内，按照组件名称+组件类型的规则进行合并
                    # 发现很多数据中没有widget_type
                    widget_type_final = widget_info.option_value.get("widget_type")
                    if not widget_type_final:
                        widget_type_final = (Widget.get(widget_info.widget_id) or {}).get("widget_type")
                    group_key = (
                        cast(WidgetInfoLabel, widget_info.label),
                        Form.Utils.reports_widget_type(widget_info.widget_id, widget_type_map),
                        cast(WidgetDataType, widget_type_final),
                    )
                    step_widget_infos[group_key][widget_info.key] = widget_info

                    # 有其他组件迁移到当前组件上
                    if widget_info.key in reversed_transfer_rule:
                        keys = reversed_transfer_rule[widget_info.key]
                        for key in keys:
                            # 把迁移之前的组件加入到 widgets 中
                            if item := widget_info_key.get(key):
                                step_widget_infos[group_key][key] = item
                            # 迁移之前的组件找不到了，根据当前组件构造一个
                            else:
                                widget_info_cloned = widget_info.clone()
                                widget_info_cloned.key = key
                                step_widget_infos[group_key][key] = widget_info_cloned
                for (
                    label,
                    widget_type,
                    widget_data_type,
                ), widget_info_keymap in step_widget_infos.items():
                    widget_infos[ReportWidgetInfoIdent(step_name, label, widget_type, widget_data_type)] = list(
                        widget_info_keymap.values()
                    )

            # 白名单内的租户，需要跨步骤合并组件
            if org_id in app_config.page_titles_merge_step_whitelist:
                merge_step_widget_infos: dict[ReportWidgetInfoIdent, list[WidgetInfo]] = dict()
                for widget_ident in widget_infos:
                    merge_step_widget_ident = ReportWidgetInfoIdent(None, *widget_ident[1:])
                    if merge_step_widget_ident not in merge_step_widget_infos:
                        merge_step_widget_infos[merge_step_widget_ident] = []
                    merge_step_widget_infos[merge_step_widget_ident].extend(widget_infos[widget_ident])
                return merge_step_widget_infos
            else:
                return widget_infos

        @staticmethod
        def form_shop(form: "Form", shop: "Shop") -> "FormShop":
            """获取 form_shop, 如果查不到关联关系，就返回一个 blank form_shop"""
            return (
                Form.Queries.form_shop(form, shop).first()
                or Form.Queries.form_shop_by_sync_merged(form, shop).first()
                or FormShop.get_blank(form, shop)
            )

        @staticmethod
        def form_wrapper(form: "Form", shop: "Shop"):
            """一定能拿到数据的 wrapper 方法"""
            form_shop = Form.Utils.form_shop(form, shop)
            return form_shop.form_wrapper

        @staticmethod
        def form_ids_by_form_shops(form_shops: List["FormShop"]):
            _form_ids = set()
            for form_shop in form_shops:
                _form_ids.add(form_shop.form_id)
                if form_shop.sync_merged_form_id:
                    _form_ids.add(form_shop.sync_merged_form_id)
            return list(_form_ids)

        @staticmethod
        def check_form_name_unique(
            form_name: str,  # 要检查的工单模板名称
            shop: "Shop",  # 要检查的范围（店铺）
            form_id: Optional[int] = None,  # 要排除的工单模板 id
        ):
            """检查工单模板名称是否重复

            以店铺维度做重复校验的范围
            在通过订阅工单模板的方式，可能会存在店铺工单模板重名
            允许短时间的重名并在工单发布时强制重名的工单模板更名
            """
            if_form_name_exists = (
                FormShop.query.join(FormShop.form)
                .where(FormShop.shop == shop)
                .where(Form.name == form_name)
                .where(Form.Filters.subscribed)
                .where(sa.true if form_id is None else Form.id != form_id)
                .exists()
            )
            if db.session.query(if_form_name_exists).scalar():
                raise ValidateError("工单模板名称已存在")

        @staticmethod
        def truncate_steps_by_step_uuid(steps: list[Step], step_uuid: str):
            """找到步骤及步骤的前序步骤"""
            truncated_steps: list[Step] = []

            def find_step_by_step_uuid(target_step_uuid: str):
                return next(
                    filter(lambda each_step: each_step.step_uuid == target_step_uuid, steps),
                    None,
                )

            def add_prev_steps_in_truncated_steps(step: Step):
                for prev_step_uuid in step.prev_step_ids:
                    if prev_step := find_step_by_step_uuid(prev_step_uuid):
                        truncated_steps.append(prev_step)
                        add_prev_steps_in_truncated_steps(prev_step)

            current_step = find_step_by_step_uuid(step_uuid)
            if current_step is None:
                return truncated_steps
            else:
                truncated_steps.append(current_step)
                add_prev_steps_in_truncated_steps(current_step)

            return truncated_steps

    @property
    def view__base(self):
        """shortcut for View.Base.from_orm(self)"""
        return Form.View.Base.from_orm(self)

    @property
    def backend_steps(self):
        """后台编辑用的"""
        return Step.Queries.steps(form_id=self.id, deleted=False)

    @property
    def job_steps(self):
        """执行任务用的"""
        if latest_version := self.versions.first():
            return self.steps.filter(Step.id.in_(cast(FormVersion, latest_version).step_id))
        else:
            return Step.Queries.steps(form_id=self.id, is_dirty=False)

    @in_transaction()
    def snapshot(self):
        """制作工单模板的快照信息"""
        log_vars.FormId.set(self.id)

        snapshot_strategy = FormVersion.which_strategy(self)
        # 快照策略为仅更新 meta
        if snapshot_strategy.is_update:
            version: FormVersion = self.versions.first()  # type: ignore[assignment]
            db.session.refresh(version, with_for_update=True)
            version.meta = Form.View.FormVersionMeta.from_orm(self).dict()
            version.updator = self.update_user  # type: ignore[assignment]
            logger.info(
                "update form version, version_no: {}, version_descriptor: {}, strategy: {}".format(
                    version.version_no, version.version_descriptor, snapshot_strategy
                )
            )
        # 快照策略为新建版本
        else:
            lasted_version: Optional[FormVersion] = self.versions.first()
            version = FormVersion.new(self)
            db.session.add(version)
            # 最新版本的 version descriptor 为 HEAD
            # 所有创建了新版本后，之前的版本的 version_descriptor 也要更新
            lasted_version and lasted_version.update_version_descriptor()
            logger.info(f"upgrade form version, from {lasted_version} to {version}")

        return version

    @in_transaction()
    def reuse_version(self, version_no: str):
        """使用目标版本的工单模板
        1. 清理掉不在目标版本的步骤
        2. 将模板版本的步骤复制一份，作为当前草稿箱内容
        """
        version: FormVersion | None = self.versions.filter_by(version_no=version_no).first()
        if not version:
            return Err(Exception(f"工单 {self.id} 版本 {version_no} 不存在"))

        target_steps = self.steps.filter(Step.id.in_(version.step_id)).all()
        for step_to_delete in self.backend_steps.filter(
            Step.step_uuid.not_in([step.step_uuid for step in target_steps])
        ).all():
            step_to_delete.delete()
        steps = [step.clone() for step in target_steps]
        for step in steps:
            step.is_dirty = True
            step.deleted = False
        db.session.add_all(steps)
        db.session.flush()
        old_map = {step.step_uuid: step for step in target_steps}
        new_map = {step.step_uuid: step for step in steps}
        for step_uuid in old_map:
            FormSymbol.update_from_step_copy(
                FormSymbolScope.from_step(old_map[step_uuid]),
                FormSymbolScope.from_step(new_map[step_uuid]),
            )
        for step in steps:
            step.update_raw_step()

        return Ok(None)

    @in_transaction()
    def drop_draft_version(self):
        """删除草稿版本
        如果工单模板发布过，恢复到最近发布的版本
        如果工单模板没有发布过，删除所有步骤，仅保留起始步骤
        """
        self.steps.filter(Step.is_dirty.is_(True)).update({Step.deleted: True}, synchronize_session=False)

        latest_version: FormVersion | None = self.versions.first()
        # 已发布的版本有可能在草稿箱里被标记删除了，需要恢复
        if latest_version:
            self.steps.filter(
                Step.id.in_(latest_version.step_id),
                Step.deleted.is_(True),
            ).update({Step.deleted: False}, synchronize_session=False)

        # 未发布的版本，把起始步骤的 deleted, next_step_ids 初始化
        else:
            begin = self.steps.filter(Step.step_type == StepType.begin).first()
            if not begin:
                logger.warning(f"form {self.id} has no begin step")
                return
            begin.deleted = False
            begin.next_step_ids = []

    @in_transaction()
    @no_auto_flush()
    def remove_invalid_refer_widget_info(self, step: Step, operator_nick: str):
        """删除其他步骤里引用本步骤的 widget info"""
        if not step.widget_collection:
            return
        if not step.widget_collection.changelog:
            return

        deleted = step.widget_collection.changelog.get("deleted_widget_key", [])
        if not deleted:
            return

        for other_step in self.backend_steps:
            other_step = cast(Step, other_step)
            # 如果 step_uuid 不相同，则说明不是同一个步骤的历史版本，此时再去处理。
            # 如果是该步骤的历史版本，则不处理。
            if other_step.step_uuid == step.step_uuid:
                continue
            result = other_step.remove_refer_widget_info_in_widget_collection(
                key_to_remove=deleted, operator_nick=operator_nick
            )
            if result:
                logger.info(f"remove invalid refer widget info, step: {other_step.step_uuid}, refer: {result}")
        # 本次已经将需要删除的 widget_infos 的清空，同时清空日志，以免日志影响到之后想要加回来这个组件。
        history_version_deleted_widget_key = (
            step.widget_collection.changelog.get("history_version_deleted_widget_key") or []
        )
        step.widget_collection.changelog.update(
            {
                "deleted_widget_key": [],
                "history_version_deleted_widget_key": history_version_deleted_widget_key + deleted,
            }
        )
        flag_modified(step.widget_collection, "changelog")

    @in_transaction()
    def step_modified(self, update_user: str):
        if update_user is not None:
            self.update_user = update_user
        self.can_publish = True


class FormVersion(DbBaseModel):
    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    created_at: Mapped[int] = mapped_column(
        sa.Integer,
        default=time.time,
        nullable=False,
    )
    updated_at: Mapped[int | None] = mapped_column(sa.Integer, default=time.time, nullable=True, onupdate=time.time)

    form_id: Mapped[int | None] = mapped_column(sa.ForeignKey("form.id"))
    form: Mapped[Form] = relationship(Form, back_populates="versions")
    step_id: Mapped[list] = mapped_column(sa.JSON, default=list, comment="当前版本已发布的步骤版本")
    job_road: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="工单流转路径")
    version_no: Mapped[str] = mapped_column(sa.String(32), default="", comment="版本号")
    version_descriptor: Mapped[str] = mapped_column(
        sa.String(32), default="", comment="版本摘要STASH(未发布的版本) HEAD etc."
    )
    updator: Mapped[str] = mapped_column(sa.String(64), default="", comment="模板的创建人（即模板发布人）")
    deleted: Mapped[bool] = mapped_column(sa.Boolean, default=False)
    meta: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="模板元信息")

    __table_args__ = (sa.UniqueConstraint("form_id", "version_descriptor", name="form_version_descriptor"),)

    def __str__(self):
        return f"FormVersion(form={self.form_id}, version={self.version_no}, {self.version_descriptor})"

    @property
    def road(self):
        return FormVersion.Schema.FormJobRoad.parse_obj(self.job_road)

    def get_first_step(self):
        return self.get_next_step(FormVersion.Schema.StepNode.BEGIN)

    def get_next_step(self, cur_step_uuid=None):
        StepNode = FormVersion.Schema.StepNode

        # cur_step_uuid 为 None 时，返回开始节点
        if not cur_step_uuid:
            return self.road.road_map[StepNode.BEGIN].current

        # 否则，返回 cur_step_uuid 对应的节点的下一个节点
        step_node = self.road.road_map[cur_step_uuid]
        # 后续为分支节点
        if step_node.next == StepNode.BRANCH:
            return step_node.next
        # 结束
        elif step_node.next == StepNode.END:
            return None
        else:
            next_step_node = self.road.road_map[step_node.next]
            return next_step_node.current

    def get_current_step(self, cur_step_uuid=None):
        if cur_step_uuid is None:
            return None
        else:
            step_node = self.road.road_map[cur_step_uuid]
            return step_node.current

    @no_auto_flush()
    def update_version_descriptor(self):
        self.version_descriptor = get_obj_hash({"steps": self.step_id, "version_no": self.version_no})

    @no_auto_flush()
    def update_version_no(self):
        """根据当前工单的所有版本号信息，更新自己的版本号为最新版本"""
        import semantic_version

        # 已经有版本号了，不需要再生成
        if self.version_no:
            return

        last_version = Form.Queries.latest_version_by_form_id(self.form_id)  # type: ignore[arg-type]
        if last_version:
            prev_version_no = last_version.version_no
        else:
            prev_version_no = None

        max_patch = max_minor = 100
        prefix = "V"
        if prev_version_no is None:
            next_version = semantic_version.Version("0.0.1")
        else:
            prev_version_no = prev_version_no.strip(prefix)
            try:
                semantic_prev_version = semantic_version.Version(prev_version_no)
                next_version = semantic_prev_version.next_patch()
                if next_version.patch >= max_patch:
                    next_version = next_version.next_minor()
                if next_version.minor >= max_minor:
                    next_version = next_version.next_major()
            except ValueError:
                next_version = semantic_version.Version("0.0.1")
        self.version_no = f"{prefix}{next_version}"

    def prev_version(self):
        version = (
            FormVersion.query.filter(FormVersion.form_id == self.form_id, FormVersion.id < self.id)
            .order_by(FormVersion.id.desc())
            .first()
        )
        return version

    @classmethod
    def which_strategy(cls, form: Form):
        class StrategyEnum(Enum):
            NEW = "NEW"
            UPDATE = "UPDATE"

            @property
            def is_update(self):
                return self == self.UPDATE

        # 没有历史版本，需要新建
        if (last_version := form.versions.first()) is None:
            return StrategyEnum.NEW
        # step_id 不变，仅 meta 信息更新，无需创建新的版本号
        if sorted(cls.new(form).step_id) == sorted(last_version.step_id):
            return StrategyEnum.UPDATE
        else:
            return StrategyEnum.NEW

    @classmethod
    def new(cls, form: "Form") -> "FormVersion":
        form_version = FormVersion()
        form_version.form_id = form.id

        StepNode = FormVersion.Schema.StepNode
        steps = Step.Queries.steps(form_id=form.id, deleted=False, is_dirty=False).all()
        step_ids = [step.id for step in steps]
        job_road = FormVersion.Schema.FormJobRoad()

        for step in steps:
            if step.step_type in [
                StepType.exclusive_gateway,
                StepType.iterate_gw_begin,
            ]:
                step_next = StepNode.BRANCH
            elif len(step.next_step_ids) == 0:  # 姑且通过这个方式判断是不是结束节点
                step_next = StepNode.END
            else:  # 普通一般节点
                step_next = step.next_step_ids[0]
            step_node = StepNode(current=step.id, next=step_next)
            job_road.road_map[step.step_uuid] = step_node
            if len(step.prev_step_ids) == 0:  # 姑且通过这个方式判断是不是开始节点
                job_road.road_map[StepNode.BEGIN] = step_node

        form_version.job_road = job_road.dict()
        form_version.step_id = step_ids
        form_version.updator = form.update_user  # type: ignore[assignment]
        form_meta_view = Form.View.FormVersionMeta.from_orm(form)
        form_version.meta = form_meta_view.dict()
        form_version.update_version_no()
        # 新建的版本，version_descriptor 为 HEAD
        form_version.version_descriptor = "HEAD"
        # form_version.form = form 时，会把 form_version 加入 db session
        # 这个方法得到的 form_version 不希望在 session 中管理
        assert form_version not in db.session.new

        return form_version

    def query_steps_with_entities(self, entities: list | None = None) -> list[Step]:
        step_query = Step.query.filter(Step.id.in_(self.step_id))
        if entities:
            step_query = step_query.with_entities(*entities)
        return step_query.all()

    def get_step_scope(self, step_uuid: str, steps: list[Step] | None = None):
        """step 所在的 scope 信息，列表顺序为 scope 的由近到远的顺序"""
        steps = steps or self.query_steps_with_entities(
            [Step.id, Step.step_uuid, Step.next_step_ids, Step.step_type, Step.data]
        )
        step_id_map = {step.id: step for step in steps}
        step_uuid_map = {step.step_uuid: step for step in steps}
        iterate_step_ids = {
            step.id for step in steps if step.step_type in (StepType.iterate_gw_begin, StepType.iterate_gw_end)
        }
        if len(iterate_step_ids) == 0:
            return []

        def get_step_by_step_uuid(step_uuid_: str):
            return step_uuid_map[step_uuid_]

        def get_step_by_step_id(step_id: int):
            return step_id_map[step_id]

        step = get_step_by_step_uuid(step_uuid)
        routes = self.all_possible_routes(steps)
        candidate_prefixes: set[tuple[FormVersion.Schema.StepNode]] = set()
        for route in routes:
            currents = [node.current for node in route]
            if step.id not in currents:
                continue
            node_idx = currents.index(step.id)
            candidate_route = tuple(
                [node for node in route[: node_idx + 1] if node.current in iterate_step_ids or node.current == step.id]
            )
            candidate_prefixes.add(candidate_route)

        scope_set = set()

        def get_step_scope_in_route(route_: list[FormVersion.Schema.StepNode]):
            stack = []
            for step_node in route_:
                current_step = get_step_by_step_id(step_node.current)
                match current_step.step_type:
                    case StepType.iterate_gw_begin:
                        stack.append(current_step.step_uuid)
                    case StepType.iterate_gw_end:
                        stack.pop()
                if step_node.current == step.id:
                    return tuple(reversed(stack))

        for route in candidate_prefixes:
            scope_set.add(get_step_scope_in_route(route))

        if len(scope_set) == 1:
            scope = []
            for each_step_uuid in scope_set.pop():
                each_step = get_step_by_step_uuid(each_step_uuid)
                widget_ref = WidgetRef.parse(each_step.data["widget_ref"], force_parse_str=True)
                scope.append(StepScope(step_uuid=each_step_uuid, widget_ref=widget_ref))
            return scope
        else:
            raise StopIteration(f"step is not unique in routes {scope_set}")

    def all_possible_routes(self, steps: list[Step] | None = None):
        steps = steps or self.query_steps_with_entities([Step.id, Step.next_step_ids])
        step_info = namedtuple("step_info", ["id", "next_step_ids"])
        step_map: dict[int, step_info] = {
            step.id: step_info(id=step.id, next_step_ids=step.next_step_ids) for step in steps
        }
        road_map = self.road.road_map
        memo: dict[str, list[list[FormVersion.Schema.StepNode]]] = {}

        def calculate_route(step_uuid):
            if step_uuid in memo:
                return memo[step_uuid]

            node = road_map[step_uuid]
            route: list[list[FormVersion.Schema.StepNode]]

            match node.next:
                case FormVersion.Schema.StepNode.END:
                    route = [[node]]
                case FormVersion.Schema.StepNode.BRANCH:
                    route = [
                        [node, *next_step_route]
                        for next_step_uuid in step_map[node.current].next_step_ids
                        for next_step_route in calculate_route(next_step_uuid)
                    ]
                case _:
                    route = [[node, *next_step_route] for next_step_route in calculate_route(node.next)]

            memo[step_uuid] = route
            return route

        routes = calculate_route(FormVersion.Schema.StepNode.BEGIN)
        return routes

    @classmethod
    def query_version_map_by_id(cls, id_list: list[int]) -> dict[int, str]:
        form_versions = (
            FormVersion.query.filter(FormVersion.id.in_(id_list))
            .options(Load(FormVersion).load_only(FormVersion.id, FormVersion.version_no, raiseload=True))
            .all()
        )
        return dict((version.id, version.version_no) for version in form_versions)

    class Schema:
        class StepNode(BaseModel):
            current: int = Field(description="当前步骤 id")
            next: Union[str, Literal["BRANCH", "END"]] = Field(
                description="后续步骤，BRANCH 表示分支，END 表示结束，其他情况为步骤 uuid"
            )

            BRANCH: ClassVar[str] = "BRANCH"
            BEGIN: ClassVar[str] = "BEGIN"
            END: ClassVar[str] = "END"
            TEMP: ClassVar[str] = "TEMP"

            def __hash__(self):
                return hash((self.current, self.next))

        class FormJobRoad(BaseModel):
            road_map: Dict[str, "FormVersion.Schema.StepNode"] = Field(default_factory=dict)

    class View:
        @omit_none_fields_before_validate("step_id", "job_road", "version_no", "version_descriptor", "updator", "meta")
        class BusinessOrderBasic(BaseModel):
            """工单实例查看工单模板版本相关的基本信息"""

            class Config:
                orm_mode = True

            id: int | None = Field(description="工单模板版本 id")
            form_id: int = Field(description="工单模板 id")
            step_id: List[int] = Field(default_factory=list, description="工单模板步骤 id")

            job_road: "FormVersion.Schema.FormJobRoad" = Field(
                default_factory=lambda: FormVersion.Schema.FormJobRoad(),
                description="工单流转路径",
            )
            version_no: str = Field(default="", description="版本号")
            version_descriptor: str = Field(default="", description="版本摘要STASH(未发布的版本) HEAD etc.")
            updator: str = Field(default="", description="模板的创建人（即模板发布人）")
            meta: dict = Field(default_factory=dict, description="模板元信息")
            created_at: int = Field(description="创建时间")

        class BusinessOrderDetail(BusinessOrderBasic):
            """工单实例查看工单模板版本相关的详细信息"""

            steps: List[Step.View.FormVersion] = Field(description="工单模板步骤信息", default_factory=list)

            @classmethod
            def from_orm_(cls, form_version) -> "FormVersion.View.BusinessOrderDetail":
                res = cls.from_orm(form_version)
                steps = Step.query.filter(Step.id.in_(form_version.step_id or [])).all()
                res.steps = [Step.View.FormVersion.from_orm(step) for step in steps]
                return res


class FormShop(DbBaseModel):
    form_id: Mapped[int] = mapped_column(sa.ForeignKey("form.id"), primary_key=True)
    form: Mapped[Form] = relationship(Form, back_populates="form_shops")
    channel_id: Mapped[int] = mapped_column(sa.ForeignKey("shop.channel_id"), primary_key=True)
    shop: Mapped["Shop"] = relationship(_resolve_shop_model, back_populates="shop_forms")

    class Status(str, LabeledEnum):
        ENABLED = "ENABLED", "店铺订阅该工单模板，且处于启用状态"
        DISABLED = "DISABLED", "店铺订阅该工单模板，但处于未启用状态"
        DELETED = "DELETED", "店铺取消订阅了工单模板，且不保留工单实例的数据"
        RESERVED = "RESERVED", "店铺取消订阅了工单模板，但保留工单实例的数据"

        BLANK = "BLANK"  # 没有实际绑定关系

        def __bool__(self):
            return self == self.ENABLED

        @classmethod
        def enabled_by_bool(cls, status: bool):
            return cls.ENABLED if status else cls.DISABLED

        @property
        def is_blank(self):
            return self == self.BLANK

        @property
        def is_subscribed(self):
            return self in [self.ENABLED, self.DISABLED]

        @property
        def is_deleted(self):
            return self in [self.DELETED, self.RESERVED, self.BLANK]

    status: Mapped[Status] = mapped_column(sa.Enum(Status), default=Status.ENABLED)

    # 冗余字段
    org_id: Mapped[int] = mapped_column(sa.Integer, nullable=False)
    sid: Mapped[str | None] = mapped_column(sa.String(32))
    sync_merged_form_id: Mapped[int | None] = mapped_column(
        sa.Integer, nullable=True, comment="之前通过工单模板关联的方式创建的模板的 id"
    )

    @property
    def form_wrapper(self):
        """比 Form.wraps(shop) 少一次 db 查询"""
        return FormWrapper(form=self.form, shop=self.shop, form_shop=self)

    @classmethod
    @no_auto_flush()
    def new(cls, form: "Form", shop: "Shop", enabled: bool = True) -> Self:
        self = cls()
        self.form = form
        self.shop = shop
        self.enabled = self.Status.ENABLED if enabled else self.Status.DISABLED  # type: ignore[attr-defined]
        self.update_shop_info(shop)
        return self

    @classmethod
    @no_auto_flush()
    def get_blank(cls, form: "Form", shop: "Shop") -> Self:
        """获取一个 FormShop 对象，它并没有真正的绑定关系"""
        # FIXME: 防止污染 session
        self = cls.new(form, shop)
        self.status = cls.Status.BLANK  # type: ignore[assignment]
        return self

    def update_shop_info(self, shop: "Shop"):
        """从 shop 中同步相关的冗余信息"""
        self.org_id = shop.org_id  # type: ignore[assignment]
        self.sid = shop.sid

    @classmethod
    def query_shop_map(cls, form_id_list: list[int]) -> dict[tuple[int, str], "Shop"]:
        form_shops: list[FormShop] = (
            FormShop.query.filter(FormShop.form_id.in_(form_id_list))
            .options(Load(FormShop).joinedload(FormShop.shop))
            .all()
        )
        return {(form_shop.form_id, unwrap_optional(form_shop.sid)): form_shop.shop for form_shop in form_shops}


class StepFieldOverride(DbBaseModel):
    channel_id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    step_uuid: Mapped[str] = mapped_column(sa.String(36), primary_key=True)

    # 冗余字段
    form_id: Mapped[int] = mapped_column(sa.ForeignKey("form.id"), nullable=False)
    sid: Mapped[str | None] = mapped_column(sa.String(32))

    class OverrideMethod(StrEnum):
        EXTEND = "EXTEND"
        UPDATE = "UPDATE"
        REPLACE = "REPLACE"  # 替换

    override_method: Mapped[OverrideMethod] = mapped_column(sa.Enum(OverrideMethod), default=OverrideMethod.REPLACE)
    override_field: Mapped[str] = mapped_column(sa.String(256), nullable=False)
    override_value: Mapped[Any | None] = mapped_column(sa.JSON)

    class View:
        class RawStep(BaseModel):
            class Config:
                orm_mode = True

            channel_id: int = Field(description="店铺 id")
            step_uuid: str = Field(description="步骤 uuid")
            form_id: int = Field(description="工单模板 id")
            sid: Optional[str] = Field(default=None, description="店铺 sid")
            override_method: "StepFieldOverride.OverrideMethod" = Field(
                default_factory=lambda: StepFieldOverride.OverrideMethod.REPLACE,
                description="重写方式",
            )
            override_field: str = Field(description="重写字段")
            override_value: Any = Field(default=None, description="重写值")

    @classmethod
    @no_auto_flush()
    def new(cls, form_shop: FormShop, step: Step, override_field: str, override_method=None):
        step_field_config = StepFieldOverride()
        step_field_config.step_uuid = step.step_uuid
        step_field_config.form_id = form_shop.form_id
        step_field_config.channel_id = form_shop.channel_id
        step_field_config.sid = form_shop.sid
        step_field_config.override_method = override_method or cls.OverrideMethod.REPLACE
        step_field_config.override_field = override_field

        return step_field_config


if TYPE_CHECKING:
    FormWrapperBase = Form
    StepWrapperBase = Step
else:
    FormWrapperBase = object
    StepWrapperBase = object


class FormWrapper(FormWrapperBase):
    """具体店铺的工单模板"""

    __slots__ = ("_form", "_shop", "_form_shop", "_form_id_list")

    def __init__(self, form: "Form", shop: "Shop", form_shop: Optional[FormShop] = None):
        self._form = form
        self._form_id_list = [self._form.id]
        self._shop = shop
        if form_shop is None:
            form_shop = Form.Utils.form_shop(form, shop)
        self._form_shop = form_shop

        if form_shop.sync_merged_form_id:
            self._form_id_list.append(form_shop.sync_merged_form_id)

    @property
    def business_orders(self):
        from robot_processor.business_order.models import BusinessOrder

        return BusinessOrder.query.filter(BusinessOrder.form_id.in_(self._form_id_list)).filter(
            BusinessOrder.sid == self._form_shop.sid
        )

    @property
    def status(self):
        return self._form_shop.status

    @property
    def enabled(self):
        return bool(self._form_shop.status)

    @property
    def deleted(self):
        return self._form_shop.status.is_deleted

    @property
    def shop(self):
        return self._shop

    @property
    def sid(self):
        return self._shop.sid

    @property
    def org_id(self):
        return self._shop.org_id

    def __getattr__(self, item):
        """
        工单模板改为了订阅的方式，某些字段需要根据店铺来获取
        通过 wrapper 的方式，尽可能减小订阅方式的改变在业务层带来的影响
        通过实例调用的顺序:
            1. 数据描述符
            2. 实例属性
            3. 非数据描述符
            4. 类属性
            5. __getattr__
        """
        # 如果是 method，要把 self 替换成 step_wrapper
        if inspect.ismethod(getattr(self._form, item, None)):
            return partial(getattr(Form, item), self)
        # 如果是 property，要把 self 替换成 step_wrapper
        elif isinstance(getattr(Form, item, None), property):
            return getattr(Form, item).fget(self)
        return getattr(self._form, item)

    def __setattr__(self, key, value):
        if key in self.__slots__:
            super().__setattr__(key, value)
        else:
            setattr(self._form, key, value)

    def __str__(self):
        return "<FormWrapper id={id}, shop={shop_title}@{shop_sid}>".format(
            id=self.id, shop_title=self.shop.title, shop_sid=self.shop.sid
        )

    def __repr__(self):
        return str(self)

    def publish(self):
        from robot_processor.business_order.tasks import update_approvers

        """发布工单模板."""
        form: Form = self._form
        need_update_approve_steps = []
        with in_transaction(), no_auto_flush():
            steps_is_dirty: List[Step] = (
                form.backend_steps.filter(Step.is_dirty.is_(True))
                .options(Load(Step).joinedload(Step.widget_collection))
                .all()
            )
            for step in steps_is_dirty:
                step.is_dirty = False
                if step.is_human():
                    if widget_collection := step.widget_collection:
                        widget_collection.is_dirty = False
                if step.step_type == StepType.approve:
                    need_update_approve_steps.append(step)
                if self.form_mold == FormMold.EVENT and step.step_type == StepType.event:
                    self.event = step.event
            form.can_publish = False
            form.snapshot()
        self.send_form_change_event()
        for step in need_update_approve_steps:
            update_approvers.send(step.id)

    def send_form_change_event(self):
        """在表单配置标记为已发布状态时，下发表单配置变更事件

        topic: robot-form-change-event

        """
        from robot_processor.ext import robot_form_change_producer

        logger.info(f"{self.id} 工单发布通知下游")

        robot_form_change_producer(
            dict(
                form_id=int(self.id),
                org_id=int(self.shop.org_id),
                sid=self.shop.sid,
                model="WidgetCollection",
                change_data=dict(
                    delete={},
                    replace={},
                ),
            )
        )

    def trigger_on_publish(self, update_user: str):
        """触发 on_publish 事件，同时提供函数签名的声明"""
        self.record_form_publish_log(update_user=update_user)
        self.produce_form_publish_message()
        self.rebind_product_provider()
        self.update_bi_report_refer()
        self.update_form_event_subscription()
        self.sync_event_schedule()

    def trigger_on_create(self, update_user: str):
        """触发 on_create 事件，同时提供函数签名的声明"""
        self.record_form_create_log(update_user=update_user)
        self.init_reports_form_info()

    def trigger_on_delete(self, update_user: str):
        """触发 on_delete 事件，同时提供函数签名的声明"""
        self.record_form_delete_log(update_user=update_user)

    def record_form_publish_log(self, /, update_user: str):
        """记录工单发布的操作日志"""
        message = Form.Schema.FormActionLog.build(
            form=self,
            action=Form.Schema.RecordLogAction.update,  # type: ignore[arg-type]
            user=update_user,
            operate_ts=int(self.updated_at),
        )
        action_client.create_action_log_by_kafka(message.dict())

    def produce_form_publish_message(self):
        """下发工单变更事件的消息"""
        from robot_processor.ext import form_producer

        # 发布工单对所有订阅的店铺都生效，因此需要遍历所有订阅的店铺
        for shop in self.subscribed_shops:
            form = self.wraps(shop)
            message = {
                "org_id": int(form.org_id),
                "sid": form.sid,
                "form_id": str(form.id),
                "name": form.name,
                "description": form.description,
                "enabled": form.enabled,
                "deleted": form.deleted,
                "enable_service_count": form.enable_service_count,
                "form_mold": form.form_mold.name,
            }
            form_producer(message)

    def rebind_product_provider(self):
        """更新商家商品数据源

        通过判断工单模板的表单配置中是否有 ref_config 来决定 action 为 delete 或 update
        """
        from sqlalchemy import func
        from sqlalchemy import select

        from robot_processor.client import asgard_client
        from robot_processor.provider.schema import BindProviderSchema

        if_ref_config = (
            select(WidgetInfo)
            .select_from(Step)
            .join(Step.widget_collection)
            .join(WidgetCollection.widget_info)
            .where(func.json_length(WidgetInfo.ref_config) > 0, Step.form == self)
            .exists()
        )

        if db.session.query(if_ref_config).scalar():
            action = "update"
        else:
            action = "delete"

        for shop in self.subscribed_shops:
            param = BindProviderSchema(sid=shop.sid, form_id=self.id, action=action).dict()
            asgard_client.bind_provider(param)

    def update_bi_report_refer(self):
        """更新BI报表的数据源"""
        pass

    def update_form_event_subscription(self):
        from robot_processor.form.event.models import FormEventSubscription

        if self.form_mold != FormMold.EVENT:
            logger.bind(form_id=self.id).info("ignore update form event subscription by form mold.")
            return
        event_step = first(filter(lambda step: step.step_type == StepType.event, self.job_steps), None)
        if event_step is None:
            logger.bind(form_id=self.id).error("ignore update form event subscription by no event step.")
            return
        for form_shop in FormShop.query.filter_by(form_id=self.id).all():
            enabled = bool(form_shop.status)
            FormEventSubscription.upsert(form_shop.shop, self, event_step, self.event, enabled)

    @in_transaction()
    def sync_event_schedule(self):
        from robot_processor.form.event.models import sync_schedule_state

        try:
            sync_schedule_state(self.org_id, self.event)
        except Exception as e:
            logger.opt(exception=e).error(f"sync evnet schedule error. {e}")

    def record_form_delete_log(self, /, update_user: str):
        """记录工单删除的操作日志"""
        # 删除工单是单个店铺的操作，因此只记录当前店铺的操作日志
        message = Form.Schema.FormActionLog.build(
            form=self,
            action=Form.Schema.RecordLogAction.delete,  # type: ignore[arg-type]
            user=update_user,
            operate_ts=int(self.updated_at),
        )
        action_client.create_action_log_by_kafka(message.dict())

    def record_form_create_log(self, /, update_user: str):
        """记录工单创建的操作日志"""
        # 创建工单时有可能直接选择关联了多个店铺，因此需要记录所有店铺的操作日志
        message = Form.Schema.FormActionLog.build(
            form=self,
            action=Form.Schema.RecordLogAction.create,  # type: ignore[arg-type]
            user=update_user,
            operate_ts=int(self.updated_at),
        )
        action_client.create_action_log_by_kafka(message.dict())

    def init_reports_form_info(self):
        """工单模板创建时，通知明细报表初始化工单模板信息"""
        from robot_processor.ext import robot_form_change_producer

        robot_form_change_producer(
            dict(
                form_id=int(self.id),
                org_id=int(self.org_id),
                sid=self.sid,
                model="WidgetCollection",  # 报表主要关心工单模板的表单配置
                change_data=dict(),  # 创建工单模板时，这里的配置还没有，保持数据格式并留空
            )
        )


class StepWrapper(StepWrapperBase):
    """具体店铺的工单模板"""

    __slots__ = ("_form", "_step", "_step_overrides", "_form_shop")

    _step_overrides: Dict[str, StepFieldOverride.View.RawStep]

    def __init__(self, step: Step, form: FormWrapper):
        self._form = form
        self._form_shop = form._form_shop  # noqa: pylint=protected-access
        self._step = step

        try:
            raw_step = Step.View.RawStep.parse_obj(self._step.raw_step)
            self._step_overrides = {config.override_field: config for config in raw_step.override_config}
        except ValidationError:
            self._step_overrides = {}

    @staticmethod
    def item_should_override(item):
        """item 是否需要重写"""
        return item in ("assignee_groups", "assistants_v2")

    @property
    def enabled(self):
        return self._form.enabled

    @property
    def shop(self):
        return self._form.shop

    @property
    def form(self):
        return self._form

    def __str__(self):
        return (
            "<StepWrapper id={id}, step_uuid={step_uuid}, " "is_dirty={is_dirty}, shop={shop_title}@{shop_sid}>"
        ).format(
            id=self.id,
            step_uuid=self.step_uuid,
            is_dirty=self.is_dirty,
            shop_title=self.shop.title,
            shop_sid=self.shop.sid,
        )

    def __repr__(self):
        return str(self)

    def __getattr__(self, item):
        """
        因为工单模板改为订阅的方式，并且支持店铺重写某些字段，
        通过 step_wrapper 可以获取店铺重写后的字段值
        """
        # 字段配置为不可重写
        if not self.item_should_override(item):
            # 如果是 method，要把 self 替换成 step_wrapper
            if inspect.ismethod(getattr(self._step, item, None)):
                return partial(getattr(Step, item), self)
            # 如果是 property，要把 self 替换成 step_wrapper
            elif isinstance(getattr(Step, item, None), property):
                return getattr(Step, item).fget(self)
            return getattr(self._step, item)

        # 字段配置为可重写，但是没有重写
        if item not in self._step_overrides:
            return getattr(self._step, item)

        # 获取配置
        step_field_config = self._step_overrides[item]
        origin_value = getattr(self._step, item)
        # 根据 override method 来组装最终渲染的结果
        if step_field_config.override_method == StepFieldOverride.OverrideMethod.EXTEND:
            return [*(origin_value or []), *step_field_config.override_value]
        elif step_field_config.override_method == StepFieldOverride.OverrideMethod.UPDATE:
            return {**(origin_value or {}), **step_field_config.override_value}
        elif step_field_config.override_method == StepFieldOverride.OverrideMethod.REPLACE:
            return step_field_config.override_value
        else:
            return origin_value  # type: ignore[unreachable]

    def __setattr__(self, key, value):
        """
        因为更新字段涉及到数据库操作，所以 step_wrapper 不支持隐式的更新 step_override_field
        默认都是更新 step 上字段的值
        店铺重写的配置，通过 `set_step_field_override` 实现
        """
        if key in self.__slots__:
            return super().__setattr__(key, value)
        else:
            setattr(self._step, key, value)

    def set_step_field_override(self, key, value):
        # 字段配置为不可重写
        if not self.item_should_override(key):
            raise TypeError(f"字段 {key} 不支持重写操作")
        if key not in self._step_overrides:
            step_field_config = StepFieldOverride.new(self._form_shop, self._step, key)
            db.session.add(step_field_config)
            self._step_overrides[key] = step_field_config
            step_field_config.override_value = value
        else:
            step_field_config = self._step_overrides[key]
            step_field_config.override_value = value

    def trigger_on_create(self, update_user: str):
        self.update_step_relationship(update_user)
        self.record_step_create_log(update_user)

    def trigger_on_update(self, update_user: str):
        self.form.remove_invalid_refer_widget_info(step=self._step, operator_nick=update_user)
        self.update_step_relationship(update_user)
        self.record_step_update_log(update_user)

    def trigger_on_delete(self, update_user: str):
        self.record_step_delete_log(update_user)
        self.delete_step_relationship(update_user)

    @logger.catch
    @in_transaction()
    @no_auto_flush()
    def update_step_relationship(self, update_user: str):
        """更新步骤的关系"""
        step_map: Dict[str, Step] = {step.step_uuid: step for step in self.form.backend_steps}
        for step_uuid in self.prev_step_ids:
            if not (step := step_map.get(step_uuid)):
                continue
            next_step_ids = set(step.next_step_ids or [])
            next_step_ids.add(self.step_uuid)
            step.pipeline_update(update_user, next_step_ids=list(next_step_ids))
        for step_uuid in self.next_step_ids:
            if not (step := step_map.get(step_uuid)):
                continue
            prev_step_ids = set(step.prev_step_ids or [])
            prev_step_ids.add(self.step_uuid)
            step.pipeline_update(update_user, prev_step_ids=list(prev_step_ids))

    @logger.catch
    def record_step_create_log(self, update_user: str):
        """记录步骤创建的操作日志"""
        message = Form.Schema.FormActionLog.build(
            form=self.form,
            action=Form.Schema.RecordLogAction.create,  # type: ignore[arg-type]
            user=update_user,
            operate_ts=int(self.updated_at),
        )
        action_client.create_action_log_by_kafka(message.dict())

    @logger.catch
    def record_step_update_log(self, update_user: str):
        """记录步骤保存的操作日志"""
        message = Form.Schema.FormActionLog.build(
            form=self.form,
            action=Form.Schema.RecordLogAction.edit,  # type: ignore[arg-type]
            user=update_user,
            operate_ts=int(self.updated_at),
        )
        action_client.create_action_log_by_kafka(message.dict())

    @logger.catch
    def record_step_delete_log(self, update_user: str):
        """记录步骤删除的操作日志"""
        message = Form.Schema.FormActionLog.build(
            form=self.form,
            action=Form.Schema.RecordLogAction.delete,  # type: ignore[arg-type]
            user=update_user,
            operate_ts=int(self.updated_at),
        )
        action_client.create_action_log_by_kafka(message.dict())

    @logger.catch
    @in_transaction()
    @no_auto_flush()
    def delete_step_relationship(self, update_user: str):
        """更新前后步骤的关系"""
        step_map: Dict[str, Step] = {step.step_uuid: step for step in self.form.backend_steps}
        for step_uuid in self.prev_step_ids:
            if not (step := step_map.get(step_uuid)):
                continue
            next_step_ids = set(step.next_step_ids or [])
            if self.step_uuid in next_step_ids:
                next_step_ids.remove(self.step_uuid)
                step.update(update_user=update_user, next_step_ids=list(next_step_ids))
        for step_uuid in self.next_step_ids:
            if not (step := step_map.get(step_uuid)):
                continue
            prev_step_ids = set(step.prev_step_ids or [])
            if self.step_uuid in prev_step_ids:
                prev_step_ids.remove(self.step_uuid)
                step.update(update_user=update_user, prev_step_ids=list(prev_step_ids))


class StepTemplate(DbBaseModel, BasicMixin):
    """节点模版"""

    __ignore_columns__ = ["created_at", "updated_at", "uuid", "form_template"]

    name: Mapped[str | None] = mapped_column(sa.String(64), comment="步骤名称")
    description: Mapped[str | None] = mapped_column(sa.TEXT, comment="步骤说明")
    step_type: Mapped[StepType] = mapped_column(sa.Enum(StepType), default=StepType.auto, comment="步骤类型")
    # 仅当步骤类型为人工时可设置
    assistants: Mapped[dict] = mapped_column(sa.JSON, comment="执行客服", default=dict)

    assistants_v2: Mapped[dict] = mapped_column(sa.JSON, comment="执行客服", default=dict)
    #
    buyer_edit: Mapped[bool] = mapped_column(sa.Boolean, default=False, comment="是否允许买家填写")
    # 仅当允许买家填写时可编辑
    buyer_reply: Mapped[list] = mapped_column(sa.JSON, default=list, comment="买家引导话术")
    # 步骤完成时，工单的状态
    status: Mapped[JobStatus] = mapped_column(sa.Enum(JobStatus), default=JobStatus.PENDING)
    # 前置节点
    prev_step_ids: Mapped[list] = mapped_column(sa.JSON, default=list)
    # 后置节点
    next_step_ids: Mapped[list] = mapped_column(sa.JSON, default=list)

    uuid: Mapped[str] = mapped_column(sa.String(32), default=get_nonce)

    form_template_id: Mapped[int | None] = mapped_column(sa.Integer, sa.ForeignKey("form_template.id"))

    widget_collection_id: Mapped[str | None] = mapped_column(sa.String(32))

    key_map: Mapped[dict] = mapped_column(sa.JSON, default=dict)

    ui_schema: Mapped[list] = mapped_column(sa.JSON, default=list)
    # 步骤上的表单编辑器的值
    data: Mapped[dict] = mapped_column(sa.JSON, default=dict)
    branch: Mapped[list] = mapped_column(sa.JSON, default=list)
    # 跳转步骤的相关配置
    jump: Mapped[dict] = mapped_column(sa.JSON, default=dict)
    form_symbol: Mapped[list] = mapped_column(sa.JSON, default=list)
    event: Mapped[EventType | None] = mapped_column(sa.Enum(EventType, native_enum=False, length=128))
    event_shortcuts: Mapped[list | None] = mapped_column(sa.JSON, default=list, comment="事件选择的常用规则")
    event_filters: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="事件配置的自定义过滤条件")

    @property
    def step_uuid(self):
        """兼容 sort_steps 方法"""
        return self.uuid

    @property
    def widget_collection(self):
        return WidgetCollection.query.get(self.widget_collection_id) if self.widget_collection_id else None

    def to_dict(self, **kwargs):
        info = super().to_dict()
        info["step_uuid"] = self.uuid
        return info

    def publish(self):
        step = Step()
        step.name = self.name
        step.description = self.description
        step.buyer_edit = self.buyer_edit
        step.buyer_reply = self.buyer_reply
        step.prev_step_ids = self.prev_step_ids
        step.next_step_ids = self.next_step_ids
        step.widget_collection_id = self.widget_collection_id
        step.key_map = self.key_map
        step.data = self.data
        step.branch = self.branch
        step.jump = self.jump or {}
        step.event = self.event
        step.event_shortcuts = self.event_shortcuts
        step.event_filters = self.event_filters
        if validation_config := self.data.get("validation_config"):
            step.data["validation_config"] = validation_config
        step.set_step_type(self.step_type)
        step.set_status(self.status)
        step.set_assistants_v2({"select_type": SelectType.all.value})
        if step.step_type == StepType.begin:
            step.widget_collection = WidgetCollection.create_or_update_widget_info_for_system_widget()
        return step

    @property
    def task(self):
        from robot_processor.rpa_service.models import Rpa

        if not (rpa_id := self.data.get("rpa_id")):
            return
        return Rpa.query.get(rpa_id)


class FormTemplate(DbBaseModel, BasicMixin):
    """工单模版"""

    # 以下四个字段为表单固有不变的
    name: Mapped[str | None] = mapped_column(sa.String(64), comment="工单名称")
    description: Mapped[str | None] = mapped_column(sa.Text, comment="工单描述")
    enabled: Mapped[bool] = mapped_column(sa.Boolean, default=False, comment="工单开关")
    co_edit: Mapped[bool] = mapped_column(sa.Boolean, default=True, comment="是否允许协同编辑")
    update_user: Mapped[str | None] = mapped_column(sa.String(128), comment="最新更新人")
    creator: Mapped[str | None] = mapped_column(sa.String(128), comment="创建人")
    # 官方模版id
    uuid: Mapped[str] = mapped_column(sa.String(32), default=get_nonce)
    code: Mapped[int] = mapped_column(sa.Integer, default=0)
    order: Mapped[int] = mapped_column(sa.Integer, default=1)
    form_mold: Mapped[FormMold] = mapped_column(
        sa.Enum(FormMold),
        default=FormMold.CUSTOM,
        comment="工单类型,不同的类型工单模板的配置有不同限制",
    )

    steps: DynamicMapped[StepTemplate] = relationship(StepTemplate, backref="form_template", lazy="dynamic")

    category: Mapped[str | None] = mapped_column(sa.String(32))
    tags: Mapped[list] = mapped_column(sa.JSON, default=list)
    # 可见维度
    visibility_level: Mapped[VisibilityType] = mapped_column(
        sa.Enum(VisibilityType),
        default=VisibilityType.ORG,
        comment="可见维度,哪种类型的店铺可以看到模板",
    )
    visibility_type: Mapped[list] = mapped_column(sa.JSON, default=list, comment="可见维度,哪种类型的店铺可以看到模板")
    deleted: Mapped[bool] = mapped_column(sa.Boolean, default=False)
    event: Mapped[str | None] = mapped_column(sa.String(128))

    def publish(self, shop, name, description, enabled, co_edit, form_category, owner_list=None):
        """
        从官方模板发布工单模板。
        """
        from robot_processor.currents import g
        from robot_processor.form.system_widget_utils import create_step_0
        from robot_processor.rpa_service.models import Rpa

        # 创建工单模板。
        form = Form()
        form.name = name
        form.description = description
        form.tags = self.tags
        form.enabled = enabled  # type: ignore[attr-defined]
        form.co_edit = co_edit
        form.uuid = self.uuid
        form.form_mold = self.form_mold
        form.category = form_category
        form.event = self.event  # type: ignore[assignment]
        db.session.add(form)
        db.session.flush()

        step_template_map = {}

        # 步骤与其对应的 form_symbol 快照信息形成的映射。
        step_id_to_form_symbols_map: dict[int, list[dict]] = {}

        if StepType.begin in [step.step_type for step in self.steps]:
            for step_template in self.steps:
                step = step_template.publish()
                db.session.add(step)
                db.session.flush()
                form.steps.append(step)
                step_template_map[str(step_template.uuid)] = str(step.step_uuid)
                step_id_to_form_symbols_map.update({step.id: step_template.form_symbol or []})
        else:
            # 部分官方工单模板可能会存在没有起始步骤的情况。
            # 所以需要判断一下，如果没有，则进行创建。
            begin_step = create_step_0(form, owners=owner_list)
            db.session.add(begin_step)
            db.session.flush()
            form.steps.append(begin_step)
            step_template_map[str(begin_step.step_uuid)] = str(begin_step.step_uuid)
            for step_template in self.steps:
                step = step_template.publish()
                db.session.add(step)
                if len(step.prev_step_ids) == 0:
                    begin_step.next_step_ids = [str(step_template.uuid)]
                    flag_modified(begin_step, "next_step_ids")
                    step.prev_step_ids = [begin_step.step_uuid]
                    flag_modified(step, "prev_step_ids")
                db.session.flush()
                form.steps.append(step)
                step_template_map[str(step_template.uuid)] = str(step.step_uuid)
                step_id_to_form_symbols_map.update({step.id: step_template.form_symbol or []})

        widget_map: dict[str, str] = {}
        step_id_to_ui_schema_map: dict[int, list] = {}

        # 按照生成的步骤的 uuid 来构建新的步骤前后关系。
        for step in form.steps:
            step.prev_step_ids = [new for prev in step.prev_step_ids or [] if (new := step_template_map.get(prev))]
            step.next_step_ids = [
                new for next_step in step.next_step_ids or [] if (new := step_template_map.get(next_step))
            ]

            # 重新生成新的key，并将对应的 ui_schema 存储至 map 中。
            if step.widget_collection_id:
                if step.step_type == StepType.begin:
                    continue
                ui_schema = []
                for widget in step.widget_collection.widget_info:
                    if widget.before:  # 引用的无需重新生成key
                        new_key = widget_map[widget.key]
                    else:
                        new_key = get_nonce()
                        widget_map[widget.key] = new_key
                    info = widget.to_dict()
                    info.update({"key": new_key})
                    ui_schema.append(info)
                step_id_to_ui_schema_map.update({step.id: ui_schema})

        # 刷写一遍 widget_info 的内容（主要是按照新生成的 widget_key 来替换原有的 key）
        for step in form.steps:
            current_step_ui_schema: list | None = step_id_to_ui_schema_map.get(step.id)
            if current_step_ui_schema is None:
                continue

            ui_schema_string = json.dumps(current_step_ui_schema, ensure_ascii=False)
            for old, new in {**widget_map, **step_template_map}.items():
                ui_schema_string = ui_schema_string.replace(old, new)
            current_step_ui_schema = json.loads(ui_schema_string)
            wc = WidgetCollection.new(current_step_ui_schema)
            db.session.add(wc)
            db.session.flush()
            step.widget_collection_id = wc.id

        # 生成新的 form_symbol。
        for step in form.steps:
            current_step_form_symbols = step_id_to_form_symbols_map.get(step.id) or []
            current_step_form_symbols_string = json.dumps(current_step_form_symbols, ensure_ascii=False)
            for old, new in {**widget_map, **step_template_map}.items():
                current_step_form_symbols_string = current_step_form_symbols_string.replace(old, new)
            current_step_form_symbols = json.loads(current_step_form_symbols_string)
            for cfs in current_step_form_symbols:
                fs = FormSymbol(
                    form_id=step.form_id,
                    step_uuid=step.step_uuid,
                    step_id=step.id,
                    name=cfs.get("name"),
                    type_spec=cfs.get("type_spec"),
                    options=cfs.get("options"),
                    parent=cfs.get("parent"),
                    parent_path=cfs.get("parent_path"),
                    redirect=cfs.get("redirect"),
                    component_id=cfs.get("component_id"),
                    render_config=cfs.get("render_config"),
                )
                db.session.add(fs)
                db.session.flush()

        # 更新每个步骤的 data、branch、key_map 以及 jump 配置信息。
        for step in form.steps:
            branch = step.branch or []
            data = step.data or {}
            key_map = step.key_map or {}
            branch_string = json.dumps(branch, ensure_ascii=False)
            data_string = json.dumps(data, ensure_ascii=False)
            key_map_string = json.dumps(key_map, ensure_ascii=False)
            for old, new in {**widget_map, **step_template_map}.items():
                branch_string = branch_string.replace(old, new)
                data_string = data_string.replace(old, new)
                key_map_string = key_map_string.replace(old, new)
            branch = json.loads(branch_string)
            data = json.loads(data_string)
            key_map = json.loads(key_map_string)
            step.branch = branch
            step.data = data
            step.key_map = key_map

            # 处理RPA
            if rpa_id := data.get("rpa_id"):
                rpa: Rpa | None = Rpa.query.get(rpa_id)
                subscribe_info = Rpa.Utils.get_org_subscribe_info(rpa, g.shop.org_id)  # type: ignore[arg-type]
                if subscribe_info.can_subscribe:
                    Rpa.Utils.subscribe(rpa=rpa, org_id=g.shop.org_id)  # type: ignore[arg-type]
                    action_client.create_action_log_by_kafka(
                        unwrap_optional(rpa).create_operation_log(
                            unwrap_optional(g.shop.org_id),
                            g.shop.sid,
                            g.login_user_detail.user_nick,  # type: ignore
                            "subscribe",
                            "TaskTemplate",
                            unwrap_optional(form.name),
                        )
                    )
                else:  # 清空
                    step.data.pop("rpa_id", None)

            # 处理跳转：重新绑定 target 的 step_uuid
            if step.step_type == StepType.jump and isinstance(step.jump, dict):
                step_auto_jump_config = step.get_auto_jump_config()
                current_target_step_uuid = step_template_map.get(str(step_auto_jump_config.target))
                step_auto_jump_config.target = current_target_step_uuid
                step.jump = step_auto_jump_config.dict()

        form.subscribe(shop, enabled)
        db.session.flush()
        for step in form.steps:
            step.update_raw_step()
        db.session.flush()
        return form


class Component(DbBaseModel, BasicMixin):
    """组件的配置

    Attributes:
        id: 组件的标识
        created_at: 创建时间
        updated_at: 最近更新时间
        schema: FieldSchema 的 JSON 定义，由前端通过 git 维护的 JSON 文件内容
                https://git.leyantech.com/fed/fraxel-schema 项目的 CI/CD 来自动同步到数据库

    """

    id: Mapped[str] = mapped_column(sa.String(128), primary_key=True)  # type: ignore[assignment]
    schema: Mapped[dict] = mapped_column(sa.JSON)

    @hybrid_property
    def type_spec(self):
        return self.schema["type_spec"]


class StaticConfig(DbBaseModel):
    """提供给前端使用的一些静态配置，后端不使用，仅提供接口给前端"""

    key: Mapped[str] = mapped_column(sa.String(64), primary_key=True)
    config: Mapped[dict] = mapped_column(sa.JSON)

    @classmethod
    def get_config(cls, config_key):
        self = db.ro_session.get(cls, config_key)
        if not self:
            return Err(f"{config_key} not found")
        return Ok(self.config)


# 这里 FormSymbol 使用 step_id 是不好的，一个 FormSymbol 的 namespace 应该是 form + step_uuid + version
# 现在使用 step_id 是因为目前一个 step_id 会归属多个 version
# 在后续的 form as document template 改造中，将不会有 step_id 的概念
class FormSymbol(DbBaseModel, BasicMixin):
    """工单中定义的一个变量

    data_type variable_name = value
        ⬆️          ⬆️          ⬆️
    type_spec     name        (initialization)
    这个数据结构定义的是 data_type 和 variable_name，initialization 是在工单实例化时完成

    （可选）一个变量可以绑定一个组件，组件用于网页表单的渲染

    Attributes:
        form_id: 变量所属的工单
        step_uuid: 变量所属的步骤节点 (scope)
        step_id: 变量所属的步骤节点 (冗余字段，用于查询)
        name: (optional) 变量名，当 parent.type=array 时，name 为 null
        type_spec: 变量的类型信息
        options: 变量的校验配置，如 required, enum；变量的数据填充配置，如 autofill
        parent: (optional) 是否为容器类型内的字段
        redirect: (optional) 变量引用自其它 scope
        component_id: (optional) 变量绑定的网页表单组件，当变量未绑定表单组件时值为 null
        render_config: (optional) 网页表单组件的渲染配置，当 widget = null 时值为 null
    Constraints:
        unique(form_id + step_id + name)

    """

    __table_args__ = (
        sa.Index("idx_form_symbol_ident", "form_id", "step_uuid", "step_id", "name"),
        sa.Index("idx_form_symbol_component", "component_id"),
        sa.Index("idx_form_symbol_type", "v_type"),
    )

    form_id: Mapped[int]
    step_uuid: Mapped[str] = mapped_column(sa.String(36))
    step_id: Mapped[int]

    name: Mapped[str | None] = mapped_column(sa.String(128))
    type_spec: Mapped[dict] = mapped_column(sa.JSON)
    v_type: Mapped[str] = mapped_column(sa.String(32), Computed("(json_unquote(json_extract(type_spec, '$.type')))"))
    options: Mapped[dict] = mapped_column(sa.JSON, default=dict)

    parent: Mapped[str | None] = mapped_column(sa.String(128))
    # 在当前 scope 内的绝对路径，如: 344e4f94-999c-4394-a558-fbad78871c28.GOODS_LIST.GOODS_LIST_ITEM.SKU
    parent_path: Mapped[str | None] = mapped_column(sa.Text)

    # 应该是完整的路径，因为 scope 内的 Name 也可能重复，如 fileId
    redirect: Mapped[str | None] = mapped_column(sa.String(256))

    component_id: Mapped[str | None] = mapped_column(sa.String(128))
    render_config: Mapped[dict] = mapped_column(sa.JSON, default=dict)
    label: Mapped[str | None] = mapped_column(
        sa.String(128), Computed("json_unquote(json_extract(render_config, '$.label'))")
    )

    @property
    def fullname_in_scope(self):
        """在当前 scope 内的绝对路径"""
        if self.parent_path:
            return f"{self.parent_path}.{self.name}"
        else:
            return self.name

    @classmethod
    @in_transaction()
    def update_form_scope_symbol(cls, scope: FormSymbolScope, data: list[FormSymbolEditableView | Symbol]):
        """覆盖更新 scope 内的 symbol"""

        def clean():
            db.session.query(FormSymbol).filter(
                FormSymbol.form_id == scope.form_id,
                FormSymbol.step_uuid == scope.step_uuid,
                FormSymbol.step_id == scope.step_id,
            ).delete()

        def insert(
            editable_view: FormSymbolEditableView | Symbol,
            parent: FormSymbol | None = None,
        ):
            if isinstance(editable_view, Symbol):
                type_spec = serialize(editable_view.type_spec.to_deprecated())
                options = {}
            else:
                type_spec = editable_view.type_spec.dict()
                options = editable_view.options.dict()
            self = FormSymbol(
                form_id=scope.form_id,
                step_uuid=scope.step_uuid,
                step_id=scope.step_id,
                name=editable_view.name,
                type_spec=type_spec,
                options=options,
                component_id=editable_view.component_id,
                render_config=editable_view.render_config,
                parent=parent.name if parent else None,
                parent_path=parent.fullname_in_scope if parent else None,
                redirect=editable_view.redirect,
            )
            added.append(self)
            if editable_view.children:
                array_item_name = f"{self.name}_ITEM"
                for child in editable_view.children:
                    if editable_view.type_spec.type == "array":
                        child.name = array_item_name
                    insert(child, self)

        clean()
        added: list[FormSymbol] = []
        for each in data:
            insert(each)
        db.session.add_all(added)

    @classmethod
    def update_from_step_copy(cls, from_scope: FormSymbolScope, to_scope: FormSymbolScope):
        cls.query_symbol_table_editable_view_by_scope(from_scope).map(
            lambda views: cls.update_form_scope_symbol(to_scope, views)
        )

    @classmethod
    def query_by_form_step(cls, form_id: int, step_id: int):
        symbols = (
            db.session.query(FormSymbol).filter(FormSymbol.form_id == form_id, FormSymbol.step_id == step_id).all()
        )

        return Ok(cast(list[FormSymbol], symbols))

    @classmethod
    def query_by_form_step_list(cls, form_id: int, step_id_list: list[int]):
        symbols = (
            db.session.query(FormSymbol)
            .filter(FormSymbol.form_id == form_id, FormSymbol.step_id.in_(step_id_list))
            .all()
        )

        return Ok(cast(list[FormSymbol], symbols))

    @classmethod
    def query_symbol_table(cls, form_id: int, version: str):
        steps_result = Form.query_steps_by_version(form_id, version)
        if steps_result.is_err():
            return Err(steps_result.unwrap_err())
        steps = steps_result.unwrap()

        form_symbols_result = cls.query_by_form_step_list(form_id, [step.id for step in steps])
        if form_symbols_result.is_err():
            return Err(form_symbols_result.unwrap_err())
        form_symbols = form_symbols_result.unwrap()

        namespace_name_map: dict[str, SymbolTable.Namespace] = dict()
        for step in steps:
            namespace = SymbolTable.Namespace(name=step.step_uuid, label=step.name)
            unfiltered_namespace_symbols = list(filter(lambda x: x.step_id == step.id, form_symbols))
            if namespace_symbols := [
                SymbolTable.Symbol.from_form_symbol(each, unfiltered_namespace_symbols)
                for each in cls.util_iter_root_symbols(unfiltered_namespace_symbols)
            ]:
                namespace.symbols = namespace_symbols
            namespace_name_map[namespace.name] = namespace

        # 全部 namespace 构造完成后，再构造 scope 信息
        reserved_in_top_namespace = set(namespace_name_map.keys())
        for step in steps:
            if not step.parent:
                continue
            namespace = namespace_name_map[step.step_uuid]
            parent: str = step.parent
            if namespace.name in reserved_in_top_namespace:
                reserved_in_top_namespace.remove(namespace.name)
            namespace_name_map[parent].append_namespace(namespace)

        symbol_table = SymbolTable(form_id=form_id, version=version)
        if namespace_name_map:
            symbol_table.namespaces = [
                namespace for name, namespace in namespace_name_map.items() if name in reserved_in_top_namespace
            ]

        return Ok(symbol_table)

    @classmethod
    def get_namespace(cls, form_id: int, step_id: int):
        step = Step.query.with_entities(Step.id, Step.step_uuid, Step.name).filter_by(id=step_id).one()
        form_symbols_result = cls.query_by_form_step(form_id, step_id)
        if form_symbols_result.is_err():
            return Err(form_symbols_result.unwrap_err())
        form_symbols = form_symbols_result.unwrap()
        namespace = SymbolTable.Namespace(
            name=step.step_uuid,
            label=step.name,
            symbols=[
                SymbolTable.Symbol.from_form_symbol(form_symbol, form_symbols)
                for form_symbol in cls.util_iter_root_symbols(form_symbols)
            ],
        )
        return Ok(namespace)

    @classmethod
    def compatible_query_symbol_table(cls, form_id: int, version: str):
        from robot_processor.form.compatible_utils import editable_view_to_symbol
        from robot_processor.form.compatible_utils import need_compatible_processing_by_symbol_table
        from robot_processor.form.compatible_utils import widget_info_to_editable_view

        symbol_table_result = cls.query_symbol_table(form_id, version)
        if symbol_table_result.is_err():
            return Err(symbol_table_result.unwrap_err())
        symbol_table = symbol_table_result.unwrap()
        if not need_compatible_processing_by_symbol_table(symbol_table):
            return Ok(symbol_table)

        steps = Form.query_steps_by_version(form_id, version).unwrap()
        widget_collection_map = {
            step.step_uuid: step.widget_collection_id for step in steps if step.widget_collection_id
        }
        widget_info_list: list[WidgetInfo] = (
            WidgetInfo.query.filter(WidgetInfo.widget_collection_id.in_(list(widget_collection_map.values())))
            .filter(WidgetInfo.before.isnot(True))
            .all()
        )
        widget_map = dict((widget.id, widget) for widget in Widget.query)
        component_map = dict((component.id, component) for component in Component.query)

        def build_namespace_symbols(namespace_: SymbolTable.Namespace):
            widget_collection_id = widget_collection_map.get(namespace_.name, None)
            if widget_collection_id:
                namespace_.symbols = [
                    editable_view_to_symbol(widget_info_to_editable_view(widget_info, widget_map, component_map))
                    for widget_info in widget_info_list
                    if str(widget_info.widget_collection_id) == widget_collection_id
                ]
            if namespace_.namespaces:
                for sub_namespace in namespace_.namespaces:
                    build_namespace_symbols(sub_namespace)

        for namespace in symbol_table.namespaces:
            build_namespace_symbols(namespace)

        return Ok(symbol_table)

    @classmethod
    def query_symbol_table_editable_view(cls, form_id: int, version: str):
        steps_result = Form.query_steps_by_version(form_id, version)
        if steps_result.is_err():
            return Err(steps_result.unwrap_err())
        steps = steps_result.unwrap()
        try:
            steps = sort_steps(steps)  # type: ignore[type-var]
        except:  # noqa
            pass
        form_symbols_result = cls.query_by_form_step_list(form_id, [step.id for step in steps])
        if form_symbols_result.is_err():
            return Err(form_symbols_result.unwrap_err())
        form_symbols = form_symbols_result.unwrap()

        view = SymbolTableEditableView(form_id=form_id, version=version, steps=[])
        for step in steps:
            step_form_symbols = list(filter(lambda x: x.step_id == step.id, form_symbols))
            step_view = SymbolTableEditableView.Step(
                step_name=step.name,
                step_uuid=step.step_uuid,
                step_id=step.id,
            )
            if symbols := [
                FormSymbolEditableView.from_form_symbol(form_symbol, step_form_symbols)
                for form_symbol in cls.sort(cls.util_iter_root_symbols(step_form_symbols))
            ]:
                step_view.symbols = symbols
            view.steps.append(step_view)

        return Ok(view)

    @classmethod
    def compatible_query_symbol_table_editable_view(cls, form_id: int, version: str):
        from robot_processor.form.compatible_utils import need_compatible_processing_by_editable_view
        from robot_processor.form.compatible_utils import widget_info_to_editable_view

        editable_view_result = cls.query_symbol_table_editable_view(form_id, version)
        if editable_view_result.is_err():
            return Err(editable_view_result.unwrap_err())
        editable_view = editable_view_result.unwrap()
        if not need_compatible_processing_by_editable_view(editable_view):
            return Ok(editable_view)

        steps = Form.query_steps_by_version(form_id, version).unwrap()
        widget_collection_map = {
            step.step_uuid: step.widget_collection_id for step in steps if step.widget_collection_id
        }
        widget_info_list = (
            WidgetInfo.query.filter(WidgetInfo.widget_collection_id.in_(list(widget_collection_map.values())))
            .filter(WidgetInfo.before.isnot(True))
            .all()
        )
        widget_map = dict((widget.id, widget) for widget in Widget.query)
        component_map = dict((component.id, component) for component in Component.query)
        for step in editable_view.steps:
            wc_id = widget_collection_map.get(step.step_uuid)
            step_widget_info_list = list(filter(lambda x: str(x.widget_collection_id) == wc_id, widget_info_list))
            step.symbols = [
                widget_info_to_editable_view(widget_info, widget_map, component_map)
                for widget_info in sorted(step_widget_info_list, key=lambda x: x.order)
            ]

        return Ok(editable_view)

    @classmethod
    def query_symbol_table_editable_view_by_scope(cls, scope: FormSymbolScope):
        form_symbols = cls.query_by_form_step(scope.form_id, scope.step_id).unwrap()
        editable_views = [
            FormSymbolEditableView.from_form_symbol(symbol, form_symbols)
            for symbol in cls.util_iter_root_symbols(sorted(form_symbols, key=lambda x: x.id))
        ]

        return Ok(editable_views)

    @classmethod
    def compatible_query_symbol_table_editable_view_by_scope(cls, scope: FormSymbolScope):
        from robot_processor.form.compatible_utils import widget_info_to_editable_view

        editable_views = cls.query_symbol_table_editable_view_by_scope(scope).unwrap()
        if editable_views:
            return Ok(editable_views)

        computed_editable_views: list[FormSymbolEditableView] = []
        wc_id = db.session.execute(
            text("select widget_collection_id from step where id = :id"),
            {"id": scope.step_id},
        ).scalar()
        if wc_id is None or wc_id == "0":
            return Ok(computed_editable_views)
        widget_info_list = (
            db.session.query(WidgetInfo)
            .filter(WidgetInfo.widget_collection_id == wc_id)
            .filter(WidgetInfo.before.isnot(True))
            .order_by(WidgetInfo.order)
            .all()
        )
        widget_map = dict((widget.id, widget) for widget in Widget.query)
        component_map = dict((component.id, component) for component in Component.query)
        for widget_info in widget_info_list:
            symbol = widget_info_to_editable_view(widget_info, widget_map, component_map)
            computed_editable_views.append(symbol)

        return Ok(computed_editable_views)

    @staticmethod
    def sort(symbols: list["FormSymbol"]):
        return sorted(symbols, key=lambda x: x.id)

    @staticmethod
    def util_iter_root_symbols(symbols: list["FormSymbol"]):
        return cast(list[FormSymbol], list(filter(lambda x: x.parent is None, symbols)))

    @staticmethod
    def util_iter_symbol_children(symbol: "FormSymbol", sibling: list["FormSymbol"]):
        return cast(
            list[FormSymbol],
            list(filter(lambda x: x.parent_path == symbol.fullname_in_scope, sibling)),
        )

    @staticmethod
    def generate_iterate_begin_gateway_system_symbols(step_name: str):
        iterator = Symbol(
            name="iterator",
            label="遍历信息",
            type_spec=TypeSpec("collection", properties={"length": TypeSpec("number"), "index": TypeSpec("number")}),
            children=[
                Symbol(name="index", label="遍历索引", type_spec=TypeSpec("number")),
                Symbol(name="length", label="遍历长度", type_spec=TypeSpec("number")),
            ],
        )
        symbol = Symbol(
            name=step_name,
            label="[系统字段]",
            type_spec=TypeSpec(
                "collection",
                properties={
                    "iterator": iterator.type_spec,
                },
            ),
            children=[iterator],
        )
        return [symbol]


class Widget(DbBaseModel, BasicMixin):
    label: Mapped[str | None] = mapped_column(sa.String(64))
    category: Mapped[WidgetCategory] = mapped_column(
        sa.Enum(WidgetCategory), default=WidgetCategory.BASIC, comment="电商/基础"
    )
    schema: Mapped[dict] = mapped_column(sa.JSON, default=dict)  # 前端用来展示的规则
    options: Mapped[dict] = mapped_column(sa.JSON, default=dict)  # 前端该组件支持哪些配置，例如，是否必填，是否可编辑等
    code: Mapped[int] = mapped_column(sa.Integer, default=1)
    # 复合组件的存储schema
    widget_meta: Mapped[list] = mapped_column(sa.JSON, default=list)
    data_schema_template: Mapped[dict] = mapped_column(
        sa.JSON, default=dict, comment="数据的结构定义和验证规则"
    )  # 组件的默认配置模板
    order: Mapped[int] = mapped_column(sa.Integer, default=0, comment="组件的顺序")
    system_widget_data: Mapped[dict] = mapped_column(
        sa.JSON, default=dict, comment="系统字段用的预定义数据，例如组件的key"
    )

    class WidgetBrief(TypedDict):
        id: int
        category: int
        label: str
        type: str
        unique: bool
        widget_meta: list

    def brief(self) -> WidgetBrief:
        info = {
            "id": self.id,
            "category": self.category.value,
            "widget_meta": self.widget_meta or [],
        }
        info.update(self.schema)

        return info  # type: ignore[return-value]

    @classmethod
    def get(cls, widget_id):
        if not widget_id:
            return {}
        widget = cache.get(f"widget_{widget_id}")
        if not widget:
            widget_object = Widget.query.get(widget_id)
            widget = widget_object.brief() if widget_object else {}  # type: ignore[typeddict-item]
            # 缓存1天
            cache.set(f"widget_{widget_id}", widget, timeout=60 * 60 * 24)
        return widget

    def to_dict(self, nested=True):
        """
        处理mysql json 顺序的问题:
            mysql存储时会对json做优化处理，打乱顺序
        """
        info = super().to_dict()
        options = info["options"]
        sort_keys = options.pop("sort_keys", [])
        sort_options = {}
        for key in sort_keys:
            sort_options[key] = options.get(key)
        info["options"] = sort_options or options
        return info

    class Utils:
        @staticmethod
        def check_if_widget_type_select(widget_type: str):
            """下拉选择组件拆分为多个 widget"""
            return widget_type in [
                "select",
                "order-question",
                "radio-tile",
                "radio-dropdown",
                "select-tile",
                "select-dropdown",
            ]

        @staticmethod
        def check_if_widget_data_type_table(widget_info: dict):
            widget_data_type = widget_info.get("widget_type", widget_info.get("option_value", {}).get("widget_type"))

            return widget_data_type == "table"

        @staticmethod
        def get_brief_for_external(widget: "Widget"):
            return {
                "widget_id": widget.id,
                "key": widget.system_widget_data["key"],
                "before": None,
                "option_value": widget.system_widget_data.get("option_value", {}),
                "schema_value": None,
                "data_schema": widget.data_schema_template,
                "ref_config": None,
                "order": 0,
            }

    class Queries:
        @staticmethod
        def widget_type_map():
            return cast(
                Dict[WidgetId, WidgetType],
                dict(db.session.query(Widget.id, Widget.schema["type"]).all()),
            )

        @staticmethod
        def widget_meta_map():
            return cast(
                Dict[WidgetId, list],
                {
                    widget_id: widget_meta or []
                    for widget_id, widget_meta in db.session.query(Widget.id, Widget.widget_meta).all()
                },
            )

        @staticmethod
        def all_system_widgets_info() -> dict:
            """系统组件的相关信息是不会改变的，只有value不同"""
            system_widget = Widget.query.filter_by(category=WidgetCategory.SYSTEM).all()
            return {
                widget.system_widget_data["key"]: Widget.Utils.get_brief_for_external(widget)
                for widget in system_widget
            }

    class View:
        @omit_none_fields_before_validate("schema", "options", "widget_meta")
        class FormEditor(BaseModel):
            """在工单模板编辑页提供组件的信息"""

            class Config:
                orm_mode = True
                use_enum_values = True

            id: int = Field(description="组件id")
            label: str = Field(description="组件名称")
            category: WidgetCategory = Field(description="组件分类：基础组件；电商组件；增强组件")

            order: int = Field(default=0, descirption="组件的顺序，现在已经没有再使用")
            code: int = Field(default=1, description="用于区分产品功能的编号，现在已经没有再使用")

            schema_: dict = Field(alias="schema", default_factory=dict, description="用于前端渲染的配置")
            options: dict = Field(default_factory=dict, description="组件可配置选项的配置")

            widget_meta: list = Field(default_factory=list, description="用于报表表头的一些配置")
            data_schema_template: dict = Field(default_factory=dict, description="数据的结构定义和验证规则")

            def dict(self, **kwargs):
                kwargs.setdefault("by_alias", True)
                return super().dict(**kwargs)

            update_wrapper(dict, BaseModel.dict, ("__doc__", "__annotations__"), tuple())


class WidgetInfo(DbBaseModel, BasicMixin):
    key: Mapped[str] = mapped_column(sa.String(64), index=True, default=get_nonce)
    widget_id: Mapped[WidgetId | None] = mapped_column(sa.Integer, index=True)
    # 表示对应的widget中的options中的取值是什么
    option_value: Mapped[dict] = mapped_column(sa.JSON, default=dict)
    # 自定义组件（复合组件）的schema是动态生成的，是对基础组件schema的引用
    schema_value: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="schema 实例数据")
    # 保存引用组件的信息
    # [{ref_key: uuid, provider_label: xxx, provider_title: xxx}]
    ref_config: Mapped[list] = mapped_column(sa.JSON, default=list)
    # 由Widget.data_schema_template 生成的事例数据
    data_schema: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="数据的结构定义和验证规则")

    widget_collection_id: Mapped[int | None] = mapped_column(sa.Integer, sa.ForeignKey("widget_collection.id"))
    widget_collection: Mapped["WidgetCollection"] = relationship(lambda: WidgetCollection, back_populates="widget_info")

    before: Mapped[bool] = mapped_column(sa.Boolean, default=False)

    order: Mapped[int] = mapped_column(sa.Integer, default=0)

    deleted: Mapped[bool] = mapped_column(sa.Boolean, default=False)

    @property
    def label(self):
        return self.option_value.get("label")

    def to_dict(self):
        return {
            "widget_id": self.widget_id,
            "option_value": self.option_value,
            "schema_value": self.schema_value,
            "ref_config": self.ref_config,
            "data_schema": self.data_schema,
            "before": self.before,
            "order": self.order,
        }

    def clone(self):
        return WidgetInfo(
            key=self.key,
            widget_id=self.widget_id,
            option_value=self.option_value,
            schema_value=self.schema_value,
            ref_config=self.ref_config,
            data_schema=self.data_schema,
            widget_collection_id=self.widget_collection_id,
            before=self.before,
            order=self.order,
            deleted=self.deleted,
        )

    def brief(self, merge_widget=False):
        widget = {
            "widget_id": self.widget_id,
            "key": self.key,
            "before": self.before,
            "option_value": self.option_value,
            "schema_value": self.schema_value or {},
            "data_schema": self.data_schema or {},
            "ref_config": self.ref_config,
            "order": self.order,
        }
        widget.update(Widget.get(self.widget_id))
        # 使用 form symbol 来管理组件配置后，组件配置统一在 option_value 中，需要兼容历史配置信息
        # 未来 ref_config 会统一到组件的数据关联设计中，不再使用 ref_config 来维护
        inner_ref_config = widget["option_value"].get("ref_config")  # type: ignore[union-attr]
        if inner_ref_config and not widget["ref_config"]:
            widget["ref_config"] = inner_ref_config

        if merge_widget:
            if widget.get("type") == "table":
                option = cast(Dict[str, Any], widget.get("option_value"))
            else:
                option = cast(Dict[str, Any], widget.pop("option_value"))
            extra = dict(label=option.get("label", ""))
            if (mode := option.get("mode")) is not None:
                extra.update({"mode": mode})
            elif (upload_type := option.get("uploadType")) is not None:
                extra.update({"mode": upload_type})
            if isinstance(option.get("options"), list):
                extra.update({"options": option.get("options")})
            extra.update(
                {
                    key: extra_value
                    for key in ["multiInput", "required", "rateItems"]
                    if (extra_value := option.get(key)) is not None
                }
            )
            widget.update(extra)
        return widget

    def value_unique(self) -> bool:
        return self.option_value.get("valueUnique", False)

    @classmethod
    def find_by_collection_id(cls, widget_collection_id: list) -> List["WidgetInfo"]:
        return cls.query.filter(WidgetInfo.widget_collection_id.in_(widget_collection_id)).all()

    class Queries:
        __slots__ = ()

        @staticmethod
        def distinct_by_widget_collection_ids(
            widget_collection_ids: Union[List[int], Subquery],
        ) -> List["WidgetInfo"]:
            """根据 widget_collection_id 列表找到所有的 widget_info，去重后返回"""
            widget_info_subquery = (
                select(func.max(WidgetInfo.id).label("id"))
                .where(WidgetInfo.widget_collection_id.in_(widget_collection_ids))  # type: ignore[arg-type]
                .where(~WidgetInfo.before)
                .group_by(WidgetInfo.key)
                .subquery()
            )
            widget_info_stmt = (
                select(WidgetInfo)
                .select_from(WidgetInfo)
                .join(widget_info_subquery, widget_info_subquery.c.id == WidgetInfo.id)
            )
            widget_infos = db.session.execute(widget_info_stmt).scalars().all()

            return widget_infos  # type: ignore[return-value]

        @staticmethod
        def by_keys_published(keys: List[str]):
            """根据 key 找最新的配置（已发布的）"""

            key_filter_query = (
                select(func.max(WidgetInfo.id).label("id"))
                .select_from(WidgetInfo)
                .join(WidgetInfo.widget_collection)
                .where(WidgetInfo.key.in_(keys))
                .where(~WidgetCollection.is_dirty)
                .group_by(WidgetInfo.key)
                .subquery()
            )
            widget_info_stmt = (
                select(WidgetInfo)
                .select_from(WidgetInfo)
                .join(key_filter_query, key_filter_query.c.id == WidgetInfo.id)
            )
            widget_infos = db.session.execute(widget_info_stmt).scalars().all()

            return cast(List[WidgetInfo], widget_infos)

    class Schema:
        class DataSchemaField(BaseModel):
            name: str = Field(description="ref: WidgetInfo.key")
            title: str = Field(description="ref: WidgetInfo.label")
            type: str = Field(description="组件类型；如 table, string")
            # 部分组件会用到，如 type=date, format=%Y-%m-%d
            format: Optional[str] = Field(default=None)
            constraints: dict = Field(default_factory=dict)
            fields: Optional[List["WidgetInfo.Schema.DataSchemaField"]] = Field(default=None)

        class DataSchema(BaseModel):
            multi_row: bool = Field(default=False)
            fields: List["WidgetInfo.Schema.DataSchemaField"] = Field(default_factory=list)

        class ProviderConfig(BaseModel):
            """数据源配置"""

            ref_key: str
            provider_label: str
            provider_title: str

        class OptionValue(BaseModel):
            """组件的配置项-待补充完善，现在只在单元测试中使用"""

            label: str = Field(description="表单组件展示的名字")
            description: Optional[str] = Field(default=None, description="表单组件的描述")
            required: bool = Field(default=False, description="是否必填")
            defaultValue: Optional[str] = Field(default=None, description="默认值")
            # table 组件
            widget_type: Optional[WidgetDataType] = Field(
                default=None,
                description="Widget.schema.widget_type，table组件 type=table",
            )
            fields: Optional[List["WidgetInfo.Schema.OptionValue"]] = Field(
                default=None,
                description="table组件下有多个field,每个field schema也是一个与WidgetOptionValue相同的结构",
            )

        class OptionValueNumber(OptionValue):
            """数字输入"""

            unit: Optional[str] = Field(default=None, description="单位")
            decimalDigits: Optional[int] = Field(default=None, description="小数点位数")
            min: Optional[float] = Field(default=None, description="范围检查-最小值")
            max: Optional[float] = Field(default=None, description="范围检查-最大值")

        class OptionValueSelect(OptionValue):
            """选择器"""

            level: Optional[int] = Field(default=None, description="层级")
            options: Optional[List[dict]] = Field(default=None, description="选项")
            hasOtherOption: Optional[bool] = Field(default=None, description="是否有其他选项")
            layoutMode: Optional[Literal["tile", "dropdown"]] = Field(default=None, description="布局模式")

        class OptionValueDate(OptionValue):
            """日期"""

            mode: Optional[Literal["date", "datetime"]] = Field(default=None, description="日期类型")

        class OptionValueReissueProduct(OptionValue):
            """补发商品"""

            multiple: Optional[bool] = Field(default=None, description="是否多选")

        class OptionValueUpload(OptionValue):
            """上传"""

            uploadType: Optional[Literal["image", "video"]] = Field(default=None, description="上传类型")

        class ReportBrief(BaseModel):
            """在报表表头中的简要信息"""

            key: str
            label: str

            uploadType: Optional[str] = Field(default=None, description="前端要用这个判断是图片/视频组件")

            mode: Optional[str] = Field(default=None, description="一般用于下拉选择组件 single|multiple")
            options: Optional[list] = Field(default=None, description="下拉选择组件的选项")
            hasOtherOption: Optional[bool] = Field(default=None, description="下拉选择的其他选项配置")
            layoutMode: Optional[str] = Field(default=None, description="下拉选择组件 tile|dropdown")

            rateItems: Optional[list] = Field(default=None, description="评分组件的选项")

            @validator("options", pre=True)
            def ignore_placeholder(cls, v):
                if not isinstance(v, list):
                    v = None
                return v

            @classmethod
            def from_(cls, *, widget_info: "WidgetInfo"):
                obj = {**(widget_info.option_value or {})}
                obj.update(key=widget_info.key)

                return cls(**obj)

            def dict(self, **kwargs):
                kwargs.setdefault("exclude_none", True)
                return super().dict(**kwargs)

        class ReportWidgetMeta(BaseModel):
            """在明细报表中使用"""

            key: Union[str, List[str]]
            label: str
            value: str
            id: Optional[int] = Field(default=None)
            optionType: Optional[str] = Field(default=None, description="flower|star")
            widget_meta: Optional[List["WidgetInfo.Schema.ReportWidgetMeta"]] = Field(default=None)
            type: Optional[str] = Field(default=None)
            options: Optional[list] = Field(default=None)

        class WidgetRefAndLeafFromRootPathPair(BaseModel):
            """维护 WidgetRef 和 LeafFromRootPath 两个实例的对应关系"""

            pair: list[tuple["WidgetRef", "WidgetInfo.View.LeafFromRootPath"]]

            def get_leaf_from_root_path_by_widget_ref(self, widget_ref: "WidgetRef"):
                for ref, leaf_from_root_path in self.pair:
                    if ref == widget_ref:
                        return leaf_from_root_path
                else:
                    raise ValueError(f"引用组件未找到 {widget_ref.json(ensure_ascii=False)}")

            @classmethod
            def build_from_ui_schema(
                cls,
                ui_schema: List["WidgetInfo.View.RawStep"],
                reference_ui_schema: List["WidgetInfo.View.RawStep"],
            ):
                # 从工单实例所用到的所有 widget_info 中，将非引用前序的组件取出，置于 map 中。
                reference_ui_schema_map = {each.key: each for each in reference_ui_schema if each.before is False}
                self = cls(pair=[])
                for widget_info in ui_schema:
                    leaf_from_root_path_list = WidgetInfo.Utils.flatten_widget_info_tree_paths(widget_info)
                    for leaf_from_root_path in leaf_from_root_path_list:
                        leaf_widget_info = leaf_from_root_path.leaf
                        leaf_widget_ref = WidgetInfo.Utils.get_widget_info_reference(leaf_widget_info)
                        if not leaf_widget_ref:
                            continue
                        target_widget_info = reference_ui_schema_map[leaf_widget_ref.key]
                        target_leaf_path_list = WidgetInfo.Utils.flatten_widget_info_tree_paths(target_widget_info)
                        target_leaf_path = None
                        for target_leaf_path in target_leaf_path_list:
                            if target_leaf_path.as_widget_ref() == leaf_widget_ref:
                                break
                        else:
                            raise ValueError(f"{leaf_widget_ref} not in {target_leaf_path_list}")
                        if target_leaf_path is None:
                            raise ValueError(f"{leaf_widget_ref} not in {target_leaf_path_list}")
                        self.pair.append((leaf_widget_ref, target_leaf_path))
                return self

    class View:
        @omit_none_fields_before_validate("ref_config", "data_schema", "before")
        class RawStep(BaseModel):
            """在 Step.raw_step.ui_schema 中保存的表单组件信息"""

            id: Optional[int] = Field(default=None, description="这里是组件的 id")
            key: str = Field(description="组件 key")
            before: bool = Field(default=False, description="是否为引用组件")
            option_value: dict = Field(default_factory=dict, description="组件配置信息")
            data_schema: "WidgetInfoSchema.DataSchema" = Field(
                default_factory=lambda: WidgetInfoSchema.DataSchema(multi_row=False, fields=[])
            )
            ref_config: List["WidgetInfoSchema.ProviderConfig"] = Field(default_factory=list)
            order: int = Field(default=0, description="组件在表单内的顺序")
            # 下面的字段是来自 Widget
            type: str = Field(description="组件类型")
            widget_type: Optional[str] = Field(default=None, description="组件数据类型")

            @property
            def is_table(self):
                return self.widget_type == "table"

            @property
            def fields(self):
                if not self.is_table:
                    return []
                else:
                    return self.option_value.get("fields", [])

            @property
            def option_value_required(self):
                """是否为必填"""
                required = bool(self.option_value.get("required"))
                return required

            def to_brief_log(self):
                tmpl = "{label}@{key}"
                return tmpl.format(label=self.option_value.get("label"), key=self.key)

        class SingleWidgetMeta(BaseModel):
            key: list[str]
            value: str
            label: str
            type: str
            widget_meta: list["WidgetInfo.View.SingleWidgetMeta"] | None

        class ReportBase(BaseModel):

            key: Union[str, List[str]] = Field(
                description="表单组件的key,兼容系统字段和自定义字段"
            )  # 明细报表要用列表，其他地方用字符串
            label: str = Field(description="字段展示的名字")
            type: str = Field(default="widget", description="字段类型")
            widget_type: Optional[str] = Field(default=None, description="字段数据类型")
            default_in_customer: bool = Field(default=True, description="是否在 customer mode 下默认展示")
            widgets: Optional[List["WidgetInfo.Schema.ReportBrief"]] = Field(default=None)  # 非默认，选了模板名称才会有
            widget_meta: list["WidgetInfo.View.SingleWidgetMeta"] = Field(default_factory=list)
            merged: bool | None = Field(description="是否合并")

        class ReportDetailed(ReportBase):
            """明细报表"""

            uploadType: Optional[str] = Field(default=None, description="前端要用这个判断是图片/视频组件")

            mode: Optional[str] = Field(default=None, description="一般用于下拉选择组件 single|multiple")
            options: Optional[list] = Field(default=None, description="下拉选择组件的选项")
            hasOtherOption: Optional[bool] = Field(default=None, description="下拉选择的其他选项配置")
            layoutMode: Optional[str] = Field(default=None, description="下拉选择组件 tile|dropdown")

            rateItems: Optional[list] = Field(default=None, description="评分组件的选项")

            widget_meta: list["WidgetInfo.Schema.ReportWidgetMeta"] | None = Field(  # type:ignore[assignment]
                default=None
            )
            type_spec: Optional[TypeSpec] = Field(default=None)

            @validator("options", pre=True)
            def ignore_placeholder(cls, v):
                if not isinstance(v, list):
                    v = None
                return v

            def dict(self, **kwargs):
                kwargs.setdefault("exclude_none", True)
                return super().dict(**kwargs)

        class LeafFromRootPath(BaseModel):
            """
            将容器类型的组件作为一个树，维护每个叶子节点的从根节点到叶子节点的路径
            非容器类型的组件，可以看作是只有根节点的树，len(routes) === 1
            """

            routes: list["WidgetInfo.View.RawStep"]

            @property
            def root(self):
                return self.routes[0]

            @property
            def leaf(self):
                return self.routes[-1]

            @property
            def path(self):
                return self.routes[:-1]

            @property
            def depth(self):
                return len(self.routes)

            @property
            def is_single(self):
                return self.depth == 1

            def get_parent(self, widget_info_node: "WidgetInfo.View.RawStep"):
                parent = None
                for node in self.routes:
                    if node.key == widget_info_node.key:
                        return parent
                    parent = node
                else:
                    return None

            def is_leaf_parent(self, widget_info_node: "WidgetInfo.View.RawStep"):
                leaf_parent = self.get_parent(self.leaf)
                if leaf_parent is None:
                    return False
                return widget_info_node.key == leaf_parent.key

            def index(self, widget_info: "WidgetInfo.View.RawStep"):
                for index, each in enumerate(self.routes):
                    if each.key == widget_info.key:
                        return index
                else:
                    raise IndexError(f"{widget_info.to_brief_log()} not in {self.to_brief_log()}")

            def update_widget_info(
                self,
                widget_info: "WidgetInfo.View.RawStep",
                *,
                option_value: dict | None = None,
            ):
                widget_info_in_path = self.routes[self.index(widget_info)]
                if option_value is not None:
                    widget_info_in_path.option_value = option_value
                # 还要更新 parent.fields 中的配置信息
                while parent := self.get_parent(widget_info_in_path):
                    new_parent_fields = []
                    for subfield in parent.option_value["fields"]:
                        if subfield["key"] == widget_info_in_path.key:
                            subfield = widget_info_in_path.dict(exclude_unset=True)
                        new_parent_fields.append(subfield)
                    parent.option_value["fields"] = new_parent_fields
                    widget_info_in_path = parent

            def as_widget_ref(self):
                """从根节点到叶子节点的路径，使用 WidgetRef 表示"""
                root, routes = self.routes[0], self.routes[1:]
                widget_ref = WidgetRef(
                    key=root.key,
                    type=root.type,
                    widget_type=root.widget_type,
                    multi_row=root.data_schema.multi_row,
                    field=None,
                )
                # 从根节点开始，把路径上的节点都绑定到 field 上
                current_ref = widget_ref
                for leaf_or_non_leaf in routes:
                    current_ref.field = WidgetRef(
                        key=leaf_or_non_leaf.key,
                        type=leaf_or_non_leaf.type,
                        widget_type=leaf_or_non_leaf.widget_type,
                        multi_row=leaf_or_non_leaf.data_schema.multi_row,
                        field=None,
                    )
                    current_ref = current_ref.field

                return widget_ref

            def to_brief_log(self):
                key_list = [each.key for each in self.routes]
                tmpl = "'{readable} as {key_list}'"
                return tmpl.format(readable=self.readable, key_list=key_list)

            @property
            def label_list(self) -> list[str]:
                """各个层级用于展示的名字"""
                return [each.option_value["label"] for each in self.routes]

            @property
            def widget_type_list(self) -> list[str]:
                """各个层级的飞梭组件类型"""
                return [unwrap_optional(each.widget_type) for each in self.routes]

            @property
            def readable(self) -> str:
                return "/".join(self.label_list)

        class ReferredBrief(BaseModel):
            """在被引用时，提供一些简单的信息给前端绑定引用关系"""

            key: str
            id: int = Field(description="这里是组件的 id")

    class Utils:
        @staticmethod
        def option_level(options: list):
            flag = False
            children_count = []
            for option in options:
                if len(option.get("children", [])) > 0:
                    flag = True
                    children_count.append(WidgetInfo.Utils.option_level(option["children"]))
            if flag:
                return max(children_count) + 1
            return 1

        @staticmethod
        def widget_meta(
            widget_info: "WidgetInfo",
            widget_type_map: Optional[dict] = None,
            widget_meta_map: Optional[dict] = None,
            keys: Optional[List[str]] = None,  # 用于处理多个 widget_key
            add_mask_suffix: bool = False,  # 是否要求前端展示脱敏字段（工单列表）
            widget_info_mapping: dict[str, "WidgetInfo"] | None = None,
        ):
            """
            如果返回 None, 会在表头过滤这个组件
            这里处理表头的展开的状态，表头的展开主要有两种
                1.电商组件的展开，例如地址组件，包含 省/市/区
                2.下拉单选/多选的展开，按层级需要展开成uuid_1,uuid_2,uuid_3..
            """
            if widget_type_map is None:
                widget_type_map = Widget.Queries.widget_type_map()
            if widget_meta_map is None:
                widget_meta_map = Widget.Queries.widget_meta_map()
            widget_meta = copy.deepcopy(widget_meta_map.get(widget_info.widget_id, []))
            widget_type = widget_type_map.get(widget_info.widget_id, "string")

            if widget_type == "text":
                return None

            need_table_widget_meta = False
            if widget_type == "collection":
                need_table_widget_meta = True
            elif Widget.Utils.check_if_widget_data_type_table(widget_info.option_value):
                need_table_widget_meta = True
            try:
                if widget_type == "array" and widget_info.option_value["fields"][0]["type"] == "collection":
                    need_table_widget_meta = True
            except:  # noqa
                pass

            # 处理下拉单选/多选的展开
            if Widget.Utils.check_if_widget_type_select(widget_type):
                if options := widget_info.option_value.get("options"):
                    level = WidgetInfo.Utils.option_level(options)
                    for i in range(level):
                        if keys:
                            key = [f"{key}_{i+1}" for key in keys]
                        else:
                            key = [f"{widget_info.key}_{i+1}"]
                        widget_meta.append(
                            {
                                "key": key,
                                "options": widget_info.option_value.get("options"),
                                "type": widget_type,
                                "label": f"{widget_info.label}_层级{i + 1}",
                                "value": f"{widget_info.label}_层级{i + 1}",
                            }
                        )
                return widget_meta

            # 处理评分组件
            elif widget_type == "rate":
                widget_meta = []
                for item in widget_info.option_value.get("rateItems", []):
                    if item.get("id") is None:
                        continue
                    if keys:
                        key = [f"{key}_{item['id']}" for key in keys]
                    else:
                        key = [f"{widget_info.key}_{item['id']}"]
                    widget_meta.append(
                        {
                            "id": item["id"],
                            "optionType": item["optionType"],
                            "key": key,
                            "label": item["label"],
                            "value": item["label"],
                            "type": widget_type,
                        }
                    )
                return widget_meta

            # 组件上声明了 widget_meta，进行一下加工
            elif widget_meta:
                for item in widget_meta:
                    if add_mask_suffix and widget_type == "address":
                        # 地址组件子字段，需要添加-masked后缀
                        item["value"] = f"{item['value']}-masked"
                    if keys:
                        key = [f"{key}_{item['value']}" for key in keys]
                    else:
                        key = [f"{widget_info.key}_{item['value']}"]
                    item["key"] = key
                    item["options"] = widget_info.option_value.get("options")
                    item["type"] = item.get("type") or widget_type
                return widget_meta
            elif need_table_widget_meta:
                # table是复合组件，内部的组件需要展开，且需要保持层级的结构，需要递归来处理
                table_widget_meta = []
                filtered_widget_info_obj_list = WidgetInfo.Utils.filter_before_option_value([widget_info.brief()])
                # table 内所有的列都是引用，所以在表头应该过滤掉这个组件
                if not filtered_widget_info_obj_list:
                    return None
                option_value = filtered_widget_info_obj_list[0]["option_value"]
                if widget_type == "array":
                    fields = widget_info.option_value["fields"][0]["option_value"].get("fields", [])
                else:
                    fields = option_value.get("fields", [])
                for item in fields:
                    # item只是一个dict,需要转换成WidgetInfo对象
                    if item.get("option_value", {}).get("hidden"):
                        logger.info(f"组件内部的列被隐藏了，field:{item['key']}")
                        continue
                    widget = WidgetInfo(
                        key=item["key"],
                        widget_id=item["id"],
                        option_value=item.get("option_value", {}),
                        data_schema=item.get("data_schema", {}),
                    )
                    if keys:
                        key = [f"{key}_{widget.key}" for key in keys]
                        key_widget_info_mapping = {}
                        if widget_info_mapping:
                            for widget_key in keys:
                                wi = widget_info_mapping.get(widget_key)
                                if wi is None:
                                    continue
                                for widget_field in wi.option_value.get("fields", []):
                                    another_widget = WidgetInfo(
                                        key=widget_field["key"],
                                        widget_id=widget_field["id"],
                                        option_value=widget_field.get("option_value", {}),
                                        data_schema=widget_field.get("data_schema", {}),
                                    )
                                    label = another_widget.option_value.get("label") or ""
                                    if label == widget.option_value.get("label") and another_widget.key != widget.key:
                                        key.append(f"{widget_key}_{another_widget.key}")
                                        key_widget_info_mapping[f"{widget_key}_{another_widget.key}"] = another_widget
                    else:
                        key = [f"{widget_info.key}_{widget.key}"]
                        key_widget_info_mapping = {}
                    table_widget_meta.append(
                        {
                            "key": key,
                            "label": widget.label,
                            "value": widget.label,
                            "options": widget_info.option_value.get("options"),
                            "type": item.get("type"),
                            "widget_meta": WidgetInfo.Utils.widget_meta(
                                widget,
                                widget_type_map,
                                widget_meta_map,
                                key,
                                add_mask_suffix,
                                key_widget_info_mapping,
                            ),
                        }
                    )
                return table_widget_meta or widget_meta
            elif widget_type == "array":
                try:
                    item = widget_info.option_value["fields"][0]
                    return [
                        {
                            "key": [item["key"]],
                            "label": item["option_value"].get("label"),
                            "value": item["option_value"].get("label"),
                            "options": item["option_value"].get("options"),
                            "type": item["type"],
                            "widget_meta": WidgetInfo.Utils.widget_meta(
                                WidgetInfo(
                                    key=item["key"],
                                    widget_id=item["id"],
                                    option_value=item["option_value"],
                                    data_schema=item["data_schema"],
                                ),
                                widget_type_map,
                                widget_meta_map,
                                keys,
                                add_mask_suffix,
                                widget_info_mapping,
                            ),
                        }
                    ]
                except (KeyError, IndexError):
                    return widget_meta
            else:
                return widget_meta

        @staticmethod
        def form_type_spec_map(form_ids):
            form_versions = (
                db.session.query(FormVersion)
                .filter(
                    FormVersion.form_id.in_(form_ids),
                    FormVersion.version_descriptor == "HEAD",
                )
                .all()
            )
            step_ids = list(set([step_id for form_version in form_versions for step_id in form_version.step_id]))
            steps = db.session.query(Step).filter(Step.id.in_(step_ids)).with_entities(Step.id, Step.step_uuid).all()
            step_uuid_list = [step.step_uuid for step in steps]

            form_symbols = (
                FormSymbol.query.with_entities(FormSymbol.name, FormSymbol.type_spec, FormSymbol.parent_path)
                .filter(
                    # KEY `idx_form_symbol_ident` (`form_id`, `step_uuid`, `step_id`, `name`)
                    FormSymbol.form_id.in_(form_ids),
                    FormSymbol.step_uuid.in_(step_uuid_list),
                    FormSymbol.step_id.in_(step_ids),
                    FormSymbol.parent_path.is_(None),
                )
                .all()
            )
            return {form_symbol.name: form_symbol.type_spec for form_symbol in form_symbols}

        @staticmethod
        def type_spec(widget_info, widget_map, component_map, form_type_spec_map):
            """组件的 type spec 信息

            Args:
                widget_info (WidgetInfo)
                widget_map (dict[int, Widget])
                component_map (dict[str, Component])
                form_type_spec_map (dict[str, dict])
            """
            from robot_processor.form.compatible_utils import widget_info_to_editable_view

            return form_type_spec_map.get(
                widget_info.key,
                widget_info_to_editable_view(widget_info, widget_map, component_map).type_spec,
            )

        @staticmethod
        def get_widget_info_repr(widget: "WidgetInfoDict", value) -> str | list[str] | None:
            """
            唯一值风险上报时使用 todo 需要迁移到组件的行为中
            """
            from robot_processor.widget.payment_method_utils import get_payment_method_keys

            if widget["type"] == "payment-method":
                assert isinstance(value, dict), "数据格式错误"
                value = value.copy()
                if "pay_tid" in value:
                    value["tid"] = value["pay_tid"]  # 之前后端自己补了pay_tid但是与前端约定的是tid
                return "-".join(
                    map(
                        lambda k: str(value.get(k)),
                        filter(
                            lambda k: value.get(k),
                            get_payment_method_keys(value["payment_method"]),
                        ),
                    )
                )
            elif widget["type"] == "usernick":
                return value
            elif widget["type"] == "alipay":
                return value
            elif widget["type"] == "order":
                if isinstance(value, dict):
                    return f"{value.get('tid')}{value.get('oid', '')}"
                elif isinstance(value, list):
                    return [f"{ele.get('tid')}{ele.get('oid', '')}" for ele in value]
            elif widget["type"] == "string":
                return value
            elif widget["type"] == "textarea":
                return value
            elif widget["type"] == "number":
                return str(value)
            return None

        @staticmethod
        def extend_ui_schema(ui_schema: list, new_item: list):
            ui_schema.extend(filter(lambda item: not item.get("before"), new_item))

        @staticmethod
        def inplace_before_option_value(ui_schema: List[dict], prev_ui_schema: List[dict]):
            """根据前序步骤的 ui_schema，补充 before=True 的 option_value"""
            for widget_info in ui_schema:
                if not widget_info.get("before"):
                    continue
                origin_widget_info = first(
                    filter(
                        lambda item: item.get("key") == widget_info.get("key"),
                        prev_ui_schema,
                    ),
                    None,
                )
                if origin_widget_info:
                    # 合并 origin_widget_info 的 option_value
                    widget_info["option_value"] = {
                        **origin_widget_info["option_value"],
                        **widget_info["option_value"],
                    }
            return ui_schema

        @staticmethod
        def filter_before_option_value(ui_schema: list[dict]):
            """在展示输出信息时，不需要展示 before=True 的组件"""
            filtered_ui_schema: list[dict] = []
            for widget_info_obj in ui_schema:
                # 不展示引用组件
                if widget_info_obj.get("before") is True:
                    continue

                # table 组件要过滤叶子节点的引用，如果过滤后 table 组件没有子节点了，就不展示
                if "fields" in widget_info_obj["option_value"]:
                    filtered_fields = WidgetInfo.Utils.filter_before_option_value(
                        widget_info_obj["option_value"]["fields"]
                    )
                    # 没有叶子了，不展示
                    # 需要前端给所有组件添加 fields 后，可以取消 len > 0 的条件
                    if len(widget_info_obj["option_value"]["fields"]) > 0 and (len(filtered_fields) == 0):
                        continue
                    widget_info_obj["option_value"]["fields"] = filtered_fields

                filtered_ui_schema.append(widget_info_obj)

            return filtered_ui_schema

        @staticmethod
        def flatten_widget_info_tree_paths(
            widget_info: "WidgetInfo.View.RawStep",
            parents: list["WidgetInfo.View.RawStep"] | None = None,
        ):
            """树状结构的组件，将其按叶子节点展开成一维数组

            Examples:
                [ref](tests/form/widget_info/test_utils_flatten_widget_info_tree_paths.py)
            """
            parents = parents or []

            # 叶子节点，组装完整的 paths 并返回
            if widget_info.widget_type != "table":
                return [WidgetInfo.View.LeafFromRootPath(routes=[*parents, widget_info])]

            else:
                paths: list[WidgetInfo.View.LeafFromRootPath] = []
                for subfield in widget_info.fields:
                    typed_subfield = cast(
                        WidgetInfo.View.RawStep,
                        WidgetInfo.View.RawStep.parse_obj(subfield),
                    )
                    paths.extend(
                        WidgetInfo.Utils.flatten_widget_info_tree_paths(typed_subfield, [*parents, widget_info])
                    )

                return paths

        @staticmethod
        def get_widget_info_reference(widget_info: "WidgetInfo.View.RawStep"):
            """
            引用组件是通过相同的 `WidgetInfo`.`key` 来声明引用源组件的
            但是在复合组件中，被引用的组件的容器 `WidgetInfo`.`key` 是不同的，所以要用 `WidgetRef` 来记录

            如果存在 before_widget_ref，则说明这各组件使用了“绑定组件”的功能，需要将数据输出到它所绑定到的组件上。

            :param widget_info:
            :return:
            """
            if "before_widget_ref" in widget_info.option_value:
                return WidgetRef(**widget_info.option_value["before_widget_ref"])
            elif widget_info.before is not True:
                # 不是引用组件，无法处理
                return None
            else:
                return WidgetRef(
                    key=widget_info.key,
                    type=widget_info.type,
                    widget_type=widget_info.widget_type,
                    multi_row=None,
                    field=None,
                )

        @staticmethod
        def get_widget_info_data_binding(widget_info: "WidgetInfo.View.RawStep"):
            """获取数据绑定信息。如果存在，则返回数据绑定信息，否则返回 None。

            Table 组件作为容器，可能不会维护数据绑定信息，就需要从它的叶子节点里获取
            再配合 `get_data_binding_expression` 根据 level 信息获取 Table 对应的数据绑定表达式
            """
            from robot_processor.form.schemas import RpaDataBinding

            if "data_binding" in widget_info.option_value:
                return RpaDataBinding(**widget_info.option_value["data_binding"])
            elif widget_info.is_table:
                subfields = [WidgetInfo.View.RawStep(**subfield) for subfield in widget_info.option_value["fields"]]
                for subfield in subfields:
                    data_binding = WidgetInfo.Utils.get_widget_info_data_binding(subfield)
                    if data_binding is not None:
                        return data_binding
                else:
                    return None
            else:
                return None

        @staticmethod
        def get_data_binding_expression(
            leaf_from_root_path: "WidgetInfo.View.LeafFromRootPath",
            widget_info: "WidgetInfo.View.RawStep",
        ):
            """根据当前组件的层级关系，获取数据绑定表达式

            因为进行数据绑定时，是按照组件层级关系来填充数据的，所以仅需要当前层级的相对表达式即可
            对于叶子节点，就是获取当前层级到表达式结束的完整信息
            对于 Table 节点，就是仅获取当前层级表达式
            """
            from robot_processor.form.schemas import RpaDataBinding

            leaf = leaf_from_root_path.leaf
            # 显式定义了 data_binding, 直接使用
            if "data_binding" in widget_info.option_value:
                data_binding = RpaDataBinding(**widget_info.option_value["data_binding"])
            # 没有显式定义 data_binding, 但是可以从叶子节点里
            elif "data_binding" in leaf.option_value:
                data_binding = RpaDataBinding(**leaf.option_value["data_binding"])
            else:
                return None

            full_expression_parts = data_binding.full_expression_parts
            last_non_list_index = data_binding.last_non_list_index

            if widget_info.is_table:
                relative_level = leaf_from_root_path.index(widget_info)
                # 有可能复合组件和数据的层级是不一致的，需要在距离叶子节点最近的一层将数据打平
                if leaf_from_root_path.is_leaf_parent(widget_info):
                    relative_expression_parts = full_expression_parts[relative_level:last_non_list_index]
                else:
                    relative_expression_parts = full_expression_parts[relative_level : relative_level + 1]
            else:
                relative_expression_parts = full_expression_parts[last_non_list_index:]
            relative_expression = data_binding.EXPRESSION_SEPARATOR.join(relative_expression_parts)

            return relative_expression

        @staticmethod
        def fill_data_by_data_binding_expression(
            data_container: dict,
            widget_info: "WidgetInfo.View.RawStep",
            data_source: dict,
            expression: str,
        ):
            key = widget_info.key
            value = jmespath_search(expression, data_source)
            if old_value := data_container.get(key):
                logger.warning(f"data_container[{key}] is not empty, old_value: {old_value}, new_value: {value}")
            data_container[key] = value

            is_empty_value = (value is None) or (value == "")
            if widget_info.option_value_required and is_empty_value:
                return Err(value)
            return Ok(value)

        @staticmethod
        def flatten_data_container_and_source(
            leaf_from_root_path: "WidgetInfo.View.LeafFromRootPath",
            data_container: dict,
            data_source: dict,
            debug_info: list[dict] | None = None,
            cursor: int | None = None,
        ):
            """将数据源和数据容器展开成一维数组"""
            debug_info = debug_info or []
            res = namedtuple("res", ["data_container", "data_source", "debug_info"])
            result: list[res] = []

            if leaf_from_root_path.is_single:
                result.append(
                    res(
                        data_container=data_container,
                        data_source=data_source,
                        debug_info=debug_info,
                    )
                )
                return result

            if cursor is None:
                cursor = 0
            # walk down through non-leaf nodes
            non_leaf_cursor_list = list(range(len(leaf_from_root_path.path)))
            has_done = cursor == non_leaf_cursor_list[-1]

            widget_info = leaf_from_root_path.routes[cursor]
            key = widget_info.key
            expression = WidgetInfo.Utils.get_data_binding_expression(leaf_from_root_path, widget_info)
            rows = jmespath_search(expression, data_source)
            if (key not in data_container) or (data_container[key] is None):
                data_container[key] = []

            if rows is None:
                logger.error(f"jmespath search none, {expression=}, {data_source=}")
                rows = []
            container_length_diff = len(rows) - len(data_container[key])
            if container_length_diff > 0:
                data_container[key].extend([{} for _ in range(container_length_diff)])

            for index, (sub_data_container, sub_data_source) in enumerate(zip(data_container[key], rows)):
                if has_done:
                    result.append(
                        res(
                            data_container=sub_data_container,
                            data_source=sub_data_source,
                            debug_info=[*debug_info, dict(key=key, index=index)],
                        )
                    )
                else:
                    result.extend(
                        WidgetInfo.Utils.flatten_data_container_and_source(
                            leaf_from_root_path,
                            sub_data_container,
                            sub_data_source,
                            [*debug_info, dict(key=key, index=index)],
                            cursor + 1,
                        )
                    )

            return result

        @staticmethod
        def fill_widget_info_by_data_binding(
            widget_info: "WidgetInfo.View.RawStep",
            data_container: dict,
            datasource: dict,
            widget_ref_pair: "WidgetInfo.Schema.WidgetRefAndLeafFromRootPathPair",
        ):
            missing: list[WidgetInfo.View.LeafFromRootPath] = []
            leaf_from_root_path_list = WidgetInfo.Utils.flatten_widget_info_tree_paths(widget_info)
            for leaf_from_root_path in leaf_from_root_path_list:
                leaf_widget_info = leaf_from_root_path.leaf
                # 看一下是否是引用组件，如果是引用组件，需要先找到引用源组件
                leaf_widget_ref = WidgetInfo.Utils.get_widget_info_reference(leaf_widget_info)
                if leaf_widget_ref is not None:
                    old_leaf_widget_ref = leaf_from_root_path
                    leaf_from_root_path = widget_ref_pair.get_leaf_from_root_path_by_widget_ref(leaf_widget_ref)
                    # 根据 leaf_widget_ref 更新一下 leaf_from_root_path
                    for index, (new, old) in enumerate(zip(leaf_from_root_path.routes, old_leaf_widget_ref.routes)):
                        if data_binding := old.option_value.get("data_binding"):
                            new.option_value["data_binding"] = data_binding
                            leaf_from_root_path.update_widget_info(new, option_value=new.option_value)
                    leaf_widget_info = leaf_from_root_path.leaf

                expression = WidgetInfo.Utils.get_data_binding_expression(leaf_from_root_path, leaf_widget_info)
                # 非数据绑定组件，无需处理
                if expression is None:
                    continue
                # 数据绑定组件，但是没有数据绑定表达式，需要报错
                if expression == "":
                    logger.error(
                        "expression is empty, "
                        f"{leaf_widget_info.to_brief_log()}, "
                        f"{[node.to_brief_log() for node in leaf_from_root_path.routes]}"
                    )
                container_source_pair = WidgetInfo.Utils.flatten_data_container_and_source(
                    leaf_from_root_path, data_container, datasource
                )
                for container_source_info in container_source_pair:
                    write_res = WidgetInfo.Utils.fill_data_by_data_binding_expression(
                        container_source_info.data_container,
                        leaf_widget_info,
                        container_source_info.data_source,
                        expression,
                    )
                    if write_res.is_err():
                        missing.append(leaf_from_root_path)
            return missing


class WidgetRef(BaseModel):
    """在工单实例中引用的组件"""

    key: str = Field(description="组件的 key")

    type: str = Field(default="unknown", description="是简单组件还是复合组件")
    # fixme 这里不允许为 None，需要在补充了所有组件的 widget_type 后，进行数据清洗
    widget_type: Optional[str] = Field(default=None, description="组件的数据类型")
    multi_row: Optional[bool] = Field(default=None, description="是否允许多行")
    field: Optional[Union[str, "WidgetRef"]] = Field(
        default=None, description="如果是 table 组件，则表示引用其中某一列"
    )

    @root_validator(skip_on_failure=True)
    def check_constraints(cls, values):
        # 推测 widget_type
        if values["widget_type"]:  # widget_type 已经提供
            pass
        # 前端会有脏数据，即使不是 table 组件，也会把 key 塞到 field 中
        elif values["field"] is not None and values["key"] != values["field"]:
            values["widget_type"] = "table"
        elif values["type"] == "table":
            values["widget_type"] = "table"

        # table 组件需要声明 multi_row 属性
        if values["widget_type"] == "table":
            assert values["multi_row"] is not None, "table 组件的 multi_row 不允许为 None"

        if values["key"] == values["field"]:
            values["field"] = None

        return values

    @property
    def is_table(self):
        return self.widget_type == "table"

    @property
    def is_iterable(self):
        return self.widget_type == "table" or self.type == "array"

    @property
    def raw_query(self):
        """
        因为 multi_row 和 field 字段会影响 Table 的查询条件
        在一些场合需要获取 table 的完整信息，所以提供了这个快捷方式
        来帮助 query table 的完整信息
        """
        assert self.is_table
        return WidgetRef(key=self.key, type="table", widget_type="table", multi_row=True)

    @classmethod
    def parse(cls, widget_ref_obj, *, force_parse_str: bool = False):
        """
        注意，这里直接传入 widget key 时，一律当作 single 类型的组件处理
        Parameters:
            widget_ref_obj: widget key 或 widget ref
            force_parse_str: (bool) 是否强制转换 string 到 widget ref 类型
        """
        if widget_ref_obj is None:
            return None

        # 传入 widget key 时，当作 single 类型的组件处理
        if isinstance(widget_ref_obj, str):
            if force_parse_str:
                return WidgetRef(key=widget_ref_obj, type="unknown")
            else:  # 对 widget key 不做转换处理
                return widget_ref_obj

        if isinstance(widget_ref_obj, WidgetRef):
            return widget_ref_obj

        if not isinstance(widget_ref_obj, dict):
            return widget_ref_obj

        # 有可能是 task_argument，但是统一走了 parse 方法
        try:
            return WidgetRef.parse_obj(widget_ref_obj)
        except ValidationError:
            return widget_ref_obj

    def get_key_by_level(self, level: int):
        """从根结点开始，获取指定层级的 widget info key"""
        target_widget_info: str | WidgetRef | None = self
        for _ in range(level):
            if not isinstance(target_widget_info, WidgetRef):
                return None
            target_widget_info = target_widget_info.field

        if isinstance(target_widget_info, WidgetRef):
            return target_widget_info.key
        elif isinstance(target_widget_info, str):
            return target_widget_info
        else:
            return None

    def is_array_item_by_level(self, level: int):
        """检查指定层级的 widget_info 是不是 array item"""
        if level == 0:
            return False
        target_widget_info: str | WidgetRef | None = self
        parent_widget_info: WidgetRef = self
        for cur in range(level):
            if not isinstance(target_widget_info, WidgetRef):
                return False
            parent_widget_info = target_widget_info
            target_widget_info = target_widget_info.field
        if isinstance(target_widget_info, WidgetRef):
            return parent_widget_info.type == "array" and target_widget_info.key.endswith("_ITEM")
        elif isinstance(target_widget_info, str):
            return parent_widget_info.type == "array" and target_widget_info.endswith("_ITEM")
        else:
            return False

    @property
    def depth(self):
        """获取组件的深度"""
        current_ref = self
        depth = 1
        while current_ref and current_ref.field:
            if isinstance(current_ref.field, str):
                current_ref = WidgetRef(key=current_ref.field, type="unknown")
            else:
                current_ref = current_ref.field
            depth += 1
        return depth

    def __eq__(self, other):
        try:
            other = WidgetRef.parse(other, force_parse_str=True)
        except ValidationError:
            return False
        if other is None:
            return False

        # 对两个 WidgetRef 进行比较
        if self.key != other.key:
            return False
        # 如果两个 WidgetRef 都有 field, 才将 field 加入比较
        if all(map(lambda widget_ref: widget_ref.field is not None, [self, other])):
            return self.field == other.field
        # 否则只比较 key 是否相同
        else:
            return True

    def to_value(self, form_composer: FormComposer):
        from robot_types.core import Value
        from robot_types.core import Var
        from robot_types.core.value import PathIndicator

        symbol_table = form_composer.symbol_table_wrapper
        symbol = symbol_table.lookup(self.key)
        if not symbol:
            return None

        path_indicators = [PathIndicator(symbol.name, symbol.type_spec.type == "array", False)]
        current_widget_ref = self.field
        while True:
            match current_widget_ref:
                case None:
                    break
                case str():
                    path_indicators.append(PathIndicator(current_widget_ref, False, False))
                    break
                case WidgetRef():
                    path_indicators.append(
                        PathIndicator(
                            current_widget_ref.key,
                            current_widget_ref.is_iterable,
                            False,
                        )
                    )
                    current_widget_ref = current_widget_ref.field
        var = Var.init_by_indicators(path_indicators)
        value = Value(
            var.resolve_type_spec(symbol_table, symbol_table.current_scope),
            var=Var.init_by_indicators(path_indicators),
        )
        return value


class WidgetCollection(DbBaseModel, BasicMixin):
    # 一个表单的schema
    uuid: Mapped[str] = mapped_column(sa.String(32), default=get_nonce)

    widget_info: DynamicMapped["WidgetInfo"] = relationship(
        "WidgetInfo",
        cascade="all,delete-orphan",
        lazy="dynamic",
        order_by="WidgetInfo.order.asc()",
        back_populates="widget_collection",
    )
    widget_info_eager: Mapped[List[WidgetInfo]] = relationship(WidgetInfo, viewonly=True)
    deleted: Mapped[bool] = mapped_column(sa.Boolean, default=False)
    # true: 未发布，false: 已发布
    is_dirty: Mapped[bool] = mapped_column(sa.Boolean, nullable=False, default=True, comment="是否已发布")

    # example:
    # {"transfer_rule": [{"to_widget_key": "605cf2bb-c826-43d9-80ce-f9426a41bdaf",
    #                     "from_widget_key": "9dd2414f-45da-4982-a038-a6ba5a87b022"}],
    #  "deleted_widget_key": ["cf481eb5-5c90-4167-9628-260ab58598ec", "dc1fab51-7b45-4df4-bdfa-d0630a4cac85",
    #                         "9dd2414f-45da-4982-a038-a6ba5a87b022", "605cf2bb-c826-43d9-80ce-f9426a41bdaf"]}

    class TransferRule(TypedDict):
        """组件数据迁移记录
        将 `from_widget_key` 的数据迁移到 `to_widget_key`
        """

        from_widget_key: str
        to_widget_key: str

    class Changelog(TypedDict, total=False):
        """表单变更记录"""

        deleted_widget_key: list  # 删除的组件key
        transfer_rule: list["WidgetCollection.TransferRule"]  # 组件key转换规则
        history_version_deleted_widget_key: list
        reserved_keys: list

    changelog: Mapped[Changelog] = mapped_column(sa.JSON, default=dict, comment="表单变更日志")

    class Widget(TypedDict, total=False):
        id: int
        category: int
        label: str
        type: str
        unique: bool
        key: str
        options: Dict
        widget_meta: list

    def widgets_info(self, brief=False) -> list[Widget]:
        widgets = []
        for widget in self.widget_info:
            if not widget.widget_id:
                logger.warning(f"widget not found: collection@{self.id}")
                continue
            widgets.append(widget.brief(merge_widget=brief))
        return widgets

    @classmethod
    def get_widget_by_key(cls, form_id, key, is_dirty=False):
        query = (
            WidgetInfo.query.join(Step, Step.widget_collection_id == WidgetInfo.widget_collection_id)
            .filter(Step.form_id == form_id, WidgetInfo.key == key)
            .order_by(WidgetInfo.widget_collection_id.desc())
        )

        if not is_dirty:
            widget_info = query.first()
            return [widget_info.widget_collection] if widget_info else []
        return [widget.widget_collection for widget in query]

    def get_widget(self, key: str):
        widget = self.widget_info.filter(WidgetInfo.key == key).first()
        return widget.brief(merge_widget=False) if widget else {}

    @classmethod
    def new(cls, ui_schema):
        widget_collection = cls()
        widgets = [WidgetInfo(**widget) for widget in ui_schema]
        db.session.add_all(widgets)
        widget_collection.widget_info = widgets
        return widget_collection

    @classmethod
    def create_with_widget_info_list(cls, widget_info_list: List[WidgetInfo], transfer_rule=None) -> "WidgetCollection":
        widget_collection = cls()
        widget_collection.widget_info = widget_info_list
        if transfer_rule:
            widget_collection.changelog = {
                "transfer_rule": cls.normalize_transfer_rule(transfer_rule),
                "deleted_widget_key": [],
                "history_version_deleted_widget_key": [],
            }
        db.session.add(widget_collection)
        db.session.flush()
        return widget_collection

    @classmethod
    def create_or_update_widget_info_for_system_widget(cls):
        widget_collection = cls()
        system_widgets_info = [
            WidgetInfo(
                key=widget.system_widget_data.get("key"),
                widget_id=widget.id,
                option_value=widget.system_widget_data.get("option_value", {}),
                data_schema=widget.data_schema_template,
            )
            for widget in Widget.query.filter_by(category=WidgetCategory.SYSTEM).all()
        ]
        widget_collection.widget_info = system_widgets_info
        db.session.add(widget_collection)
        return widget_collection

    def update_with_widget_info_list(
        self, widget_info_list: List[WidgetInfo], transfer_rule=None
    ) -> "WidgetCollection":
        if self.is_dirty:
            self.update_inline(widget_info_list, transfer_rule)
            logger.info(f"已更新 widget collection {self.id}")
            return self
        else:
            new_widget_collection = self.fork_and_update(widget_info_list, transfer_rule)
            logger.info(f"已基于 {self.id} 创建 widget collection {new_widget_collection.id}")
            return new_widget_collection

    @classmethod
    def normalize_transfer_rule(cls, transfer_rule):
        # 前端代码(https://git.leyantech.com/fed/work-order-web) 中使用了 origin_widget_key 和 target_widget_key
        # 但是后端代码中使用了 from_widget_key 和 to_widget_key，所以这里需要做一下转换
        transfer_rule = transfer_rule or []
        result = []
        for rule in transfer_rule:
            from_widget_key = rule.get("from_widget_key", rule.get("origin_widget_key"))
            to_widget_key = rule.get("to_widget_key", rule.get("target_widget_key"))
            result.append({"from_widget_key": from_widget_key, "to_widget_key": to_widget_key})
        return result

    def get_prev_version(self) -> Optional["WidgetCollection"]:
        return (
            WidgetCollection.query.filter(WidgetCollection.uuid == self.uuid, WidgetCollection.id < self.id)
            .order_by(WidgetCollection.id.desc())
            .first()
        )

    def update_inline(self, widget_info_list: List[WidgetInfo], transfer_rule=None) -> "WidgetCollection":
        assert self.is_dirty
        old_widget_keys = set(widget.key for widget in self.widget_info)
        new_widget_keys = set(widget.key for widget in widget_info_list)
        # 找出当前版本的 widget_infos 比需要新建的 widget_infos 多出的部分。
        cur_version_difference = old_widget_keys.difference(new_widget_keys)

        if prev_version := self.get_prev_version():
            prev_version_widget_keys = set(widget.key for widget in prev_version.widget_info)
            # 再找出前一版本的 widget_infos 比需要新建的 widget_infos 多出来的部分。
            prev_version_difference = prev_version_widget_keys.difference(new_widget_keys)
        else:
            prev_version_difference = set()

        # 将这些多出来的 widget_infos 存放到 deleted_widget_key 中，在步骤更新的时候，将这些 widget_info 删除。
        deleted_widget_keys = cur_version_difference.union(prev_version_difference)
        changelog = copy.deepcopy(self.changelog or {})
        changelog.setdefault("transfer_rule", []).extend(self.normalize_transfer_rule(transfer_rule))
        changelog.setdefault("deleted_widget_key", [])
        changelog["deleted_widget_key"] = list(set(changelog["deleted_widget_key"]).union(deleted_widget_keys))

        self.changelog = changelog
        self.widget_info = widget_info_list
        db.session.add(self)
        db.session.flush()
        return self

    def fork_and_update(self, widget_info_list: List[WidgetInfo], transfer_rule=None) -> "WidgetCollection":
        assert not self.is_dirty
        transfer_rule = self.normalize_transfer_rule(transfer_rule)
        old_widget_keys = set(widget.key for widget in self.widget_info)
        new_widget_keys = set(widget.key for widget in widget_info_list)
        deleted_widget_keys = list(old_widget_keys.difference(new_widget_keys))
        widget_collection = WidgetCollection()
        widget_collection.uuid = self.uuid
        widget_collection.changelog = {
            "transfer_rule": transfer_rule,
            "deleted_widget_key": deleted_widget_keys,
            "history_version_deleted_widget_key": [],
        }
        widget_collection.widget_info = widget_info_list
        db.session.add(widget_collection)
        db.session.flush()
        return widget_collection

    @in_transaction()
    @no_auto_flush()
    def delete(self):
        """标记为删除状态"""
        widget_collection_ids = (
            db.session.execute(select(WidgetCollection.id).where(WidgetCollection.uuid == self.uuid)).scalars().all()
        )
        WidgetCollection.query.filter(WidgetCollection.id.in_(widget_collection_ids)).update(
            {WidgetCollection.deleted: True}
        )
        WidgetInfo.query.filter(WidgetInfo.widget_collection_id.in_(widget_collection_ids)).update(
            {WidgetInfo.deleted: True}
        )

    @in_transaction()
    @no_auto_flush()
    def remove_refer_widget_info(self, key_to_remove: List[str]):
        """删除引用了 key_to_remove 中的 widget_info"""
        widget_info_list = [
            widget_info
            for widget_info in self.widget_info.all()
            if widget_info.before is False or widget_info.key not in key_to_remove
        ]

        if self.is_dirty:
            # 草稿箱状态，可以直接改
            for new_order, widget_info in enumerate(widget_info_list):
                widget_info.order = new_order
            self.widget_info = widget_info_list
            widget_collection = self
        else:
            # 已发布状态，需要新建一个
            widget_collection = WidgetCollection(uuid=self.uuid, deleted=self.deleted)
            db.session.add(widget_collection)
            new_widget_info_list = []
            for new_order, widget_info in enumerate(widget_info_list):
                widget_info = widget_info.clone()
                widget_info.order = new_order
                new_widget_info_list.append(widget_info)
            widget_collection.widget_info = new_widget_info_list

        return widget_collection

    class Queries:
        @staticmethod
        def widget_collection_ids_by_form(form_ids: List[int]):
            return (
                select(WidgetCollection.id)
                .select_from(FormShop)
                .join(Form.Options.from_form_shop)
                .join(Form.steps)
                .join(Step.widget_collection)
                .where(~WidgetCollection.deleted)
                .where(FormShop.form_id.in_(form_ids))
            )

    class Utils:
        @staticmethod
        def merge_changelog_transfer_rule(widget_collections: List["WidgetCollection"]):
            """将历史版本的组件迁移规则合并，生成最终的组件迁移规则

            Examples:
                >>> widget_collections = [
                ...     WidgetCollection(changelog={'transfer_rule': [{'from_widget_key': '1', 'to_widget_key': '2'}]}),
                ...     WidgetCollection(changelog={'transfer_rule': [{'from_widget_key': '2', 'to_widget_key': '3'}]}),
                ...     WidgetCollection(changelog={'transfer_rule': [{'from_widget_key': '3', 'to_widget_key': '4'}]}),
                ... ]
                >>> WidgetCollection.Utils.merge_changelog_transfer_rule(widget_collections)
                {'1': '4', '2': '4', '3': '4'}
            """
            merged_transfer_rule: dict[str, str] = {}
            for widget_collection in widget_collections:
                if not widget_collection.changelog:
                    continue
                transfer_rule = {
                    rule["from_widget_key"]: rule["to_widget_key"]
                    for rule in widget_collection.changelog.get("transfer_rule", [])
                }
                # 先更新 merged transfer rule
                merged_transfer_rule = {
                    from_: transfer_rule.get(to_, to_)
                    for from_, to_ in merged_transfer_rule.items()  # 之前迁移过的组件又发生了迁移
                }
                merged_transfer_rule.update(transfer_rule)

            return merged_transfer_rule

        @staticmethod
        def reverse_changelog_transfer_rule(
            transfer_rule: Dict[str, str],
        ) -> Dict[str, Set[str]]:
            reversed_transfer_rule = defaultdict(set)
            for from_, to_ in transfer_rule.items():
                reversed_transfer_rule[to_].add(from_)

            return reversed_transfer_rule


class WidgetAutoNumber(DbBaseModel, BasicMixin):
    widget_key: Mapped[str] = mapped_column(sa.String(64), index=True, nullable=False)
    current_number: Mapped[int] = mapped_column(sa.Integer, nullable=False, default=1)

    @classmethod
    def get_next(cls, widget: WidgetInfoDict) -> str:
        """
        获取到自动编号的编号字符串。

        :param widget:
        :return:
        """

        config = widget.get("option_value", {})
        if config == {}:
            logger.warning("option value is empty! order is {}.".format(widget.get("order")))

        str_list = []

        # 获取前缀。
        prefix = config.get("prefix", "")
        str_list.append(str(prefix))

        # 获取格式化时间。
        date_format = config.get("dateFormat", "")
        str_list.append(cls.get_format_date(cls.get_date_formate_map().get(date_format, "")))

        # 添加行锁，防止数据冲突。
        widget_auto_number = cls.query.filter_by(widget_key=widget.get("key")).with_for_update().first()
        if not widget_auto_number:
            widget_auto_number = cls()
            widget_auto_number.widget_key = widget.get("key")  # type: ignore[assignment]
            widget_auto_number.current_number = 0

            db.session.add(widget_auto_number)

        widget_auto_number.current_number += 1
        number = widget_auto_number.current_number

        # 获取数字的填充位数。
        extent = config.get("extent", 0)
        str_list.append(str(number).zfill(extent))

        return "".join(str_list)

    @classmethod
    def handle_widget_auto_number(cls, business_order) -> None:
        """
        处理自动编号。

        :param business_order:
        :return:
        """
        auto_number_widget_id = WidgetAutoNumber.get_widget_id()
        if auto_number_widget_id:
            widgets = business_order.get_non_ref_widgets()
            for widget_key, widget in widgets.items():
                if widget.get("widget_id") != auto_number_widget_id:
                    continue
                # 当该组件的值为 "等待系统自动生成"，则进行编号自增。
                if business_order.data.get(widget_key) == "等待系统自动生成":
                    number = WidgetAutoNumber.get_next(widget)
                    business_order_data = (business_order.data or {}).copy()
                    business_order_data.update({widget.get("key"): number})
                    business_order.data = business_order_data
                    logger.info(f"auto number widget key: {widget_key}, current_number: {number}")

    @staticmethod
    def get_format_date(date_format: str) -> str:
        """
        根据传递的时间格式，将当前时间进行格式化，并返回。

        :param date_format:
        :return:
        """
        return time.strftime(date_format)

    @staticmethod
    def get_date_formate_map() -> Dict[str, str]:
        """
        获取确认可用的时间格式类型映射。

        :return:
        """
        return {
            "YYYYMMDD": "%Y%m%d",
            "YYYY-MM-DD": "%Y-%m-%d",
            "YYYY-MM": "%Y-%m",
            "YYYY": "%Y",
        }

    @classmethod
    def reset_current_number(
        cls,
        widget_key: str,
        org_id: str,
        sid: str,
        login_user_detail: AccountDetailV2,
        step_id: int,
    ):
        import json

        from robot_processor.client import action_client

        widget_auto_number = cls.query.filter_by(widget_key=widget_key).first()
        if widget_auto_number:
            widget_auto_number.current_number = 0
            db.session.commit()

            step = Step.query.filter_by(id=step_id).first()

            action_client.create_action_log_by_kafka(
                dict(
                    org_id=org_id,
                    sid=sid,
                    user=login_user_detail.user_nick,
                    platform="",
                    label="",
                    job_label=step.name,  # type: ignore[union-attr]
                    operator="reset",
                    model="forms",
                    object_id=str(step.form_id),  # type: ignore[union-attr]
                    operate_ts=int(time.time()),
                    raw_json=json.dumps(
                        {
                            "job": step.name,  # type: ignore[union-attr]
                            "widget_key": widget_key,
                        },
                        ensure_ascii=True,
                    ),
                )
            )
            return True, None
        else:
            return False, "当前模版还未产生工单任务，重置失败"

    @staticmethod
    def get_widget_id() -> Optional[int]:
        """
        获取"自动编号"这一组件在数据库/缓存中的组件 ID 号。

        :return:
        """
        from robot_processor.ext import cache

        auto_number_widget_key = "auto_number_widget_key"

        widget_id = cache.get(auto_number_widget_key)
        if widget_id:
            return widget_id
        else:
            widget = Widget.query.filter_by(label="自动编号").first()
            if widget:
                widget_id = widget.id
                cache.set(auto_number_widget_key, widget_id, timeout=0)
                return widget_id
            else:
                return None


class Operator(DbBaseModel, BasicMixin):
    """操作符"""

    type: Mapped[str | None] = mapped_column(sa.String(32), comment="数据类型")
    operator: Mapped[str | None] = mapped_column(sa.String(32), comment="操作符")
    label: Mapped[str | None] = mapped_column(sa.String(32), comment="名称")
    # 是单行操作符还是多行操作符
    category: Mapped[int] = mapped_column(sa.Integer, default=0, comment="0=单行；1=多行")
    option: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="前端需要的配置项")

    def to_info(self) -> dict:
        info = {
            "label": self.label,
            "operator": self.operator,
            "category": self.category,
        }
        info.update(self.option)
        return info


class Address(DbBaseModel, BasicMixin):
    """地址选择器数据"""

    province: Mapped[str | None] = mapped_column(sa.String(32), index=True)
    city: Mapped[str] = mapped_column(sa.String(32), default="", index=True)
    zone: Mapped[str] = mapped_column(sa.String(32), default="", index=True)
    code: Mapped[str | None] = mapped_column(sa.String(8))

    def brief(self):
        return {
            "province": self.province,
            "city": self.city if self.city != self.province + "市辖区" else self.province,  # type: ignore[operator]
            "zone": self.zone,
            "town": "",
        }

    @classmethod
    def get_address_by_query(cls, query: "AddressSchema.AddressQuery") -> list["AddressView.AddressBrief"]:
        def get_all_province():
            address_list: list[Address] = cls.query.filter(cls.city == "").all()
            return [
                AddressView.AddressBrief(
                    province=address.province,
                    city=address.city,
                    zone=address.zone,
                    town="",
                )
                for address in address_list
            ]

        def get_province_city():
            address_list: list[Address] = cls.query.filter(
                cls.province == query.province, cls.city != "", cls.zone == ""
            ).all()
            return [
                AddressView.AddressBrief(
                    province=address.province,
                    city=address.city,
                    zone=address.zone,
                    town="",
                )
                for address in address_list
            ]

        def get_city_zone():
            address_list: list[Address] = cls.query.filter(
                cls.province == query.province, cls.city == query.city, cls.zone != ""
            ).all()
            return [
                AddressView.AddressBrief(
                    province=address.province,
                    city=address.city,
                    zone=address.zone,
                    town="",
                )
                for address in address_list
            ]

        def get_town():
            address: Address | None = cls.query.filter(cls.province == query.province, cls.city == query.city).first()
            if not address:
                return []
            address_town_list: list[AddressTown] = AddressTown.query.filter(
                AddressTown.pcode == f"{address.code}000000"
            ).all()
            return [
                AddressView.AddressBrief(
                    province=address.province,
                    city=address.city,
                    zone=address.zone,
                    town=address_town.town,
                )
                for address_town in address_town_list
            ]

        def get_zone_town():
            address: Address | None = cls.query.filter(
                cls.province == query.province,
                cls.city == query.city,
                cls.zone == query.zone,
            ).first()
            if not address:
                return []
            address_town_list: list[AddressTown] = AddressTown.query.filter(
                AddressTown.pcode == f"{address.code}000000"
            ).all()
            return [
                AddressView.AddressBrief(
                    province=address.province,
                    city=address.city,
                    zone=address.zone,
                    town=address_town.town,
                )
                for address_town in address_town_list
            ]

        if query.is_query_province:
            return get_all_province()
        if query.is_query_city:
            return get_province_city()
        if query.is_query_zone:
            return get_city_zone()
        if query.is_query_town:
            return get_town()
        if query.is_query_zone_town:
            return get_zone_town()

        return []


class AddressSchema:
    class AddressQuery(BaseModel):
        province: str | None
        city: str | None
        zone: str | None
        query_town: bool  # 有些市没有区，影响是否通过 zone 查询

        @classmethod
        def build(
            cls,
            province: str | None,
            city: str | None,
            zone: str | None,
            query_town: bool = False,
        ):
            if province in ProvinceShortMap:
                province = ProvinceShortMap[province]
            if (province is not None) and (city == province):
                city = f"{city}市辖区"

            return cls(province=province, city=city, zone=zone, query_town=query_town)

        @property
        def is_query_province(self):
            return self.province is None

        @property
        def is_query_city(self):
            return all([self.province is not None, self.city is None])

        @property
        def is_query_zone(self):
            return all([self.province is not None, self.city is not None, self.zone is None])

        @property
        def is_query_town(self):
            return all(
                [
                    self.query_town,
                    self.province is not None,
                    self.city is not None,
                ]
            )

        @property
        def is_query_zone_town(self):
            return all(
                [
                    self.province is not None,
                    self.city is not None,
                    self.zone is not None,
                ]
            )


class AddressTown(DbBaseModel, BasicMixin):
    """地址选择器数据"""

    pcode: Mapped[str | None] = mapped_column(sa.String(12), index=True)
    code: Mapped[str] = mapped_column(sa.String(12), default="", index=True)
    town: Mapped[str] = mapped_column(sa.String(64), default="", index=True)


class AddressView:
    @omit_none_fields_before_validate("province", "city", "zone", "town")
    class AddressBrief(BaseModel):
        province: str = Field(default="")
        city: str = Field(default="")
        zone: str = Field(default="")
        town: str = Field(default="")

        @root_validator(skip_on_failure=True)
        def fix_city(cls, values):
            if values["city"] == values["province"] + "市辖区":
                values["city"] = values["province"]
            return values


class StepScope(BaseModel):
    step_uuid: str
    widget_ref: WidgetRef

    def check_is_same_step_scope(self, target_step_scope) -> bool:
        """
        检测当前步骤作用域与传入的步骤作用域是否相同。
        :param target_step_scope:
        :return:
        """
        if not isinstance(target_step_scope, StepScope):
            return False
        return self.step_uuid == target_step_scope.step_uuid


# FIXME 之前的临时定制全部迁移到产品化方案后，Event 可以下线
class Event(DbBaseModel):
    class Type(StrEnum):
        PDD_REFUND = auto()  # 拼多多仅退款
        PDD_REFUND_WITH_RETURN = auto()  # 拼多多退货退款

    id: Mapped[Type] = mapped_column(sa.Enum(Type), primary_key=True)
    mapper: Mapped[list] = mapped_column(sa.JSON, default=list)

    @classmethod
    def get_by_event_type(cls, event_type):
        stmt = sa.select(cls).where(cls.id == event_type)
        result = db.session.execute(stmt)
        return result.scalar_one()

    def get_mapper(self):
        from robot_processor.form.data_converter import DataMapper

        return [DataMapper.validate(each) for each in self.mapper]


class Webhook(DbBaseModel):
    class State(StrEnum):
        ENABLED = auto()
        DISABLED = auto()

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    org_id: Mapped[int] = mapped_column(sa.Integer, nullable=False)
    form_id: Mapped[int] = mapped_column(sa.Integer, nullable=False)
    step_uuid: Mapped[str] = mapped_column(sa.String(32), nullable=False)
    event: Mapped[str] = mapped_column(sa.String(128), nullable=False)
    state: Mapped[State] = mapped_column(sa.Enum(State), nullable=False, default=State.ENABLED)

    created_at: Mapped[datetime] = mapped_column(sa.DateTime, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(sa.DateTime, default=datetime.now, onupdate=datetime.now)

    @classmethod
    def get_by_event(cls, org_id, event):
        stmt = sa.select(cls).where(cls.org_id == org_id, cls.event == event, cls.state == Webhook.State.ENABLED)
        result = db.session.scalars(stmt)
        return result.all()

    def get_form(self):
        return db.session.get(Form, self.form_id)

    def get_form_namespace(self):
        form_version = (
            FormVersion.query.filter(FormVersion.form_id == self.form_id, FormVersion.deleted.isnot(True))
            .order_by(FormVersion.id.desc())
            .first()
        )
        assert form_version, "工单未发布"
        step_id = form_version.get_current_step(self.step_uuid)
        namespace = FormSymbol.get_namespace(self.form_id, step_id).unwrap()
        return namespace


# Alias
WidgetInfoSchema = WidgetInfo.Schema
WidgetInfoView = WidgetInfo.View


def update_forward_refs():
    from robot_processor.rpa_service.models import Rpa
    from robot_processor.shop.models import Shop

    Step.Schema.Notifier.update_forward_refs()
    Step.View.Base.update_forward_refs(Shop=Shop, Rpa=Rpa)
    Step.View.FormEditor.update_forward_refs(Shop=Shop, Rpa=Rpa)
    Step.View.FormVersion.update_forward_refs(Shop=Shop, Rpa=Rpa)
    Step.View.Miniapp.update_forward_refs(Shop=Shop, Rpa=Rpa)
    Step.View.RawStep.update_forward_refs(Shop=Shop, Rpa=Rpa, StepFieldOverride=StepFieldOverride)

    FormVersion.Schema.FormJobRoad.update_forward_refs()
    FormVersion.View.BusinessOrderBasic.update_forward_refs()
    FormVersion.View.BusinessOrderDetail.update_forward_refs()
    StepFieldOverride.View.RawStep.update_forward_refs()
    Form.Schema.FormActionLog.update_forward_refs()
    WidgetInfo.Schema.OptionValue.update_forward_refs()
    WidgetInfo.Schema.OptionValueNumber.update_forward_refs()
    WidgetInfo.Schema.OptionValueSelect.update_forward_refs()
    WidgetInfo.Schema.OptionValueDate.update_forward_refs()
    WidgetInfo.Schema.OptionValueReissueProduct.update_forward_refs()
    WidgetInfo.Schema.OptionValueUpload.update_forward_refs()
    WidgetInfo.Schema.DataSchema.update_forward_refs()
    WidgetInfo.Schema.DataSchemaField.update_forward_refs()
    WidgetInfo.Schema.ReportWidgetMeta.update_forward_refs()
    WidgetInfo.Schema.WidgetRefAndLeafFromRootPathPair.update_forward_refs()
    WidgetInfo.View.ReportBase.update_forward_refs()
    WidgetInfo.View.ReportDetailed.update_forward_refs()
    WidgetInfo.View.RawStep.update_forward_refs()
    WidgetInfo.View.LeafFromRootPath.update_forward_refs()
    WidgetInfo.View.SingleWidgetMeta.update_forward_refs()

    WidgetRef.update_forward_refs()


update_forward_refs()
