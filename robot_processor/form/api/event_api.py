from dataclasses import asdict

from flask import Blueprint
from pydantic import BaseModel

from robot_processor.decorators import org_required
from robot_processor.utils import GenericResponse
from robot_processor.validator import validate

router = Blueprint("form-editor-event-api", __name__, url_prefix="/v1")


class EventShortcut(BaseModel):
    label: str
    value: str
    description: str


class ListAllEventsResponse(BaseModel):
    class Event(BaseModel):
        id: str
        label: str
        description: str | None
        icon: str
        symbols: list
        shortcuts: list[EventShortcut]

    events: list[Event]


@router.get("/form-editor/events")
@org_required
@validate
def list_all_events() -> GenericResponse[ListAllEventsResponse]:
    from robot_processor.form.event.models import event_registry

    response = ListAllEventsResponse(events=[])
    for event in event_registry.values():
        event_type = event.id
        view = response.Event(
            id=event_type.name,
            label=event.label,
            description=event.description,
            icon=event.icon,
            symbols=[asdict(symbol.to_deprecated()) for symbol in event.symbols],
            shortcuts=[
                EventShortcut(
                    label=shortcut.label,
                    description=shortcut.description,
                    value=shortcut.value,
                )
                for shortcut in event.get_shortcuts().values()
            ],
        )
        response.events.append(view)

    return GenericResponse[ListAllEventsResponse].Success(response)
