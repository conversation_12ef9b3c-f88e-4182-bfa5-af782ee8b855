import typing as t

from flask import Blueprint
from flask import jsonify
from flask import request
from loguru import logger
from pydantic import BaseModel

from robot_processor.currents import g
from robot_processor.decorators import shop_required
from robot_processor.enums import FormMold
from robot_processor.enums import WidgetCategory
from robot_processor.error.base import BizError
from robot_processor.ext import db
from robot_processor.form.models import Address
from robot_processor.form.models import AddressSchema
from robot_processor.form.models import Component
from robot_processor.form.models import Operator
from robot_processor.form.models import Widget
from robot_processor.form.models import WidgetCollection
from robot_processor.form.models import WidgetInfo
from robot_processor.validator import validate

from .widget_schema import GetAllWidgetsRequest
from .widget_schema import GetSchema
from .widget_schema import PostCustomizeForm
from .widget_schema import PutCustomizeForm
from .widget_schema import WidgetAutoNumberResetReq

api = Blueprint("widget-api", __name__)


@api.before_request
@shop_required
def before_request():
    pass


@api.errorhandler(BizError)
def biz_error_wrapper(e: BizError):
    return jsonify(success=False, msg=e.biz_display), 400


def is_buyer_widget(widget):
    schema = widget.schema
    widget_type = schema.get("type")
    whitelist = [
        "string",
        "textarea",
        "order",
        "usernick",
        "rate",
        # 2023.09.04 新增商品/价格组件
        "product",
        "price",
        "received-amount",
        "payment",
        # 下拉选择组件拆分后，单选组件有自己的 widget type
        "radio-tile",
        "radio-dropdown",
    ]

    if widget_type in whitelist:
        return True

    # 下拉选择类的组件，目前仅支持单选组件
    # 这个是历史版本的下拉选择组件，已废弃（无法新增，历史数据仍然可以使用）
    if widget_type == "select" and schema.get("mode") == "single":
        return True

    # 文件上传类的组件，目前仅支持图片上传
    if widget_type == "upload" and schema.get("uploadType") == "image":
        return True

    return False


def filter_widgets(category, codes: t.List[int], get_buyer_widget=False):
    widgets = Widget.query.filter(Widget.category == category, Widget.code.in_(codes))
    result_widgets = []
    if get_buyer_widget:
        for widget in widgets:
            if is_buyer_widget(widget):
                result_widgets.append(widget)
    else:
        for widget in widgets:
            if not widget.schema.get("limits") or not widget.schema.get("limits").get("rules"):  # type: ignore[union-attr]  # noqa: E501
                result_widgets.append(widget)
            else:
                rules = widget.schema.get("limits").get("rules")  # type: ignore[union-attr]
                if widget.schema.get("limits").get("rule_relation") == "OR":  # type: ignore[union-attr]
                    rule_relation = any
                else:
                    rule_relation = all
                if rule_relation(
                    [r.get("rule_type").lower() == "platform" and r.get("rule_value") == g.shop.platform for r in rules]
                ):
                    result_widgets.append(widget)
    return result_widgets


@api.get("/v1/form-editor/widgets")
@validate
def list_all_widgets(query: GetAllWidgetsRequest):
    """获取全部组件的简略信息"""
    if not (contract := g.shop.contract):
        return jsonify(reason="飞梭旗舰版功能，请联系店铺销售升级产品类型"), 403

    if query.form_mold:
        if query.form_mold == FormMold.BUYER.name:
            # 只返回可以让买家填写的组件
            return jsonify(
                widgets=[
                    {
                        "category": category.label,
                        "members": [widget.brief() for widget in filter_widgets(category, contract.codes, True)],
                    }
                    for category in WidgetCategory
                ]
            )
    else:
        return jsonify(
            widgets=[
                {
                    "category": category.label,
                    "members": [widget.brief() for widget in filter_widgets(category, contract.codes)],
                }
                for category in WidgetCategory
            ]
        )


def build_widget_info(param: t.Union[PostCustomizeForm, PutCustomizeForm]):
    from robot_processor.form.models import WidgetInfo

    widget_info_list = []
    for index, widget_info in enumerate(param.widgets):
        widget_dict = Widget.get(widget_info.id)
        if not widget_dict:
            raise BizError(f"未找到组件! id={widget_info.id}")
        widget_type = widget_dict.get("widget_type", "")
        if widget_type == "table" and not widget_info.data_schema:
            raise BizError(f"table组件必须设置data_schema! key={widget_info.key}")
        widget_info_inst = WidgetInfo()
        widget_info_inst.key = widget_info.key
        widget_info_inst.widget_id = widget_info.id  # type: ignore[assignment]
        widget_info_inst.option_value = widget_info.option_value
        widget_info_inst.schema_value = widget_info.schema_value
        widget_info_inst.data_schema = widget_info.data_schema if widget_info.data_schema else {}
        widget_info_inst.ref_config = [item.dict() for item in widget_info.ref_config]
        widget_info_inst.before = widget_info.before
        widget_info_inst.order = index
        widget_info_list.append(widget_info_inst)
    return widget_info_list


@api.post("/v1/form-editor/widgets")
@validate
def create_widget_collection(body: PostCustomizeForm):
    """创建 widget info 列表和 widget collection，返回 widget collection id."""
    from robot_processor.form.models import WidgetCollection

    widget_info = build_widget_info(body)
    widget_collection = WidgetCollection.create_with_widget_info_list(widget_info)
    logger.info(f"已创建 widget collection {widget_collection.id}")
    db.session.commit()
    return jsonify(success=True, widget_collection_id=widget_collection.id)


@api.put("/v1/form-editor/widgets")
@validate
def update_widget_collection(body: PutCustomizeForm):
    """创建 widget info 列表，创建或修改 widget collection，返回 widget collection id."""
    from robot_processor.form.models import WidgetCollection

    widget_info = build_widget_info(body)
    widget_collection: WidgetCollection = WidgetCollection.query.get_or_404(body.widget_collection_id)
    updated = widget_collection.update_with_widget_info_list(widget_info)
    db.session.commit()
    return jsonify(success=True, widget_collection_id=updated.id)


class GetWidgetSchemaResp(BaseModel):
    widget_schemas: t.List[Widget.View.FormEditor]

    def dict(self, **kwargs):
        kwargs.setdefault("by_alias", True)
        return super().dict(**kwargs)


@api.post("/v1/form-editor/widgets/schema")
@validate
def get_widget_schema(body: GetSchema) -> GetWidgetSchemaResp:
    """根据组件 ids 获取 widget schema"""
    widgets = Widget.query.filter(Widget.id.in_(body.widget_ids)).all()

    return GetWidgetSchemaResp(widget_schemas=[Widget.View.FormEditor.from_orm(widget) for widget in widgets])


@api.get("/v1/form-editor/widgets/widget-collection/<collection_id>")
def get_collection_by_id(collection_id):
    """获取用户组件信息详情"""
    brief = request.args.get("brief", "false")
    is_brief = True if brief == "true" else False
    widget_collection = WidgetCollection.query.get_or_404(collection_id)
    return jsonify(widgets=widget_collection.widgets_info(is_brief))


@api.get("/v1/form-editor/widgets/operators")
def get_operators():
    ret: dict[str, list[dict]] = {}
    for opt in Operator.query.all():
        if opt.type not in ret:
            ret[opt.type] = []  # type: ignore[index]
        ret[opt.type].append(opt.to_info())  # type: ignore[index]

    return jsonify(success=True, data=ret)


@api.post("/v1/form-editor/widget-auto-number/reset")
@validate
def widget_auto_number_reset(body: WidgetAutoNumberResetReq):
    from robot_processor.form.models import WidgetAutoNumber

    org_id = g.auth.org_id
    sid = g.shop.sid

    assert g.login_user_detail
    is_ok, error = WidgetAutoNumber.reset_current_number(
        body.widget_key,
        str(org_id),
        sid,
        g.login_user_detail,
        body.step_id,
    )

    if is_ok:
        return jsonify(success=True)
    else:
        return jsonify(reason=error), 400


@api.get("/v1/widget-data/address")
def get_address():
    address_query = AddressSchema.AddressQuery.build(
        province=request.args.get("province"),
        city=request.args.get("city"),
        zone=request.args.get("zone"),
        query_town=bool(request.args.get("query_town")),
    )
    address_list = Address.get_address_by_query(address_query)
    return jsonify(result=[address.dict() for address in address_list])


class UiSchemaToSymbolsBody(BaseModel):
    class Widget(BaseModel):
        id: int  # 对应的Widget模型的id
        key: str  # 前端生成的备用WidgetInfo uuid
        before: bool
        option_value: dict
        data_schema: dict | None

    ui_schema: list[Widget]


@api.post("/v1/form-editor/ui-schema-to-symbols")
@validate
def ui_schema_to_symbols(body: UiSchemaToSymbolsBody):
    from robot_processor.form.compatible_utils import widget_info_to_editable_view

    widget_map = dict((widget.id, widget) for widget in Widget.query)
    component_map = dict((component.id, component) for component in Component.query)
    symbols = []
    for idx, widget_info_obj in enumerate(body.ui_schema):
        widget_dict = Widget.get(widget_info_obj.id)
        if not widget_dict:
            raise BizError(f"未找到组件 id={widget_info_obj.id}")
        widget_type = widget_dict.get("widget_type", "")
        if widget_type == "table" and not widget_info_obj.data_schema:
            raise BizError(f"table组件必须设置data_schema! key={widget_info_obj.key}")
        widget_info = WidgetInfo()
        widget_info.key = widget_info_obj.key
        widget_info.widget_id = widget_info_obj.id  # type: ignore[assignment]
        widget_info.option_value = widget_info_obj.option_value
        widget_info.data_schema = widget_info_obj.data_schema or dict()
        widget_info.before = widget_info_obj.before
        widget_info.order = idx
        symbols.append(widget_info_to_editable_view(widget_info, widget_map, component_map))
    return jsonify(symbols=[symbol.dict(exclude_none=True) for symbol in symbols])
