from typing import List

from flask import Blueprint
from flask import jsonify
from flask import request
from pydantic import Field
from sqlalchemy.orm import Load

from robot_processor.currents import g
from robot_processor.decorators import shop_required
from robot_processor.error.validate import ValidateError
from robot_processor.form.handlers import get_prev_widgets
from robot_processor.form.models import Step
from robot_processor.rpa_service.models import Rpa
from robot_processor.rpa_service.models import RpaRuleType
from robot_processor.shop.models import Shop
from robot_processor.utils import BaseResponse
from robot_processor.validator import validate

api = Blueprint("task-api", __name__, "")


@api.before_request
@shop_required
def auth():
    pass


class ShopTaskResponse(BaseResponse):
    tasks: List[Rpa.View.FormEditor] = Field(alias="data")


@api.get("/tasks")
@api.get("/task-templates")
@ValidateError.decorator
@validate
def get_tasks() -> ShopTaskResponse:
    """
    展示所有已订阅/未订阅可订阅的 rpa
    过滤不符合规则约束的 rpa
    """
    shop: Shop = g.shop
    rpa_list: List[Rpa] = Rpa.Queries.online().options(Load(Rpa).joinedload(Rpa.limits)).all()

    tasks = []
    for rpa in rpa_list:
        subscribe_info = Rpa.Utils.get_org_subscribe_info(rpa=rpa, org_id=shop.org_id)  # type: ignore[arg-type]
        if subscribe_info.need_hidden:  # 不满足租户限制的，不会在页面上展示
            continue
        # 已订阅
        if subscribe_info.has_subscribed:
            broken_limits = Rpa.Utils.get_broken_rpa_limits_for_shop(rpa, shop)
            # 当前店铺满足 rpa 的所有条件
            if not broken_limits:
                tasks.append(Rpa.View.FormEditor.from_(rpa=rpa, has_subscribed=True))
            # erp 和工单模板是独立配置的，不满足 erp 条件时，是可以先进行模板配置的
            elif broken_limits == [RpaRuleType.ERP]:
                tasks.append(Rpa.View.FormEditor.from_(rpa=rpa, has_subscribed=True))
            # 当前店铺不满足 rpa 的所有条件，如平台限制，不需要展示在可选任务中
            else:
                continue
        # 未订阅，把可以订阅的 rpa 也展示出来
        elif subscribe_info.can_subscribe:
            tasks.append(Rpa.View.FormEditor.from_(rpa=rpa, has_subscribed=False))

    return ShopTaskResponse.Success(tasks)


def query_step(step_uuid: str):
    step = Step.Queries.by_step_uuid(step_uuid).first()
    if step is not None:
        return step
    else:
        raise ValidateError("工单步骤不存在", code=400)


@api.get("/steps/<step_uuid>/widget-collection")
@ValidateError.decorator(rewrite_response_status=True)
def get_collection_by_steps(step_uuid):
    """获取 step 及前置 step 关联的 WidgetCollection

    因工单模板的版本流程预览里也会用到，所以这里不能过滤已删除的步骤
    """
    step = query_step(step_uuid)
    brief = request.args.get("brief", "") == "true"
    version_descriptor = request.args.get("version_descriptor", default=None, type=str)
    return jsonify(widget_info=get_prev_widgets(step, brief, version_descriptor=version_descriptor))


@api.get("/steps/<step_uuid>/concepts")
@ValidateError.decorator(rewrite_response_status=True)
def get_concepts_by_steps(step_uuid):
    """获取 step 及前置 step 关联的 组件/固定参数"""
    step = query_step(step_uuid)
    version_descriptor = request.args.get("version_descriptor", default=None, type=str)
    brief = request.args.get("brief", "") == "true"
    widget_info = get_prev_widgets(step, brief=brief, is_required=False, version_descriptor=version_descriptor)
    return jsonify(widget_info=widget_info, fixed_concepts=[])
