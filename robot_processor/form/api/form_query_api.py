"""各类查询/查看 工单模板相关的 API."""

from collections import defaultdict
from operator import attrgetter
from typing import Dict
from typing import List
from typing import Optional
from typing import cast

from flask import Blueprint
from flask import jsonify
from flask import request
from loguru import logger
from more_itertools import first
from pydantic import BaseModel
from pydantic import validator
from sqlalchemy import and_
from sqlalchemy import or_

from robot_processor.base_schemas import Response
from robot_processor.business_order.seller.enums import MyBusinessOrdersTab
from robot_processor.business_order.seller.query_by_accessor import AccessorQuery
from robot_processor.business_order.seller.schema import BusinessOrdersDetailRequest
from robot_processor.business_order.seller.schema import SidListMixin
from robot_processor.currents import g
from robot_processor.decorators import mini_app_required
from robot_processor.decorators import org_required
from robot_processor.decorators import shop_required
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import StepType
from robot_processor.ext import cache
from robot_processor.ext import db
from robot_processor.form.models import Form
from robot_processor.form.models import FormShop
from robot_processor.form.models import Step
from robot_processor.form.models import WidgetInfo
from robot_processor.shop.models import Shop
from robot_processor.shop.models import ShopStatus
from robot_processor.utils import GenericResponse
from robot_processor.utils import ResultUtil
from robot_processor.utils import wrap_status_code_in_response_body
from robot_processor.validator import validate

api = Blueprint("form_query_api", __name__)
miniapp_api = Blueprint("mini-app_form_query_api", __name__)
miniapp_api.after_request(wrap_status_code_in_response_body)

plugin_api = Blueprint("plugin_form_query_api", __name__)
plugin_api.after_request(wrap_status_code_in_response_body)


class FormShopInfo(BaseModel):
    form_id: int
    shop_sid: str
    shop_platform: str
    shop_nick: Optional[str]
    shop_title: Optional[str]


class FormInfo(BaseModel):
    name: str
    form_info: List[FormShopInfo]


class ListForm(BaseModel):
    forms: List[FormInfo]


ListFormResponse = GenericResponse[ListForm]


@api.get("/v1/forms/names/assistant-accessible")
@org_required
@validate
def get_assistant_accessible_form_names() -> ListFormResponse:
    """当前客服可以使用哪些模板创建工单."""
    from sqlalchemy.orm import load_only

    from robot_processor.form.getter import get_store_available_forms
    from robot_processor.form.models import Form

    query_sid = request.args.getlist("sid", type=str)

    available_forms: List[Form] = get_store_available_forms(g.org_id, query_sid, g.auth.user_id)
    form_ids = [form.id for form in available_forms]
    # group by form name
    forms_by_name: dict[str, list[Form]] = {}
    for form in available_forms:
        forms_by_name.setdefault(form.name, []).append(form)  # type: ignore[arg-type]

    shops = (
        Shop.Queries.org_shops_by_org_id(g.org_id)
        .options(load_only(Shop.sid, Shop.platform, Shop.nick, Shop.title))
        .all()
    )  # 不直接使用 form.shop 访问是为了避免 N+1 查询
    org_shop_map = {shop.channel_id: shop for shop in shops}
    form_channel_ids_map = Form.Queries.subscribed_channel_ids_by_form_ids(form_ids)

    res = ListForm(forms=[])
    for form_name, form_list in forms_by_name.items():
        info = FormInfo(name=form_name, form_info=[])
        for form in form_list:
            for channel_id in form_channel_ids_map.get(form.id, []):
                if (shop := org_shop_map.get(channel_id)) and shop.sid in query_sid:
                    info.form_info.append(
                        FormShopInfo(
                            form_id=form.id,
                            shop_sid=shop.sid,
                            shop_platform=shop.platform,
                            shop_nick=shop.nick,
                            shop_title=shop.title,
                        )
                    )
        if info.form_info:
            res.forms.append(info)

    return ListFormResponse.Success(res)


class FormsNameReq(BaseModel):
    sid: str


class FormsNameRes(BaseModel):
    class FormBrief(BaseModel):
        name: str
        deleted: bool

    data: List[str]
    data_v2: List[FormBrief]


class FormsIdsReq(BaseModel):
    sid: str


class FormsIdsRes(BaseModel):
    data: Dict[str, List[int]]


class GetFormsBodyReq(BaseModel):
    class ShopInfo(BaseModel):
        sid: str
        platform: str

    shops: list[ShopInfo] | None


@api.post("/v1/<origin>/forms")
@shop_required
@validate
def get_forms_by_shops(origin: str, body: GetFormsBodyReq):
    sids = [shop_info.sid for shop_info in (body.shops or [])]
    # 如果前端没有传 sid，则使用 token 中的 org_id 来查询所有的店铺。
    shops: list[Shop] = []
    if len(sids) == 0:
        shops = Shop.query.filter(
            Shop.org_id == g.shop.org_id,
            Shop.status.in_([ShopStatus.ENABLE]),
        ).all()
    else:
        shops = Shop.query.filter(
            Shop.sid.in_(sids),
            Shop.status.in_([ShopStatus.ENABLE]),
            or_(*[and_(Shop.sid == shop_info.sid, Shop.platform == Shop.platform) for shop_info in (body.shops or [])]),
        ).all()

    # 查询 form_shop
    form_shops: list[FormShop] = FormShop.query.filter(
        FormShop.channel_id.in_([shop.channel_id for shop in shops]),
        FormShop.status.in_([FormShop.Status.ENABLED, FormShop.Status.RESERVED, FormShop.Status.DISABLED]),
    ).all()

    shop_channel_id_to_shop_mapping: dict[int, Shop] = {
        shop.channel_id: shop for shop in shops if shop.channel_id is not None
    }
    form_id_to_info_mapping: dict[int, list[dict]] = {}
    # 遍历 form_shop，生成已归档的工单模板列表以及 form_id 与最终结果的映射。
    for form_shop in form_shops:
        shop = shop_channel_id_to_shop_mapping.get(form_shop.channel_id)
        if shop is None:
            continue
        if origin_info := form_id_to_info_mapping.get(form_shop.form_id):
            new_info = origin_info + [
                {
                    "status": form_shop.status,
                    "sid": shop.sid,
                    "platform": shop.platform,
                    "title": shop.title,
                }
            ]
        else:
            new_info = [
                {
                    "status": form_shop.status,
                    "sid": shop.sid,
                    "platform": shop.platform,
                    "title": shop.title,
                }
            ]
        form_id_to_info_mapping.update({form_shop.form_id: new_info})

    forms: list[Form] = Form.query.filter(Form.id.in_([form_shop.form_id for form_shop in form_shops])).all()

    # 遍历 form。
    form_infos = []
    for form in forms:
        form_info = form_id_to_info_mapping.get(form.id) or []
        if form_version := form.versions.first():
            version = form_version.version_no
        else:
            version = "STASH"
        form_infos.append({"form_id": form.id, "form_name": form.name, "form_info": form_info, "form_version": version})

    return {"forms": form_infos}


@api.get("/v1/task-center/forms/name")
@shop_required
@validate
def forms_name_filter_by_sid(query: FormsNameReq) -> FormsNameRes:
    if "," in query.sid:
        request_sid = query.sid.split(",")
    else:
        request_sid = request.args.getlist("sid", str)
    form_shops = (
        Form.Queries.form_shops_by_sids(request_sid)
        .options(Form.Options.joined_load_form)
        .filter(Form.Filters.can_view_in_report)
        .all()
    )
    forms = [form_shop.form for form_shop in form_shops]
    form_name = list({form.name for form in forms})
    form_brief = [  # 需要按照工单名/工单删除状态进行合并
        FormsNameRes.FormBrief(name=form_name, deleted=form_deleted)
        for form_name, form_deleted in set((form.name, form.get_deleted()) for form in forms)
    ]

    return FormsNameRes(data=form_name, data_v2=form_brief)


@api.get("/v1/task-center/forms/ids")
@shop_required
@validate
def forms_ids_filter_by_sid(query: FormsIdsReq) -> FormsIdsRes:
    """这个接口是明细报表在使用，提供工单模板及其对应的 id 的映射关系"""
    if "," in query.sid:
        request_sid = query.sid.split(",")
    else:
        request_sid = request.args.getlist("sid", str)

    form_shops = (
        Form.Queries.form_shops_by_sids(request_sid)
        .options(Form.Options.joined_load_form)
        .filter(Form.Filters.can_view_in_report)
        .all()
    )
    form_dict = defaultdict(set)
    for form_shop in form_shops:
        form = form_shop.form
        form_dict[form.name].add(form.id)
        if form_shop.sync_merged_form_id:
            form_dict[form.name].add(form_shop.sync_merged_form_id)

    data = {name: list(form_ids) for name, form_ids in form_dict.items()}
    return FormsIdsRes(data=data)


class GetFormsQuerySchema(BaseModel):
    category: Optional[str] = None


@plugin_api.get("/v1/available-forms")
@org_required
@validate
def get_available_forms(query: GetFormsQuerySchema):
    from robot_processor.form.getter import get_store_available_forms
    from robot_processor.user_customize_config.models import UserCustomizeConfig

    sid = g.auth.store_id
    user_id = g.auth.user_id
    category = query.category

    ck = f"available-forms_{sid}_{user_id}_{category}"
    forms = cache.get(ck)
    if not forms:
        forms = get_store_available_forms(g.org_id, [sid], user_id, category=category)
        forms = [form.view__base.dict() for form in forms]
        cache.set(ck, forms, timeout=30)

    form_setting = (
        UserCustomizeConfig.Queries.form_query_config(g.auth.login_user_id, g.auth.login_user_type, sid)
        or UserCustomizeConfig.Schemas.FormQueryConfig.default().dict()
    )

    # 用户自定义的工单模板排序
    form_id_sorts = form_setting.get("sorts", [])

    if not form_id_sorts:
        return jsonify(forms=[form for form in forms])

    query_dict = {}
    unsorted_forms = []
    for form in forms:
        # 查询出的工单模板构建成一个dict
        query_dict[form["id"]] = form
        if form["id"] not in form_id_sorts:
            # 新增加的模板，不在用户的排序列表中，默认放在最后
            unsorted_forms.append(form)

    forms_response = []

    for form_id in form_id_sorts:
        if query_dict.get(form_id):
            forms_response.append(query_dict.get(form_id))

    forms_response.extend(unsorted_forms)

    return jsonify(forms=forms_response)


@plugin_api.get("/v1/forms/<form_id>/schema")
@org_required
def get_form_schema(form_id):
    if not (form := Form.query.get(form_id)):
        response = ResultUtil.build_dict(404, None, "无效的工单模板")
        return ResultUtil.build_cloud_rep(response)

    # 如果指定了 step_id 则返回目标步骤的信息
    if step_id := request.args.get("step_id"):
        steps = []
        step = form.job_steps.filter_by(id=step_id).first()
    else:
        steps = form.job_steps.all()
        # 找到开始步骤
        # todo fixme 为了兼容性，第一步为没有前序步骤的human step 或者 system begin step后面的human step
        if begin_step_list := [i for i in steps if i.step_type == StepType.begin]:
            begin_step: Step = sorted(begin_step_list, key=attrgetter("id"))[-1]
            step = sorted([i for i in steps if i.step_uuid == begin_step.next_step_ids[0]], key=attrgetter("id"))[-1]
        else:
            step = next(
                iter(
                    sorted(
                        filter(
                            lambda x: len(x.prev_step_ids) == 0 and x.step_type == StepType.human,
                            steps,
                        ),
                        key=attrgetter("id"),
                        reverse=True,
                    )
                ),
                None,
            )
    if not step:
        response = ResultUtil.build_dict(404, None, "无效的工单模板")
        return ResultUtil.build_error_rep(response)

    form_schema = Form.View.FormSchema.from_(form=form, current_step=step, steps=steps)
    for widget_info_obj in form_schema.current_step.ui_schema:
        if not widget_info_obj.get("before"):
            continue
        if "key" in widget_info_obj and str(widget_info_obj["key"]).startswith("system_"):
            continue
        widget_info = first(WidgetInfo.Queries.by_keys_published([widget_info_obj["key"]]), None)
        if not widget_info:
            continue
        # 将引用组件的 option_value 和原组件 option_value 合并
        widget_info_option_value = widget_info.option_value
        widget_info_option_value.update(widget_info_obj["option_value"])
        widget_info_obj["option_value"] = widget_info_option_value
        # widget_info_obj 的 option_value 是被打平的
        widget_info_obj.update(widget_info_obj["option_value"])

    return jsonify(form_schema.dict())


@miniapp_api.get("/v1/mini-app/forms/<form_id>/schema")
@mini_app_required
def mini_app_get_form_schema(form_id):
    shop_obj = cast(dict, g.shop)
    step_id = request.args.get("step_id")

    shop = Shop.Queries.optimal_shop_by_sid(
        shop_obj["sid"], platform=shop_obj["platform"], org_id=shop_obj["org_id"]  # type: ignore[index]
    )
    if not (form := Form.query.get(form_id)):
        response = ResultUtil.build_dict(404, None, "无效的工单模板")
        return ResultUtil.build_cloud_rep(response)

    form = form.wraps(shop)  # type: ignore[arg-type]

    # 如果指定了 step_id 则返回目标步骤的信息
    if step_id:
        steps = []
        step = form.job_steps.filter_by(id=step_id).first()
    # 否则返回开始步骤的信息
    else:
        steps = form.job_steps.all()
        # 找到开始步骤
        # todo fixme 为了兼容性，第一步为没有前序步骤的human step 或者 system begin step后面的human step
        if begin_step_list := [i for i in steps if i.step_type == StepType.begin]:
            begin_step: Step = sorted(begin_step_list, key=attrgetter("id"))[-1]
            step = sorted([i for i in steps if i.step_uuid == begin_step.next_step_ids[0]], key=attrgetter("id"))[-1]
        else:
            step = next(
                iter(
                    sorted(
                        filter(
                            lambda x: len(x.prev_step_ids) == 0 and x.step_type == StepType.human,
                            steps,
                        ),
                        key=attrgetter("id"),
                        reverse=True,
                    )
                ),
                None,
            )
    if not step:
        response = ResultUtil.build_dict(404, None, "无效的工单模板")
        return ResultUtil.build_cloud_rep(response)

    form_schema = Form.View.FormSchema.from_(
        form=form,
        current_step=step,
        steps=steps,
        shop_title=shop_obj.get("title"),
        shop_pic_url=shop_obj.get("pic_url"),  # type: ignore[attr-defined]
    )
    response = ResultUtil.build_dict(200, form_schema.dict(), "")
    return ResultUtil.build_cloud_rep(response)


class StepResponse(BaseModel):
    id: int
    name: str
    step_uuid: str
    prev_step_ids: List[str] = []
    next_step_ids: List[str] = []


class SellerFormResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    updated_at: int
    form_category: Optional[str]
    tags: Optional[List[str]] = []
    steps: Optional[List[StepResponse]]
    total: Optional[int]


class SellerFormsResponse(BaseModel):
    forms: List[SellerFormResponse] = []


class HasDataStepsReq(SidListMixin):
    form_ids: List[int]
    tab: str = "ALL"
    picked: bool = False
    status: Optional[BusinessOrderStatus]

    @validator("tab")
    def tab_registered(cls, v):
        try:
            MyBusinessOrdersTab[v.upper()]
        except KeyError:
            raise ValueError("非法标签页")
        return v


class HasDataStepsRes(BaseModel):
    class GroupedStep(BaseModel):
        name: str
        step_id: List[int]

    steps: List[GroupedStep]


@api.post("/v1/seller/accessors/forms/steps")
@validate
def has_data_steps(body: HasDataStepsReq) -> Response[HasDataStepsRes]:
    """获取指定工单模板中，有数据的步骤"""

    logger.bind(k=str(body.tab)).info("获取工单模板步骤")
    if not body.form_ids:
        return Response.Success(HasDataStepsRes(steps=[]))

    finder = AccessorQuery(body.sid_list, g.auth.user_id, g.auth.org_id)
    step_id = finder.accessors_form_steps(body.form_ids, body.tab, body.picked, body.status)
    steps = (
        db.session.query(Step.id, Step.step_uuid, Step.name).filter(Step.id.in_(step_id)).order_by(Step.id.desc()).all()
    )
    step_names: dict[str, str] = {}
    # 先根据 step_uuid 分组，选组内 step_id 最大的作为展示步骤名
    for step in steps:
        step_uuid = step.step_uuid
        step_names.setdefault(step_uuid, step.name)
    step_ids: dict[str, list[int]] = {}
    for step in steps:
        step_uuid = step.step_uuid
        step_name = step_names[step_uuid]
        step_id = step.id
        step_ids.setdefault(step_name, []).append(step_id)
    steps_res = [HasDataStepsRes.GroupedStep(name=name, step_id=step_ids[name]) for name in step_ids]
    return Response(HasDataStepsRes(steps=steps_res))


@api.post("/v1/seller/accessors/forms")
@org_required
@validate
def get_forms(body: BusinessOrdersDetailRequest) -> SellerFormsResponse:
    """获取指定客服 进行中实例 对应的表单/步骤"""
    logger.bind(k=str(body.tab)).info("获取工单模板")
    try:
        MyBusinessOrdersTab[body.tab.upper()]
    except KeyError:
        return SellerFormsResponse(forms=[])

    accessor_query = AccessorQuery(body.sid_list, g.auth.user_id, g.auth.org_id)
    forms = accessor_query.accessors_forms(body.tab, picked=body.picked)
    return SellerFormsResponse(forms=forms)
