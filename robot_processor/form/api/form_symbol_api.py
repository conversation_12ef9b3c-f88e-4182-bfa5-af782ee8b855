from dataclasses import asdict

from flask import Blueprint
from loguru import logger
from pydantic import BaseModel

from robot_processor.base_schemas import Response
from robot_processor.client import app_config
from robot_processor.decorators import org_required
from robot_processor.enums import StepType
from robot_processor.ext import db
from robot_processor.form.models import Form
from robot_processor.form.models import FormSymbol
from robot_processor.form.models import FormSymbolEditableView
from robot_processor.form.models import FormVersion
from robot_processor.form.models import Step
from robot_processor.form.models import SymbolTable
from robot_processor.form.models import SymbolTableEditableView
from robot_processor.form.symbol_table import FormSymbolScope
from robot_processor.form.utils.scope_utils import TempStepNodeInfo
from robot_processor.form.utils.scope_utils import get_all_latest_steps_by_form_id
from robot_processor.form.utils.scope_utils import get_step_can_call_form_symbols
from robot_processor.form.utils.scope_utils import get_step_uuid_to_form_symbols_mapping
from robot_processor.form.utils.scope_utils import get_step_uuid_to_scopes_mapping
from robot_processor.utils import filter_none
from robot_processor.validator import validate

router = Blueprint("form-symbol-api", __name__)


class GetSymbolTableRequest(BaseModel):
    form_id: int
    version: str = "STASH"


class GetScopeFormSymbolsRequest(BaseModel):
    form_id: int
    step_uuid: str = FormVersion.Schema.StepNode.TEMP
    temp_step_node_info: TempStepNodeInfo | None = None


class GetScopeFormSymbolResponse(BaseModel):
    form_symbol_views: dict[str, list[FormSymbolEditableView]]


@router.get("/v1/form-symbols/symbol-table")
@validate
def get_symbol_table(query: GetSymbolTableRequest) -> Response[SymbolTable]:
    """获取工单的 SymbolTable"""
    symbol_table_result = FormSymbol.compatible_query_symbol_table(query.form_id, query.version)
    if symbol_table_result.is_err():
        return Response.Failed(str(symbol_table_result.err()))

    symbol_table = symbol_table_result.unwrap()
    return Response.Success(symbol_table)


@router.get("/v1/form-symbols/symbol-table/editable-view")
@validate
def list_symbol_table_editable_view(
    query: GetSymbolTableRequest,
) -> Response[SymbolTableEditableView]:
    """获取全部表单的符号-组件视图"""
    editable_view_result = FormSymbol.compatible_query_symbol_table_editable_view(query.form_id, query.version)
    if editable_view_result.is_err():
        return Response.Failed(str(editable_view_result.err()))

    editable_view = editable_view_result.unwrap()
    return Response.Success(editable_view)


@router.post("/v1/scope_form_symbols")
@validate
def get_scope_form_symbols(
    body: GetScopeFormSymbolsRequest,
) -> Response[GetScopeFormSymbolResponse]:
    logger.info("请求参数为: {}".format(body))
    try:
        if app_config.scope_form_symbols_api_use_form_composer and body.step_uuid != FormVersion.Schema.StepNode.TEMP:
            return Response.Success(
                GetScopeFormSymbolResponse(
                    form_symbol_views=get_static_form_symbols(form_id=body.form_id, step_uuid=body.step_uuid)
                )
            )
    except Exception as e:
        logger.exception(f"get_static_form_symbols failed. {e}")
    steps = get_all_latest_steps_by_form_id(form_id=body.form_id)
    step_uuid_to_scopes_mapping = get_step_uuid_to_scopes_mapping(
        steps=steps, temp_step_node_info=body.temp_step_node_info
    )
    step_uuid_to_form_symbols_mapping = get_step_uuid_to_form_symbols_mapping(form_id=body.form_id, steps=steps)
    form_symbol_views = get_step_can_call_form_symbols(
        step_uuid=body.step_uuid,
        step_uuid_to_form_symbols_mapping=step_uuid_to_form_symbols_mapping,
        step_uuid_to_scopes_mapping=step_uuid_to_scopes_mapping,
    )
    return Response.Success(GetScopeFormSymbolResponse(form_symbol_views=form_symbol_views))


@router.get("/v1/forms/<int:form_id>/steps/symbols")  # 默认是第一个步骤
@router.get("/v1/forms/<int:form_id>/steps/<int:step_id>/symbols")
@org_required
def get_form_step_symbols(form_id: int, step_id: int | None = None):
    from robot_processor.currents import g

    form: Form | None = Form.Queries.by_org_id(g.org_id).filter(Form.id == form_id).first()
    if form is None:
        return Response.Failed("无效的工单模板")
    if step_id is None:
        form_version = form.versions.first()
        if form_version is None:
            return Response.Failed("工单模板未发布")
        step_id = form_version.get_first_step()
    step = Step.query.filter_by(id=step_id, form_id=form_id).first()
    if step is None:
        return Response.Failed("无效的步骤")
    symbols_result = FormSymbol.query_symbol_table_editable_view_by_scope(FormSymbolScope.from_step(step))
    if symbols_result.is_err():
        return Response.Failed(str(symbols_result.unwrap_err()))
    return Response.Success(dict(symbols=[symbol.dict() for symbol in symbols_result.unwrap()]))


def get_static_form_symbols(form_id: int, step_uuid: str):
    form = db.session.get_one(Form, form_id)
    form_composer = form.get_form_composer_in_draft()
    symbol_table = form_composer.symbol_table_wrapper
    grouped_symbols: dict[str, list[dict]] = dict()

    def update_grouped_symbols(scope):
        for step in form_composer.steps:
            if symbol_table.step_scope_mapper[step.id] == scope:
                grouped_symbols[step.step_uuid] = list(map(lambda x: asdict(x.to_deprecated()), step.symbols))
                if step.step_type == StepType.iterate_gw_begin:
                    system_symbols = FormSymbol.generate_iterate_begin_gateway_system_symbols(step.name)
                    system_symbols_obj = [filter_none(asdict(symbol.to_deprecated())) for symbol in system_symbols]
                    grouped_symbols[step.step_uuid].extend(system_symbols_obj)
        if scope.parent:
            update_grouped_symbols(symbol_table.scope_mapper[scope.parent])

    current_scope = symbol_table.step_scope_mapper[form_composer.step_uuid_mapper[step_uuid].id]
    update_grouped_symbols(current_scope)
    return grouped_symbols
