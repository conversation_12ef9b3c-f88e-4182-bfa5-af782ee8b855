# mypy: disable-error-code="arg-type, return-value, func-returns-value, var-annotated"
from enum import Enum
from operator import attrgetter
from typing import cast, Dict, Optional, ClassVar, Type, Tuple, Union, List

from attrs import define, field
from loguru import logger
from result import Ok, Err

from robot_processor.business_order.condition.condition import BranchDict
from robot_processor.enums import StepType, AssigneeRule, DataType
from robot_processor.form.biz_config import biz_config
from robot_processor.form.models import Step, WidgetRef
from robot_processor.form.schemas import RpaArgOutputSchema, StepAutoJumpConfig
from robot_processor.rpa_service.models import Rpa
from robot_processor.client.conf import app_config
from robot_processor.form.models import StepAutoSkip, StepAutoRetry


@define(kw_only=True, frozen=True, slots=True)
class FormPublishCheckWidgetInfo:
    widget_info_uuid: WidgetRef = field()
    widget_collection_id: int = field()
    is_ref: bool = field()
    option_value: dict = field(default=dict)


@define(frozen=True, slots=True)
class CheckExtraInfo:
    field_type: Optional[str]
    field_display: Optional[str]
    check_field: Optional[Type[Enum]]


class CheckCategory(Enum):
    assistant = CheckExtraInfo(
        "assistant",
        "客服信息",
        Enum("CheckAssistant", ["not_set", "wrong_assignee_rule"]),
    )
    branch = CheckExtraInfo(
        "branch",
        "分支信息",
        Enum(
            "CheckBranch",
            [
                "not_set",
                "ref_invalid",
                "ref_not_set",
                "next_step_not_set",
                "next_step_invalid",
            ],
        ),
    )
    widget_collection = CheckExtraInfo(
        "widget_collection",
        "表单信息",
        Enum("CheckWidgetCollection", ["not_set", "ref_invalid"]),
    )
    task = CheckExtraInfo(
        "task",
        "RPA 任务",
        Enum("CheckTask", ["not_set", "argument_required", "ref_invalid"]),
    )
    step_flow = CheckExtraInfo(
        "step_flow",
        "步骤信息",
        Enum(
            "CheckStepFlow",
            [
                "not_set",
                "invalid_begin_step",
                "unreachable",
                "invalid_order",
                "have_loop",
            ],
        ),
    )
    display_rule = CheckExtraInfo(
        "display_rule",
        "显隐规则",
        Enum("CheckDisplayRule", ["not_set", "ref_invalid"]),
    )

    @property
    def field_type(self):
        return self.value.field_type

    @property
    def field_display(self):
        return self.value.field_display

    @property
    def check_field(self):
        return self.value.check_field


@define(kw_only=True, frozen=True, slots=True)
class PublishCheckDescription:
    """工单发布校验"""

    @define(kw_only=True, frozen=True, slots=True)
    class Metadata:
        step_id: Optional[int] = field()
        step_uuid: Optional[str] = field()
        step_type: Optional[str] = field()
        field_type: str = field()
        branch_id: Optional[str] = field()
        concept_key: Optional[Union[str, WidgetRef]] = field()
        argument_name: Optional[str] = field()
        widget_info_is_deleted: Optional[bool] = field()

    @define(kw_only=True, frozen=True, slots=True)
    class DisplayInfo:
        step_name: Optional[str] = field()
        field_display: str = field()
        error_display: str = field()

    @define(kw_only=True, frozen=True, slots=True)
    class StatsInfo:
        check_category: CheckCategory = field()
        check_field: str = field()

    metadata: Metadata = field()
    display_info: DisplayInfo = field()
    stats_info: StatsInfo = field()

    CheckCategory: ClassVar = CheckCategory

    @classmethod
    def form_step(
            cls,
            step: Optional["Step"],
            check_category: CheckCategory,
            check_field: str,
            field_type: Optional[str] = None,
            field_display: Optional[str] = None,
            error_display: str = "",
            branch_id: Optional[str] = None,
            concept_key: Optional[Union[str, WidgetRef]] = None,
            argument_name: Optional[str] = None,
            widget_info_is_deleted: Optional[bool] = None,
    ):
        if step:
            step_id = step.id
            step_uuid = step.step_uuid
            step_type = step.step_type.name
            step_name = step.name
        else:
            step_id = step_uuid = step_type = step_name = None  # type: ignore[assignment]

        return cls(
            metadata=PublishCheckDescription.Metadata(
                step_id=step_id,
                step_uuid=step_uuid,
                step_type=step_type,
                field_type=field_type or check_category.field_type,
                branch_id=branch_id,
                concept_key=concept_key,
                argument_name=argument_name,
                widget_info_is_deleted=widget_info_is_deleted,
            ),
            display_info=PublishCheckDescription.DisplayInfo(
                step_name=step_name,
                field_display=field_display or check_category.field_display,
                error_display=error_display,
            ),
            stats_info=PublishCheckDescription.StatsInfo(
                check_category=check_category, check_field=check_field
            ),
        )


@define(kw_only=True, frozen=True, slots=True)
class FormPublishCheck:
    """工单发布校验"""

    step: List[Step]
    widget_info: List[FormPublishCheckWidgetInfo]

    @property
    def begin(self):
        if len(self.step) == 0:
            return

        for each_step in self.step:
            if not each_step.prev_step_ids:
                return each_step
        else:
            return self.step[0]

    @property
    def step_uuid_map(self) -> Dict[str, Step]:
        return {each_step.step_uuid: each_step for each_step in self.step}

    def form_publish_check(self) -> List["PublishCheckDescription"]:
        check_description = [
            *self.FlowCheck.check_begin(self),
            *self.FlowCheck.check_reachable(self),
            *self.FlowCheck.check_loop(self),
            *self.FlowCheck.check_assignee_rule(self),
        ]
        for step in self.step:
            step_task = step.task
            check_description.extend(
                [
                    *self.StepCheck.check_assistant(self, step),
                    *self.StepCheck.check_jump_config(self, step),
                    *self.StepCheck.check_skip_and_retry_config(self, step),
                    *self.StepCheck.check_widget_collection(self, step),
                    *self.StepCheck.check_widget_collection_refer_widget_info(self, step),
                    *self.StepCheck.check_task(self, step, step_task),
                    *self.StepCheck.check_task_arguments(self, step, step_task),
                    *self.StepCheck.check_task_refer_widget_info(self, step, step_task),
                    *self.StepCheck.check_branch(self, step),
                    *self.StepCheck.check_branch_refer_widget_info(self, step),
                ]
            )

        return check_description

    class CommonCheck:
        @staticmethod
        def check_task_argument_required(task_arguments: List[dict], rpa_keymap: dict) -> List[Tuple[dict, dict]]:
            error_arguments = []

            for task_argument in RpaArgOutputSchema.filter_input_argument(task_arguments):
                # keymap中的引用也可能是 table 组件
                keymap_target = rpa_keymap.get(task_argument['name'])

                if not task_argument.get('required', False):
                    continue
                if task_argument['data_type'] == DataType.AUTO.value:
                    continue
                if keymap_target is None:
                    error_arguments.append((task_argument, None))
                elif isinstance(keymap_target, (list, str, dict)) and len(keymap_target) == 0:
                    error_arguments.append((task_argument, None))
                elif task_argument['data_type'] == DataType.MSG_RULES.value:  # branch 类型的有子参数需要校验
                    error_arguments.extend(
                        FormPublishCheck.StepTaskBranchArgumentCheck.check_branch_sub_task_argument_required(
                            task_argument, rpa_keymap
                        )
                    )

            return error_arguments

        @staticmethod
        def check_task_argument_content_required(task_arguments: List[dict],
                                                 rpa_keymap: dict) -> List[Tuple[dict, dict]]:
            error_segment = []

            for task_argument in RpaArgOutputSchema.filter_input_argument(task_arguments):
                if task_argument['data_type'] == DataType.MSG_RULES:
                    if rpa_keymap.get("skip") and task_argument.get("name") == "content":
                        continue

                    task_argument.get("arguments", []) and error_segment.extend(
                        FormPublishCheck.StepTaskBranchArgumentCheck.check_branch_sub_task_argument_content_required(
                            task_argument, rpa_keymap
                        )
                    )
                    continue

                keymap_target = rpa_keymap.get(task_argument['name'])

                if task_argument['data_type'] != DataType.INPUT_TEXTAREA:
                    continue
                if keymap_target is None:
                    continue
                if not isinstance(keymap_target, list):
                    continue

                for segment in keymap_target:
                    if segment.get("type") != "concept":
                        continue
                    if segment.get("concept_type") == "fixed":
                        continue
                    if not segment.get("key"):
                        error_segment.append((task_argument, segment))

            return error_segment

        @staticmethod
        def check_task_argument_branch_condition_required(task_arguments: List[dict], rpa_keymap: dict):
            error_argument_branch = []
            for task_argument in RpaArgOutputSchema.filter_input_argument(task_arguments):
                keymap_target = rpa_keymap.get(task_argument['name'])
                if keymap_target is None:
                    continue

                # 检查多规则分支条件
                branch_strategy = keymap_target.get("branch_status", "multiple")
                if branch_strategy == "single":  # 不是多分支，不用检查
                    return error_argument_branch

                send_method = keymap_target.get("send_method", "same_send_same")
                send_method_target = keymap_target.get(send_method, {})
                branch: List[dict] = send_method_target.get("multiple", [])
                for each_branch in FormPublishCheck.CommonCheck.check_branch_condition_required(branch):
                    error_argument_branch.append((task_argument, each_branch))

            return error_argument_branch

        @staticmethod
        def check_task_argument_content_refer(
                content: list[dict],
                valid_widget_info_uuid: list[str | WidgetRef]
        ) -> list[dict]:
            error_segment = []

            for segment in content:
                if segment.get("type") != "concept":
                    continue
                if segment.get("concept_type") == "fixed":
                    continue
                ref_widget_info_uuid = WidgetRef.parse(segment.get("key"))
                if ref_widget_info_uuid is None:
                    continue
                if ref_widget_info_uuid not in valid_widget_info_uuid:
                    error_segment.append(segment)

            return error_segment

        @staticmethod
        def check_task_argument_content_length(task: Rpa, rpa_keymap: dict) -> List[dict]:

            error_arguments = []

            if task.name not in biz_config.get_input_length_check_config().get("care_task", []):
                return error_arguments
            argument_name = biz_config.get_input_length_check_config().get(task.name, {}).get("name")
            max_length = biz_config.get_input_length_check_config().get(task.name, {}).get("max_length", 500)
            for argument in task.arguments:
                if argument["data_type"] != DataType.INPUT_TEXTAREA:
                    continue
                if argument['name'] != argument_name:
                    continue
                keymap_target = rpa_keymap.get(argument['name'])
                if keymap_target is None:
                    continue
                if not isinstance(keymap_target, list):
                    continue
                text = ""
                for segment in keymap_target:
                    if segment["type"] != "text":
                        continue
                    text += segment["text"]
                if len(text) > max_length:
                    error_arguments.append(argument)

            return error_arguments

        @staticmethod
        def check_branch_condition_required(branch: List[dict]) -> List[dict]:
            error_branch = []

            def check_condition_group_required(current_branch: dict, condition_group):
                if not condition_group:
                    return
                for condition in condition_group.get("condition_list", []):
                    if condition.get("type") == "single":
                        ref = condition.get("data", {}).get("ref")
                        if ref is None:
                            error_branch.append(current_branch)
                            return
                    else:
                        check_condition_group_required(
                            current_branch, condition.get("data")
                        )

            for each_branch in branch:
                check_condition_group_required(
                    each_branch, each_branch.get("condition_group")
                )

            return error_branch

        @staticmethod
        def check_branch_condition_refer(
                branch: list[dict],
                valid_widget_info_uuid: list[str | WidgetRef]
        ) -> list[dict]:
            error_branch = []

            for each_branch in branch:
                # todo: condition_group 里格式化出来的 WidgetRef 对象，当前必然是没有 widget_type 的。
                #  因此基本不可能在下列 valid_widget_info_uuid 中找到。
                widget_refs = FormPublishCheck.Util.extract_branch_widget_ref(each_branch.get("condition_group"))
                for each_ref in widget_refs:
                    # todo 这里的 valid_widget_info_uuid 仅有widget uuid 数据
                    #  后续还需要增加 widget.field 数据，才能真正校验table组件的引用
                    if each_ref not in valid_widget_info_uuid:
                        error_branch.append(each_branch)
                        break

            return error_branch

        @staticmethod
        def check_branch_condition_enum_options_refer(
                self: 'FormPublishCheck',
                branch_list: list[BranchDict]
        ):
            error_branch = []
            for branch in branch_list:
                if branch["type"] == "DEFAULT":
                    continue
                for condition in branch["condition_group"]["condition_list"]:
                    if "value_type" not in condition["data"]:
                        continue
                    if condition["data"]["value_type"] != "ENUM":
                        continue
                    if condition["data"]["operator"] not in (
                        "IN", "CONTAINS_ANY", "CONTAINS_ALL", "DISJOINT", "MATCH_ANY"
                    ):
                        continue
                    ref = WidgetRef.parse(condition["data"]["ref"])
                    options = FormPublishCheck.Util.get_enum_options(self, ref)
                    for target in condition["data"]["value"]:
                        if not FormPublishCheck.Util.check_enum_option_valid(options, target):
                            error_branch.append((branch, "/".join(target)))

            return error_branch

        @staticmethod
        def check_branch_argument_required(branch: List[dict], task_arguments: List[dict]) -> List[Tuple[dict, dict]]:
            error_branch_task_argument = []

            for each_branch in branch:
                wrong_task_arguments = (
                    FormPublishCheck.CommonCheck.check_task_argument_required(
                        task_arguments, each_branch
                    )
                )
                for (each_task_argument, _) in wrong_task_arguments:
                    error_branch_task_argument.append((each_task_argument, each_branch))

            return error_branch_task_argument

    class Util:
        @staticmethod
        def get_step_prev(self: "FormPublishCheck", step: Step, visited=None):
            visited = visited or []
            all_prev = dict()

            for prev_step_uuid in step.prev_step_ids:
                prev_step = self.step_uuid_map.get(prev_step_uuid)
                if not prev_step:
                    continue
                if prev_step_uuid in visited:
                    continue
                visited.append(prev_step_uuid)
                all_prev[prev_step_uuid] = prev_step
                all_prev.update(self.Util.get_step_prev(self, prev_step, visited))

            return all_prev

        @staticmethod
        def get_step_valid_widget_info_uuid(self: "FormPublishCheck", step: Step):
            prev_step = self.Util.get_step_prev(self, step).values()
            # rpa支持在当前步骤创建组件，需要把当前step也加上
            prev_and_self_steps = [*prev_step, step]
            prev_step_widget_collection_id = [
                str(each_step.widget_collection_id)
                for each_step in prev_and_self_steps
                if each_step.widget_collection_id
            ]
            valid_widget_info_uuid = [
                widget_info.widget_info_uuid
                for widget_info in self.widget_info
                if not widget_info.is_ref
                if str(widget_info.widget_collection_id) in prev_step_widget_collection_id
            ]

            return valid_widget_info_uuid

        @staticmethod
        def get_step_ref_widget_info_uuid(self: "FormPublishCheck", step: Step):
            ref_widget_info_uuid = [
                widget_info.widget_info_uuid
                for widget_info in self.widget_info
                if str(widget_info.widget_collection_id) == step.widget_collection_id
                if widget_info.is_ref
            ]

            return ref_widget_info_uuid

        @staticmethod
        def get_non_ref_widget_info(self: "FormPublishCheck"):
            non_ref_widget_info_uuid = [
                widget_info.widget_info_uuid
                for widget_info in self.widget_info
                if not widget_info.is_ref
            ]

            return non_ref_widget_info_uuid

        @staticmethod
        def extract_branch_widget_ref(condition_group: dict):
            widget_refs = []

            if not condition_group:  # default branch
                return widget_refs
            for condition in condition_group.get("condition_list", []):
                if condition.get("type") == "single":
                    if ref := condition.get("data", {}).get("ref"):
                        # 支持对table组件的引用
                        widget_refs.append(WidgetRef.parse(ref))
                else:
                    widget_refs.extend(
                        FormPublishCheck.Util.extract_branch_widget_ref(
                            condition.get("data", {})
                        )
                    )
            return widget_refs

        @staticmethod
        def task_argument_required_error_display(task_argument: dict, branch: Optional[dict] = None):

            if task_argument['data_type'] == DataType.SELECT:
                error_display_prefix = "请关联"
            elif task_argument['data_type'] in (
                DataType.INPUT_SELECT,
                DataType.INPUT_SELECT_EXTRA,
                DataType.INPUT_MULTI_SELECT_DROPDOWN,
                DataType.MSG_RULES,
            ):
                error_display_prefix = "请选择"
            else:
                error_display_prefix = "请输入"

            if task_argument['data_type'] == DataType.MSG_RULES:
                argument_label = "单/多消息规则配置"
            else:
                argument_label = task_argument.get("label", "")

            if not branch:
                error_display = f"{error_display_prefix}【{argument_label}】"
            else:
                error_display = (
                    f"{error_display_prefix}【{branch.get('name', '')}-{argument_label}】"
                )

            return error_display

        @staticmethod
        def get_enum_options(self: "FormPublishCheck", widget_ref: WidgetRef | str):
            widget_info = next(
                filter(lambda x: not x.is_ref and x.widget_info_uuid == widget_ref, self.widget_info),
                None
            )
            if not widget_info:
                return []
            return widget_info.option_value.get("options", [])

        @staticmethod
        def check_enum_option_valid(options: list[dict], option: list[str]):
            target_options = options
            for each_level_option in option:
                target_option = next(
                    filter(lambda x: x["value"] == each_level_option, target_options),
                    None
                )
                if target_option is None:
                    return False
                target_options = target_option.get("children", [])
            return True

        @staticmethod
        def extract_msg_rules_sub_arguments(task_argument: dict):
            return [
                *task_argument.get("commons", []),
                *task_argument.get("arguments", []),
                *task_argument.get("optionals", []),
            ]

    class TaskArgumentCheck:
        @staticmethod
        def check_refer(
                self: 'FormPublishCheck',
                step: 'Step',
                keymap: dict,
                task_argument: dict,
        ):
            check_category = PublishCheckDescription.CheckCategory.task
            check_field = "ref_invalid"
            argument_name = task_argument["name"]
            argument_label = task_argument.get("label", "")
            if not (configured_ref := keymap.get(argument_name)):
                return Ok(None)

            valid = self.Util.get_step_valid_widget_info_uuid(self, step)
            non_ref = self.Util.get_non_ref_widget_info(self)
            check_description = cast(list[PublishCheckDescription], [])

            match task_argument["data_type"]:
                case DataType.SELECT:
                    widget_info_key = cast(str | WidgetRef, WidgetRef.parse(configured_ref))
                    if widget_info_key not in valid:
                        error_display = f"参数[{argument_label}]引用的组件不存在"
                        refer_is_deleted = widget_info_key not in non_ref
                        check_description.append(PublishCheckDescription.form_step(
                            step,
                            check_category,
                            check_field,
                            error_display=error_display,
                            argument_name=argument_name,
                            concept_key=widget_info_key,
                            widget_info_is_deleted=refer_is_deleted,
                        ))

                case DataType.INPUT_TEXTAREA:
                    content = cast(list[dict], configured_ref)
                    for segment in self.CommonCheck.check_task_argument_content_refer(
                        content, valid
                    ):
                        segment_label = segment.get("label", segment.get("key"))
                        segment_key = cast(str | WidgetRef, WidgetRef.parse(segment.get("key")))
                        refer_is_deleted = segment_key not in non_ref
                        error_display = "参数[{}]引用的组件[{}]不存在".format(
                            argument_label, segment_label
                        )
                        check_description.append(PublishCheckDescription.form_step(
                            step,
                            check_category,
                            check_field,
                            error_display=error_display,
                            argument_name=argument_name,
                            concept_key=segment_key,
                            widget_info_is_deleted=refer_is_deleted,
                        ))

                case DataType.MSG_RULES:
                    msg_rule = cast(dict, configured_ref)
                    sub_arguments = self.Util.extract_msg_rules_sub_arguments(task_argument)

                    try:
                        match msg_rule.get("branch_status"), msg_rule.get("send_method"):
                            case "single", _:  # 仅检查 textarea
                                configured_keymap = msg_rule.get("single", {})
                                for sub_argument in sub_arguments:
                                    self.TaskArgumentCheck.check_refer(
                                        self, step, configured_keymap, sub_argument
                                    ).or_else(check_description.extend)

                            case "multiple", "different_send_different":
                                configured_keymap_list = msg_rule.get(
                                    "different_send_different", {}).get("multiple", [])
                                for configured_keymap in configured_keymap_list:
                                    for sub_argument in sub_arguments:
                                        self.TaskArgumentCheck.check_refer(
                                            self, step, configured_keymap, sub_argument
                                        ).or_else(check_description.extend)

                            case "multiple", "same_send_same":
                                # branch 的配置 和 keymap 的配置在一个 dict 里
                                configured_keymap_list = msg_rule.get(
                                    "same_send_same", {}).get("multiple", [])
                                for configured_keymap in configured_keymap_list:
                                    for sub_argument in sub_arguments:
                                        self.TaskArgumentCheck.check_refer(
                                            self, step, configured_keymap, sub_argument
                                        ).or_else(check_description.extend)

                    except Exception as e:
                        # do nothing because of branch feature in staging
                        logger.opt(exception=e).error("校验消息规则参数引用组件失败")

            if check_description:
                return Err(check_description)
            else:
                return Ok(None)

    class StepTaskBranchArgumentCheck:
        """新增了 StepTaskArgumentType.BRANCH 类型，但是太复杂了，校验逻辑拿出来"""

        @staticmethod
        def check_branch_sub_task_argument_required(task_argument: dict,
                                                    rpa_keymap: dict) -> List[Tuple[dict, dict]]:

            error_arguments = []

            target_keymap = rpa_keymap.get(task_argument['name'])
            if target_keymap is None:
                return error_arguments

            branch_strategy = target_keymap.get("branch_status", "multiple")
            if branch_strategy == "single":
                sub_keymap = target_keymap.get("single", {})
                check_arguments = []
                task_argument.get("optionals", []) and check_arguments.extend(task_argument["optionals"])
                task_argument.get("commons", []) and check_arguments.extend(task_argument["commons"])
                task_argument.get("arguments", []) and check_arguments.extend(task_argument["arguments"])
                error_arguments.extend(
                    FormPublishCheck.CommonCheck.check_task_argument_required(
                        check_arguments, sub_keymap
                    )
                )

            elif branch_strategy == "multiple":
                send_method = target_keymap.get("send_method", "same_send_same")
                sub_keymap = target_keymap.get(send_method, {})
                check_arguments = []  # 仅判断非分支依赖的参数
                branch_check_arguments = []  # 分支依赖的参数
                for argument in task_argument.get("optionals", []):
                    depend = [condition["value"] for condition in argument.get("condition", [])]
                    if send_method in depend:
                        branch_check_arguments.append(argument)
                    else:
                        check_arguments.append(argument)
                error_arguments.extend(
                    FormPublishCheck.CommonCheck.check_task_argument_required(
                        check_arguments, sub_keymap
                    )
                )
                error_arguments.extend(
                    FormPublishCheck.CommonCheck.check_branch_argument_required(
                        sub_keymap.get("multiple", []), branch_check_arguments
                    )
                )

            return error_arguments

        @staticmethod
        def check_branch_sub_task_argument_content_required(task_argument: dict, rpa_keymap: dict) \
                -> List[Tuple[dict, dict]]:
            error_segment = []

            target_keymap = rpa_keymap.get(task_argument['name'])
            if target_keymap is None:
                return error_segment

            branch_strategy = target_keymap.get("branch_status", "multiple")
            if branch_strategy == "single":
                sub_keymap = target_keymap.get("single", {})
                check_arguments = []
                task_argument.get("optionals", []) and check_arguments.extend(task_argument['optionals'])
                task_argument.get("commons", []) and check_arguments.extend(task_argument['commons'])
                task_argument.get("arguments", []) and check_arguments.extend(task_argument['arguments'])
                error_segment.extend(
                    FormPublishCheck.CommonCheck.check_task_argument_content_required(
                        check_arguments, sub_keymap
                    )
                )

            elif branch_strategy == "multiple":
                send_method = target_keymap.get("send_method", "same_send_same")
                sub_keymap_set = target_keymap.get(send_method, {}).get("multiple", [])
                check_arguments = [
                    *(task_argument.get("arguments", [])),
                    *(task_argument.get("commons", [])),
                    *(task_argument.get("optionals", [])),
                ]
                for sub_keymap in sub_keymap_set:
                    error_segment.extend(
                        FormPublishCheck.CommonCheck.check_task_argument_content_required(
                            check_arguments, sub_keymap
                        )
                    )

            return error_segment

    class StepCheck:
        @staticmethod
        def check_assistant(self: "FormPublishCheck", step: Step):
            check_description = []
            check_category = PublishCheckDescription.CheckCategory.assistant
            check_field = check_category.check_field.not_set.name

            if step.step_type != StepType.human:
                return check_description

            if not step.assistants_v2:
                check_description.append(
                    PublishCheckDescription.form_step(
                        step, check_category, check_field, error_display="缺少客服信息"
                    )
                )

            return check_description

        @staticmethod
        def check_skip_and_retry_config(self: "FormPublishCheck", step: Step):
            check_description = []
            check_category = PublishCheckDescription.CheckCategory.step_flow
            check_field = check_category.check_field.not_set.name

            auto_skip_config: StepAutoSkip | None = StepAutoSkip.query.filter(
                StepAutoSkip.step_uuid == step.step_uuid
            ).first()

            auto_retry_config: StepAutoRetry | None = StepAutoRetry.query.filter(
                StepAutoRetry.step_uuid == step.step_uuid
            ).first()

            if auto_skip_config is not None and auto_retry_config is not None:
                if auto_skip_config.can_skip and auto_retry_config.can_retry:
                    check_description.append(
                        PublishCheckDescription.form_step(
                            step, check_category, check_field, error_display="不能同时开启自动跳过和自动重试"
                        )
                    )
            return check_description

        @staticmethod
        def check_jump_config(self: "FormPublishCheck", step: Step):
            """
            检测跳转步骤的配置信息。
            :param self:
            :param step:
            :return:
            """
            check_description = []
            check_category = PublishCheckDescription.CheckCategory.step_flow
            check_field = check_category.check_field.not_set.name

            if step.step_type != StepType.jump:  # 非跳转步骤则直接返回成功。
                return check_description

            auto_jump_config: StepAutoJumpConfig = step.get_auto_jump_config()
            if auto_jump_config.target is None:
                check_description.append(
                    PublishCheckDescription.form_step(
                        step, check_category, check_field, error_display="缺少需要跳转到的步骤"
                    )
                )
                return check_description

            # 如果需要跳转到的步骤被删除或尚未保存，也不允许发布。
            jump_to_step: Step | None = Step.query.filter(
                Step.form_id == step.form_id,
                Step.step_uuid == auto_jump_config.target,
                Step.deleted.is_(False),
            ).order_by(Step.id.desc()).first()
            if jump_to_step is None:
                check_description.append(
                    PublishCheckDescription.form_step(
                        step, check_category, check_field, error_display="需要跳转的步骤已被删除或尚未保存"
                    )
                )
                return check_description

            # 检测是否开启跳转限制
            if not auto_jump_config.limit_enable:
                if jump_to_step.step_type not in [
                    StepType.human,
                    StepType.approve
                ]:
                    check_description.append(
                        PublishCheckDescription.form_step(
                            step, check_category, check_field, error_display="跳转至非人工步骤必须开启自动跳转次数限制"
                        )
                    )
                    return check_description
            else:
                match auto_jump_config.limit_type:
                    # 检测跳转次数限制
                    case StepAutoJumpConfig.LimitType.COUNT:
                        if auto_jump_config.limit_max_times is None:
                            check_description.append(
                                PublishCheckDescription.form_step(
                                    step, check_category, check_field, error_display="未配置次数限制"
                                )
                            )
                        elif auto_jump_config.limit_max_times is not None \
                                and auto_jump_config.limit_max_times > app_config.JUMP_CONFIG_MAX_TIMES:
                            check_description.append(
                                PublishCheckDescription.form_step(
                                    step, check_category, check_field, error_display="超出次数限制上限"
                                )
                            )
                        return check_description
                    # 检测跳转时间限制
                    case StepAutoJumpConfig.LimitType.DATE:
                        if auto_jump_config.limit_range is None:
                            check_description.append(
                                PublishCheckDescription.form_step(
                                    step, check_category, check_field, error_display="未配置时间限制"
                                )
                            )
                        elif auto_jump_config.limit_range is not None \
                                and auto_jump_config.limit_range > app_config.JUMP_CONFIG_MAX_TIME_RANGE:
                            check_description.append(
                                PublishCheckDescription.form_step(
                                    step, check_category, check_field, error_display="超出时间限制上限"
                                )
                            )
                        return check_description

            # 检测跳转时间间隔
            if auto_jump_config.interval is None:
                if jump_to_step.step_type not in [
                        StepType.human,
                        StepType.approve
                ]:
                    check_description.append(
                        PublishCheckDescription.form_step(
                            step, check_category, check_field, error_display="未配置跳转时间间隔"
                        )
                    )
            elif auto_jump_config.interval < app_config.JUMP_CONFIG_MIN_INTERVAL:
                check_description.append(
                    PublishCheckDescription.form_step(
                        step, check_category, check_field, error_display="低于跳转时间间隔的下限"
                    )
                )

            return check_description

        @staticmethod
        def check_widget_collection(self: "FormPublishCheck", step: Step):
            check_description = []
            check_category = PublishCheckDescription.CheckCategory.widget_collection
            check_field = check_category.check_field.not_set.name

            if step.step_type != StepType.human:
                return check_description

            if not step.widget_collection_id:
                check_description.append(
                    PublishCheckDescription.form_step(
                        step, check_category, check_field, error_display="缺少表单信息"
                    )
                )

            return check_description

        @staticmethod
        def check_widget_collection_refer_widget_info(self: "FormPublishCheck", step: Step):
            check_description = []
            check_category = PublishCheckDescription.CheckCategory.widget_collection
            check_field = check_category.check_field.ref_invalid.name

            ref_widget_info_uuid = self.Util.get_step_ref_widget_info_uuid(self, step)

            valid_widget_info_uuid = self.Util.get_step_valid_widget_info_uuid(self, step)

            if step.step_type != StepType.human:
                return check_description

            for each_ref in ref_widget_info_uuid:
                if each_ref not in valid_widget_info_uuid:
                    check_description.append(
                        PublishCheckDescription.form_step(
                            step,
                            check_category,
                            check_field,
                            error_display="表单引用的组件不存在",
                        )
                    )
            return check_description

        @staticmethod
        def check_task(self: "FormPublishCheck", step: Step, step_task: Optional[Rpa]):
            check_description = []
            check_category = PublishCheckDescription.CheckCategory.task
            check_field = check_category.check_field.not_set.name

            if step.step_type != StepType.auto:
                return check_description

            if not step_task:
                check_description.append(
                    PublishCheckDescription.form_step(
                        step, check_category, check_field, error_display="缺少任务信息"
                    )
                )
            return check_description

        @staticmethod
        def check_task_arguments(self: "FormPublishCheck", step: Step, step_task: Optional[Rpa]):

            check_description = []
            check_category = PublishCheckDescription.CheckCategory.task
            check_field = check_category.check_field.argument_required.name

            if step.step_type != StepType.auto:
                return check_description
            if not step_task:
                return check_description

            rpa_keymap = step.key_map or {}

            # 任务参数必填校验
            task_argument_has_wrong = self.CommonCheck.check_task_argument_required(step_task.arguments, rpa_keymap)
            for task_argument, relate_branch in task_argument_has_wrong:
                error_display = (
                    FormPublishCheck.Util.task_argument_required_error_display(
                        task_argument, relate_branch
                    )
                )
                check_description.append(
                    PublishCheckDescription.form_step(
                        step,
                        check_category,
                        check_field,
                        argument_name=task_argument["name"],
                        error_display=error_display,
                    )
                )

            segment_has_wrong = self.CommonCheck.check_task_argument_content_required(
                step_task.arguments, rpa_keymap
            )
            for task_argument, segment in segment_has_wrong:
                error_display = (
                    f"参数[{task_argument.get('label', '')}]" f"引用的组件[{segment.get('label', '')}]不存在"
                )
                check_description.append(
                    PublishCheckDescription.form_step(
                        step,
                        check_category,
                        check_field,
                        error_display=error_display,
                        argument_name=task_argument["name"],
                        widget_info_is_deleted=False,
                    )
                )

            # 富文本框长度校验
            task_argument_exceed_length = self.CommonCheck.check_task_argument_content_length(step_task, rpa_keymap)
            for task_argument in task_argument_exceed_length:
                check_description.append(
                    PublishCheckDescription.form_step(
                        step,
                        check_category,
                        check_field,
                        error_display="订单备注超出字符限制",
                        argument_name=task_argument['name'],
                        widget_info_is_deleted=False,
                    )
                )

            # 多分支类型任务分支条件校验
            branch_type_task_arguments = [
                task_argument
                for task_argument in RpaArgOutputSchema.filter_input_argument(step_task.arguments)
                if task_argument["data_type"] == DataType.MSG_RULES
            ]
            branch_type_task_arguments_has_wrong = FormPublishCheck.CommonCheck.\
                check_task_argument_branch_condition_required(branch_type_task_arguments, rpa_keymap)
            for task_argument, branch in branch_type_task_arguments_has_wrong:
                branch_name = branch.get("name", "")
                check_description.append(
                    PublishCheckDescription.form_step(
                        step,
                        check_category,
                        check_field,
                        argument_name=task_argument['name'],
                        error_display=f"参数【单/多消息规则配置】的分支【{branch_name}】条件未设置",
                    )
                )

            return check_description

        @staticmethod
        def check_task_refer_widget_info(self: "FormPublishCheck", step: Step, step_task: Optional[Rpa]):
            check_description = []
            check_category = PublishCheckDescription.CheckCategory.task
            check_field = "ref_invalid"

            if step.step_type != StepType.auto:
                return check_description
            if not step_task:
                return check_description

            rpa_keymap = step.key_map or {}
            for task_argument in RpaArgOutputSchema.filter_input_argument(step_task.arguments):
                (self.TaskArgumentCheck.check_refer(self, step, rpa_keymap, task_argument)
                 .or_else(check_description.extend))

                if task_argument["data_type"] == DataType.MSG_RULES:
                    msg_rule = cast(dict, rpa_keymap.get(task_argument["name"]))
                    match msg_rule.get("branch_status"), msg_rule.get("send_method"):
                        case "multiple", "different_send_different":
                            configured_branch_list = msg_rule.get(
                                "different_send_different", {}
                            ).get("multiple", [])
                        case "multiple", "same_send_same":
                            configured_branch_list = msg_rule.get(
                                "same_send_same", {}
                            ).get("multiple", [])
                        case _:
                            continue

                    for branch, option in self.CommonCheck.check_branch_condition_enum_options_refer(
                            self, configured_branch_list
                    ):
                        branch_name = branch.get("name", "")
                        branch_id = branch.get("id", "")
                        check_description.append(
                            PublishCheckDescription.form_step(
                                step,
                                check_category,
                                check_field,
                                error_display=f"分支[{branch_name}]的条件值引用的枚举选项[{option}]不存在",
                                branch_id=branch_id,
                            )
                        )

            return check_description

        @staticmethod
        def check_branch(self: "FormPublishCheck", step: Step):
            check_description = []
            check_category = PublishCheckDescription.CheckCategory.branch
            check_field = check_category.check_field
            check_field_not_set = check_field.not_set.name
            check_field_next_step_not_set = check_field.next_step_not_set.name
            check_field_next_step_invalid = check_field.next_step_invalid.name

            # 目前仅检测排他网关和遍历网关开始节点的 branch。
            if step.step_type not in [
                StepType.exclusive_gateway,
                StepType.iterate_gw_begin,
            ]:
                return check_description

            if not step.branch:
                check_description.append(
                    PublishCheckDescription.form_step(
                        step,
                        check_category,
                        check_field=check_field_not_set,
                        error_display="缺少分支",
                    )
                )
                return check_description

            for each_branch in step.branch:
                branch_name = each_branch.get("name", "")
                branch_id = each_branch.get("id", "")
                branch_next_step_uuid = each_branch.get("next")
                if not branch_next_step_uuid:
                    check_description.append(
                        PublishCheckDescription.form_step(
                            step,
                            check_category,
                            check_field=check_field_next_step_not_set,
                            error_display=f"分支[{branch_name}]缺少后续步骤",
                            branch_id=branch_id,
                        )
                    )
                    continue
                if branch_next_step_uuid not in step.next_step_ids:
                    check_description.append(
                        PublishCheckDescription.form_step(
                            step,
                            check_category,
                            check_field=check_field_next_step_invalid,
                            error_display=f"分支[{branch_name}]后续步骤不匹配",
                            branch_id=branch_id,
                        )
                    )

            return check_description

        @staticmethod
        def check_branch_refer_widget_info(self: "FormPublishCheck", step: Step):
            check_description = []
            check_category = PublishCheckDescription.CheckCategory.branch
            check_field = check_category.check_field.ref_invalid.name

            if step.step_type != StepType.exclusive_gateway:
                return check_description

            if not step.branch:
                return check_description

            valid_widget_info_uuid = self.Util.get_step_valid_widget_info_uuid(self, step)

            branch_has_wrong = self.CommonCheck.check_branch_condition_refer(step.branch, valid_widget_info_uuid)
            for each_branch in branch_has_wrong:
                branch_name = each_branch.get("name", "")
                branch_id = each_branch.get("id", "")
                check_description.append(
                    PublishCheckDescription.form_step(
                        step,
                        check_category,
                        check_field,
                        error_display=f"分支[{branch_name}]引用的组件不存在",
                        branch_id=branch_id,
                    )
                )

            for branch, option in self.CommonCheck.check_branch_condition_enum_options_refer(self, step.branch):
                branch_name = branch.get("name", "")
                branch_id = branch.get("id", "")
                check_description.append(
                    PublishCheckDescription.form_step(
                        step,
                        check_category,
                        check_field,
                        error_display=f"分支[{branch_name}]的条件值引用的枚举选项[{option}]不存在",
                        branch_id=branch_id,
                    )
                )

            return check_description

    class FlowCheck:
        @staticmethod
        def check_begin(self: "FormPublishCheck") -> List["PublishCheckDescription"]:
            check_category = CheckCategory.step_flow
            check_field = check_category.check_field.invalid_begin_step.name
            begin_step_count = 0

            begin_step = []  # 没有 prev 的 step

            for each_step in self.step:
                if not each_step.prev_step_ids:
                    begin_step_count += 1
                    begin_step.append(each_step)

            if begin_step_count == 0:
                return [
                    PublishCheckDescription.form_step(
                        None,
                        check_category,
                        check_field,
                        error_display="缺少开始节点",
                    )
                ]
            elif begin_step_count > 1:
                true_begin_step = sorted(begin_step, key=attrgetter("id"))[0]  # 业务定义 id 最小的那个是真正的开始步骤
                begin_step_name = "、".join(
                    map(
                        lambda step_name: f"【{step_name}】",
                        [
                            step.name
                            for step in begin_step
                            if step.name != true_begin_step.name  # 列出多余步骤，所以要过滤真正的开始步骤
                        ],
                    )
                )
                return [
                    PublishCheckDescription.form_step(
                        None,
                        check_category,
                        check_field,
                        error_display=f"只能有一个开始节点，请检查多余的步骤：{begin_step_name}",
                    )
                ]
            return []

        @staticmethod
        def check_reachable(self: "FormPublishCheck") -> List[PublishCheckDescription]:
            """检查流程步骤的前后关系和是否可达"""
            check_description = []
            check_category = CheckCategory.step_flow
            check_field_unreachable = check_category.check_field.unreachable.name
            check_field_invalid_order = check_category.check_field.invalid_order.name
            step_uuid_map = self.step_uuid_map
            walked = {step_uuid: False for step_uuid in step_uuid_map}

            def walk_down(step: Step):
                if not step.prev_step_ids:
                    walked[step.step_uuid] = True
                for next_step_uuid in step.next_step_ids:
                    next_step = step_uuid_map.get(next_step_uuid)
                    if not next_step:
                        check_description.append(
                            PublishCheckDescription.form_step(
                                step,
                                check_category,
                                check_field=check_field_invalid_order,
                                error_display=f"后续步骤不存在[{next_step_uuid}]",
                            )
                        )
                        continue
                    walked[next_step_uuid] = True
                    if step.step_uuid not in next_step.prev_step_ids:
                        check_description.append(
                            PublishCheckDescription.form_step(
                                next_step,
                                check_category,
                                check_field=check_field_invalid_order,
                                error_display=f"步骤的前序步骤和步骤[{step.name}]不匹配",
                            )
                        )

            for each_step in self.step:
                walk_down(each_step)

            unreachable_step = [
                step_uuid_map[step_uuid]
                for step_uuid, is_walked in walked.items()
                if not is_walked
            ]
            for each_step in unreachable_step:
                check_description.append(
                    PublishCheckDescription.form_step(
                        each_step,
                        check_category,
                        check_field=check_field_unreachable,
                        error_display="步骤不可达",
                    )
                )

            return check_description

        @staticmethod
        def check_loop(self: "FormPublishCheck") -> List[PublishCheckDescription]:
            """检查流程是否有环"""
            check_description = []
            check_category = CheckCategory.step_flow
            check_field = check_category.check_field.have_loop.name
            step_uuid_map = self.step_uuid_map
            walked = {
                step_uuid: len(set(step.prev_step_ids))
                for step_uuid, step in step_uuid_map.items()
            }

            if not self.begin:
                return check_description

            for step in self.step:
                for next_step_uuid in set(step.next_step_ids or []):
                    next_step = step_uuid_map.get(next_step_uuid)
                    if not next_step:
                        continue
                    if walked[next_step_uuid] < 0:  # 后续步骤已经被访问过了，可能会是一个环
                        next_step_ident = (
                            next_step.name if next_step else next_step_uuid
                        )
                        check_description.append(
                            PublishCheckDescription.form_step(
                                step,
                                check_category,
                                check_field,
                                error_display=f"步骤[{step.name}]与[{next_step_ident}]之间存在循环",
                            )
                        )
                    else:
                        walked[next_step_uuid] -= 1

            return check_description

        @staticmethod
        def check_assignee_rule(self: "FormPublishCheck") -> List[PublishCheckDescription]:
            check_description = []
            check_category = CheckCategory.assistant
            check_field = check_category.check_field.wrong_assignee_rule.name
            step_uuid_map = self.step_uuid_map
            walked = {step_uuid: False for step_uuid in step_uuid_map}

            def walk_down(step: Step):
                if step.step_type == StepType.human:
                    return

                for next_step_uuid in step.next_step_ids:
                    if next_step_uuid not in walked:
                        continue
                    if walked[next_step_uuid]:
                        continue
                    next_step = step_uuid_map[next_step_uuid]
                    if next_step.assignee_rule == AssigneeRule.MANUAL:
                        check_description.append(
                            PublishCheckDescription.form_step(
                                next_step,
                                check_category,
                                check_field,
                                error_display="不允许设置手动分派逻辑",
                            )
                        )

            for each_step in self.step:
                walk_down(each_step)

            return check_description


def form_publish_check(form_id: int) -> List["PublishCheckDescription"]:
    from robot_processor.form.models import WidgetInfo, Widget, Step

    steps = Step.Queries.steps(form_id=form_id, deleted=False).all()
    widget_collection_id = [
        each_step.widget_collection_id
        for each_step in steps
        if each_step.widget_collection_id
    ]
    widget_collection_info = WidgetInfo.find_by_collection_id(widget_collection_id)
    # 查询 widget。
    widget_ids = [wci.widget_id for wci in widget_collection_info]
    widgets = Widget.query.filter(Widget.id.in_(widget_ids)).all()
    widget_schema_maps = {w.id: w.schema for w in widgets}
    widget_info_list = []

    def add_widget_info_to_widget_info_list(widget_info_: WidgetInfo):
        widget_schema = widget_schema_maps.get(widget_info.widget_id) or {}
        formatted = FormPublishCheckWidgetInfo(
            widget_info_uuid=WidgetRef(
                key=widget_info_.key,
                type=widget_schema.get("type") or "table",  # 线上数据已清洗，仅部分电商组件为 table 的未更新
                widget_type=widget_schema.get("widget_type"),
                field=None,
                multi_row=False,
            ),
            widget_collection_id=widget_info_.widget_collection_id,
            is_ref=bool(widget_info.before),
            option_value=dict(widget_info_.option_value),
        )
        widget_info_list.append(formatted)
        if "fields" not in widget_info_.option_value:
            return
        for sub_field in widget_info_.option_value["fields"]:
            sub_widget_info = WidgetInfo()
            sub_widget_info.key = sub_field["key"]
            sub_widget_info.widget_id = sub_field["id"]
            sub_widget_info.option_value = sub_field.get("option_value", {})
            sub_widget_info.before = sub_field["before"]
            sub_widget_info.widget_collection_id = widget_info_.widget_collection_id
            add_widget_info_to_widget_info_list(sub_widget_info)

    for widget_info in widget_collection_info:
        add_widget_info_to_widget_info_list(widget_info)

    return FormPublishCheck(step=steps, widget_info=widget_info_list).form_publish_check()
