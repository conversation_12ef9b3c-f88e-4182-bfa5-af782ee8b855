from enum import StrEnum
from functools import wraps
from typing import Any
from typing import ClassVar
from typing import Dict
from typing import List
from typing import Literal
from typing import Optional
from typing import Union

from pydantic import BaseModel
from pydantic import Field
from pydantic import root_validator
from robot_types.core import Filter
from robot_types.core import TypeSpec
from robot_types.core import Value
from robot_types.helper import deserialize
from typing_extensions import NotRequired
from typing_extensions import Required
from typing_extensions import TypedDict

from robot_processor.enums import DataType
from robot_processor.enums import WidgetValueUniqueCheckType
from robot_processor.form.types import WidgetDataType
from robot_processor.utils import filter_none


class _RpaArgLimit(TypedDict, total=False):
    widget_type: Required[str]  # 组件类型
    mode: NotRequired[str]  # （可选）组件选定的模式


class RpaInputOption(BaseModel):
    desc: Optional[str] = None
    type: Optional[str] = None
    label: Optional[str] = None
    value: Union[None, str, Dict[str, Any], float, int, bool] = None  # data_type ==
    # 6时，使用Dict
    tips: Optional[str] = None
    mode: Optional[str] = None
    widget_type: Optional[str] = None

    class Config:
        smart_union = True


class RpaDataBinding(BaseModel):
    """数据绑定规则"""

    EXPRESSION_SEPARATOR: ClassVar[str] = "."

    expression: str
    level: str

    @property
    def depth(self):
        """数据的层级深度

        因 object 类型可以打平，所以需要不需要计算层级深度
        """
        return len(self.level.split(self.EXPRESSION_SEPARATOR))

    @property
    def full_expression_parts(self):
        return self.expression.split(self.EXPRESSION_SEPARATOR)

    @property
    def last_non_list_index(self):
        """找到最后一个非 list 的节点索引"""
        for index, part in enumerate(self.full_expression_parts[::-1]):
            # jmespath 的 expression 使用 []
            if part.endswith("[]"):
                _last_non_list_index = -index if index != 0 else len(self.full_expression_parts)
                break
        else:
            _last_non_list_index = -1

        return _last_non_list_index


class RpaArgInputSchema(BaseModel):
    """RPA 表单配置，输入的参数"""

    # 通用字段
    name: str = Field(description="对应 keymap 中的 key")
    label: str = Field(description="展示在用户界面的名称")
    desc: Optional[str] = Field(description="展示在用户界面的描述")
    data_type: DataType = Field(
        description="展示在用户界面的组件样式。会影响："
        "1. 运行时的取值方式，由 robot_processor.job.job_model_wrapper.JobArguments.get_arg_by_task_arg_name 维护;"
        "2. 用户界面的组件样式;"
    )
    limits: Optional[List[_RpaArgLimit]] = Field(description="Self.data_type=DataType.SELECT 时，可以绑定的组件类型。")
    required: bool = Field(default=False, description="是否必填")
    default_value: Optional[str | float | int] = Field(description="默认值")
    options: Optional[List[RpaInputOption]] = Field(description="data_type为4/6/7/9时需要指定下拉options")

    # 发消息专用(data_type = 10)
    arguments: Optional[List["RpaArgInputSchema"]] = Field(default=None)
    optionals: Optional[List["RpaArgInputSchema"]] = Field(default=None)
    commons: Optional[List["RpaArgInputSchema"]] = Field(default=None)
    condition: Optional[List[Dict[str, Any]]] = Field(default=None)
    send_method: Optional[List[Dict[str, Any]]] = Field(default=None)

    class Config:
        extra = "allow"

    @classmethod
    def generate_constants(cls, data: Union["RpaArgInputSchema", dict]) -> "RpaArgInputSchema":
        # 当且仅当data_type == 10时，send_method是固定值
        # 当且仅当data_type == 10时，options是固定值
        if isinstance(data, dict):
            ret = RpaArgInputSchema.parse_obj(data)
        else:
            ret = data
        if ret.data_type == DataType.MSG_RULES:
            ret.send_method = [
                {
                    "info": "多规则消息由同一账号发送",
                    "tips": "由同一个账号按照多消息的规则匹配来发送消息",
                    "label": "同一账号发送",
                    "value": "same_send_same",
                },
                {
                    "info": "多规则消息由不同账号发送",
                    "tips": "在不同的消息规则下可配置不同的账号来发送消息",
                    "label": "不同账号发送",
                    "value": "different_send_same",
                },
            ]
            ret.options = [
                RpaInputOption(tips="单消息发送", label="单消息规则配置", value="single"),
                RpaInputOption(tips="多消息发送", label="多消息规则配置", value="multiple"),
            ]
        return ret


class RpaArgOutputSchema(BaseModel):
    """RPA 表单配置，输出的参数"""

    label: Optional[str] = Field(description="展示在用户界面的名称", default=None)
    name: str = Field(description="实际的key")
    desc: Optional[str] = Field(description="展示在用户界面的描述", default=None)
    default_type: WidgetDataType = Field(description="默认的组件")
    allow_types: List[WidgetDataType] = Field(description="允许的组件类型")
    children: Optional[List["RpaArgOutputSchema"]] = Field(description="如果 Self.default_type=WidgetType.TABLE，则可以配置子组件")
    data_binding: RpaDataBinding = Field(description="数据绑定规则")
    # rpa执行时不需要不关心selected字段，这个字段仅仅用于和运营中台前端交互使用
    # 什么时候会设置selected字段? 运营中台获取rpa详情时，后端通过计算代码支持的output,和rpa实际存储的output比较
    # 这两份数据的交集会被设置成True
    selected: bool = Field(description="是否选择为输出", default=False)

    @classmethod
    def check_if_output_argument_obj(cls, obj: dict):
        """检查是否是 RpaArgOutputSchema 的实例"""
        return "allow_types" in obj

    @classmethod
    def filter_output_argument(cls, task_arguments: List[dict]):
        return list(filter(cls.check_if_output_argument_obj, task_arguments))

    @classmethod
    def filter_input_argument(cls, task_arguments: List[dict]):
        return list(filter(lambda obj: not cls.check_if_output_argument_obj(obj), task_arguments))


class StepAutoJumpConfig(BaseModel):
    """
    跳转步骤的配置信息。
    """

    class LimitType(StrEnum):
        DATE = "date"
        COUNT = "count"

    # 需要跳转到的步骤的 step_uuid
    target: str | None
    # 是否开启跳转限制
    limit_enable: bool = False
    # 跳转的时间限制
    limit_range: int | None
    # 跳转的次数限制
    limit_max_times: int | None
    # 跳转的时间间隔
    interval: int | None
    # 是否开启重试后重新计数
    auto_retry: bool = False
    # 是否开启第一次执行需要按照设置的延时进行等待
    waiting_on_first_execution: bool = True
    # 启用的配置的类型
    limit_type: LimitType = LimitType.COUNT


class StepValidationCondition(BaseModel):
    """表单校验规则"""

    # ValidationConfig 给前端使用，不能对 jmespath expression 进行 normalize 处理
    a: dict
    o: list
    b: Optional[dict]

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        return filter_none(super().dict(**kwargs))


class StepValidationRule(BaseModel):

    name: str
    conditions: list[StepValidationCondition | dict]
    check_type: WidgetValueUniqueCheckType
    check_tip: str | None = Field("")
    relation: Literal["and", "or"] = Field("and")

    @root_validator(pre=True, skip_on_failure=True)
    def normalize_filters(cls, values):
        if "conditions" not in values and "filters" in values:
            values["conditions"] = values.pop("filters")
        return values

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        return filter_none(super().dict(**kwargs))

    @property
    def obj_filters(self):
        return [condition if isinstance(condition, dict) else condition.dict() for condition in self.conditions]

    def to_value(self):
        predicate = deserialize({"relation": self.relation, "conditions": self.obj_filters}, Filter)
        return Value(type_spec=TypeSpec("boolean"), predicate=predicate)


class StepValidationConfig(BaseModel):
    enabled: bool
    validations: list[StepValidationRule] | None

    def __init__(self, **data):
        super().__init__(**data)
        if self.enabled and self.validations is None:
            self.validations = []

    @classmethod
    def default(cls):
        return cls(enabled=False, validations=None)

    @wraps(BaseModel.dict)
    def dict(self, **kwargs):
        kwargs["exclude_none"] = True
        return super().dict(**kwargs)
