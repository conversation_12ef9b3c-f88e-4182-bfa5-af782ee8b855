import json

from loguru import logger

from robot_processor.constants import SERVICE_HEADER_KEY
from robot_metrics import Stats
from robot_processor.client_mixins import Session
from robot_processor.client.conf import app_config as config

_statsd_it = Stats.Client.timer(client_name="robot-transfer-server")


class RobotTransferClient:
    session = Session()

    @property
    def endpoint(self):
        return config.ROBOT_TRANSFER_ENDPOINT

    @_statsd_it
    def new_transfer(self, body):
        _logger = logger.bind(
            action=body.get("action"),
            uid=body.get("uid")
        )
        url = f"{self.endpoint}/rpa/transfers"
        try:
            self.session.headers[SERVICE_HEADER_KEY] = config.ROBOT_TRANSFER_TOKEN
            resp = self.session.post(url, json=body)
            resp.raise_for_status()
        except BaseException as e:
            _logger.opt(exception=e).error("new transfer error, request={} ", json.dumps(body))
            return {}
        resp_json = resp.json()
        _logger.info(f"transfer request: {json.dumps(body)}, response: {resp_json}")
        return resp_json

    def put_transfer(self, sid, bo_id, action='close', operate_reason="", org_id="", platform="", operator=""):
        _logger = logger.bind(sid=sid, business_order_id=bo_id)
        url = f"{self.endpoint}/rpa/transfers"
        try:
            self.session.headers[SERVICE_HEADER_KEY] = config.ROBOT_TRANSFER_TOKEN
            resp = self.session.put(
                url, json={
                    "sid": sid,
                    "action": action,
                    "business_order_id": bo_id,
                    "operate_reason": operate_reason,
                    "org_id": org_id,
                    "platform": platform,
                    "operator": operator
                }
            )
        except BaseException as e:
            _logger.opt(exception=e).error(f"打款信息修改失败 {action=} error={e} {operator=} {operate_reason=}")
            return {}
        resp_json = resp.json()
        _logger.info(f"打款信息修改完成, {action=} {operator=} {operate_reason=} response={resp_json}")
        return resp_json

    @_statsd_it
    def find_transfer(self, business_order_id):
        url = f"{self.endpoint}/rpa/transfers/{business_order_id}"
        try:
            self.session.headers[SERVICE_HEADER_KEY] = config.ROBOT_TRANSFER_TOKEN
            resp = self.session.get(url)
        except BaseException as e:
            logger.error(str(e))
            return {}

        if resp.status_code != 200:
            return {}
        return resp.json()
