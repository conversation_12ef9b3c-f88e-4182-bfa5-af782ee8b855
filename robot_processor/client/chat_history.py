import json
import typing as t

from loguru import logger

from robot_metrics import Stats
from robot_processor.client_mixins import Session
from robot_processor.constants import SERVICE_HEADER_KEY
from robot_processor.client.conf import app_config as config

_statsd_it = Stats.Client.timer(client_name="robot-chat-history")


class ChatHistoryClient:
    session = Session()

    @_statsd_it
    def get_chat_history(self, seller_nick, buyer_nick, count=100, message_type='consult', content_type='IMAGE'):
        """
        message_type: consult:买家发送 reply:卖家发送 all:全部
        """
        url = f"{config.ROBOT_CHAT_HISTORY_ENDPOINT}/v1/get_chat_history_by_nick"
        body = {
            "seller_nick": seller_nick,
            "buyer_nick": buyer_nick,
            "count": count,
            "message_type": message_type,
            "content_type": content_type,
        }
        try:
            resp = self.session.post(url, json=body, headers={SERVICE_HEADER_KEY: config.ROBOT_CHAT_HISTORY_TOKEN})
            # {
            #     "success": True,
            #     "data": [
            #         {
            #             "sender": sender_nick,
            #             "timestamp": timestamp,
            #             "content": content,
            #             "message_type": consult or reply,
            #         }]
            # }
            resp.raise_for_status()
            resp_json = resp.json()
        except BaseException as e:
            logger.opt(exception=e).error("get chat history error, request={} ", json.dumps(body))
            return {'data': []}
        return resp_json

    def get_history_images(self, seller_nick, buyer_nick, count=100, message_type='all') -> t.List[str]:
        resp_json = self.get_chat_history(seller_nick, buyer_nick, count=count, message_type=message_type)
        return [item.get('content', {}).get('text') for item in resp_json.get('data', [])]
