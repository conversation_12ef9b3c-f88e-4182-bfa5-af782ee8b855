import time
from typing import Iterable
from typing import List
from typing import Optional
from typing import <PERSON>ple

from leyan_proto.digismart.kiosk import dgt_org_channel_pb2 as org_channel_pb
from loguru import logger
from result import Err
from result import Ok
from result import Result
from sqlalchemy import and_
from sqlalchemy import or_

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.auth import KioskJwtPayload
from robot_processor.client.conf import app_config as config
from robot_processor.client_mixins import Session
from robot_processor.db import db
from robot_processor.enums import Creator
from robot_processor.shop.kiosk_models import KioskOrg
from robot_processor.shop.kiosk_models import KioskOrgSalesMan
from robot_processor.shop.kiosk_models import KioskShop
from robot_processor.users.models import GroupUserMapping
from robot_processor.users.models import LeyanUser
from robot_processor.users.models import LeyanUserShopMapping
from robot_processor.users.models import Permission
from robot_processor.users.models import PlatformUser
from robot_processor.users.models import PlatformUserMapping
from robot_processor.users.models import Role
from robot_processor.users.models import RolePermissionMapping
from robot_processor.users.models import UserGroup
from robot_processor.users.models import UserRoleMapping
from robot_processor.utils import response_to_log


class KioskClient:
    session = Session()

    def __init__(self):
        self._timeout = 3

    def _http_get(self, path):
        url = f"{config.KIOSK_APP_ENDPOINT}{path}"
        try:
            resp = self.session.get(url, timeout=self._timeout, headers={"Authorization": config.KIOSK_APP_TOKEN})
            logger.info(f"GET {url} response: {resp.json()}")
        except Exception as e:
            logger.opt(exception=e).warning(f"GET {url} error")
            return {}
        return resp.json()["data"]

    def auth_shop_mapping_by_erp_shop(self, auth_type, erp_shop_id):
        return self._http_get(f"/auth-manager/{auth_type}/auth/shop-mappings-by-erp-shop?erpPlatformSid={erp_shop_id}")

    def auth_detail(self, auth_type, shop, auth_account):
        return self._http_get(
            f"/auth-manager/{auth_type}/auth/detail" f"?sid={shop.sid}&orgId={shop.org_id}&authAccount={auth_account}"
        )

    def get_user_by_nick(self, sid: str, channel_type: str, nick: str) -> Optional[AccountDetailV2]:
        """根据店铺信息和昵称查找账号具体信息, 优先查平台账号，找不到时再查找飞梭账号."""
        channel_type = channel_type.lower()
        platform_user = PlatformUser.query.filter(
            PlatformUser.sid == sid,
            PlatformUser.nick == nick,
            PlatformUser.platform == channel_type,
            PlatformUser.status == 1,
        ).first()
        if platform_user:
            logger.info(f"通过 {nick=} {sid=} {channel_type=} 获取到平台账号 {platform_user.id}")
            return platform_user.to_account_detail_v2()
        leyan_user = (
            LeyanUser.query.join(LeyanUserShopMapping, LeyanUser.id == LeyanUserShopMapping.user_id)
            .join(KioskShop, KioskShop.id == LeyanUserShopMapping.shop_id)
            .filter(LeyanUser.nickname == nick, KioskShop.sid == sid, KioskShop.platform == channel_type)
            .first()
        )
        if leyan_user:
            logger.info(f"通过 {nick=} {sid=} {channel_type=} 获取到乐言账号 {leyan_user.id}")
            return leyan_user.to_account_detail_v2()
        else:
            return None

    def get_bound_leyan_user(self, platform_user_id: int) -> Optional[AccountDetailV2]:
        """通过平台账号id获取绑定的乐言账号.

        :param platform_user_id: 平台账号id
        :return: 该平台账号绑定的乐言账号信息, 用 dataclass AccountDetailV 表示
        """
        user = (
            LeyanUser.query.join(PlatformUserMapping, LeyanUser.id == PlatformUserMapping.user_id)
            .filter(PlatformUserMapping.platform_user_id == platform_user_id)
            .first()
        )
        return user.to_account_detail_v2() if user else None

    @staticmethod
    def get_bound_leyan_users_by_platform_user_ids(platform_user_ids: Iterable[int]) -> list[AccountDetailV2]:
        """
        通过平台账号 id 列表来获取它们所有绑定的乐言账号。
        :param platform_user_ids:
        :return:
        """
        leyan_users: list[LeyanUser] = (
            LeyanUser.query.join(PlatformUserMapping, LeyanUser.id == PlatformUserMapping.user_id)
            .filter(PlatformUserMapping.platform_user_id.in_(platform_user_ids))
            .all()
        )

        return [leyan_user.to_account_detail_v2() for leyan_user in leyan_users]

    def get_leyan_users_by_channel_id(self, channel_id: int) -> list[AccountDetailV2]:
        """获取店铺的乐言用户列表
        1. 直接绑定店铺的乐言用户
        2. 绑定了店铺的平台账号的乐言用户
        """
        users_has_bound_shop = db.session.query(LeyanUserShopMapping.user_id).filter(
            LeyanUserShopMapping.shop_id == int(channel_id)
        )
        users_has_bound_plat_user = db.session.query(PlatformUserMapping.user_id).filter(
            PlatformUserMapping.channel_id == int(channel_id)
        )
        leyan_users: list[LeyanUser] = LeyanUser.query.filter(
            LeyanUser.id.in_(users_has_bound_shop) | LeyanUser.id.in_(users_has_bound_plat_user), LeyanUser.status == 1
        ).all()
        return [u.to_account_detail_v2() for u in leyan_users]

    def get_leyan_users_by_users_and_groups(
        self, users: list[AccountDetailV2], group_uuids: set[str] | None, channel_id: int
    ) -> list[AccountDetailV2]:
        """在指定店铺内查找符合下列条件之一的平台账号:

        - id 出现在 users 中，且 users 中对应的 user_type 是 LEYAN
        - 所绑定的平台账号 id 出现在 users 中，且 users 中对应的 user_type 是 ASSISTANT
        - id 或者所绑定的平台账号 id，关联在了任意一个 groups 上
        """
        users = users or []
        platform_user_ids = {u.user_id for u in users if u.user_type == Creator.ASSISTANT and u.user_id is not None}
        leyan_user_ids = {u.user_id for u in users if u.user_type == Creator.LEYAN and u.user_id is not None}
        if group_uuids:
            for group_user_mapping in (
                GroupUserMapping.query.join(UserGroup, GroupUserMapping.group_uuid == UserGroup.group_uuid)
                .filter(UserGroup.group_uuid.in_(group_uuids), UserGroup.deleted == 0)
                .all()
            ):
                if group_user_mapping.user_type == Creator.ASSISTANT:
                    platform_user_ids.add(group_user_mapping.user_id)
                elif group_user_mapping.user_type == Creator.LEYAN:
                    leyan_user_ids.add(group_user_mapping.user_id)
        if platform_user_ids:
            for u in (
                db.session.query(LeyanUser.id)
                .select_from(LeyanUser)
                .join(PlatformUserMapping, LeyanUser.id == PlatformUserMapping.user_id)
                .join(PlatformUser, PlatformUser.id == PlatformUserMapping.platform_user_id)
                .filter(
                    PlatformUserMapping.channel_id == channel_id,
                    PlatformUser.id.in_(platform_user_ids),
                    PlatformUser.status == 1,
                    LeyanUser.status == 1,
                )
                .all()
            ):
                leyan_user_ids.add(u.id)
        leyan_users: list[LeyanUser] = (
            LeyanUser.query.join(LeyanUserShopMapping, LeyanUser.id == LeyanUserShopMapping.user_id)
            .filter(LeyanUserShopMapping.shop_id == channel_id, LeyanUser.id.in_(leyan_user_ids), LeyanUser.status == 1)
            .all()
        )
        return [u.to_account_detail_v2() for u in leyan_users]

    def get_user_by_id(self, user_type: Creator, user_id: int) -> Optional[AccountDetailV2]:
        """获取指定 类型/id 的账号详情."""
        if user_type == Creator.ASSISTANT:
            platform_user: PlatformUser | None = PlatformUser.query.get(user_id)
            return platform_user.to_account_detail_v2() if platform_user else None
        elif user_type == Creator.LEYAN:
            user: LeyanUser | None = LeyanUser.query.filter(LeyanUser.id == user_id).first()
            return user.to_account_detail_v2() if user else None
        else:
            return None

    def get_platform_users_by_users_and_groups(
        self, users: Iterable[AccountDetailV2], group_uuids: set[str] | None, channel_id
    ) -> List[AccountDetailV2]:
        """在指定店铺内查找符合下列条件之一的平台账号:

        - id 出现在 users 中，且 users 中对应的 user_type 是 ASSISTANT
        - 所绑定的乐言账号 id 出现在 users 中，且 users 中对应的 user_type 是 LEYAN
        - id 或者所绑定的乐言账号 id，关联在了任意一个 groups 上
        """
        leyan_user_ids = set(u.user_id for u in users if u.user_type == Creator.LEYAN and u.user_id is not None)
        platform_user_ids = set(u.user_id for u in users if u.user_type == Creator.ASSISTANT and u.user_id is not None)
        if group_uuids:
            mappings = (
                GroupUserMapping.query.join(UserGroup, GroupUserMapping.group_uuid == UserGroup.group_uuid)
                .filter(GroupUserMapping.group_uuid.in_(group_uuids))
                .filter(UserGroup.status == 1, UserGroup.deleted == 0)
                .all()
            )
            for m in mappings:
                if m.user_type == Creator.ASSISTANT:
                    platform_user_ids.add(m.user_id)
                elif m.user_type == Creator.LEYAN:
                    leyan_user_ids.add(m.user_id)
        platform_users = (
            PlatformUser.query.join(PlatformUserMapping, PlatformUser.id == PlatformUserMapping.platform_user_id)
            .filter(PlatformUser.id.in_(platform_user_ids) | PlatformUserMapping.user_id.in_(leyan_user_ids))
            .filter(PlatformUserMapping.channel_id == channel_id)
            .filter(PlatformUser.status == 1)
            .all()
        )
        return [platform_user.to_account_detail_v2() for platform_user in platform_users]

    def get_platform_users_by_leyan_user_ids(self, leyan_user_ids: Iterable[int]) -> list[AccountDetailV2]:
        """通过乐言账号 id 列表来获取它们所有绑定的平台账号。"""
        platform_users = (
            PlatformUser.query.join(PlatformUserMapping, PlatformUserMapping.platform_user_id == PlatformUser.id)
            .filter(PlatformUserMapping.user_id.in_(leyan_user_ids))
            .all()
        )
        return [u.to_account_detail_v2() for u in platform_users]

    def get_platform_user_ids_by_leyan_user_id(self, leyan_user_id: int) -> list[int]:
        """获取一个乐言账号绑定的所有平台账号 id."""
        query = (
            db.session.query(PlatformUserMapping.platform_user_id)
            .select_from(PlatformUserMapping)
            .filter(PlatformUserMapping.user_id == leyan_user_id)
        )
        return [r.platform_user_id for r in query]

    def get_granted_store_ids_for_user(self, leyan_user_id: int, org_id: int) -> list[str]:
        """获取用户被授权的店铺 id 列表, 这里的授权是指：

        1. 通过 LeyanUserShopMapping 做了乐言账号和店铺的绑定
        2. 通过 PlatformUserMapping 对乐言账号和店铺的平台账号做了绑定
        """
        # 1. 通过 LeyanUserShopMapping 做了乐言账号和店铺的绑定
        query = (
            db.session.query(KioskShop.sid)
            .select_from(KioskShop)
            .join(LeyanUserShopMapping, KioskShop.id == LeyanUserShopMapping.shop_id)
            .filter(LeyanUserShopMapping.user_id == leyan_user_id, KioskShop.org_id == org_id)
            .filter(KioskShop.status == 1, KioskShop.deleted == 0)
        )
        directly_bound_sids = set(t.sid for t in query)
        # 2. 通过 PlatformUserMapping 对乐言账号和店铺的平台账号做了绑定
        query2 = (
            db.session.query(KioskShop.sid)
            .select_from(KioskShop)
            .join(PlatformUserMapping, KioskShop.id == PlatformUserMapping.channel_id)
            .filter(PlatformUserMapping.user_id == leyan_user_id, KioskShop.org_id == org_id)
            .filter(KioskShop.status == 1, KioskShop.deleted == 0)
        )
        indirectly_bound_sids = set(t.sid for t in query2)
        return list(directly_bound_sids | indirectly_bound_sids)

    def get_leyan_user_ids_by_sub_users(self, sid, platform, sub_user_ids) -> List[int]:
        """
        通过淘宝子账号id 获取对应的乐言账号
        """
        platform = platform.lower()
        sub_user_ids = [user_id if user_id else platform + sid for user_id in sub_user_ids]
        platform_users = PlatformUser.query.filter(
            PlatformUser.sid == sid, PlatformUser.platform == platform, PlatformUser.sub_id.in_(sub_user_ids)
        ).all()
        platform_user_ids = [user.id for user in platform_users]
        leyan_users = (
            LeyanUser.query.join(PlatformUserMapping, LeyanUser.id == PlatformUserMapping.user_id)
            .filter(PlatformUserMapping.platform_user_id.in_(platform_user_ids))
            .all()
        )
        return [user.id for user in leyan_users]

    def get_users_groups(self, org_id, users: List[Tuple[int, Creator]]) -> set[str]:
        leyan_user_ids = [user_id for user_id, user_type in users if user_type == Creator.LEYAN]
        platform_user_ids = [user_id for user_id, user_type in users if user_type == Creator.ASSISTANT]
        org_id = int(org_id)
        leyan_groups_filter = and_(GroupUserMapping.user_id.in_(leyan_user_ids), UserGroup.type == UserGroup.TYPE_LEYAN)
        platform_groups_filter = and_(
            GroupUserMapping.user_id.in_(platform_user_ids), UserGroup.type == UserGroup.TYPE_PLATFORM
        )
        groups_orm = (
            UserGroup.query.join(GroupUserMapping, UserGroup.group_uuid == GroupUserMapping.group_uuid)
            .filter(UserGroup.org_id == org_id, UserGroup.deleted == 0, UserGroup.status == 1)
            .filter(or_(leyan_groups_filter, platform_groups_filter))
            .all()
        )
        return {group.group_uuid for group in groups_orm}

    def has_function_users_by_sid(self, org_id, sid, function_code: str) -> list[AccountDetailV2]:
        """找出拥有指定权限的用户."""
        org_id = int(org_id)
        all_users = (
            LeyanUser.query.join(LeyanUserShopMapping, LeyanUserShopMapping.user_id == LeyanUser.id)
            .join(KioskShop, LeyanUserShopMapping.shop_id == KioskShop.id)
            .filter(KioskShop.org_id == org_id, KioskShop.sid == sid)
            .filter(KioskShop.status == 1, KioskShop.deleted == 0)
            .filter(LeyanUser.status == 1)
        )
        result = []
        for user in all_users:
            user_permissions = self.get_permissions_for_user(user.id)
            if function_code in user_permissions:
                result.append(user.to_account_detail_v2())
        return result

    @staticmethod
    def get_roles_for_leyan_user(user_id: int) -> list[Role]:
        """
        查询飞梭账号关联的所有有效角色。
        """
        roles: list[Role] = (
            db.session.query(Role)
            .join(
                UserRoleMapping,
                UserRoleMapping.role_id == Role.id,
            )
            .filter(
                UserRoleMapping.user_id == user_id,
                UserRoleMapping.deleted == 0,
            )
            .filter(
                Role.status == 1,
                Role.deleted == 0,
            )
            .all()
        )
        return roles

    @staticmethod
    def get_roles_for_platform_user(user_id: int) -> list[Role]:
        """
        查询平台账号关联的所有有效角色。
        """
        roles: list[Role] = (
            db.session.query(Role)
            .join(
                UserRoleMapping,
                UserRoleMapping.role_id == Role.id,
            )
            .join(
                LeyanUser,
                LeyanUser.id == UserRoleMapping.user_id,
            )
            .join(
                PlatformUserMapping,
                PlatformUserMapping.user_id == LeyanUser.id,
            )
            .filter(
                PlatformUserMapping.platform_user_id == user_id,
            )
            .filter(
                LeyanUser.status == 1,
                LeyanUser.locked == 0,
            )
            .filter(
                UserRoleMapping.deleted == 0,
            )
            .all()
        )
        return roles

    def get_permissions_for_user(self, user_id: int) -> set[str]:
        """获取乐言账号的功能权限列表"""
        leyan_user: LeyanUser | None = LeyanUser.query.filter_by(id=user_id).with_entities(LeyanUser.org_id).first()
        if not leyan_user:
            return set()
        org_id = leyan_user.org_id
        # 系统默认角色，是 org_id 为 0 的角色
        default_role_query = (
            db.session.query(Role.role_code, Permission.function_code)
            .select_from(Role)
            .join(RolePermissionMapping, Role.id == RolePermissionMapping.role_id)
            .join(Permission, RolePermissionMapping.function_id == Permission.id)
            .filter(Role.org_id == 0, Role.deleted == 0, Role.status == 1, Permission.deleted == 0)
        )
        # (默认角色名, 权限代码)
        default_role_permissions: set[tuple[str, str]] = set((r, p) for r, p in default_role_query)
        # 默认角色的权限代码集合
        default_roles = set(role_code for role_code, _ in default_role_permissions)

        user_role_query = (
            db.session.query(Role.role_code)
            .select_from(Role)
            .join(UserRoleMapping, Role.id == UserRoleMapping.role_id)
            .filter(UserRoleMapping.user_id == user_id, Role.deleted == 0, Role.status == 1)
        )
        # 用户拥有的角色代码集合
        user_roles = set(role_code for role_code, in user_role_query)
        # 用户拥有的默认角色代码集合
        user_default_roles = user_roles & default_roles
        # 用户拥有的自定义角色代码集合
        user_custom_roles = user_roles - default_roles

        # 用户实际拥有的权限 = 商家自定义角色的权限 + (系统默认角色的权限  + 商家在系统默认角色上显式添加的权限 - 商家在系统默认角色上显式剔除的权限)
        # p_effective = p_custom + (p_default + p_default_plus - p_default_exclude)
        p_custom, p_default, p_default_plus, p_default_exclude = set(), set(), set(), set()
        if user_custom_roles:
            custom_role_permission_query = (
                db.session.query(Permission.function_code)
                .select_from(Permission)
                .join(RolePermissionMapping, RolePermissionMapping.function_id == Permission.id)
                .join(Role, RolePermissionMapping.role_id == Role.id)
                .filter(Role.role_code.in_(user_custom_roles))
            )
            p_custom = set(p for p, in custom_role_permission_query)

        if user_default_roles:
            p_default = set(p for r, p in default_role_permissions if r in user_default_roles)
            default_permission_query = (
                db.session.query(Permission.function_code, RolePermissionMapping.mapping_type)
                .select_from(Permission)
                .join(RolePermissionMapping, RolePermissionMapping.function_id == Permission.id)
                .join(Role, RolePermissionMapping.role_id == Role.id)
                .filter(Role.role_code.in_(user_default_roles))
                .filter(Role.org_id == org_id)
            )
            for function_code, mapping_type in default_permission_query:
                if mapping_type == 0:
                    p_default_exclude.add(function_code)
                else:
                    p_default_plus.add(function_code)

        return p_custom | ((p_default | p_default_plus) - p_default_exclude)

    def get_org_info_by_org_id(self, org_id: int) -> Optional[org_channel_pb.OrgInfo]:
        """org_channel_pb.OrgInfo 暂不包含合同相关信息"""
        org: KioskOrg | None = KioskOrg.query.filter(KioskOrg.id == org_id, KioskOrg.deleted == 0).first()
        if not org:
            return None
        org_type_names = {1: "乐言官方", 2: "客户", 3: "员工租户", 4: "模板租户", 5: "其他"}
        org_info = org_channel_pb.OrgInfo(
            id=org.id,
            org_type=org_type_names.get(org.org_type, "") if org.org_type is not None else "",
            org_name=org.org_name or "",
            zone=org.zone or "",
            status="NORMAL" if org.status == 1 else "DISABLE",
            deleted="DELETED" if org.deleted == 1 else "EFFECTIVE",
        )
        if org_sale_man := (
            KioskOrgSalesMan.query.filter(KioskOrgSalesMan.master_cid == org.master_cid)
            .order_by(KioskOrgSalesMan.id.desc())
            .first()
        ):
            org_info.salesman = org_sale_man.sales_man or ""
            org_info.support = org_sale_man.implementer or ""

        return org_info

    def list_users_by_org(self, org_id: int) -> tuple[list[AccountDetailV2], list[AccountDetailV2]]:
        """获取指定租户下的所有乐言账号和平台账号."""
        org_id = int(org_id)
        leyan_users = LeyanUser.query.filter(LeyanUser.org_id == org_id).all()
        platform_users = (
            PlatformUser.query.join(
                KioskShop, and_(KioskShop.sid == PlatformUser.sid, KioskShop.platform == PlatformUser.platform)
            )
            .filter(KioskShop.org_id == org_id)
            .all()
        )
        return (
            [user.to_account_detail_v2() for user in leyan_users],
            [user.to_account_detail_v2() for user in platform_users],
        )

    def verify_code(self, phone: str, code: str) -> Result[str, str]:
        """检查手机验证码登录"""
        url = f"{config.KIOSK_APP_ENDPOINT}/login/msLogin/{phone}/{code}"
        resp = self.session.post(url, timeout=self._timeout)
        if resp.status_code != 200:
            return Err(response_to_log(resp))
        resp_json = resp.json()
        if resp_json["code"] != "0":
            return Err(resp_json["msg"])
        token: str = resp_json["data"]
        return Ok(token)

    def get_jwt_for_user(self, platform_user_nick: str, sid: str, platform: str) -> Result[KioskJwtPayload, str]:
        """获取用户的 jwt token"""
        kiosk_shop: KioskShop | None = KioskShop.query.filter(
            KioskShop.sid == sid, KioskShop.platform == platform.lower()
        ).first()
        if not kiosk_shop:
            return Err("店铺没有绑定，请联系管理员")
        leyan_user: LeyanUser | None = (
            LeyanUser.query.join(PlatformUserMapping, LeyanUser.id == PlatformUserMapping.user_id)
            .join(PlatformUser, PlatformUser.id == PlatformUserMapping.platform_user_id)
            .filter(PlatformUser.nick == platform_user_nick, PlatformUserMapping.channel_id == kiosk_shop.id)
            .first()
        )
        if not leyan_user:
            return Err("用户没有绑定店铺，请联系管理员")
        if leyan_user.status != 1:
            return Err("用户已被禁用/锁定，请联系管理员")

        platform_user = PlatformUser.query.filter(
            PlatformUser.sid == sid,
            PlatformUser.nick == platform_user_nick,
            PlatformUser.platform == platform.lower(),
        ).first()
        if not platform_user:
            return Err("获取平台用户信息失败.")

        iat = int(time.time())
        exp = iat + 7 * 24 * 3600  # 七天后失效
        jwt_payload = KioskJwtPayload(
            iss="kiosk",
            org_id=kiosk_shop.org_id,
            user_id=leyan_user.id,
            nick_name=leyan_user.nickname,
            phone_number=leyan_user.phone,
            login_type="org_user",
            channel_id=kiosk_shop.id,
            channel_type=kiosk_shop.platform,
            store_id=kiosk_shop.sid,
            store_name=platform_user_nick.split(":")[0],  # 淘宝/天猫的平台子账号格式是 "<店铺名>:<子账号名>"
            login_user_type=Creator.ASSISTANT,
            login_user_id=platform_user.id,
            login_user_nick=platform_user.nick,
            nick=platform_user.nick,
            iat=iat,
            exp=exp,
        )
        return Ok(jwt_payload)

    def list_shop_by_rpa_shop_binding(self, org_id: int, rpa_shop_name: str):
        """通过客户在后台维护的 rpa 店铺名和飞梭店铺的关系，查找关联的飞梭店铺。一个 rpa 店铺可以关联多个飞梭店铺"""
        from robot_processor.shop.auth_manager import CredentialShopMapping
        from robot_processor.shop.kiosk_models import KioskShop
        from robot_processor.shop.kiosk_models import RpaThirdShop
        from robot_processor.shop.models import Shop

        rpa_shop = RpaThirdShop.query.filter_by(org_id=org_id, shop_name=rpa_shop_name).first()
        if not rpa_shop:
            return Err(ValueError(f"未配置过 {rpa_shop_name}"))
        kiosk_shops = (
            KioskShop.query.join(CredentialShopMapping, KioskShop.id == CredentialShopMapping.channel_id)
            .filter(KioskShop.org_id == org_id, CredentialShopMapping.erp_platform_sid == rpa_shop.id)
            .all()
        )
        if not kiosk_shops:
            return Err(ValueError(f"{rpa_shop_name} 未绑定飞梭店铺"))
        return Ok(Shop.query.filter(Shop.channel_id.in_([channel.id for channel in kiosk_shops])).all())
