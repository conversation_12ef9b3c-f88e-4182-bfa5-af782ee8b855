import time
import typing as t

from limits import RateLimitItem
from limits.storage.redis import RedisStorage
from limits.strategies import MovingWindowRateLimiter
from limits.util import parse_many
from loguru import logger

from robot_metrics import Stats

_statsd_it = Stats.Client.timer(client_name="limiter-server")


class Limiter:
    storage: RedisStorage
    _limiter: MovingWindowRateLimiter

    def init_app(self, app):
        storage_url = app.config.get("STORAGE_URL")
        self.storage: RedisStorage = RedisStorage(storage_url)
        self._limiter = MovingWindowRateLimiter(self.storage)

    def wait_acquire_permission(
        self,
        limit_key: str,
        limit_value: str,
        consume_rate_limit_token: bool = True,
        timeout: int = 3,
        interval: float | int = 0.1,
    ):
        if timeout <= 0:  # 不等待
            return self.acquire_permission(limit_key, limit_value, consume_rate_limit_token)
        else:
            until = time.time() + timeout
            while time.time() < until:
                if self.acquire_permission(limit_key, limit_value, consume_rate_limit_token):
                    return True
                time.sleep(interval)
            return False

    @_statsd_it
    def acquire_permission(self, limit_key: str, limit_value: str, consume_rate_limit_token=True) -> bool:
        """从限流令牌桶中获取令牌.

        :param limit_key: 限流的 key, 也就是在什么维度上限流
        :param limit_value: 限流的 value, 也就是按什么速度限流
        :param consume_rate_limit_token: 是否消费限流令牌, 默认为 True
        :return: 是否获取到令牌, True 为获取到, False 为未获取到. 获取到令牌后，调用方可以继续执行业务逻辑，否则应该等待一段时间后重试
        """
        permitted, not_permitted = True, False

        if not consume_rate_limit_token:
            permit_method = self._limiter.test
        else:
            permit_method = self._limiter.hit

        try:
            limits: t.List[RateLimitItem] = parse_many(limit_value)
            for limit in limits:
                if limit.amount <= 0:
                    return not_permitted
                permitted_ = permit_method(limit, limit_key)
                if not permitted_:
                    return not_permitted
            return permitted
        except Exception as error:
            logger.opt(exception=error).error(error)
            return not_permitted

    def reset_limit_hit(self, limit_key: str, limit_value: str) -> bool:
        """
        重置 limit 的命中记录。
        """
        permit_method = self._limiter.clear

        try:
            limits: t.List[RateLimitItem] = parse_many(limit_value)
            for limit in limits:
                if limit.amount <= 0:
                    continue
                permit_method(limit, limit_key)
            return True
        except Exception as error:
            logger.opt(exception=error).error(error)
            return False
