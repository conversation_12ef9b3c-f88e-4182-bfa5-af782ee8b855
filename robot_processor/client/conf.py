from lepollo import ApolloConfig
from lepollo import get_config


class AppConfig(ApolloConfig):
    __namespace__ = "application"

    ACTION_LOG_AUTH_TOKEN: str
    ACTION_LOG_ENDPOINT: str = "http://zb-api.leyanbot.com/nevermore/v1/timelines/create"

    CHAT_CLIENT_OPEN_GATEWAY_API_KEY: str
    CHAT_CLIENT_OPEN_GATEWAY_ENDPOINT: str = "https://pre-opengw.leyanbot.com"
    CHAT_CLIENT_SCORE_GRPC_GATEWAY_ENDPOINT: str = "http://pre-api.leyanbot.com/scout"
    CHAT_CLIENT_SCORE_GATEWAY_API_KEY: str
    CHAT_CLIENT_SESSION_MANAGER_GRPC_GATEWAY_ENDPOINT: str = "http://pre-api.leyanbot.com/session-manager"
    CHAT_CLIENT_SESSION_MANAGER_GATEWAY_API_KEY: str

    ROBOT_CHAT_HISTORY_ENDPOINT: str = "https://pre-api.leyanbot.com/dgt-chat-history"
    ROBOT_CHAT_HISTORY_TOKEN: str

    ONLINE_MOLA_KEEP_ALIVE_SECONDS: int = 20

    KIOSK_APP_ENDPOINT: str = "http://zb-api.leyanbot.com/kiosk"
    KIOSK_APP_TOKEN: str
    KIOSK_USE_REMOTE: str = "false"

    RISK_CONTROL_TIMEOUT: int = 3

    TAOBAO_APP_KEY: str
    TAOBAO_APP_SECRET: str

    ROBOT_TRANSFER_ENDPOINT: str = "http://localhost"
    ROBOT_TRANSFER_TOKEN: str

    N8N_ENDPOINT: str = "http://mola-api.ganjutech.com"

    TASK_QUEUE_URL: str = "redis://localhost/0"

    USER_TASK_URL: str = "redis://localhost/1"
    USER_TASK_PROCESSES: int = 1
    USER_TASK_THREADS: int = 1
    USER_TASK_QUEUES: str = "USER_TASK"
    USER_TASK_BATCH_UPLOAD_MAX_SIZE: int = 1024 * 1024 * 100

    USER_TASK_OSS_BIZ_KEY: str = "user_task.business_order.import"
    USER_TASK_FED_TASK_FETCH_INTERVAL: int = 1000
    USER_TASK_BATCH_LIMIT_COUNT: int = 2000
    USER_TASK_BATCH_LIMIT_COUNT_WHITELIST: str = "{}"

    RPA_TOP10: str = '["测试RPA", "另一个测试RPA"]'

    BI_DATABASE_CONNECTION_URI = "mysql+pymysql://root:123456@localhost/archived_db"

    ZTO_DOMAIN: str = "https://japi.zto.com"
    ZTO_CUSTOMER_KEYS: str = "{}"
    ZTO_ALLOW_PACKAGE_DELIVERYING_ORG_MAPS: str = '{"1001": true}'

    JT_INTERCEPTION_WAIT_CALLBACK_SECONDS: int = 15

    YTO_QUERY_TRACE_CUSTOMER_CODE_TO_KEY: str = '{"partner_id": "test", "partner_key": "test"}'

    EMS_CUSTOMER_KEYS: str = (
        '{"1001":{"authorization":"123r432134",' '"sm4_key":"TvaBgrhE46sft3nZlfe7xw==","sender_no":"1001"}}'
    )

    EMS_ORG_CUSTOMER_KEYS: str = (
        '{"3":[{"authorization": "Spl182NqfFoe8szg",'
        '"sm4_key": "YzVSb1U4M3hEd21INWpmaw==","sender_no": "1100175811487"}]}'
    )

    WDT_IGNORE_CANCELED_WHITE_LIST: str = "{}"

    LOGISTICS_SERVICE_TOKEN: str = ""
    LOGISTICS_SERVICE_URL: str = "https://wuliu.market.alicloudapi.com/kdi"
    PDD_BRIDGE_HOST: str = ""
    PDD_BRIDGE_TOKEN: str = ""
    PDD_BRIDGE_CALLBACK: str = "http://"
    PDD_BRIDGE_ACK_TIMEOUT: int = 20
    PDD_AUTH_CHECK_HOST: str = "http://"
    PDD_AUTH_CHECK_TOKEN: str = ""

    EXPORT_SINGLE_EXCEL_MAX_ROWS: int = 10000

    DOUDIAN_CLOUD_HOST: str = ""
    DOUDIAN_CLOUD_TOKEN: str = ""
    DOUDIAN_CLOUD_AES_SECRET: str = "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF"

    DOUDIAN_ORDER_FILL_CODE_BY_API_ORG_WHITE_LIST: str = '{"1001": true}'

    # 为了过抖店审核
    DOUDIAN_GRANT_PASS_NON_CODE_REQUEST: str = "false"

    MOLA_API_URL: str = "http://mola-api.ganjutech.com"
    MOLA_API_TOKEN: str = ""

    CK_HOST: str = ""
    CK_USERNAME: str = ""
    CK_PASSWORD: str = ""

    # 食品助手
    SPZS_TAOBAO_APP_KEY: str
    SPZS_TAOBAO_APP_SECRET: str

    # 乐销客
    LXK_TAOBAO_APP_KEY: str
    LXK_TAOBAO_APP_SECRET: str

    # 慧眼卫士
    HYWS_TAOBAO_APP_KEY: str
    HYWS_TAOBAO_APP_SECRET: str
    HYWS_TOKENS: str = '{"1001": "xx"}'

    # 侧边栏版本限定
    MIN_PLUGIN_VERSION: str = "0.0.1"

    MOGUYUN_SEND_URL: str = "https://mg.shmoguyun.com/sms/batch/v1"
    MOGUYUN_REPORT_URL: str = "https://mg.shmoguyun.com/sms/report/v1"
    MOGUYUN_APP_KEY: str = ""
    MOGUYUN_APP_SECRET: str = ""
    MOGUYUN_APP_CODE: str = ""

    OPERATE_DEBOUNCE_EX_SECONDS: int = 5

    # 自动跳转的最大时间限制（单位：秒）
    JUMP_CONFIG_MAX_TIME_RANGE: int = 5 * 24 * 60 * 60
    # 自动跳转的最大次数限制
    JUMP_CONFIG_MAX_TIMES: int = 120
    # 自动跳转的最小时间间隔（单位：秒）
    JUMP_CONFIG_MIN_INTERVAL: int = 10 * 60

    # 授权过期检查时间（单位：秒）
    AUTH_EXPIRES_DELTA: int = 7 * 24 * 60 * 60

    # 刷新淘宝 access_token 的剩余时间间隔（单位：秒）
    REFRESH_TAOBAO_TOKEN_TIME_DELTA: int = 30 * 60

    TAOBAO_MIX_NICK_APP_KEY: str = ""
    TAOBAO_MIX_NICK_APP_SECRET: str = ""

    KS_APP_KEY: str = ""
    KS_SIGN_SECRET: str = ""

    JD_APP_KEY: str = ""
    JD_APP_SECRET: str = ""
    JD_TOKENS: str = '{"1001": "xx"}'

    WDT_PLATFORM_ID_TO_NAME_JSON: str = """{"127": "自有平台", "0": "线下", "1": "淘宝", "2": "淘宝分销", "3": "京东", "4": "拍拍",
                               "5": "亚马逊", "6": "1号店", "7": "当当", "8": "库巴", "9": "阿里巴巴", "10": "ECShop",
                               "11": "麦考林", "12": "V+", "13": "苏宁易购", "14": "唯品会", "15": "易迅", "16": "聚美",
                               "17": "口袋通（有赞）", "18": "Hishop", "19": "微铺宝", "20": "美丽说", "21": "蘑菇街",
                               "22": "贝贝网", "23": "ECstore", "24": "折800", "25": "融e购", "26": "穿衣助手",
                               "27": "楚楚街", "28": "微盟旺店", "29": "卷皮网", "30": "顺丰嘿客", "31": "飞牛网",
                               "32": "微店", "33": "百度mall", "34": "蜜芽宝贝", "35": "明星衣橱", "36": "善融商城",
                               "37": "速卖通", "38": "萌店", "39": "拼多多", "40": "京东到家", "41": "百度外卖",
                               "42": "美团外卖", "43": "大众点评", "44": "口碑外卖", "45": "饿了么", "46": "我买网",
                               "47": "人人店", "48": "美囤妈妈", "49": "91拼团", "50": "网易考拉海购", "51": "千米网",
                               "52": "特奢汇", "53": "楚楚街拼团", "54": "天猫企业购", "55": "孩子王", "56": "小红书",
                               "57": "格格家", "58": "云集", "59": "楚楚通", "60": "返利网", "61": "酒仙网",
                               "62": "平安好医生", "63": "下厨房", "64": "好食期", "65": "大V店", "66": "好衣库",
                               "67": "达令家", "68": "爱库存", "69": "放心购(抖店）", "70": "每日一淘", "71": "贝壳优品",
                               "72": "小米有品", "73": "未来集市", "74": "爱奇艺商城", "75": "快手小店",
                               "76": "魔筷星选", "77": "海拍客", "78": "壹钱包", "79": "每日优鲜", "80": "网易严选",
                               "81": "多点", "82": "天猫超市-盘货", "83": "微信视频号", "84": "娇兰佳人",
                               "86": "今日爆团", "87": "阿里健康大药房", "88": "必要商城", "89": "腾讯枫页",
                               "90": "微信小商城", "91": "快手金牛", "92": "摩点", "93": "易订货",
                               "94": "天猫国际直营（轻轨三号线）", "96": "驿氪", "97": "妈妈良品", "98": "小芒电商",
                               "99": "卡美啦", "100": "年丰大当家", "101": "得物新平台", "102": "考拉商家直发平台",
                               "103": "美团闪购", "104": "新华书店网上商城", "105": "云货优选平台", "106": "零售通平台",
                               "107": "易久批平台", "108": "腾讯惠聚平台", "109": "小鹅拼拼平台", "110": "度小店平台",
                               "111": "枫页小店", "112": "丁香妈妈平台", "113": "药师帮平台", "114": "蜂雷平台",
                               "115": "SHEIN希音平台", "116": "bilibili会员购平台", "117": "群接龙平台",
                               "118": "招商银行掌上生活平台", "119": "华为商城平台", "121": "单创VTN平台",
                               "122": "天虹商城平台", "123": "有路平台", "126": "定制", "128": "猫享-考拉自营",
                               "129": "东方福利网", "130": "浦惠到家", "131": "B+东方播麦", "134": "闲鱼",
                               "135": "小亚通", "136": "百度营销", "137": "淘宝代打代发", "139": "抖音供销",
                               "140": "顺联动力", "142": "喵街", "125": "系统供销", "143": "映兔", "144": "闯货",
                               "145": "知至阅读", "146": "百度健康", "147": "聚爱优选", "148": "微博电商",
                               "149": "QQ小店", "150": "微商相册", "151": "禾量", "152": "花城农夫", "153": "药帮忙",
                               "154": "抖音超市", "155": "1药城", "159": "美团闪电仓", "160": "广发商城",
                               "161": "微一案管销易(社交新零售)", "164": "翼支付", "165": "本来生活", "166": "书链通",
                               "167": "京东五星", "168": "达摩", "169": "东方甄选", "171": "一商创信私域",
                               "172": "淘宝买菜", "173": "盒马云超", "174": "聚好麦", "175": "红星美凯龙",
                               "176": "大师熊", "178": "量多多", "192": "星妈优选", "201": "抖音来客",
                               "181": "小云商城"}"""

    WDT_ULTI_PLATFORM_ID_AND_SUB_PLATFORM_ID_TO_NAME_JSON: str = """{"127_0": "其他（自有商城）",
                                                   "0_0": "线下", "1_0": "淘宝 - 淘宝集市",
                                                   "1_1": "淘宝 - 天猫商城", "2_0": "淘宝分销", "3_0": "京东 - SOP",
                                                   "3_1": "京东 - LBP", "3_2": "京东 - SOPL", "3_3": "京东 - FBP/FCS",
                                                   "3_4": "京东 - 全球购", "3_5": "京东 - 厂家直送",
                                                   "3_6": "京东 - 京东供销", "3_7": "京东 - EDI", "4_0": "拍拍",
                                                   "5_0": "亚马逊", "6_0": "1号店", "7_0": "当当网", "8_0": "国美",
                                                   "8_1": "国美 - 国美自营", "9_0": "阿里巴巴",
                                                   "9_1": "阿里巴巴 - C2M(淘工厂)", "10_0": "ECShop", "11_0": "麦考林",
                                                   "12_0": "V+", "13_0": "苏宁", "13_1": "苏宁 - 苏宁特卖",
                                                   "14_0": "唯品会", "14_1": "唯品会 - marketplace", "15_0": "易迅",
                                                   "16_0": "聚美", "16_1": "聚美 - 聚美国际", "17_0": "有赞(口袋通)",
                                                   "18_0": "Hishop", "19_0": "慧策产品 - 微铺宝商城",
                                                   "19_1": "慧策产品 - 智能门店", "19_2": "慧策产品 - 店+小程序",
                                                   "20_0": "美丽说", "21_0": "蘑菇街", "21_1": "蘑菇街 - 锐鲨科技",
                                                   "22_0": "贝贝网", "23_0": "ECstore", "24_0": "折800",
                                                   "25_0": "融e购", "26_0": "穿衣助手", "27_0": "楚楚街",
                                                   "28_0": "微盟 - 旺铺", "28_1": "微盟 - 微商城",
                                                   "28_2": "微盟 - 智慧零售", "29_0": "卷皮网", "30_0": "顺丰嘿客",
                                                   "32_0": "微店", "33_0": "百度mall", "34_0": "蜜芽",
                                                   "35_0": "明星衣橱", "36_0": "善融商城", "37_0": "速卖通",
                                                   "38_0": "萌店", "39_0": "拼多多", "39_1": "拼多多 - 拼多多国际店铺",
                                                   "39_2": "拼多多 - 快团团", "40_0": "京东到家", "41_0": "百度外卖",
                                                   "42_0": "美团外卖", "45_0": "饿了么", "46_0": "我买网",
                                                   "47_0": "人人店", "48_0": "美囤妈妈（宝宝树）", "49_0": "91拼团",
                                                   "50_0": "考拉海购", "51_0": "千米网", "52_0": "特奢汇",
                                                   "53_0": "楚楚街拼团", "55_0": "孩子王", "55_1": "孩子王 - 一件代发",
                                                   "56_0": "小红书", "56_1": "小红书 - 小红书", "57_0": "格格家",
                                                   "57_1": "格格家 - 环球捕手", "58_0": "云集",
                                                   "58_1": "云集 - 云集POP", "59_0": "楚楚通", "60_0": "返利网",
                                                   "61_0": "酒仙网", "62_0": "平安好医生", "63_0": "下厨房",
                                                   "64_0": "好食期", "65_0": "大V店", "66_0": "好衣库（鲸灵）",
                                                   "67_0": "达令家", "68_0": "爱库存", "69_0": "抖店（放心购）",
                                                   "69_1": "抖店（放心购） - 厂家代打", "70_0": "每日一淘",
                                                   "71_0": "贝壳优品", "72_0": "小米有品", "73_0": "未来集市",
                                                   "74_0": "爱奇艺商城", "75_0": "快手小店", "76_0": "魔筷星选",
                                                   "77_0": "海拍客", "78_0": "壹钱包", "79_0": "每日优鲜",
                                                   "80_0": "网易严选", "80_1": "网易严选 - 代销2.0", "81_0": "多点",
                                                   "82_0": "天猫超市一盘货", "83_0": "微信视频号", "84_0": "娇兰佳人",
                                                   "85_0": "途虎养车", "86_0": "今日爆团", "87_0": "阿里健康大药房",
                                                   "88_0": "必要", "89_0": "腾讯枫页", "90_0": "微信小商店",
                                                   "91_0": "快手金牛", "92_0": "摩点", "93_0": "易订货",
                                                   "94_0": "天猫国际直营（轻轨三号线）", "96_0": "驿氪",
                                                   "97_0": "妈妈良品", "98_0": "小芒电商", "99_0": "卡美啦",
                                                   "100_0": "年丰大当家", "101_0": "得物", "102_0": "考拉商家直发",
                                                   "103_0": "美团闪购 - 团好货", "103_1": "美团闪购 - 医药健康",
                                                   "104_0": "新华书店", "105_0": "云货优选", "106_0": "零售通",
                                                   "107_0": "易久批", "108_0": "腾讯惠聚", "109_0": "小鹅拼拼",
                                                   "110_0": "度小店", "111_0": "枫页小店", "112_0": "丁香妈妈",
                                                   "113_0": "药师帮", "114_0": "峰雷", "115_0": "SHEIN希音",
                                                   "116_0": "bilibili会员购", "117_0": "群接龙",
                                                   "118_0": "招商银行掌上生活", "119_0": "华为商城",
                                                   "120_0": "微信连接器", "121_0": "单创VTN", "122_0": "天虹商城",
                                                   "123_0": "有路网", "125_0": "分销", "126_1": "定制 - 启博微分销",
                                                   "126_2": "定制 - Farfetch", "126_3": "定制 - 寺库",
                                                   "126_4": "定制 - 分期乐", "126_5": "定制 - 邮乐网",
                                                   "126_6": "定制 - 脉宝云", "126_7": "定制 - 好物满仓",
                                                   "126_8": "定制 - 微一案", "126_9": "定制 - 零购",
                                                   "126_10": "定制 - 执御", "126_11": "定制 - 洋码头",
                                                   "126_12": "定制 - 亲宝宝", "126_13": "定制- 秀购",
                                                   "126_14": "定制 - 萌推", "126_15": "定制 - 环球好货",
                                                   "126_16": "定制 - 一条", "126_17": "定制 - 孔夫子旧书网",
                                                   "126_18": "定制 - 1899", "126_19": "定制 - 洋葱OMALL",
                                                   "126_20": "定制 - 毒", "126_21": "定制 - 棒棒糖",
                                                   "126_22": "定制 - 越洋店铺", "126_23": "定制 - 购书云",
                                                   "126_24": "定制 - 公主购", "126_25": "定制 - 康爱多",
                                                   "126_26": "定制 - 年糕妈妈", "126_27": "定制 - see小店",
                                                   "126_28": "定制 - 部落管家", "128_0": "猫享-考拉自营", "131_0": "B+",
                                                   "134_0": "闲鱼", "135_0": "小亚通", "136_0": "百度营销2.0/百度优选",
                                                   "137_0": "淘宝代打代发", "138_0": "震坤行", "139_0": "抖音供销",
                                                   "140_0": "顺联动力", "141_0": "行云货仓", "142_0": "喵街",
                                                   "146_0": "百度健康", "148_0": "微博电商", "150_0": "微商相册",
                                                   "151_0": "禾量", "152_0": "花城农夫", "154_0": "抖音超市",
                                                   "159_0": "美团闪电仓", "164_0": "翼支付", "165_0": "本来生活",
                                                   "166_0": "书链通", "168_0": "达摩", "169_0": "东方甄选",
                                                   "171_0": "一商创信私域", "172_0": "淘宝买菜", "173_0": "盒马云超",
                                                   "174_0": "聚好麦", "176_0": "大师熊", "177_0": "药九九",
                                                   "179_0": "梵蜜琳", "182_0": "秉坤", "182_1": "秉坤 - 订货商城",
                                                   "192_0": "星妈优选", "199_0": "好又多"}"""

    KM_SOURCE_TO_PLATFORM_JSON: str = """{
        "tb": "淘宝",
        "tm": "天猫",
        "jd": "京东",
        "pdd": "拼多多",
        "sys": "系统店铺",
        "xhs": "小红书",
        "pdd_wd": "拼多多"
    }"""

    KM_REFRESH_TOKEN_BEFORE_DAYS: int = 7

    USER_TASK_STRING_LENGTH: int = 50
    USER_TASK_TEXT_AREA_LENGTH: int = 2000

    BATCH_SKUS_SPU_ID_LIST_LENGTH = 20
    BATCH_SPUS_SPU_ID_LIST_LENGTH = 20

    JD_LYZR_API_ENDPOINT = ""

    SF_MONTHLY_CARD_NO_ORG_MAPS: str = '{"1001": "xx"}'

    XHS_APP_KEY: str = ""
    XHS_APP_SECRET: str = ""
    XHS_REQUEST_TIMEOUT: int = 5000

    @property
    def human_job_dispatch_online_max_hours(self):
        """人工任务自动分派在线客服会自动重试，需要设置最大重试时间上线"""
        return self.get_int("human_job.dispatch_online.max_hours", default_value=24)

    @property
    def human_job_dispatch_online_interval_minutes(self):
        """人工任务自动分派在线客服会自动重试，需要设置重试间隔"""
        return self.get_int("human_job.dispatch_online.interval_minutes", default_value=10)

    @property
    def task_center_all_tab_whitelist(self):
        """任务中心全部tab使用 union 逻辑"""
        return self.get_list("task_center.all_tab.whitelist", ",")

    @property
    def page_titles_merge_step_whitelist(self):
        """表头支持跨步骤合并"""
        return self.get_list("page_titles.merge_step_whitelist", ",")

    @property
    def page_titles_reserved_keys_whitelist(self):
        return self.get_list("page_titles.reserved_keys.whitelist", ",")

    @property
    def retry_job_queue_barrier_times(self):
        return self.get_int("dramatiq.retry_job_queue.barrier_times", 100)

    @property
    def service_buffet_default_ui_schema(self):
        from json import loads

        return self.get("service-buffet.default-ui-schema", loads, {})

    @property
    def bi_mysql8_database_uri(self):
        return self.get_str("bi.mysql8.uri", "mysql+pymysql://root:123456@localhost/archived_db")

    @property
    def bi_mysql8_whitelist(self):
        from json import loads

        return self.get("bi.mysql8.org-whitelist", loads, [])

    @property
    def allow_invalid_tracking_number(self):
        from json import loads

        return self.get("job.tracking-number.no-check.whitelist", loads, [])

    @property
    def enable_query_bo_debug_log(self):
        """是否打开查询工单的日志打印"""
        return self.get_bool("job.query-business-order.debug-log-enabled", False)

    @property
    def wdtulti_trade_query_v2_whitelist(self):
        from json import loads

        return self.get("job.wdtulti.trade-query-v2.whitelist", loads, [])

    @property
    def component_use_default_value_blacklist(self):
        """使用默认值配置的组件黑名单"""
        from json import loads

        return self.get("component.use-default-value.blacklist", loads, [])

    @property
    def exception_pool_follow_fields(self):
        """异常池处理关注的 job 变更字段列表"""
        from json import loads

        return self.get(
            "exception-pool.follow-fields",
            loads,
            ["status", "exc_info", "process_mark"],
        )

    @property
    def scope_form_symbols_api_use_form_composer(self):
        return self.get_bool("form-composer.use-for-scope-form-symbols-api", True)

    @property
    def form_non_interruptible_whitelist(self):
        """非中断的执行工单模板白名单"""
        return list(map(int, self.get_list("form.non-interruptible.whitelist")))

    @property
    def redis_client_connection_url(self):
        return self.get_str("redis_client.connection.uri", "redis://")


app_config = get_config(config_class=AppConfig)


class OssConfig(ApolloConfig):
    __namespace__ = "OSS"

    OSS_KEY_ID: str
    OSS_KEY_SECRET: str
    OSS_ENDPOINT: str = "oss-cn-zhangjiakou.aliyuncs.com"
    OSS_BUCKET_NAME: str = "stg-robot-processor"
    OSS_FOLDER_NAME: str = "robot-processor"
    OSS_URL_BASE: str = "https://test.oss.com/"
    OSS_LINK_TTL: int = 1200

    IMAGE_OSS_KEY_ID: str
    IMAGE_OSS_KEY_SECRET: str
    IMAGE_OSS_ENDPOINT: str = "oss-cn-zhangjiakou.aliyuncs.com"
    IMAGE_OSS_BUCKET_NAME: str = "stg-robot-processor"
    IMAGE_OSS_FOLDER_NAME: str = "images"
    IMAGE_OSS_URL_BASE: str = "https://test.oss.com/"
    IMAGE_OSS_LINK_TTL: int = 1200

    VIDEO_OSS_KEY_ID: str
    VIDEO_OSS_KEY_SECRET: str
    VIDEO_OSS_ENDPOINT: str = "oss-cn-zhangjiakou.aliyuncs.com"
    VIDEO_OSS_BUCKET_NAME: str = "stg-robot-processor"
    VIDEO_OSS_FOLDER_NAME: str = "videos"
    VIDEO_OSS_URL_BASE: str = "https://test.oss.com/"
    VIDEO_OSS_LINK_TTL: int = 1200

    RPA_CONTROL_OSS_KEY_ID: str
    RPA_CONTROL_OSS_KEY_SECRET: str
    RPA_CONTROL_OSS_ENDPOINT: str = "oss-cn-zhangjiakou.aliyuncs.com"
    RPA_CONTROL_OSS_BUCKET_NAME: str = "rpa-control"
    RPA_CONTROL_OSS_LINK_TTL: int = 30 * 60


oss_config = get_config(config_class=OssConfig)
