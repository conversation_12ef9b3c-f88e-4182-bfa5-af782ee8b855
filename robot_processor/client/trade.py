"""定义获取订单的client"""

from typing import List
from typing import cast

from dramatiq import Retry
from google.protobuf.json_format import MessageToDict
from leyan_grpc.client.venice import GaiaStub
from leyan_proto.digismart.trade.dgt_trade_pb2 import BatchGetTradeByIdsRequest
from leyan_proto.digismart.trade.dgt_trade_pb2 import FlagColor
from leyan_proto.digismart.trade.dgt_trade_pb2 import GetChannelInfoByTidsReq
from leyan_proto.digismart.trade.dgt_trade_pb2 import GetPeriodTradeListByPageRequest
from leyan_proto.digismart.trade.dgt_trade_pb2 import GetRecentTradesRequest
from leyan_proto.digismart.trade.dgt_trade_pb2 import GetTradeByTidRequest
from leyan_proto.digismart.trade.dgt_trade_pb2 import GetTradeRateRequest
from leyan_proto.digismart.trade.dgt_trade_pb2 import GetTradesByTidListAndChannelReq
from leyan_proto.digismart.trade.dgt_trade_pb2 import ResponseCode
from leyan_proto.digismart.trade.dgt_trade_pb2 import TradeInfo
from leyan_proto.digismart.trade.dgt_trade_pb2 import TradeStatus as pb_TaobaoTradeStatus
from leyan_proto.digismart.trade.dgt_trade_pb2 import TryGetPlaintextNickByOpenUidRequest
from leyan_proto.digismart.trade.dgt_trade_pb2 import UpdateMemoType
from leyan_proto.digismart.trade.dgt_trade_pb2 import UpdateTradeMemoRequest
from leyan_proto.digismart.trade.dgt_trade_pb2 import UpdateTradeMemoResponse
from leyan_proto.digismart.trade.dgt_trade_pb2_grpc import DgtTradeServiceStub
from loguru import logger
from sqlalchemy import func

from robot_metrics import Stats
from robot_processor.decorators import retry

_statsd_it = Stats.Client.timer(client_name="trade-server")
_logger = logger.bind(client="trade")


def retry_condition(r):
    return r[0] == ResponseCode.API_SUCCESS or r[0] != ResponseCode.API_ERROR


class TradeClient:
    def __init__(self):
        self._skip = False
        self._timeout = 5
        self._stub = None
        self._modify_seller_nick = {}

    def init_app(self, app):
        self._skip = app.config.get("TRADE_SKIP", False)
        self._timeout = app.config.get("TRADE_TIMEOUT", 5)
        self._modify_seller_nick = app.config.get("MODIFY_SELLER_NICK", {})
        self._stub = GaiaStub(DgtTradeServiceStub, "dgt-trade-server", tracing_enabled=True)

    @property
    def client(self):
        return cast(DgtTradeServiceStub, self._stub)

    @staticmethod
    def set_business_order_status(sid, orders):
        from robot_processor.business_order.models import BusinessOrder

        oid_ids = list((orders.keys()))
        for bo in (
            BusinessOrder.query.with_entities(
                BusinessOrder.v_oid,
                func.count("*").label("cnt"),
            )
            .filter(
                BusinessOrder.sid == sid,
                BusinessOrder.v_oid.in_(oid_ids),
                BusinessOrder.deleted.isnot(True),
            )
            .group_by(BusinessOrder.v_oid)
        ):
            orders[bo.v_oid]["has_business_order"] = True
            orders[bo.v_oid]["business_order_total"] = bo.cnt

        return orders

    @_statsd_it
    def get_trades_by_buyer(self, sid: str, seller_nick: str, buyer_nick: str, open_uid: str, token: str):
        trades, orders = self._get_trades_by_buyer(seller_nick, buyer_nick, open_uid, token)

        modify_nick = self._modify_seller_nick.get(seller_nick)
        if modify_nick:
            trades_changed, orders_changed = self._get_trades_by_buyer(modify_nick, buyer_nick, open_uid, token)
            trades = trades + trades_changed
            orders.update(orders_changed)

        self.set_business_order_status(sid, orders)
        return {"trades": trades}

    @logger.catch(level="DEBUG", default=([], {}))
    def _get_trades_by_buyer(self, seller_nick: str, buyer_nick: str, open_uid: str, token: str):
        req = GetRecentTradesRequest(
            seller_nick=seller_nick,
            buyer_id=buyer_nick,
            buyer_open_uid=open_uid,
            access_token=token,
        )
        trades = []
        orders = {}
        resp = self.client.GetRecentTrades(req, timeout=self._timeout)
        for trade in resp.trades:
            trade_info = MessageToDict(
                trade,
                preserving_proto_field_name=True,
                including_default_value_fields=True,
            )
            for order in trade_info["orders"]:
                orders[order["oid"]] = order
                order["business_order_total"] = 0

            trades.append(trade_info)
        return trades, orders

    @logger.catch(level="DEBUG", default=TradeInfo())
    @_statsd_it
    def get_trade_by_tid(self, sid: str, tid: str):
        req = GetTradeByTidRequest()
        req.tid = tid
        req.store_id = sid
        return self.client.GetTradeByTid(req)

    @_statsd_it
    def try_get_plaintext_nick_from_trade(self, buyer_open_uid: str):
        rpc_req = TryGetPlaintextNickByOpenUidRequest(buyer_open_uid=buyer_open_uid)
        rpc_res = self.client.TryGetPlaintextNickByOpenUid(rpc_req)
        if rpc_res.code != ResponseCode.API_SUCCESS:
            _logger.warning("method=TryGetPlaintextNickByOpenUid, " f"code={rpc_res.code}, error={rpc_res.msg}")
            return ""
        return rpc_res.plaintext_buyer_nick

    @logger.catch(level="WARNING")
    @_statsd_it
    def get_trade_by_tid_and_channel(
        self,
        tids: List[str],
        channel_type: str = "PDD",
        sid=None,
        token=None,
        org_id=None,
    ):
        from robot_processor.utils import string_wrapper

        req = GetTradesByTidListAndChannelReq(
            tid_list=tids,
            channel_type=channel_type,
            access_token=token or "",
            sid=string_wrapper(sid),
            org_id=org_id or "",
        )
        return self.client.GetTradesByTidListAndChannel(req)

    @logger.catch(level="DEBUG")
    @_statsd_it
    def get_trade_from_multi_plat_by_tid(self, sid: str, seller_nick: str, tid: str, token: str):
        """调用trade接口依次查询历史库、同步库
        、淘宝api，查到订单数据立即返回，否则查询下一个数据源
        """
        req = GetTradeByTidRequest()
        req.tid = tid
        req.store_id = sid
        req.access_token = token

        trade_info = MessageToDict(
            self.client.GetTradeFromDbOrApiByTid(req),
            preserving_proto_field_name=True,
            including_default_value_fields=True,
        )
        """
            当出现查询结果的seller_nickname与店铺的seller_nick
            不一样，丢掉这条结果
        """
        logger.info(f"GetTradeFromDbOrApiByTid. trades={trade_info}")
        orders = {}
        for order in trade_info["orders"]:
            orders[order["oid"]] = order
            order["business_order_total"] = 0

        self.set_business_order_status(sid, orders)
        return trade_info

    @_statsd_it
    @retry(3, retry_condition)
    def update_memo(
        self,
        tid: str,
        token: str,
        memo: str,
        flag: int,
        time_flag: bool,
        update_memo_type=UpdateMemoType.NEW,
    ):
        req = UpdateTradeMemoRequest()
        req.tid = tid
        req.flag = cast("FlagColor.ValueType", flag)
        req.memo = memo
        req.access_token = token
        req.update_memo_type = update_memo_type
        req.time_flag = time_flag
        try:
            resp = self.client.UpdateTradeMemo(req)
        except BaseException as e:
            logger.exception(f"call update memo error: {str(e)}")
            return 500, str(e)
        else:
            return resp.code, resp.msg

    def update_memo_for_job(
        self, sid: str, req: UpdateTradeMemoRequest, check_rate_limit=True
    ) -> UpdateTradeMemoResponse:
        from robot_processor.client import token_bucket_limiter

        if check_rate_limit:
            token_bucket_key = "taobao:trade:memo"
            if not token_bucket_limiter.try_acquire_token_for_store(token_bucket_key, sid):
                raise Retry(message=f"{token_bucket_key}:{sid} is rate limited", delay=3000)
        return self.client.UpdateTradeMemo(req)

    @logger.catch(level="DEBUG", default={"code": "API_ERROR", "msg": "api 调用错误"})
    @_statsd_it
    def get_rate_by_tid_list(self, tids: List, token: str):
        req = GetTradeRateRequest()
        req.tid.extend(tids)
        req.access_token = token
        resp = self.client.GetTradeRateByTid(req)
        rate_info = MessageToDict(resp, preserving_proto_field_name=True, including_default_value_fields=True)
        return rate_info

    @logger.catch(level="DEBUG", default=[])
    @_statsd_it
    def get_trade_by_tid_list(self, tid_list):
        req = BatchGetTradeByIdsRequest(tid=tid_list)
        resp = self.client.BatchGetTradeByIds(req)
        return [
            MessageToDict(
                trade,
                preserving_proto_field_name=True,
                including_default_value_fields=True,
            )
            for trade in resp.trades
        ]

    def tid_orders_map(self, ids):
        from itertools import chain

        # apollo ?
        limit = 100
        return {
            trade["trade_id"]: trade["orders"]
            for trade in chain.from_iterable(
                [self.get_trade_by_tid_list(ids[i : i + limit]) for i in range(0, len(ids), limit)]
            )
        }

    @_statsd_it
    def get_channel_info_by_tid_list(self, tid_list: List[str], org_id: str):
        req = GetChannelInfoByTidsReq(tid=tid_list, org_id=org_id)
        res = self.client.GetChannelInfoByTids(req)
        if res.code != ResponseCode.API_SUCCESS:
            logger.error(f"get_channel_info_by_tid_list error: {res.msg}")
            return []
        return res.trade_channel_info

    @_statsd_it
    def get_period_trade_list_by_page(
        self,
        page_no,
        page_size,
        start_time,
        end_time,
        platform,
        sid=None,
        seller_nick=None,
    ):
        req = GetPeriodTradeListByPageRequest(
            page_no=page_no,
            page_size=page_size,
            start_time=start_time,
            end_time=end_time,
            channel_type=platform,
        )
        if sid:
            req.store_id = sid
        if seller_nick:
            req.seller_nick = seller_nick
        resp = self.client.GetPeriodTradeListByPage(req)
        return resp

    @staticmethod
    def to_pdd_order_status_zh(pdd_order_status: int):
        """发货状态，枚举值：1：待发货，2：已发货待签收，3：已签收

        Reference:
            https://open.pinduoduo.com/application/document/api?id=pdd.order.information.get
        """
        return {
            1: "待发货",
            2: "已发货待签收",
            3: "已签收",
        }[pdd_order_status]

    @staticmethod
    def to_pdd_refund_status_zh(pdd_refund_status: int):
        """退款状态

        Reference:
            https://open.pinduoduo.com/application/document/api?id=pdd.order.information.get
        """
        return {1: "无售后或售后关闭", 2: "售后处理中", 3: "退款中", 4: "退款成功"}[pdd_refund_status]

    @staticmethod
    def to_taobao_trade_status_zh(taobao_trade_status):
        return {
            pb_TaobaoTradeStatus.WAIT_BUYER_PAY: "等待买家付款",
            pb_TaobaoTradeStatus.WAIT_SELLER_SEND_GOODS: "等待卖家发货",
            pb_TaobaoTradeStatus.SELLER_CONSIGNED_PART: "卖家部分发货",
            pb_TaobaoTradeStatus.WAIT_BUYER_CONFIRM_GOODS: "等待买家确认收货",
            pb_TaobaoTradeStatus.TRADE_BUYER_SIGNED: "买家已签收（货到付款专用）",
            pb_TaobaoTradeStatus.TRADE_FINISHED: "交易成功",
            pb_TaobaoTradeStatus.TRADE_CLOSED: "交易关闭",
            pb_TaobaoTradeStatus.TRADE_CLOSED_BY_TAOBAO: "交易被淘宝关闭",
            pb_TaobaoTradeStatus.TRADE_NO_CREATE_PAY: "没有创建支付宝交易",
            pb_TaobaoTradeStatus.WAIT_PRE_AUTH_CONFIRM: "余额宝0元购合约中",
            pb_TaobaoTradeStatus.PAY_PENDING: "外卡支付付款确认中",
            pb_TaobaoTradeStatus.ALL_WAIT_PAY: "所有买家未付款的交易",
            pb_TaobaoTradeStatus.ALL_CLOSED: "所有关闭的交易",
            pb_TaobaoTradeStatus.PAID_FORBID_CONSIGN: "该状态代表订单已付款但是处于禁止发货状态",
        }.get(taobao_trade_status, "未知")
