from typing import Optional, List
from loguru import logger
from requests_opentracing import SessionTracing
from pydantic import BaseModel, Field

from robot_processor.enums import ProviderFactoryType
from rpa.conf import rpa_config as config


class AsgardClient:
    session = SessionTracing()

    @property
    def endpoint(self):
        return config.ASGARD_ENDPOINT

    @property
    def timeout(self):
        return config.ASGARD_TIMEOUT

    def get(self, url, param=None):
        try:
            resp = self.session.get(
                url,
                timeout=self.timeout
            )
        except BaseException as exc:
            logger.error(f"call asgard error : {str(exc)}")
            return {}
        return resp.json()

    def post(self, url, body):
        try:
            resp = self.session.post(
                url,
                json=body,
                timeout=self.timeout
            )
        except BaseException as exc:
            logger.error(f"call asgard error : {str(exc)}")
            return {}
        return resp.json()

    def post_file(self, url, file):
        try:
            resp = self.session.post(
                url,
                files={'file': file.read()},
                timeout=self.timeout
            )
        except BaseException as exc:
            logger.error(str(exc))
            return {}
        return resp.json()

    def get_providers(self, sid):
        url: str = f"{self.endpoint}/shops/{sid}/providers"
        return self.get(url)

    def query_provider(self, query, provider_id, sid):
        url: str = f"{self.endpoint}/shops/{sid}/providers/{provider_id}"

        return self.post(url, query)

    def del_row(self, query, provider_id, sid):
        url: str = f"{self.endpoint}/shops/{sid}/providers/{provider_id}/rows"

        return self.post(url, query)

    def upload(self, query, sid):
        url = f"{self.endpoint}/shops/{sid}/providers/upload"

        return self.post(url, query)

    def upload_check(self, upload_type, file, sid):
        url = f"{self.endpoint}/shops/{sid}/providers/upload/check?upload_type={upload_type}"

        return self.post_file(url, file)

    def download(self, query, provider_id, sid):
        url = f"{self.endpoint}/shops/{sid}/providers/{provider_id}/download"

        return self.post(url, query)

    def titles(self, provider_id, sid):
        url: str = f"{self.endpoint}/shops/{sid}/providers/{provider_id}/titles"

        return self.get(url)

    def prefilled(self, sid, config, body):
        url: str = f"{self.endpoint}/shops/{sid}/providers/prefilled"
        body.update({"config": config})
        logger.debug(body)
        return self.post(url, body)

    def bind_provider(self, query):
        url: str = f"{self.endpoint}/shops/{query['sid']}/providers/bind"
        return self.post(url, query)

    class Schema:
        class ProviderTemplate(BaseModel):
            id: Optional[int]
            sid: Optional[str]
            provider_name: str
            provider_type: ProviderFactoryType = Field(default=ProviderFactoryType.PRODUCT)

        class Title(BaseModel):
            sid: str
            provider_id: int
            title: str
            title_type: str
            label: Optional[str]
            disabled: Optional[bool]

    class Response:
        class GetProviders(BaseModel):
            data: List['AsgardClient.Schema.ProviderTemplate'] = Field(default_factory=list)

        class Titles(BaseModel):
            titles: List['AsgardClient.Schema.Title'] = Field(default_factory=list)


AsgardClient.Response.GetProviders.update_forward_refs()
AsgardClient.Response.Titles.update_forward_refs()
