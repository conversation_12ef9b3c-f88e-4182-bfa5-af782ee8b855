import hashlib
import json
import time
from typing import Any
from typing import Dict
from typing import List
from typing import Type
from typing import TypeVar

import arrow
from loguru import logger
from pydantic import BaseModel
from pydantic import root_validator

from robot_processor.client_mixins import Session
from robot_processor.utils import make_fields_optional

RespT = TypeVar("RespT", bound=BaseModel)


@make_fields_optional
class KuaishouRefund(BaseModel):
    oid: int
    refundId: int
    handlingWay: int
    negotiateStatus: int
    refundFee: int
    skuId: int
    refundReason: int
    status: int
    refundType: int
    sellerId: int
    refundDesc: str
    submitTime: int
    relItemId: int
    negotiateUpdateTime: int
    updateTime: int
    createTime: int
    relSkuId: int
    skuNick: str
    logisticsId: int
    endTime: int
    itemId: int
    receiptStatus: int
    refundReasonDesc: str
    expireTime: int

    readableHandlingWay: str
    readableNegotiateStatus: str
    readableStatus: str
    readableRefundType: str
    readableReceiptStatus: str
    createDatetime: str
    submitDatetime: str
    expireDatetime: str
    updateDatetime: str

    @root_validator(skip_on_failure=True)
    def generate_all_readable_str(cls, values):
        handlingWay = values.get("handlingWay")
        if handlingWay is not None:
            handlingWayMap = {1: "退货退款", 10: "仅退款", 3: "换货"}
            values["readableHandlingWay"] = handlingWayMap.get(handlingWay)
        negotiateStatus = values.get("negotiateStatus")
        if negotiateStatus is not None:
            negotiateStatusMap = {0: "未知状态", 1: "待商家处理", 2: "商家同意", 3: "商家驳回，等待买家修改"}
            values["readableNegotiateStatus"] = negotiateStatusMap.get(negotiateStatus)
        status = values.get("status")
        if status is not None:
            statusMap = {
                10: "买家已经申请退款，等待卖家同意",
                12: "卖家已拒绝，等待买家处理",
                20: "协商纠纷，等待平台处理",
                30: "卖家已经同意退款，等待买家退货",
                40: "买家已经退货，等待卖家确认收货",
                45: "卖家已经发货，等待买家确认收货",
                50: "卖家已经同意退款，等待系统执行退款",
                60: "退款成功",
                70: "退款关闭",
            }
            values["readableStatus"] = statusMap.get(status)
        refundType = values.get("refundType")
        if refundType is not None:
            refundTypeMap = {0: "未知", 1: "买家申请退款", 2: "卖家主动退款"}
            values["readableRefundType"] = refundTypeMap.get(refundType)
        receiptStatus = values.get("receiptStatus")
        if receiptStatus is not None:
            receiptStatusMap = {0: "未知", 1: "未收到货", 2: "已收到货"}
            values["readableReceiptStatus"] = receiptStatusMap.get(receiptStatus)

        createTime = values.get("createTime")
        if createTime is not None:
            values["createDatetime"] = arrow.get(createTime, tzinfo="Asia/Shanghai").strftime("%Y-%m-%d %H:%M:%S")

        submitTime = values.get("submitTime")
        if submitTime is not None:
            values["submitDatetime"] = arrow.get(submitTime, tzinfo="Asia/Shanghai").strftime("%Y-%m-%d %H:%M:%S")

        expireTime = values.get("expireTime")
        if expireTime is not None:
            values["expireDatetime"] = arrow.get(expireTime, tzinfo="Asia/Shanghai").strftime("%Y-%m-%d %H:%M:%S")

        updateTime = values.get("updateTime")
        if updateTime is not None:
            values["updateDatetime"] = arrow.get(updateTime, tzinfo="Asia/Shanghai").strftime("%Y-%m-%d %H:%M:%S")
        return values


@make_fields_optional
class KuaishouRefundListResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        currentPage: int
        pageSize: int
        totalPage: int
        totalSize: int
        beginTime: int
        endTime: int
        pcursor: str
        refundOrderInfoList: List[KuaishouRefund]

    result: int
    data: Data
    error_msg: str


@make_fields_optional
class KuaishouOrderFeeDetailResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        totalFee: int
        discountFee: int
        expressFee: int
        platformAllowance: int
        sellerRecv: int
        buyerPay: int
        governmentDiscount: int
        platformBearAmount: int

    data: Data
    result: int
    error_msg: str


@make_fields_optional
class KuaishouRefundCommentBasicListResp(BaseModel):
    @make_fields_optional
    class RefundComment(BaseModel):
        title: str
        role: int
        createTime: int
        staffAccountId: int
        staffAccountName: str

    result: int
    error_msg: str
    data: List[RefundComment]


@make_fields_optional
class KuaishouRefundAgreeResp(BaseModel):
    result: int
    error_msg: str


@make_fields_optional
class KuaishouRefundConfirmReceiptResp(BaseModel):
    result: int
    error_msg: str


@make_fields_optional
class MerchantLogisticsInfoView(BaseModel):
    expressNo: str
    expressCode: int

    expressName: str

    @root_validator(skip_on_failure=True)
    def generate_all_readable_str(cls, values):
        expressCode = values.get("expressCode")
        if expressCode is not None:
            express_code_map = {
                1: "百世快递",
                2: "EMS",
                3: "申通快递",
                4: "顺丰速运",
                5: "天天快递",
                6: "圆通速递",
                7: "韵达快运",
                8: "邮政快递包裹",
                9: "中通快递",
                10: "宅急送",
                11: "韵达快递",
                12: "中通快运",
                13: "圆通快运",
                14: "京东",
                15: "优速物流",
                17: "德邦",
                18: "民航快递",
                19: "快捷速递",
                20: "DHL中国件",
                21: "凡宇快递",
                22: "凡客配送",
                23: "国通快递",
                24: "佳吉快运",
                25: "跨越速运",
                26: "民邦速递",
                27: "全晨快递",
                28: "全一快递",
                29: "如风达",
                30: "圣安物流",
                31: "速尔快递",
                32: "盛辉物流",
                33: "万象物流",
                34: "新邦物流",
                35: "中铁快运",
                36: "信丰物流",
                37: "远成物流",
                38: "运通中港",
                39: "增益速递",
                40: "安能快递",
                41: "AAE",
                42: "Aramex",
                43: "安能快运（安能物流）",
                44: "安得物流",
                45: "安迅物流",
                46: "安捷物流",
                47: "Xlobo贝海国际",
                48: "百世快运",
                49: "斑马物流",
                50: "传喜物流",
                51: "承诺达",
                52: "成都东骏物流",
                53: "德邦快递",
                54: "大田物流",
                55: "递四方",
                56: "当当",
                57: "D速快递",
                58: "EMS-国际件",
                59: "EWE全球快递",
                61: "飞豹快递",
                62: "飞洋快递",
                63: "高捷快运",
                64: "海外环球",
                65: "黄马甲",
                66: "黑猫宅急便",
                67: "汇通天下物流",
                68: "环球速运",
                69: "恒路物流",
                70: "京东订单",
                71: "九曳供应链",
                72: "加运美",
                73: "佳怡物流",
                74: "京广速递",
                75: "加州猫速递",
                76: "龙邦速递",
                77: "联邦快递",
                78: "联昊通",
                79: "日日顺物流",
                80: "晟邦物流",
                81: "苏宁物流",
                82: "苏宁订单",
                83: "盛丰物流",
                84: "速必达",
                85: "申通国际",
                86: "顺心捷达",
                87: "商桥物流",
                88: "天地华宇",
                89: "USPS",
                90: "UPS",
                91: "微特派",
                92: "万家物流",
                93: "亚马逊中国",
                94: "远成快运",
                95: "亚风速递",
                96: "燕文物流",
                97: "邮政标准快递",
                98: "优邦速运",
                99: "易客满",
                100: "壹米滴答",
                101: "一智通",
                102: "芝麻开门",
                103: "中邮速递",
                104: "中铁物流",
                105: "中粮鲜到家物流",
                106: "中外运速递",
                107: "卓志速运",
                108: "TNT",
                109: "极兔速递",
                110: "全时速运",
                111: "特急送",
                112: "众邮快递",
                113: "菜鸟速递",
                114: "安鲜达",
                115: "韩国邮政",
                116: "龙邦速递",
                117: "易达通快递",
                118: "林氏物流",
                119: "EMS包裹",
                120: "中通国际",
                121: "新顺丰(NSF)",
                122: "澳邮中国快运",
                123: "圆通国际",
                124: "方舟速递",
                125: "联合速递",
                126: "海信物流",
                127: "丰网速运",
                128: "一站通速运",
                129: "快弟来了",
                130: "速邮达",
                131: "敏華物流",
                132: "天马迅达",
                133: "小飞侠速递",
                134: "天翼快递",
                135: "中通冷链",
                136: "京东快运",
                137: "京东大件",
                138: "具语物流",
                139: "邮政电商标快",
                140: "1919物流",
                141: "速腾物流",
                142: "平安达腾飞快递",
                143: "小米物流",
                144: "全友大件物流",
                145: "捷安信物流",
                147: "顾家物流",
                1001: "跨境-中通快递",
                1002: "跨境-圆通快递",
                1003: "跨境-申通快递",
                1004: "跨境-百世快递",
                1005: "跨境-韵达快递",
                1007: "跨境-顺丰速运",
                1008: "跨境-EMS",
                9999: "其他",
                19999: "跨境-其他",
            }
            express_name = express_code_map.get(expressCode)
            values["expressName"] = express_name
        return values


@make_fields_optional
class ReturnFreightInfo(BaseModel):
    returnFreightAmount: int
    returnFreightStatus: int
    returnFreightApplyDesc: str
    returnFreightApplyImages: List[str]


@make_fields_optional
class KuaishouRefundDetailResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        logisticsInfo: MerchantLogisticsInfoView
        refundFee: int
        returnFreightInfo: ReturnFreightInfo

    result: int
    data: Data
    error_msg: str


@make_fields_optional
class KuaishouMemoAddResp(BaseModel):
    result: int
    error_msg: str


@make_fields_optional
class OrderBaseInfo(BaseModel):
    oid: int
    payTime: int
    buyerOpenId: str
    buyerNick: str
    sellerOpenId: str
    sellerNick: str
    expressFee: int
    discountFee: int
    totalFee: int
    status: int
    sendTime: int
    refundTime: int
    createTime: int
    updateTime: int
    remark: str
    cpsType: int
    validPromiseShipmentTimeStamp: int
    preSale: int
    recvTime: int
    commentStatus: int
    payType: int


@make_fields_optional
class OrderItemInfo(BaseModel):
    skuId: int
    relSkuId: int
    skuDesc: str
    skuNick: str
    itemId: int
    relItemId: int
    itemTitle: str
    itemLinkUrl: str
    itemPicUrl: str
    num: int
    originalPrice: int
    discountFee: int
    price: int
    goodsCode: str
    warehouseCode: str


@make_fields_optional
class OrderLogisticsInfo(BaseModel):
    expressNo: str
    expressCode: int
    logisticsId: int

    expressName: str

    @root_validator(skip_on_failure=True)
    def generate_all_readable_str(cls, values):
        expressCode = values.get("expressCode")
        if expressCode is not None:
            express_code_map = {
                1: "百世快递",
                2: "EMS",
                3: "申通快递",
                4: "顺丰速运",
                5: "天天快递",
                6: "圆通速递",
                7: "韵达快运",
                8: "邮政快递包裹",
                9: "中通快递",
                10: "宅急送",
                11: "韵达快递",
                12: "中通快运",
                13: "圆通快运",
                14: "京东",
                15: "优速物流",
                17: "德邦",
                18: "民航快递",
                19: "快捷速递",
                20: "DHL中国件",
                21: "凡宇快递",
                22: "凡客配送",
                23: "国通快递",
                24: "佳吉快运",
                25: "跨越速运",
                26: "民邦速递",
                27: "全晨快递",
                28: "全一快递",
                29: "如风达",
                30: "圣安物流",
                31: "速尔快递",
                32: "盛辉物流",
                33: "万象物流",
                34: "新邦物流",
                35: "中铁快运",
                36: "信丰物流",
                37: "远成物流",
                38: "运通中港",
                39: "增益速递",
                40: "安能快递",
                41: "AAE",
                42: "Aramex",
                43: "安能快运（安能物流）",
                44: "安得物流",
                45: "安迅物流",
                46: "安捷物流",
                47: "Xlobo贝海国际",
                48: "百世快运",
                49: "斑马物流",
                50: "传喜物流",
                51: "承诺达",
                52: "成都东骏物流",
                53: "德邦快递",
                54: "大田物流",
                55: "递四方",
                56: "当当",
                57: "D速快递",
                58: "EMS-国际件",
                59: "EWE全球快递",
                61: "飞豹快递",
                62: "飞洋快递",
                63: "高捷快运",
                64: "海外环球",
                65: "黄马甲",
                66: "黑猫宅急便",
                67: "汇通天下物流",
                68: "环球速运",
                69: "恒路物流",
                70: "京东订单",
                71: "九曳供应链",
                72: "加运美",
                73: "佳怡物流",
                74: "京广速递",
                75: "加州猫速递",
                76: "龙邦速递",
                77: "联邦快递",
                78: "联昊通",
                79: "日日顺物流",
                80: "晟邦物流",
                81: "苏宁物流",
                82: "苏宁订单",
                83: "盛丰物流",
                84: "速必达",
                85: "申通国际",
                86: "顺心捷达",
                87: "商桥物流",
                88: "天地华宇",
                89: "USPS",
                90: "UPS",
                91: "微特派",
                92: "万家物流",
                93: "亚马逊中国",
                94: "远成快运",
                95: "亚风速递",
                96: "燕文物流",
                97: "邮政标准快递",
                98: "优邦速运",
                99: "易客满",
                100: "壹米滴答",
                101: "一智通",
                102: "芝麻开门",
                103: "中邮速递",
                104: "中铁物流",
                105: "中粮鲜到家物流",
                106: "中外运速递",
                107: "卓志速运",
                108: "TNT",
                109: "极兔速递",
                110: "全时速运",
                111: "特急送",
                112: "众邮快递",
                113: "菜鸟速递",
                114: "安鲜达",
                115: "韩国邮政",
                116: "龙邦速递",
                117: "易达通快递",
                118: "林氏物流",
                119: "EMS包裹",
                120: "中通国际",
                121: "新顺丰(NSF)",
                122: "澳邮中国快运",
                123: "圆通国际",
                124: "方舟速递",
                125: "联合速递",
                126: "海信物流",
                127: "丰网速运",
                128: "一站通速运",
                129: "快弟来了",
                130: "速邮达",
                131: "敏華物流",
                132: "天马迅达",
                133: "小飞侠速递",
                134: "天翼快递",
                135: "中通冷链",
                136: "京东快运",
                137: "京东大件",
                138: "具语物流",
                139: "邮政电商标快",
                140: "1919物流",
                141: "速腾物流",
                142: "平安达腾飞快递",
                143: "小米物流",
                144: "全友大件物流",
                145: "捷安信物流",
                147: "顾家物流",
                1001: "跨境-中通快递",
                1002: "跨境-圆通快递",
                1003: "跨境-申通快递",
                1004: "跨境-百世快递",
                1005: "跨境-韵达快递",
                1007: "跨境-顺丰速运",
                1008: "跨境-EMS",
                9999: "其他",
                19999: "跨境-其他",
            }
            express_name = express_code_map.get(expressCode)
            values["expressName"] = express_name
        return values


@make_fields_optional
class OrderDeliveryInfo(BaseModel):
    splitDeliveryOrder: bool
    deliveryStatus: int

    readableDeliveryStatus: str

    @root_validator(skip_on_failure=True)
    def generate_all_readable_str(cls, values):
        deliveryStatus = values.get("deliveryStatus")
        if deliveryStatus is not None:
            delivery_status_map = {10: "部分发货", 40: "全部发货"}
            values["readableDeliveryStatus"] = delivery_status_map.get(deliveryStatus)
        return values


@make_fields_optional
class OrderNoteInfo(BaseModel):
    createTime: int
    note: str
    flagTagCode: str
    operatorName: str


@make_fields_optional
class OrderNote(BaseModel):
    orderNoteInfo: List[OrderNoteInfo]


@make_fields_optional
class KuaishouQueryOrderResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        orderBaseInfo: OrderBaseInfo
        orderItemInfo: OrderItemInfo
        orderLogisticsInfo: List[OrderLogisticsInfo]
        orderDeliveryInfo: OrderDeliveryInfo
        orderNote: OrderNote

    result: int
    data: Data
    error_msg: str


class KuaishouClient:
    session = Session()

    def __init__(self):
        self.sign_secret = None
        self.app_key = None

    def init_app(self, app_key: str, sign_secret: str):
        self.app_key = app_key
        self.sign_secret = sign_secret

    @property
    def endpoint(self):
        return "https://openapi.kwaixiaodian.com/"

    def _sign(self, params: dict):
        sorted_params = sorted(params.items())
        sign_str = "&".join(f"{k}={v}" for k, v in sorted_params)
        sign_str += f"&signSecret={self.sign_secret}"
        return hashlib.md5(sign_str.encode()).hexdigest()

    def _request_post(self, access_token: str, path: str, method: str, data: dict, resp_cls: Type[RespT]) -> RespT:
        params = {
            "appkey": self.app_key,
            "method": method,
            "version": "1",
            "param": json.dumps(data, separators=(",", ":"), ensure_ascii=False),
            "access_token": access_token,
            "timestamp": int(time.time() * 1000),
            "signMethod": "MD5",
        }
        sign = self._sign(params)
        params["sign"] = sign
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        response = self.session.post(self.endpoint + path, data=params, headers=headers)
        resp_json = response.json()
        logger.info(f"kuaishou resp: {resp_json}")
        return resp_cls.parse_obj(resp_json)

    def _request_get(self, access_token: str, path: str, method: str, data: dict, resp_cls: Type[RespT]) -> RespT:
        params = {
            "appkey": self.app_key,
            "method": method,
            "version": "1",
            "param": json.dumps(data, separators=(",", ":"), ensure_ascii=False),
            "access_token": access_token,
            "timestamp": int(time.time() * 1000),
            "signMethod": "MD5",
        }
        sign = self._sign(params)
        params["sign"] = sign

        response = self.session.get(self.endpoint + path, params=params)

        resp_json = response.json()
        logger.info(f"kuaishou resp: {resp_json}")
        return resp_cls.parse_obj(resp_json)

    def refund_list(
        self, access_token: str, begin_time: int, end_time: int, page_no: int, page_size: int, pcursor: str
    ):
        data = {
            "beginTime": begin_time,
            "endTime": end_time,
            "currentPage": page_no,
            "pageSize": page_size,
            "queryType": 2,
            "type": 9,
            "pcursor": pcursor,
        }
        return self._request_post(
            access_token,
            "open/seller/order/refund/pcursor/list",
            "open.seller.order.refund.pcursor.list",
            data,
            KuaishouRefundListResp,
        )

    def query_order(self, access_token: str, oid: str):
        data = {"oid": int(oid)}
        return self._request_post(access_token, "open/order/detail", "open.order.detail", data, KuaishouQueryOrderResp)

    def refund_detail(self, access_token: str, refund_id: str):
        data = {"refundId": int(refund_id)}
        return self._request_post(
            access_token,
            "open/seller/order/refund/detail",
            "open.seller.order.refund.detail",
            data,
            KuaishouRefundDetailResp,
        )

    def memo_add(self, access_token: str, oid: str, note: str | None, flag: int | None):
        data: Dict[str, Any] = {"orderId": int(oid)}
        if note:
            data["note"] = note
        if flag:
            data["flag"] = flag
        return self._request_post(
            access_token, "open/seller/order/note/add", "open.seller.order.note.add", data, KuaishouMemoAddResp
        )

    def refund_agree(self, access_token: str, refund_id: str, refund_amount: int):
        data = {"refundId": int(refund_id), "refundAmount": refund_amount}
        return self._request_post(
            access_token,
            "open/seller/order/refund/approve",
            "open.seller.order.refund.approve",
            data,
            KuaishouRefundAgreeResp,
        )

    # 商家确认收货
    def refund_confirm_receipt(self, access_token: str, refund_id: str, return_freight_amount: int | None):
        data = {"refundId": int(refund_id)}
        if return_freight_amount:
            data["returnFreightAmount"] = return_freight_amount
            data["returnFreightHandlingAdvice"] = 1

        return self._request_post(
            access_token,
            "open/seller/order/refund/confirm/receipt",
            "open.seller.order.refund.confirm.receipt",
            data,
            KuaishouRefundConfirmReceiptResp,
        )

    def order_fee_detail(self, access_token: str, oid: str):
        data = {"orderId": int(oid)}
        return self._request_post(
            access_token,
            "open/seller/order/fee/detail",
            "open.seller.order.fee.detail",
            data,
            KuaishouOrderFeeDetailResp,
        )

    def refund_comment_basic_list(self, access_token: str, refund_id: str):
        data = {"refundId": int(refund_id)}
        return self._request_get(
            access_token,
            "open/refund/comment/basic/list",
            "open.refund.comment.basic.list",
            data,
            KuaishouRefundCommentBasicListResp,
        )
