import hashlib
import time
from typing import TypeVar, Type, Dict, Any, List

from loguru import logger
from pydantic import BaseModel

from robot_processor.client import app_config
from robot_processor.client.errors import XiaohongshuServiceError
from robot_processor.client_mixins import Session
from robot_processor.utils import make_fields_optional

RespT = TypeVar("RespT", bound=BaseModel)


class AfterSaleBasicInfo(BaseModel):
    returnsId: str
    returnType: int
    reasonId: int
    reasonNameZh: str
    status: int
    userId: str
    orderId: str
    applyTime: int
    updatedAt: int
    expireTime: int
    desc: str
    returnsTag: int
    expectedRefundAmountYuan: float


@make_fields_optional
class ListAfterSaleResp(BaseModel):
    class Data(BaseModel):
        afterSaleBasicInfos: List[AfterSaleBasicInfo]
        totalCount: int

    code: str
    msg: str
    success: bool
    data: Data


@make_fields_optional
class ReturnAddress(BaseModel):
    province: str
    city: str
    county: str
    town: str
    street: str
    phone: str
    name: str
    fullAddress: str


@make_fields_optional
class AfterSaleSku(BaseModel):
    skuId: str
    skuName: str
    image: str
    price: float
    boughtCount: int
    appliedCount: int
    appliedTotalAmountYuan: float
    scskucode: str
    barcode: str
    skuERPCode: str


@make_fields_optional
class OrderLogisticsInfo(BaseModel):
    expressNo: str
    expressCompanyCode: str
    expressCompanyName: str


@make_fields_optional
class ReturnLogisticsInfo(BaseModel):
    expressNo: str
    expressCompanyCode: str
    expressCompanyName: str
    fillExpressNoTime: int
    expressSignTime: int


@make_fields_optional
class AfterSaleLogisticsInfo(BaseModel):
    order: OrderLogisticsInfo
    afterSale: ReturnLogisticsInfo


@make_fields_optional
class NegotiateRecord(BaseModel):
    title: str
    operatorRoleName: str
    operatorRole: int
    time: str


@make_fields_optional
class AfterSaleInfo(BaseModel):
    returnsId: str
    returnType: int
    reasonId: int
    reasonNameZh: str
    status: int
    userId: str
    orderId: str
    applyTime: int
    updatedAt: int
    expireTime: int
    returnAddress: ReturnAddress
    proofPhotos: list[str]
    desc: str
    supportCarriageInsurance: bool
    skus: list[AfterSaleSku]
    closeReasonZh: str
    returnsTag: int
    appliedShipFeeAmountYuan: float
    appliedSkusAmountYuan: float
    expectedRefundAmountYuan: float
    refundAmountYuan: float
    refundStatus: int
    cargoStatus: int
    refundTime: int


@make_fields_optional
class AfterSaleDetail(BaseModel):
    afterSaleInfo: AfterSaleInfo
    logisticsInfo: AfterSaleLogisticsInfo
    negotiateRecords: list[NegotiateRecord]


@make_fields_optional
class AfterSaleDetailResp(BaseModel):
    code: str
    msg: str
    success: bool
    data: AfterSaleDetail


@make_fields_optional
class TrackRecord(BaseModel):
    @make_fields_optional
    class Location(BaseModel):
        provinceName: str
        cityName: str

    eventAt: str
    eventDesc: str
    trackingPartnerSyncAt: str
    nodeId: int
    subNodeId: int
    trackingStatus: int
    trackingStatusDesc: str
    location: Location


@make_fields_optional
class TrackInfo(BaseModel):
    orderId: str
    deliveryOrderId: str
    currentStatusDesc: str
    expressCompanyCode: str
    expressCompanyName: str
    expressNo: str
    records: list[TrackRecord]


@make_fields_optional
class OrderTrackingResp(BaseModel):
    @make_fields_optional
    class Data(BaseModel):
        orderTrackInfos: list[TrackInfo]

    success: bool
    error_code: int
    data: Data


@make_fields_optional
class TransferExtendInfo(BaseModel):
    orderDeclaredAmount: int


@make_fields_optional
class SimpleDeliveryOrder(BaseModel):
    deliveryOrderIndex: int
    expressCompanyCode: str
    expressTrackingNo: str
    itemIdList: list[str]
    skuIdList: list[str]
    status: int


@make_fields_optional
class SkuDetail(BaseModel):
    taxPerSku: int
    quantity: int
    scSkuCode: str
    pricePerSku: int
    rawPricePerSku: int
    merchantDiscountPerSku: int
    skuName: str
    paidAmountPerSku: int
    redDiscountPerSku: int
    erpCode: str
    depositAmountPerSku: int
    barcode: str
    skuId: str


@make_fields_optional
class SkuInfo(BaseModel):
    skuAfterSaleStatus: int
    totalPaidAmount: int
    skuSpec: str
    isChannel: bool
    totalRedDiscount: int
    erpcode: str
    skuDetailList: list[SkuDetail]
    skuTag: int
    totalMerchantDiscount: int
    skuQuantity: int
    skuName: str
    deliveryMode: int
    totalNetWeight: int
    totalTaxAmount: int
    skuId: str
    skuImage: str


@make_fields_optional
class BoundExtendInfo(BaseModel):
    zoneCodes: list[str]
    productValue: int
    payAmount: int
    shippingFee: int
    discountAmount: int
    taxAmount: int


@make_fields_optional
class OrderDetailResp(BaseModel):
    class Data(BaseModel):
        orderId: str
        cancelStatus: int
        expressCompanyCode: str
        receiverCityId: str
        expressTrackingNo: str
        totalMerchantDiscount: int
        receiverCountryId: str
        transferExtendInfo: TransferExtendInfo
        createdTime: int
        shopId: str
        promiseLastDeliveryTime: int
        orderTagList: list[str]
        finishTime: int
        orderAfterSalesStatus: int
        totalPayAmount: int
        paidTime: int
        sellerRemark: str
        presaleDeliveryStartTime: int
        simpleDeliveryOrderList: list[SimpleDeliveryOrder]
        planInfoName: str
        skuList: list[SkuInfo]
        deliveryTime: int
        receiverCountryName: str
        merchantActualReceiveAmount: int
        presaleDeliveryEndTime: int
        shopName: str
        orderStatus: int
        logistics: str
        boundExtendInfo: BoundExtendInfo
        paymentType: int
        receiverDistrictId: str
        totalDepositAmount: int
        totalNetWeightAmount: int
        outTradeNo: str
        logisticsMode: int
        outPromotionAmount: int
        unpack: bool
        totalChangePriceAmount: int
        receiverProvinceName: str
        totalRedDiscount: int
        sellerRemarkFlag: int
        openAddressId: str
        totalShippingFree: int
        updateTime: int
        userId: str
        receiverProvinceId: str
        cancelTime: int
        receiverCityName: str
        receiverDistrictName: str
        planInfoId: str

    success: bool
    error_code: int
    data: Data


@make_fields_optional
class OrderMemoResp(BaseModel):
    success: bool
    error_code: int
    data: str


class XiaohongshuClient:
    api_url = "https://ark.xiaohongshu.com/ark/open_api/v3/common_controller"
    session = Session()

    def __init__(self):
        self.app_key: str | None = None
        self.app_secret: str | None = None

    def init_app(self, app_key: str, app_secret: str):
        self.app_key = app_key
        self.app_secret = app_secret

    def _sign(self, body: dict) -> str:
        row_str = "{}?appId={}&timestamp={}&version=2.0{}".format(
            body["method"], body["appId"], body["timestamp"],
            self.app_secret
        )
        return hashlib.md5(row_str.encode("utf-8")).hexdigest()

    def _request(self, access_token: str,
                 method: str, data: dict, resp_cls: Type[RespT]) -> RespT:
        params = data.copy()
        params["method"] = method
        params["appId"] = self.app_key
        params["timestamp"] = int(time.time() * 1000)
        params["version"] = "2.0"
        params["sign"] = self._sign(params)
        params["accessToken"] = access_token
        resp = self.session.post(self.api_url, json=params,
                                 timeout=app_config.XHS_REQUEST_TIMEOUT)
        logger.info(f"xiaohongshu request url: {self.api_url} body: {params} "
                    f"resp: {resp.text}")
        resp_json = resp.json()
        if not resp_json.get("success"):
            raise XiaohongshuServiceError(resp_json)
        return resp_cls.parse_obj(resp_json)

    def list_aftersale(self,
                       access_token: str,
                       page_no: int, page_size: int,
                       order_id: str | None = None,
                       start_time: int | None = None,
                       end_time: int | None = None,
                       time_type: int | None = None) -> ListAfterSaleResp:
        # https://open.xiaohongshu.com/document/api?apiNavigationId=210&id=52&gatewayId=165&gatewayVersionId=2804&apiId=30115&apiParentNavigationId=16
        data: Dict[str, Any] = {
            "pageNo": page_no,
            "pageSize": page_size,
        }
        if order_id:
            data["orderId"] = order_id
        if start_time and end_time and time_type:
            data["startTime"] = start_time
            data["endTime"] = end_time
            data["timeType"] = time_type
        return self._request(
            access_token,
            "afterSale.listAfterSaleInfos",
            data,
            ListAfterSaleResp
        )

    def aftersale_detail(self,
                         access_token: str,
                         return_id: str):
        data: Dict[str, Any] = {
            "needNegotiateRecord": True,
            "returnsId": return_id,
        }
        return self._request(
            access_token,
            "afterSale.getAfterSaleInfo",
            data,
            AfterSaleDetailResp
        )

    def order_tracking(self, access_token: str, order_id: str):
        # 物流轨迹
        data: Dict[str, Any] = {
            "orderId": order_id
        }
        return self._request(
            access_token,
            "order.getOrderTracking",
            data,
            OrderTrackingResp
        )

    def order_detail(self, access_token: str, order_id: str):
        # 订单详情
        data: Dict[str, Any] = {
            "orderId": order_id
        }
        return self._request(
            access_token,
            "order.getOrderDetail",
            data,
            OrderDetailResp
        )

    def order_memo(self, access_token: str, order_id: str, note: str,
                   operator: str,
                   flag: int):
        data: Dict[str, Any] = {
            "orderId": order_id,
            "sellerMarkNote": note,
            "operator": operator,
            "sellerMarkPriority": flag
        }
        return self._request(
            access_token,
            "order.modifySellerMarkInfo",
            data,
            OrderMemoResp
        )
