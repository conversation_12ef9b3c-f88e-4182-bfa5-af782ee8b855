from typing import Tuple, NamedTuple

from loguru import logger

from robot_processor.client.logistics_clients.enum import LogisticsType, \
    InterceptionStatus
from robot_processor.client.logistics_clients.exception_manager import \
    AbstractLogisticsClientException
from robot_processor.client.logistics_clients.jdl_sdk import JdlSD<PERSON>
from robot_processor.client.logistics_clients.logistics_client import \
    AbstractLogisticsIntercept, AbstractLogisticsClient


class JdlLogisticsClientException(AbstractLogisticsClientException):
    def common_error_code_path(self):
        return "$.code"

    def common_error_msg_path(self):
        return "$.msg"


class CustomerKey(NamedTuple):
    access_token: str
    customer_code: str


class JdlLogisticsClient(AbstractLogisticsClient):

    def __init__(self, logistics_type: LogisticsType | None = None):
        super().__init__(logistics_type)
        self.keys: dict[str, CustomerKey] = {}

    def init(self, params: dict | None = None):
        if not params:
            return
        for param in params["keys"]:
            access_token = param.get("access_token", "")
            customer_code = param.get("customer_code", "")
            self.keys.update({
                access_token: Customer<PERSON><PERSON>(access_token, customer_code)
            })

    def create_interception(self, content=None) -> \
            Tuple[bool, str | None, dict | None]:
        error_messages: set[str] = set()
        latest_error_message: str = ""
        latest_response: dict = {}

        for key in self.keys.values():
            sdk = JdlSDK(key.access_token, key.customer_code)
            resp = sdk.intercept_report(content.get("waybill_no"))
            # resultType注释
            # 0 - 取消成功
            # 1 - 拦截成功
            # 4 - 拦截中
            if resp and resp.get("code") == 0 and resp.get("data") and resp.get(
                    "data").get("resultType") in [0, 1, 4]:
                return True, None, resp
            if not resp:
                latest_error_message = "系统报错"
                latest_response = {}
                error_messages.add("{}: {}".format(key.access_token, "系统报错"))
            else:
                latest_error_message = resp.get("msg")
                latest_response = resp
                error_messages.add("{}: {}".format(key.access_token, latest_error_message))
        if len(self.keys) > 1:
            logger.error("JDL 请求的完整错误原因如下: \n{}".format(
                "\n".join(list(error_messages))
            ))
        return False, latest_error_message, latest_response

    def cancel_interception(self, content=None):
        pass

    def query_trace(self, content=None):
        error_messages: set[str] = set()
        latest_response: dict = {}

        for key in self.keys.values():
            sdk = JdlSDK(key.access_token, key.customer_code)
            resp = sdk.query_traces(content.get("waybill_no"))
            if resp and resp.get("code") == 0:
                return resp
            if not resp:
                latest_error_message = "系统报错"
            else:
                latest_error_message = resp.get("msg")
            latest_response = resp
            error_messages.add("{}: {}".format(key.access_token, latest_error_message))
        if len(self.keys) > 1:
            logger.error("JDL 请求的完整错误原因如下: \n{}".format(
                "\n".join(list(error_messages))
            ))
        return latest_response


class JdlIntercept(AbstractLogisticsIntercept):

    def check_interception_status_by_trace(self, waybill_no, has_interception_record) -> \
            Tuple[InterceptionStatus, str | None]:
        is_ok, err_msg, resp = self.client.create_interception(
            {"waybill_no": waybill_no})
        # resultType = 1
        if is_ok and resp and resp["data"]["resultType"] == 1:
            return InterceptionStatus.SUCCESS, None
        # resultType = 0 or 4
        if is_ok:
            return InterceptionStatus.INTERCEPTION_WAIT, None
        # resultType = 2 or 3
        if resp:
            return InterceptionStatus.FAILED, None
        return InterceptionStatus.UNKNOWN, "系统错误"
