import json
import time
from hashlib import md5

from loguru import logger

from robot_processor.client.logistics_clients.enum import LogisticsType, InterceptionStatus
from robot_processor.client.logistics_clients.exception_manager import AbstractLogisticsClientException
from robot_processor.client.logistics_clients.logistics_client import (
    AbstractLogisticsClient,
    AbstractLogisticsIntercept,
)
from robot_processor.client.logistics_clients.schema import StandardTrace
from robot_processor.client_mixins import Session
from robot_processor.ext import cache
from rpa.conf import rpa_config as conf
from rpa.yundaex import YundaexClient


class YdLogisticsClient(AbstractLogisticsClient):
    session = Session()
    sdk: YundaexClient

    def __init__(self, logistics_type: LogisticsType | None = None) -> None:
        super().__init__(logistics_type)
        self.app_key = conf.YD_API_APP_KEY
        self.app_secret = conf.YD_API_APP_SECRET

    @property
    def endpoint(self):
        return conf.YD_API_ENDPOINT

    def readable_action(self, action: str):
        action_map = {
            "ACCEPT": "收件扫描",
            "GOT": "揽件扫描",
            "ARRIVAL": "入中转",
            "DEPARTURE": "出中转",
            "SENT": "派件中",
            "INBOUND": "第三方代收入库",
            "SIGNED": "已签收",
            "OUTBOUND": "第三方代收快递员取出",
            "SIGNFAIL": "签收失败",
            "RETURN": "退回件",
            "ISSUE": "问题件",
            "REJECTION": "拒收",
            "OTHER": "其他",
            "OVERSEA_IN": "入库扫描",
            "OVERSEA_OUT": "出库扫描",
            "CLEARANCE_START": "清关开始",
            "CLEARANCE_FINISH": "清关结束",
            "CLEARANCE_FAIL": "清关失败",
            "OVERSEA_ARRIVAL": "干线到达",
            "OVERSEA_DEPARTURE": "干线离开",
            "TRANSFER": "转单",
        }
        return action_map.get(action)

    def query_logistic(self, mail_no: str) -> tuple[bool, list[StandardTrace]]:
        traces: list[StandardTrace] = []
        # 韵达查询物流轨迹需要先订阅物流轨迹
        key = f"yd_domain_query_logistic:{mail_no}"
        if not cache.get(key):
            subscribe_resp = self._send("/outer/logictis/subscribe",
                                        {"orders": [{"orderid": mail_no,
                                                     "mailno": mail_no}]})
            if not subscribe_resp or subscribe_resp.get("code") != "0000":
                logger.warning(f"韵达订阅物流轨迹失败 {subscribe_resp}")
                return False, traces
            else:
                cache.set(key, 86400 * 60, 1)
                time.sleep(conf.YD_SUBSCRIBE_SLEEP_SECONDS)
        query_resp = self._send("/outer/logictis/query", {"mailno": mail_no})
        if query_resp.get("code") == "0000":
            if not (query_resp.get("data") or {}).get("steps"):
                return True, traces
            for step in query_resp["data"]["steps"]:
                traces.append(StandardTrace(
                    update_time=step["time"],
                    action=self.readable_action(step["action"]),
                    desc=step["description"]
                ))
            return True, traces
        return False, traces

    def _send(self, api_path: str, param: dict) -> dict:
        sign = md5((json.dumps(param) + "_" +
                    self.app_secret).encode()).hexdigest()

        headers = {
            "Content-Type": "application/json",
            "app-key": "002510",
            "sign": sign
        }
        try:
            resp = self.session.post(self.endpoint + api_path, json=param,
                                     headers=headers)
            resp.raise_for_status()
            resp_json = resp.json()
            logger.info(f"resp: {resp_json}")
            return resp_json
        except Exception as e:
            logger.exception(f"韵达接口请求发生异常 {e}")
            return {}

    def init(self, params=None):
        if not params:
            return
        self.sdk = YundaexClient(params["shop"], params["auth_account"])

    def create_interception(self, content=None):
        response = self.sdk.intercept_invokeExpressInte(content)
        if response.is_err():
            return False, str(response.unwrap_err()), None
        intercept_result = response.unwrap()
        if not intercept_result.data.can_intercept:
            return False, intercept_result.data.unblockable_reason, intercept_result.dict()
        return True, None, intercept_result.dict()


class YDIntercept(AbstractLogisticsIntercept):
    sdk: YundaexClient

    def init(self, params=None):
        super().init(params)
        if not params:
            return
        self.sdk = YundaexClient(params["shop"], params["auth_account"])

    def check_interception_status_by_trace(self, waybill_no, has_interception_record):
        from rpa.yundaex.schemas import NotifyExpressInterceptionRealStatus

        request = NotifyExpressInterceptionRealStatus(shipId=waybill_no)
        response = self.sdk.intercept_notifyExpressInterceptionRealStatus(request)
        if response.is_err():
            return InterceptionStatus.UNKNOWN, str(response.unwrap_err())
        intercept_result = response.unwrap()
        match intercept_result.data.realInterceptStatus:
            case 2:
                return InterceptionStatus.INTERCEPTION_WAIT, intercept_result.data.real_intercept_status
            case 3:
                return InterceptionStatus.FAILED, intercept_result.data.real_intercept_status
            case 4:
                return InterceptionStatus.SUCCESS, intercept_result.data.real_intercept_status
            case 5:
                return InterceptionStatus.FAILED, intercept_result.data.real_intercept_status
            case 6:
                return InterceptionStatus.SUCCESS, intercept_result.data.real_intercept_status
            case 7:
                return InterceptionStatus.UNKNOWN, intercept_result.data.real_intercept_status
            case _:
                return InterceptionStatus.UNKNOWN, intercept_result.data.realInterceptStatus


class YDClientExceptionManager(AbstractLogisticsClientException):
    def common_error_code_path(self):
        return "$.data.unblockableReason"

    def common_error_msg_path(self):
        return "$.data.unblockable_reason"
