from abc import ABC
from typing import Tuple

from loguru import logger

from robot_processor.client.logistics_clients.enum import LogisticsType, \
    InterceptionStatus


class AbstractLogisticsClient(ABC):
    """
    - 查询物流的轨迹
    - 下发物流的拦截
    - 取消物流的拦截
    """

    def __init__(self, logistics_type: LogisticsType | None = None):
        self.logistics_type = logistics_type

    def init(self, params=None):
        """
        一些初始化的动作
        """""

    def dispatch(self) -> "AbstractLogisticsClient":
        from robot_processor.client.logistics_clients.sto_domain import \
            StoClient
        from robot_processor.client.logistics_clients.ems_domain import \
            EMSClient
        from robot_processor.client.logistics_clients.jt_domain import \
            JTExpressClient
        from robot_processor.client.logistics_clients.zto_domain import \
            ZTOExpressClient
        from robot_processor.client.logistics_clients.yto_domain import \
            YtoLogisticsClient
        from robot_processor.client.logistics_clients.yd_domain import \
            YdLogisticsClient
        from robot_processor.client.logistics_clients.jdl_domain import \
            JdlLogisticsClient
        from robot_processor.client.logistics_clients.sf_domain import \
            SFClient


        if not self.logistics_type:
            return AbstractLogisticsClient(self.logistics_type)
        # 显式写出所有的子类，方便维护
        return {
            LogisticsType.STO: StoClient(self.logistics_type),
            LogisticsType.EMS: EMSClient(self.logistics_type),
            LogisticsType.JT: JTExpressClient(self.logistics_type),
            LogisticsType.ZTO: ZTOExpressClient(self.logistics_type),
            LogisticsType.YTO: YtoLogisticsClient(self.logistics_type),
            LogisticsType.YD: YdLogisticsClient(self.logistics_type),
            LogisticsType.JDL: JdlLogisticsClient(self.logistics_type),
            LogisticsType.SF: SFClient(self.logistics_type),
        }[self.logistics_type]

    def endpoint(self):
        raise NotImplementedError

    def sign(self, content=None):
        raise NotImplementedError

    def request(self, api_name=None, content=None):
        raise NotImplementedError

    def create_interception(self, content=None) -> Tuple[bool, str | None, dict | None]:
        """
        发起拦截请求
        :param content:
        :return: 是否成功，错误信息，完整的response
        """
        raise NotImplementedError

    def cancel_interception(self, content=None):
        raise NotImplementedError

    def query_trace(self, content=None):
        raise NotImplementedError


class AbstractLogisticsIntercept(ABC):
    def __init__(self, logistics_type: LogisticsType):
        from robot_processor.client.logistics_clients.exception_manager \
            import AbstractLogisticsClientException
        self.logistics_type = logistics_type
        self.client = AbstractLogisticsClient(logistics_type).dispatch()
        self.exception_manager = AbstractLogisticsClientException(logistics_type).dispatch()

    def init(self, params: dict | None = None):
        """
        进行一些初始化的动作
        """
        self.client.init(params)

    def dispatch(self):
        from robot_processor.client.logistics_clients.sto_domain import StoIntercept
        from robot_processor.client.logistics_clients.ems_domain import EMSIntercept
        from robot_processor.client.logistics_clients.jt_domain import JTIntercept
        from robot_processor.client.logistics_clients.zto_domain import ZTOIntercept
        from robot_processor.client.logistics_clients.yto_domain import YtoIntercept
        from robot_processor.client.logistics_clients.jdl_domain import JdlIntercept
        from robot_processor.client.logistics_clients.sf_domain import SFIntercept
        from robot_processor.client.logistics_clients.yd_domain import YDIntercept

        return {
            LogisticsType.STO: StoIntercept(self.logistics_type),
            LogisticsType.EMS: EMSIntercept(self.logistics_type),
            LogisticsType.JT: JTIntercept(self.logistics_type),
            LogisticsType.ZTO: ZTOIntercept(self.logistics_type),
            LogisticsType.YTO: YtoIntercept(self.logistics_type),
            LogisticsType.JDL: JdlIntercept(self.logistics_type),
            LogisticsType.SF: SFIntercept(self.logistics_type),
            LogisticsType.YD: YDIntercept(self.logistics_type)
        }[self.logistics_type]

    def create_interception(self, content=None) -> Tuple[bool, str | None, dict | None]:
        return self.client.create_interception(content)

    def cancel_intercept(self, content=None):
        return self.client.cancel_interception(content)

    def event_callback(self, body):
        raise NotImplementedError

    def event_callback_response(self):
        raise NotImplementedError

    def check_interception_status_by_callback(self, waybill_no) -> Tuple[bool, InterceptionStatus, str | None]:
        """
        根据回调信息查询拦截状态
        :param waybill_no:
        :return: 是否有回调，拦截的状态，错误信息
        """
        return False, InterceptionStatus.UNKNOWN, None

    def check_interception_status_by_trace(
            self, waybill_no,
            has_interception_record) -> Tuple[InterceptionStatus, str | None]:
        """
        根据物流轨迹查询拦截状态
        :param waybill_no:
        :param has_interception_record:
        :return:
        """
        raise NotImplementedError

    def interception_success_by_callback(self, payload):
        """
        根据回调信息，检查拦截成功的依据
        """
        if not payload:
            return False
        return False

    def check_interception_success_by_callback(self, waybill_no):
        from robot_processor.business_order.models import WebhookMessages

        all_webhook_msgs = WebhookMessages.query.filter_by(
            system_name=self.logistics_type.value, event_type="INTERCEPT",
            system_unique_id=waybill_no
        ).all()
        if not all_webhook_msgs:
            return False
        return any([self.interception_success_by_callback(item.payload) for item in all_webhook_msgs])

    def check_interception_status_final(self, interception_status, err_msg, waybill_no, check_type):
        if interception_status == InterceptionStatus.SUCCESS:
            return InterceptionStatus.SUCCESS, None, check_type
        elif interception_status == InterceptionStatus.FAILED:
            logger.info(f"物流公司：{self.logistics_type.value}, "
                        f"物流单号：{waybill_no}, 根据{check_type}检查拦截状态，拦截状态:{interception_status}, 错误信息：{err_msg}")
            return InterceptionStatus.FAILED, err_msg, check_type
        elif interception_status == InterceptionStatus.UNKNOWN:
            logger.info(f"物流公司：{self.logistics_type.value}, "
                        f"物流单号：{waybill_no}, 根据{check_type}检查拦截状态，拦截状态:{interception_status}, 错误信息：{err_msg}")
            return InterceptionStatus.UNKNOWN, "拦截状态不明", check_type

        return InterceptionStatus.INTERCEPTION_WAIT, None, check_type

    # 主动检查物流拦截的状态
    def check_interception_status_by_self(
            self, waybill_no,
            has_interception_record) -> Tuple[InterceptionStatus, str | None, str]:
        """
        1. 根据webhook的结果进行拦截状态的判断
        2. 根据物流轨迹的结果进行拦截状态的判断
        """
        # 根据webhook的结果进行拦截状态的判断
        has_callback, interception_status, err_msg = self.check_interception_status_by_callback(waybill_no)
        if has_callback:
            interception_status, msg, check_type = self.check_interception_status_final(
                interception_status, err_msg, waybill_no, "回调信息"
            )
            # 如果从回调中已经能够明确判断出“拦截成功”或者“拦截失败”，则返回。
            # 否则将藉由物流轨迹来进行一遍判断。
            if interception_status in [InterceptionStatus.SUCCESS, InterceptionStatus.FAILED]:
                return interception_status, msg, check_type

        # 根据物流轨迹的结果进行拦截状态的判断
        interception_status, err_msg = self.check_interception_status_by_trace(waybill_no, has_interception_record)
        return self.check_interception_status_final(interception_status, err_msg, waybill_no, "物流轨迹")
