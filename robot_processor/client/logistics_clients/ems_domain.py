import datetime
import json
from base64 import b64decode, b64encode
from enum import St<PERSON><PERSON><PERSON>
from typing import <PERSON>ple, NamedTuple

from gmssl import sm4
from loguru import logger

from robot_processor.client.logistics_clients.enum import InterceptionStatus, \
    LogisticsType
from robot_processor.client.logistics_clients.exception_manager import \
    AbstractLogisticsClientException
from robot_processor.client.logistics_clients.logistics_client import \
    AbstractLogisticsClient, AbstractLogisticsIntercept
from robot_processor.client.logistics_clients.schema import \
    EMSQueryLogisticOutput
from robot_processor.client_mixins import Session
from rpa.conf import rpa_config as conf


class EmsClientExceptionManager(AbstractLogisticsClientException):
    pass


CIPHERTEXT_PREFIX_SM4ECB = "|$4|"


class Identification(StrEnum):
    change_address = "1"    # 修改地址
    cancel_logistic = "2"   # 撤单


class CustomerKey(NamedTuple):
    authorization: str
    sm4_key: str
    sender_no: str
    crypt_sm4: sm4.CryptSM4


class EMSClient(AbstractLogisticsClient):
    session = Session()

    def __init__(self, logistics_type: LogisticsType | None = None):
        super().__init__(logistics_type)
        self.keys: dict[str, CustomerKey] = {}

    def init(self, content: dict[str, list[dict[str, str]]] | None = None) -> None:
        if not content:
            return
        params = content.get("keys") or []
        for param in params:
            # 授权码
            authorization = param.get("authorization", "")
            # sm4_key
            sm4_key = param.get("sm4_key", "")
            # 协议客户编码，目前在测试中看起来是不通用的。
            sender_no = param.get("sender_no", "")
            crypt_sm4 = sm4.CryptSM4(padding_mode=sm4.PKCS7)
            crypt_sm4.set_key(b64decode(sm4_key), sm4.SM4_ENCRYPT)
            self.keys.update({sender_no: CustomerKey(
                authorization=authorization,
                sm4_key=sm4_key,
                sender_no=sender_no,
                crypt_sm4=crypt_sm4
            )})

    @classmethod
    def generate_ciphertext_bytes(cls, sm4_key: str, crypt_sm4: sm4.CryptSM4,  body: dict) -> bytes:
        """
        使用国密 SM4 算法生成密文。
        """
        data = json.dumps(body, separators=(",", ":"), ensure_ascii=False)
        content = data + sm4_key
        return crypt_sm4.crypt_ecb(content.encode())

    @classmethod
    def generate_logitcs_interface(cls, sm4_key: str, crypt_sm4: sm4.CryptSM4, body: dict) -> str:
        """
        将密文进行 base64 转换，并追加前缀。
        """
        ciphertext_bytes = cls.generate_ciphertext_bytes(sm4_key, crypt_sm4, body)
        b64_string = b64encode(ciphertext_bytes).decode()
        return CIPHERTEXT_PREFIX_SM4ECB + b64_string

    @property
    def endpoint(self):
        return conf.EMS_API_ENDPOINT

    @classmethod
    def generate_sign(cls, body: dict, customer_key: CustomerKey) -> str:
        return cls.generate_logitcs_interface(customer_key.sm4_key, customer_key.crypt_sm4, body)

    def send(self, api_code: str, body: dict, customer_key: CustomerKey) -> dict:
        """
        发送请求。
        """
        logitcs_interface = self.generate_sign(body, customer_key)
        data = {
            "apiCode": api_code,
            "senderNo": customer_key.sender_no,
            "authorization": customer_key.authorization,
            "timeStamp": datetime.datetime.now().strftime(
                "%Y-%m-%d %H:%M:%S"
            ),
            "logitcsInterface": logitcs_interface
        }
        try:
            resp = self.session.post(
                self.endpoint,
                data=data,
                headers={
                    # 如果使用 request 默认的 User-Agent，会被 EMS 的防火墙拦下。
                    # 如果使用浏览器的 User-Agent，则会被 EMS 服务器返回 404。
                    "User-Agent": "",
                    "Content-Type": "application/x-www-form-urlencoded"
                }
            )
            resp.raise_for_status()
            logger.info("本次 EMS 原始请求体为 {}, 加密后请求体为 {}, 响应为：{}".format(
                body,
                data,
                resp.content.decode("utf-8")
            ))
            resp_content = resp.json()
            return resp_content
        except Exception as e:
            logger.exception(f"EMS 接口请求发生异常 {e}")
            return {}

    @staticmethod
    def handler_response(resp: dict) -> tuple[str, str | None]:
        if resp.get("retCode") != "00000":
            return "", resp.get("retMsg") or "EMS 服务异常"
        return resp.get("retBody") or "{}", None

    def create_interception(self, content=None) -> Tuple[bool, str | None, dict | None]:
        """
        撤单。
        :param content:
        :return:
        """
        body = {
            "identification": Identification.cancel_logistic,
            "waybillNo": content,
        }
        error_messages: set[str] = set()
        latest_error_message: str = ""
        for key in self.keys.values():
            resp = self.send("020007", body, key)
            result, err = self.handler_response(resp)
            if not err:
                return True, result, None
            else:
                error_messages.add("{}: {}".format(key.sender_no, err))
                latest_error_message = err
        if len(self.keys) > 1:
            logger.error("EMS 请求的完整错误原因如下: \n{}".format(
                "\n".join(list(error_messages))
            ))
        return False, latest_error_message, None

    def cancel_interception(self, content=None):
        pass

    def query_trace(self, content=None) -> tuple[bool, str]:
        """
        获取运单轨迹。
        :param content:
        :return:
        """
        body = {
            "waybillNo": content
        }
        error_messages = set()
        latest_error_message: str = ""
        for key in self.keys.values():
            resp = self.send("040001", body, key)
            result, err = self.handler_response(resp)
            if not err:
                return True, result
            else:
                error_messages.add("{}: {}".format(key.sender_no, err))
                latest_error_message = err
        if len(self.keys) > 1:
            logger.error("EMS 请求的完整错误原因如下: \n{}".format(
                "\n".join(list(error_messages))
            ))
        return False, latest_error_message

    def change_receiver_address(self, content: dict) -> tuple[bool, str]:
        """
        改地址。
        :param content: EMSReceiverAddress.dict()
        """
        error_messages = set()
        latest_error_message: str = ""
        for key in self.keys.values():
            resp = self.send("020007", content, key)
            result, err = self.handler_response(resp)
            if not err:
                return True, result
            else:
                error_messages.add("{}: {}".format(key.sender_no, err))
                latest_error_message = err
        if len(self.keys) > 1:
            logger.error("EMS 请求的完整错误原因如下: \n{}".format(
                "\n".join(list(error_messages))
            ))
        return False, latest_error_message


class EMSIntercept(AbstractLogisticsIntercept):

    def event_callback(self, body):
        pass

    def event_callback_response(self):
        pass

    def check_interception_status_by_trace(self,
                                           waybill_no,
                                           has_interception_record) -> Tuple[InterceptionStatus, str | None]:
        # 主动查询下物流轨迹，看下有没有拦截的记录
        success, response = self.client.query_trace(waybill_no)
        if not success:
            return InterceptionStatus.UNKNOWN, "拦截状态不明"
        logistic_trace = EMSQueryLogisticOutput(**json.loads(response))
        for item in logistic_trace.response_items:
            if (item.op_name == "撤单") and ("撤单成功" in (item.op_desc or "")):
                return InterceptionStatus.SUCCESS, ""
        return InterceptionStatus.INTERCEPTION_WAIT, ""
