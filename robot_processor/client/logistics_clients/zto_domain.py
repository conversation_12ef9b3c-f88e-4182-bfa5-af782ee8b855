import json
from base64 import b64encode
from hashlib import md5
from typing import Named<PERSON>up<PERSON>
from urllib.parse import urljoin
from uuid import uuid4

from loguru import logger
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.client_mixins import Session
from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.client.logistics_clients.logistics_client import \
    AbstractLogisticsClient, AbstractLogisticsIntercept
from robot_processor.client.logistics_clients.exception_manager import \
    AbstractLogisticsClientException
from robot_processor.client.conf import app_config
from robot_processor.client.logistics_clients.schema import ZTOCallbackRespSchema, ZTOWaybillIntercept
from robot_processor.ext import db
from robot_processor.client.logistics_clients.enum import InterceptionStatus
from robot_processor.client.logistics_clients.schema import ZTOQueryLogisticOutput
from robot_processor.enums import ZTOInterceptStatus


class ZTOClientExceptionManager(AbstractLogisticsClientException):
    def common_error_code_path(self) -> str:
        return "$.statusCode"

    def common_error_msg_path(self) -> str:
        return "$.message"


class CustomerKey(NamedTuple):
    app_key: str
    app_secret: str


class ZTOExpressClient(AbstractLogisticsClient):
    session = Session()

    def __init__(self, logistics_type: LogisticsType | None = None):
        super().__init__(logistics_type)
        self.keys: dict[str, CustomerKey] = {}
        self.org_id: str | None = None

    def init(self, content: dict | None = None):
        if not content:
            return
        params = content.get("keys") or []
        self.org_id = content.get("org_id")
        for param in params:
            app_key = param.get("app_key", "")
            app_secret = param.get("app_secret", "")
            self.keys.update({
                app_key: CustomerKey(app_key=app_key, app_secret=app_secret)
            })

    @property
    def endpoint(self):
        return app_config.ZTO_DOMAIN

    @staticmethod
    def generate_digest(body: dict, secret: str):
        # 生成请求体和数字签名。
        raw_body = json.dumps(body, separators=(",", ":"), ensure_ascii=False)
        content = raw_body + secret
        result = b64encode(
            md5(content.encode("utf-8")).digest()
        ).decode()
        return result

    def sign(self, content=None):
        return self.generate_digest(
            content.get("body"),
            content.get("app_secret")
        )

    @staticmethod
    def generate_raw_body_and_digest(
        body: dict, secret: str
    ) -> tuple[str, str]:
        # 生成请求体和数字签名。
        raw_body = json.dumps(body, separators=(",", ":"), ensure_ascii=False)
        content = raw_body + secret
        result = b64encode(
            md5(content.encode("utf-8")).digest()
        ).decode()
        return raw_body, result

    def send(self, api_code: str, body: dict, customer_key: CustomerKey) -> dict:
        # 根据 app_secret 和需要发送的信息进行组合，得到原生请求体和数字签名。
        digest = self.sign({
            "body": body,
            "app_secret": customer_key.app_secret,
        })
        data = json.dumps(body, separators=(",", ":"), ensure_ascii=False)
        # 组合请求头。
        headers = {
            "Content-Type": "application/json",
            "x-appKey": customer_key.app_key,
            "x-dataDigest": digest,
        }
        url = urljoin(self.endpoint, api_code)

        try:
            # 发送请求。
            resp = self.session.post(
                url=url,
                data=data.encode("utf-8"),
                headers=headers
            )
            resp.raise_for_status()
            logger.info("本次 ZTO 原始请求体为 {}, 加密后请求体为 {}, 响应为：{}".format(
                body,
                data,
                resp.content.decode("utf-8")
            ))
            resp_content = resp.json()
            return resp_content
        except Exception as e:
            logger.exception(f"ZTO 接口请求发生异常 {e}")
            return {}

    @staticmethod
    def handle_response(resp: dict) -> tuple[dict, str | None]:
        if not resp.get("status"):
            return resp, resp.get("message") or "ZTO 服务异常"
        return resp, None

    def query_trace(self, content=None):
        """
        获取运单轨迹。
        :param content:
        :return:
        """
        body = {
            "billCode": content.get("bill_code")
        }
        if mobile_phone := content.get("mobile_phone"):
            body.update({"mobilePhone": mobile_phone})

        error_messages: set[str] = set()
        result: dict = {}
        for key in self.keys.values():
            resp = self.send("zto.merchant.waybill.track.query", body, key)
            result, err = self.handle_response(resp)
            if not err:
                return result
            else:
                error_messages.add("{}: {}".format(key.app_key, err))
        if len(self.keys) > 1:
            logger.error("ZTO 请求的完整错误原因如下: \n{}".format(
                "\n".join(list(error_messages))
            ))
        return result

    def create_interception(self, content=None) -> tuple[bool, str | None, dict | None]:
        """
        下发拦截请求。
        :param content:
        :return:
        """
        error_messages: set[str] = set()
        latest_error_message: str = ""
        latest_response: dict = {}
        for key in self.keys.values():
            request_id = str(uuid4())
            body = {
                "billCode": content.get("waybill_no"),
                "requestId": request_id,
                "thirdBizNo": request_id,
                "destinationType": content.get("destination_type") or 2
            }
            resp = self.send("thirdcenter.createIntercept", body, key)
            result, err = self.handle_response(resp)
            if not err:
                return True, "", result
            else:
                error_messages.add("{}: {}".format(key.app_key, err))
                latest_error_message = err
                latest_response = resp
        if len(self.keys) > 1:
            logger.error("ZTO 请求的完整错误原因如下: \n{}".format(
                "\n".join(list(error_messages))
            ))
        return False, latest_error_message, latest_response

    def cancel_interception(self, content=None):
        """
        取消拦截。
        :param content:
        :return:
        """
        body = {
            "billCode": content.get("waybill_no"),
            "thirdBizNo": content.get("third_biz_no")
        }
        error_messages: set[str] = set()
        latest_response: dict = {}
        latest_error_message: str = ""
        for key in self.keys.values():
            resp = self.send("thirdcenter.cancelIntercept", body, key)
            result, err = self.handle_response(resp)
            if not err:
                return True, "", result
            else:
                error_messages.add("{}: {}".format(key.app_key, err))
                latest_error_message = err
                latest_response = resp
        if len(self.keys) > 1:
            logger.error("ZTO 请求的完整错误原因如下: \n{}".format(
                "\n".join(list(error_messages))
            ))
        return False, latest_error_message, latest_response

    def query_interception_status(self, content=None):
        """
        查询最新拦截件状态。
        :param content:
        :return:
        """
        body = {
            "billCode": content.get("waybill_no"),
        }
        error_messages: set[str] = set()
        latest_response: dict = {}
        latest_error_message: str = ""
        for key in self.keys.values():
            resp = self.send("thirdcenter.queryInterceptAndReturnStatus", body, key)
            result, err = self.handle_response(resp)
            if not err:
                return True, "", result
            else:
                error_messages.add("{}: {}".format(key.app_key, err))
                latest_error_message = err
                latest_response = resp
        if len(self.keys) > 1:
            logger.error("ZTO 请求的完整错误原因如下: \n{}".format(
                "\n".join(list(error_messages))
            ))
        return False, latest_error_message, latest_response


class ZTOIntercept(AbstractLogisticsIntercept):
    def event_callback(self, body):
        from robot_processor.business_order.models import WebhookMessages

        data = body.get("data")
        if data is None:
            return

        # 格式化业务参数。
        result = self.parse_zto_callback_payload(json.loads(data))
        if result is None:
            return
        webhook_message = WebhookMessages(
            system_name=self.logistics_type.value,
            event_type="INTERCEPT",
            system_unique_id=result.waybillCode,
            payload=result.dict(exclude_none=True)
        )
        db.session.add(webhook_message)
        flag_modified(webhook_message, "payload")
        db.session.commit()

    def event_callback_response(self):
        return ZTOCallbackRespSchema(status=True, statusCode="200", message="成功")

    def interception_success_by_callback(self, payload):
        result = self.parse_zto_callback_payload(payload)
        if result is None:
            return False
        org_maps: dict = json.loads(app_config.ZTO_ALLOW_PACKAGE_DELIVERYING_ORG_MAPS)
        org_id = getattr(self.client, "org_id") if hasattr(self.client, "org_id") else None
        if org_id is not None and org_maps.get(org_id):
            is_success = result.serviceOrderStatus in ("INTERCEPT_SUCCESS", "PACKAGE_DELIVERYING")
        else:
            is_success = result.serviceOrderStatus in ("INTERCEPT_SUCCESS",)
        if is_success:
            logger.info("中通通过回调确认拦截成功时的状态为: {}".format(result.serviceOrderStatusMsg))
        return is_success

    @staticmethod
    def parse_zto_callback_payload(payload: dict) -> ZTOWaybillIntercept | None:
        if not payload:
            return None
        try:
            return ZTOWaybillIntercept(**payload)
        except Exception as e:
            logger.exception(f"中通 callback 解析失败{e}")
            return None

    def check_interception_status_by_callback(self, waybill_no) -> tuple[bool, InterceptionStatus, str | None]:
        """
        根据 callback 检测拦截状态。
        :param waybill_no:
        :return:
        """
        from robot_processor.business_order.models import WebhookMessages

        all_webhook_msgs = WebhookMessages.query.filter_by(
            system_name=self.logistics_type.value, event_type="INTERCEPT",
            system_unique_id=waybill_no
        ).order_by(WebhookMessages.id.desc()).all()
        if len(all_webhook_msgs) <= 0:
            return False, InterceptionStatus.UNKNOWN, "拦截状态不明"

        all_payloads = [item.payload for item in all_webhook_msgs]
        # 将目前所有的回调中的 payload 按照操作时间排序。(因为中通的回调里，先操作的也可能会先发送回调过来)
        sorted_all_payloads = sorted(all_payloads, key=lambda p: p.get("operationTime") or 0, reverse=True)
        for payload in sorted_all_payloads:
            if self.interception_success_by_callback(payload):
                return True, InterceptionStatus.SUCCESS, None
            result = self.parse_zto_callback_payload(payload)
            if result is None:
                return False, InterceptionStatus.UNKNOWN, "拦截状态不明"
            if result.serviceOrderStatus in [
                ZTOInterceptStatus.INTERCEPTING,
                ZTOInterceptStatus.PACKAGE_DELIVERYING
            ]:
                return True, InterceptionStatus.INTERCEPTION_WAIT, "拦截退改中"
            if result.serviceOrderStatus in [
                ZTOInterceptStatus.INTERCEPT_FAIL,
                ZTOInterceptStatus.INTERCEPT_CANCEL
            ]:
                return True, InterceptionStatus.FAILED, f"拦截失败，当前拦截状态: {result.serviceOrderStatusMsg}"
        return True, InterceptionStatus.UNKNOWN, "拦截状态不明"

    def check_interception_status_by_trace(
            self, waybill_no,
            has_interception_record) -> tuple[InterceptionStatus, str | None]:
        """
        主动查询下物流轨迹，看下有没有拦截的记录
        :param waybill_no:
        :param has_interception_record:
        :return:
        """
        response = self.client.query_trace({"bill_code": waybill_no})
        query_logistic_trace = ZTOQueryLogisticOutput(**response)
        if not query_logistic_trace.status:
            return InterceptionStatus.UNKNOWN, "拦截状态不明"

        waybill_traces = [detail for detail in query_logistic_trace.result if detail.bill_code == waybill_no]
        # 无物流轨迹和揽收状态下，可能物流轨迹中不会再有退件
        if has_interception_record and len(waybill_traces) <= 1:
            return InterceptionStatus.SUCCESS, ""
        if len(waybill_traces) == 0:
            return InterceptionStatus.UNKNOWN, "拦截状态不明"
        query_logistic_trace.result = waybill_traces
        sorted_trace_records = sorted(
            waybill_traces,
            key=lambda record: (record.scan_date is not None, record.scan_date),
            reverse=True
        )
        for trace in sorted_trace_records:
            # 退改类型的文档：https://open.zto.com/#/documents?menuId=103
            if isinstance(trace.return_update_type, str) and trace.return_update_type.startswith("R"):
                return InterceptionStatus.SUCCESS, ""
        return InterceptionStatus.INTERCEPTION_WAIT, ""
