import re
import json
from abc import ABC

import objectpath
from lepollo import ApolloConfig, get_config

from robot_processor.client.logistics_clients.enum import LogisticsType, InterceptionExceptionType


class AbstractLogisticsClientException(ABC):
    def __init__(self, logistics_type: LogisticsType):
        self.logistics_type = logistics_type
        self.is_ok: bool | None = None
        self.err_msg: str | None = None
        self.error_type: InterceptionExceptionType = InterceptionExceptionType.MATCH_ERROR_UNKNOWN
        self.suggestion: str | None = None

    def dispatch(self) -> "AbstractLogisticsClientException":
        # 显式写出所有的子类，方便维护
        from robot_processor.client.logistics_clients.sto_domain import \
            StoClientExceptionManager
        from robot_processor.client.logistics_clients.ems_domain import \
            EmsClientExceptionManager
        from robot_processor.client.logistics_clients.jt_domain import \
            JTClientExceptionManager
        from robot_processor.client.logistics_clients.zto_domain import \
            ZTOClientExceptionManager
        from robot_processor.client.logistics_clients.yto_domain import \
            YtoLogisticsClientException
        from robot_processor.client.logistics_clients.jdl_domain import \
            JdlLogisticsClientException
        from robot_processor.client.logistics_clients.sf_domain import \
            SFClientExceptionManager
        from robot_processor.client.logistics_clients.yd_domain import YDClientExceptionManager


        return {
            LogisticsType.STO: StoClientExceptionManager(self.logistics_type),
            LogisticsType.EMS: EmsClientExceptionManager(self.logistics_type),
            LogisticsType.JT: JTClientExceptionManager(self.logistics_type),
            LogisticsType.ZTO: ZTOClientExceptionManager(self.logistics_type),
            LogisticsType.YTO: YtoLogisticsClientException(
                self.logistics_type),
            LogisticsType.JDL: JdlLogisticsClientException(
                self.logistics_type),
            LogisticsType.SF: SFClientExceptionManager(self.logistics_type),
            LogisticsType.YD: YDClientExceptionManager(self.logistics_type)
        }[self.logistics_type]

    def common_error_code_path(self) -> str:
        return ""

    def common_error_msg_path(self) -> str:
        return ""

    def match_error(self, resp, error_msg=None):
        rules: list[dict] = config.get_logistics_errors_match_rule(self.logistics_type)
        match_error_item = None

        if resp:
            error_code = objectpath.Tree(resp).execute(self.common_error_code_path())
            error_msg = objectpath.Tree(resp).execute(self.common_error_msg_path())
            for error_item in rules:
                error_codes = error_item.get("error_code", [])
                regex_patterns = error_item.get("regex", [])

                # 检查 error_code 是否匹配
                if error_code in error_codes:
                    match_error_item = error_item
                    break

                # 检查正则表达式是否匹配
                for regex_pattern in regex_patterns:
                    if error_msg and re.match(regex_pattern, error_msg):
                        match_error_item = error_item
                        break

                if match_error_item:
                    break  # 如果找到匹配的错误项，则跳出循环
        else:
            for error_item in rules:
                regex_patterns = error_item.get("regex", [])

                # 检查正则表达式是否匹配
                for regex_pattern in regex_patterns:
                    if error_msg and re.match(regex_pattern, error_msg):
                        match_error_item = error_item
                        break

                if match_error_item:
                    break  # 如果找到匹配的错误项，则跳出循环

        if match_error_item and "error_type" in match_error_item:
            self.is_ok = False
            self.err_msg = error_msg
            self.error_type = InterceptionExceptionType[match_error_item["error_type"]]
            self.suggestion = match_error_item.get("suggestion")
        else:
            self.is_ok = False
            self.err_msg = error_msg
            self.error_type = InterceptionExceptionType.MATCH_ERROR_UNKNOWN
            self.suggestion = "拦截状态失败，请咨询技术详细报错"
        return self


class Config(ApolloConfig):
    __namespace__ = "application"

    def get_logistics_errors_match_rule(self, logistics_type: LogisticsType):
        key = f"logistics.errors_match_rule.{logistics_type.value}"

        def converter(raw_config_value: str | None):
            if raw_config_value is None:
                return []
            return json.loads(raw_config_value)

        return self.get(key, converter, default_value=[])


config = get_config(config_class=Config)
