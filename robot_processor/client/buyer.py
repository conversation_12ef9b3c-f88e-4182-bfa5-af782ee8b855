from typing import cast, Optional

from leyan_grpc.client.venice import GaiaStub
from leyan_proto.digismart.buyer_server.buyer_server_rpc_pb2 import (
    GetReceiverInfoRequest,
    UpdateReceiverInfoRequest,
    ReceiverInfo,
    GetUserNickByTidRequest,
    GetUserNickByBuyerOpenUidRequest, GetLeyanBuyerIdByBuyerNickRequest, GetLeyanBuyerIdByBuyerNickResponse
)
from leyan_proto.digismart.buyer_server.buyer_server_rpc_pb2_grpc import DgtBuyerServerRpcServiceStub
from leyan_proto.digismart.dgt_common_pb2 import ChannelType, DgtResponseCode
from loguru import logger

from robot_metrics import Stats

_statsd_it = Stats.Client.timer(client_name="buyer-server")


class BuyerClient:
    def __init__(self):
        self._stub = cast(
            DgtBuyerServerRpcServiceStub,
            GaiaStub.auto_stub(DgtBuyerServerRpcServiceStub)
        )
        self._logger = logger.bind(rpcClient="DgtBuyerServer")

    @property
    def client(self):
        return self._stub

    @_statsd_it
    def get_receiver_info(self, sid, nick, platform, tid):
        req = GetReceiverInfoRequest()
        req.sid = sid
        req.seller_nick = nick
        req.tid = tid
        req.channel_type = getattr(ChannelType, platform)
        try:
            return self.client.GetReceiverInfo(req)
        except Exception as error:
            logger.exception(f"failed to get receiver infos. {error=}")
            return

    @_statsd_it
    def update_receiver_info(self, sid, nick, platform, tid, receiver):
        district = ""
        if receiver.get("district") is not None:
            district = receiver.get("district")
        elif receiver.get("zone") is not None:
            district = receiver.get("zone")
        receiver_info = ReceiverInfo(
            receiver_name=receiver.get("name"),
            receiver_mobile=receiver.get("mobile"),
            receiver_phone=receiver.get("phone"),
            receiver_country=receiver.get("country"),
            receiver_state=receiver.get("state"),
            receiver_city=receiver.get("city"),
            receiver_district=f"{district}",
            receiver_town=receiver.get('town'),
            receiver_address=receiver.get("address"),
            receiver_zip=receiver.get("zip"))
        req = UpdateReceiverInfoRequest(
            sid=sid, seller_nick=nick, tid=tid,
            channel_type=getattr(ChannelType, platform),
            receiver_info=receiver_info)
        try:
            resp = self.client.UpdateReceiverInfo(req)
            if resp.code == DgtResponseCode.OK:
                return True, ""
            else:
                return False, resp.error_msg
        except BaseException as e:
            logger.error("UpdateReceiverInfo error: "
                         f"exception: {repr(e)}", exc_info=True
                         )
            return False, str(e)

    @_statsd_it
    def get_buyer_nick_by_tid(self, tid: str, seller_id: str):
        rpc_req = GetUserNickByTidRequest(tid=tid, seller_id=seller_id)
        rpc_res = self.client.GetUserNickByTid(rpc_req)
        if rpc_res.code != DgtResponseCode.OK:
            self._logger.warning(
                "method=GetUserNickByTid, "
                f"tid={tid} seller_id={seller_id}"
                f"code={rpc_res.code}, error={rpc_res.error_msg}"
            )
            return None
        return rpc_res.buyer_nick

    @_statsd_it
    def get_buyer_nick_by_open_uid(self, buyer_open_uid: str, app_id: str):
        rpc_req = GetUserNickByBuyerOpenUidRequest(
            buyer_open_uid=buyer_open_uid, app_id=app_id
        )
        rpc_res = self.client.GetUserNickByBuyerOpenUid(rpc_req)
        if rpc_res.code != DgtResponseCode.OK:
            self._logger.warning(
                "method=GetUserNickByBuyerOpenUid, "
                f"openId={buyer_open_uid}"
                f"code={rpc_res.code}, error={rpc_res.error_msg}"
            )
            return None
        return rpc_res

    @_statsd_it
    def get_buyer_info_by_buyer_nick(self, buyer_nick: str) -> Optional["GetLeyanBuyerIdByBuyerNickResponse"]:
        rpc_req = GetLeyanBuyerIdByBuyerNickRequest(
            buyer_nick=buyer_nick
        )
        rpc_res = self.client.GetBuyerInfoByBuyerNick(rpc_req)
        if rpc_res.code != DgtResponseCode.OK:
            self._logger.warning(
                "method=get_buyer_info_by_buyer_nick, "
                f"openId={buyer_nick} "
                f"code={rpc_res.code}, error={rpc_res.error_msg}"
            )
            return None
        return rpc_res
