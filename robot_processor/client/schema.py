import typing as t

from pydantic import BaseModel

from robot_processor.utils import make_fields_optional


class PopClientResponse(BaseModel):
    success: bool
    errorCode: t.Optional[int]
    traceId: str
    errorMessage: t.Optional[str]


class PopClientRequest(BaseModel):
    method: str  # 拼多多接口类型
    sellerId: str  # 拼多多店铺ID
    callbackUrl: str  # 回调URL
    payload: t.Dict[str, t.Any]  # 拼多多接口业务参数


class PddDecryptPayload(BaseModel):
    data_tag: str  # 解密tag，对于订单数据是订单号
    encrypted_data: str


class PddDecryptResponse(BaseModel):
    data_tag: str
    data_type: t.Optional[int] = None
    decrypted_data: str = ""
    encrypted_data: str
    error_code: int
    error_msg: t.Optional[str] = None
    virtual_identify_number: t.Optional[str] = None
    virtual_number_type: t.Optional[int] = None


class PddBatchDecryptPayload(BaseModel):
    data_list: t.List[PddDecryptPayload]


class PddBatchDecryptResponse(BaseModel):
    data_decrypt_list: t.List[PddDecryptResponse]


class PddRefundListPayload(BaseModel):
    after_sales_status: int
    after_sales_type: int
    start_updated_at: int  # 最后更新时间开始时间时间戳
    end_updated_at: int  # 最后更新时间结束时间时间戳
    page: int = 1
    page_size: int = 100


class PddRefundListResponse(BaseModel):
    class RefundIncrementGetResponse(BaseModel):
        @make_fields_optional
        class Refund(BaseModel):
            after_sale_reason: str  # 售后原因
            after_sales_status: int  # 售后状态
            after_sales_type: int  # 售后类型
            confirm_time: str  # 成团时间
            created_time: str  # 创建时间
            discount_amount: str  # 订单折扣金额（元）
            dispute_refund_status: int  # Enum[1:纠纷退款 0:非纠纷退款]
            good_image: str  # 商品图片
            goods_id: int  # 商品编码
            goods_name: str  # 商品名称
            goods_number: str  # 商品数量
            goods_price: str  # 商品单价
            id: int  # 售后编号
            order_amount: str  # 订单金额（元）
            order_sn: str  # 订单编号
            outer_goods_id: str  # 商家外部编码（商品）
            outer_id: str  # 商家外部编码（sku）
            refund_amount: str  # 退款金额（元）
            refund_operator_role: int  # Enum[0:"默认",1:"用户",2:"商家",3:"平台",4:"系统",5:"团长",6:"司机",7:"代理人"]
            shipping_name: str  # 退货物流公司名称
            sku_id: str  # 商品规格ID
            speed_refund_flag: int  # 极速退款标志位 1：极速退款，0：非极速退款
            speed_refund_status: (str)  # 极速退款状态，"1"：有极速退款资格，"2"：极速退款失败, "3" 表示极速退款成功，其他表示非极速退款
            tracking_number: str  # 快递运单号
            updated_time: str  # 更新时间
            user_shipping_status: str  # 0-未勾选 1-消费者选择的收货状态为未收到货 2-消费者选择的收货状态为已收到货

        refund_list: list[Refund]
        total_count: int

    refund_increment_get_response: RefundIncrementGetResponse


class PddRefundDetailPayload(BaseModel):
    order_sn: str
    after_sale_id: t.Optional[str] = None


class PddRefundAgreePayload(BaseModel):
    order_sn: str
    after_sale_id: str


class PddRefundAgreeResponse(BaseModel):
    class Response(BaseModel):
        class Result(BaseModel):
            after_sales_id: str
            message: str
            order_sn: str
            succ: bool

        result: Result

    response: Response


class PddRefundDetailResponse(BaseModel):
    after_sales_reason: str
    after_sales_status: int
    after_sales_type: int
    expire_time: t.Optional[int] = None
    confirm_time: t.Optional[int] = None  # 成团时间
    discount_amount: t.Optional[int] = None  # 订单折扣金额
    express_no: t.Optional[str] = None  # 退货物流单号
    goods_number: int  # 商品数量
    goods_price: int  # 商品单价
    order_amount: int  # 交易金额
    refund_amount: int  # 退款金额
    order_sn: str  # 订单号
    recreated_at: int  # 售后单创建时间（重新申请时间）
    refund_operator_role: t.Optional[int] = None  # 同意退款操作人
    id: int  # 售后单ID
    images: list[str]  # 用户申请售后上传的图片列表
    join_or_not: str  # 是否介入 1介入 0未介入
    remark: t.Optional[str] = None  # 用户申请输入的描述信息
    shipping_name: t.Optional[str] = None  # 退货物流名称
    shipping_status: int  # 发货状态
    sku_id: str  # 商品规格ID
    updated_time: str  # 更新时间
    speed_refund_flag: int  # 极速退款标志位 1：极速退款，0：非极速退款
    part_after_sales_type: t.Optional[int] = None  # 部分售后类型：0：无意义、1：件数/件、2：比例/%
    part_after_sales_value: t.Optional[int] = None  # 部分售后件数/比例；注意只有退货退款类型才有意义


class PddOrderUploadExtraLogisticsPayload(BaseModel):
    class Package(BaseModel):
        shipping_id: int
        tracking_number: str

    packages: t.List[Package]
    order_sn: str
    extra_track_type: t.Optional[int] = None


class PddOrderUploadExtraLogisticsResponse(BaseModel):
    class Response(BaseModel):
        success: bool

    upload_extra_logistics_response: Response


class PddLogisticsCompaniesGetResponse(BaseModel):
    class Response(BaseModel):
        class LogisticsCompany(BaseModel):
            available: int
            id: int
            logistics_company: str
            code: str

        logistics_companies: t.List[LogisticsCompany]

    logistics_companies_get_response: Response


class PddRefundStatusCheckPayload(BaseModel):
    order_sns: str


class PddOrderUpdateAddressPayload(BaseModel):
    order_sn: str
    city: str
    city_id: int
    province: str
    province_id: int
    town: str
    town_id: int
    address: str
    receiver_name: str
    receiver_phone: str


class PddRefundStatusCheckResponse(BaseModel):
    class Response(BaseModel):
        class OrderSnsExistsRefundListItem(BaseModel):
            order_sn: str

        order_sns_exists_refund: list[OrderSnsExistsRefundListItem]

    refund_status_check_response: Response


@make_fields_optional
class PddOrderUpdateAddressResponse(BaseModel):
    class Response(BaseModel):
        class Result(BaseModel):
            msg: str
            order_sn: str

        success: bool
        errorCode: int
        errorMsg: str
        result: Result

    response: Response


class PddOrderInformationGet(BaseModel):
    order_sn: str

    class Response(BaseModel):
        class OrderInfoGetResponse(BaseModel):
            @make_fields_optional
            class OrderInfo(BaseModel):
                @make_fields_optional
                class CardInfo(BaseModel):
                    card_no: str
                    mask_password: str

                @make_fields_optional
                class ConsolidateInfo(BaseModel):
                    consolidate_type: int

                @make_fields_optional
                class ExtraDeliveryListItem(BaseModel):
                    logistics_id: int
                    tracking_number: str

                @make_fields_optional
                class GiftDeliveryListItem(BaseModel):
                    logistics_id: int
                    tracking_number: str

                @make_fields_optional
                class GiftListItem(BaseModel):
                    goods_count: int  # 赠品数量
                    goods_id: int  # 赠品id
                    goods_img: str  # 赠品图片
                    goods_name: str  # 赠品名称
                    goods_price: float  # 赠品销售价格
                    goods_spec: str  # 赠品规格
                    outer_goods_id: str  # 商家外部商品编码
                    outer_id: str  # 商家外部sku编码
                    sku_id: int  # 赠品规格编码

                @make_fields_optional
                class ItemListItem(BaseModel):
                    goods_count: int  # 商品数量
                    goods_id: int  # 商品编号
                    goods_img: str  # 商品图片
                    goods_name: str  # 商品名称
                    goods_price: float  # 商品销售价格
                    goods_spec: str  # 商品规格，使用（规格值1,规格值2）组合作为sku的表示，中间以英文逗号隔开
                    outer_goods_id: str  # 商家外部编码（商品），注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息。
                    outer_id: str  # 商家外部编码（sku），注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息。
                    sku_id: int  # 商品规格编码

                @make_fields_optional
                class OrderDepotInfo(BaseModel):
                    @make_fields_optional
                    class WareSubInfoListItem(BaseModel):
                        ware_id: int  # 子货品id
                        ware_name: str  # 子货品1编码
                        ware_quantity: int  # 子货品数量
                        ware_sn: str  # 子货品编码

                    depot_code: str  # 仓库编码
                    depot_id: str  # 仓库id
                    depot_name: str  # 仓库名称
                    depot_type: int  # 仓库类型，1：自有仓 2：订阅仓 两者都不是则传空
                    ware_id: str  # 货品id
                    ware_name: str  # 货品名称
                    ware_sn: str  # 货品编码
                    ware_sub_info_list: list[WareSubInfoListItem]  # 子货品列表（组合货品才会有子货品信息）
                    ware_type: int  # 货品类型（0：普通货品，1：组合货品）

                @make_fields_optional
                class OrderTagListItem(BaseModel):
                    name: str  # 标签名称
                    value: int  # 是否有标签：0=无标签，1=有标签

                @make_fields_optional
                class PromotionDetailListItem(BaseModel):
                    discount_amount: float  # 优惠金额（元）
                    promotion_type: int  # 优惠券类型。30-以旧换新优惠（优惠金额已包含平台优惠金额里）

                @make_fields_optional
                class ResendDeliveryListItem(BaseModel):
                    logistics_id: int
                    tracking_number: str

                @make_fields_optional
                class ServiceFeeDetailItem(BaseModel):
                    service_fee: float  # 服务费金额，单位：元
                    service_name: str  # 服务费类型

                @make_fields_optional
                class StepOrderInfo(BaseModel):
                    advanced_paid_fee: float  # 已付定金 单位：元
                    step_discount_amount: float  # 膨胀金额 （包含券减） 单位：元
                    step_paid_fee: float  # 分阶段已付金额（定金+尾款） 单位：元
                    step_trade_status: int  # 定金订单状态：step_trade_status 枚举：0-定金未付尾款未付、1-定金已付尾款未付、2-定金已付尾款已付

                @make_fields_optional
                class StoreInfo(BaseModel):
                    store_id: int  # 门店id
                    store_name: str  # 门店名称
                    store_number: str  # 门店自定义编码

                address: str  # 收件详细地址(加密)
                address_mask: str  # 详细地址（打码）
                after_sales_status: int  # 售后状态
                bonded_warehouse: str  # 保税仓名称
                buyer_memo: str  # 买家备注
                capital_free_discount: float
                card_info_list: list[CardInfo]
                cat_id_1: int  # 商品一级分类
                cat_id_2: int  # 商品二级分类
                cat_id_3: int  # 商品三级分类
                cat_id_4: int  # 商品四级分类
                city: str  # 收件城市
                city_id: int  # 收件城市ID
                confirm_status: int  # 成交状态：0：未成交、1：已成交、2：已取消
                confirm_time: str  # 成交时间
                consolidate_info: ConsolidateInfo  # 集运信息
                country: str  # 收件地国家或地区
                country_id: int  # 国家或地区编码
                created_time: str  # 创建时间
                delivery_home_value: float  # 送货入户费用 单位：元
                delivery_install_value: float  # 送货入户并安装 单位：元
                delivery_one_day: int  # 是否当日发货，1-是，0-否
                discount_amount: float  # 折扣金额（元）折扣金额=平台优惠+商家优惠+团长免单优惠金额
                duo_duo_pay_reduction: float  # 多多支付立减金额，单位：元
                duoduo_wholesale: int  # 是否多多批发，1-是，0-否
                extra_delivery_list: list[ExtraDeliveryListItem]  # 订单多包裹发货时使用的其他发货快递信息
                free_sf: int  # 是否顺丰包邮 1表示是 0表示否
                gift_delivery_list: list[GiftDeliveryListItem]  # 赠品额外运单列表
                gift_list: list[GiftListItem]  # 赠品列表
                goods_amount: float  # 商品金额（元）商品金额=商品销售价格*商品数量-订单改价折扣金额
                group_order_id: int  # 拼团ID
                group_role: int  # 团身份。0-团员，1-团长
                group_status: int  # 成团状态：0：拼团中、1：已成团、2：团失败
                home_delivery_type: int  # 送货入户并安装服务 0-不支持送货，1-送货入户不安装，2-送货入户并安装
                home_install_value: float  # 上门安装费用 单位：元
                inner_transaction_id: str  # 支付申报订单号（多多国际清关请使用此字段，单号以XP开头）
                invoice_status: int  # 发票申请,1代表有 0代表无
                is_lucky_flag: int  # 是否抽奖订单，1-非抽奖订单，2-抽奖订单
                is_pre_sale: int  # 是否为预售商品 1表示是 0表示否
                is_stock_out: int  # 是否缺货 0-无缺货处理 1： 有缺货处理
                item_list: list[ItemListItem]  # 订单中商品sku列表
                last_ship_time: str  # 订单承诺发货时间
                logistics_id: int  # 快递公司编号
                mkt_biz_type: int  # 市场业务类型，0-普通订单，1-拼内购订单
                only_support_replace: int  # 只换不修，1:是，0:否
                open_address_id: str  # 合单ID
                order_change_amount: float  # 订单改价折扣金额，单位元
                order_depot_info: OrderDepotInfo  # 仓库信息
                order_sn: str  # 订单编号
                order_status: int  # 发货状态，枚举值：1：待发货，2：已发货待签收，3：已签收
                order_tag_list: list[OrderTagListItem]  # 订单标签列表
                """订单标签列表
                no_trace_delivery=无痕发货
                only_support_replace=只换不修
                duoduo_wholesale=多多批发
                return_freight_payer=退货包运费
                free_sf=顺丰包邮
                support_nationwide_warranty=全国联保
                self_contained=门店自提
                delivery_one_day=当日发货
                oversea_tracing=全球购溯源
                distributional_sale=分销订单
                open_in_festival=不打烊
                region_black_delay_shipping=发货时间可延迟
                same_city_distribution=同城配送
                has_subsidy_postage=补贴运费红包
                has_sf_express_service=顺丰加价
                community_group=小区团购
                has_ship_additional=加运费发顺丰
                ship_additional_order=加运费补差价订单
                conso_order=集运订单
                allergy_refund=过敏包退
                professional_appraisal=专业鉴定
                ship_hold=暂停发货
                home_delivery_door=送货上门
                direct_mail_activity=直邮活动
                local_depot=本地仓订单
                trade_in_national_subsidy=以旧换新·国家补贴
                bought_from_vegetable=多多买菜次日达·送货上门
                delivery_schedule=分批发货
                """
                pay_amount: float  # 支付金额（元）支付金额=商品金额-折扣金额+邮费+服务费
                pay_no: str  # 支付单号
                pay_time: str  # 支付时间
                pay_type: str  # 支付方式，枚举值：QQ,WEIXIN,ALIPAY,LIANLIANPAY
                platform_discount: float  # 平台优惠金额
                postage: float  # 邮费
                pre_sale_time: str  # 预售时间
                promise_delivery_time: str  # 承诺送达时间
                promotion_detail_list: list[PromotionDetailListItem]  # 优惠券信息
                province: str  # 收件省份
                province_id: int  # 收件省份ID
                receive_time: str  # 确认收货时间
                receiver_address: str  # 收件人地址，不拼接省市区。订单状态为待发货状态，且订单未被风控打标的情况下返回密文数据；其余情况返回空字符串。
                receiver_address_mask: str  # 收件人地址（打码）
                receiver_name: str  # 收件人姓名。订单状态为待发货状态，且订单未被风控打标的情况下返回密文数据；其余情况返回空字符串。
                receiver_name_mask: str  # 收件人姓名（打码）
                receiver_phone: str  # 收件人电话。订单状态为待发货状态，且订单未被风控打标的情况下返回密文数据；其余情况返回空字符串。
                receiver_phone_mask: str  # 收件人手机号（打码）
                refund_status: int  # 退款状态，枚举值：1：无售后或售后关闭，2：售后处理中，3：退款中，4： 退款成功
                remark: str  # 商家订单备注
                remark_tag: int  # 订单备注标记，1-红色，2-黄色，3-绿色，4-蓝色，5-紫色
                remark_tag_name: str  # 订单备注标记名称
                resend_delivery_list: list[ResendDeliveryListItem]  # 补寄额外运单列表
                return_freight_payer: int  # 退货包运费，1:是，0:否
                risk_control_status: int  # 订单审核状态（0-正常订单， 1-审核中订单）
                self_contained: int  # 是否门店自提，1-是，0-否
                seller_discount: float  # 店铺优惠金额
                service_fee_detail: list[ServiceFeeDetailItem]  # 服务费明细列表
                """服务费明细列表
                sf_express_fee=顺丰加价服务
                install_fee=上门安装服务
                store_install_fee=到店安装服务
                take_to_store_install_fee=携货到店安装
                dismantle_and_home_install_fee=拆旧+上门安装
                """
                ship_additional_link_order: str  # 关联的加运费发顺丰的补差价订单
                ship_additional_origin_order: str  # 加运费补差价订单的原单
                shipping_time: str  # 发货时间
                shipping_type: int  # 创建交易时的物流方式(1-预约配送，2-1小时达，3-消费者预约送达)
                step_order_info: StepOrderInfo  # 定金订单信息 ，非定金订单该字段为null
                stock_out_handle_status: int  # 缺货处理状态 -1:无缺货处理 0: 缺货待处理 1缺货已处理
                store_info: StoreInfo  # 门店信息
                support_nationwide_warranty: int  # 全国联保，1:是，0:否
                town: str  # 收件地区县
                town_id: int  # 区县编码
                tracking_number: str  # 快递运单号
                trade_type: int  # 订单类型 0-普通订单、1-定金订单
                updated_at: str  # 订单最近一次更新时间
                urge_shipping_time: str  # 催发货时间
                yyps_date: str  # 预约配送日期
                yyps_time: str  # 预约配送时段

            order_info: OrderInfo

        order_info_get_response: OrderInfoGetResponse


class DoudianReceiverInfoResp(BaseModel):
    receiver_name: str
    receiver_phone: str
    province: str
    city: str
    town: str
    street: str
    detail: str

    @property
    def full_address(self):
        return f"{self.province} {self.city} {self.town} {self.street} {self.detail}"


class DoudianOrderAddrFieldSchema(BaseModel):
    id: str
    name: str


class DoudianSkuSpecSchema(BaseModel):
    name: str
    value: str


class DoudianOrderSkuSchema(BaseModel):
    product_id: int  # 商品编码
    sku_id: int  # 商品sku id
    goods_price: int  # 单位：分
    product_name: str  # 商品名称
    out_product_id: t.Optional[str] = None  # 商品外部编码，商家不可见
    out_sku_id: t.Optional[str] = None  # 外部sku id，商家不可见
    code: t.Optional[str] = None  # 外部sku编码，商家可见
    spec: t.List[DoudianSkuSpecSchema] = []  # 规格列表
    item_num: int  # 订单商品数量

    @property
    def spec_str(self) -> str:
        raw = [x.value for x in self.spec]
        return ";".join(raw)


class DoudianOrderLogisticsSchema(BaseModel):
    tracking_no: str
    company: str
    ship_time: t.Optional[int] = None
    delivery_id: str
    company_name: str


class DoudianOrderAddrSchema(BaseModel):
    city: DoudianOrderAddrFieldSchema
    province: DoudianOrderAddrFieldSchema
    street: DoudianOrderAddrFieldSchema
    town: DoudianOrderAddrFieldSchema
    encrypt_detail: str


class DoudianOrderDetailSchema(BaseModel):
    order_id: str
    order_status: int

    create_time: int  # 下单时间
    pay_time: t.Optional[int] = None  # 付款时间
    pay_amount: int  # 实付金额（分）

    # 收货信息相关
    post_addr: DoudianOrderAddrSchema  # 收货地址
    encrypt_post_tel: str  # 收货人电话（加密）
    encrypt_post_receiver: str  # 收货人电话（加密）

    seller_words: str
    seller_remark_stars: int

    # sku信息
    sku_order_list: t.List[DoudianOrderSkuSchema]

    # 物流信息
    logistics_info: t.List[DoudianOrderLogisticsSchema]


class DoudianExchangeSkuInfo(BaseModel):
    sku_id: str
    code: str
    num: int
    out_sku_id: str
    out_warehouse_id: str
    supplier_id: str
    url: str
    name: str
    price: str
    spec_desc: str


class DoudianOrderLogistics(BaseModel):
    tracking_no: str
    company_name: str
    company_code: str
    logistics_time: int
    logistics_state: int


class DoudianPlaceHolder(BaseModel):
    text: str
    url: str


class DoudianReasonSecondLabel(BaseModel):
    code: int
    name: str


class DoudianSkuSpec(BaseModel):
    name: str
    value: str


class DoudianRelatedOrderInfo(BaseModel):
    sku_order_id: str
    order_status: int
    pay_amount: int
    post_amount: int
    item_num: int
    create_time: int
    tax_amount: int
    is_oversea_order: int
    product_name: str
    product_id: int
    product_image: str
    sku_spec: t.List[DoudianSkuSpec]
    shop_sku_code: str
    logistics_code: str
    aftersale_pay_amount: int
    aftersale_post_amount: int
    aftersale_tax_amount: int
    aftersale_item_num: int
    promotion_pay_amount: int
    price: int
    logistics_company_name: str
    given_sku_order_ids: t.List[str]


class DoudianOrderInfo(BaseModel):
    shop_order_id: str
    related_order_info: t.List[DoudianRelatedOrderInfo]
    order_flag: int


class DoudianAftersaleInfo(BaseModel):
    aftersale_status_to_final_time: int
    aftersale_id: str
    aftersale_order_type: int
    aftersale_type: int
    aftersale_status: int
    related_id: str
    apply_time: int
    update_time: int
    status_deadline: int
    refund_amount: int
    refund_post_amount: int
    aftersale_num: int
    part_type: int
    aftersale_refund_type: int
    refund_type: int
    arbitrate_status: int
    create_time: int
    refund_tax_amount: int
    left_urge_sms_count: int
    return_logistics_code: str
    risk_decision_code: int
    risk_decision_reason: str
    risk_decision_description: str
    return_promotion_amount: int
    refund_status: int
    arbitrate_blame: int
    exchange_sku_info: DoudianExchangeSkuInfo
    return_logistics_company_name: str
    exchange_logistics_company_name: str
    remark: str
    got_pkg: int
    order_logistics: t.List[DoudianOrderLogistics]
    is_agree_refuse_sign: int
    reason_second_labels: t.List[DoudianReasonSecondLabel]
    store_id: int
    store_name: str
    aftersale_sub_type: int
    auto_audit_bits: t.List[int]
    refund_time: t.Optional[int]


class TextPart(BaseModel):
    logistics_text: str
    aftersale_status_text: str
    aftersale_type_text: str
    return_logistics_text: str
    aftersale_refund_type_text: str
    reason_text: str
    bad_item_text: str
    arbitrate_status_text: str


class DoudianAfterSale(BaseModel):
    aftersale_info: DoudianAftersaleInfo
    order_info: DoudianOrderInfo
    text_part: TextPart


class DoudianAfterSaleListResp(BaseModel):
    has_more: bool
    total: int
    page: int
    size: int
    items: t.List[DoudianAfterSale]


class DoudianAfterSaleOperateResult(BaseModel):
    aftersale_id: int
    status_code: int
    status_msg: str


class DoudianAftersaleOperateResp(BaseModel):
    items: t.List[DoudianAfterSaleOperateResult]


class DoudianEvidence(BaseModel):
    type: int
    url: str
    desc: t.Optional[str] = None
