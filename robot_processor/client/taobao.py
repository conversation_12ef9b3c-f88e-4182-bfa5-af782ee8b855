import hashlib
import json
import time
from typing import Any
from typing import Dict
from urllib.parse import parse_qsl
from urllib.parse import urlencode

from loguru import logger
from requests import HTTPError
from result import Err
from result import Ok
from result import Result

from robot_metrics import Stats
from robot_processor.client_mixins import Session
from robot_processor.error.client_request import TOPError
from robot_processor.types.client.top.top_response import JushitaJdpUsersGet
from robot_processor.types.client.top.top_response import MiniappShorturlCreate
from robot_processor.types.client.top.top_response import MiniappTemplateInstantiate
from robot_processor.types.client.top.top_response import MiniappTemplateOnlineapp
from robot_processor.types.client.top.top_response import MiniappTemplate<PERSON>ueryapp
from robot_processor.types.client.top.top_response import MiniappTemplateUpdateapp
from robot_processor.types.client.top.top_response import OaidDecryptResult
from robot_processor.types.client.top.top_response import OrderSkuUpdateResult
from robot_processor.types.client.top.top_response import RefundMessagesResult
from robot_processor.types.client.top.top_response import RefundsAgree
from robot_processor.types.client.top.top_response import RefundsReview
from robot_processor.types.client.top.top_response import SellerMemosGetResult
from robot_processor.types.client.top.top_response import ShopSellerGet
from robot_processor.types.client.top.top_response import SkusQuantityUpdateResult
from robot_processor.types.client.top.top_response import SmsSendByOaidResult
from robot_processor.types.client.top.top_response import SubUsersGet
from robot_processor.types.client.top.top_response import TaobaoLogisticsTraceGet
from robot_processor.types.client.top.top_response import TaobaoTradeRatesGet
from robot_processor.types.client.top.top_response import TempalteCreate
from robot_processor.types.client.top.top_response import TmallTradeRateFeedsGet
from robot_processor.utils import filter_none
from robot_processor.utils import unwrap_optional

_statsd_it = Stats.Client.timer(client_name="taobao-server")


class TaobaoClient:
    session = Session()

    def __init__(self):
        self._access_key: str | None = None
        self._access_secret: str | None = None

    def init_app(self, access_key: str, access_secret: str):
        self._access_key = access_key
        self._access_secret = access_secret

    @property
    def api_url(self):
        return "https://gw.api.taobao.com/router/rest"

    @property
    def app_key(self):
        return unwrap_optional(self._access_key)

    @_statsd_it
    def api_request(self, method, access_token=None, retry_times=1, **data) -> Result[Any, Exception]:
        _logger = logger.bind(k=method)
        data["method"] = method
        data["session"] = access_token
        while retry_times > 0:
            retry_times -= 1
            try:
                resp = self.session.post(self.api_url, data=data, auth=self._auth)
                resp.raise_for_status()  # raise HTTPError
                response_dict = resp.json()

                if "error_response" not in response_dict:
                    _logger.info(f"调用 {method} 成功 {resp.text}")
                    return Ok(response_dict)

                can_retry_error = ("Remote service error",)
                if retry_times > 0 and response_dict["error_response"]["msg"] in can_retry_error:
                    _logger.warning(f"调用 {method} 失败: {resp.text}, 剩余重试次数: {retry_times}")
                    continue
                else:
                    _logger.error(f"调用 {method} 失败: {resp.text}, 重试次数已用完")
                    return Err(
                        TOPError(
                            resp.request.body,
                            response_dict["error_response"],
                            response_dict["error_response"]["msg"],
                        )
                    )
            except Exception as e:
                if retry_times > 0 and isinstance(e, HTTPError):
                    _logger.warning(f"调用 {method} 发生异常: {e}, 剩余重试次数: {retry_times}")
                    continue
                else:
                    _logger.error(f"调用 {method} 发生异常: {e}")
                    return Err(e)

        _logger.error(f"调用 {method} 失败, 重试次数已用完")
        return Err(Exception("retry times exceed"))

    def _sign(self, payload):
        if self._access_key is None or self._access_secret is None:
            raise Exception("access_key or access_secret not exists")
        query = "".join(f"{key}{val}" for key, val in sorted(payload.items()))
        bytes_to_sign = "".join([self._access_secret, query, self._access_secret]).encode("utf8")
        return hashlib.md5(bytes_to_sign).hexdigest().upper()

    def _auth(self, request):
        if self._access_key is None or self._access_secret is None:
            raise Exception("access_key or access_secret not exists")
        payload = dict(parse_qsl(request.body))
        payload["format"] = "json"
        payload["simplify"] = "true"
        payload["app_key"] = self._access_key
        payload["timestamp"] = str(int(time.time() * 1000))
        payload["sign_method"] = "md5"
        payload["v"] = "2.0"
        if "method" not in payload:
            raise KeyError("method not set")
        signature = self._sign(payload)
        payload["sign"] = signature
        request.body = urlencode(payload)
        return request

    def tmall_traderate_feeds_get(self, access_token: str, oid: str) -> Result[TmallTradeRateFeedsGet, Exception]:
        """查询子订单对应的评价、追评以及语义标签

        通过子订单ID获取天猫订单对应的评价，追评，以及对应的语义标签

        References:
            https://open.taobao.com/api.htm?docId=22532&docType=2
        """
        return self.api_request("tmall.traderate.feeds.get", access_token, child_trade_id=oid)

    def taobao_traderates_get_by_datetime_range(
        self, access_token, page_no: int, start_date: str, end_date: str
    ) -> Result[TaobaoTradeRatesGet, Exception]:
        """搜索评价信息

        References:
            https://open.taobao.com/v2/doc?spm=a219a.15212433.0.0.231c669acceils#/apiFile?docType=2&docId=55
        """
        return self.api_request(
            "taobao.traderates.get",
            access_token,
            fields="tid,oid",
            rate_type="get",
            start_date=start_date,
            end_date=end_date,
            use_has_next="true",
            page_size=150,
            role="buyer",
            page_no=page_no,
        )

    def taobao_refunds_agree(
        self, access_token: str, refund_id: str, amount: int, version: int
    ) -> Result[RefundsAgree, Exception]:
        """淘宝同意退款
        卖家同意退款，支持批量退款，只允许子账号操作。淘宝/天猫退款一次最多能退20笔，总金额不限。新增是否需要短信验证，默认为需要

        References:
            https://open.taobao.com/api.htm?docId=22465&docType=2
        """
        return self.api_request(
            "taobao.rp.refunds.agree",
            access_token,
            refund_infos=f"{refund_id}|{amount}|" f"{version}",
            ignore_code="true",
        )

    def taobao_logisitcs_trace_get(self, access_token: str, tid: str) -> Result[TaobaoLogisticsTraceGet, Exception]:
        """用户根据交易号查询物流流转信息

        References:
            https://open.taobao.com/api.htm?docId=65623&docType=2&source=search
        """
        return self.api_request("taobao.logistics.trace.get", access_token, tid=int(tid))

    def tmall_refunds_agree(
        self, access_token: str, refund_id: str, amount: int, version: int, phase: str
    ) -> Result[RefundsAgree, Exception]:
        """天猫同意退款
        卖家同意退款，支持批量退款，只允许子账号操作。淘宝/天猫退款一次最多能退20笔，总金额不限。新增是否需要短信验证，默认为需要

        References:
            https://open.taobao.com/api.htm?docId=22465&docType=2
        """
        return self.api_request(
            "taobao.rp.refunds.agree",
            access_token,
            refund_infos=f"{refund_id}|{amount}|" f"{version}|{phase}",
            ignore_code="true",
        )

    def sms_template_create(
        self, template_type: int, template_type_class: str, remark: str, template_name: str, template_content: str
    ) -> Result[TempalteCreate, Exception]:
        """淘宝短信模板创建

        References:
            https://open.taobao.com/api.htm?docId=60274&docType=2&scopeId=11815
        """
        return self.api_request(
            "taobao.jst.sms.template.create",
            sms_template_for_isv_request=json.dumps(
                {
                    "template_type": template_type,
                    "template_type_class": template_type_class,
                    "remark": remark,
                    "template_name": template_name,
                    "template_content": template_content,
                }
            ),
        )

    def sms_template_query(self, template_code: str):
        """淘宝短信模板查询

        References:
            https://open.taobao.com/api.htm?docId=60275&docType=2&scopeId=11815
        """
        return self.api_request(
            "taobao.jst.sms.template.query", query_sms_template_request=json.dumps({"template_code": template_code})
        )

    def sms_sign_create(self, access_token: str, sign_name: str, remark: str, sign_source: int):
        return self.api_request(
            "taobao.jst.sms.signname.create",
            access_token,
            add_sms_sign_request=json.dumps({"sign_name": sign_name, "remark": remark, "sign_source": sign_source}),
        )

    def sms_send_by_oaid(
        self, access_token: str, oid: str, sms_free_sign_name: str, template_code: str, oaid: str, params: Dict
    ) -> Result[SmsSendByOaidResult, Exception]:
        """基于OAID的短信发送接口
        基于OAID的短信发送接口

        References:
            https://open.taobao.com/api.htm?spm=a219a.7386797.0.0.2c05669aVkaaC5&source=search&docId=54973&docType=2
        """
        return self.api_request(
            "taobao.jst.sms.oaid.message.send",
            access_token,
            param_send_message_by_o_a_i_d_request=json.dumps(
                {
                    "order_id": oid,
                    "params": params,
                    "sms_free_sign_name": sms_free_sign_name,
                    "template_code": template_code,
                    "oaid": oaid,
                }
            ),
        )

    def tmall_refunds_review(
        self, access_token: str, sub_nick: str, refund_id: str, version: int, phase: str, message: str
    ) -> Result[RefundsReview, Exception]:
        """天猫审核退款单

        References:
            https://open.taobao.com/api.htm?spm=a219a.7386653.1.186.1a504de0qosGhB&source=search&docId=23875&docType=2
        """
        return self.api_request(
            "taobao.rp.refund.review",
            access_token,
            operator=sub_nick,
            refund_id=refund_id,
            refund_phase=phase,
            refund_version=version,
            message=message,
            result="true",
        )

    def ordersku_update(
        self, access_token: str, oid: str, sku_id: str | None = None, sku_props: str | None = None
    ) -> Result[OrderSkuUpdateResult, Exception]:
        """更新交易的销售属性

        References:
            https://open.taobao.com/api.htm?docId=240&docType=2
        """
        return self.api_request(
            "taobao.trade.ordersku.update", access_token, oid=oid, sku_id=sku_id, sku_props=sku_props
        )

    def skus_quantity_update(
        self, access_token: str, num_iid: int, skuid_quantities: str, type: int
    ) -> Result[SkusQuantityUpdateResult, Exception]:
        """提供按照全量/增量的方式批量修改SKU库存的功能

        References:
            https://open.taobao.com/api.htm?spm=a219a.7386797.0.0.4c3f669aB0t2Vv&source=search&docId=21169&docType=2
        """
        return self.api_request(
            "taobao.skus.quantity.update", access_token, num_iid=num_iid, skuid_quantities=skuid_quantities, type=type
        )

    def shop_seller_get(self, access_token: str, fields: str) -> Result[ShopSellerGet, Exception]:
        """卖家店铺基础信息查询

        获取卖家店铺的基本信息

        References:
            https://open.taobao.com/api.htm?docId=42908&docType=2
        """
        return self.api_request("taobao.shop.seller.get", access_token, retry_times=3, fields=fields)

    def subusers_get(self, access_token: str, user_nick: str) -> Result[SubUsersGet, Exception]:
        """获取指定账户的子账号简易信息列表

        获取主账号下的子账号简易账号信息集合。（只能通过主账号登陆并且查询该属于主账号的子账号信息）

        References:
            https://open.taobao.com/api.htm?docId=21686&docType=2

        """
        return self.api_request("taobao.subusers.get", access_token, retry_times=3, user_nick=user_nick)

    def miniapp_app_seller_config_complete(self, access_token: str, app_id: str) -> Result[dict, Exception]:
        """商家完成小程序相关配置

        通过该接口告知平台商家已经完成小程序相关的必要设置，可进行后续操作。主要用于小部件、客服插件等场景。

        Args:
            access_token: Session key
            app_id: 商家已完成配置的小部件/B端插件的appid

        References:
            https://open.taobao.com/api.htm?docId=51768&docType=2
        """
        return self.api_request("taobao.miniapp.app.seller.config.complete", access_token, retry_times=3, app_id=app_id)

    def jushita_jdp_users_get(self, page_no=1, page_size=200, user_id=None) -> Result[JushitaJdpUsersGet, Exception]:
        """获取开通的订单同步服务的用户

        获取开通的订单同步服务的用户，含有rds的路由关系

        Args:
            page_no: 当前页数
            page_size: 每页记录数，默认200
            user_id: 如果传了user_id表示单条查询

        References:
            https://open.taobao.com/api.htm?docId=22129&docType=2
        """
        return self.api_request(
            "taobao.jushita.jdp.users.get", **filter_none(dict(page_no=page_no, page_size=page_size, user_id=user_id))
        )

    def jushita_jdp_user_add(self, access_token: str, rds_name: str) -> Result[dict, Exception]:
        """添加数据推送用户

        提供给接入数据推送的应用添加数据推送服务的用户

        Args:
            access_token: Session key
            rds_name: 数据推送服务的用户名称

        References:
            https://open.taobao.com/api.htm?docId=21736&docType=2
        """
        return self.api_request("taobao.jushita.jdp.user.add", access_token, retry_times=3, rds_name=rds_name)

    def jushita_jdp_user_delete(self, nick: str) -> Result[dict, Exception]:
        """删除数据推送用户

        删除应用的数据推送用户，用户被删除后，重新添加时会重新同步历史数据。

        Args:
            nick: 要删除用户的昵称

        References:
            https://open.taobao.com/api.htm?docId=21732&docType=2
        """
        return self.api_request("taobao.jushita.jdp.user.delete", retry_times=3, nick=nick)

    def miniapp_shorturl_create(self, access_token: str, miniapp_url: str) -> Result[MiniappShorturlCreate, Exception]:
        """生成淘宝小程序短链接

        提供淘宝小程序短链接生成的能力，
        只允许对淘宝小程序对应的域名：https://m.duanqu.com/ 生成对应的短链接，其他域名无效
        特别注意：短链接有效期为30天，超过时效短链接将无法正常跳转到原始链接地址，请勿将短链接投放或装修到长期存在的入口

        Args:
            access_token
                Session key
            miniapp_url
                小程序链接地址。
                说明：链接地址，只允许https协议，域名只支持m.duanqu.com，
                链接必须包含_ariver_appid参数，
                链接不能够包含spm、short_name、app、tb_url_time_stamp这些系统保留参数

        References:
            https://open.taobao.com/api.htm?docId=52233&docType=2
        """
        return self.api_request("taobao.miniapp.shorturl.create", access_token, miniapp_url=miniapp_url)

    def miniapp_template_instantiate(
        self,
        access_token: str,
        clients: str,
        description: str,
        ext_json: str,
        icon: str,
        template_id: str,
        template_version: str,
    ) -> Result[MiniappTemplateInstantiate, Exception]:
        """构建实例化应用

        实例化saas化的小程序应用，创建一个小程序应用实例

        Args:
            access_token: Session key
            clients: 投放端,目前可投放: taobao(淘宝),tmall(天猫)，taobao为必填，需要模板在这些端上已经发布上线
            description: 描述长度(9~200)
            ext_json: 扩展信息，JSON格式。
            icon: 小程序icon
            template_id: 模板id
            template_version: 模板版本

        """
        return self.api_request(
            "taobao.miniapp.template.instantiate",
            access_token,
            retry_times=3,
            clients=clients,
            description=description,
            ext_json=ext_json,
            icon=icon,
            template_id=template_id,
            template_version=template_version,
        )

    def miniapp_template_queryapp(
        self,
        access_token: str,
        template_id: str,
        page_num: int = 1,
        page_size: int = 50,
    ) -> Result[MiniappTemplateQueryapp, Exception]:
        """查询实例化应用版本

        根据模板id和商家信息，查询实例化小程序版本查询

        Args:
            access_token: Session key
            template_id: 模板id
            page_num: 分页号,>=1
            page_size: 分页大小，最大50，按照小程序 id 倒序
        References:
            https://open.taobao.com/api.htm?docId=47798&docType=2

        """
        return self.api_request(
            "taobao.miniapp.template.queryapp",
            access_token,
            retry_times=3,
            template_id=template_id,
            page_num=page_num,
            page_size=page_size,
        )

    def miniapp_template_query_latest_online_version(
        self, access_token: str, template_id: str, app_id: str | None
    ) -> str | None:
        if not app_id:
            return None
        api_res = self.miniapp_template_queryapp(access_token, template_id)
        match api_res:
            case Ok(res):
                for app_version_info in res["all_version_infos"]:
                    if app_version_info["app_info"]["app_id"] == app_id:
                        for version_dto in app_version_info["app_version_list"]:
                            if version_dto["status"] == "ONLINE":
                                return version_dto["template_version"]
        return None

    def miniapp_template_updateapp(
        self,
        access_token: str,
        clients: str,
        app_id: str,
        template_id: str,
        template_version: str,
    ) -> Result[MiniappTemplateUpdateapp, Exception]:
        """更新实例化应用

        商家应用c端模板实例化小程序更新，生成新的版本，但不会自动上线新版本

        Args:
            access_token: Session key
            clients: 投放端,目前可投放: taobao(淘宝),tmall(天猫)，taobao为必填，需要模板在这些端上已经发布上线
            app_id: 小程序 id
            template_id: 模板 id
            template_version: 模板版本
        References:
            https://open.taobao.com/api.htm?docId=47799&docType=2
        """
        return self.api_request(
            "taobao.miniapp.template.updateapp",
            access_token,
            retry_times=3,
            clients=clients,
            app_id=app_id,
            template_id=template_id,
            template_version=template_version,
        )

    def miniapp_template_onlineapp(
        self,
        access_token: str,
        clients: str,
        app_id: str,
        template_id: str,
        template_version: str,
        app_version: str,
    ) -> Result[MiniappTemplateOnlineapp, Exception]:
        """上线实例化应用

        将指定的预览版本发布上线，预览版本号由构建实例化或更新实例化接口返回。

        References:
            https://open.taobao.com/api.htm?docId=47754&docType=2
        """
        return self.api_request(
            "taobao.miniapp.template.onlineapp",
            access_token,
            retry_times=3,
            clients=clients,
            app_id=app_id,
            template_id=template_id,
            template_version=template_version,
            app_version=app_version,
        )

    def create_auth_token(
        self,
        code: str,
    ) -> Result[dict, Exception]:
        """
        用户通过 code 换获取 access_token。
        :param code:
        :return:
        """
        return self.api_request(
            method="taobao.top.auth.token.create",
            code=code,
        )

    def refresh_auth_token(
        self,
        refresh_token: str,
    ) -> Result[dict, Exception]:
        """
        根据 refresh_token 重新生成 token，目前只有服务市场订购类应用可以刷新 token。
        其他类型应用（如商家后台）使用固定时长 token，不提供刷新功能。

        :param refresh_token:
        :return:
        """
        return self.api_request(
            method="taobao.top.auth.token.refresh",
            refresh_token=refresh_token,
        )

    def get_open_id_by_mix_nick(self, mix_nick: str) -> Result[dict, Exception]:
        return self.api_request(method="taobao.openuid.get.bymixnick", mix_nick=mix_nick)

    def oaid_decrypt(self, access_token: str, oaid: str, tid: str) -> Result[OaidDecryptResult, Exception]:
        return self.api_request(
            "taobao.top.oaid.decrypt",
            access_token,
            query_list=json.dumps([{"oaid": oaid, "tid": tid, "scene": "1007"}]),
        )

    def refund_messages_get(self, access_token: str, refund_id: str) -> Result[RefundMessagesResult, Exception]:
        return self.api_request(
            "taobao.refund.messages.get",
            access_token,
            page_no=1,
            page_size=100,
            fields="id,content,created,message_type,owner_id,owner_nick,"
            "owner_role,refund_id,refund_phase,owner_open_uid",
            refund_id=refund_id,
        )

    def trade_seller_memos_get(self, access_token: str, tid: str) -> Result[SellerMemosGetResult, Exception]:
        """查询订单备注列表

        查询订单备注列表

        Args:
            access_token: Session key
            tid: 订单号
        References:
            https://open.taobao.com/api.htm?docId=68457&docType=2
        """
        return self.api_request("taobao.trade.sellermemos.get", access_token, tid=tid)

    def trade_memo_update(
        self, access_token: str, tid: str, memo: str | None, flag: int | None
    ) -> Result[dict, Exception]:
        """修改交易备注

        需要商家或以上权限才可调用此接口，可重复调用本接口更新交易备注，本接口同时具有添加备注的功能

        References:
            https://open.taobao.com/api.htm?docId=49&docType=2&source=search
        """
        data = filter_none(dict(memo=memo, flag=flag))
        if not data:
            return Err(ValueError("memo and flag cannot be empty at the same time"))
        return self.api_request("taobao.trade.memo.update", access_token, tid=tid, **data)

    def trade_support_refund_open(
        self,
        access_token: str,
        tid: str,
        oid: str | None,
    ) -> Result[dict, Exception]:
        """
        预售订单打开退定金入口
        打开预售订单退定金入口，买家可见退款入口。后续如果买家申请退款，定金全额退款（无需商家同意）

        References:
            https://open.taobao.com/api.htm?source=search&docId=72130&docType=2
        """
        data = filter_none(dict(tid=tid, oid=oid))
        method = "taobao.trade.support.refund.open"
        return self.api_request(method=method, access_token=access_token, **data)

    def change_address(
        self,
        access_token: str,
        tid: str,
        receiver_name: str | None = None,
        receiver_phone: str | None = None,
        receiver_mobile: str | None = None,
        receiver_state: str | None = None,
        receiver_city: str | None = None,
        receiver_district: str | None = None,
        receiver_address: str | None = None,
        receiver_zip: str | None = None,
        receiver_town: str | None = None,
    ):
        """
        更改交易的收货地址

        References:
             https://open.taobao.com/api.htm?docId=241&docType=2&scopeId=12147
        """
        data = filter_none(
            dict(
                tid=tid,
                receiver_name=receiver_name,
                receiver_phone=receiver_phone,
                receiver_mobile=receiver_mobile,
                receiver_state=receiver_state,
                receiver_city=receiver_city,
                receiver_district=receiver_district,
                receiver_address=receiver_address,
                receiver_zip=receiver_zip,
                receiver_town=receiver_town,
            )
        )
        method = "taobao.trade.shippingaddress.update"
        return self.api_request(method=method, access_token=access_token, **data)
