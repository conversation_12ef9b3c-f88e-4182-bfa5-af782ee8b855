from typing import cast, Optional

from leyan_proto.digismart.dgt_risk_control.dgt_risk_control_pb2 import (
    RiskControlReportRequest, Dimension, GetRiskControlRequest
)
from leyan_proto.digismart.dgt_risk_control.dgt_risk_control_pb2_grpc import DgtRiskControlServiceStub
from leyan_grpc.client.venice import GaiaStub

from robot_metrics import Stats
from robot_processor.client.conf import app_config as config

_statsd_it = Stats.Client.timer(client_name="risk-control-server")


class RiskControlClient:

    def __init__(self):
        self._stub = GaiaStub(DgtRiskControlServiceStub,
                              "dgt-risk-control", "risk-control-server")

    @property
    def timeout(self):
        return config.RISK_CONTROL_TIMEOUT

    @property
    def stub(self):
        return cast(DgtRiskControlServiceStub, self._stub)

    @_statsd_it
    def report_bo_risk(self, org_id, sid, model, scope, bo_id: int, dimensions: list) -> bool:
        req = RiskControlReportRequest(
            org_id=org_id, sid=sid, model=model, scope=scope, object_id=str(bo_id),
            dimensions=[
                Dimension(dimension=each["dimension"], dimension_value=each["dimension_value"])
                for each in dimensions
                if each.get("dimension_value")
            ]
        )
        resp = self.stub.Report(req, timeout=self.timeout)
        return resp.success

    @_statsd_it
    def cancel_risk(self, org_id, sid, model, scope, bo_id) -> bool:
        req = RiskControlReportRequest(
            org_id=org_id, sid=sid, model=model, scope=scope, object_id=str(bo_id),
        )
        resp = self.stub.Delete(req, timeout=self.timeout)
        return resp.success

    @_statsd_it
    def query_risk(self, org_id, sid, model, scope, dimensions, exclude_bo_id=None):
        """
        唯一值校验时查询维度已存在的工单，如果传入了工单id 需要排除掉当前工单id
        """
        req = GetRiskControlRequest(
            org_id=org_id, sid=sid, model=model, scope=scope,
            dimensions=[Dimension(**d) for d in dimensions]
        )
        resp = self.stub.Query(req, timeout=self.timeout)
        bo_ids = {str(d.object_id) for d in resp.data}
        if exclude_bo_id:
            bo_ids = bo_ids.difference([str(exclude_bo_id)])
        return bo_ids

    def widget_value_unique(self, widget, value) -> list[tuple[str, str | list[str]]]:
        from robot_processor.form.models import WidgetInfo
        from robot_processor.form.api.widget_schema import WidgetInfoDict

        key_value_pairs: list[tuple[str, str | list[str]]] = []
        if value is None:
            return key_value_pairs
        value_unique = widget.get("option_value", {}).get("valueUnique")
        if value_unique:
            value_repr = WidgetInfo.Utils.get_widget_info_repr(cast(WidgetInfoDict, widget), value)
            if value_repr is not None:
                key_value_pairs.append((widget["key"], value_repr))
        if widget.get("type") == "collection":
            for child in widget.get("option_value", {}).get("fields", []):
                for child_key, child_value in self.widget_value_unique(child, value.get(child["key"])):
                    key_value_pairs.append(("{}.{}".format(widget["key"], child_key), child_value))
        return key_value_pairs

    def check_widget_value_unique(self, org_id, sid, form_id: int,
                                  business_order_id: Optional[int], data_for_check: dict,
                                  widgets: dict, raise_on_fail=False):
        from robot_processor.error.errors import BusinessOrderValueUniqueConstraintError
        from robot_processor.enums import WidgetValueUniqueCheckType

        results = {}
        for key, value in data_for_check.items():
            widget = widgets.get(key)
            if not widget:
                continue
            for check_key, value_repr in self.widget_value_unique(widget, value):
                if isinstance(value_repr, str):
                    value_repr = [value_repr]
                res = self.query_risk(
                    org_id, sid, "business_order", str(form_id),
                    [{"dimension": check_key, "dimension_value": v} for v in value_repr],
                    exclude_bo_id=business_order_id
                )
                if not res:
                    continue
                results[key] = res
                check_type = widget['option_value'].get('checkType', WidgetValueUniqueCheckType.CREATE_FORBIDDEN.value)
                if check_type == WidgetValueUniqueCheckType.CREATE_FORBIDDEN.value:
                    if raise_on_fail:
                        e = BusinessOrderValueUniqueConstraintError(
                            job_id=0, business_order_id=business_order_id,
                            widget_key=key, value=value, detail=res
                        )
                        e.biz_display += "，请修改 {}".format(
                            widget['option_value']["label"]
                        )
                        raise e
        return results
