from enum import StrEnum

from lepollo import ApolloConfig
from lepollo import get_config
from pydantic import BaseModel
from requests_opentracing import SessionTracing
from result import Err
from result import Ok

from robot_processor.error.client_request import SuperBookError
from robot_processor.utils import make_fields_optional
from robot_processor.utils import response_to_log


class SuperBook:
    """
    References:
        https://leyan.yuque.com/lyzr/tools/pm0bdwdbcq2us6bo
    """

    session = SessionTracing()

    class AppName(StrEnum):
        # 淘宝乐语助人
        TB_LYZR = "tb_lyzr"
        # 淘宝乐销客
        TB_LXK = "tb_lxk"
        # 拼多多爱图西
        PDD_ATX = "pdd_atx"
        # 拼多多207
        PDD_207 = "pdd_207"
        # 京东言准
        JD_YZ = "jd_yz"
        # 抖音爱图西
        DY_ATX = "dy_atx"
        # 快手乐分析
        KS_LFX = "ks_lfx"
        JD_FS = "jd_fs"
        XHS_ERP = "xhs_erp"

    def _request(self, path: str):
        response = self.session.get(
            super_book_config.endpoint + path, headers={"Authorization": "Bearer " + super_book_config.token}
        )
        if response.status_code != 200:
            return Err(SuperBookError(dict(path=path), response_to_log(response)))
        return Ok(response.json())

    def query_access_token(self, app_name: AppName, store_id: str) -> Ok["AccessTokenRes"] | Err[SuperBookError]:
        """授权查询"""
        path = f"/access-tokens/app-name/{app_name}/seller/{store_id}"
        return self._request(path).map(AccessTokenRes.parse_obj)

    def query_isv_orders(self, app_name: AppName, store_id: str) -> Ok["IsvOrderRes"] | Err[SuperBookError]:
        """订购查询"""
        path = f"/isv-orders/app-name/{app_name}/seller/{store_id}"
        return self._request(path).map(IsvOrderRes.parse_obj)


super_book_client = SuperBook()


class SuperBookConfig(ApolloConfig):
    __namespace__ = "client"

    @property
    def endpoint(self):
        return self.get_str("superbook.endpoint", "https://pre-api.leyanbot.com/superbook/service")

    @property
    def timeout(self):
        return self.get_int("superbook.timeout", 3)

    @property
    def token(self):
        return self.get_str("superbook.token", "")


super_book_config = get_config(config_class=SuperBookConfig)


@make_fields_optional
class AccessTokenRes(BaseModel):
    success: bool = True
    message: str
    access_token: str
    app_name: SuperBook.AppName
    expires_at: int
    leyan_seller_id: str


@make_fields_optional
class IsvOrderRes(BaseModel):
    class IsvOrder(BaseModel):
        app_name: SuperBook.AppName
        order_id: str
        seller_id: int
        service_start_time: int
        service_end_time: int

    success: bool = True
    message: str
    leyan_seller_id: str
    isv_orders: list[IsvOrder]
