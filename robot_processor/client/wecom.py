"""发送企业微信群（内部）消息"""
import base64
import hashlib
import io
import json
from enum import StrE<PERSON>
from typing import List, Tuple

from loguru import logger

from robot_processor.client_mixins import Session
from robot_processor.enums import EnumMixin
from robot_processor.imghdr import compress_image


class SenderEnum(EnumMixin, StrEnum):
    INPUT_TEXT_MOBILE = "INPUT_TEXT_MOBILE"
    INPUT_TEXT_USER = "INPUT_TEXT_USER"
    AT_ALL = "AT_ALL"


class TextPictureEnum(EnumMixin, StrEnum):
    SEPARATE = "SEPARATE"
    MERGE = "MERGE"


class WeComSender:
    session = Session()

    @staticmethod
    def image_base64_md5(picture_url):
        try:
            resp = WeComSender.session.get(url=picture_url)
            resp.raise_for_status()
        except BaseException as e:
            logger.opt(exception=e).error("获取图片失败")
            return

        # 图片（base64编码前）最大不能超过2M，支持JPG, PNG格式
        max_size = 2 * 1024 * 1024
        if len(resp.content) > max_size:
            ext = picture_url.split(".")[-1]
            compressed_image = compress_image(io.BytesIO(resp.content), ext, max_size)
            image_data = compressed_image.getvalue()
        else:
            image_data = resp.content
        image_base64 = str(base64.b64encode(image_data), encoding="utf-8")
        my_md5 = hashlib.md5()
        img_data = base64.b64decode(image_base64)
        my_md5.update(img_data)
        myhash = my_md5.hexdigest()

        return image_base64, myhash

    @staticmethod
    def get_post_payload(sender, image_urls, text_picture_merge, content) -> List[dict]:
        payload = []
        mentioned_list = []
        mentioned_mobile_list = []

        logger.info(
            f"message_args sender:{sender}, image_urls:{image_urls}, "
            f"text_picture_merge:{text_picture_merge}, content:{content}"
        )

        sender = sender or {}
        at_mode = sender.get("value")

        if at_mode == SenderEnum.AT_ALL:
            mentioned_mobile_list.append("@all")
        elif at_mode == SenderEnum.INPUT_TEXT_MOBILE:
            mentioned_mobile_list = [
                name.strip()
                for name in sender.get("extra", "").split(",")
                if name.strip()
            ]
        elif at_mode == SenderEnum.INPUT_TEXT_USER:
            mentioned_list = [
                name.strip()
                for name in sender.get("extra", "").split(",")
                if name.strip()
            ]

        if image_urls:
            text_picture_merge = text_picture_merge or {}
            text_picture_mode = text_picture_merge.get("value")
            if text_picture_mode == TextPictureEnum.MERGE:
                images = " ".join(
                    [
                        f"\n [图片{index}链接]({image})"
                        for index, image in enumerate(image_urls, start=1)
                    ]
                )
                _payload = {
                    "msgtype": "markdown",
                    "markdown": {
                        "content": f"{content} {images}",
                    },
                }
                payload.append(_payload)

            elif text_picture_mode == TextPictureEnum.SEPARATE:
                _payload = {
                    "msgtype": "text",
                    "text": {
                        "content": content,
                        "mentioned_mobile_list": mentioned_mobile_list,
                        "mentioned_list": mentioned_list,
                    },
                }

                payload.append(_payload)

                for image_url in image_urls:
                    image_data = WeComSender.image_base64_md5(image_url)
                    if image_data:
                        image_base64, myhash = image_data
                        _payload = {
                            "msgtype": "image",
                            "image": {"base64": image_base64, "md5": myhash},
                        }
                        payload.append(_payload)

        else:
            _payload = {
                "msgtype": "text",
                "text": {
                    "content": content,
                    "mentioned_mobile_list": mentioned_mobile_list,
                    "mentioned_list": mentioned_list,
                },
            }
            payload.append(_payload)

        return payload

    @staticmethod
    def send_message(
        url, sender, image_urls, text_picture_merge, content
    ) -> Tuple[bool, str]:
        payload_list = WeComSender.get_post_payload(
            sender, image_urls, text_picture_merge, content
        )
        headers = {"Content-Type": "application/json"}
        for payload in payload_list:
            try:
                resp = WeComSender.session.post(url=url, headers=headers, json=payload)
                resp.raise_for_status()
            except BaseException as e:
                return False, f"调用企业微信消息发送接口失败: {str(e)}"
            ret = resp.json()
            err_code = str(ret.get("errcode", ""))
            if err_code != "0":
                logger.error(f"企业微信消息发送失败 url={url}, payload={payload}, result={ret}")
                error_message = ret.get("errmsg")
                return False, error_message
        else:
            logger.info(
                "企业微信发送成功 url={}, payload={}",
                url,
                json.dumps(payload_list, ensure_ascii=False),
            )
            return True, ""
