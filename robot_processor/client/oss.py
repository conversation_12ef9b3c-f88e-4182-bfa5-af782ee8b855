import time
from hashlib import md5
from io import BytesIO
from os import path
from urllib.parse import quote

import oss2
from oss2.exceptions import ClientError
from oss2.models import PutObjectResult

from robot_metrics import Stats
from robot_processor import imghdr
from robot_processor.client.conf import oss_config as config

_statsd_it = Stats.Client.timer(client_name="oss-server")


class OSSException(Exception):
    def __init__(self, message):
        self.message = message


class InvalidImageError(OSSException):
    pass


class UploadError(OSSException):
    pass


class OSSClient:
    def __init__(self):
        self.bucket = oss2.Bucket(
            auth=oss2.Auth(config.OSS_KEY_ID, config.OSS_KEY_SECRET),
            endpoint=config.OSS_ENDPOINT,
            bucket_name=config.OSS_BUCKET_NAME,
        )

    @_statsd_it
    def upload_then_gen_link(self, key, data):
        """

        :param key: 上传到OSS的文件名
        :param data: 待上传的内容。
        :return: 签名 URL 以及 URL 过期时间
        """
        result: PutObjectResult = self.bucket.put_object(key, data)
        if not (199 < result.status < 300):
            raise ClientError(f"upload to oss failed with key: {key}")
        link_ttl = config.OSS_LINK_TTL
        url = self.bucket.sign_url("GET", key, link_ttl, slash_safe=True)
        if not url.startswith("https"):
            url = url.replace("http", "https")
        return url, link_ttl + int(time.time())


class ImageOSSClient:
    def __init__(self):
        self.bucket = oss2.Bucket(
            auth=oss2.Auth(config.IMAGE_OSS_KEY_ID, config.IMAGE_OSS_KEY_SECRET),
            endpoint=config.IMAGE_OSS_ENDPOINT,
            bucket_name=config.IMAGE_OSS_BUCKET_NAME,
        )

    @_statsd_it
    def upload_image(self, org_id, image, need_zip=False, origin_name: str | None = None) -> str:
        """
        directory structure: /org_id/xxx.(jpg,jpeg..)
        """
        origin_name = origin_name or image.filename
        if not imghdr.what(image):
            raise InvalidImageError(f"invalid image file {origin_name}")
        __, extname = path.splitext(origin_name)
        if need_zip:
            image = imghdr.compress_image(BytesIO(image.read()), extname)
        data = image.read()
        digest = md5(data).hexdigest()
        headers = dict()
        headers["x-oss-storage-class"] = "Standard"
        headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PUBLIC_READ
        key = "{0}/{1}/{2}".format(config.IMAGE_OSS_FOLDER_NAME, org_id, f"{digest}{extname}")
        result: PutObjectResult = self.bucket.put_object(key, data, headers=headers)
        if not (199 < result.status < 300):
            raise UploadError(f"upload to oss failed with key: {key}")
        return config.IMAGE_OSS_URL_BASE + key


class VideoOSSClient:
    def __init__(self):
        self.bucket = oss2.Bucket(
            auth=oss2.Auth(config.VIDEO_OSS_KEY_ID, config.VIDEO_OSS_KEY_SECRET),
            endpoint=config.VIDEO_OSS_ENDPOINT,
            bucket_name=config.VIDEO_OSS_BUCKET_NAME,
        )

    @_statsd_it
    def upload_video(self, org_id, video, origin_name: str | None = None) -> str:
        """
        directory structure: /org_id/xxx.(wmv...)
        """
        origin_name = origin_name or video.filename
        __, extname = path.splitext(origin_name)
        data = video.read()
        digest = md5(data).hexdigest()
        headers = dict()
        headers["x-oss-storage-class"] = "Standard"
        headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PUBLIC_READ
        key = "{0}/{1}/{2}".format(config.VIDEO_OSS_FOLDER_NAME, org_id, f"{digest}{extname}")
        result: PutObjectResult = self.bucket.put_object(key, data, headers=headers)
        if not (199 < result.status < 300):
            raise UploadError(f"upload to oss failed with key: {key}")
        return config.VIDEO_OSS_URL_BASE + key


class FileOSSClient:
    folder_name = "files"

    def __init__(self):
        self.bucket = oss2.Bucket(
            auth=oss2.Auth(config.OSS_KEY_ID, config.OSS_KEY_SECRET),
            endpoint=config.OSS_ENDPOINT,
            bucket_name=config.OSS_BUCKET_NAME,
        )

    @_statsd_it
    def upload_file(self, org_id, file, origin_name=None):
        origin_name = origin_name or file.filename
        __, extname = path.splitext(origin_name)
        data = file.read()
        digest = md5(data).hexdigest()
        headers = dict()
        headers["x-oss-storage-class"] = "Standard"
        headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PUBLIC_READ
        key = "{0}/{1}/{2}".format(self.folder_name, org_id, f"{digest}{extname}")
        result: PutObjectResult = self.bucket.put_object(key, data, headers=headers)
        if not (199 < result.status < 300):
            raise UploadError(f"upload to oss failed with key: {key}")
        return config.OSS_URL_BASE + quote(key)


class RpaControlOSSClient:
    def __init__(self):
        self.bucket = oss2.Bucket(
            auth=oss2.Auth(config.RPA_CONTROL_OSS_KEY_ID, config.RPA_CONTROL_OSS_KEY_SECRET),
            endpoint=config.RPA_CONTROL_OSS_ENDPOINT,
            bucket_name=config.RPA_CONTROL_OSS_BUCKET_NAME,
        )

    def gen_download_url(self, oss_key, expires: int | None = None):
        if expires is None:
            expires = config.RPA_CONTROL_OSS_LINK_TTL
        return self.bucket.sign_url("GET", oss_key, expires, slash_safe=True)

    def get_object(self, oss_key):
        object_stream = self.bucket.get_object(oss_key)
        if object_stream.status != 200:
            raise ClientError(f"get object from oss failed with key: {oss_key}")
        return object_stream.read()
