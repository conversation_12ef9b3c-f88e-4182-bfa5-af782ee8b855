import time
from datetime import datetime, timedelta
from typing import Optional

from pydantic import parse_obj_as
from result import Result, Ok, Err

from robot_processor.client_mixins import Session
from robot_processor.ext import cache
from robot_processor.utils import unwrap_optional
from robot_metrics import Stats
from .schema import (
    Response,
    SubscribeWorkflowReq,
    SubscribeWorkflowRes,
    UpdateWorkflowStateReq,
    UpdateWorkflowStateRes,
    ExecuteWorkflowReq,
    ExecuteWorkflowRes,
    WorkflowSubscriptionReq,
    WorkflowSubscriptionRes,
    ExecuteAckResponse
)
from rpa.conf import rpa_config as config

_statsd_it = Stats.Client.timer(client_name="rpa-server")


class RpaServerClient:
    session = Session()

    def subscribe_workflow(self, template_id: int, org_id: int) -> Response[SubscribeWorkflowRes]:
        api_path = "/feisuo/workflows"
        res = self.session.post(
            config.RPA_SERVER_ENDPOINT + api_path,
            json=SubscribeWorkflowReq(template_id=template_id, org_id=org_id).dict(),
            timeout=int(config.RPA_SERVER_TIMEOUT),
            headers={"LEYAN-FEISUO-TOKEN": config.RPA_SERVER_TOKEN}
        )
        res.raise_for_status()
        return parse_obj_as(Response[SubscribeWorkflowRes], res.json())

    def enable_workflow(self, workflow_id: int) -> Response[UpdateWorkflowStateRes]:
        api_path = f"/feisuo/workflows/{workflow_id}"
        res = self.session.put(
            config.RPA_SERVER_ENDPOINT + api_path,
            json=UpdateWorkflowStateReq(state="ENABLED").dict(),
            timeout=int(config.RPA_SERVER_TIMEOUT),
            headers={"LEYAN-FEISUO-TOKEN": config.RPA_SERVER_TOKEN}
        )
        res.raise_for_status()
        return parse_obj_as(Response[UpdateWorkflowStateRes], res.json())

    def disable_workflow(self, workflow_id: int) -> Response[UpdateWorkflowStateRes]:
        api_path = f"/feisuo/workflows/{workflow_id}"
        res = self.session.put(
            config.RPA_SERVER_ENDPOINT + api_path,
            json=UpdateWorkflowStateReq(state="DISABLED").dict(),
            timeout=int(config.RPA_SERVER_TIMEOUT),
            headers={"LEYAN-FEISUO-TOKEN": config.RPA_SERVER_TOKEN}
        )
        res.raise_for_status()
        return parse_obj_as(Response[UpdateWorkflowStateRes], res.json())

    def get_workflow_id(self, template_name: str, org_id: int):
        """获取或订阅指定模板的 workflow_id"""
        api_path = "/feisuo/workflows:subscription"
        res = self.session.put(
            config.RPA_SERVER_ENDPOINT + api_path,
            json=WorkflowSubscriptionReq(template_name=template_name, org_id=org_id).dict(),
            timeout=int(config.RPA_SERVER_TIMEOUT),
            headers={"LEYAN-FEISUO-TOKEN": config.RPA_SERVER_TOKEN}
        )
        res.raise_for_status()
        return parse_obj_as(Response[WorkflowSubscriptionRes], res.json())

    def execute_workflow(
        self, workflow_id: int, execution_id: Optional[int], payload: dict
    ) -> Result[Response[ExecuteWorkflowRes], Exception]:
        api_path = f"/feisuo/workflows/{workflow_id}/_execute"
        try:
            res = self.session.put(
                config.RPA_SERVER_ENDPOINT + api_path,
                json=ExecuteWorkflowReq(payload=payload, execution_id=execution_id).dict(),
                timeout=int(config.RPA_SERVER_TIMEOUT),
                headers={"LEYAN-FEISUO-TOKEN": config.RPA_SERVER_TOKEN}
            )
            res.raise_for_status()
        except Exception as e:
            return Err(e)
        return Ok(parse_obj_as(Response[ExecuteWorkflowRes], res.json()))

    @_statsd_it
    def execute_workflow_wait_ack(
        self, workflow_id: int, input_execution_id: int | None, payload: dict, timeout: timedelta | None = None
    ):
        execute_result = self.execute_workflow(workflow_id, input_execution_id, payload)
        if execute_result.is_err():
            return Err(execute_result.unwrap_err())
        execution_id = unwrap_optional(execute_result.unwrap().data).execution_id
        if timeout is None:
            barrier = datetime.now() + config.rpa_client_wait_ack_timeout
        else:
            barrier = datetime.now() + timeout
        ack: dict | None = None
        while datetime.now() < barrier:
            ack = cache.get(self.execution_cache_key(execution_id))
            if ack:
                break
            time.sleep(.3)
        if not ack:
            return Err(TimeoutError(f"ack timeout with execution_id: {execution_id}", execution_id))
        return Ok(ExecuteAckResponse(**ack))

    @staticmethod
    def execution_cache_key(execution_id):
        return f"rpa-execution:{execution_id}"
