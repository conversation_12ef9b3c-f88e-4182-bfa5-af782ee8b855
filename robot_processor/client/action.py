from dataclasses import dataclass
from typing import Optional

from loguru import logger

from robot_metrics import Stats
from robot_processor.client.conf import app_config as config
from robot_processor.client_mixins import Session

_statsd_it = Stats.Client.timer(client_name="action-server")


class ActionClient:
    session = Session()

    @_statsd_it
    def create_action_log_by_http(self, data):
        try:
            headers = {"Authentication-Token": config.ACTION_LOG_AUTH_TOKEN}
            resp = self.session.post(config.ACTION_LOG_ENDPOINT, json=data, headers=headers)
        except BaseException as e:
            logger.error(str(e))
            return False
        try:
            return resp.json().get("success")
        except Exception as e:
            logger.error("nevermore 服务异常: {}", e)
            return False

    def create_action_log_by_kafka(self, data):
        from robot_processor.ext import action_log_producer
        try:
            action_log_producer(data)
        except BaseException as e:
            logger.opt(exception=e).error("create action log via kafka failed.")


@dataclass
class Log:
    sid: str
    org_id: str
    platform: str
    user: str
    model: str
    operator: str
    operate_ts: int
    raw_json: str
    object_id: str
    label: Optional[str]
