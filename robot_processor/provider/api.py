from typing import List

from flask import Blueprint, request
from pydantic import BaseModel, Field

from robot_processor.client import asgard_client
from robot_processor.currents import g
from robot_processor.decorators import shop_required
from robot_processor.enums import ProviderFactoryType
from robot_processor.provider.schema import (
    ProviderQuerySchema,
    ProviderListSchema,
    ProviderUploadSchema,
    ProviderTemplateSchema,
    ProviderDownloadSchema,
    ProviderUploadRespSchema,
    ProviderUploadCheckSchema,
    ProviderDeleteSchema,
    ProviderTitleSchema,
    ProviderTitleBriefSchema,
)
from robot_processor.validator import validate

provider_extra_api = Blueprint("provider-extra-api", __name__)


@provider_extra_api.get('/providers')
@shop_required
@validate
def list_provider_templates() -> ProviderTemplateSchema:
    ret = asgard_client.get_providers(g.shop.sid)
    return ProviderTemplateSchema(**ret)


@provider_extra_api.post('/providers/<provider_id>')
@shop_required
@validate
def list_providers(provider_id, body: ProviderQuerySchema) -> ProviderListSchema:
    query = body.dict()
    ret = asgard_client.query_provider(query, provider_id, g.shop.sid)
    return ProviderListSchema(**ret)


@provider_extra_api.post("/providers/<provider_id>/rows")
@shop_required
@validate
def delete_rows(provider_id, body: ProviderDeleteSchema):
    query = body.dict()
    asgard_client.del_row(query, provider_id, g.shop.sid)
    return {'success': True}


@provider_extra_api.post("/providers/upload")
@shop_required
@validate
def upload_providers(body: ProviderUploadSchema) -> ProviderUploadRespSchema:
    query = body.dict()
    query["update_user"] = g.nick
    ret = asgard_client.upload(query, g.shop.sid)
    if ret:
        return ProviderUploadRespSchema(**ret)
    else:
        return ProviderUploadRespSchema(
            success=False,
            error_msg="系统故障或请求超时"
        )


@provider_extra_api.post("/providers/upload/check")
@shop_required
@validate
def upload_providers_check(query: ProviderUploadSchema) -> ProviderUploadCheckSchema:
    upload_type = query.upload_type
    file = request.files["file"]
    ret = asgard_client.upload_check(upload_type, file, g.shop.sid)
    return ProviderUploadCheckSchema(**ret)


@provider_extra_api.post("/providers/<provider_id>/download")
@shop_required
@validate
def download_providers(provider_id, body: ProviderQuerySchema) -> ProviderDownloadSchema:
    query = body.dict()
    ret = asgard_client.download(query, provider_id, g.shop.sid)
    return ProviderDownloadSchema(**ret)


@provider_extra_api.get("/providers/<provider_id>/titles")
@shop_required
@validate
def titles(provider_id) -> ProviderTitleSchema:
    ret = asgard_client.titles(provider_id, g.shop.sid)
    return ProviderTitleSchema(**ret)


class ListProviderRequest(BaseModel):
    provider_type: ProviderFactoryType = Field(default=ProviderFactoryType.PRODUCT)


class ListProviderResponse(BaseModel):
    titles: List[ProviderTitleBriefSchema]


@provider_extra_api.get('/forms/<form_id>/providers')
@shop_required
@validate
def list_shop_provider_titles(form_id, query: ListProviderRequest) -> ListProviderResponse:
    """查找当前店铺支持的工单数据源.

    NOTE: 这个方法似乎和上面的 list_provider_templates 重复了.
    """
    shop = g.shop
    result = asgard_client.get_providers(shop.sid)
    if not result:
        return ListProviderResponse(titles=[])

    result = asgard_client.Response.GetProviders.parse_obj(result)
    for item in result.data:
        if item.provider_type == query.provider_type:
            provider = item
            title_res = asgard_client.titles(provider.id, shop.sid)
            return ListProviderResponse(titles=title_res.get("titles", []))
    else:
        return ListProviderResponse(titles=[])
