import typing

import sentry_sdk
from sentry_sdk.tracing import Transaction

from flask import Flask, request_started, request, request_finished, Response


@request_started.connect
def force_enable_sentry_transaction(_):
    with sentry_sdk.configure_scope() as scope:
        if request.headers.get('debug-mode', '').lower() != "true":
            return
        transaction: typing.Optional[Transaction] = scope.transaction
        if not transaction:
            return
        transaction.set_context("debug-mode", True)
        if not transaction.sampled:
            transaction.sampled = True
            transaction.init_span_recorder(1000)


@request_finished.connect
def inject_sentry_trace_id(_, response: Response):
    with sentry_sdk.configure_scope() as scope:
        transaction: typing.Optional[Transaction] = scope.transaction
        if transaction and transaction.sampled:
            response.headers['sentry-trace-id'] = transaction.trace_id


def _register_sentry_before_send_transaction_hook():
    from robot_processor.ext import cache
    client = sentry_sdk.Hub.current.client
    if client:
        old_hook_handler = client.options.get('before_send_transaction')

        def hook(event, hint):
            debug_mode = event.get('contexts', {}).get('debug-mode', False)
            trace_id = event.get('contexts', {}).get('trace', {}).get('trace_id')
            if debug_mode and trace_id:
                cache.set(f'sentry_trace:{trace_id}', event, timeout=120)
            if old_hook_handler:
                return old_hook_handler(event, hint)
            else:
                return event

        client.options['before_send_transaction'] = hook


def init_app(app: Flask):
    @app.before_first_request
    def register_sentry_before_send_transaction_hook():
        _register_sentry_before_send_transaction_hook()

    @app.get('/v1/sentry_traces/<trace_id>')
    def retrieve_sentry_trace(trace_id):
        from robot_processor.ext import cache
        return cache.get(f'sentry_trace:{trace_id}') or {}
