"""打款回写event"""
import json
import time
from typing import ClassVar

from loguru import logger
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.alipay.schema import TransferEvent, TransferAction
from robot_processor.business_order.exception_rule.models import ExceptionBusinessOrder
from robot_processor.business_order.models import Job, BusinessOrder
from robot_processor.business_order.tasks import package_jobs
from robot_processor.client import action_client
from robot_processor.enums import BusinessOrderStatus, JobProcessMark
from robot_processor.ext import db
from robot_processor.job.human_job import HumanJobController
from robot_processor.utils import convert_data, check_uuid4


class AbstractTransferAction:
    action: ClassVar[TransferAction]
    process_mark: ClassVar[JobProcessMark]
    log_operator: ClassVar[str]
    success_message: ClassVar[str] = ""
    error_message: ClassVar[str] = ""

    def __init__(self, record: TransferEvent, job: Job):
        self.record = record
        self.job = job
        self.order: BusinessOrder = self.job.business_order

    @classmethod
    def get(cls, record):
        if not record.action:
            return
        if not (record.job_id and record.bo_id):
            return
        if not (job := Job.query.get(record.job_id)):
            return
        for action_class in AbstractTransferAction.__subclasses__():
            if action_class.action.upper() == record.action.upper():
                return action_class(record, job)

    def execute(self, **kwargs):
        raise NotImplementedError

    def done(self) -> tuple[bool, str]:
        self.job.process_mark = self.process_mark
        self.order.set_updator_by_nick(self.record.operator_user or self.job.assignee)
        self.order.updated_at = int(time.time())
        self.order.extra_data.update({
            "operate_action": self.process_mark.name,
            "operate_reason": self.record.operate_reason,
        })
        flag_modified(self.order, "extra_data")
        db.session.add(self.order)
        db.session.commit()
        logger.info(f"Transfer of business order {self.order.id} "
                    f"do {self.record.action} "
                    f"by {self.record.operator_user} "
                    f"for {self.record.operate_reason}")

        # 发送操作日志
        # 操作日志中操作评论comment字段在raw_json中，
        # 因此此处将operate_reason赋值给raw_json.comment
        raw_data = self.order.data
        raw_data["comment"] = self.record.operate_reason
        raw_data["job"] = self.job.raw_step_v2["name"]
        if shop := self.order.shop:
            org_id = shop.org_id or ""
        else:
            org_id = ""
        action_log = {
            "sid": str(self.order.sid),
            "org_id": org_id,
            "platform": "",
            "model": "business_orders",
            "label": self.order.name,
            "object_id": str(self.order.id),
            "operator": self.log_operator,
            "operate_ts": int(time.time()),
            "user": self.record.operator_user or "",
            "raw_json": json.dumps(raw_data, ensure_ascii=True)
        }
        action_client.create_action_log_by_kafka(action_log)
        return True, self.success_message


class TransferRejectAction(AbstractTransferAction):
    """
    打款驳回
    """
    action = TransferAction.REJECT
    process_mark = JobProcessMark.REJECT
    log_operator = "transfer-reject"
    error_message = "驳回失败"

    def execute(self):
        from robot_processor.business_order.job_wrappers.wrapper import set_business_order_status_against_current_job
        from robot_processor.enums import JobStatus, StepType
        from robot_processor.assistant.constants import ASSISTANT_DISABLED

        has_begin_step = False

        for idx, job in enumerate(self.order.all_jobs()):
            if idx == 0 and job.step_type == StepType.begin:
                has_begin_step = True
                continue
            elif (not has_begin_step and idx == 0 and job.step_type == StepType.human) or (
                has_begin_step and idx == 1 and job.step_type == StepType.human
            ):
                # 打款被驳回时，将工单回退到首个工单 job，并将首个人工 job 的状态设置为 PENDING，以提醒负责这个 job 的客服进行处理。
                # 但如果这个 job 上还没有设置执行客服，或者执行客服已经被禁用，则将这个 job 标记为失败状态。
                assign_client = HumanJobController(job)
                self.order.set_current_execute_job(job)
                # 当初始任务的执行客服未找到，或者被禁用、删除时，需要进行以下处理。
                if assign_client.current_assignee and assign_client.current_assignee.is_valid():
                    # 后续处理。
                    job.set_status(JobStatus.PENDING)  # 第一步设置为pending 其他步骤为init
                    job.process_mark = JobProcessMark.REJECT
                    set_business_order_status_against_current_job(job)
                else:
                    Job.Utils.mark_failed(job, ASSISTANT_DISABLED)
            else:
                job.set_status(JobStatus.INIT)
                job.process_mark = JobProcessMark.REJECT
        return self.done()


class TransferEditAction(AbstractTransferAction):
    """
    打款更新  需要更新bo数据
    """
    action = TransferAction.EDIT
    process_mark = JobProcessMark.UPGRADE
    log_operator = "update"
    error_message = "更新失败"

    def execute(self):
        def convert(new, old):
            # 仅针对字符 / 数组做兼容
            if isinstance(old, str) and isinstance(new, list):
                new = ";".join(new)
            if isinstance(old, list) and isinstance(new, str):
                new = new.split(";")
            return new

        job = self.job

        order = job.business_order
        # {"key1": xxx, "key2": "1.0"}
        data = order.data
        # {"tid": "key1", "amount": "key2", "", "comment": "key3"}
        key_map = job.raw_step_v2.get("key_map") or {}

        transfer_info = self.record.transfer_info
        if not transfer_info:
            return False, "缺少必要信息"
        # {"tid": xxx, "amount": "1.0"}
        bo_data = convert_data(job)

        transfer_info.amount = round(float(transfer_info.amount), 2)
        transfer_info_data = transfer_info.dict(exclude_unset=True, exclude_defaults=True)
        # 类似备注/打款理由的组件可能存在 下拉组件/单行文本 所以需要做兼容
        for key, value in transfer_info_data.items():
            if isinstance(value, str) or isinstance(value, list):
                if key in key_map and data.get(key_map[key]):
                    transfer_info_data[key] = convert(value, data[key_map[key]])

        bo_data.update(transfer_info_data)
        data.update(
            {
                value: bo_data[key]
                for key, value in key_map.items()
                # key_map中还可能存在 value不是uuid 的情况。。。。
                if bo_data.get(key) and check_uuid4(str(value))
            }
        )
        order.data = data
        flag_modified(order, "data")
        return self.done()


class TransferCloseAction(AbstractTransferAction):
    """
    打款关闭
    """
    action = TransferAction.CLOSE
    process_mark = JobProcessMark.CLOSE
    log_operator = "close"
    error_message = "关闭失败"

    def execute(self):
        self.job.remove_from_pool()
        self.job.end_timing()
        self.order.set_status(BusinessOrderStatus.CLOSE)
        # FIXME 这里有两个问题：1. 前端传入的 job_id 是错误的；2. 在 robot 中对 job 的操作应该使用统一的入口
        ExceptionBusinessOrder.Utils.remove(self.order.id)
        return self.done()


class TransferRecoverAction(AbstractTransferAction):
    """
    打款重启
    """
    action = TransferAction.RECOVER
    process_mark = JobProcessMark.REOPEN
    log_operator = "reopen"
    error_message = "恢复失败"

    def execute(self):
        self.order.set_status(BusinessOrderStatus.RUNNING)
        package_jobs.send(self.order.id)
        if self.order.current_job:
            self.order.current_job.start_timing()
        return self.done()
