import enum
from typing import Optional, Union, List

from pydantic import BaseModel

from robot_processor.enums import PlanWhenAssignException


class TransferAction(enum.StrEnum):
    EDIT = 'EDIT'
    REJECT = 'REJECT'
    CLOSE = 'CLOSE'
    RECOVER = 'RECOVER'
    PAY = 'PAY'


class TradeInfo(BaseModel):
    tid: str = ''
    oid: Optional[str]


class ReceiveInfo(BaseModel):
    receive_account: str = ""
    # / ** 收款方真实姓名 * /
    receive_name: str = ""
    # / ** 打款方式 * /
    payment_method: Optional[str]
    # / ** 买家昵称 * /
    receive_usernick: Optional[str] = ''


class TransferInfo(BaseModel):
    # /** 打款金额 */
    amount: Union[str, float]
    # /** 打款理由 */
    payment_reason: List[Union[str, List[str]]] = []
    # / ** 打款备注 * /
    comment: str = ""
    # / ** 图片附件 * /
    pic_url: List[Union[str, dict]] = []
    # / ** 订单信息 * /
    tid: List[TradeInfo] = []
    # / ** 收款信息 * /
    receive_info: Optional[ReceiveInfo]


class TransferEvent(BaseModel):
    bo_id: Optional[str]
    job_id: Optional[str]
    action: TransferAction
    transfer_info: Optional[TransferInfo]
    operate_reason: str = ''
    operator_user: str = ''
    job_assistant_type: Optional[str] = None
    job_assistant_id: Optional[str] = None
    assign_account_exception: Optional[PlanWhenAssignException] = PlanWhenAssignException.ENTER_EXCEPTION_POOL
