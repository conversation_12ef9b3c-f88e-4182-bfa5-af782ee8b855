import json
from abc import ABC
from typing import TYPE_CHECKING
from typing import Op<PERSON>
from zipfile import BadZipFile

import arrow
import sqlalchemy as sa
from flask import current_app
from flask import g
from loguru import logger
from result import Err
from result import Ok

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.business_order.business_order_manager import FormVali<PERSON><PERSON>
from robot_processor.business_order.errors import BusinessOrderError
from robot_processor.business_order.errors import BusinessOrderValidateError
from robot_processor.business_order.errors import ComponentValueUniqueRemind
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import Job
from robot_processor.business_order.models import get_form_composer
from robot_processor.business_order.tasks import package_jobs
from robot_processor.client import action_client
from robot_processor.client.conf import app_config
from robot_processor.constants import TIME_ZONE
from robot_processor.enums import Creator
from robot_processor.enums import FromType
from robot_processor.enums import JobProcessMark
from robot_processor.enums import JobStatus
from robot_processor.ext import db
from robot_processor.form.models import Step
from robot_processor.logging import vars as log_vars
from robot_processor.user_task.config import user_task_config
from robot_processor.user_task.helper import user_task
from robot_processor.user_task.model import BatchImportBoTask
from robot_processor.user_task.model import UserTask

from ...utils import unwrap_optional
from .excel import BoDataError


class InvalidDataException(Exception):
    pass


class BusinessOrderState(ABC):
    task: BatchImportBoTask
    name: str
    task_status: UserTask.Status

    def __init__(self, task: BatchImportBoTask):
        self.task = task

    @classmethod
    def from_orm_instance(cls, instance: UserTask) -> "BusinessOrderState":
        task = BatchImportBoTask(instance)
        for clazz in BusinessOrderState.__subclasses__():
            if clazz.name == task.state:
                return clazz(task)
        return PendingState(task)

    def transition(self):
        ...


class PendingState(BusinessOrderState):
    name = "pending"
    task_status = UserTask.Status.Pending

    def do_overall_checks(self) -> str:
        from robot_processor.widget.widgets import filter_step_widgets

        try:
            template = self.task.template
        except (ValueError, BadZipFile, KeyError):
            logger.warning(f"解析文件失败({self.task.id})")
            return "工单数据解析失败，请检查[文件格式]"

        stores = {info.sid for info in template.metadata.system_info.values()}
        if template.metadata.sid:
            stores.add(template.metadata.sid)

        self.task.backfill_form_info(
            form_id=template.metadata.form_id,
            form_name=template.metadata.form_name,
            step_id=template.metadata.step_id,
            step_name=template.metadata.step_name,
            record_count=template.record_count,
            covered_sids=stores,
        )

        if not (self.task.filename.endswith(".xlsx") or self.task.filename.endswith(".xlsm")):
            return "工单数据上传失败，请检查[文件格式]"

        if len(self.task.raw_file) > app_config.USER_TASK_BATCH_UPLOAD_MAX_SIZE:
            return "文件过大，请按照上传页面提示调整文件大小"

        for header, widget in zip(template.widget_headers, template.metadata.widgets):
            if header is None or widget.label not in header:
                return "表头格式错误"

        (step_id,) = (
            db.session.query(sa.func.max(Step.id))
            .where(Step.is_dirty.is_(False), Step.step_uuid == template.metadata.step_uuid)
            .first()
        )

        if step_id != template.metadata.step_id:
            return "工单已更新，请重新下载最新的模板文件"

        widgets_in_db = filter_step_widgets(Step.get_step_widgets(step_id))
        for (label_in_db, key_in_db, _), in_template in zip(widgets_in_db, template.metadata.widgets):
            if key_in_db != in_template.key:
                return "工单已更新，请重新下载最新的模板文件"

        if template.record_count == 0:
            return "文件中无数据"
        limit = user_task_config.get_org_user_task_batch_limit(self.task.req_org_id)
        if template.raw_rows > limit:
            return "当前文件限制最大数据为 {} 行，请调整后重新上传".format(limit)

        if self.task.req_org_id != template.metadata.org_id:
            return "不是当前店铺的模板文件"

        file_digest = template.digest

        query = UserTask.query.filter_by(sid=self.task.req_sid).filter(
            sa.func.json_extract(UserTask.request, '$."key_in_oss"').like(f"%{file_digest}%"),
            UserTask.status.in_(
                (
                    UserTask.Status.Success,
                    UserTask.Status.Running,
                    UserTask.Status.Pending,
                    UserTask.Status.WaitConfirm,
                    UserTask.Status.PartialFailed,
                )
            ),
            UserTask.id != self.task.id,
        )
        if bool(query.first()):
            return "当前文件已经导入过，不可重复导入"
        return ""

    def do_per_row_checks(self):
        template = self.task.template
        failure = self.task.failure_items.copy()
        unique_remain_failure = self.task.unique_remain_failure_items.copy()
        bo_data_list = template.get_data_for_bo_create()
        for bo_data in bo_data_list:
            if isinstance(bo_data, BoDataError):
                failure.append(BatchImportBoTask.FailureItem(row=bo_data.row, reason=bo_data.error))
                continue

            form_composer = get_form_composer(int(bo_data.form_version_id))
            step = unwrap_optional(db.session.get(Step, bo_data.step_id))
            form_validator = FormValidator.init_form_validator(bo_data.shop, form_composer, step)
            check_result = form_validator.validate(bo_data.data)
            match check_result:
                case Ok():
                    pass
                case Err(BusinessOrderValidateError() as err):
                    match form_validator.process_errors_only_remind(err):
                        case Err(validate_error):
                            if TYPE_CHECKING:
                                validate_error: BusinessOrderValidateError  # type: ignore
                            unique_remain_failure.append(
                                BatchImportBoTask.FailureItem(
                                    row=bo_data.row,
                                    reason=validate_error.to_log(without_extra=True),
                                )
                            )
                    match form_validator.process_errors_for_create(err):
                        case Err(validate_error):
                            if TYPE_CHECKING:
                                validate_error: BusinessOrderValidateError  # type: ignore
                            failure.append(
                                BatchImportBoTask.FailureItem(
                                    row=bo_data.row,
                                    reason=validate_error.to_log(without_extra=True),
                                )
                            )
                case Err(BusinessOrderError() as err):
                    failure.append(BatchImportBoTask.FailureItem(row=bo_data.row, reason=str(err)))
            match form_validator.component_value_unique_check(data=bo_data.data):
                case Err(ComponentValueUniqueRemind() as err):
                    if TYPE_CHECKING:
                        err: ComponentValueUniqueRemind  # type: ignore
                    widget_option_value = {widget_key: option_value for _, widget_key, option_value in bo_data.widgets}
                    reason_list = []
                    for check_key in err.widgets:
                        widget_options = widget_option_value[check_key]
                        reason_list.append(f"{widget_options['label']} 唯一性校验失败")
                    unique_remain_failure.append(
                        BatchImportBoTask.FailureItem(row=bo_data.row, reason=", ".join(reason_list))
                    )
        return failure, unique_remain_failure

    def transition(self):
        failed_reason = self.do_overall_checks()

        if failed_reason:
            self.task.set_state(FailedState.name, FailedState.task_status)
            self.task.set_fail_reason(failed_reason)
            self.task.set_finished_at(arrow.get().int_timestamp)
            db.session.commit()
        else:
            failure, unique_remain_failure = self.do_per_row_checks()
            next_state: type[BusinessOrderState]
            if unique_remain_failure:
                next_state = WaitConfirmState
            else:
                next_state = CreateState
            self.task.set_state(next_state.name, next_state.task_status)
            self.task.set_failure_items(failure)
            self.task.set_unique_remain_failure_items(unique_remain_failure)
            db.session.commit()
            start_user_task.send(self.task.id)


class CreateState(BusinessOrderState):
    name = "create"
    task_status = UserTask.Status.Running

    def transition(self):
        batch_size = int(current_app.config.get("USER_TASK_BATCH_SIZE", 10))
        bo_data_list = self.task.template.get_data_for_bo_create()

        for i in range(batch_size):
            try:
                # 创建一个工单然后将任务放回队列 等待下一次重新执行在大文件时，会大幅提高读文件的次数
                # 根据 Apollo 配置决定一次处理多少笔工单。
                if self.task.current_index >= len(bo_data_list):
                    logger.info("所有数据已完成导入")
                    break
                bo_data = bo_data_list[self.task.current_index]
                validate_failures = [item["reason"] for item in self.task.failure_items if item["row"] == bo_data.row]
                if validate_failures:
                    raise InvalidDataException("第 {} 行数据验证有问题: {}".format(bo_data.row, validate_failures))
                if isinstance(bo_data, BoDataError):
                    raise InvalidDataException("第 {} 行数据有问题: {}".format(bo_data.row, bo_data.error))
                try:
                    # 创建工单。
                    with db.session.no_autoflush:
                        self._create_bo(bo_data)
                except Exception as e:
                    # 创建失败则记录下信息，并回滚。
                    db.session.rollback()
                    raise Exception("第 {} 行创建工单失败, 行数据: {}, 异常: {}".format(bo_data.row, bo_data.data, e))
            except InvalidDataException as e:
                # 数据验证的异常，将其等级降至 warning。
                logger.warning(e)
            except Exception as e:
                # 记录工单创建的异常。
                logger.exception(e)
            finally:
                self.task.inc_current_index(1)
                continue
        else:
            if self.task.current_index < self.task.record_count:
                logger.info("完成了一批导入，继续下一批导入")
                db.session.commit()
                start_user_task.send_with_options(
                    args=(self.task.id,),
                )
                return

        next_state: type[BusinessOrderState]
        if len(self.task.created_business_order_ids) == self.task.record_count:
            next_state = FinishedState
        elif len(self.task.created_business_order_ids) == 0 and len(self.task.failure_items) == 0:
            # 仅 PartialFailedState 时允许导出失败的明细
            # 因此 Failed 状态被视为导入文件校验失败，PartialFailed 状态被视为导入数据行存在问题
            next_state = FailedState
        else:
            next_state = PartialFailedState

        self.task.set_state(next_state.name, next_state.task_status)
        self.task.inc_current_index(-1)
        self.task.set_finished_at(arrow.get().int_timestamp)
        self.task.set_statistic()
        db.session.commit()

    def _create_bo(self, bo_data):
        """
        只有批量导入创建工单使用
        """
        self.task.set_lock()
        creator = AccountDetailV2(
            user_type=Creator(self.task.user_info["user_type"]),
            user_id=self.task.user_info["user_id"],
            user_nick=self.task.user_info["user_nick"],
        )

        update_attr = {
            "form_id": bo_data.form.id,
            "sid": bo_data.shop.sid,
            "aid": creator.user_nick,
            "data": bo_data.data,
            "from_type": FromType.USER_TASK,
            "form_version_id": bo_data.form_version_id,
        }

        bo = BusinessOrder()
        db.session.add(bo)
        for attr, value in update_attr.items():
            setattr(bo, attr, value)
        bo.set_creator_info(creator)
        bo.set_updator(creator)
        BusinessManager.merge_tid_oid_uid_from_data(bo)
        # 尝试从 “买家昵称" widget 中提取出 uid
        if not bo.uid and bo_data.uid:
            bo.uid = bo_data.uid
        db.session.flush()
        action_client.create_action_log_by_kafka(
            dict(
                org_id=bo_data.shop.org_id,
                sid=bo.sid,
                user=bo.aid or "",
                platform="",
                label=bo.name,
                operator="create",
                model="business_orders",
                object_id=str(bo.id),
                operate_ts=arrow.now(TIME_ZONE).int_timestamp,
                raw_json=json.dumps(update_attr, ensure_ascii=True),
            )
        )

        # 为新工单创建首个 job
        begin_job = bo.init_bo_jobs()
        assert begin_job is not None
        bo.set_current_execute_job(begin_job)
        db.session.flush()

        # 更新状态为保存
        begin_job.set_status(JobStatus.PENDING)
        begin_job.process_mark = JobProcessMark.SAVE
        begin_job.set_assignee_assistant(creator)
        begin_job.start_timing()

        self.task.add_business_order_id(bo.id)
        db.session.commit()
        logger.bind(business_order_id=bo.id).info("已创建工单: row={}, data={}", bo_data.row, bo_data.data)
        operate_reason = "批量导入"

        log_vars.Job.set(begin_job)

        db.session.refresh(begin_job, with_for_update=True)
        db.session.refresh(bo, with_for_update=True)

        # 起始步骤在批量导入时需要执行
        begin_step = begin_job.step
        if begin_step is not None and begin_step.is_begin():
            from robot_processor.job.begin_job import BeginJobController

            BeginJobController(begin_job).run_job()
        begin_job.set_assignee_assistant(creator)
        begin_job.set_status(JobStatus.SUCCEED)
        begin_job.process_mark = JobProcessMark.ACCEPT
        logger.info("将批量创建工单的第一个任务状态设为成功")

        bo_extra_data = bo.extra_data.copy()
        bo_extra_data.update({"operate_action": "ACCEPT", "operate_reason": operate_reason})
        bo.extra_data = bo_extra_data

        # 下一步分派执行客服
        next_job = begin_job.next
        if next_job:
            if next_job.need_manual_assign():
                Job.Utils.mark_failed(next_job, "需要手动分派执行客服")
                logger.info("已将下一步任务标记为失败, 需要手动分派执行客服")
            else:
                logger.info("下一步任务不需要手动分派执行客服")
        else:
            logger.info("工单没有下一步任务")

        begin_job.end_timing()
        db.session.commit()
        # todo: 确认是否需要追加 job_transition？
        package_jobs.send_with_options(args=(bo.id, begin_job.id))
        return bo


class FailedState(BusinessOrderState):
    name = "failed"
    task_status = UserTask.Status.Failed


class FinishedState(BusinessOrderState):
    name = "finished"
    task_status = UserTask.Status.Success


class PartialFailedState(BusinessOrderState):
    name = "partial_failed"
    task_status = UserTask.Status.PartialFailed


class WaitConfirmState(BusinessOrderState):
    name = "wait_confirm"
    task_status = UserTask.Status.WaitConfirm

    def confirm(self, ignore_duplication_error: bool):
        self.task.set_state(CreateState.name, CreateState.task_status)
        if not ignore_duplication_error:
            failure_items = self.task.failure_items.copy()
            failure_items.extend(self.task.unique_remain_failure_items)
            self.task.set_failure_items(failure_items)
        db.session.commit()
        start_user_task.send(self.task.id)


@user_task.actor
def start_user_task(task_id: int):
    with logger.contextualize(k=str(task_id), actor_name="batch_import_bo"):
        instant: Optional[UserTask] = UserTask.query.get(task_id)
        if instant is None:
            logger.error(f"[UserTask] start async failed for {task_id}. not found")
            return

        task = BatchImportBoTask(instant)
        logger.info(f"[UserTask] start async for {task_id} in {task.state}")
        state = BusinessOrderState.from_orm_instance(instant)

        if task.get_lock():
            return
        task.set_lock()
        # HACK: 方便 widgets.py 中的 class TracingRecord 读取当前批量导入任务的发起人用户信息
        g.cur_batch_import_bo_task = task
        try:
            state.transition()
        except Exception as e:
            logger.exception(f"[BusinessOrderContext] Task Failed: {str(e)}")
            task.set_state(FailedState.name, FailedState.task_status)
            task.set_fail_reason("系统异常")
            db.session.commit()
        task.clear_lock()
