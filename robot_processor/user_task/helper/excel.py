from base64 import b64decode
from base64 import b64encode
from dataclasses import asdict
from dataclasses import dataclass
from dataclasses import field
from functools import cached_property
from hashlib import md5
from io import BytesIO
from json import dumps
from json import loads
from typing import Final
from typing import List
from typing import Optional
from typing import Set
from typing import cast

import xlsxwriter
from flask import current_app
from loguru import logger
from more_itertools import first
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter

from robot_processor.business_order.models import BusinessOrder
from robot_processor.constants import PLATFORM_MAP
from robot_processor.error.validate import ValidateError
from robot_processor.ext import db
from robot_processor.form.api.widget_schema import StepWidget
from robot_processor.form.api.widget_schema import WidgetKey
from robot_processor.form.api.widget_schema import WidgetOptionLabel
from robot_processor.form.models import Form
from robot_processor.form.models import Step
from robot_processor.shop.models import Shop
from robot_processor.utils import unwrap_optional
from robot_processor.widget.widgets import cast_and_validate_bo_raw_inputs
from robot_processor.widget.widgets import filter_step_widgets
from robot_processor.widget.widgets import get_widget_class

DATA_SHEET: Final = "批量上传数据"
META_SHEET: Final = "metadata(勿动)"
META_CELL = (0, 0)  # A1 左上角第一个 cell
HEADER_ROW = 1
HEADER_COL_BEGIN = 1
DEMO_DATA_ROW_BEGIN = 2
DATA_ROW_BEGIN = 3
DATA_COL_BEGIN = 1
OVERALL_DESCRIPTION = """导入说明：
1.第二行表单字段不支持修改，修改后导入数据与工单模板数据不一致，影响导入结果
2.表单字段下一行为示例数据，请勿删除
3.标题带*红色字段表示必填，未填写会导入失败
4.格式不符的行数据在全部数据导入后，提示部分导入失败，不影响其他行数据导入
5.批量导入表单无自动同步能力，需手动填写
6.若进行跨平台店铺批量导入，表单中包含平台、店铺字段，请勿删除"""


@dataclass
class DataCell:
    content: str | int | float
    format: Optional[dict] = field(default_factory=dict)


@dataclass
class HeaderCell:
    content: str | int | float
    format: Optional[dict] = field(default_factory=dict)
    data_validation: Optional[dict] = field(
        default_factory=dict
    )  # 所在列的 data validation


Cell = str | int | float | DataCell | HeaderCell | None


@dataclass(unsafe_hash=True, frozen=True)
class RowMeta:
    form_id: int
    step_id: int
    sid: str


@dataclass
class ExcelMetadata:
    @dataclass
    class WidgetInfo:
        key: WidgetKey
        label: WidgetOptionLabel

    org_id: int
    sid: str
    form_id: int
    form_name: str
    step_id: int
    step_name: str
    step_uuid: str
    widgets: List[WidgetInfo]
    system_info: dict[str, RowMeta] = field(
        default_factory=dict
    )  # 跨店时 title:platform -> form_id的映射
    is_multi_shop: bool = False

    def b64encode(self) -> str:
        return b64encode(dumps(asdict(self)).encode("utf8")).decode("utf8")

    @classmethod
    def b64decode(cls, s) -> "ExcelMetadata":
        data = loads(b64decode(s))
        widgets = data.pop("widgets", [])
        data["widgets"] = [cls.WidgetInfo(**widget) for widget in widgets]
        system_info = data.pop("system_info", {})
        data["system_info"] = {k: RowMeta(**v) for k, v in system_info.items()}
        if "org_id" not in data:
            shop = Shop.query.filter_by(sid=data["sid"]).first()
            data["org_id"] = int(shop.org_id) if shop else None  # type: ignore[arg-type]

        return ExcelMetadata(**data)

    def as_excel_cell(self) -> Cell:
        return DataCell(self.b64encode(), format={"color": "white"})

    @cached_property
    def platforms(self) -> Set[str]:
        return (
            {key.split(":")[0] for key in self.system_info}
            if self.system_info
            else set()
        )

    @cached_property
    def stores(self) -> Set[str]:
        return (
            {key.split(":")[1] for key in self.system_info}
            if self.system_info
            else set()
        )


Row = List[Cell]


@dataclass
class Template:
    """
    用于生成 excel 模板的数据结构.

    :param widget_headers: 各个 widget 对应的表头信息
    :param demo_data: 示例数据
    :param metadata: 店铺,form_id, widget_id 等元数据
    """

    widget_headers: List[HeaderCell]
    demo_data: List[DataCell]
    metadata: ExcelMetadata

    @classmethod
    def export_form_to_excel_template(
        cls, form_name: str, form_ids: List[int]
    ) -> "Template":
        """
        为指定的 form 生成 excel 模板.

        :param form_name: form 名称
        :param form_ids: form id 列表，这些 form 的第一步的 widget 列表应该一致
        :return: excel 模板, 包含表头, 示例数据, metadata。可以通过 serialize_to_excel 方法将其序列化成 excel 文件
        """
        forms = Form.query.filter(Form.id.in_(form_ids), Form.name == form_name).all()
        if not forms:
            raise ValidateError("工单未找到", code=404)
        form_step = []
        widget_labels = set()
        for form in forms:
            if not (step := form.get_startup_step()):
                raise ValidateError("工单模板未配置，无法下载模板", code=404)
            widget_labels.add(step.get_widget_labels())
            form_step.append((form, step))
        if len(widget_labels) > 1:
            logger.warning(
                "各 form 第一步的 widget 不一致: widget_labels={}", widget_labels
            )
            raise ValidateError("工单模板第一步不一致，无法导出", code=400)
        store_to_form = {
            f"{PLATFORM_MAP.get(shop.platform.upper())}:{shop.title}": RowMeta(
                form.id, step.id, shop.sid
            )
            for form, step in form_step
            for shop in form.shops
        }

        form, step = first(form_step)
        widgets = []
        step_widgets = filter_step_widgets(Step.get_step_widgets(step.id))
        for widget_label, widget_key, widget_option in step_widgets:
            widget_class = get_widget_class(widget_label)
            widget = widget_class(
                widget_key=widget_key, widget_label=widget_label, options=widget_option
            )
            widgets.append(widget)

        with db.session.no_autoflush:
            recent_bo = (
                BusinessOrder.query.filter(BusinessOrder.form_id == form.id)
                .order_by(BusinessOrder.created_at.desc())
                .first()
            )
            if recent_bo:
                demo_data = recent_bo.data.copy()
            else:
                demo_data = current_app.config.get("WIDGET_DEMO_DATA", {}).copy()

        shop = first(form.subscribed_shops)
        excel_metadata = ExcelMetadata(
            org_id=int(shop.org_id),  # type: ignore[arg-type]
            sid=shop.sid,
            form_id=form.id,
            form_name=form.name,  # type: ignore[arg-type]
            step_id=step.id,
            step_name=unwrap_optional(step.name),
            step_uuid=step.step_uuid,
            widgets=[
                ExcelMetadata.WidgetInfo(
                    key=widget.widget_key, label=widget.options["label"]
                )
                for widget in widgets
            ],
            system_info=store_to_form,
            is_multi_shop=bool(store_to_form),
        )

        widget_header_cells = []
        demo_data_cells = []
        for w in widgets:
            widget_header_cells.append(
                HeaderCell(
                    content=w.get_header_title(),
                    format=None if w.is_optional() else {"font_color": "red"},
                    data_validation=w.get_data_validation(),
                )
            )

            demo_value = demo_data.get(w.widget_key, None)
            demo_data_cells.append(
                DataCell(
                    content=w.serialize_data_to_str(demo_value),
                    format=w.get_data_format(),
                )
            )

        return Template(
            widget_headers=widget_header_cells,
            demo_data=demo_data_cells,
            metadata=excel_metadata,
        )

    def serialize_to_excel(self) -> BytesIO:
        """
        将模板数据序列化成 excel 文件.

        这个方法不应该依赖 self.widgets/self.demo_data/self.metadata 之外的数据，特别是不要在这个方法中访问数据库.

        excel 文件中将包含两个 sheet:
        - DATA_SHEET: 用于存放填写说明，数据表头，示例数据
        - META_SHEET: 用于存放 metadata，data_sheet 中某些列的候选项

        data sheet 的布局大致如下：

        | 导入说明        |       |        |        |           |        |        |
        |               | * 平台 | * 店铺  |   字段1 |  字段2    |  字段3  |        |
        | 示例数据        |       |        |   数据1 | 数据2     |  数据3  |        |
        | 此行开始填写内容 |       |        |         |          |        |        |

        metadata sheet 的布局大致如下：

        | base64 编码的 metadata |       |        |        |        |        |        |
        | 平台 | 平台候选1 | 平台候选2 | 平台候选3 | 平台候选4 | 平台候选5 | 平台候选6 | 平台候选7 |
        | 店铺 | 店铺候选1 | 店铺候选2 | 店铺候选3 | 店铺候选4 | 店铺候选5 | 店铺候选6 | 店铺候选7 |
        | 字段3 | 字段3候选1 | 字段3候选2 | 字段3候选3 | 字段3候选4 | 字段3候选5 | 字段3候选6 | 字段3候选7 |
        :return: excel 文件
        """
        file = BytesIO()
        workbook = xlsxwriter.Workbook(file)
        data_sheet = workbook.add_worksheet(DATA_SHEET)
        meta_sheet = workbook.add_worksheet(
            META_SHEET
        )  # metadata sheet 中的前十行用于写入固定格式的数据，第十一行开始写入各widget的候选项

        def write_headers(row_index: int) -> int:
            source_row_index = 1  # 从 metadata sheet 的第 2 行开始写入候选项
            headers: list[None | HeaderCell] = [None]  # header 行首列置空

            platforms, stores = self.metadata.platforms, self.metadata.stores
            if len(stores) > 1:  # 这是一个跨店铺的模板
                if len(platforms) > 1:  # 这是一个跨平台的模板
                    headers.append(
                        HeaderCell(
                            content="*平台",
                            format={"font_color": "red"},
                            data_validation={
                                "validate": "list",
                                "source": list(platforms),
                            },
                        )
                    )
                headers.append(
                    HeaderCell(
                        content="*店铺",
                        format={"font_color": "red"},
                        data_validation={"validate": "list", "source": list(stores)},
                    )
                )

            headers.extend(self.widget_headers)

            for col_index, cell in enumerate(headers):
                if not isinstance(cell, HeaderCell):
                    continue

                _format = workbook.add_format(cell.format) if cell.format else None
                data_sheet.write(row_index, col_index, cell.content, _format)

                if not cell.data_validation:
                    continue
                validation = cell.data_validation.copy()
                if "list" == validation.get("validate") and "source" in validation:
                    # 这是一个带候选项的列，我们把候选项写到 metadata sheet 的某一行 source_row 中
                    # 并将这一列的 validate source 设置为对 source_row 的索引
                    options = validation["source"]
                    meta_sheet.write(source_row_index, 0, cell.content)
                    for option_idx, option in enumerate(options):
                        meta_sheet.write(source_row_index, option_idx + 1, option)
                    option_ref = f"='{META_SHEET}'!${get_column_letter(2)}${source_row_index+1}:${get_column_letter(len(options) + 1)}${source_row_index+1}"  # noqa
                    validation["source"] = option_ref
                    source_row_index += 1

                # 对整列数据做校验，所以 first_row 是 1, last_row 是一个很大的值, first_col 和 last_col 都是当前列
                data_sheet.data_validation(
                    first_row=1,
                    last_row=1048575,
                    first_col=col_index,
                    last_col=col_index,
                    options=validation,
                )
            return len(headers)

        def write_cells(sheet, row_index, *cells):
            for col_index, cell in enumerate(cells):
                if cell is None:
                    continue
                elif isinstance(cell, (str, int, float)):
                    sheet.write(row_index, col_index, cell)
                elif isinstance(cell, DataCell):
                    _format = workbook.add_format(cell.format) if cell.format else None
                    sheet.write(row_index, col_index, cell.content, _format)
                else:
                    logger.warning("不支持的 cell 类型: {} {}", type(cell), cell)

        write_cells(meta_sheet, 0, self.metadata.as_excel_cell())
        write_cells(data_sheet, 0, OVERALL_DESCRIPTION)
        header_row_length = write_headers(1)
        leave_empty_cols = header_row_length - len(self.widget_headers) - 1
        demo_data_row = (
            [DataCell("示例数据", format={"font_color": "red"})]
            + [None] * leave_empty_cols
            + self.demo_data
        )
        write_cells(data_sheet, 2, *demo_data_row)
        write_cells(
            data_sheet,
            3,
            DataCell(content="此行开始填写内容", format={"font_color": "red"}),
        )
        workbook.add_vba_project("multiSelect.bin")
        workbook.close()
        file.seek(0)
        return file


@dataclass
class BoData:
    """用于创建 business order 的数据结构.该 class 中包含的所有信息均来自于 excel 文件."""

    row: int  # excel 中的行号，从 4 开始, 前三行总是固定为： 说明，widgets，示例数据
    row_meta: Optional[
        RowMeta
    ]  # excel 中一行的元数据，包括 form_id, step_id, sid, 如果为 None 则表示该行没有正确填写平台/店铺
    data: Optional[
        list
    ]  # excel 中一行的原始数据，包括 平台/店铺名和 widget 数据，如果为 None 则表示这是一个空行
    widget_keys: Optional[
        list
    ]  # widget 的 key 列表，如果为 None 则表示需要通过 row_meta 中的 form_id 和 step_id 从数据库获取


@dataclass
class BoDataForCreate:
    """用于创建 business order 的数据结构.与 BoData 不同的是，这个 class 中包含的信息有些不是直接提取自 excel 文件，而是从数据库中获取到的."""

    row: int  # excel 中的行号，从 4 开始, 前三行总是固定为： 说明，widgets，示例数据
    form: Form
    form_version_id: str
    shop: Shop
    uid: Optional[str]  # 买家昵称对应 widget 的值
    widgets: list[StepWidget]
    data: dict  # widget_key -> widget_value 的映射
    step_id: int


@dataclass
class BoDataError:
    row: int  # excel 中的行号
    error: str  # 错误信息


@dataclass
class TemplateDataReader:
    raw_rows: int  # excel 文件的最大行数(由 openpyxl 给出)
    digest: str  # excel 文件的 md5 值
    metadata: ExcelMetadata
    widget_headers: List[str]
    data: List[BoData] = field(default_factory=list)

    @classmethod
    def deserialize_from_excel(cls, input_raw: bytes) -> "TemplateDataReader":
        workbook = load_workbook(BytesIO(input_raw))
        data_sheet = workbook[DATA_SHEET]
        meta_sheet = workbook[META_SHEET]
        metadata = ExcelMetadata.b64decode(meta_sheet["A1"].value)
        if len(metadata.platforms) > 1 and len(metadata.stores) > 1:
            system_col_count = (
                3  # 跨平台 跨店, 每行布局为：空列，平台，店铺, 数据1，数据2...
            )
        elif len(metadata.platforms) == 1 and len(metadata.stores) > 1:
            system_col_count = 2  # 同平台 跨店, 每行布局为：空列，店铺, 数据1，数据2...
        else:
            system_col_count = 1  # 同平台 同店, 每行布局为: 空列, 数据1，数据2...

        headers_row = data_sheet[HEADER_ROW + 1]  # 1-based index
        widget_headers = [
            cast(str, cell.value) for cell in headers_row[system_col_count:]
        ]
        # 去除尾部空列
        while widget_headers and not widget_headers[-1]:
            widget_headers.pop()

        def parse_row_data(row_idx, col_values) -> BoData:
            if system_col_count == 3:
                # 跨平台 跨店
                platform, store, *row_data = col_values
                row_meta = metadata.system_info.get(f"{platform}:{store}")
                if row_meta is None:
                    logger.warning(
                        "跨平台跨店模板中，没有设置正确的 platform 和 store: row={}",
                        col_values,
                    )
                return BoData(
                    row=row_idx, row_meta=row_meta, data=row_data, widget_keys=None
                )
            elif system_col_count == 2:
                # 同平台 跨店
                store, *row_data = col_values
                row_meta = metadata.system_info.get(
                    f"{next(iter(metadata.platforms))}:{store}"
                )
                if row_meta is None:
                    logger.warning(
                        "跨店模板中，没有设置争取的 platform 和 store: row={}",
                        col_values,
                    )
                return BoData(
                    row=row_idx, row_meta=row_meta, data=row_data, widget_keys=None
                )
            else:
                # 同平台 同店
                row_meta = RowMeta(
                    form_id=metadata.form_id, step_id=metadata.step_id, sid=metadata.sid
                )
                widget_keys = [w.key for w in metadata.widgets]
                return BoData(
                    row=row_idx,
                    row_meta=row_meta,
                    data=col_values,
                    widget_keys=widget_keys,
                )

        row_data_list = []
        continuous_empty_row_count = 0
        row_idx = DATA_ROW_BEGIN  # 用于记录当前行号
        for row in data_sheet.iter_rows(
            min_row=DATA_ROW_BEGIN + 1
        ):  # excel 行数从 1 开始计数，所以这里做一次 +1
            col_values = [
                c.value for c in row[1 : system_col_count + len(widget_headers)]
            ]
            row_idx += 1
            bo_data = parse_row_data(row_idx, col_values)
            if not col_values or all(v is None for v in col_values):
                continuous_empty_row_count += 1
                if continuous_empty_row_count > 10:
                    logger.warning("连续10行为空，停止读取")
                    break
                else:
                    row_data_list.append(
                        BoData(row=row_idx, row_meta=None, data=None, widget_keys=None)
                    )
            else:
                row_data_list.append(bo_data)
                continuous_empty_row_count = 0
        # 去除尾部的连续空行
        while row_data_list and row_data_list[-1].data is None:
            row_data_list.pop()
        digest = md5(input_raw).hexdigest()
        return TemplateDataReader(
            raw_rows=data_sheet.max_row,
            digest=digest,
            widget_headers=widget_headers,
            metadata=metadata,
            data=row_data_list,
        )

    @cached_property
    def record_count(self) -> int:
        return len(self.data)

    def get_data_for_bo_create(self) -> list[BoDataForCreate | BoDataError]:
        """
        将 excel 中的信息转换成可以用于直接创建 business order 的数据.

        :return: 是一个列表，列表行数和 self.data 相同，也和原始 excel 文件中的数据行数相同。
                 列表中每一项对应 excel 中的一个数据行，值可能是 dict 或者 str。
                 如果是 dict，表示转换成功，可以用来创建 business order；
                 如果是 str，表示转换失败，str 中包含错误信息。
        """
        widgets_cache: dict[
            RowMeta, tuple[Form | None, Shop | None, list[StepWidget]]
        ] = {}
        result: list[BoDataError | BoDataForCreate] = []
        for item in self.data:
            if item.data is None:
                result.append(BoDataError(row=item.row, error="空行"))
                continue
            if item.row_meta is None:
                result.append(
                    BoDataError(row=item.row, error="平台和店铺名称在飞梭后台匹配失败")
                )
                continue
            if item.row_meta in widgets_cache:
                form, shop, widgets = widgets_cache[item.row_meta]
            else:
                form = Form.query.get(item.row_meta.form_id)
                shop = Shop.Queries.optimal_shop_by_sid(item.row_meta.sid)
                widgets = filter_step_widgets(
                    Step.get_step_widgets(item.row_meta.step_id)
                )
                widgets_cache[item.row_meta] = (form, shop, widgets)

            if form is None:
                result.append(BoDataError(row=item.row, error="表单不存在"))
                continue
            if shop is None:
                result.append(BoDataError(row=item.row, error="店铺信息不存在"))
                continue
            form_wrapper = form.wraps(shop)
            if not form_wrapper.enabled:
                result.append(BoDataError(row=item.row, error="表单未启用"))
                continue
            widget_keys = [key for _, key, _ in widgets]
            bo_data, error = cast_and_validate_bo_raw_inputs(
                widgets, {key: value for key, value in zip(widget_keys, item.data)}
            )
            if error:
                result.append(BoDataError(row=item.row, error=error))
                continue
            user_nick_widget = next(
                (key for label, key, _ in widgets if label == "买家昵称"), None
            )
            if user_nick_widget:
                uid = bo_data[user_nick_widget]
            else:
                uid = None
            form_version = unwrap_optional(form.versions.first())
            step_id = form_version.get_first_step()
            result.append(
                BoDataForCreate(
                    row=item.row,
                    form=form,
                    form_version_id=str(form_version.id),
                    shop=shop,
                    uid=uid,
                    widgets=widgets,
                    data=bo_data,
                    step_id=step_id,
                )
            )
        return result


def create_failure_template(raw_file: bytes, proxy_row_reason_map: dict) -> BytesIO:
    file = BytesIO()
    workbook = load_workbook(BytesIO(raw_file))
    data_sheet = workbook[DATA_SHEET]
    reason_col_index = data_sheet.max_column + 1
    data_sheet.insert_cols(reason_col_index)
    for row in range(data_sheet.max_row, DATA_ROW_BEGIN, -1):
        if row not in proxy_row_reason_map:
            data_sheet.delete_rows(row)
        else:
            reason = proxy_row_reason_map[row]
            data_sheet.cell(row, reason_col_index, reason)
    workbook.save(file)
    file.seek(0)
    return file
