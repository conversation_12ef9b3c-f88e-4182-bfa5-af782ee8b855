from typing import List

from dramatiq.brokers.redis import Redis<PERSON>roker
from flask import Flask

from robot_processor.client.conf import app_config as config
from robot_processor.dramatiq import AppContextMiddleware
from robot_processor.dramatiq import LazyActor


class UserTask:
    queue_name = "USER_TASK"

    def __init__(self):
        self._actors: List[LazyActor] = list()
        self._broker = None

    def actor(self, func):
        lazy_actor = LazyActor(func, {'queue_name': self.queue_name})
        self._actors.append(lazy_actor)
        return lazy_actor

    def init(self, app: Flask):
        from robot_processor.user_task.helper import bo_context # noqa

        self._broker = RedisBroker(url=config.USER_TASK_URL, middleware=[AppContextMiddleware(app)])

        for actor in self._actors:
            actor.register(self._broker)

    def run(self):
        from dramatiq import Worker
        assert self._broker
        worker = Worker(self._broker)
        worker.start()

    def retry(self):
        import time
        from loguru import logger
        from sqlalchemy.sql.operators import in_op
        from robot_processor.ext import db
        from robot_processor.user_task import model
        from robot_processor.user_task.helper import bo_context

        while True:
            time.sleep(300)

            with db.session.begin():
                current_processed_task_id = db.session.query(
                    model.UserTask.id
                ).filter(
                    model.UserTask.status.in_([
                        model.UserTask.Status.PartialFailed,
                        model.UserTask.Status.Success,
                        model.UserTask.Status.Failed,
                    ]),
                    model.UserTask.origin_id.is_(None)  # 只执行原始任务
                ).order_by(model.UserTask.id.desc()).limit(1).scalar()
                if not current_processed_task_id:
                    continue
                logger.info(f"current processed task id: {current_processed_task_id}")
                tasks: List[model.UserTask] = model.UserTask.query.filter(
                    model.UserTask.id < current_processed_task_id,
                    in_op(
                        model.UserTask.status,
                        [
                            model.UserTask.Status.Pending,
                            model.UserTask.Status.Running
                        ]
                    ),
                    model.UserTask.origin_id.is_(None)  # 只执行原始任务
                ).all()

                for task in tasks:
                    if task.method == model.UserTask.Method.BatchImport:
                        try:
                            bo_context.start_user_task.send(task.id)
                        except:  # noqa
                            logger.exception("BusinessOrderCreate start async failed")
                    else:
                        logger.warning(f'不支持的 task method {task.method}')


user_task = UserTask()
