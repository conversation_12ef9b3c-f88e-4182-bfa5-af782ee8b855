import enum

#   // 自动重试状态有下面几种
#   //  已开启｜在重试周期内
#   //  已开启｜超出重试周期
#   //  未开启
#   //  异常类型不支持
#   //  RPA任务不支持


class AutoRetryStatus(enum.Enum):
    ENABLED_IN_CYCLE = "已开启\n在重试周期内"
    ENABLED_OUT_OF_CYCLE = "已开启\n超出重试周期"
    DISABLED = "未开启"
    UNSUPPORTED_EXCEPTION_TYPE = "异常类型不支持"
    UNSUPPORTED_RPA_TASK = "RPA任务不支持"

    UNSUPPORTED_JOB_STATUS = "不支持的job状态"
    UNSUPPORTED_BUSINESS_ORDER_STATUS = "不支持的工单状态"
