import grpc
from leyan_proto.digismart.robot_web.auth_manager_pb2 import \
    GetShopGrantRecordResponse
from leyan_proto.digismart.robot_web.auth_manager_pb2 import GrantRecordInfo
from leyan_proto.digismart.robot_web.auth_manager_pb2_grpc import \
    AuthManagerServicer

from robot_processor.shop.auth_manager import AuthType
from robot_processor.shop.auth_manager import Credentials
from robot_processor.shop.models import Shop
from robot_processor.utils import unwrap_optional


class AuthManager(AuthManagerServicer):
    def GetShopGrantRecord(self, request, context):
        response = GetShopGrantRecordResponse()
        shop = Shop.Queries.optimal_shop_by_sid(request.sid, org_id=request.org_id)
        if not shop:
            context.abort(grpc.StatusCode.NOT_FOUND, "shop not found.")
        auth_type = AuthType(request.auth_type)
        credentials = Credentials.list_by_shop_auth_type(
            unwrap_optional(shop), auth_type
        )
        match auth_type:
            case AuthType.ALIPAY:
                for credential in credentials:
                    grant_info = GrantRecordInfo.Alipay(
                        account=credential.auth_account,
                        remark=credential.auth_extra_data.get("remark", ""),
                        status=credential.available_status,
                        name=credential.auth_extra_data.get("accountName", ""),
                        sid=credential.auth_extra_data.get("sid", ""),
                    )
                    response.alipay.append(grant_info)
        return response
