from grpc import ServicerContext
from leyan_proto.digismart.robot_web.form_pb2 import FormMetaInfo
from leyan_proto.digismart.robot_web.form_pb2 import FormVersion as FormVersionPb
from leyan_proto.digismart.robot_web.form_pb2 import ListFormVersionRequest, ListFormVersionResponse
from leyan_proto.digismart.robot_web.form_pb2_grpc import FormServiceServicer
from werkzeug.exceptions import NotFound

from robot_processor.currents import g
from robot_processor.form.models import Form as FormOrm
from robot_processor.form.models import FormVersion as FormVersionOrm
from robot_processor.form.models import FormWrapper
from robot_processor.shop.models import Shop
from robot_processor.utils import unwrap_optional


class FormService(FormServiceServicer):
    def ListFormVersion(self, request: ListFormVersionRequest, context: ServicerContext) -> ListFormVersionResponse:
        shop = Shop.Queries.optimal_shop_by_sid(unwrap_optional(g.auth.store_id), org_id=g.auth.org_id)
        if not shop:
            raise NotFound(f'store {g.auth.store_id} not found')
        if not FormOrm.Queries.is_subscribed(request.form_id, shop):
            raise NotFound(f'store {g.auth.store_id} has no form {request.form_id}')
        return self.do_list_form_version(request)

    @classmethod
    def _convert_form_meta_to_proto(cls, form: FormWrapper) -> FormMetaInfo:
        return FormMetaInfo(
            id=form.id,
            name=form.name,  # type: ignore[arg-type]
            description=form.description,  # type: ignore[arg-type]
            form_category=form.category,  # type: ignore[arg-type]
            tags=form.tags,
            enabled=bool(form.enabled),
            enable_service_count=bool(form.enable_service_count),
            co_edit=bool(form.co_edit),
            can_publish=bool(form.can_publish),
            form_mold=form.form_mold.name,
            update_user=form.update_user,  # type: ignore[arg-type]
            updated_at=form.updated_at,
        )

    @classmethod
    def _convert_form_version_to_proto(cls, version: FormVersionOrm, form: FormWrapper) -> FormVersionPb:
        return FormVersionPb(
            form_id=version.form_id,  # type: ignore[arg-type]
            step_id=version.step_id,
            version_no=version.version_no,
            version_descriptor=version.version_descriptor,
            updator=version.updator,
            created_at=version.created_at,
            meta=cls._convert_form_meta_to_proto(form)
        )

    def do_list_form_version(self, request: ListFormVersionRequest) -> ListFormVersionResponse:
        shop = Shop.Queries.optimal_shop_by_sid(unwrap_optional(g.auth.store_id), org_id=g.auth.org_id)
        form = FormOrm.query.get(request.form_id).wraps(shop)  # type: ignore[union-attr,arg-type]
        versions_query = form.versions.filter(~FormVersionOrm.deleted)
        response = ListFormVersionResponse(succeed=True)
        for version in versions_query:
            response.data.append(self._convert_form_version_to_proto(version, form))
        return response
