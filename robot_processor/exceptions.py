import json


from flask import Response
from werkzeug.exceptions import HTTPException as BasicHTTPException


class HTTPException(BasicHTTPException):
    success: bool = False
    error_code: int
    message: str
    status_code: int = 200

    def __init__(self, error_code: int, message: str, status_code: int = 200):
        super().__init__()
        self.error_code = error_code
        self.message = message
        self.status_code = status_code

    def get_response(self, environ=None, scope=None) -> Response:
        return Response(
            status=self.status_code,
            content_type="application/json",
            response=json.dumps({
                "success": self.success,
                "error_code": self.error_code,
                "message": self.message,
            }),
        )
