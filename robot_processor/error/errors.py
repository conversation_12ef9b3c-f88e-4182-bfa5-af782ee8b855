from typing import Optional

from .base import BizError


class ScenarioError(BizError):
    status_code = 400

    _biz_display: Optional[str] = None

    @property
    def biz_display(self):
        if self._biz_display is not None:
            return str(self._biz_display)
        return super().biz_display

    @biz_display.setter
    def biz_display(self, display_message: str):
        self._biz_display = display_message


class BusinessOrderError(ScenarioError):
    pass


class BusinessOrderNotFoundError(BusinessOrderError):
    status_code = 404
    _biz_display = "工单实例未找到"

    def __init__(self, business_order_id: int, biz_display: str | None = None):
        super().__init__("工单实例未找到," f"business order id: {business_order_id}")
        self._biz_display = biz_display or self._biz_display


class TransferStatusUnSupportError(BusinessOrderError):
    status_code = 403

    def __init__(self, business_order_id, current_status, expect_status, biz_display: str | None = None):
        super().__init__(
            f"打款单状态不支持，当前状态：{current_status}, "
            f"支持的状态：{expect_status}, "
            f"business order id: {business_order_id}"
        )
        self._biz_display = biz_display or self._biz_display


class BusinessOrderNeedEnterExceptionalPoolError(BusinessOrderError):
    status_code = 400

    def __init__(self, business_order_id: int, job_id: int, error: str):
        super().__init__(
            "工单异常，进入异常任务池,"
            f"business order id: {business_order_id} "
            f"job id: {job_id} error type: {error}"
        )
        self.business_order_id = business_order_id
        self.job_id = job_id
        self.error = error


class BusinessOrderValueUniqueConstraintError(BusinessOrderError):
    status_code = 400
    _biz_display = "唯一值校验检查失败"

    def __init__(self, job_id: int, business_order_id: int | None, widget_key: str, value, detail=None):
        self.detail = detail
        super().__init__(
            "工单数据保存失败，唯一值校验不通过，"
            f"job id: {job_id} business order id: {business_order_id} "
            f"widget key: {widget_key} value: {value}"
        )


class JobError(ScenarioError):
    _biz_display = "[非法操作]不支持的操作类型"


class JobDeletedError(JobError):
    status_code = 404
    _biz_display = "工单步骤已删除"

    def __init__(self, job_id: int):
        super().__init__(f"工单步骤已删除, job id: {job_id}")


class JobRetryConfigError(JobError):
    status_code = 403
    _biz_display = "[非法操作]当前任务配置为不允许重试"

    def __init__(self, job_id: int):
        super().__init__(f"任务配置不支持重试, job id: {job_id}")


class JobStatusUnSupportError(JobError):
    status_code = 403
    _biz_display = "[非法操作]任务状态不支持"

    def __init__(self, job_id: int, current_status, expect_status, biz_display: str | None = None):
        super().__init__(f"任务状态不支持，job id: {job_id}" f"当前状态:{current_status}，支持的状态:{expect_status}")
        self._biz_display = biz_display or self._biz_display


class CurrentJobUnMatchError(JobError):
    status_code = 403
    _biz_display = "[非法操作]操作的任务不是工单当前任务"

    def __init__(self, job_id: int, expect_job_id: int):
        super().__init__(f"任务不匹配，当前需要执行的任务为: {expect_job_id}，实际执行的任务为: {job_id}")


class JobStepTypeUnSupportError(JobError):
    status_code = 403
    _biz_display = "[非法操作]任务类型不支持"

    def __init__(self, job_id: int, step_type: int, biz_display: str | None = None):
        super().__init__(f"任务步骤类型不支持，job id: {job_id} step type: {step_type}")
        self._biz_display = biz_display or self._biz_display


class JobCandidateAssistantEmptyError(JobError):
    status_code = 400
    _biz_display = "当前步骤无执行客服，建议指派客服后再催促"

    def __init__(self, job_id: int):
        super().__init__(f"任务缺少有效的执行客服账号, job id: {job_id}")


class JobNoRejectToIDError(JobError):
    status_code = 400
    _biz_display = "未指定退回步骤"

    def __init__(self, job_id: int):
        super().__init__(f"未指定退回步骤, job id: {job_id}")


class JobAssigneeAssistantInvalidError(JobError):
    status_code = 403
    _biz_display = "[非法操作]当前任务执行客服账号已失效"

    def __init__(self, job_id: int, assistant):
        super().__init__(f"任务客服账号已失效, job id: {job_id} assistant: {assistant}")


class JobAssigneeAssistantAuthError(JobError):
    status_code = 403
    _biz_display = "[非法操作]指定客服账号无权限处理该任务"

    def __init__(self, job_id: int, assignee_assistant, candidate_assistants, biz_display: str | None = None):
        super().__init__(
            "指定客服账号无权限处理该任务,"
            f"job id: {job_id} "
            f"assignee assistant: {assignee_assistant} "
            f"candidate assistants: {candidate_assistants}"
        )
        self._biz_display = biz_display or self._biz_display


class JobCantPickError(JobError):
    status_code = 403

    def __init__(self, job_id: int, pick_assistant):
        super().__init__(f"客服无法领取该任务, job id: {job_id} pick assistant: {pick_assistant}")


class JobActionNeedAdminError(JobError):
    status_code = 403
    _biz_display = "[非法操作]需要管理员权限"

    def __init__(self, job_id: int, action, assistant):
        super().__init__(f"{action}操作需要管理员权限，job id: {job_id} assistant: {assistant}")


class JobNeedAssigneeAssistantError(JobError):
    status_code = 400
    _biz_display = "[非法操作]任务需要指定执行客服"

    def __init__(self, job_id: int, biz_display: str | None = None):
        super().__init__(f"任务需要指定执行客服，job id: {job_id}")
        self._biz_display = biz_display or self._biz_display


class AlipayBusinessOrderStatusNotSupportError(JobError):
    status_code = 403
    _biz_display = "[非法操作]批量打款"

    def __init__(self, current_status, biz_display: str | None = None):
        super().__init__(f"工单打款单状态不支持，当前状态{current_status}")
        self._biz_display = biz_display or self._biz_display


class JobCanNotSkip(JobError):
    status_code = 400
    _biz_display = "当前任务无法跳过，请检查步骤配置和操作权限"
