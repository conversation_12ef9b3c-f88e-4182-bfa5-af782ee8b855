"""这里定义一些异常的分类，请不要直接使用这里异常"""


class RobotProcessorException(Exception):
    pass


class BizError(RobotProcessorException):
    """业务异常"""

    status_code = 406

    def __init__(self, message, /, *args, status_code=None, **context):
        super().__init__(message, *args)
        self.status_code = status_code or self.status_code
        self.context = context

    @property
    def biz_display(self) -> str:
        """用于接口响应的展示"""
        return str(self)


class ClientRequestError(RobotProcessorException):
    """请求外部服务时发生的异常"""

    service: str

    def __init__(self, req=None, res=None, message=None):
        self.req = req
        self.res = res
        self.message = message

    def __str__(self):
        return "{} 接口请求失败，请求参数：{} 返回：{} {}".format(
            self.service,
            self.req,
            self.res,
            "" if self.message is None else self.message,
        )


class JobProcessError(RobotProcessorException):
    """工单流转处理过程中发生的异常"""
