from typing import List

from dramatiq import Retry
from pydantic import BaseModel

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.wdtulti import WdtUltiQM, StockinRefundQueryResp, StockinOrder, WdtQmRateLimitError


class StockinOrderOutput(BaseModel):
    orders: List[StockinOrder]


class WdtultiRefundQueryByLogisticsNoExecutor(AutoJobControllerBase):
    job_type = JobType.WDTULTI_STOCKIN_REFUND_QUERY_BY_REFUND_NO
    output_model = StockinOrderOutput
    """
    旺店通旗舰版根据退换单号获取退货入库单
    """

    def process(self):
        refund_no = self.args.get_arg_by_task_arg_name("refund_no")
        if not refund_no:
            return JobStatus.FAILED, "没有退换单号"
        refund_nos = refund_no.split(",")
        orders: List[StockinOrder] = []
        try:
            for refund_no in refund_nos:
                resp: StockinRefundQueryResp = WdtUltiQM(
                    self.shop.sid).stockin_refund_query_by_refund_no(refund_no)
                if resp.data.total_count == 0:
                    return JobStatus.FAILED, "没有获取到退换单"
                orders.extend(resp.data.order)
        except WdtQmRateLimitError as e:
            raise Retry(str(e), delay=1000)

        self.states.write_job_widget_collection(StockinOrderOutput(
            orders=orders).dict())
        return JobStatus.SUCCEED, ""
