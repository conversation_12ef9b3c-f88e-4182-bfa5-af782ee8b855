from pydantic import BaseModel, Field
from result import Ok, Err

from robot_processor.enums import JobStatus, JobType
from robot_processor.fs_type import basic as basic_fs_type
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.converter.string_transformer import jst_logistics_info_copied_string_extractor


class JstLogisticsInfoCopiedToStringParam(BaseModel):
    copied_jst_logistics_info: basic_fs_type.String = Field(title="聚水潭复制过来的物流信息")


class JstLogisticsInfoCopiedToStringOutput(BaseModel):
    logistics_no_str: str = Field(title="物流单号字符串")


class JstLogisticsInfoCopiedToStringExecutor(AutoJobControllerBase):
    job_type = JobType.CONVERTER_JST_LOGISTICS_INFO_COPIED_TO_STRING
    input_model = JstLogisticsInfoCopiedToStringParam
    output_model = JstLogisticsInfoCopiedToStringOutput

    def process(self):
        copied_jst_logistics_info_res = basic_fs_type.String.from_python_value(
            self.args.get_arg_by_task_arg_name("copied_jst_logistics_info")
        )
        match copied_jst_logistics_info_res:
            case Err(err):
                return JobStatus.FAILED, err
            case Ok(string_container):
                if string_container is None:
                    return JobStatus.FAILED, "没有聚水潭复制过来的物流信息"
                copied_jst_logistics_info: basic_fs_type.String = string_container
            case _:
                raise Exception("unreachable")

        converted = jst_logistics_info_copied_string_extractor(
            copied_jst_logistics_info
        )
        converted_str = ",".join(converted.to_json())
        output = JstLogisticsInfoCopiedToStringOutput(logistics_no_str=converted_str)

        self.states.write_job_widget_collection(output.dict())
        return JobStatus.SUCCEED, None
