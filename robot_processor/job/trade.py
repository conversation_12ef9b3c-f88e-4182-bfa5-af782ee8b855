from typing import Optional, <PERSON><PERSON>

from dramatiq import Retry
from flask import current_app
from loguru import logger
from result import Ok, Err, Result

import robot_processor.logging.vars as log_vars
from robot_processor.enums import JobStatus, JobType, ErpType
from robot_processor.error.job_process import (
    ShopNotFound,
    ErpNotConfigured,
    ErpNotSupported,
)
from robot_processor.job.auto_job import AutoJobControllerBase

from robot_processor.utils import response_to_log
from rpa.erp.baisheng import BaiShengClient
from rpa.erp.wanliniu import WanliniuClient
from rpa.erp.wdt import WdtClient
from rpa.erp.wdt.schemas import Trade
from rpa.erp.wdtulti import WdtUltiClient


class TradeExecutor(AutoJobControllerBase):
    job_type = JobType.TRADE

    def get_rate_limit_setting(self, conf: dict) -> Optional[Tuple[str, str]]:
        """订单相关任务，优先使用租户级别的限流配置"""
        shop = self.job_wrapper.shop
        if shop:
            limit_key = f"JOB_LIMITER_{self.job_type}_{shop.org_id}"
        else:
            limit_key = f"JOB_LIMITER_{self.job_type}_{self.job.business_order.sid}"
        per_job_type_key = f"JOB_LIMITER_{self.job_type}"
        for conf_key in [limit_key, per_job_type_key]:
            if limit_value := conf.get(conf_key):
                return limit_key, limit_value
        return None

    @staticmethod
    def get_argument_label(arugments: list, arg_name: str):
        labels = [arg.get("label") for arg in arugments if arg.get("name") == arg_name]
        if labels:
            return labels[0]
        return ''

    def save_to_data(self, trade: dict) -> Result[None, str]:
        if missing_args := self.states.write_job_output(trade, allow_empty_value=False):
            result_str = ' / '.join(missing_args)
            return Err(f'补发单未发货，输出字段 {result_str} 查询为空')
        return Ok(None)

    def filter_wdt_trade(self, trade_result: list):
        tid_status = current_app.config.get("wdt_order_status", '95,96,101,105,110,113').split(',')
        if len(trade_result) == 1:
            trade_info = trade_result[0]
        else:
            trade_infos = [trade for trade in trade_result if
                           str(trade.get("trade_status")) in tid_status and str(trade.get("logistics_no"))]
            trade_info = trade_infos[0] if trade_infos else {}

        return trade_info

    @staticmethod
    def filter_invalid_logistics_no(logistics_no):
        # template_default_val 是一些预定义的默认填充值
        template_default_val = current_app.config.get("template_default_val", '-').split(',')
        if logistics_no in template_default_val:
            return ""
        else:
            return logistics_no

    @staticmethod
    def parse_tid(tid_obj):
        tid = tid_obj
        if isinstance(tid_obj, list):
            tid = tid_obj[0].get("tid")
        elif isinstance(tid_obj, dict):
            tid = tid_obj.get("tid")
        return tid

    @staticmethod
    def check_erp_trade(trades, trade_status, erp_type):
        if not trades:
            return False
        if erp_type == ErpType.JST:
            return len(list(filter(lambda trade: trade.get("status") in trade_status, trades))) > 0
        return True

    @staticmethod
    def _get_wdt_warehouse_name(trade_info: Trade, wdt_client_: WdtClient):
        ret = []
        if warehouse_no := trade_info.warehouse_no:
            warehouse_nos = warehouse_no.split(',')
            for warehouse_no_ in warehouse_nos:
                wh_resp = wdt_client_.warehouse_query(warehouse_no_)
                if wh_resp.response.errorcode == 0:
                    ret.append(wh_resp.response.warehouses[0].name)
        if ret:
            return ",".join(ret)
        return f'找不到分仓信息，请检查分仓编码是否正确 {warehouse_no}'

    def _wdt_process(self, set_job_status_on_success) -> Result[None, str]:
        # TODO wdt_tid 的 parse 也可以写到 JobArgument
        tid = self.parse_tid(self.args.get_arg_by_task_arg_name("wdt_tid"))
        logistics_no = self.filter_invalid_logistics_no(self.args.get_arg_by_task_arg_name("orginal_logistics_no"))
        trade_no = self.args.get_arg_by_task_arg_name("trade_no")

        wdt_client_ = WdtClient(self.order.sid)

        try:
            resp = wdt_client_.trade_query(tid, logistics_no, check_rate_limit=True)
            trades = resp.response.trades
            if not trades:
                return Err("找不到对应订单信息")
            # 过滤已发货的订单:
            # - 95: 已发货
            # - 96: 未知
            # - 101: 未知
            # - 105: 部分打款
            # - 110: 已完成
            # - 113: 异常发货
            # ref: https://open.wangdian.cn/qyb/open/apidoc/doc?path=trade_query.php
            shipped_wdt_order_status = current_app.config.get("wdt_order_status", '95,96,101,105,110,113').split(',')
            # 是否要全部发货才执行，默认false，与之前的行为一致
            if self.args.get_arg_by_task_arg_name("need_all_sent"):
                if len([trade for trade in trades if str(trade.trade_status) not in shipped_wdt_order_status]):
                    return Err("订单未发货")
            trades = [trade for trade in trades if str(trade.trade_status) in shipped_wdt_order_status]
            if trade_no:
                trades = [trade for trade in trades if trade.trade_no == trade_no]
            if trades:
                if self.args.get_arg_by_task_arg_name("is_need_merge"):
                    trade = Trade.merge_trades(trades)
                else:
                    trade = trades[0]
                output = dict()
                output['logistics_name'] = trade.logistics_name
                output["paid"] = trade.paid
                output['logistics_no'] = trade.logistics_no
                output['warehouse_no'] = trade.warehouse_no
                output['warehouse_name'] = self._get_wdt_warehouse_name(trade, wdt_client_)
                output['goods_no'] = ';'.join([goods.goods_no for goods in trade.goods_list])
                output['goods_name'] = ';'.join([goods.goods_name for goods in trade.goods_list])
                output['spec_no'] = ';'.join([goods.spec_no for goods in trade.goods_list])
                output['spec_name'] = ';'.join([goods.spec_name for goods in trade.goods_list])
                output['trade_status_zh'] = trade.trade_status_zh

                return self.save_to_data(output)
            else:
                # 订单未发货
                return Err("订单未发货")
        except Retry as e:
            raise e
        except Exception as e:
            # 保留完整信息进入异常池
            return Err(f"查询订单信息失败: {tid=}, {logistics_no=}" + str(e))

    def _wdtulti_process(self) -> Result[None, str]:
        tid = self.parse_tid(self.args.get_arg_by_task_arg_name('wdt_tid'))
        logistics_no = self.filter_invalid_logistics_no(self.args.get_arg_by_task_arg_name("orginal_logistics_no"))
        trade_no = self.args.get_arg_by_task_arg_name("trade_no")
        if not tid:
            return Err("缺失 wdtulti_tid 字段")
        res = WdtUltiClient(self.order.sid).trade_query(wdt_tid=tid, logistics_no=logistics_no)  # type: ignore[arg-type]  # noqa: E501
        resp_json = res.get("response")
        logger.info(f"wdtulti {tid=}, {logistics_no=}, {resp_json=}")
        if resp_json and resp_json.get("status") == 0 and resp_json.get("data"):
            data = resp_json["data"]
            if data["total_count"] == 0:
                return Err(f"没有找到符合条件的订单 tid:{tid}")

            trades = data["order"]
            if trade_no:
                trades = [trade for trade in data["order"] if trade["trade_no"] == trade_no]
            trade_info = self.filter_wdt_trade(trades)
            if not trade_info:
                return Err(f"没有找到符合条件的订单 tid:{tid}")

            trade_info["paid"] = str(sum([float(detail['paid']) for detail in trade_info['detail_list']]))
            trade_info["goods_no"] = ';'.join([detail['goods_no'] for detail in trade_info['detail_list']])
            trade_info["goods_name"] = ';'.join([detail['goods_name'] for detail in trade_info['detail_list']])
            trade_info["spec_no"] = ';'.join([detail['spec_no'] for detail in trade_info['detail_list']])
            trade_info["spec_name"] = ';'.join([detail['spec_name'] for detail in trade_info['detail_list']])
            if warehouse_no := trade_info.get('warehouse_no'):
                warehouse_query_res = WdtUltiClient(self.order.sid).warehouse_query(warehouse_no)  # type: ignore[arg-type]  # noqa: E501
                if (
                        int(warehouse_query_res["response"]["status"]) == 0
                        and warehouse_query_res["response"]["data"]["details"]
                ):
                    trade_info["warehouse_name"] = warehouse_query_res["response"]["data"]["details"][0]["name"]
                return self.save_to_data(trade_info)
            else:
                return Err(f"没有找到符合条件的订单 tid:{tid}")

        else:
            return Err(f"请检查订单是否存在 tid:{tid}")

    def _jst_process(self) -> Result[None, str]:
        """最旧版本的聚水潭订单信息回执，与jst_trade中的回执相比较，只输出一笔订单的信息"""
        from robot_processor.job.jst_trade import JstTradeExecutor
        status, msg = JstTradeExecutor(self.job).process()
        if status == JobStatus.SUCCEED:
            return Ok(None)
        else:
            return Err(msg)

    def _wanliniu_process(self) -> Result[None, str]:
        tid = self.args.get_trades()[0].tid
        tp_type = self.args.get_arg_by_task_arg_name("tp_type")
        match res := WanliniuClient(self.order.sid).trade_with_sku_query(tid=tid, tp_type=tp_type):  # type: ignore[arg-type]  # noqa: E501
            case Ok(trade_info):
                return self.save_to_data(trade_info)
            case Err():
                return res

    def _baisheng_process(self) -> Result[None, str]:
        tid = self.args.get_arg_by_task_arg_name("erp_tid")
        if isinstance(tid, (list, dict)):
            tid = self.args.get_trades()[0].tid

        resp = BaiShengClient(sid=self.job_wrapper.shop.sid).get_order_list(tid)
        if not resp.ok:
            error_message = f"获取订单 {tid} 失败 {resp.status_code}, {resp.text}"
            logger.warning(error_message)
            return Err(response_to_log(resp))

        resp_json = resp.json()
        if resp_json.get('status') == 'api-success' and resp_json.get('message') == 'success':
            for order in resp_json.get("data").get("orderListGets", []):
                if order.get("deal_code") == tid:
                    trade = {
                        "warehouse": order.get("fhck"),
                        "logistics_corp": order.get("shipping_name"),
                        "logistics_no": order.get("shipping_sn"),
                        "payment": order.get("payment")
                    }
                    return self.save_to_data(trade)
            else:
                return Err(f'未查询到订单 {tid} {resp.text}')
        else:
            error_message = f"获取订单 {tid} 失败, {resp.text}"
            logger.warning(error_message)
            return Err(response_to_log(resp))

    def _duohong_process(self, set_job_status_on_success) -> Result[None, str]:
        from robot_processor.job.erp_trade_handler.duohong import DuoHongTradeHandler
        bo_data = {
            "tid_new": self.args.get_arg_by_task_arg_name("tid_new")
        }
        handler = DuoHongTradeHandler(shop=self.job_wrapper.shop, job=self.job, convert_data=bo_data)
        if handler.is_skip():
            return Ok(None)
        result = handler.get_receipt()
        match result:
            case Ok((trade, status)):
                if status == JobStatus.SUCCEED:
                    return self.save_to_data(trade)
                if status == JobStatus.RUNNING:
                    set_job_status_on_success(JobStatus.RUNNING)
                    logger.info(f"send job task@{self.job.id}@duohong")
                return Ok(None)
            case Err():
                return result
            case _:
                return Err('未知错误')

    def process(self):
        if not self.job_wrapper.shop:
            return JobStatus.FAILED, ShopNotFound()
        if not self.job_wrapper.erp_info:
            return JobStatus.FAILED, ErpNotConfigured()

        erp_type = self.job_wrapper.erp_info.erp_type
        log_vars.ErpType.set(erp_type.name)  # type: ignore[union-attr]

        job_status_on_success = JobStatus.SUCCEED

        def set_job_status_on_success(_job_status):
            nonlocal job_status_on_success
            job_status_on_success = _job_status

        if ErpType.WDT == erp_type:
            result = self._wdt_process(set_job_status_on_success)

        elif ErpType.WDTULTI == erp_type:
            result = self._wdtulti_process()

        elif ErpType.JST == erp_type:
            result = self._jst_process()

        elif ErpType.WANLINIU == erp_type:
            result = self._wanliniu_process()

        elif ErpType.BAISHENG == erp_type:
            result = self._baisheng_process()

        elif ErpType.DUOHONG == erp_type:
            result = self._duohong_process(set_job_status_on_success)
        else:
            return JobStatus.FAILED, ErpNotSupported(erp_type)

        match result:
            case Ok():
                return job_status_on_success, None
            case Err(error_message):
                return JobStatus.FAILED, error_message
