from typing import cast

from pydantic import BaseModel
from result import Result, Err, Ok

from robot_processor.enums import JobStatus, JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.mola import MolaClient


class WlnCreateExchangeOutput(BaseModel):
    salercpt_no: str  # 万里牛内部单号


class WlnCreateExchangeRpaExecutor(AutoJobControllerBase):
    job_type = JobType.WLN_CREATE_EXCHANGE_RPA
    output_model = WlnCreateExchangeOutput

    def get_rate_limit_setting(self, conf: dict):
        org_limit_key = f"JOB_LIMITER_{self.job_type}_ORG_{self.job_wrapper.shop.org_id}"
        default_limit_key = f"JOB_LIMITER_{self.job_type}"
        conf_keys = [org_limit_key, default_limit_key]
        for conf_key in conf_keys:
            if limit_value := conf.get(conf_key):
                return org_limit_key, limit_value

    def process(self):
        if not (shop := self.job_wrapper.shop):
            return JobStatus.FAILED, "无店铺信息"
        exchange_uid = self.args.get_arg_by_task_arg_name("exchange_uid")
        if not exchange_uid:
            return JobStatus.FAILED, "缺少万里牛单号ID"
        exchange_uid = cast(str, exchange_uid)
        # 下单时同步凭证图片
        images = self.args.get_arg_by_task_arg_name("image")
        if images:
            pics = [i.get("url") for i in images]
            if len(pics) > 4:
                pics = pics[:4]
        else:
            pics = None
        # 仓库和快递公司，部分工单模板会使用选择组件；选择组件使用 get_arg_by_task_arg_name 获取到的是 list 类型的数据。
        # 因此使用特定的格式化方法。
        storage_name = self.args.get_arg_as_str_by_task_arg_name("storage_name")
        delivery_name = self.args.get_arg_as_str_by_task_arg_name("delivery_name")
        res = MolaClient(shop.sid).create_trade_form_exchange(exchange_uid, pics, storage_name, delivery_name)
        salercpt_no = self._get_salercpt_no(res)
        output = self.output_model(salercpt_no=salercpt_no)
        self.states.write_job_widget_collection(output.dict())
        return JobStatus.SUCCEED, None

    def _get_salercpt_no(self, res: Result[dict, str]) -> str:
        match res:
            case Err(error_message):
                raise Exception(f"未返回内部单号: {error_message}")
            case Ok(body):
                result = body.get("result", {})
                entity_states = result.get("entityStates", {})
                key = "salercpt_no"
                for obj in entity_states.values():
                    if isinstance(obj, dict) and key in obj:
                        return obj[key]
                raise Exception("未返回内部单号")
