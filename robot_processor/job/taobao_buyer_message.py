from pydantic import BaseModel

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job import AutoJobControllerBase
from rpa.mola import MolaClient


class Output(BaseModel):
    buyer_message: str  # 买家备注


class TaobaoTradeExecutor(AutoJobControllerBase):
    job_type = JobType.TAOBAO_BUYER_MESSAGE
    output_model = Output

    def process(self):
        tid = self.args.get_trades()[0].tid
        consult_trade_res = MolaClient(self.shop.sid).get_qianniu_delivery_consult_trade(tid)
        if consult_trade_res.is_err():
            return JobStatus.FAILED, consult_trade_res.unwrap_err()
        consult_trade = consult_trade_res.unwrap()
        if consult_trade["success"] and consult_trade["result"]:
            buyer_id = consult_trade["result"]["data"]["buyerId"]
            res = MolaClient(self.shop.sid).get_buyer_trade_list(buyer_id)
            if res.is_err():
                return JobStatus.FAILED, res.unwrap_err()
            resp = res.unwrap()
            if resp["success"] and resp["result"]:
                trades = resp["result"]["data"]["orders"]
                trades = [trade for trade in trades if trade["bizOrderId"] == tid]
                if not trades:
                    return JobStatus.FAILED, "找不到订单信息"
                buyer_message = trades[0].get("buyerMessage") or ""
                self.states.write_job_widget_collection(Output(buyer_message=buyer_message).dict())
                return JobStatus.SUCCEED, None
            return JobStatus.SUCCEED, resp["message"]
        return JobStatus.FAILED, consult_trade["message"]
