import typing as t

from loguru import logger
from pydantic import BaseModel
from result import Err, Ok

from robot_processor.business_order.job_wrappers.wrapper import (
    set_business_order_status_against_current_job,
    record_action_log,
)
from robot_processor.business_order.tasks import package_jobs
from robot_processor.client import rpa_client
from robot_processor.db import in_transaction
from robot_processor.enums import JobStatus, JobType, JobProcessMark
from robot_processor.ext import cache
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.job.base import JobExecuteResult
from robot_processor.job.skip_helper import SkipHelper
from robot_processor.utils import unwrap_optional
from requests import RequestException


class JobSkipError(Exception):
    ...


class RpaAsyncJobController(AutoJobControllerBase):
    """
    依赖飞梭rpa客户端实现job基类

    1. robot-processor作为job发起方
    2. rpa-control作为客户端调度服务
    3. 飞梭rpa客户端作为job实际执行方
    调用流程分为两步
    1. Job controller process逻辑
        - 飞梭调用rpa-control申请执行rpa任务
        - rpa-control创建一条执行记录，并将ID返回给飞梭
        - 飞梭将执行记录ID和job_id对应关系缓存到redis，并将job置为RUNNING状态
    2. Job controller ack逻辑
        - 当rpa任务执行完成后，rpa-control会将结果推送到kafka
        - 飞梭kafka服务监听到消息后，从缓存中找到该条执行记录对应的job_id
        - kafka服务调用Job controller中的`rpa_client_ack`方法，结束job（将job状态置为成功或失败）

    如果需要对接飞梭rpa，有三个方法需要注意
    - `gen_rpa_client_payload` 该方法属于process逻辑的一部分，用于组装rpa-control请求的参数，具体参数内容和飞梭rpa的机器人实现有关，不同的job应该有不同的组装逻辑
    - `on_success` 该方法属于ack逻辑的一部分，用于解析rpa机器人成功的业务结果以及回写bo data
    - `on_fail` 该方法属于ack逻辑的一部分，用于解析rpa机器人失败的业务结果以及回写bo data

    注意！如果新增了job_type类型，需要发web、queue、和kafka三个服务
    """
    job_type: JobType
    output_model: t.Type[BaseModel]

    def gen_rpa_client_payload(self) -> t.Dict:
        """
        组装rpa client请求body
        """
        raise NotImplementedError()

    def on_success(self, rpa_response: dict):
        """
        rpa结果返回成功处理逻辑
        """
        ...

    def on_fail(self, rpa_response: dict):
        """
        rpa结果返回失败处理逻辑
        """
        ...

    def process(self) -> t.Tuple[JobStatus, t.Union[str, Exception, None]]:
        workflow_id = self.job.raw_step_v2["task"]["context"]["workflow_id"]
        execution_id = self.job.extra_data.get("execution_id")
        try:
            param_dict = self.gen_rpa_client_payload()
        except JobSkipError:
            return JobStatus.SUCCEED, None
        except Exception as ex:
            return JobStatus.FAILED, str(ex)
        try:
            result = rpa_client.execute_workflow(workflow_id=workflow_id,
                                                 execution_id=execution_id,
                                                 payload=param_dict)
        except RequestException as ex:
            return JobStatus.FAILED, str(ex)

        match result:
            case Ok(res):
                execution_id = unwrap_optional(res.data).execution_id
                logger.info('已触发 rpa 执行 execution_id={}', execution_id)
                self.states.write_job_states({'execution_id': execution_id})
                execution_cache_key = "rpa-execution:{execution_id}"
                cache.set(
                    execution_cache_key.format(execution_id=execution_id),
                    self.job.id,
                    timeout=86400,
                )
                return JobStatus.RUNNING, None
            case Err(exception):
                return JobStatus.FAILED, str(exception)

    def rpa_client_ack(self, rpa_response: dict):
        """
        rpa执行结束回调，由kafka事件触发
        """
        # fixme 使用任务重入 process 的方式代替 ack 的方式
        success: bool = rpa_response["success"]
        result = JobExecuteResult()
        with in_transaction():
            if success:
                logger.info("rpa 执行成功 output={}", rpa_response["output"])
            elif SkipHelper(self.job).try_auto_skip():
                logger.info(f"任务失败，尝试自动跳过 {self.job.id}")
                result.auto_skip = True
                result.process_mark = JobProcessMark.SKIP
                logger.info(f"任务自动跳过成功 {self.job.id} 状态已经在SkipHelper更新")
                # 自动跳过成功则也视为成功。
                success = True
            else:
                logger.warning("rpa 执行失败 response={}", rpa_response)
                result.status = JobStatus.FAILED
                result.exc_info = str(rpa_response)

            self.job.process_mark = result.process_mark
            self.job.exc_info = result.exc_info
            self.job.set_status(result.status)
            set_business_order_status_against_current_job(self.job)
            record_action_log(job=self.job, result=result)

        if success:
            self.on_success(rpa_response)
            # todo: 确认是否需要记录 job_transition？
            package_jobs(self.job.business_order_id, self.job.id)
            cache.delete(
                "rpa-execution:{execution_id}".format(
                    execution_id=rpa_response["meta"]["execution_id"]
                )
            )
        else:
            self.on_fail(rpa_response)
