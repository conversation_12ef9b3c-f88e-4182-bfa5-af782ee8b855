from typing import Dict
from typing import Optional

from pydantic import BaseModel

from robot_processor.client import taobao_client
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase


class Output(BaseModel):
    success: bool


class <PERSON>baoChangeAddressExecutor(AutoJobControllerBase):
    job_type = JobType.TAOBAO_CHANGE_ADDRESS
    output_model = Output

    def get_receiver(self) -> Optional[Dict[str, str]]:
        receiver = self.args.get_arg_by_task_arg_name("new_address")
        if not receiver:
            return None
        zone = receiver.pop("zone", "")
        if zone:
            receiver["district"] = zone
        if not receiver.get("district"):
            receiver["district"] = ""
        if not receiver.get("town"):
            receiver["town"] = ""
        return receiver

    def process(self):
        tid = self.args.get_trades()[0].tid
        receiver = self.get_receiver()
        if not receiver:
            return JobStatus.FAILED, "没有新的地址"
        grant_record = self.shop.get_recent_record()
        if not grant_record:
            return JobStatus.FAILED, "没有授权"
        taobao_res = taobao_client.change_address(
            grant_record.access_token,
            tid,
            receiver_name=receiver.get("name"),
            receiver_phone=None,
            receiver_mobile=receiver.get("mobile"),
            receiver_state=receiver.get("state"),
            receiver_city=receiver.get("city"),
            receiver_district=receiver.get("district"),
            receiver_address=receiver.get("address"),
            receiver_zip=None,
            receiver_town=receiver.get("town"),
        )
        if taobao_res.is_success():
            self.states.write_job_widget_collection(Output(success=True).dict())
            return JobStatus.SUCCEED, ""
        else:
            self.states.write_job_widget_collection(Output(success=False).dict())
            return JobStatus.FAILED, str(taobao_res.unwrap_err())
