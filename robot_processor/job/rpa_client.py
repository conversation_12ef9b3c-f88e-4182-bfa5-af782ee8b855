import json
from typing import Optional, List

from loguru import logger
from robot_processor.enums import JobType
from robot_processor.job.rpa_async_job import Rpa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>roller, JobSkipError


class RPAClient(RpaAsyncJobController):
    job_type = JobType.RPA_CLIENT

    def get_rate_limit_setting(self, conf: dict):
        job_ident = f"RPA_CLIENT:{self.get_identifier()}"
        org_limit_key = f"JOB_LIMITER_{job_ident}_ORG_{self.job_wrapper.shop.org_id}"
        default_limit_key = f"JOB_LIMITER_{job_ident}_ORG"

        conf_keys = [org_limit_key, default_limit_key]
        for conf_key in conf_keys:
            if limit_value := conf.get(conf_key):
                return org_limit_key, limit_value

    def get_identifier(self) -> Optional[str]:
        step = self.job.step
        if not step:
            return None
        task = step.task
        if not task:
            return None
        rpa_metadata = task.rpa_client_metadata
        if rpa_metadata:
            return rpa_metadata.ident
        else:
            return None

    def process_execution_screenshot(self, rpa_output_list):
        """处理执行结果的截图

        会将截图的 url
            1. 写入 job.extra_data
            2. 如果配置了输出字段，回写到组件上
        """
        screenshot = next(filter(lambda data: data.get("name") == "screenshots", rpa_output_list), None)
        if not screenshot:
            return
        screenshot_url = screenshot["object"]["success_url"]
        self.states.write_job_states({"screenshot": screenshot_url}, replace=False)
        files = [{"url": screenshot_url, "name": "执行结果截图.png"}]
        self.states.write_job_output({"screenshots": files})

    def on_success(self, rpa_response: dict):
        output = json.loads(rpa_response["output"])
        if output:
            logger.info(f"{output=}")
            self.process_execution_screenshot(output)
            ident = self.get_identifier()
            if ident == "wdt_trade_info_reveal":
                rpa_output = output[0]
                if rpa_output["type"] == "object":
                    self.states.write_job_output(rpa_output['object'])
                else:  # text
                    self.states.write_job_output(json.loads(output["text"]))

    def gen_rpa_client_payload(self) -> dict:
        from .qianniu import SenderSchema, SenderEnum
        ident = self.get_identifier()
        if not ident:
            raise Exception("未找到 rpa client 任务标识")
        if ident == "qn_send_message":
            msg_arguments = self.args.get_send_message_arguments()
            if msg_arguments.skip:
                raise JobSkipError()
            param_dict = {
                'pid': self.job.business_order.sid,
                'receiver': msg_arguments.usernick,
                'text': msg_arguments.content,
            }
            sender_schema = SenderSchema(**(msg_arguments.sender or {}))
            senders = sender_schema.get_senders(self.job, None) or []
            if senders:
                param_dict['sub_id'] = [sender.sub_id for sender in senders]  # type: ignore[assignment]
            if msg_arguments.image_urls:
                param_dict['image_url'] = msg_arguments.image_urls[0]
        elif ident == "wdt_trade_info_reveal":
            trades = self.args.get_trades("order")
            if not trades:
                raise Exception("没有提供订单号")
            param_dict = {'order': trades[0].tid}
        elif ident == "send_message_qq_group":
            msg_arguments = self.args.get_send_message_arguments()
            if msg_arguments.skip:
                raise JobSkipError()
            param_dict = {
                "groupname": msg_arguments.qq_group,
                "content": msg_arguments.content,
                "image_urls": msg_arguments.image_urls  # type: ignore[dict-item]
            }
        elif ident == "send_dingtalk_group":
            msg_arguments = self.args.get_send_message_arguments()
            if msg_arguments.skip:
                raise JobSkipError()
            param_dict = {
                "groupname": msg_arguments.dingtalk_group,
                "content": msg_arguments.content,
                "image_urls": msg_arguments.image_urls  # type: ignore[dict-item]
            }
        elif ident == "send_wechat_group":
            msg_arguments = self.args.get_send_message_arguments()
            if msg_arguments.skip:
                raise JobSkipError()
            param_dict = {
                "groupname": msg_arguments.wechat_group,
                "content": msg_arguments.content,
                "image_urls": msg_arguments.image_urls  # type: ignore[dict-item]
            }
        elif ident == "pdd_client_send_msg":
            msg_arguments = self.args.get_send_message_arguments()
            if msg_arguments.skip:
                raise JobSkipError()
            trades = self.args.get_trades()
            if not trades:
                raise Exception("拼多多发消息必须提供订单号")
            param_dict = {
                "pid": self.job.business_order.sid,
                "receiver": trades[0].tid,  # 拼多多发消息不能直接指定收件人，只能使用订单号
                "text": msg_arguments.content,
                "image_url": msg_arguments.image_urls[0] if msg_arguments.image_urls else None
            }
            sender = msg_arguments.sender or {}
            sender_type_value = sender.get("value", "")
            if sender_type_value not in SenderEnum._value2member_map_:
                raise Exception(f"发送类型不支持: [{sender_type_value}]")
            sender_type = SenderEnum(sender_type_value)
            if sender_type == SenderEnum.MANUAL_INPUT:
                # 获取sender
                if not msg_arguments.sender:
                    raise Exception("指定客服为空")
                sub_nick: Optional[List[str]] = msg_arguments.sender.get("extra")
                if not sub_nick:
                    raise Exception("指定客服为空")
                param_dict["sub_nick"] = sub_nick  # type: ignore[assignment]
            elif sender_type == SenderEnum.RANDOM_ONLINE:
                # 随机在线客服路由由rpa-control实现，不需要传sub_nick参数
                ...
            else:
                raise Exception(f"发送类型不支持: [{sender_type.value}]")
        else:
            raise Exception("未知的 rpa client ident")
        return param_dict
