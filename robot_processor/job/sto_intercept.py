from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.enums import JobType
from robot_processor.constants import STO_INTERCEPT_CACHE_KEY_PREFIX
from robot_processor.job.confirm_logistics_intercept import \
    ConfirmLogisticsInterceptExecutor
from robot_processor.job.logistics_intercept import LogisticsInterceptExecutor


class StoInterceptExecutor(LogisticsInterceptExecutor):
    from robot_processor.job.logistics_intercept import InterceptInputModel

    job_type = JobType.STO_INTERCEPT

    def logistics_intercept_cache_prefix(self):
        return STO_INTERCEPT_CACHE_KEY_PREFIX

    def input_params(self) -> "InterceptInputModel":
        from robot_processor.job.logistics_intercept import InterceptInputModel
        return InterceptInputModel(
            waybill_no=self.args.get_arg_by_task_arg_name('waybillNo'),
            logistics_type=LogisticsType.STO.value
        )

    def create_interception_adapter(self):
        return {"waybillNo": self.args.get_arg_by_task_arg_name('waybillNo')}


class StoConfirmInterceptExecutor(ConfirmLogisticsInterceptExecutor):
    from robot_processor.job.confirm_logistics_intercept import InterceptInputModel

    def logistics_intercept_cache_prefix(self):
        return STO_INTERCEPT_CACHE_KEY_PREFIX

    job_type = JobType.STO_CONFIRM_INTERCEPT

    def input_params(self) -> "InterceptInputModel":
        from robot_processor.job.confirm_logistics_intercept import InterceptInputModel

        return InterceptInputModel(
            waybill_no=self.args.get_arg_by_task_arg_name('waybillNo'),
            logistics_type=LogisticsType.STO.value
        )
