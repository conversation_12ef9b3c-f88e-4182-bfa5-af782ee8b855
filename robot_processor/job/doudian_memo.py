import time
from typing import <PERSON><PERSON>

from result import Ok, Err

from robot_processor.utils import ts2date
from loguru import logger

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.mola import MolaClient


class MemoExecutor(AutoJobControllerBase):
    job_type = JobType.DOUDIAN_MEMO

    def process(self) -> <PERSON><PERSON>[JobStatus, str]:
        trades = self.args.get_trades()
        if not trades:
            tid = ''
        else:
            tid = trades[0].tid
        seller_words = self.args.get_arg_by_task_arg_name('content')
        time_flag = self.args.get_arg_by_task_arg_name('time_flag')
        if time_flag:
            seller_words = f"{seller_words} " \
                           f"{ts2date(time.time(), date_format='MM-DD HH:mm')}"
        mode = self.args.get_arg_by_task_arg_name('mode')
        # star 可能为0 所以不用or
        star = self.args.get_arg_by_task_arg_name("star")
        if star is None:
            star = -1

        payload = {
                "order_id": tid,
                "seller_words": seller_words,
                "star": star,
                "mode": mode
        }
        res = MolaClient(self.order.sid).update_doudian_memo(payload)  # type: ignore[arg-type]
        match res:
            case Ok():
                logger.info('抖店备注更新成功, payload={}', payload)
                return JobStatus.SUCCEED, ""
            case Err(error_message):
                logger.warning('抖店备注更新失败, payload={}', payload)
                return JobStatus.FAILED, error_message
