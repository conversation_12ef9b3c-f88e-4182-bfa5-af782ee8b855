"""
退货入库，检查l_id在数据源中是否存在
"""
import datetime
import enum
from typing import List

import arrow
from dramatiq import Retry
from loguru import logger
from result import Ok
from result import Result

from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.utils.global_search_bo import global_search_by_single_keyword
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.form.models import Step
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.rpa_service.models import RpaContext
from robot_processor.shop.models import Shop
from rpa.mola import MolaClient


class ProcessResult(enum.StrEnum):
    已入库 = "已入库"
    未入库 = "未入库"
    部分入库 = "部分入库"


class CheckLidExecutor(AutoJobControllerBase):
    job_type = JobType.CHECK_LID

    l_ids: list[str] = []
    l_company: str | None = None

    def check_rate_limit(self, consume_rate_limit_token=True) -> Result[None, Retry]:
        if self.args.get_arg_by_task_arg_name("data_source") == "form":
            return Ok(None)
        else:
            return super().check_rate_limit(False)

    def get_retry_delay(self) -> int:
        default_retry_delay = 24 * 3600 * 1000

        step: Step | None = Step.query.filter(Step.id == self.job.step_id).first()
        if step is None:
            logger.error("未能找到对应的步骤")
            return default_retry_delay

        rpa_id: int | None = step.data.get("rpa_id")
        if rpa_id is None:
            logger.error("未能找到步骤上配置的 rpa_id")
            return default_retry_delay

        rpa_context: RpaContext | None = RpaContext.query.filter(
            RpaContext.rpa_id == rpa_id, RpaContext.org_id == self.shop.org_id
        ).first()
        if rpa_context is None:
            return default_retry_delay
        return rpa_context.get_common_data().retry_delay or default_retry_delay

    def process(self):
        l_id = self.args.get_arg_by_task_arg_name("l_id")
        if "," in l_id:
            l_ids = l_id.split(",")
        else:
            l_ids = [l_id]
        self.l_ids = l_ids
        self.l_company = self.args.get_arg_by_task_arg_name("l_company")
        try:
            result_list: list[bool]
            if self.args.get_arg_by_task_arg_name("data_source") == "form":
                result_list = self._is_contain_in_bo_data(
                    sids=[shop.sid for shop in Shop.query.filter_by(org_id=self.job_wrapper.shop.org_id).all()],
                    l_ids=l_ids,
                    l_company=self.l_company,
                    form_name=self.args.get_arg_by_task_arg_name("form_name"),
                    l_id_compare_to=self.args.get_arg_by_task_arg_name("l_id_compare_to"),
                    l_company_compare_to=self.args.get_arg_by_task_arg_name("l_company_compare_to"),
                )
            else:
                result_list = self._is_contain_in_erp_data(
                    shop=self.job_wrapper.shop,
                    l_ids=l_ids,
                )
        except Exception as e:
            logger.error(f"检查l_id是否存在时发生错误：{e}")
            return JobStatus.FAILED, str(e)
        if all(result_list):
            r = ProcessResult.已入库
        elif not any(result_list):
            r = ProcessResult.未入库
        else:
            r = ProcessResult.部分入库

        created_at = self.job_wrapper.order.created_at
        delay = self.args.get_arg_by_task_arg_name("process_delay")
        # 未入库，没到截止时间，自动重试
        if r != ProcessResult.已入库 and not self._is_overdue(created_at, delay):
            retry_delay = self.get_retry_delay()
            raise Retry(
                message="还未到查询判断周期，并且还未入库，等待自动重试",
                delay=retry_delay,
            )
        self.states.write_job_output({"match_result": r.value})
        return JobStatus.SUCCEED, None

    @staticmethod
    def _is_contain_in_bo_data(sids, l_ids, l_company, form_name, l_id_compare_to, l_company_compare_to) -> List[bool]:
        """是否存在于指定工单数据中"""
        # 先从es检索
        result_list = []
        for l_id in l_ids:
            # 再从工单数据double check
            if bo_ids := global_search_by_single_keyword(sids, l_id):
                logger.info(f"es 检索到匹配的工单{bo_ids}，开始double check")
                bos = BusinessOrder.query.filter(BusinessOrder.id.in_(bo_ids)).all()
                result_list.append(
                    CheckLidExecutor._double_check_widget_value(
                        bos, form_name, l_id, l_company, l_id_compare_to, l_company_compare_to
                    )
                )
            else:
                result_list.append(False)
        return result_list

    @staticmethod
    def _double_check_widget_value(bos, form_name, l_id, l_company, l_id_compare_to, l_company_compare_to) -> bool:
        """从精确工单数据中在此确认匹配"""
        for bo in bos:
            if bo.form.name != form_name:
                logger.info(f"模板名称不匹配 {bo.id}")
                continue
            if not [
                i for i in bo.jobs[0].raw_step_v2["ui_schema"] if i["option_value"]["label"] == l_id_compare_to
            ]:  # noqa
                logger.info(f"找不到对应组件 l_id_compare_to {bo.id}")
                continue
            l_id_compare_to_uuid = [
                i for i in bo.jobs[0].raw_step_v2["ui_schema"] if i["option_value"]["label"] == l_id_compare_to
            ][0][
                "key"
            ]  # noqa
            if l_id not in bo.data.get(l_id_compare_to_uuid):
                logger.info(f"找不到对应组件 l_id_compare_to {bo.id}")
                continue
            # 快递公司是选填的比较项
            if l_company_compare_to:
                if not [
                    i for i in bo.jobs[0].raw_step_v2["ui_schema"] if i["option_value"]["label"] == l_company_compare_to
                ]:  # noqa
                    logger.info(f"找不到对应组件 l_company_compare_to {bo.id}")
                    continue
                l_company_compare_to_uuid = [
                    i for i in bo.jobs[0].raw_step_v2["ui_schema"] if i["option_value"]["label"] == l_company_compare_to
                ][0][
                    "key"
                ]  # noqa
                if bo.data.get(l_company_compare_to_uuid) != l_company:
                    logger.info(f"找不到对应组件 l_company_compare_to {bo.id}")
                    continue
            return True
        return False

    @staticmethod
    def _is_contain_in_erp_data(shop, l_ids) -> List[bool]:
        """是否存在于erp数据中"""
        result_list = [MolaClient(shop.sid).check_tracking_number_registered(l_id) for l_id in l_ids]
        return result_list

    @staticmethod
    def _is_overdue(created_at: int, delay: int):
        """是否逾期"""
        return arrow.get(created_at) + datetime.timedelta(days=int(delay)) < arrow.now()
