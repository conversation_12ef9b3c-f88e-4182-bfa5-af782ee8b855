from dramatiq.errors import Retry

from robot_processor.enums import JobStatus, JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.job.jst_trade import TradesFinder
from rpa.erp.jst import JstNewSDK, JstSDK, WmsQueryResp, JstLimitError


class JstModifyWmsExecutor(AutoJobControllerBase):
    """
    如果客户的使用场景是指定补发单的仓库为原单仓库，建议在回执阶段回写仓库编号，然后在这里作为输入
    如果客户的使用场景涉及拆分单，建议配置子订单号，精准获取仓库编号
    """
    job_type = JobType.JST_MODIFY_WMS

    def process(self):
        # 内部订单号
        o_id = self.args.get_arg_by_task_arg_name("o_id")
        match o_id:
            case [{"tid": tid}, *_]:
                o_id = tid  # type: ignore[unreachable]
            case str() | int():
                o_id = o_id
        # 是否需要指定为子订单号的仓库
        oi_id = self.args.get_arg_by_task_arg_name("oi_id")
        # 仓库编号（优先级最高）
        wms_co_id = self.args.get_arg_by_task_arg_name("wms_co_id")
        # 仓库名称
        wms_co_name = self.args.get_arg_by_task_arg_name("wms_co_name")

        try:
            if not wms_co_id:
                # 如果配置了 oi_id（是否需要指定为子订单号的仓库），则仓库名不会起效。
                if oi_id:
                    tid = None
                    trade_oid = None
                    if trades := self.args.get_trades():
                        tid = trades[0].tid
                        trade_oid = trades[0].oid
                    wms_co_id = self.get_wms_co_id_by_tid_and_o_id(tid, trade_oid, self.shop)
                elif wms_co_name:
                    if isinstance(wms_co_name, list):
                        wms_co_name = wms_co_name[0]
                    jst_skd = JstSDK(self.shop.sid)
                    resp: WmsQueryResp = jst_skd.get_wms_info_by_org_id(self.job_wrapper.shop.org_id)
                    wms_infos = [info for info in resp.datas if info.name == wms_co_name]
                    wms_co_id = wms_infos[0].wms_co_id
                else:
                    return JobStatus.FAILED, "参数错误, 子订单号和仓库编号和仓库名称不能同时为空"

            JstNewSDK(self.shop.sid).modify_wms(o_id, wms_co_id)
        except JstLimitError as e:
            raise Retry(str(e), delay=1000)
        except Exception as e:
            return JobStatus.FAILED, str(e)
        return JobStatus.SUCCEED, None

    @staticmethod
    def get_wms_co_id_by_tid_and_o_id(tid, oi_id, shop):
        """
        通过指定的原子订单号，需要修改的内部单号，获取发货仓编号
        """
        orders = TradesFinder(tid=tid, oi_id=oi_id, shop=shop, is_reissue="0").process()
        order = orders[0]
        return order.wms_co_id
