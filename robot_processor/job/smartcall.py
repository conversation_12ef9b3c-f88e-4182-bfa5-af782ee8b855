import re

import phonenumbers
from loguru import logger
from result import Ok, Err

from robot_processor.enums import JobStatus, JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.smartcall.client import SmartCallClient


def _ensure_plain_value(value):
    # hack: 外呼只接收值类型（数字 文本等）作为入参
    if value and isinstance(value, (list, tuple)):
        return value[0]
    else:
        return value


class SmartCallExecutor(AutoJobControllerBase):
    job_type = JobType.SMARTCALL

    def process(self):
        shop = self.job_wrapper.shop
        if not shop:
            logger.info("没找到工单对应店铺")
            return JobStatus.FAILED, "没找到工单对应店铺"

        smartcall_info = shop.smartcalls.filter_by(shop_id=shop.id).first()
        if not smartcall_info:
            logger.info(f"店铺 {shop.sid} 未配置智能外呼信息:")
            return JobStatus.FAILED, "店铺未配置智能外呼信息"

        graph_main_id = _ensure_plain_value(self.args.get_arg_by_task_arg_name("graph_main_id"))
        if not graph_main_id:
            return JobStatus.FAILED, "未获取到外呼场景"

        fixed_params = ['mobile', 'graph_main_id']
        param_map = {"business_order_id": self.job_wrapper.order.id,
                     "step_id": self.job.step_id}

        mobile = self.args.get_arg_by_task_arg_name("mobile")
        if not mobile:
            return JobStatus.FAILED, "未获取到手机号"

        mobile = self.get_valid_phone_number(mobile)
        if not mobile:
            return JobStatus.FAILED, f"手机号格式不正确:{mobile}"

        for arg_name, widget_key in self.args.key_map.items():
            if arg_name in fixed_params:
                continue
            arg_value = self.args.get_arg_by_task_arg_name(arg_name)
            if arg_value and isinstance(arg_value, str):
                param_map[arg_name] = _ensure_plain_value(arg_value)
            else:
                param_map[arg_name] = _ensure_plain_value(
                    self.args.get_and_render_brief_value_by_widget_key(widget_key)
                )

        param_map['店铺名称'] = self.args.get_arg_by_task_arg_name('店铺名称') or shop.title

        res = (
                SmartCallClient(shop=self.job_wrapper.shop, smartcall_info=smartcall_info).
                start_call(mobile=mobile, graph_main_id=graph_main_id, param_map=param_map)
            )
        match res:
            case Ok(result):
                self.states.write_job_states(result)
                logger.info(f"外呼成功, {result=} {mobile=} {graph_main_id=} {param_map=}")
                return JobStatus.RUNNING, None
            case Err(error_message):
                logger.error(f"外呼失败, error={error_message} {mobile=} {graph_main_id=} {param_map=}")
                return JobStatus.FAILED, error_message

    @staticmethod
    def get_valid_phone_number(raw):
        raw = str(raw)
        regex = re.compile(r'\+*(86)*-*(1\d{10})-*(\d*)')
        results = regex.findall(raw)
        if results:
            country_code, mobile, extension = results[0]
            number = phonenumbers.PhoneNumber(
                country_code=country_code if country_code else '86',  # type: ignore[arg-type]
                national_number=mobile,
                extension=extension if extension else None
            )
            if phonenumbers.is_valid_number_for_region(number, 'CN'):
                national_number = number.national_number
                return f"{national_number}-{extension}" if extension else str(national_number)
