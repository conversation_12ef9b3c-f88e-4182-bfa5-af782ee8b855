from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.constants import YD_INTERCEPT_CACHE_KEY_PREFIX
from robot_processor.enums import JobType
from robot_processor.job.logistics_intercept import InterceptInputModel
from robot_processor.job.logistics_intercept import Logistics<PERSON>nterceptExecutor
from rpa.yundaex.schemas import InterceptInvokeExpressInte


class YDInterceptReportExecutor(LogisticsInterceptExecutor):
    job_type = JobType.YD_INTERCEPT_REPORT

    def logistics_intercept_cache_prefix(self):
        return YD_INTERCEPT_CACHE_KEY_PREFIX

    def input_params(self):
        return InterceptInputModel(
            waybill_no=self.args.get_arg_by_task_arg_name("waybill_no") or "",
            logistics_type=LogisticsType.YD.value
        )

    def init_logistics_domain(self):
        return {
            "shop": self.shop,
            "auth_account": self.args.get_arg_by_task_arg_name("auth_account")
        }

    def create_interception_adapter(self):
        start_site_code = self.args.get_arg_by_task_arg_name("start_site_code")
        intercept_sponsor_name = self.args.get_arg_by_task_arg_name("intercept_sponsor_name")
        remark = self.args.get_arg_by_task_arg_name("remark")
        return InterceptInvokeExpressInte(
            shipId=self.input_params().waybill_no,
            startSiteCode=start_site_code,
            interceptSponsorName=intercept_sponsor_name,
            remark=remark
        )
