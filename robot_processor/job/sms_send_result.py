from dramatiq import Retry
from loguru import logger

from robot_processor.enums import JobStatus, JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.sms.models import SMSSendRecord


class SMSSendResultExecutor(AutoJobControllerBase):
    job_type = JobType.SMS_SEND_RESULT

    def process(self):
        uid = self.args.get_arg_by_task_arg_name("uid")
        record = SMSSendRecord.query.filter_by(uid=uid).first()
        if not record:
            return JobStatus.FAILED, f"没有短信发送记录{uid}"
        if record.status == -1:
            raise Retry(delay=10000)
        if record.status == 0:
            return JobStatus.SUCCEED, ""
        logger.warning(f"短信发送失败 status: {record.status} desc: {record.desc}")
        return JobStatus.FAILED, f"短信发送失败 status: {record.status}"
