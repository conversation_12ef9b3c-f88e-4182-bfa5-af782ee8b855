from pydantic import BaseModel

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.jackyun.schemas import OrderUpdateLogisticsInfoReq
from rpa.erp.jackyun.sdk import JackyunSDK


class OutputModel(BaseModel):
    success: bool


class JackyunUpdateLogisticsInfoExecutor(AutoJobControllerBase):
    job_type = JobType.JACKYUN_UPDATE_LOGISTICS_INFO
    output_model = OutputModel

    def get_logistic_name(self):
        logistic_name = self.args.get_arg_by_task_arg_name("logistic_name")
        if not logistic_name:
            return None
        elif isinstance(logistic_name, list):
            return logistic_name[0]
        return logistic_name

    def process(self):
        trade_no = self.args.get_arg_by_task_arg_name("trade_no")
        seller_memo = self.args.get_arg_by_task_arg_name("seller_memo")
        logistic_name = self.get_logistic_name()
        if not logistic_name:
            return JobStatus.FAILED, "未指定物流公司"
        logistic_resp = JackyunSDK(self.shop.sid).logistics_get()
        logistic_infos = [info for info in logistic_resp.result.data.logisticInfo if info.logisticName == logistic_name]
        if not logistic_infos:
            return JobStatus.FAILED, "未找到指定物流公司"
        logistic_code = logistic_infos[0].logisticCode
        logistic_no = self.args.get_arg_by_task_arg_name("logistic_no")
        req = OrderUpdateLogisticsInfoReq(erporderNo=trade_no, logisticCode=logistic_code, logisticNo=logistic_no)
        if seller_memo:
            req.sellerMemo = seller_memo
        update_resp = JackyunSDK(self.shop.sid).order_update_logsitics_info(req)
        if update_resp.code == 200:
            self.states.write_job_widget_collection({"success": True})
            return JobStatus.SUCCEED, None
        self.states.write_job_widget_collection({"success": False})
        return JobStatus.FAILED, update_resp.msg
