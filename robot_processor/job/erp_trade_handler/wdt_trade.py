"""
旺店通企业版订单信息回执
"""
import json
import time
from typing import List

from dramatiq import Retry
from loguru import logger

from robot_processor.client.conf import app_config
from robot_processor.enums import ErpType
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.error.client_request import WdtRequestError
from robot_processor.ext import cache
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.job.erp_trade_handler.exceptions import ErpFilteredError
from robot_processor.job.erp_trade_handler.exceptions import ErpTradeNotFound
from robot_processor.job.job_model_wrapper import JobArguments
from robot_processor.job.job_model_wrapper import JobStates
from robot_processor.job.utils import get_valid_tracking_numbers
from robot_processor.job.utils import split_string_to_list
from robot_processor.shop.models import ErpInfo
from rpa.erp.wdt import BatchInfo
from rpa.erp.wdt import StockoutOrderQueryTradeResp
from rpa.erp.wdt import Trade
from rpa.erp.wdt import WdtClient
from rpa.erp.wdt import WdtOpenAPIClient
from rpa.erp.wdt import WdtRateLimitError
from rpa.erp.wdt import WdtTradeOutput


class WdtTradeType:
    NORMAL = 1
    REISSUE = 7


class WdtTradeExecutor(AutoJobControllerBase):
    job_type = JobType.WDT_TRADE

    def get_rate_limit_setting(self, conf: dict) -> tuple[str, str] | None:
        # 查询店铺绑定的旺店通企业版的授权
        erp_info: ErpInfo | None = (
            self.job_wrapper.shop.erps.filter(ErpInfo.erp_type == ErpType.WDT).order_by(ErpInfo.id.desc()).first()
        )
        if erp_info is None:
            return None

        wdt_sid = erp_info.meta.get("sid")
        if not wdt_sid:
            return None
        # 多包裹相关接口有1分钟60次的限流，但订单查询本身没有限流，因此只有在多包裹被配置输出时做限流
        if self.args.is_argument_configured_in_task_arguments("orders[].is_multi_packages"):
            limit_key = f"JOB_LIMITER_{self.job_type}_WDT_SID_{wdt_sid}"
            return limit_key, "55/minute"
        return None

    def process(self):
        wdt_client = WdtClient(self.order.sid)
        wdt_open_api_client = WdtOpenAPIClient(self.order.sid)
        # 支持多订单，任何一笔挂了就算失败，因为写bo data是覆盖性的行为
        if arg_trades := self.args.get_trades("wdt_tid"):
            bo_trades = self.args.merge_oid_in_trade(arg_trades)
            try:
                all_trades = []
                for idx, bo_trade in enumerate(bo_trades):
                    tid = bo_trade.tid
                    oids = bo_trade.oids
                    logistics_no = self.args.get_arg_by_task_arg_name("orginal_logistics_no")
                    trade_no = self.args.get_arg_by_task_arg_name("trade_no")
                    trades = TradeFinder.process(
                        tid=tid,
                        logistics_no=logistics_no,
                        trade_no=trade_no,
                        shop=self.job_wrapper.shop,
                        trade_type=self.args.get_arg_by_task_arg_name("trade_type") or "all",  # 飞梭定义的订单类型,
                        src_oids=oids if self.args.is_argument_configured("src_oid") else None,
                        ignore_gift=(self.args.get_arg_by_task_arg_name("ignore_gift") == "True"),  # 是否需要赠品
                        goods_refund_status_list=self.args.get_arg_by_task_arg_name("goods_refund_status_list"),
                        ignore_not_found_error=self.args.get_arg_by_task_arg_name("ignore_not_found_error") or False,
                    )
                    all_trades.extend(trades)
                    # 避免限流
                    if idx != len(bo_trades) - 1:
                        time.sleep(2)
                adder = ExtraInfoAdder(all_trades, self.args, wdt_client, wdt_open_api_client)
                adder.process()
                self._save_data(all_trades)

            except Retry as e:
                raise e
            except Exception as e:
                logger.warning(f"旺店通订单信息回执失败: {e}")
                return JobStatus.FAILED, str(e)

        return JobStatus.SUCCEED, ""

    def _save_data(self, all_trades):
        DataSaver.process(all_trades, self.args.get_arg_by_task_arg_name("is_need_merge"), self.states)


class WdtTradeInfoExecutor(WdtTradeExecutor):
    job_type = JobType.WDT_TRADE_INFO
    output_model = WdtTradeOutput

    def _save_data(self, all_trades):
        trades_raw = [trade.dict() for trade in all_trades]
        res = self.states.write_job_widget_collection(data={"orders": trades_raw, "count": len(trades_raw)})
        status_str = ",".join([str(trade.trade_status_zh) for trade in all_trades])
        if res.missing:
            missing_str = " / ".join(arg.readable for arg in res.missing)
            raise Exception(f"当前订单状态：{status_str} 获取输出字段失败: {missing_str}")


class TradeFinder:
    @staticmethod
    def process(
        *,
        shop,
        tid,
        src_oids: list[str] | None = None,
        trade_no: str | None = None,
        trade_type: str = "all",
        logistics_no: str | None = None,
        ignore_gift: bool = False,
        goods_refund_status_list: list[int] | None = None,
        ignore_not_found_error: bool = False,
    ) -> List[Trade]:
        if shop.org_id in app_config.allow_invalid_tracking_number:
            logistics_nos = split_string_to_list(logistics_no)
        else:
            logistics_nos = get_valid_tracking_numbers(logistics_no)
        trade_nos = split_string_to_list(trade_no)
        wdt_client = WdtClient(shop.sid)
        all_trades: List[Trade] = []
        if logistics_nos:
            for logistics_no in logistics_nos:
                # 传订单号可以作为唯一条件查询准确的一笔订单,但是不支持传多个，只能挨个查询
                trades = wdt_client.trade_query(
                    logistics_no=logistics_no, check_rate_limit=True, blocking=True
                ).response.trades
                all_trades.extend(trades or [])
        elif trade_nos:
            for tn in trade_nos:
                # 系统订单编号，如果使用订单编号，其余参数不起效，其余参数可以不传
                # 可以作为唯一条件查询准确的一笔订单,但是不支持传多个，只能挨个查询
                trades = wdt_client.trade_query(trade_no=tn, check_rate_limit=True, blocking=True).response.trades
                all_trades.extend(trades or [])
        else:
            resp = wdt_client.trade_query(wdt_tid=tid, trade_no=trade_no, check_rate_limit=True)
            all_trades = resp.response.trades or []
            if not ignore_not_found_error and not all_trades:
                raise ErpTradeNotFound()
        # 在原店补发功能上线之后，可以依靠trade_type来区分普通订单trade_type=1和补发订单trade_type=7
        if trade_type != "all":
            if trade_type == "reissue":
                all_trades = [trade for trade in all_trades if str(trade.trade_type) == str(WdtTradeType.REISSUE)]
            elif trade_type == "normal":
                all_trades = [trade for trade in all_trades if str(trade.trade_type) == str(WdtTradeType.NORMAL)]
            if not ignore_not_found_error and not all_trades:
                raise ErpFilteredError("找不到符合查询条件的订单 筛选条件: 订单类型")
        # 过滤子订单号
        if src_oids:
            for trade in all_trades:
                # todo 暂时先给出所有的赠品，后续如果商家有不同的需求，做成输出配置
                trade.goods_list = [
                    good for good in trade.goods_list if (good.src_oid in src_oids or good.gift_type != 0)
                ]
                paid = 0.0
                for good in trade.goods_list:
                    paid += float(good.paid or 0.0)
                trade.paid = str(paid)
            all_trades = [trade for trade in all_trades if trade.goods_list]
            if not all_trades:
                raise ErpFilteredError("找不到符合查询条件的订单 筛选条件: 子订单号")
        # ref: https://open.wangdian.cn/qyb/open/apidoc/doc?path=trade_query.php

        if ignore_gift:
            for trade in all_trades:
                # 0非赠品 1自动赠送 2手工赠送 3回购自动送赠品  4前N有礼送赠品 6天猫优仓赠品 7淘宝CRM会员送赠
                trade.goods_list = [good for good in trade.goods_list if good.gift_type == 0]
            all_trades = [i for i in all_trades if i.goods_list]

        # 前端某些情况，会传一个空的列表过来。在此利用类型的隐式转换来作为判断依据。
        if goods_refund_status_list:
            for trade in all_trades:
                # 货品退款状态 0无退款,1取消退款,2已申请退款,3等待退货,4等待收货,5退款成功 6已关闭
                trade.goods_list = [good for good in trade.goods_list if good.refund_status in goods_refund_status_list]
            all_trades = [i for i in all_trades if i.goods_list]

        if json.loads(app_config.WDT_IGNORE_CANCELED_WHITE_LIST).get(shop.sid):
            # 过滤“已取消”的订单。
            all_trades = [trade for trade in all_trades if trade.trade_status != 5]
            if not all_trades:
                raise ErpFilteredError("找不到符合查询条件的订单 筛选条件: 订单状态")
        return all_trades


class DataSaver:
    @staticmethod
    def process(trades: List[Trade], need_merge: bool, states: JobStates):
        concerned_fields = [
            "logistics_name",
            "paid",
            "logistics_no",
            "warehouse_no",
            "warehouse_name",
            "goods_no",
            "goods_name",
            "spec_no",
            "spec_name",
            "trade_status_zh",
            "all_goods_info",
            "has_split_order",
        ]
        # 对于关心的输出项，如果要回写到复合组件，按具体的输出项纬度进行拆分
        # 例如： orderA: good1,good2   orderB: good3,good4
        # 需要回写的是订单号，只需要 [{"order_no":"A"}, {"order_no":"B"}]
        # 需要回写的是商品编码+订单号，需要
        # [{"order_no":"A", "goods_no":"good1"}, {"order_no":"B", "goods_no":"good3"},{"order_no":"B",
        # "goods_no":"good4"}]
        # 或者[{"order_no":"A", "goods_no":["good1", "good2"]}, ...]

        # 如果要回写到单行输入，先merge到一个订单(取决于是否需要合并)，用逗号分开，再回写
        # 下面准备好两份数据，一份是单行（single_trade），一份是多行（trades）
        single_trade = Trade.merge_trades(trades) if need_merge else trades[0]

        to_multi_fields = [i for i in concerned_fields if states.check_widget_is_table_by_arg_name(i)]
        to_single_fields = [i for i in concerned_fields if i not in to_multi_fields]

        to_single_data = {}
        for field in to_single_fields:
            to_single_data[field] = getattr(single_trade, field)

        # 对于单行输出，直接用write_job_output回写
        missing_args = states.write_job_output(to_single_data, allow_empty_value=False)
        if missing_args:
            raise Exception(f"当前订单状态：{to_single_data.get('trade_status_zh')} 获取输出字段失败: {missing_args}")

        # 对于多行输出，需要先把数据拆分成多行，再回写
        # todo fixme 目前只支持一层，sku级别不支持，等更完整的方案（例如复合组件嵌套）
        to_multi_rows = []
        for trade in trades:
            row = {}
            for field in to_multi_fields:
                row[field] = getattr(trade, field)
            if row:
                # to_multi_fields不为空（代表存在复合组件），才能添加数据
                to_multi_rows.append(row)
        trades_status_str = ",".join([str(trade.trade_status_zh) for trade in trades])
        missing_args = states.write_job_output_with_multi_row(to_multi_rows)
        if missing_args:
            raise Exception(f"当前订单状态：{trades_status_str} 获取输出字段失败: {missing_args}")


class ExtraInfoAdder:
    def __init__(
        self, trades: List[Trade], args: JobArguments, wdt_client: WdtClient, wdt_open_api_client: WdtOpenAPIClient
    ):
        self.trades = trades
        self.args = args
        self.wdt_client = wdt_client
        self.wdt_open_api_client = wdt_open_api_client

    def process(self):
        trades = self.trades
        for trade in trades:
            self._get_wdt_warehouse_name(trade)
            self._get_batch_no_list(trade)
            self._get_is_multi_packages(trade)

    def _get_wdt_warehouse_name(self, trade: Trade):
        ret = []
        if warehouse_no := trade.warehouse_no:
            warehouse_nos = warehouse_no.split(",")
            for warehouse_no_ in warehouse_nos:
                cache_key = "wdt.warehouse.query:{}:{}".format(self.wdt_client.sid, warehouse_no_)
                if cached_warehouse_name := cache.get(cache_key):
                    ret.append(cached_warehouse_name)
                else:
                    try:
                        wh_resp = self.wdt_client.warehouse_query(warehouse_no_)
                        if wh_resp.response.errorcode == 0:
                            warehouse_name = wh_resp.response.warehouses[0].name
                            cache.set(cache_key, warehouse_name, timeout=3600)
                            ret.append(warehouse_name)
                    except WdtRateLimitError as e:
                        raise Retry(str(e), delay=3000)
                    except Exception as e:
                        logger.error("查询仓库信息时发生错误: {}", e)

        if ret:
            trade.warehouse_name = ",".join(ret)
        else:
            trade.warehouse_name = f"找不到分仓信息，请检查分仓编码是否正确 {warehouse_no}"

    def _get_is_multi_packages(self, trade: Trade):
        if self.args.is_argument_configured_in_task_arguments("orders[].is_multi_packages"):
            trade.is_multi_packages = False
            try:
                multi_packages_resp = self.wdt_open_api_client.logistics_multi_query(trade.trade_no)
                if multi_packages_resp.logistics_multi_list and len(multi_packages_resp.logistics_multi_list) > 1:
                    trade.is_multi_packages = True
            except WdtRequestError as e:
                if "订单不存在" not in e.message:
                    raise e

    def _get_batch_no_list(self, trade: Trade):
        if not self.args.is_argument_configured_in_task_arguments(
            "orders[].goods_list[].batch_list[].batch_no"
        ) and not self.args.is_argument_configured_in_task_arguments("orders[].consign_time"):
            return
        resp: StockoutOrderQueryTradeResp = self.wdt_client.stockout_order_query_trade(trade.trade_no)
        for stockout in resp.response.stockout_list:
            trade.consign_time = stockout.consign_time
            for detail in stockout.details_list:
                for position in detail.position_list:
                    for goods in trade.goods_list:
                        if goods.spec_no == detail.spec_no and position.stockout_order_detail_id == str(detail.rec_id):
                            batch = BatchInfo(
                                batch_id=position.batch_id,
                                batch_no=position.batch_no,
                                position_no=position.position_no,
                                position_goods_count=int(float(position.position_goods_count)),
                            )
                            goods.batch_list.append(batch)
