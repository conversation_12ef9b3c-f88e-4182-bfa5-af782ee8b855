"""
ERP订单信息回执abc
"""
from typing import List

from robot_processor.job.auto_job import AutoJobControllerBase  # noqa ligth merge 冲突
from robot_processor.job.job_model_wrapper import JobStates, JobArguments
from robot_processor.shop.models import Shop


class TradeExecutor(AutoJobControllerBase):
    def process(self):
        pass


class ErpOrderExtraInfoMixin:
    """通过订单接口获取一条订单详情时，需要补充的其他信息"""

    pass


class ErpOrder:
    """表示一条erp中订单"""
    extra_info_cls = ErpOrderExtraInfoMixin
    pass


class TradeFinder:
    """
    用来查询订单，过滤订单，类里面定义的方法尽可能全部是静态方法，便于调试
    """

    @staticmethod
    def try_find_orders(*args, need_filter=True) -> List[ErpOrder]:
        return []


class TradeFilter:
    @staticmethod
    def filter_orders(orders: List[ErpOrder]) -> List[ErpOrder]:
        return []


class SplitMergeHelper:
    @staticmethod
    def get_full_orders(order: ErpOrder, shop: Shop):
        pass


class DataSaver:
    """
    存储获取到的数据信息
    后续统一用write_job_widget_collection实现
    """
    def __init__(self, args: JobArguments, states):
        self.args: JobArguments = args
        self.states: JobStates = states
        self.is_need_merge = args.get_arg_by_task_arg_name("is_need_merge")

    def process(self, orders: List[ErpOrder]):
        pass
