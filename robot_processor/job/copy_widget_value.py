from result import Err, Ok

from robot_processor.enums import JobType, JobStatus
from robot_processor.form.models import FormSymbol, WidgetRef
from robot_processor.job.auto_job import AutoJobControllerBase


class CopyWidgetValueExecutor(AutoJobControllerBase):
    job_type = JobType.COPY_WIDGET_VALUE

    def process(self):
        check_res = self.ensure_same_type()
        if check_res.is_err():
            return JobStatus.FAILED, check_res.unwrap_err()
        self.states.write_job_output(
            {"copy_to": self.args.get_arg_by_task_arg_name("copy_from")}
        )
        return JobStatus.SUCCEED, None

    def ensure_same_type(self):
        try:
            copy_from = WidgetRef.validate(self.args.key_map["copy_from"])
            copy_to = WidgetRef.validate(self.args.key_map["copy_to"])
        except Exception as e:
            return Err(ValueError(f"解析组件配置失败 {e}"))
        if not (copy_from.depth == copy_to.depth == 1):
            return Err(ValueError("不支持跨层级复制"))
        version_no = self.order.form_version.version_no
        symbol_table_res = FormSymbol.query_symbol_table(self.order.form_id, version_no)  # type: ignore[arg-type]
        if symbol_table_res.is_err():
            return symbol_table_res
        symbol_table = symbol_table_res.unwrap()

        def find_symbol(name: str):
            for namespace in symbol_table.namespaces:
                for form_symbol in namespace.symbols:
                    if form_symbol.name == name:
                        return form_symbol
            return None

        copy_from_symbol = find_symbol(copy_from.key)
        copy_to_symbol = find_symbol(copy_to.key)
        if not copy_from_symbol:
            return Err(ValueError(f"未找到组件 {copy_from.key}"))
        if not copy_to_symbol:
            return Err(ValueError(f"未找到组件 {copy_to.key}"))
        if copy_from_symbol.type_spec.is_match(copy_to_symbol.type_spec):
            return Ok(None)
        else:
            return Err(
                TypeError(
                    f"组件类型不匹配: {copy_from_symbol.type_spec} != {copy_to_symbol.type_spec}"
                )
            )
