import time
import typing as t

from flask import current_app
from pydantic import BaseModel

from robot_processor.client.logistics_clients.enum import LogisticsType, \
    InterceptionStatus
from robot_processor.client.logistics_clients.logistics_client import \
    AbstractLogisticsIntercept
from robot_processor.enums import JobStatus
from robot_processor.ext import cache
from robot_processor.job import AutoJobControllerBase


class InterceptOutputModel(BaseModel):
    interceptStatus: str
    errorMsg: t.Optional[str] = ""
    suggestion: t.Optional[str] = ""
    matchErrorType: t.Optional[int]


class InterceptInputModel(BaseModel):
    """
    额外的字段可以继承自这个类
    """
    waybill_no: str
    logistics_type: str


class ConfirmLogisticsInterceptExecutor(AutoJobControllerBase):
    output_model = InterceptOutputModel

    def logistics_intercept_cache_prefix(self):
        raise NotImplementedError

    def init_logistics_domain(self) -> dict:
        return dict()

    def input_params(self) -> "InterceptInputModel":
        raise NotImplementedError

    def on_wait_timeout(self, check_type=None):
        """等待拦截结果超时"""

    def on_interception_success(self, check_type=None):
        """已确认拦截结果，且拦截成功"""
        cache.delete(self.cache_key())

    def on_interception_failed(self, check_type=None):
        """已确认拦截结果，且拦截失败"""
        cache.delete(self.cache_key())

    def has_interception_record(self):
        return cache.get(self.cache_key()) is not None

    def spin_wait_hours(self):
        return int(current_app.config.get("SPIN_WAIT_HOURS", 4))

    def logistics_wait_max_timeout_days(self):
        return int(current_app.config.get("LOGISTICS_CALLBACK_WAIT_MAX_TIMEOUT_DAYS", 2))

    def spin(self):
        from robot_processor.business_order.tasks import package_jobs

        package_jobs.send_with_options(args=(self.job.business_order_id, self.job.id),
                                       delay=1000 * 60 * 60 * self.spin_wait_hours())

    def check_wait_max_timeout(self):
        return int(time.time()) - self.job.created_at > self.logistics_wait_max_timeout_days() * 24 * 60 * 60

    def write_data_final(self, intercept_status: InterceptionStatus, suggestion=None, error_msg=None):
        data = InterceptOutputModel(interceptStatus=intercept_status.value, suggestion=suggestion, errorMsg=error_msg)
        self.states.write_job_widget_collection(data.dict())

    def process_by_interception_status(self, interception_status, error_msg, check_type):

        self.write_data_final(interception_status, None, error_msg)

        if interception_status == InterceptionStatus.SUCCESS:
            self.on_interception_success(check_type)
            return JobStatus.SUCCEED, None
        elif interception_status == InterceptionStatus.FAILED:
            self.on_interception_failed(check_type)
            return JobStatus.FAILED, error_msg
        elif interception_status == InterceptionStatus.INTERCEPTION_WAIT:
            self.spin()
            # FIXME: 不应该有长期 RUNNING 状态的任务
            return JobStatus.RUNNING, None
        elif interception_status == InterceptionStatus.WAIT_TIMEOUT_MAX:
            self.on_wait_timeout(check_type)
            return JobStatus.FAILED, error_msg
        elif interception_status == InterceptionStatus.UNKNOWN:
            # 拦截状态不明则也进入自旋，直到获取到一个明确的结果。
            self.spin()
            return JobStatus.RUNNING, None

        return JobStatus.RUNNING, None

    def cache_key(self):
        waybill_no = self.input_params().waybill_no
        prefix = self.logistics_intercept_cache_prefix()
        return f"{prefix}{waybill_no}"

    def cache_value(self):
        return f"{self.job.business_order_id}#{str(self.job.id)}"

    def before_check(self):
        waybill_no = self.input_params().waybill_no
        logistics_type = self.input_params().logistics_type
        if not (waybill_no and logistics_type):
            return False, "运单号和物流公司不得为空！"
        return True, None

    def process(self) -> t.Tuple[JobStatus, t.Union[str, Exception, None]]:
        is_ok, error_msg = self.before_check()
        if not is_ok:
            return JobStatus.FAILED, error_msg

        waybill_no = self.input_params().waybill_no
        logistics_type = self.input_params().logistics_type
        logistics_domain = AbstractLogisticsIntercept(LogisticsType(logistics_type)).dispatch()
        logistics_domain.init(self.init_logistics_domain())
        # 等待时间超长
        if self.check_wait_max_timeout():
            interception_status, error_msg = InterceptionStatus.WAIT_TIMEOUT_MAX, "等待拦截状态刷新超时"
            check_type = None
        else:
            # 主动检查拦截状态
            interception_status, error_msg, check_type = logistics_domain.check_interception_status_by_self(
                    waybill_no, self.has_interception_record())
        return self.process_by_interception_status(interception_status, error_msg, check_type)
