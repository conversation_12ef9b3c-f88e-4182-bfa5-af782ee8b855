from flask import current_app
from loguru import logger

from robot_processor.enums import JobStatus, JobType
from robot_processor.job.auto_job import AutoJobControllerBase


class Redmine569481Executor(AutoJobControllerBase):
    job_type = JobType.REDMINE_569481

    def process(self):
        form_id = self.job_wrapper.form_id()
        logger.info(f"{form_id}")
        image_map = current_app.config.get("REDMINE_569481", {})
        if not image_map.get(str(form_id)):
            return JobStatus.FAILED, "没有图片配置"
        self.states.write_job_output({"image": image_map.get(str(form_id))})
        return JobStatus.SUCCEED, ""
