from typing import List

from dramatiq import Retry
from pydantic import BaseModel

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.wdtulti import StockinRefundReturnLogisticsPackage
from rpa.erp.wdtulti import WdtRateLimitError
from rpa.erp.wdtulti import WdtUltiOpenAPIClient


class Output(BaseModel):
    packages: List[StockinRefundReturnLogisticsPackage]


class WdtultiStockinRefundReturnLogisticsPackageQueryExecutor(AutoJobControllerBase):
    job_type = JobType.WDTULTI_STOCKIN_REFUND_RETURN_LOGISTICS_PACKAGE_QUERY
    output_model = Output
    """
    旺店通旗舰版退货物流包裹查询
    """

    def process(self):
        logistics_no = self.args.get_arg_by_task_arg_name("logistics_no")
        if not logistics_no:
            return JobStatus.FAILED, "没有物流单号"

        try:
            resp = WdtUltiOpenAPIClient(self.shop.sid).stockin_refund_return_logistics_package_query(logistics_no)
        except WdtRateLimitError as e:
            raise Retry(message=str(e), delay=60 * 1000)
        self.states.write_job_widget_collection(Output(packages=resp.data.record).dict())
        return JobStatus.SUCCEED, ""
