import typing as t

from pydantic import parse_obj_as

from robot_processor.client import sto_client
from robot_processor.client.logistics_clients.schema import \
    StoQueryTraceData, StoWaybillItem
from robot_processor.enums import JobType, JobStatus
from robot_processor.job import AutoJobControllerBase
from robot_processor.client.logistics_clients.utils import get_filtered_traces
from robot_processor.client.logistics_clients.enum import LogisticsType


class StoQueryTraceExecutor(AutoJobControllerBase):
    job_type = JobType.STO_QUERY_TRACE
    output_model = StoQueryTraceData

    def process(self) -> t.Tuple[JobStatus, t.Union[str, Exception, None]]:
        waybill_no = self.args.get_arg_by_task_arg_name('waybillNo')
        get_trace_records_method = self.args.get_arg_by_task_arg_name("get_trace_records_method")
        data = {"waybillNoList": [waybill_no]}
        resp = sto_client.query_trace(data)
        if not resp:
            return JobStatus.FAILED, "请求申通失败"
        if resp.get("success") == "false":
            return JobStatus.FAILED, f"申通请求报错:{resp.get('expInfo', '')}"
        # 提取data中的数组部分
        data_array = next(iter(resp.get("data", {}).values()), [])  # type: ignore[var-annotated]
        # 实例化为StoQueryTraceData模型
        trace_data = StoQueryTraceData(response_items=parse_obj_as(t.List[StoWaybillItem], data_array))

        filtered_traces, err = get_filtered_traces(
            trace_data.response_items,
            logistic_type=LogisticsType.STO,
            get_trace_records_method=get_trace_records_method
        )
        if err:
            return JobStatus.FAILED, err
        trace_data.response_items = filtered_traces
        self.states.write_job_widget_collection(trace_data.dict())
        return JobStatus.SUCCEED, None
