from typing import Union, <PERSON><PERSON>

from dramatiq import Retry
from loguru import logger

from robot_processor.error.client_request import JstRequestError
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.enums import JobStatus, JobType
from robot_processor.job.jst_trade import TradesFinder
from rpa.erp.jst import JstNewSDK, JstOrderRemarkUploadResp, JstLimitError


class JstSetNodeExecutor(AutoJobControllerBase):
    """便签｜线下备注"""
    job_type = JobType.JST_SET_NODE

    def process(self) -> Tuple[JobStatus, Union[str, Exception, None]]:
        try:
            node: str | None = self.args.get_arg_as_str_by_task_arg_name("node")
            if node is None:
                return JobStatus.FAILED, "未填写线下备注"
            is_append = self.args.get_arg_by_task_arg_name("is_append")

            tid: str | None = self.args.get_trades()[0].tid
            oid: str | None = self.args.get_arg_by_task_arg_name("o_id")

            if oid:
                # 理论上一个线上单号的所有订单的店铺编码应该是一致的，取第一个就行
                orders = TradesFinder(
                    shop=self.job_wrapper.shop,
                    o_id=oid,
                    tid=None
                ).try_get_orders()
                # 通过oid查询的时候，一般前序是一个创建补发单的操作，这笔订单数据需要等待一定时间 才能被奇门查到
                if not orders:
                    raise Retry(message="订单数据未同步到奇门，请稍后重试", delay=30000)
                shop_id = orders[0].shop_id
                tid = None
            else:
                orders = TradesFinder(
                    shop=self.job_wrapper.shop,
                    tid=tid
                ).try_get_orders()
                if not orders:
                    raise Retry(message="订单数据未同步到奇门，请稍后重试", delay=30000)
                shop_id = orders[0].shop_id

            if is_append:
                node = "{}{}".format(
                    orders[0].node or "",
                    node
                )

            result: JstOrderRemarkUploadResp = JstNewSDK(self.shop.sid).node_soid_set(
                oid,
                tid,
                node,
                shop_id
            )
            logger.info(f"result: {result}")

            if result.code != 0:
                r, msg = JobStatus.FAILED, result.msg
            else:
                r, msg = JobStatus.SUCCEED, ""
        except JstLimitError as e:
            raise Retry(str(e), delay=1000)
        except JstRequestError as e:
            r, msg = JobStatus.FAILED, e.message
        self.states.write_job_output({
            "result": "成功" if r == JobStatus.SUCCEED else "失败",
            "description": msg
        })
        return r, msg
