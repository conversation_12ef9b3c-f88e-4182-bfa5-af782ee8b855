from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.baisheng import BaiShengClient


class BaishengSellerMemoExecutor(AutoJobControllerBase):
    job_type = JobType.BAISHENG_SELLER_MEMO

    def get_seller_msg(self):
        seller_msg = self.args.get_arg_by_task_arg_name("seller_msg")
        if not seller_msg:
            return None
        elif isinstance(seller_msg, list):
            return seller_msg[0]
        return seller_msg


    def process(self):
        order_sn = self.args.get_arg_by_task_arg_name("order_sn")
        seller_msg = self.get_seller_msg()
        if not order_sn:
            return JobStatus.FAILED, "系统单号为空"
        if not seller_msg:
            return JobStatus.FAILED, "备注内容为空"
        resp = BaiShengClient(sid=self.job_wrapper.shop.sid).order_info_change(order_sn, seller_msg)
        if not resp.ok:
            return JobStatus.FAILED, f"网络问题 {resp.status_code}"

        resp_obj = resp.json()
        if resp_obj.get("status") == "api-success" and resp_obj.get("message") == "success":
            return JobStatus.SUCCEED, None
        else:
            return JobStatus.FAILED, resp_obj.get("message")
