import time
from typing import Any, Optional
import json

from flask import current_app
from pydantic import BaseModel
from loguru import logger

from robot_processor.client.logistics_clients.schema import \
    EMSQueryLogisticOutput, EMSReceiverAddress
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.job.confirm_logistics_intercept import \
    ConfirmLogisticsInterceptExecutor
from robot_processor.job.logistics_intercept import LogisticsInterceptExecutor
from robot_processor.enums import JobType, JobStatus
from robot_processor.utils import combine_address_with_town
from robot_processor.constants import EMS_INTERCEPT_CACHE_KEY_PREFIX
from robot_processor.client.logistics_clients.ems_domain import EMSClient, \
    Identification
from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.client.logistics_clients.utils import get_filtered_traces
from robot_processor.shop.models import Shop


class ReceiverAddressInfo(BaseModel):
    state: str | None
    city: str | None
    zone: str | None
    district: str | None
    town: str | None
    address: str | None

    @classmethod
    def parse_from(cls, origin_data: Any) -> Optional["ReceiverAddressInfo"]:
        if not isinstance(origin_data, dict):
            return None
        try:
            receiver_address_info = ReceiverAddressInfo(**origin_data)
            return receiver_address_info
        except Exception as e:
            logger.error("地址组件数据解析失败", e)
            return None


class CancelLogisticOutput(BaseModel):
    """
    邮政撤单的输出信息。
    """
    intercept_status: str
    message: str | None
    suggestion: str


class ChangeAddressOutput(BaseModel):
    message: str


def get_customer_keys_by_shop(shop: Shop, sender_no: str | None = None) -> list[dict[str, str]]:
    customer_keys: list[dict[str, str]] = shop.get_logistic_grant_records(LogisticsType.EMS)
    if len(customer_keys) == 0:
        return []

    if sender_no:
        current_customer_key = None
        for customer_key in customer_keys:
            if customer_key.get("sender_no") == sender_no:
                current_customer_key = customer_key
        if not current_customer_key:
            return []
        else:
            return [current_customer_key]
    return customer_keys


def get_ems_client_by_shop(shop: Shop, sender_no: str | None) -> tuple[EMSClient, None] | tuple[None, str]:
    """
    使用特定的 sender_no 或基于当前店铺绑定的所有 EMS 物流授权来生生成 client。
    """
    customer_keys = shop.get_logistic_grant_records(LogisticsType.EMS)
    if len(customer_keys) == 0:
        return None, "缺少协议客户授权码等信息"
    if sender_no is not None and len(
            [customer_key for customer_key in customer_keys if customer_key.get("sender_no") == sender_no]
    ) == 0:
        return None, "该协议客户号尚未配置"

    try:
        ems_client = EMSClient(logistics_type=LogisticsType.EMS)
        ems_client.init({"keys": customer_keys})
        return ems_client, None
    except Exception as e:
        logger.error("EMS Client 初始化失败: ", e)
        return None, "EMS Client 初始化失败"


class EMSChangeAddressExecutor(AutoJobControllerBase):
    """
    EMS 修改收件人地址。
    """
    job_type = JobType.EMS_CHANGE_ADDRESS
    output_model = ChangeAddressOutput

    def process(self) -> tuple[JobStatus, str | None]:
        ems_client, err = get_ems_client_by_shop(self.shop, self.args.get_arg_by_task_arg_name("sender_no"))
        if ems_client is None or err is not None:
            return JobStatus.FAILED, err

        waybill_no = self.args.get_arg_by_task_arg_name("waybill_no")
        receiver_address_info = ReceiverAddressInfo.parse_from(
            self.args.get_arg_by_task_arg_name("new_address")
        )

        if waybill_no is None:
            return JobStatus.FAILED, "缺少运单号"

        if not receiver_address_info:
            return JobStatus.FAILED, "缺少需要修改的收件人地址信息"

        receiver_addr = combine_address_with_town(
            receiver_address_info.town or "",
            receiver_address_info.address or ""
        )
        if any([
            not receiver_address_info.state,
            not receiver_address_info.city,
            not receiver_addr,
        ]):
            return JobStatus.FAILED, "缺少需要修改的收件人地址信息"

        success, response = ems_client.change_receiver_address(
            EMSReceiverAddress(
                identification=Identification.change_address,
                waybillNo=waybill_no,
                receiverProv=receiver_address_info.state or "",
                receiverCity=receiver_address_info.city or "",
                receiverAddress=receiver_addr,
                receiverCounty=(receiver_address_info.district or receiver_address_info.zone or None)
            ).dict(exclude_unset=True)
        )

        if not success:
            return JobStatus.FAILED, response
        output_content = self.output_model(message="成功")
        self.states.write_job_widget_collection(output_content.dict())
        return JobStatus.SUCCEED, None


class EMSQueryLogisticExecutor(AutoJobControllerBase):
    """
    EMS 查询物流轨迹。
    """
    job_type = JobType.EMS_QUERY_LOGISTIC
    output_model = EMSQueryLogisticOutput

    def process(self) -> tuple[JobStatus, str | None]:
        ems_client, err = get_ems_client_by_shop(self.shop, self.args.get_arg_by_task_arg_name("sender_no"))
        if ems_client is None or err is not None:
            return JobStatus.FAILED, err

        waybill_no = self.args.get_arg_by_task_arg_name("waybill_no")

        if waybill_no is None:
            return JobStatus.FAILED, "缺少运单号"

        get_trace_records_method = self.args.get_arg_by_task_arg_name("get_trace_records_method")

        success, response = ems_client.query_trace(waybill_no)

        if not success:
            return JobStatus.FAILED, response

        logistic_trace_records = self.output_model(**json.loads(response))
        # 如果没有物流轨迹则直接返回成功。
        if len(logistic_trace_records.response_items) == 0:
            self.states.write_job_widget_collection({})
            return JobStatus.SUCCEED, None

        filtered_traces, err = get_filtered_traces(
            logistic_trace_records.response_items,
            logistic_type=LogisticsType.EMS,
            get_trace_records_method=get_trace_records_method
        )
        if err:
            return JobStatus.FAILED, err
        logistic_trace_records.response_items = filtered_traces

        output_content = logistic_trace_records.dict()
        self.states.write_job_widget_collection(output_content)
        return JobStatus.SUCCEED, None


class EmsInterceptExecutor(LogisticsInterceptExecutor):
    from robot_processor.job.logistics_intercept import InterceptInputModel

    job_type = JobType.EMS_CANCEL_LOGISTIC

    def init_logistics_domain(self):
        customer_keys: list[dict[str, str]] = get_customer_keys_by_shop(
            self.shop,
            self.args.get_arg_by_task_arg_name("sender_no")
        )
        return {"keys": customer_keys}

    def before_check(self):
        # 获取基础信息：协议客户号和运单号，并根据协议客户号获取对应的 ems_client。
        sender_no = self.args.get_arg_by_task_arg_name("sender_no")
        customer_keys: list[dict[str, str]] = get_customer_keys_by_shop(
            self.shop,
            sender_no
        )
        if len(customer_keys) == 0:
            if sender_no:
                return None, "该协议客户号尚未配置"
            else:
                return None, "缺少协议客户授权码等信息"

        waybill_no = self.args.get_arg_by_task_arg_name("waybill_no")
        if waybill_no is None:
            return False, "缺少运单号"
        return True, None

    def logistics_intercept_cache_prefix(self):
        return EMS_INTERCEPT_CACHE_KEY_PREFIX

    def input_params(self) -> "InterceptInputModel":
        from robot_processor.job.logistics_intercept import InterceptInputModel
        return InterceptInputModel(
            waybill_no=self.args.get_arg_by_task_arg_name('waybill_no'),
            logistics_type=LogisticsType.EMS.value
        )

    def create_interception_adapter(self):
        return self.args.get_arg_by_task_arg_name('waybill_no')


class EMSConfirmInterceptExecutor(ConfirmLogisticsInterceptExecutor):
    from robot_processor.job.confirm_logistics_intercept import InterceptInputModel

    def logistics_intercept_cache_prefix(self):
        return EMS_INTERCEPT_CACHE_KEY_PREFIX

    job_type = JobType.EMS_CONFIRM_INTERCEPT

    def before_check(self):
        # 获取基础信息：协议客户号和运单号，并根据协议客户号获取对应的 ems_client。
        sender_no = self.args.get_arg_by_task_arg_name("sender_no")
        customer_keys: list[dict[str, str]] = get_customer_keys_by_shop(
            self.shop,
            sender_no
        )
        if len(customer_keys) == 0:
            if sender_no:
                return None, "该协议客户号尚未配置"
            else:
                return None, "缺少协议客户授权码等信息"

        waybill_no = self.args.get_arg_by_task_arg_name("waybill_no")
        if waybill_no is None:
            return False, "缺少运单号"
        return True, None

    def spin(self):
        from robot_processor.business_order.tasks import package_jobs
        # ems 物流拦截指令下发后，会很快同步到物流轨迹上，所以前几次自旋可以快一点
        if int(time.time()) - self.job.created_at < 30 * 60:
            package_jobs.send_with_options(args=(self.job.business_order_id, self.job.id),
                                           delay=1000 * 60 * 10)
        else:
            package_jobs.send_with_options(args=(self.job.business_order_id, self.job.id),
                                           delay=1000 * 60 * 60 * self.spin_wait_hours())

    def logistics_wait_max_timeout_days(self):
        return int(current_app.config.get("EMS_TIMEOUT_DAYS", 3))

    def init_logistics_domain(self):
        customer_keys: list[dict[str, str]] = get_customer_keys_by_shop(
            self.shop,
            self.args.get_arg_by_task_arg_name("sender_no")
        )
        return {"keys": customer_keys}

    def input_params(self) -> "InterceptInputModel":
        from robot_processor.job.confirm_logistics_intercept import InterceptInputModel

        return InterceptInputModel(
            waybill_no=self.args.get_arg_by_task_arg_name('waybill_no'),
            logistics_type=LogisticsType.EMS.value
        )
