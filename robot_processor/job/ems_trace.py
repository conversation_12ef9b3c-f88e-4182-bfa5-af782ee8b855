from enum import Enum
from typing import List

import objectpath
from dramatiq import Retry
from loguru import logger
from pydantic import BaseModel, Field
from result import Ok, Err

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.mola import MolaClient


class Argument(BaseModel):
    name: str
    label: str
    required: bool


class RpaArgument(Enum):
    emsSid = Argument(name="ems_sid", label="邮政ID", required=True)
    mailNumber = Argument(name="mail_number", label="邮政快递单号", required=True)
    mailInfo = Argument(name="mail_info", label="邮件信息", required=True)
    queryInfo = Argument(name="query_info", label="查件内容", required=True)
    remark = Argument(name="remark", label="备注", required=True)
    imgPathList = Argument(name="image", label="发送图片", required=False)

    sender = Argument(name="sender", label="发送账号", required=True)

    @property
    def rpa_name(self):
        return self.value.name

    @property
    def required(self):
        return self.value.required


class TracePackageData(BaseModel):
    mailNumber: str = Field(description="邮政快递单号")
    mailInfo: str = Field(description="邮件信息")
    queryInfo: str = Field(description="查件内容")
    remark: str = Field(default='', description="备注")
    imgPathList: List[str] = Field(description="发送图片")


class AddCommentData(BaseModel):
    mailNumber: str = Field(description="邮政快递单号")
    comment: str = Field(description="邮件信息")
    commentMethod: str = Field("submit", description="追评操作，我们固定为'仅提交'")


class EmsTraceExecutor(AutoJobControllerBase):
    job_type = JobType.EMS_TRACE

    def get_ems_id(self):
        """文本框"""
        return self.args.get_arg_by_task_arg_name(RpaArgument.emsSid.rpa_name) or ''

    def get_mail_number(self):
        """引用前序组件，组件类型为 String|TrackingNum"""
        mail_number = self.args.get_arg_by_task_arg_name(RpaArgument.mailNumber.rpa_name)
        return mail_number

    def get_mail_info(self):
        """富文本框"""
        mail_info = self.args.get_arg_by_task_arg_name(RpaArgument.mailInfo.rpa_name)
        return mail_info

    def get_query_info(self):
        """单选列表"""
        query_info = self.args.get_arg_by_task_arg_name(RpaArgument.queryInfo.rpa_name)
        return query_info

    def get_remark(self):
        """富文本框"""
        remark = self.args.get_arg_by_task_arg_name(RpaArgument.remark.rpa_name)
        return remark

    def get_sender(self):
        """发送账号"""
        sender = self.args.get_arg_by_task_arg_name(RpaArgument.sender.rpa_name)
        if sender["value"] == "RANDOM_ONLINE":
            return ""
        elif sender["value"] == "MANUAL_INPUT":
            return ",".join(sender["extra"])
        else:
            return ""

    def get_images(self) -> List[str]:
        image_urls = self.args.get_arg_by_task_arg_name("image")
        if not image_urls:
            return []
        if isinstance(image_urls, str):
            # 打款凭证
            return [image_urls]
        elif isinstance(image_urls, list):
            # 图片上传组件
            return [img.get("url") for img in image_urls]
        else:
            return []

    def process(self):
        operation_method = self.states.get_job_state('rpa:ems-trace:should') or 'trace-package'
        if operation_method not in ["trace-package", "add-comment"]:
            logger.warning("未知的操作类型: {} 回退到 trace-package", operation_method)
            operation_method = "trace-package"
        payload: TracePackageData | AddCommentData
        if operation_method == "trace-package":
            payload = TracePackageData(
                mailNumber=self.get_mail_number(),
                mailInfo=self.get_mail_info(),
                queryInfo=self.get_query_info(),
                remark=self.get_remark(),
                imgPathList=self.get_images(),
            )
        else:
            payload = AddCommentData(
                mailNumber=self.get_mail_number(), comment=self.get_remark()
            )

        mola = MolaClient(self.get_ems_id())
        result = mola.post_ems_trace(method=operation_method, sender=self.get_sender(), payload=payload.dict())
        match result:
            case Ok(value):
                result_tree = objectpath.Tree(value)
                if (
                    "error" == result_tree.execute("$.result.state")
                    and "邮件号已经是问题件了" == result_tree.execute("$.result.msg")
                ):
                    self.states.write_job_states({"rpa:ems-trace:should": "add-comment"})
                    raise Retry(message="该单号已提交过，重试追加评论")
                else:
                    return JobStatus.SUCCEED, None
            case Err(error_message):
                return JobStatus.FAILED, error_message + f" mail_no={payload.mailNumber}"
