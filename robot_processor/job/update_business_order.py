from __future__ import annotations

from loguru import logger
from robot_types.core import Value
from robot_types.helper import ValueResolver
from robot_types.helper import deserialize
from robot_types.helper import serialize
from robot_types.helper.data_projection import ProjectionRule
from robot_types.helper.data_projection import parse_projection_rules
from robot_types.helper.data_projection import update_existing

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.business_order.job_action import JobAction
from robot_processor.business_order.models import BusinessOrder
from robot_processor.enums import Creator
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.enums import UpdateStrategy
from robot_processor.ext import db
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.utils import dict_diff
from robot_processor.utils import unwrap_optional


class UpdateBusinessOrderExecutor(AutoJobControllerBase):
    job_type = JobType.UPDATE_BUSINESS_ORDER

    def process(self):
        projection_rules = deserialize(self.args.key_map.get("projection"), list[ProjectionRule])
        projection, const_context = parse_projection_rules(projection_rules)
        # 更新当前工单
        if self.args.key_map.get("update_current_business_order", True):
            business_order = self.order
            update_strategy = UpdateStrategy.FORCE

        # 更新指定工单
        else:
            try:
                business_order_id_value: Value = deserialize(self.args.key_map["business_order_id"], Value)
            except KeyError:
                return JobStatus.FAILED, "未绑定更新的工单ID"
            business_order_id = business_order_id_value.with_resolver(ValueResolver(self.order.data.copy())).resolve()
            business_order = db.session.get_one(BusinessOrder, business_order_id)
            update_strategy = UpdateStrategy.SAFE

        source_data = self.order.data.copy()
        source_data["const"] = const_context
        target_data = dict()  # type: ignore[var-annotated]
        # FIXME 过滤掉未变更的 key
        update_existing.apply_projection(source_data, projection, target_data)
        data_for_update = serialize(target_data)
        logger.info(f"diff: {dict_diff(business_order.data.copy(), data_for_update)['update']}")
        job_action = JobAction(job_id=unwrap_optional(business_order.current_job_id))
        try:
            job_action.update_order(
                data=data_for_update,
                operate_assistant=AccountDetailV2(
                    user_type=Creator.RPA, user_id=self.job.id, user_nick=f"{unwrap_optional(self.job.step).name}"
                ),
                update_strategy=update_strategy,
                operate_reason="自动化应用: 更新工单数据",
            )
        except Exception as e:
            return JobStatus.FAILED, e

        return JobStatus.SUCCEED, None
