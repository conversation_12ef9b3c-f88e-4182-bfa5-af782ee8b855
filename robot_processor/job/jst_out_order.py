import json
from typing import List
from typing import Op<PERSON>
from typing import <PERSON><PERSON>

from dramatiq import Retry
from pydantic import BaseModel

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.ext import cache
from robot_processor.job import AutoJobControllerBase
from robot_processor.plugin.trade_utils import ErpTradeManager
from rpa.erp.jst import JstLimitError
from rpa.erp.jst import JstNewSDK
from rpa.erp.jst import JstQmSD<PERSON>
from rpa.erp.jst import JstSDK
from rpa.erp.jst.schemas import OpenOrdersOutSimpleQuery
from rpa.erp.jst.schemas import OutOrder
from rpa.erp.jst.utils import complete_wms_co_name_by_wms_co_id


class Output(BaseModel):
    orders: List[OutOrder]
    count: int


class JstOutOrderByOIDExecutor(AutoJobControllerBase):
    job_type = JobType.JST_OUT_ORDER_BY_O_ID
    output_model = Output

    def get_rate_limit_setting(self, conf: dict) -> Optional[Tuple[str, str]]:
        # 聚水潭api会有限流 https://open.jushuitan.com/document/2273.html
        limit_key = f"JOB_LIMITER_{self.job_type}_ORG_{self.job_wrapper.shop.org_id}"  # 在租户维度进行限流
        for conf_key in [limit_key, f"JOB_LIMITER_{self.job_type}"]:
            if limit_value := conf.get(conf_key):
                return limit_key, limit_value
        return None

    def process(self):
        trades = self.args.get_trades()
        tid = None
        if trades:
            tid = trades[0].tid
        o_id = self.args.get_arg_by_task_arg_name("o_id")  # 这是聚水潭的内部订单号
        if not tid and not o_id:
            return JobStatus.FAILED, "聚水潭内部单号或订单号不能都为空"
        qm_client = JstQmSDK(self.shop.sid)
        open_client = JstNewSDK(self.shop.sid)
        if o_id:
            action_resp = open_client.order_action_query(o_id)
            if action_resp.data.datas:
                splited_oids = [action.remark for action in action_resp.data.datas if action.name == "被拆分"]
            else:
                splited_oids = []
            if splited_oids:
                params = OpenOrdersOutSimpleQuery(o_ids=splited_oids)
            else:
                params = OpenOrdersOutSimpleQuery(o_ids=[o_id])
            datas = ErpTradeManager.query_out_orders_jst(qm_client, open_client, params).datas
        else:
            params = OpenOrdersOutSimpleQuery(so_ids=[tid])
            datas = ErpTradeManager.query_out_orders_jst(qm_client, open_client, params).datas
            if self.args.is_argument_configured("order_type"):
                need_order_types = self.args.get_arg_by_task_arg_name("order_type")
                if need_order_types:
                    datas = [data for data in datas if data.order_type in need_order_types]
            if self.args.get_arg_by_task_arg_name("order_status"):
                need_order_status = self.args.get_arg_by_task_arg_name("order_status")
                if need_order_status:
                    datas = [data for data in datas if data.readable_status in need_order_status]

        if logistic_no := self.args.get_arg_by_task_arg_name("logistic_no"):
            if isinstance(logistic_no, list):
                datas = [data for data in datas if data.l_id in logistic_no]
            else:
                datas = [data for data in datas if data.l_id == logistic_no]

        if order_status_list := self.args.get_arg_by_task_arg_name("order_status_list"):
            datas = [data for data in datas if data.readable_status in order_status_list]
        # https://redmine.leyantech.com/issues/659973
        if self.shop.org_id == "2827":
            datas = [data for data in datas if data.readable_status != "作废"]

        if datas:
            if any(
                [
                    self.args.is_argument_configured_in_task_arguments("orders[].items[].batch_no"),
                    self.args.is_argument_configured_in_task_arguments("orders[].items[].supplier_name"),
                ]
            ):
                for data in datas:
                    for item in data.items:
                        if data.batchs:
                            for batch in data.batchs:
                                if item.sku_id == batch.sku_id:
                                    item.supplier_name = batch.supplier_name
                                    item.batch_no = batch.batch_no
                        elif item.supplier_id:
                            jst_new_sdk = JstNewSDK(sid=self.shop.sid)
                            if supplier_name := self.cache_get_supplier_name_by_supplier_id(
                                jst_new_sdk, item.supplier_id
                            ):
                                item.supplier_name = supplier_name
                        else:
                            jst_new_sdk = JstNewSDK(sid=self.shop.sid)
                            if supplier_name := self.cache_get_supplier_name_by_sku(jst_new_sdk, item.sku_id):
                                item.supplier_name = supplier_name
            if self.args.is_argument_configured_in_task_arguments("orders[].warehouse"):
                jst_sdk = JstSDK(self.shop.sid)
                for data in datas:
                    data.warehouse = data.warehouse or complete_wms_co_name_by_wms_co_id(
                        jst_sdk, self.shop.org_id, data.wms_co_id  # type: ignore[arg-type]
                    )
            for order in datas:
                self.add_wave_id(order)
                self.add_packer_name(order)
                self.add_picker_name(order)
                self.add_inspecter_name(order)
                self.add_wms_co_name(order)
            self.states.write_job_widget_collection(Output(orders=datas, count=len(datas)).dict())
        return JobStatus.SUCCEED, ""

    def cache_get_supplier_name_by_supplier_id(self, jst_sdk: JstNewSDK, supplier_id) -> str | None:
        cache_key = f"jst_supplier_name_{jst_sdk.co_id}:{supplier_id}"
        if cached := cache.get(cache_key):
            return json.loads(cached)
        else:
            jst_sdk.limiter_test(wait=True)
            try:
                supplier_resp = jst_sdk.get_supplier_by_supplier_id(supplier_id)
            except JstLimitError:
                raise Retry(message=f"聚水潭限流: {jst_sdk.limit_key}", delay=3000)
            if supplier_resp.data.list:
                supplier_name = supplier_resp.data.list[0].co_name
            else:
                supplier_name = None
            cache.set(cache_key, json.dumps(supplier_name), timeout=3600)
            return supplier_name

    def cache_get_supplier_name_by_sku(self, jst_sdk: JstNewSDK, sku_id) -> str | None:
        cache_key = f"jst_supplier_name_{jst_sdk.co_id}:{sku_id}"
        if cached := cache.get(cache_key):
            return json.loads(cached)
        else:
            jst_sdk.limiter_test(wait=True)
            try:
                supplier_resp = jst_sdk.get_supplier_by_sku(sku_id)
            except JstLimitError:
                raise Retry(message=f"聚水潭限流: {jst_sdk.limit_key}", delay=3000)
            if supplier_resp.data.list:
                supplier_name = supplier_resp.data.list[0].supplier_name
            else:
                supplier_name = None
            cache.set(cache_key, json.dumps(supplier_name), timeout=3600)
            return supplier_name

    def add_wave_id(self, order):
        if any(
            [
                self.args.is_argument_configured_in_task_arguments("orders[].wave_id"),
                self.args.is_argument_configured_in_task_arguments("orders[].picker_name"),
            ]
        ):
            ret = JstNewSDK(self.job_wrapper.shop.sid).get_wave_id(order.o_id)
            if ret:
                order.wave_id = ret

    def add_packer_name(self, order):
        if self.args.is_argument_configured_in_task_arguments("orders[].packer_name"):
            ret = JstNewSDK(self.job_wrapper.shop.sid).get_packer_name(order.o_id)
            if ret:
                order.packer_name = ret

    def add_picker_name(self, order):
        if self.args.is_argument_configured_in_task_arguments("orders[].picker_name") and order.wave_id:
            ret = JstNewSDK(self.job_wrapper.shop.sid).get_picker_name(order.wave_id)
            if ret:
                order.picker_name = ret

    def add_inspecter_name(self, order):
        if self.args.is_argument_configured_in_task_arguments("orders[].inspecter_name"):
            ret = JstNewSDK(self.job_wrapper.shop.sid).get_inspecter_name(order.o_id)
            if ret:
                order.inspecter_name = ret

    def add_wms_co_name(self, order):
        if self.args.is_argument_configured_in_task_arguments("orders[].wms_co_name"):
            if order.wms_co_id == 0:
                order.wms_co_name = "未设定发货仓库"
            else:
                for wms_info in (
                    JstQmSDK(self.job_wrapper.shop.sid)
                    .get_wms_info_by_org_id(self.job_wrapper.shop.org_id, False)
                    .datas
                ):
                    if wms_info.wms_co_id == order.wms_co_id:
                        order.wms_co_name = wms_info.name
                        break
                else:
                    order.wms_co_name = "未知分仓"
