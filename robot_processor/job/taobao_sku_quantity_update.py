from flask import current_app
from result import Ok, Err

from robot_processor.client import spzs_taobao_client
from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase


class TaobaoSkuQuantityUpdate(AutoJobControllerBase):

    job_type = JobType.TAOBAO_SKU_QUANTITY_UPDATE
    output_model = None

    def process(self):
        sku_id = ""
        spu_id = ""
        if self.args.is_argument_configured("sku_id"):
            sku_id = self.args.get_arg_by_task_arg_name("sku_id")
        if self.args.is_argument_configured("spu_id"):
            spu_id = self.args.get_arg_by_task_arg_name("spu_id")
        quantity = self.args.get_arg_by_task_arg_name("quantity")
        type = self.args.get_arg_by_task_arg_name("type")
        if self.args.is_argument_configured("skus"):
            skus = self.args.get_reissue_skus("skus")
            if len(skus.sku_list) != 1:
                return JobStatus.FAILED, "只支持单个SKU"
            sku = skus.sku_list.pop(0)
            if not sku.sku_id:
                return JobStatus.FAILED, "商品SKU中没有SKU ID"
            if not sku.spu_id:
                return JobStatus.FAILED, "商品SKU中没有SPU ID"
            sku_id = sku.sku_id
            spu_id = sku.spu_id
        if not sku_id:
            return JobStatus.FAILED, "缺少商品SKU或SKU ID"
        if not spu_id:
            return JobStatus.FAILED, "缺少商品SPU或SPU ID"
        access_tokens = current_app.config.get("SPZS_ACCESS_TOKENS", {})
        if self.shop.sid not in access_tokens:
            return JobStatus.FAILED, "店铺缺少授权"
        access_token = access_tokens.get(self.shop.sid)

        match spzs_taobao_client.skus_quantity_update(access_token, int(spu_id),
                                                      f"{sku_id}:{quantity}",
                                                      type):
            case Ok(_):
                return JobStatus.SUCCEED, ""
            case Err(exception):
                return JobStatus.FAILED, str(exception)
