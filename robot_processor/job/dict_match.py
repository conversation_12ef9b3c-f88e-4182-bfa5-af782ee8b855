from loguru import logger
from yaml import load, Loader

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase


class DictMatchExecutor(AutoJobControllerBase):
    job_type = JobType.DICT_MATCH

    def process(self):
        key = self.args.get_arg_by_task_arg_name("key")
        mapping = self.args.get_arg_by_task_arg_name("mapping")
        try:
            data = load(mapping, Loader=Loader)
            data = {str(k): v for k, v in data.items()}
        except Exception as e:
            logger.opt(exception=e).error("映射规则解析失败")
            return JobStatus.FAILED, "映射规则解析失败"
        if key in data:
            self.states.write_job_output({"val": data[key]})
        elif "兜底规则" in data:
            self.states.write_job_output({"val": data["default"]})
        else:
            return JobStatus.FAILED, f'提供的规则中没有 "{key}" 的信息'

        return JobStatus.SUCCEED, None
