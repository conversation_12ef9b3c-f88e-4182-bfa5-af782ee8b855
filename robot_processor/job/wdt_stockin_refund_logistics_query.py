from typing import List

from pydantic import BaseModel

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.wdt import WdtOpenAPIClient, StockRefundLogistics


class Output(BaseModel):
    packages: List[StockRefundLogistics]


class WdtStockinRefundLogisticsQueryExecutor(AutoJobControllerBase):
    job_type = JobType.WDT_STOCKIN_REFUND_LOGISITCS_QUERY
    output_model = Output
    """
    旺店通企业版退货物流包裹查询
    """

    def process(self):
        logistics_no = self.args.get_arg_by_task_arg_name("logistics_no")
        if not logistics_no:
            return JobStatus.FAILED, "没有物流单号"

        resp = WdtOpenAPIClient(self.shop.sid).query_stock_refund_logistics(
            logistics_no)
        self.states.write_job_widget_collection(Output(
            packages=resp.stock_refund_logistics).dict())
        return JobStatus.SUCCEED, ""
