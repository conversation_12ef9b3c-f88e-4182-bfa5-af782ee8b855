from typing import Tuple, Optional

from dramatiq import Retry
from result import Err, Ok

from robot_processor.client import token_bucket_limiter
from pydantic import BaseModel
from robot_processor.enums import JobType, ErpType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.utils import combine_address_with_town, get_district_or_zone
from rpa.mola import MolaClient
from loguru import logger

import robot_processor.logging.vars as log_vars


class AfterSaleTradeOutput(BaseModel):
    exchange_uid: Optional[str]


class AfterSaleTradeExecutor(AutoJobControllerBase):
    job_type = JobType.AFTER_SALE_TRADE
    output_model = AfterSaleTradeOutput

    def process(self) -> Tuple[JobStatus, Optional[str]]:
        if not (shop := self.job_wrapper.shop):
            return JobStatus.FAILED, "无店铺信息"
        if not self.job_wrapper.erp_info:
            return JobStatus.FAILED, "缺失 ERP TOKEN"

        erp_type = self.job_wrapper.erp_info.erp_type
        log_vars.ErpType.set(erp_type.name)  # type: ignore[union-attr]

        if erp_type != ErpType.WANLINIU:
            return JobStatus.FAILED, "暂不支持该 ERP"

        # body 的 schema 参考：https://git.leyantech.com/digismart/mola-service/-/blob/master/src/sites/wanliniu/service.ts#L1545  # noqa
        remark = self.args.get_arg_by_task_arg_name('remark') or ""
        reason = self.args.get_arg_by_task_arg_name("reason")
        images = self.args.get_arg_by_task_arg_name("image")
        if images:
            pics = [i.get("url") for i in images]
            if len(pics) > 4:
                pics = pics[:4]
        else:
            pics = []
        body = {
            "tid": self.args.get_trades()[0].tid,  # 订单ID
            "skuList": [
                {
                    "skuId": sku.outer_sku_id or sku.sku_id,
                    "itemNum": sku.qty,
                }
                for sku in self.args.get_reissue_skus().sku_list
            ],  # 必传 补发SKU列表
            "remark": f'{remark} {self.job_wrapper.order.aid}',  # 备注 文本 需传飞梭账号名
            "reason": reason[0] if reason else "",  # 问题原因 单选
            "pics": pics,  # 凭证
        }

        if receiver := self.args.get_arg_by_task_arg_name("address"):
            body["receiverAddress"] = {
                "receiverState": receiver.get("state"),  # 省
                "receiverCity": receiver.get("city"),  # 市
                "receiverDistrict": f"{get_district_or_zone(receiver)}",  # 区
                "detailAddress": combine_address_with_town(
                    f"{receiver.get('town') or ''}", f"{receiver.get('address')}"
                ),  # 详细地址
                "receiverMobile": receiver.get("mobile"),  # 收货手机号
                "receiverName": receiver.get("name"),  # 买家姓名
            }  # 收货地址 不传则直接使用原地址

        token_bucket_key = 'mola:wanliniu-erp:create-after-sale-trade'
        if not token_bucket_limiter.try_acquire_token_for_store(token_bucket_key, shop.sid):
            raise Retry(message=f'{token_bucket_key}:{shop.sid} is rate limited', delay=3000)
        res = MolaClient(shop.sid).create_after_sale_trade(body)
        logger.info("创建万里牛售后单返回: {}", res)
        match res:
            case Err(error_message):
                return JobStatus.FAILED, error_message
            case Ok(body):
                if not body:
                    exchange_uid = None
                else:
                    result = body.get("result", {})
                    exchange_uid = result.get("returnValue", {}).get("data", {}).get("exchange", {}).get("exchange_uid")
                output = self.output_model(exchange_uid=exchange_uid)
                self.states.write_job_widget_collection(output.dict())
                return JobStatus.SUCCEED, None
