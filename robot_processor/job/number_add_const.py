#  Copyright 2024 Leyantech Ltd. All Rights Reserved.
from pydantic import BaseModel

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase


class Output(BaseModel):
    result: float


class NumberAddConstExecutor(AutoJobControllerBase):
    job_type = JobType.NUMBER_ADD_CONST
    output_model = Output

    def process(self):
        left = self.args.get_arg_by_task_arg_name("left")
        if left is None:
            return JobStatus.FAILED, "左值不存在"

        self.states.write_job_widget_collection({"result": left + 1})
        return JobStatus.SUCCEED, None
