import json
import typing as t
from io import BytesIO
from urllib import parse
import re

import requests
from pydantic import BaseModel, Field
from loguru import logger

from robot_processor.client import file_oss_client
from robot_processor.enums import JobType
from robot_processor.job.rpa_async_job import RpaAsyncJobController
from robot_processor.utils import make_fields_optional
from rpa.conf import rpa_config


@make_fields_optional
class OrderDetail(BaseModel):
    wdt_order_number: str = Field(alias="订单编号")
    tid: str = Field(alias="原始单号")
    oid: str = Field(alias="原始子单号")
    goods_name: str = Field(alias="商品名称")
    goods_spec: str = Field(alias="商品规格")
    unit: str = Field(alias="单位名称")
    qty: str = Field(alias="数量")
    total_amount: str = Field(alias="总价")
    tax_rate: str = Field(alias="税率")
    pin: str = Field(alias="税务编码")
    before_tax_total_price: str = Field(alias="税前总价")
    before_tax_unit_price: str = Field(alias="税前单价")
    tax: str = Field(alias="税额")
    memo: str = Field(alias="备注")
    before_tax_discount_amount: str = Field(alias="税前折扣金额")
    discount_tax: str = Field(alias="折扣税额")
    discount_total_price: str = Field(alias="折扣总价")
    kind: str = Field(alias="类别")


@make_fields_optional
class InvoiceDetail(BaseModel):
    invoice_serial_no: str = Field(alias="发票编号")
    invoice_business_type: str = Field(alias="开票类型")
    tid: str = Field(alias="原始单号")
    shop_name: str = Field(alias="店铺")
    payer_name: str = Field(alias="开票方")
    invoice_isv_name: str = Field(alias="开票服务商名称")
    invoice_status: str = Field(alias="发票状态")
    invoice_category: str = Field(alias="发票种类")
    invoice_kind: str = Field(alias="发票类型")
    red_or_blue: str = Field(alias="红蓝票")
    invoice_title: str = Field(alias="发票抬头")
    invoice_number: str = Field(alias="发票号码")
    invoice_code: str = Field(alias="发票代码")
    payee_tax_identification_number: str = Field(alias="收款方纳税人识别号")
    wdt_order_number: str = Field(alias="订单编号")
    customer_nick: str = Field(alias="客户网民")
    tel_number: str = Field(alias="联系电话")
    payer_email: str = Field(alias="电子邮箱")
    payer_address: str = Field(alias="地址信息")
    payer_bank: str = Field(alias="开户银行")
    payer_bank_account: str = Field(alias="银行账号")
    drawer: str = Field(alias="开票人")
    payee_name: str = Field(alias="收款人")
    reviewer: str = Field(alias="复核人")
    creator: str = Field(alias="创建人")
    confirmor: str = Field(alias="确认人")
    amount: str = Field(alias="总金额")
    goods_total_amount: str = Field(alias="货品合计金额")
    actual_total_tax: str = Field(alias="实际合计税额")
    before_tax_discount_amount: str = Field(alias="税前折扣金额")
    discount_tax: str = Field(alias="折扣税额")
    invoice_start_time: str = Field(alias="开票日期")
    invoice_payer_memo: str = Field(alias="开票方备注")
    invoice_print_memo: str = Field(alias="发票打印备注")
    invoice_download_url: str = Field(alias="发票下载地址")
    invoice_odf_download_url: str = Field(alias="发票下载地址ODF")
    invoice_xml_download_url: str = Field(alias="发票下载地址XML")
    email_send_status: str = Field(alias="邮件发送状态")
    actual_total_amount: str = Field(alias="实际合计金额")
    error_message: str = Field(alias="错误信息")
    created_time: str = Field(alias="创建时间")
    detail_list: list[OrderDetail]
    latest_operate_log: str = Field(alias="首条操作明细")

    # 以下字段是服务没有提供，需要进行转化获取的
    oss_url: str = Field(description="存入我们这边服务的 oss 的 url")


@make_fields_optional
class WDTUltiGetInvoiceResult(BaseModel):
    label: str
    name: str
    result_object: list[InvoiceDetail] = Field(alias="object")
    result_type: str = Field(alias="type")


class RPAOutput(BaseModel):
    output: list[WDTUltiGetInvoiceResult]


@make_fields_optional
class OutputModel(BaseModel):
    datas: list[InvoiceDetail]


class RpaWDTUltiGetInvoiceExecutor(RpaAsyncJobController):
    job_type = JobType.WDTULTI_GET_INVOICE
    output_model = OutputModel

    def gen_rpa_client_payload(self) -> t.Dict:
        trades = self.args.get_trades()
        if not isinstance(trades, list) or len(trades) == 0:
            raise Exception("旺店通旗舰版获取发票必须提供订单号")
        # 订单号
        tid = trades[0].tid

        # 请求参数
        param_dict = {
            "order": tid,
            "invoice_header": None,
            "invoice_money": None,
        }

        # 发票抬头，非必填
        if invoice_header := self.args.get_arg_by_task_arg_name("invoice_header"):
            param_dict.update({"invoice_header": str(invoice_header)})
        # 发票金额，非必填
        if invoice_money := self.args.get_arg_by_task_arg_name("invoice_money"):
            param_dict.update({"invoice_money": str(invoice_money)})

        logger.info(f"RPA 获取发票的请求参数: {param_dict}")
        return param_dict

    def on_success(self, rpa_response: dict) -> None:
        logger.info(f"RPA 获取发票的响应内容：{rpa_response}")
        output = json.loads(rpa_response["output"])
        data = RPAOutput(output=output)
        result = OutputModel(datas=[])

        for content in data.output:
            invoice_detail_list = self.filter_invoice(content.result_object)
            logger.info(f"过滤后的发票列表的数据为: {invoice_detail_list}")
            for detail in invoice_detail_list:
                url = self.download_file_to_oss(detail.invoice_download_url)
                detail.oss_url = url
                result.datas.append(detail)
        self.states.write_job_widget_collection(result.dict())

    @staticmethod
    def filter_invoice(invoice_detail_list: list[InvoiceDetail]) -> list[InvoiceDetail]:
        """
        过滤相关发票。
        :param invoice_detail_list:
        :return:
        """
        log_red_invoice_code_list = []
        log_red_to_blue_invoice_code_list = []

        red_invoice_code_re_compile = re.compile(rpa_config.INVOICE_CODE_RED_RE_PATTERN)
        red_to_blue_invoice_code_re_compile = re.compile(rpa_config.INVOICE_CODE_RED_TO_BLUE_RE_PATTERN)

        logger.info("红票编号有: {}".format([
            invoice_detail.invoice_serial_no for invoice_detail in invoice_detail_list
            if invoice_detail.red_or_blue == "红票"
        ]))
        logger.info("蓝票编号有: {}".format([
            invoice_detail.invoice_serial_no for invoice_detail in invoice_detail_list
            if invoice_detail.red_or_blue == "蓝票"
        ]))

        for invoice_detail in invoice_detail_list:
            latest_operate_log = (invoice_detail.latest_operate_log or "").replace(" ", "")
            # 获取到红票。
            if match_result := red_invoice_code_re_compile.search(latest_operate_log):
                log_red_invoice_code_list.append(match_result.group(1))
            # 获取到红票要冲的蓝票
            if match_result := red_to_blue_invoice_code_re_compile.search(latest_operate_log):
                log_red_to_blue_invoice_code_list.append(invoice_detail.invoice_serial_no)

        logger.info(f"日志中记录的红票的编号有: {log_red_invoice_code_list}")
        logger.info(f"日志中记录的红票冲掉的蓝票的编号有: {log_red_to_blue_invoice_code_list}")

        return [
            invoice_detail for invoice_detail in invoice_detail_list
            if (
                invoice_detail.red_or_blue == "蓝票"
                and invoice_detail.invoice_serial_no not in log_red_to_blue_invoice_code_list
            )
        ]

    def download_file_to_oss(self, download_url: str) -> str:
        if parse.urlparse(download_url).scheme not in ["http", "https"]:
            return "无法获取该发票链接文件"
        try:
            response = requests.get(download_url)
            response.raise_for_status()
        except Exception as e:
            logger.exception(e)
            return "获取发票文件失败"

        try:
            content_type = response.headers.get("Content-Type") or ""
            extend_name = ""
            if "application/pdf" in content_type:
                extend_name = ".pdf"

            bytes_io = BytesIO(response.content)

            url = file_oss_client.upload_file(
                self.shop.org_id,
                bytes_io,
                f"发票{extend_name}"
            )
            return url
        except Exception as e:
            logger.exception(e)
            return "转换发票文件失败"
