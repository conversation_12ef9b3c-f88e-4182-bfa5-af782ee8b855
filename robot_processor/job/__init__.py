import typing as t

from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.rpa_service.output_args import JobOutputGenerator
from robot_processor.signals import booting
from robot_processor.utils import get_all_subclasses
from robot_processor.utils import import_this_module

if t.TYPE_CHECKING:
    from robot_processor.form.schemas import RpaArgOutputSchema


all_auto_job_controllers: t.Dict["JobType", t.Type["AutoJobControllerBase"]] = {}
all_auto_job_output_schemas: t.Dict["JobType", t.List["RpaArgOutputSchema"]] = {}


@booting.connect
def init_app(_):
    # 自动注册当前 package 下的 JobExecutor
    import_this_module(__path__, __name__ + ".")

    for cls in get_all_subclasses(AutoJobControllerBase):
        if not getattr(cls, "job_type", None):
            continue
        job_type = cls.job_type
        if job_type in all_auto_job_controllers:
            raise ValueError(f"一个 job type 上实现了重复的 job controller: {cls.job_type} {cls}")
        all_auto_job_controllers[job_type] = cls
        if hasattr(cls, "output_model"):
            all_auto_job_output_schemas[job_type] = JobOutputGenerator(cls).job_output_schema

    return AutoJobControllerBase
