import json
from typing import List, Optional

from lepollo import get_config
from loguru import logger

from .base import BaseN8NWorkflow
from .jst import JstGetErpTradeWorkflow, JSTProductWorkflow  # NOQA
from .transfer import TransferWorkflow  # NOQA
from .taobao import TaobaoOrderWorkflow  # NOQA
from .tmall import TmallTradeWorkflow # NOQA
from .autofill import AutofillWorkflow  # NOQA
from .pdd import PddTradeWorkflow, PddSmallTransferWorkflow, PddMemoWorkflow  # NOQA
from .jd import JdTradeWorkflow  # NOQA
from .doudian import DoudianTradeWorkflow  # NOQA
from .others import *  # NOQA

from robot_processor.utils import get_all_subclasses

apollo_config = get_config("application")

n8n_workflow_classes = {}
for cls in get_all_subclasses(BaseN8NWorkflow):
    if not getattr(cls, "name", None):
        continue
    if cls.name in n8n_workflow_classes:
        raise ValueError(f"n8n workflow class 重复了: {cls.name} {cls}")
    n8n_workflow_classes[cls.name] = cls


def get_n8n_workflow_config(flow_uuid) -> Optional[dict]:
    def _n8n_workflows_converter(s) -> List[dict]:
        try:
            return json.loads(s)
        except Exception as e:
            logger.warning(f"failed to parse n8n workflow config: {e}")
        return []

    for n8n_workflow_config in apollo_config.get("n8n_workflows", _n8n_workflows_converter, []):
        if flow_uuid in n8n_workflow_config['webhook_path']:
            return n8n_workflow_config

    for workflow_name in n8n_workflow_classes:
        config_key = f"n8n_workflows_{workflow_name}".upper()
        for n8n_workflow_config in apollo_config.get(config_key, _n8n_workflows_converter, []):
            if flow_uuid in n8n_workflow_config['webhook_path']:
                return {**n8n_workflow_config, 'name': workflow_name}
    return None


def get_n8n_workflow(flow_uuid):
    if n8n_workflow_config := get_n8n_workflow_config(flow_uuid):
        if n8n_workflow_classe := n8n_workflow_classes.get(n8n_workflow_config['name']):
            return n8n_workflow_classe(n8n_workflow_config)
    return None
