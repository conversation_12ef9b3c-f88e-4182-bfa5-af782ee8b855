from result import Err, Ok

from robot_processor.enums import JobStatus
from robot_processor.job.job_model_wrapper import JobModelWrapper, JobArguments, JobStates
from robot_processor.job.n8n_workflows import BaseN8NWorkflow
from rpa.mola import MolaClient


class DoudianTradeWorkflow(BaseN8NWorkflow):
    name = "<PERSON>udianTrade"

    def process(self, job_wrapper: JobModelWrapper, args: JobArguments, states: JobStates):
        output_widget_data = self.config['output_widget_data']

        tid = self.get_trade(args)['tid']
        res = MolaClient(job_wrapper.order.sid).doudian_query_trade(tid)
        match res:
            case Err(error_message):
                return JobStatus.FAILED, error_message
            case Ok(result):
                _output_widget_data = {}
                for widget_label, val_expr in output_widget_data.items():
                    _output_widget_data[widget_label] = eval(f'f{val_expr!r}', result, {})

                # note: 下单时间有可能为空, 例如 workflow 4194, 4232 和 4762
                if not _output_widget_data.get("下单时间"):
                    _output_widget_data.pop("下单时间")

                if _output_widget_data:
                    states.write_widget_data(_output_widget_data)

                return JobStatus.SUCCEED, None
