from result import Err, Ok

from robot_processor.enums import JobStatus
from robot_processor.job.job_model_wrapper import JobModelWrapper, JobArguments, JobStates
from robot_processor.job.n8n_workflows import BaseN8NWorkflow
from rpa.mola import MolaClient


class TmallTradeWorkflow(BaseN8NWorkflow):
    name = "TmallTrade"

    def process(self, job_wrapper: JobModelWrapper, args: JobArguments, states: JobStates):
        output_widget_data = self.config['output_widget_data']

        tid = self.get_trade(args)['tid']
        res = MolaClient(job_wrapper.order.sid).call_namespace_method('tmall-trade', 'get-trade-basic', dict(tid=tid))
        match res:
            case Err(error_message):
                return JobStatus.FAILED, error_message
            case Ok(body):
                if (trade := body['result']) and not trade:
                    return JobStatus.FAILED, "未查询到相关订单信息，请确认订单是否正确"

                _output_widget_data = {}

                # Note: 收货地址对象 字段是个 object, 用于 workflow 5633
                if output_address_widget_label := self.config.get('output_address_widget_label'):
                    _output_widget_data[output_address_widget_label] = trade['收货地址对象']

                for widget_label, val_expr in output_widget_data.items():
                    _output_widget_data[widget_label] = eval(f'f{val_expr!r}',
                                                             dict(item=trade), {})
                if _output_widget_data:
                    states.write_widget_data(_output_widget_data)

                return JobStatus.SUCCEED, None
