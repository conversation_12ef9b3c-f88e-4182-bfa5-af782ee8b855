from typing import <PERSON><PERSON>

from pydantic import BaseModel
from pydantic import Field
from result import Err
from result import Ok

from robot_processor.client import taobao_client
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.mola.client import MolaClient


class DepositRefundOutput(BaseModel):
    tid: str = Field(description="主订单ID")
    oid: str = Field(description="子订单ID")
    operation_time: str | None = Field(description="操作时间")
    remark: str | None = Field(description="备注说明")


class RefundRPAExecutor(AutoJobControllerBase):
    job_type = JobType.TAOBAO_REFUND

    def process(self) -> Tuple[JobStatus, str]:
        shop = self.job_wrapper.shop
        if shop is None:
            return JobStatus.FAILED, "缺失 Shop 信息"
        sid = shop.sid
        if sid is None:
            return JobStatus.FAILED, "Shop 缺失 SID 信息"
        # 获取订单号
        trades = self.args.get_trades()
        if len(trades) == 0:
            return JobStatus.FAILED, "未获取到订单号"
        tid = trades[0].tid

        res = MolaClient(shop.sid).call_namespace_method("qianniu", "refund-prepay", {"tid": tid})

        match res:
            case Err(error_message):
                return JobStatus.FAILED, error_message
            case Ok():
                return JobStatus.SUCCEED, ""


class DepositRefundRPAExecutor(AutoJobControllerBase):
    job_type = JobType.DEPOSIT_REFUND
    output_model = DepositRefundOutput

    def process(self) -> tuple[JobStatus, str]:
        shop = self.job_wrapper.shop
        if shop is None:
            return JobStatus.FAILED, "缺失 Shop 信息"
        grant_record = shop.get_recent_record()
        if not grant_record:
            return JobStatus.FAILED, "未查询到店铺授权信息"

        # 获取订单号
        trades = self.args.get_trades()
        if len(trades) == 0:
            return JobStatus.FAILED, "未获取到订单号"
        tid = trades[0].tid
        oid = trades[0].oid

        res = taobao_client.trade_support_refund_open(
            access_token=grant_record.access_token,
            tid=tid,
            oid=oid,
        )

        if res.is_err():
            return JobStatus.FAILED, str(res.unwrap_err())
        else:
            response = res.unwrap()
            output = DepositRefundOutput(**response)
            self.states.write_job_widget_collection(output.dict())
            return JobStatus.SUCCEED, ""
