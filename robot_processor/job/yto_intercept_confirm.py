from pydantic import BaseModel

from robot_processor.enums import JobType
from robot_processor.business_order.models import InterceptEvent
from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.job.confirm_logistics_intercept import (
    ConfirmLogisticsInterceptExecutor,
    InterceptInputModel,
)


class InterceptConfirmOutput(BaseModel):
    has_callback: str  # 是否有拦截回调信息: 是;否
    intercept_status: str  # 拦截状态: 成功;失败;未知
    error_message: str  # 拦截失败时，错误信息


class YTOInterceptConfirmExecutor(ConfirmLogisticsInterceptExecutor):
    job_type = JobType.YTO_INTERCEPT_CONFIRM

    def logistics_intercept_cache_prefix(self):
        pass

    def init_logistics_domain(self):
        auth_account: str | None = self.args.get_arg_by_task_arg_name("auth_account")
        intercept_at = self.args.get_arg_by_task_arg_name("intercept_at")
        if self.shop.channel_id is None:
            return {
                "credentials": None,
                "bo_id": self.order.id,
                "job_id": self.job.id,
                "intercept_at": intercept_at
            }
        customer_keys = self.shop.get_logistic_grant_records(LogisticsType.YTO)
        if auth_account:
            customer_keys = [customer_key for customer_key in customer_keys if customer_key.get("partner_key")]
        return {
            "credentials": customer_keys,
            "bo_id": self.order.id,
            "job_id": self.job.id,
            "intercept_at": intercept_at
        }

    def input_params(self):
        return InterceptInputModel(
            waybill_no=self.args.get_arg_by_task_arg_name("waybill_no"),
            logistics_type=LogisticsType.YTO.value,
        )

    def get_or_register_intercept_event(self):
        return InterceptEvent.get_or_register_intercept_event(
            self.input_params().waybill_no,
            LogisticsType.YTO.value,
            self.order.id,
            self.job.id,
        )

    def on_wait_timeout(self, check_type=None):
        if intercept_event := self.get_or_register_intercept_event():
            intercept_event.mark_process_timeout()

    def on_interception_success(self, check_type=None):
        # 通过回调结果确认拦截成功的场景，才需要对 InterceptEvent 进行更新
        if check_type != "回调信息":
            return
        if intercept_event := self.get_or_register_intercept_event():
            intercept_event.mark_process_finished()

    def on_interception_failed(self, check_type=None):
        # 通过回调结果确认拦截成功的场景，才需要对 InterceptEvent 进行更新
        if check_type != "回调信息":
            return
        if intercept_event := self.get_or_register_intercept_event():
            intercept_event.mark_process_finished()
