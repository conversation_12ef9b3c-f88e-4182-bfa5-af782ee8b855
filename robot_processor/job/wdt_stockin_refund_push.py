from pydantic import BaseModel

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.wdt import WdtOpenAPIClient, \
    StockinRefundPushReq, WdtClient, StockinRefundDetail


class Output(BaseModel):
    success: bool


class WdtStockinRefundPushExecutor(AutoJobControllerBase):
    job_type = JobType.WDT_STOCKIN_REFUND_PUSH
    output_model = Output
    """
    旺店通企业版创建退货入库单
    """

    def process(self):
        refund_no = self.args.get_arg_by_task_arg_name("refund_no")
        outer_no = self.args.get_arg_by_task_arg_name("outer_no")
        warehouse_name = self.get_warehouse_name()
        remark = self.args.get_arg_by_task_arg_name("remark")
        skus = self.args.get_reissue_skus("sku_id")
        is_check = int(self.args.get_arg_by_task_arg_name("is_check") or 0)
        warehouse_no = None
        if warehouse_name:
            warehouse_query_resp = WdtClient(self.order.sid).warehouse_query()
            warehouses = [warehouse for warehouse in
                          warehouse_query_resp.response.warehouses
                          if warehouse.name == warehouse_name]
            if warehouses:
                warehouse_no = warehouses[0].warehouse_no
        if not outer_no:
            outer_no = str(self.job.id)

        detail_list = []
        for sku in skus.sku_list:
            combine_sku_resp = WdtOpenAPIClient(
                self.shop.sid).query_combine_skus_by_combine_sku_id(
                sku.outer_sku_id)  # type: ignore[arg-type]
            if combine_sku_resp.suites:
                suite = combine_sku_resp.suites[0]
                for sku_in_suite in suite.specs_list:
                    detail_list.append(
                        StockinRefundDetail(
                            spec_no=sku_in_suite.spec_no,
                            stockin_num=sku.qty * sku_in_suite.num,
                            stockin_price=sku.price))
            else:
                detail_list.append(StockinRefundDetail(spec_no=sku.outer_sku_id,
                                                       stockin_num=sku.qty,
                                                       stockin_price=sku.price))
        req = StockinRefundPushReq(
            refund_no=refund_no,
            outer_no=outer_no,
            warehouse_no=warehouse_no,
            remark=remark,
            detail_list=detail_list,
            is_check=is_check
        )
        WdtOpenAPIClient(self.shop.sid).stock_refund_push(req)
        self.states.write_job_widget_collection(Output(success=True).dict())
        return JobStatus.SUCCEED, ""

    def get_warehouse_name(self):
        warehouse_name = self.args.get_arg_by_task_arg_name("warehouse_name")
        if not warehouse_name:
            return None
        elif isinstance(warehouse_name, list):
            return warehouse_name[0]
        return warehouse_name
