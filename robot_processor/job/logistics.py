import random
import time

from flask import current_app
from result import Ok, Err

from robot_processor.enums import JobStatus, JobType
from robot_processor.error.job_process import ShopNotFound
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.mola import MolaClient


class LogisticsExecutor(AutoJobControllerBase):
    job_type = JobType.QUERY_LOGISTICS

    def process(self):
        shop = self.job_wrapper.shop
        if not shop:
            return JobStatus.FAILED, ShopNotFound()

        dispute_id = self.args.get_arg_by_task_arg_name("dispute_id")
        rand = current_app.config.get("DELAY_RANDOM_INS", (0.5, 2))
        time.sleep(random.uniform(float(rand[0]), float(rand[1])))
        res = MolaClient(shop.sid).query_tmall_logistics(dispute_id)
        match res:
            case Ok(logistics):
                self.states.write_job_output(logistics)
                return JobStatus.SUCCEED, None
            case Err(error_message):
                return JobStatus.FAILED, f"请求失败: dispute_id：{dispute_id} 错误：{error_message}"
