import time
from typing import List

from pydantic import BaseModel

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.kuaimai import KmSDK, KuaimaiQmSDK, KuaimaiOrder


class KuaimaiConfirmOutput(BaseModel):
    sid: str  # 快麦系统订单号


class KuaimaiConfirmSaleExecutor(AutoJobControllerBase):
    job_type = JobType.KUAIMAI_CONFIRM_AFTER_SALE
    output_model = KuaimaiConfirmOutput

    def process(self):
        aftersale_id = self.args.get_arg_by_task_arg_name("aftersale_id")
        tid = self.args.get_trades()[0].tid
        res = KmSDK(shop=self.job_wrapper.shop).confirm_after_sale_order(aftersale_id)
        # 已解决的售后单，再次解决，success也是True
        if not res.success:
            return JobStatus.FAILED, "快麦售后单解决失败"
        # 售后单解决后没有返回sid，只能查奇门获取最新的补发单的sid
        time.sleep(2)
        all_orders: List[KuaimaiOrder] = KuaimaiQmSDK(sid=self.job_wrapper.shop.sid).get_orders(tid=tid).trades
        reissue_order = sorted(
            [i for i in all_orders if "REISSUE" in i.type_as_name], key=lambda x: x.sid
        )[-1]
        write_res = self.states.write_job_widget_collection({"sid": reissue_order.sid})
        if write_res.missing:
            missing_arg_str = "/".join(arg.readable for arg in write_res.missing)
            return JobStatus.FAILED, f"无法获取输出参数{missing_arg_str}"
        return JobStatus.SUCCEED, ""
