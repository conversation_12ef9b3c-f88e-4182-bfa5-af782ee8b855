from typing import Optional

from pydantic import BaseModel

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.wdt import Refund
from rpa.erp.wdt import RefundQueryResp
from rpa.erp.wdt import WdtClient


class RefundOrder(BaseModel):
    spec_no: str
    goods_name: str
    goods_no: str
    spec_name: str
    spec_code: str
    refund_num: int
    stockin_num: int


class RefundOutput(BaseModel):
    refund_id: int
    refund_no: str
    api_outer_no: str
    src_no: Optional[str]
    stockin_pre_no: str
    platform_id: str
    process_status_zh: str
    status_zh: str
    goods_amount: float
    refund_amount: float
    return_logistics_name: str
    return_logistics_no: str
    refund_order_list: list[RefundOrder]


class WdtRefundQueryBySrcRefundNoExecutor(AutoJobControllerBase):
    job_type = JobType.WDT_REFUND_QUERY_BY_SRC_REFUND_NO
    output_model = RefundOutput
    """
    旺店通企业版根据平台退换单号获取退换单
    """

    def process(self):
        src_refund_no = self.args.get_arg_by_task_arg_name("src_refund_no")
        if not src_refund_no:
            return JobStatus.FAILED, "没有平台退换单号"
        resp: RefundQueryResp = WdtClient(self.shop.sid).refund_query_by_src_refund_no(src_refund_no)
        if len(resp.response.refunds) == 0:
            return JobStatus.FAILED, "没有获取到退换单"
        if len(resp.response.refunds) > 1:
            return JobStatus.FAILED, "获取到不止1个退换单"
        refund: Refund = resp.response.refunds.pop()
        self.states.write_job_widget_collection(
            RefundOutput(
                refund_id=refund.refund_id,
                refund_no=refund.refund_no,
                api_outer_no=refund.api_outer_no,
                src_no=refund.src_no,
                stockin_pre_no=refund.stockin_pre_no,
                platform_id=refund.platform_id,
                process_status_zh=refund.process_status_zh,
                status_zh=refund.status_zh,
                goods_amount=refund.goods_amount,
                refund_amount=refund.refund_amount,
                return_logistics_name=refund.return_logistics_name,
                return_logistics_no=refund.return_logistics_no,
                refund_order_list=[
                    RefundOrder(
                        spec_no=refund_order.spec_no,
                        goods_name=refund_order.goods_name,
                        goods_no=refund_order.goods_no,
                        spec_name=refund_order.spec_name,
                        spec_code=refund_order.spec_code,
                        refund_num=refund_order.refund_num,
                        stockin_num=refund_order.stockin_num,
                    )
                    for refund_order in refund.refund_order_list
                ],
            ).dict()
        )
        return JobStatus.SUCCEED, ""
