from typing import Any, Optional, List, Dict, cast
import re

from pydantic import Field, BaseModel, root_validator, validator
from robot_processor.job.basic.schemas import BasicForm, BasicArgument, BasicResponse


class CreateTradeReturnOrderForm(BasicForm):
    class ReceiverInfo(BaseModel):
        # guanyiyun 只有三级地址，与飞梭对不上，zone/town 都会传给mola
        # mola 先使用zone 调用api，如返回'区域信息不正确'，在使用town 去尝试
        town: Optional[str]
        areaId: Optional[str] = Field(alias="zone")
        cityId: str = Field(alias="city")
        provinceId: str = Field(alias="state")
        receiverAddress: Optional[str] = Field(alias="address")
        receiverMobile: Optional[str] = Field(alias="mobile")
        receiverName: Optional[str] = Field(alias="name")

        @validator("cityId")
        def convert_city(cls, v):
            v = v.replace("市辖区", "")
            return v

        @validator("provinceId")
        def convert_province(cls, v):
            if v in ("上海市", "北京市", "天津市", "重庆市", "台北市"):
                v = v.replace("市", "")
            return v

    tid: str
    note: Optional[str] = ""
    express_code: str
    logistics_name: str
    warehouse_in_name: str
    warehouse_out_name: str
    items_in: Any
    items_out: Any
    receiver_info: Optional[ReceiverInfo]

    @root_validator(pre=True)
    def check_list_or_str(cls, values):
        logistics_name = values.get("logistics_name")
        if isinstance(logistics_name, list) and len(logistics_name) > 0:
            values["logistics_name"] = logistics_name[0]
        warehouse_in_name = values.get("warehouse_in_name")
        if isinstance(warehouse_in_name, list) and len(warehouse_in_name) > 0:
            values["warehouse_in_name"] = warehouse_in_name[0]
        warehouse_out_name = values.get("warehouse_out_name")
        if isinstance(warehouse_out_name, list) and len(warehouse_out_name) > 0:
            values["warehouse_out_name"] = warehouse_out_name[0]
        return values


class CreateTradeReturnOrderArgument(BasicArgument):
    class Item(BaseModel):
        spu_id: str
        sku_id: str
        outer_sku_id: Optional[str]
        qty: Optional[int]
        pic: Optional[str] = None
        price: Optional[float] = 0
        type: Optional[int] = 1

    class ReceiverInfo(BaseModel):
        provinceId: str
        cityId: str
        areaId: Optional[str]
        town: Optional[str]
        receiverAddress: Optional[str]
        receiverMobile: Optional[str] = Field(alias="mobile")
        receiverName: Optional[str] = Field(alias="name")

    tid: str
    note: str
    expressCode: str = Field(alias="express_code")
    logisticsName: str = Field(alias="logistics_name")
    warehouseInName: str = Field(alias="warehouse_in_name")
    warehouseOutName: str = Field(alias="warehouse_out_name")
    itemsIn: List[Item] = Field(alias="items_in")
    itemsOut: List[Item] = Field(alias="items_out")
    receiverInfo: Optional[ReceiverInfo] = Field(alias="receiver_info")

    @staticmethod
    def parse_product(product: Any) -> List[Dict[str, Any]]:
        if isinstance(product, dict):
            after_sales_type = product.get("after_sales_type")
            if after_sales_type == "non_original":
                skus = product.get("sku_list")
            else:
                skus = []
        else:
            skus = product
        return cast(List[Dict], skus)

    @classmethod
    def parse_form(cls, form: CreateTradeReturnOrderForm) -> "CreateTradeReturnOrderArgument":
        args = cls(
            tid=form.tid,
            note=form.note,
            express_code=form.express_code,
            logistics_name=form.logistics_name,
            warehouse_in_name=form.warehouse_in_name,
            warehouse_out_name=form.warehouse_out_name,
            items_in=[{
                "spu_id": sku.get("spu", ""),
                "sku_id": sku.get("sku", ""),
                "outer_sku_id": sku.get('outer_sku') or sku.get('sku'),
                "qty": sku.get('quantity') or sku.get('qty'),
                "pic": sku.get('pic_url'),
                "price": 0,
                "type": sku.get('type', 1)
            } for sku in cls.parse_product(form.items_in)],
            items_out=[{
                "spu_id": sku.get("spu", ""),
                "sku_id": sku.get("sku", ""),
                "outer_sku_id": sku.get('outer_sku') or sku.get('sku'),
                "qty": sku.get('quantity') or sku.get('qty'),
                "pic": sku.get('pic_url'),
                "price": 0,
                "type": sku.get('type', 1)
            } for sku in cls.parse_product(form.items_out)],
        )
        if form.receiver_info is not None:
            form.receiver_info.receiverAddress = "{state}{city}{zone}{town}{address}".format(
                state=form.receiver_info.provinceId,
                city=form.receiver_info.cityId,
                zone=form.receiver_info.areaId or "",
                town=form.receiver_info.town or "",
                address=form.receiver_info.receiverAddress or "",
            )
            args.receiverInfo = form.receiver_info  # type: ignore[assignment]
        return args


class CreateTradeReturnOrderResponse(BasicResponse):
    class Result(BaseModel):
        code: Optional[str] = Field(alias="code")

    success: bool
    result: Optional[Result]


class GetReturnOrdersForm(BasicForm):
    tid: str

    @validator("tid")
    def match_tid(cls, v):
        pattern = r'\d+'
        result = re.search(pattern, v)
        if not result:
            raise ValueError("未能成功匹配换货单号")
        return result.group()


class GetReturnOrdersArgument(BasicArgument):
    tid: str

    @classmethod
    def parse_form(cls, form: GetReturnOrdersForm) -> "GetReturnOrdersArgument":
        return cls(tid=form.tid)


class GetReturnOrdersResponse(BasicResponse):
    class Result(BaseModel):
        class Data(BaseModel):
            swapOrderCode: str

        data: Optional[Data]

    success: bool
    result: Optional[Result]
