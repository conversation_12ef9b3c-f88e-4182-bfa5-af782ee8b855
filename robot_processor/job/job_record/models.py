import datetime

from sqlalchemy.dialects.mysql import DATETIME
import sqlalchemy as sa
from sqlalchemy.orm import mapped_column, Mapped

from robot_processor.db import DbBaseModel, BasicMixin


class JobRecord(DbBaseModel, BasicMixin):
    """
    一次job的执行情况
    """
    bo_id: Mapped[int | None] = mapped_column(
        sa.Integer, comment="business_order外键"
    )
    sid: Mapped[str | None] = mapped_column(sa.String(32), index=True)
    step_uuid: Mapped[str | None] = mapped_column(sa.String(32))
    step_name: Mapped[str | None] = mapped_column(sa.String(32), index=True)
    step_type: Mapped[str] = mapped_column(sa.String(32), comment="步骤类型")  # 避免ddl改动
    job_id: Mapped[int | None] = mapped_column(sa.Integer)
    start: Mapped[int | None] = mapped_column(sa.Integer, comment="开始时间戳", index=True)
    end: Mapped[int | None] = mapped_column(sa.Integer, comment="结束时间戳", index=True)
    cost: Mapped[int | None] = mapped_column(sa.Integer, comment="耗时")
    assigner_name: Mapped[str | None] = mapped_column(sa.String(32), index=True, comment="执行人名称")
    assigner_id: Mapped[int | None] = mapped_column(sa.Integer)
    assigner_type: Mapped[int | None] = mapped_column(sa.Integer)
    form_id: Mapped[int | None] = mapped_column(sa.Integer)
    form_name: Mapped[str | None] = mapped_column(sa.String(64), index=True)
    next_jobs: Mapped[list[int]] = mapped_column(sa.JSON, comment="后序任务id列表", default=list)


class DailyJobRecord(DbBaseModel, BasicMixin):
    """
    每日job执行按照 （执行人，工单名，步骤名, 表单名）汇总
    """
    __table_args__ = (
        sa.Index(
            "ix_sid_step_name_step_type_assigner_name_form_name",
            "sid",
            "step_name",
            "step_type",
            "assigner_name",
            "form_name"
        ),
    )
    sid: Mapped[str | None] = mapped_column(sa.String(32), index=True)
    step_uuid: Mapped[str | None] = mapped_column(sa.String(32))
    step_name: Mapped[str | None] = mapped_column(sa.String(32), index=True)
    step_type: Mapped[str | None] = mapped_column(sa.String(32), comment="步骤类型")
    assigner_name: Mapped[str | None] = mapped_column(sa.String(32), index=True, comment="执行人名称")
    assigner_id: Mapped[int | None] = mapped_column(sa.Integer)
    assigner_type: Mapped[int | None] = mapped_column(sa.Integer)
    form_id: Mapped[int | None] = mapped_column(sa.Integer)
    form_name: Mapped[str | None] = mapped_column(sa.String(64), index=True)
    date: Mapped[datetime.datetime] = mapped_column(DATETIME(fsp=3), index=True)
    bo_id: Mapped[int | None] = mapped_column(
        sa.Integer, comment="business_order外键"
    )

    # =======
    cost: Mapped[int] = mapped_column(sa.Integer, comment="耗时", default=0)
    execute_times_total: Mapped[int] = mapped_column(sa.Integer, comment="执行次数", default=0)
