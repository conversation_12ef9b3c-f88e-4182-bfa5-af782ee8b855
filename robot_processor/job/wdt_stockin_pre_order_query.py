from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.wdt import StockinPreOrder
from rpa.erp.wdt import WdtClient


class WdtStockinRefundLogisticsQueryExecutor(AutoJobControllerBase):
    job_type = JobType.WDT_STOCKIN_PRE_ORDER_QUERY
    output_model = StockinPreOrder
    """
    旺店通企业版退货预入库单查询
    """

    def process(self):
        pre_stockin_no = self.args.get_arg_by_task_arg_name("pre_stockin_no")
        if not pre_stockin_no:
            return JobStatus.FAILED, "没有退货预入库单号"
        resp = WdtClient(self.shop.sid).stockin_pre_order_query(pre_stockin_no)
        if resp.response.stockin_pre_order_list:
            stockin_pre_order = resp.response.stockin_pre_order_list[0]
            self.states.write_job_widget_collection(stockin_pre_order.dict())
            return JobStatus.SUCCEED, ""
        return JobStatus.FAILED, "没有找到对应的退货预入库单"
