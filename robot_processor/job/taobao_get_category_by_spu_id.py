import sqlalchemy as sa
from pydantic import BaseModel
from sqlalchemy.orm import Mapped, mapped_column

from robot_processor.client import item_client
from robot_processor.db import DbBaseModel
from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase


class Output(BaseModel):
    category: str


class TaobaoFlattenCategory(DbBaseModel):
    __tablename__ = "tb_flatten_categories"

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    cid: Mapped[int] = mapped_column(sa.Integer)
    name: Mapped[str] = mapped_column(sa.String(100))


class TaobaoGetCategoryBySpuIdExecutor(AutoJobControllerBase):
    job_type = JobType.TAOBAO_GET_CATEGORY_BY_SPU_ID
    output_model = Output

    def process(self):
        spu_id = self.args.get_arg_by_task_arg_name("spu_id")
        if not spu_id:
            return JobStatus.FAILED, "商品ID为空"
        spu = item_client.get_spu(spu_id)
        if not spu:
            return JobStatus.FAILED, "找不到商品"
        cid = spu["cid"]
        category = TaobaoFlattenCategory.query.filter_by(cid=int(cid)).first()
        if not category:
            return JobStatus.FAILED, "找不到商品类目"
        output = Output(category=category.name)
        self.states.write_job_widget_collection(output.dict())
        return JobStatus.SUCCEED, None
