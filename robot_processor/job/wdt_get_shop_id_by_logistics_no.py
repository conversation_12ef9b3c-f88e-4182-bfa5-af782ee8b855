from pydantic import BaseModel

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.shop.models import Shop
from rpa.erp.wdt import WdtClient, RefundQueryResp, Refund


class Output(BaseModel):
    shop_id: int
    shop_title: str
    tid: str


class WdtGetShopIdByLogisticsNoExecutor(AutoJobControllerBase):
    job_type = JobType.WDT_GET_SHOP_ID_BY_LOGISTICS_NO
    output_model = Output
    """
    旺店通企业版基于退货物流单号获取退换单所归属的店铺ID
    """

    def process(self):
        logistics_no = self.args.get_arg_by_task_arg_name("logistics_no")
        if not logistics_no:
            return JobStatus.FAILED, "没有提供物流单号"
        resp: RefundQueryResp = WdtClient(
            self.shop.sid).refund_query_by_logistics_no(logistics_no)
        if len(resp.response.refunds) == 0:
            return JobStatus.FAILED, "没有获取到退换单"
        refund: Refund = resp.response.refunds.pop()
        shop_map = {
            "紫涵服饰旗舰": "紫涵服饰旗舰",
            "紫涵服饰outlet店": "紫涵服饰outlet店",
            "海贝服饰旗舰店": "海贝服饰旗舰店",
            "海贝自营店": "紫涵正品折扣店",
            "IHAPPY海贝": "海贝官方旗舰店",
            "抖音紫涵官方旗舰店": "紫涵官方旗舰店",
            "抖音紫涵奥莱旗舰店": "紫涵奥莱旗舰店",
            "拼多多紫涵服饰旗舰店": "紫涵服饰旗舰店",
            "拼多多海贝服饰旗舰店": "海贝服饰旗舰店",
            "拼多多海贝服饰专营店": "海贝服饰专营店",
            "拼多多紫涵女装旗舰店": "紫涵女装旗舰店",
            "紫涵女装官方旗舰": "紫涵（ZIHAN)服饰旗舰店",
            "海贝女装京东旗舰店": "海贝女装旗舰店",
            "唯品会紫涵店": "唯品官方特卖旗舰店"
        }
        feisuo_shop_title = shop_map.get(refund.shop_name)
        if not feisuo_shop_title:
            return JobStatus.FAILED, "没有找到对应的飞梭店铺"
        if refund.shop_name == "拼多多海贝服饰旗舰店":
            feisuo_shop = Shop.query.filter_by(
                title=feisuo_shop_title, platform="PDD").first()
        else:
            feisuo_shop = Shop.query.filter_by(title=feisuo_shop_title).first()
        if not feisuo_shop:
            return JobStatus.FAILED, "没有找到对应的飞梭店铺"
        self.states.write_job_widget_collection(Output(
            tid=refund.tid,
            shop_id=feisuo_shop.id,
            shop_title=feisuo_shop.title
        ).dict())
        return JobStatus.SUCCEED, ""
