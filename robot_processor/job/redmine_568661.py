import re

from robot_processor.enums import JobStatus, JobType
from robot_processor.job.auto_job import AutoJobControllerBase

MEMO_TABLE = {
    "丢件": ["丢件", "疑似丢件"],
    "漏发": ["漏发", "少发"],
    "未发": ["未撕单", "未发", "没发", "出库无物流", "无物流"],
    "已入库": ["已入库"],
    "未入库": ["未入库"],
    "补偿": ["补偿", "退差价"]
}

AMOUNT_REGEX = re.compile(r"(\d+(\.\d+)?)")


class Redmine568661Executor(AutoJobControllerBase):
    job_type = JobType.REDMINE_568661

    def process(self):
        seller_memo = self.args.get_arg_by_task_arg_name("seller_memo")
        print_memo = self.args.get_arg_by_task_arg_name("print_memo")

        memos = []
        amount = None
        for memo, keywords in MEMO_TABLE.items():
            if memo == "未入库" and "已入库" in memos:
                continue
            if any(keyword in seller_memo
                   or keyword in print_memo for keyword in keywords):
                memos.append(memo)
            if memo == "补偿":
                for keyword in keywords:
                    test_memo = None
                    test_memo = seller_memo if keyword in seller_memo else test_memo
                    test_memo = print_memo if keyword in print_memo else test_memo
                    if test_memo:
                        groups = AMOUNT_REGEX.findall(test_memo[test_memo.index(keyword) + 1:])
                        if groups:
                            amount = float(groups[0][0])
                            break

        memo = "无扣权限" if not memos else "/".join(memos)
        output = {
            "memo": memo
        }
        if amount:
            output["amount"] = str(amount)
        self.states.write_job_output(output)
        return JobStatus.SUCCEED, ""
