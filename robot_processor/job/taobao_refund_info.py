from pydantic import BaseModel, Field

from robot_processor.db import db
from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.refund.models import TaobaoRefund


# FIXME 旧版指令不支持复杂的数据类型，所以不能直接使用智能建单中定义的事件数据类型
class TaobaoRefundInfoOutput(BaseModel):
    company_name: str | None = Field(None, description="退货快递公司")
    sid: str | None = Field(None, description="退货快递单号")
    refund_status_zh: str | None = Field(None, description="售后状态")


class TaobaoRefundInfoExecutor(AutoJobControllerBase):
    job_type = JobType.TAOBAO_REFUND_INFO
    output_model = TaobaoRefundInfoOutput

    def process(self):
        refund_id = self.args.get_arg_by_task_arg_name("refund_id")
        if refund_id is None:
            return JobStatus.FAILED, "缺失 Refund ID 信息"

        refund_record = db.session.get(TaobaoRefund, refund_id)
        if refund_record is None:
            return JobStatus.FAILED, "未找到退款信息"

        refund_info = refund_record.get_refund_info()
        output = TaobaoRefundInfoOutput(
            company_name=refund_info.company_name,
            sid=refund_info.sid,
            refund_status_zh=refund_info.status.label
        )
        res = self.states.write_job_widget_collection(dict(output))
        if res.missing:
            missing_arg_str = "/".join(arg.readable for arg in res.missing)
            return JobStatus.FAILED, f"获取 {missing_arg_str} 信息失败"
        else:
            return JobStatus.SUCCEED, None
