from pydantic import BaseModel

from robot_processor.client import doudian_cloud
from robot_processor.client.errors import DoudianCloudServiceError
from robot_processor.client.schema import DoudianAftersaleOperateResp
from robot_processor.client.schema import DoudianAfterSaleOperateResult
from robot_processor.client.schema import DoudianEvidence
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job import AutoJobControllerBase

REASON_CODE_MAP = {
    "商品已发出，如消费者不再需要请拒收后申请仅退款": 1,
    "消费者误操作/取消申请": 3,
    "问题已解决，待用户收货": 4,
    "协商一致，用户取消售后": 7,
    "已与消费者协商补偿，包括差价、赠品、额外补偿": 8,
    "已与消费者协商补发商品": 9,
    "已与消费者协商换货": 10,
    "退回商品与原订单商品不符": 12,
    "退回商品不完好": 13,
    "定制商品不支持七天无理由退货，定制商品不接受质量问题以外的退货": 19,
    "消费者申请的金额有误": 21,
    "运费未协商一致": 25,
    "商品已经签收，如消费者不再需要可以申请退货退款": 26,
    "商品没问题，消费者未举证或举证无效": 27,
    "申请时间已超7天无理由退换货时间": 34,
    "消费者填错号码": 36,
    "已完成服务，消费者未提供凭证或凭证无效": 37,
    "其他": 42,
    "已与消费者协商一致仅退款": 47,
    "问题已解决，待用户确认收货": 48,
    "已与消费者协商一致延迟发货": 49,
    "未少发漏发": 50,
    "协商一致，用户取消以换代修": 59,
    "商品为人为质量问题": 60,
    "无换新商品，消费者不接受其他方案": 61,
    "协商一致，用户取消维修": 67,
    "退回地址与商家售后地址不符": 70,
    "商品在途，物流正常": 72,
    "商家未收到退货商品": 73,
    "消费者上传的单号有误，请核实正确物流单号后重新上传": 74,
    "申请时间已超7天无理由退货时间": 75,
}


class DoudianRefundWithReturnsRefuseOutput(BaseModel):
    execution_result: str


class DoudianRefundWithReturnsRefuseExecutor(AutoJobControllerBase):
    job_type = JobType.DOUDIAN_REFUND_WITH_RETURNS_REFUSE
    output_model = DoudianRefundWithReturnsRefuseOutput

    def process(self):
        aftersale_id = self.args.get_arg_by_task_arg_name("aftersale_id")
        if aftersale_id is None:
            return JobStatus.FAILED, "缺失售后单号"
        reason = self.args.get_arg_by_task_arg_name("reason")
        if not reason:
            return JobStatus.FAILED, "缺失拒绝理由"
        reason_code = REASON_CODE_MAP.get(reason)
        if not reason_code:
            return JobStatus.FAILED, "非法的拒绝原因"
        remark = self.args.get_arg_by_task_arg_name("remark")
        evidences = self.args.get_arg_by_task_arg_name("evidences")
        if not evidences:
            return JobStatus.FAILED, "缺失凭证"
        ignore_repetitive_operation_error = self.args.get_arg_by_task_arg_name("ignore_repetitive_operation_error")
        resp: DoudianAftersaleOperateResp | None = None
        try:
            resp = doudian_cloud.doudian_refund_refuse(
                store_id=self.shop.sid,
                aftersale_id=aftersale_id,
                operate_type=102,
                evidence=[DoudianEvidence(type=1, url=evidence["url"]) for evidence in evidences],
                reject_reason_code=reason_code,
                reason=reason,
                remark=remark,
            )
        except DoudianCloudServiceError as e:
            # FIXME(<EMAIL>): 先查询售后单
            if "售后状态错误" in str(e):
                resp = doudian_cloud.doudian_refund_refuse(
                    store_id=self.shop.sid,
                    aftersale_id=aftersale_id,
                    operate_type=112,
                    evidence=[DoudianEvidence(type=1, url=evidence["url"]) for evidence in evidences],
                    reject_reason_code=reason_code,
                    reason=reason,
                    remark=remark,
                )
            else:
                return JobStatus.FAILED, str(e)
        result: DoudianAfterSaleOperateResult = resp.items.pop()
        if result.status_code == 0:
            self.states.write_job_widget_collection({"execution_result": "成功"})
            return JobStatus.SUCCEED, ""
        if ignore_repetitive_operation_error == "true" and "所审核售后单均已完结" in result.status_msg:
            self.states.write_job_widget_collection({"execution_result": "售后单已被处理"})
            return JobStatus.SUCCEED, ""
        return JobStatus.FAILED, result.status_msg
