import random
import re
import typing as t
from functools import cached_property
from typing import List

from loguru import logger
from result import Err
from result import Ok

from robot_processor.enums import EnumMixin
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.cellar import cellar_client
from rpa.conf import cellar_config


class SenderStrategyEnum(EnumMixin):
    UNKNOWN = 0
    RANDOM_ONLINE = 1  # 随机在线
    INPUT_TEXT = 2  # 输入的任意字符串
    LAST_RECEPTION = 3  # 最后接待客服


class BaseSendMsgWithTid(AutoJobControllerBase):
    """通过订单号发消息"""

    allow_platform: List[str] = []

    @cached_property
    def select_assistant_result(self) -> Ok[str] | Err[Exception]:
        """通过 cached_property 保证每次 job 执行过程中使用到相同的客服"""
        msg_rules = self.args.get_send_message_arguments()
        sender = msg_rules.sender or {}
        if sender.get("value") and sender.get("extra"):
            assistant = sender["extra"]
        elif msg_rules.assistant:
            assistant = msg_rules.assistant
        else:
            return Err(Exception("未指定客服"))
        candidate_assistants = assistant.split(";")
        chosen_assistant = random.choice(candidate_assistants)
        logger.info(f"select assistant {chosen_assistant} | candidate {candidate_assistants}")
        return Ok(chosen_assistant)

    def get_tid_pattern(self) -> str:
        """获取订单号pattern"""
        raise NotImplementedError

    def process(self):
        msg_rules = self.args.get_send_message_arguments()
        if msg_rules.skip:
            return JobStatus.SUCCEED, ""

        sender = msg_rules.sender or {}

        send_strategy_name = sender.get("value", "")

        if not send_strategy_name:
            send_strategy = SenderStrategyEnum.INPUT_TEXT
        elif not SenderStrategyEnum.has(send_strategy_name):
            return JobStatus.FAILED, f"不支持当前发送策略：{send_strategy_name}"
        else:
            send_strategy = SenderStrategyEnum.get(send_strategy_name)

        match msg_rules.tid:
            case str() as tid:
                tid = tid
            case {"tid": tid}:
                tid = tid
            case [{"tid": tid}, *_]:
                tid = tid
            case _:
                trades = self.args.get_trades()
                tid = trades[0].tid if trades else None
        text = msg_rules.content
        images = msg_rules.image_urls
        image_url = images[0] if images else ""
        if send_strategy == SenderStrategyEnum.INPUT_TEXT and self.select_assistant_result.is_err():
            return JobStatus.FAILED, "未指定默认客服"
        if not tid:
            return JobStatus.FAILED, "订单ID为空"
        if re.match(self.get_tid_pattern(), tid) is None:
            return JobStatus.FAILED, f"错误的订单ID{tid}"
        if not text:
            return JobStatus.FAILED, "发送内容为空"

        shop = self.job_wrapper.shop
        if not shop:
            return JobStatus.FAILED, "店铺未开通"
        platform = shop.platform
        if platform not in self.allow_platform:
            return JobStatus.FAILED, "不支持当前平台"

        # 过滤条件
        extra_data = {
            "trace_status_excluded": msg_rules.trace_status_excluded or [],
            "refund_status_excluded": msg_rules.refund_status_excluded or [],
        }

        res = cellar_client.send_message(
            platform=platform.lower(),
            tid=tid,
            sid=shop.sid,
            assistant_fetch_strategy=send_strategy.value,
            assistant=self.select_assistant_result.unwrap_or(""),
            text=text,
            pic_url=image_url,
            extra_data=extra_data,
        )

        match res:
            case Ok():
                return JobStatus.SUCCEED, ""
            case Err(error_message):
                return JobStatus.FAILED, error_message


class PddSendMsgWithTid(BaseSendMsgWithTid):
    """拼多多消息"""

    job_type = JobType.PDD_SEND_MSG_TID
    allow_platform = ["PDD"]

    def get_tid_pattern(self) -> str:
        return cellar_config.PDD_TID_PATTERN


class DoudianSendMsgWithTid(BaseSendMsgWithTid):
    """抖店消息"""

    job_type = JobType.DOUDIAN_SEND_MSG_TID
    allow_platform = ["DOUDIAN"]

    def get_rate_limit_setting(self, conf: dict) -> t.Optional[t.Tuple[str, str]]:
        msg_rules = self.args.get_send_message_arguments()
        if msg_rules.sender and msg_rules.sender.get("extra"):
            assistant_name = self.select_assistant_result.unwrap()
            limit_key = f"JOB_LIMITER_{self.job_type}_{assistant_name}"
            limit_value = conf.get(f"JOB_LIMITER_{self.job_type}_BY_ASSISTANT", "1/10second")
            return limit_key, limit_value
        return super().get_rate_limit_setting(conf)

    def get_tid_pattern(self) -> str:
        return cellar_config.DOUDIAN_TID_PATTERN
