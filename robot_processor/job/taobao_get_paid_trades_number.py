from robot_processor.client import trade_client, buyer_client
from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.shop.models import GrantRecord


class HasTradeAfterOrderCreateExecutor(AutoJobControllerBase):
    job_type = JobType.TAOBAO_GET_PAID_TRADES_NUMBER

    def process(self):
        buyer_nick = self.args.get_arg_by_task_arg_name("usernick")
        buyer_info = buyer_client.get_buyer_info_by_buyer_nick(buyer_nick)
        if buyer_info:
            buyer_open_uid = buyer_info.buyer_open_uid
            if buyer_open_uid:
                grant_record: GrantRecord | None = GrantRecord.query.filter_by(
                    shop_id=self.job_wrapper.shop.id).order_by(
                    GrantRecord.updated_at.desc()).first()
                trades = trade_client.get_trades_by_buyer(
                    self.job_wrapper.shop.sid, self.job_wrapper.shop.nick,
                    buyer_nick, buyer_open_uid,
                    grant_record.access_token)  # type: ignore[union-attr]
                count = 0
                for trade in trades["trades"]:
                    if trade["status"] not in ["TRADE_CLOSED", "PAY_PENDING",
                                               "WAIT_BUYER_PAY",
                                               "TRADE_NO_CREATE_PAY",
                                               "TRADE_CLOSED_BY_TAOBAO"]:
                        count = count + 1
                self.states.write_job_output({"count": count})
                return JobStatus.SUCCEED, None
        return JobStatus.FAILED, f"找不到买家信息{buyer_nick}"
