import typing as t

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.kuaimai import KmSDK, KMException


class KuaimaiModifySellerMemoAndFlagExecutor(AutoJobControllerBase):
    job_type = JobType.KUAIMAI_MODIFY_SELLER_MEMO_AND_FLAG

    def process(self) -> t.<PERSON>[JobStatus, t.Union[str, Exception, None]]:
        tid = None
        trades = self.args.get_trades()
        if len(trades) > 0:
            tid = trades[0].tid

        kuaimai_sid = self.args.get_arg_by_task_arg_name("kuaimai_sid")
        seller_memo = self.args.get_arg_by_task_arg_name("seller_memo")
        kuaimai_flag = self.args.get_arg_by_task_arg_name("kuaimai_flag")

        try:
            res = KmSDK(shop=self.job_wrapper.shop).modify_seller_memo_and_flag(
                seller_memo=seller_memo,
                kuaimai_flag=kuaimai_flag,
                kuaimai_sid=kuaimai_sid,
                tid=tid,
            )
        except KMException as e:
            return JobStatus.FAILED, str(e)

        if res.success:
            return JobStatus.SUCCEED, ""
        else:
            return JobStatus.FAILED, "快麦修改备注与旗帜失败"
