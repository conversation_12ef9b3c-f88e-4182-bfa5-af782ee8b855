import typing as t

from loguru import logger

from robot_processor.client import xiaohongshu_client
from robot_processor.enums import JobStatus, JobType, UpdateMemoType, AuthType
from robot_processor.error.job_process import ShopNotFound
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.utils import text_append_now_datetime


class XhsMemoExecutor(AutoJobControllerBase):
    job_type = JobType.XHS_MEMO

    def process(self):
        shop = self.job_wrapper.shop
        if not shop:
            return JobStatus.FAILED, ShopNotFound()
        seller_words = self.args.get_arg_by_task_arg_name('content')
        if not seller_words:
            return JobStatus.FAILED, "未配置备注内容"
        seller_words = t.cast(str, seller_words)
        time_flag = self.args.get_arg_by_task_arg_name('time_flag')
        time_formatter = self.args.get_arg_by_task_arg_name(
            "time_formatter") or 'MM-DD HH:mm'

        if time_flag:
            seller_words = text_append_now_datetime(seller_words,
                                                    time_formatter)

        trades = self.args.get_trades()
        tid = trades[0].tid if trades else ''

        logger.info("order id: {}", tid)
        if not tid:
            return JobStatus.FAILED, "订单号为空，无法备注"
        access_token = self.shop.get_access_token(AuthType.XHS_ERP)
        try:
            order_info = xiaohongshu_client.order_detail(
                access_token, tid).data
        except Exception as error:
            logger.opt(exception=error).error(
                f"获取订单信息失败, {tid=}, {error=}")
            return JobStatus.FAILED, f"无法找到原始订单: {tid}"
        old_memo = order_info.sellerRemark
        logger.info("旧备注: {}", old_memo)
        raw_mode = self.args.get_arg_by_task_arg_name("mode")
        if not raw_mode:
            return JobStatus.FAILED, "未提供备注类型"
        mode = UpdateMemoType[t.cast(str, raw_mode)]
        logger.info("备注类型: {}", mode.name)
        new_memo = self._generate_new_memo(mode, old_memo, seller_words)
        tag_name = self._get_tag_name()
        if tag_name is None:
            tag_name = order_info.sellerRemarkFlag
        memo_resp = xiaohongshu_client.order_memo(access_token, tid, new_memo,
                                                  "飞梭",
                                                  tag_name)
        if memo_resp.success:
            return JobStatus.SUCCEED, ""
        return JobStatus.FAILED, memo_resp.error_code


def _generate_new_memo(self, mode: UpdateMemoType,
                       old_memo: t.Optional[str],
                       new_memo: str
                       ) -> str:
    max_memo_size = 500
    if mode == UpdateMemoType.OVERWRITE:
        return new_memo
    if old_memo:
        ret = f"{old_memo}\n{new_memo}"
    else:
        ret = new_memo
    if mode == UpdateMemoType.COVER_OLD_PRIOR:
        ret = ret[:max_memo_size]
    elif mode == UpdateMemoType.COVER_NEW_PRIOR:
        start_idx = max(len(ret) - max_memo_size, 0)
        ret = ret[start_idx:]
    return ret


def _get_tag_name(self) -> t.Optional[int]:
    remark_type = self.args.get_arg_by_task_arg_name("remarkTag") or "KEEP"
    remark_type = t.cast(str, remark_type)
    logger.info("插旗选项: {}", remark_type)
    option_mapper = {
        "KEEP": None,
        "YELLOW": 3,
        "PURPLE": 6,
        "GREEN": 4,
        "BLUE": 5,
        "RED": 2,
        "GREY": 1,
    }
    if remark_type not in option_mapper:
        raise Exception(f"非法的插旗选项: {remark_type}")
    return option_mapper[remark_type]
