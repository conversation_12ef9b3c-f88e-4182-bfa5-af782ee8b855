from loguru import logger
from result import Ok, Err

from robot_processor.enums import JobStatus, JobType, SmartCallPlatform
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.smartcall.client import SmartCallClient


def _ensure_plain_value(value):
    # hack: 外呼只接收值类型（数字 文本等）作为入参
    if value and isinstance(value, (list, tuple)):
        return value[0]
    else:
        return value


class SmartCallByTidExecutor(AutoJobControllerBase):
    job_type = JobType.SMARTCALL_BY_TID

    def process(self):
        shop = self.job_wrapper.shop
        if not shop:
            logger.info("没找到工单对应店铺")
            return JobStatus.FAILED, "没找到工单对应店铺"

        smartcall_info = shop.smartcalls.filter_by(shop_id=shop.id).first()
        if not smartcall_info:
            logger.info(f"店铺 {shop.sid} 未配置智能外呼信息:")
            return JobStatus.FAILED, "店铺未配置智能外呼信息"

        graph_main_id = _ensure_plain_value(self.args.get_arg_by_task_arg_name("graph_main_id"))
        if not graph_main_id:
            return JobStatus.FAILED, "未获取到外呼场景"

        fixed_params = ['tid', 'graph_main_id']
        param_map = {"business_order_id": self.job_wrapper.order.id,
                     "step_id": self.job.step_id}

        all_trades = self.args.get_trades()
        if not all_trades:
            return JobStatus.FAILED, "未获取到订单信息"

        tid = all_trades[0].tid
        if not tid:
            return JobStatus.FAILED, f"未获取到订单信息:{all_trades}"

        for arg_name, widget_key in self.args.key_map.items():
            if arg_name in fixed_params:
                continue
            arg_value = self.args.get_arg_by_task_arg_name(arg_name)
            if arg_value and isinstance(arg_value, str):
                param_map[arg_name] = _ensure_plain_value(arg_value)
            else:
                param_map[arg_name] = _ensure_plain_value(
                    self.args.get_and_render_brief_value_by_widget_key(widget_key)
                )

        param_map['店铺名称'] = self.args.get_arg_by_task_arg_name('店铺名称') or shop.title

        if not (platform := SmartCallPlatform.adapt(shop.platform)):
            return JobStatus.FAILED, f"不支持的店铺平台:{shop.platform}"

        res = (
                SmartCallClient(shop=self.job_wrapper.shop, smartcall_info=smartcall_info).
                start_call_by_tid(tid=tid, sid=shop.sid, platform=platform.value,
                                  graph_main_id=graph_main_id, param_map=param_map)
            )
        match res:
            case Ok(result):
                self.states.write_job_states(result)
                logger.info(f"外呼成功, {result=} {tid=} {graph_main_id=} {param_map=}")
                return JobStatus.RUNNING, None
            case Err(error_message):
                logger.warning(f"外呼失败, error={error_message} {tid=} {graph_main_id=} {param_map=}")
                return JobStatus.FAILED, error_message
