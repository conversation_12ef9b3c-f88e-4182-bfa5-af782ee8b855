from typing import Optional

from loguru import logger

from robot_processor.enums import JobType, JobStatus
from robot_processor.job import AutoJobControllerBase
from robot_processor.job.new_jst_after_sale_upload import AfterSaleOutput
from rpa.erp.jst import AfterSaleUploadReq, JstQmSDK, AfterSaleSearchReq, JstSDK


class JstAfterSaleUpdateReq(AfterSaleUploadReq):
    total_amount: Optional[float] = None  # type: ignore
    shop_status: Optional[str] = None  # type: ignore
    good_status: str  # type: ignore


class JstAfterSaleUpdateExecutor(AutoJobControllerBase):
    job_type = JobType.JST_AFTER_SALE_UPDATE
    output_model = AfterSaleOutput

    def process(self):
        as_id = self.args.get_arg_by_task_arg_name("as_id")
        # 先获取原售后单的信息
        origin_after_sale_req = self.get_update_data_from_origin_aftersale_order(
            tid=self.args.get_trades()[0].tid if self.args.get_trades() else None,
            as_id=str(int(as_id)) if as_id else None,
            shop=self.shop
        )

        # 再根据输入的参数进行调整,具体有哪些可以更新取决于聚水潭
        for arg_name in ["logistics_company", "l_id", "shop_status", "good_status", "question_type", "type"]:
            if arg_value := self.args.get_arg_as_str_by_task_arg_name(arg_name):
                setattr(origin_after_sale_req, arg_name, arg_value)

        # 重新上传
        JstSDK(self.shop.sid or "").aftersale_upload([origin_after_sale_req])

        return JobStatus.SUCCEED, None

    @staticmethod
    def get_update_data_from_origin_aftersale_order(tid, as_id, shop) -> JstAfterSaleUpdateReq:
        assert tid or as_id, "tid和as_id不能同时为空"
        req = AfterSaleSearchReq()
        req.so_ids = tid
        req.as_ids = as_id
        datas = JstQmSDK(shop.sid).query_refund_trade_list(req).datas
        if not datas:
            raise Exception(f"未找到原售后单信息, tid: {tid}, as_id: {as_id}")
        after_sale = datas[0]
        logger.info(f"原售后单信息: {after_sale}")
        return JstAfterSaleUpdateReq(
            as_id=after_sale.as_id,
            outer_as_id=after_sale.outer_as_id,
            shop_id=after_sale.shop_id,
            so_id=after_sale.so_id,
            type=after_sale.type,
            logistics_company=after_sale.logistics_company,
            l_id=after_sale.l_id,
            shop_status=after_sale.shop_status,
            remark=after_sale.remark,
            good_status=after_sale.good_status,
            question_type=after_sale.question_type,
            refund=after_sale.refund,
            payment=after_sale.payment,
            items=after_sale.items,
        )
