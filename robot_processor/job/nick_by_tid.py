#  Copyright 2023 Leyantech Ltd. All Rights Reserved.

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase


class NickByTradeExecutor(AutoJobControllerBase):
    job_type = JobType.NICK_BY_TRADE

    def process(self):
        from robot_processor.client import get_buyer_nick
        trades = self.args.get_trades(arg_name='order_no')
        tid = trades[0].tid if trades else ""
        if not tid:
            return JobStatus.FAILED, "订单号为空"
        nick = get_buyer_nick(self.job_wrapper.order.sid, tid=tid)
        if not nick:
            return JobStatus.FAILED, "未查找到订单号对应昵称"
        self.states.write_job_output(dict(usernick=nick))
        return JobStatus.SUCCEED, None
