from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.constants import YD_INTERCEPT_CACHE_KEY_PREFIX
from robot_processor.enums import JobType
from robot_processor.job.confirm_logistics_intercept import ConfirmLogisticsInterceptExecutor
from robot_processor.job.logistics_intercept import InterceptInputModel


class YDInterceptConfirmExecutor(ConfirmLogisticsInterceptExecutor):
    job_type = JobType.YD_CONFIRM_INTERCEPT

    def logistics_intercept_cache_prefix(self):
        return YD_INTERCEPT_CACHE_KEY_PREFIX

    def input_params(self):
        return InterceptInputModel(
            waybill_no=self.args.get_arg_by_task_arg_name("waybill_no") or "", logistics_type=LogisticsType.YD.value
        )

    def init_logistics_domain(self):
        return {"shop": self.shop, "auth_account": self.args.get_arg_by_task_arg_name("auth_account")}
