import uuid

import matplotlib as mpl
import matplotlib.pyplot as plt
import matplotlib.style as mplstyle
import numpy as np
import pandas as pd

from robot_processor.client import image_oss_client
from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase

mplstyle.use('fast')

def mergecells(table, cells):
    '''
    Merge N matplotlib.Table cells

    Parameters
    -----------
    table: matplotlib.Table
        the table
    cells: list[set]
        list of sets od the table coordinates
        - example: [(0,1), (0,0), (0,2)]

    Notes
    ------
    https://stackoverflow.com/a/53819765/12684122
    '''
    cells_array = [np.asarray(c) for c in cells]
    h = np.array([cells_array[i + 1][0] - cells_array[i][0] for i in
                  range(len(cells_array) - 1)])
    v = np.array([cells_array[i + 1][1] - cells_array[i][1] for i in
                  range(len(cells_array) - 1)])
    # if it's a horizontal merge, all values for `h` are 0
    if not np.any(h):
        # sort by horizontal coord
        cells = np.array(sorted(list(cells), key=lambda v: v[1]))
        edges = ['BTL'] + ['BT' for i in range(len(cells) - 2)] + ['BTR']
    elif not np.any(v):
        cells = np.array(sorted(list(cells), key=lambda h: h[0]))
        edges = ['TRL'] + ['RL' for i in range(len(cells) - 2)] + ['BRL']
    else:
        raise ValueError("Only horizontal and vertical merges allowed")
    for cell, e in zip(cells, edges):
        table[cell[0], cell[1]].visible_edges = e
    txts = [table[cell[0], cell[1]].get_text() for cell in cells]
    tpos = [np.array(t.get_position()) for t in txts]
    # transpose the text of the left cell
    trans = (tpos[-1] - tpos[0]) / 2
    # didn't had to check for ha because I only want ha='center'
    txts[0].set_transform(mpl.transforms.Affine2D().translate(*trans))
    for txt in txts[1:]:
        txt.set_visible(False)


class CustomizeOrg254_1Executor(AutoJobControllerBase):
    job_type = JobType.CUSTOMIZE_ORG_254_1

    def process(self) -> tuple[JobStatus, str | None]:
        data = self.args.get_arg_by_task_arg_name("supplier_info")
        # 提取数据并转换为DataFrame
        supplier = data["0e20c36f-a9a1-48ad-87ab-d1c708cb653b"]
        items = data["9cc2537f-80bd-44d6-861c-d94def70d94a"]

        rows = []
        for item in items:
            style = item["2d0786d4-f447-4585-ad5c-ac2a241425bc"]
            for detail in item["c441a613-97db-4126-b4c8-08f269b82b08"]:
                row = {
                    "供应商": supplier,
                    "款式": style,
                    "颜色": detail["bec5c2d0-fbf1-4aa3-9560-bc005ecc8b72"],
                    "尺码": detail["7246af25-c19e-4169-8156-bf21bef14d81"],
                    "数量": detail["a520c948-9d21-460e-9f3e-83d3c5978975"],
                    "缺货天数": detail["ae7fe20e-e785-4e05-b5e7-00a318c6bbe3"]
                }
                rows.append(row)

        df = pd.DataFrame(rows)

        plt.rcParams['font.sans-serif'] = 'WenQuanYi Micro Hei'
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
        fig, ax = plt.subplots(figsize=(10, 4))
        ax.axis('tight')
        ax.axis('off')
        # 创建表格
        table = ax.table(cellText=df.values, colLabels=df.columns,  # type: ignore[arg-type]
                         cellLoc='center',
                         loc='center')
        j = 0
        for i in range(1, len(df) + 1):
            if i == len(df) or str(df.iloc[i]["供应商"]) != str(
                    df.iloc[i - 1]["供应商"]):
                cells = []
                for k in range(j, i):
                    cells.append((k + 1, 0))
                mergecells(table, cells)
                j = i

        j = 0
        for i in range(1, len(df) + 1):
            if i == len(df) or str(df.iloc[i]["款式"]) != str(
                    df.iloc[i - 1]["款式"]):
                cells = []
                for k in range(j, i):
                    cells.append((k + 1, 1))
                mergecells(table, cells)
                j = i

        path = '/'.join(['/tmp', f'{uuid.uuid4().hex}.png'])
        plt.savefig(path, bbox_inches='tight', dpi=300)
        plt.clf()
        file = open(path, 'rb')
        url = image_oss_client.upload_image(self.shop.org_id, file,
                                            origin_name=file.name)
        self.states.write_job_output(
            {"out_image": [{"url": url, "name": file.name}]})
        return JobStatus.SUCCEED, ""
