import re

from loguru import logger


def normalize_address(address):
    if not isinstance(address, dict):
        return address
    address.setdefault("name", "")
    address.setdefault("mobile", "")
    address.setdefault("state", "")
    address.setdefault("city", "")
    address.setdefault("zone", "")
    address.setdefault("town", "")
    address.setdefault("address", "")
    return address


def change_wdt_address(address: dict):
    """
    将旺店通的补发输入参数中的地址 转化成旺店通期望的格式
    否则会报错 地址解析错误或者省市区匹配错误
    https://open.wangdian.cn/qyb/open/guide?path=guide_ccdmb
    目前“省”级别的地址，对直辖市而言，/v1/widget-data/address接口返回了重庆市，而旺店通预期的是重庆，先只确认这一块
    """
    def change_province(data):
        for need_change in ["上海市", "北京市", "天津市", "重庆市", "台湾省"]:
            if need_change in data:
                data = data.replace(need_change, need_change[:-1])
        return data

    if address.get("state"):
        address["state"] = change_province(address["state"])

    # address是详细的信息，可能也有省的信息
    if address.get("address"):
        address["address"] = change_province(address["address"])
    return address


def remove_string_spaces(origin_string: str) -> str:
    return origin_string.replace(" ", "")


def split_string_to_list(origin_string: str | None) -> list[str]:
    """
    将字符串按照 `,`、`，` 来切割成一个列表数据。
    :param origin_string:
    :return:
    """
    if origin_string is None:
        return []
    # 去除所有空格。
    current_string = remove_string_spaces(origin_string)
    # 根据中、英文逗号进行分割。
    if not current_string:
        string_list = []
    elif "," in current_string:
        string_list = current_string.split(",")
    elif "，" in current_string:
        string_list = current_string.split("，")
    else:
        string_list = [current_string]
    return string_list


def check_tracking_number(l_id: str) -> bool:
    if not l_id:
        return False
    pattern = r'^[A-Za-z0-9]{10,}$'
    if re.match(pattern, l_id):
        return True
    else:
        logger.warning(f"快递单号格式不正确 {l_id}")
        return False


def get_valid_tracking_numbers(tracking_numbers_string: str | None) -> list[str]:
    """
    返回有效的物流单号信息。
    :param tracking_numbers_string:
    :return:
    """
    logistics_nos = split_string_to_list(tracking_numbers_string)
    return [x for x in logistics_nos if check_tracking_number(x)]
