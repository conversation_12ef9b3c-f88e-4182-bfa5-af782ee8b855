from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase


class AssignStringExecutor(AutoJobControllerBase):
    """
    常量赋值，待实现变量赋值。
    用户实际场景如下：
        在一个补发大场景下需要基于一些外部条件区分不同的补发子场景完成补发，而在没有常量赋值的能力前，
        不同的补发子场景是没办法被记录下来的，因此在数据层面无法区分这些子场景。
    """
    job_type = JobType.ASSIGN_STRING

    def process(self):
        from_string = self.args.get_arg_by_task_arg_name('from_string')
        self.states.write_job_output(dict(to_string=from_string))
        return JobStatus.SUCCEED, None
