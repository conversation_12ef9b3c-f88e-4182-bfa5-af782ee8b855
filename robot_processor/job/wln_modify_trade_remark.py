import typing as t

from dramatiq import Retry
from pydantic import BaseModel

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.wln import WlnClient
from rpa.erp.wln.schemas import WlnModifyTradeRemarkReq


class WlnModifyTradeRemarkOutputModel(BaseModel):
    result: str


class WlnModifyTradeRemarkExecutor(AutoJobControllerBase):
    job_type = JobType.WLN_MODIFY_TRADE_REMARK
    output_model = WlnModifyTradeRemarkOutputModel

    def process(self) -> t<PERSON>[JobStatus, t.Union[str, Exception, None]]:
        trades = self.args.get_trades()
        # XD 开头的万里牛系统单号
        bill_code = self.args.get_arg_as_str_by_task_arg_name("bill_code")
        if len(trades) == 0 and not bill_code:
            return JobStatus.FAILED, "订单号和 XD 系统单号至少要有一项"
        # 当使用平台订单号去备注，如果订单在系统内拆单了，则会随机备注其中一笔。
        if not bill_code:
            bill_code = trades[0].tid

        remark = self.args.get_arg_by_task_arg_name("remark")
        if not remark:
            return JobStatus.FAILED, "未填写备注"

        wln_client = WlnClient.init_by_sid(self.job_wrapper.shop.sid)

        req = WlnModifyTradeRemarkReq(
            bill_code=bill_code,
            remark=remark,
        )

        # 修改数据类型（如果没有设定，则默认备注到“系统备注”）
        if data_type := self.args.get_arg_by_task_arg_name("data_type"):
            req.data_type = data_type
        # 添加旗帜
        if flag := self.args.get_arg_by_task_arg_name("flag"):
            req.flag = flag
        # 修改备注方式
        if is_add := self.args.get_arg_by_task_arg_name("is_add"):
            req.is_add = is_add

        resp = wln_client.modify_trade_remark_with_req(req)
        if resp.code == 801:
            raise Retry(resp.message, delay=10000)

        if resp.code == 0:
            self.states.write_job_widget_collection({"result": "成功"})
            return JobStatus.SUCCEED, None
        return JobStatus.FAILED, "修改订单备注失败"
