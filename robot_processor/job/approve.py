from functools import cached_property

from robot_processor.enums import Job<PERSON>tat<PERSON>, JobProcessMark, SelectType, ApproveType
from robot_processor.form.models import Step
from robot_processor.job.base import JobControllerBase, JobExecuteResult
from robot_processor.assistant.schema import AccountDetailV2, AssistantV2
from robot_processor.business_order.models import JobApprover
from robot_processor.ext import db
from robot_processor.utils import cached_instance_method


class ApproveException(Exception):
    pass


class ApproveJobController(JobControllerBase):
    def run_job(self) -> JobExecuteResult:
        current_step: Step | None = self.job.step
        if current_step is None:
            return JobExecuteResult(
                status=JobStatus.FAILED,
                exc_info=f"未找到任务 {self.job.id} 的步骤"
            )
        self.init_approvers()
        self.job.start_timing()
        return JobExecuteResult(
            status=JobStatus.PENDING,
            process_mark=JobProcessMark.UNPROCESSED,
        )

    def init_approvers(self) -> None:
        """
        初始化审批人，即清空、重建。
        :return:
        """
        JobApprover.clean_by_job_id(self.job.id)
        for approver in self.get_all_valid_approvers():
            job_approver = JobApprover(
                job_id=self.job.id,
                step_uuid=self.job.step_uuid,
                user_id=approver.user_id,
            )
            db.session.add(job_approver)
        db.session.commit()

    @cached_property
    def latest_version_step_assistants(self) -> AssistantV2:
        """
        获取最新版的步骤上的 assistants_v2 信息。
        :return:
        """
        latest_version_step: Step | None = self.job.current_step() or self.job.step
        if latest_version_step is None:
            raise ApproveException(f"任务 {self.job.id} 缺失步骤信息")
        assistants = latest_version_step.get_assistants_v2()
        return assistants

    @cached_instance_method
    def get_all_valid_approvers(self, check_sequential_multi_sign: bool = False) -> list[AccountDetailV2]:
        """
        从最新的步骤信息上，获取最新的审批人。
        :return:
        """
        if all([
            check_sequential_multi_sign,
            (self.latest_version_step_assistants.select_type != SelectType.all),
            (self.latest_version_step_assistants.approve_type == ApproveType.SEQUENTIAL_MULTI_TO_SIGN)
        ]):
            return self.latest_version_step_assistants.details or []
        else:
            all_approvers = self.latest_version_step_assistants.get_latest_assignee_account_details(
                self.job.shop
            )
            return [approver for approver in all_approvers if approver.is_valid()]

    def get_operated_approver_ids(self) -> list[int]:
        """
        从 JobApprover 中获取执行过审批操作的审批人。
        :return:
        """
        job_approvers: list[JobApprover] = JobApprover.query.filter(
            JobApprover.job_id == self.job.id,
            JobApprover.is_approved.is_(True)
        ).all()
        return [job_approver.user_id for job_approver in job_approvers]

    def check_multi_to_sign_is_approved(self) -> bool:
        """
        检测会签与依次审批是否构成审批通过。
        如果返回 True，则说明审批通过。
        :param accessors:
        :return:
        """
        all_approver_ids = {i.user_id for i in self.get_all_valid_approvers()}
        operated_approver_ids = set(self.get_operated_approver_ids())
        if all_approver_ids.issubset(operated_approver_ids):
            return True
        else:
            return False

    def archive_operated_approvers(self, operated_approvers: list[int]) -> None:
        """
        将进行了审批操作的审批人进行归档。
        :param operated_approvers:
        :return:
        """
        self.job.extra_data_wrapper.extend_archived_operated_approver_ids(operated_approvers)

    def get_archived_operated_approvers(self) -> list[int]:
        """
        获取归档的、进行过审批操作的审批人。
        :return:
        """
        return list(self.job.extra_data_wrapper.get_archived_operated_approver_ids())

    def clean_archived_operated_approvers(self) -> None:
        """
        清空归档的、进行过审批操作的审批人。
        :return:
        """
        self.job.extra_data_wrapper.clean_archived_operated_approvers()
