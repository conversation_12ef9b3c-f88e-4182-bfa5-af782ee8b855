from hashlib import md5
from typing import Optional, <PERSON><PERSON>

from loguru import logger

from robot_processor.client.feishu import <PERSON><PERSON><PERSON>
from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase


class SendFeishuGroupWithWebhookExecutor(AutoJobControllerBase):
    job_type = JobType.SEND_FEISHU_GROUP_WITH_WEBHOOK

    def get_rate_limit_setting(self, conf: dict) -> Optional[Tuple[str, str]]:
        message_args = self.args.get_send_message_arguments()
        feishu_bot_hash = md5((message_args.feishu_webhook or "").encode()).hexdigest()

        per_feishu_bot_key = f"JOB_LIMITER_{self.job_type}_{feishu_bot_hash}"
        per_job_type_key = f"JOB_LIMITER_{self.job_type}"
        limit_key = per_feishu_bot_key
        conf_keys = [per_feishu_bot_key, per_job_type_key]

        for key in conf_keys:
            if limit_value := conf.get(key):
                return limit_key, limit_value
        return limit_key, "4/second"

    def process(self):
        msg_rules = self.args.get_send_message_arguments()
        if msg_rules.skip:
            logger.info("已配置跳过消息发送")
            return JobStatus.SUCCEED, None

        feishu_webhook = msg_rules.feishu_webhook
        if not feishu_webhook:
            return JobStatus.FAILED, "Webhook为空"
        content = msg_rules.content
        feishu_secret = msg_rules.feishu_secret or ""
        resp = Feishu().send_feishu_group_with_webhook(feishu_webhook, content, feishu_secret)
        # arg-type]
        if resp.code == 0:
            return JobStatus.SUCCEED, ""
        return JobStatus.FAILED, resp.msg
