import typing as t

import arrow
from flask import current_app
from loguru import logger
from pydantic import BaseModel

from robot_processor.client.logistics_clients.enum import LogisticsType, \
    InterceptionStatus, InterceptionExceptionType
from robot_processor.client.logistics_clients.exception_manager import \
    AbstractLogisticsClientException
from robot_processor.client.logistics_clients.logistics_client import \
    AbstractLogisticsIntercept
from robot_processor.enums import JobStatus
from robot_processor.ext import cache
from robot_processor.job import AutoJobControllerBase
from robot_processor.client.conf import app_config


class InterceptOutputModel(BaseModel):
    interceptStatus: str
    errorMsg: t.Optional[str] = ""
    suggestion: t.Optional[str] = ""
    matchErrorType: t.Optional[int]
    matchErrorMsg: t.Optional[str]
    # 拦截发起时间，格式为：2021-08-01 12:00:00
    # 用于拦截结果确认时，过滤发起拦截之前的物流轨迹信息
    interceptAt: t.Optional[str] = ""
    ztoCenterBizNo: str | None


class InterceptInputModel(BaseModel):
    """
    额外的字段可以继承自这个类
    """
    waybill_no: str
    logistics_type: str


class LogisticsInterceptExecutor(AutoJobControllerBase):
    output_model = InterceptOutputModel

    def logistics_intercept_cache_prefix(self):
        raise NotImplementedError

    def input_params(self) -> "InterceptInputModel":
        raise NotImplementedError

    def init_logistics_domain(self) -> dict:
        return dict()

    def cache_timeout_days(self):
        return int(current_app.config.get("LOGISTICS_TIMEOUT_DAYS", 15))

    def interception_create_success(self, resp=None):
        cache.set(self.cache_key(), self.cache_value(), timeout=60 * 60 * 24 * self.cache_timeout_days())
        self.write_data_final(
            InterceptionStatus.INTERCEPTION_CREATE_SUCCESS,
            intercept_at=arrow.now().format("YYYY-MM-DD HH:mm:ss")
        )

    def write_data_final(
        self, intercept_status: InterceptionStatus,
        suggestion=None, error_msg=None, match_error_type=None, match_error_msg=None,
        intercept_at=None
    ):
        data = InterceptOutputModel(
            interceptStatus=intercept_status.value,
            suggestion=suggestion,
            errorMsg=error_msg,
            matchErrorType=match_error_type,
            matchErrorMsg=match_error_msg,
            interceptAt=intercept_at
        )
        self.states.write_job_widget_collection(data.dict())

    def create_interception_adapter(self):
        raise NotImplementedError

    def process_by_match_error(
        self, match_error: AbstractLogisticsClientException,
        logistics_domain: AbstractLogisticsIntercept
    ):
        logger.info(f"match error:{match_error.error_type}, error_msg:{match_error.err_msg}")
        if match_error.error_type.is_intercepted():
            # 重复拦截的情况
            has_intercept = logistics_domain.check_interception_success_by_callback(self.input_params().waybill_no)
            interception_status, _ = logistics_domain\
                .check_interception_status_by_trace(self.input_params().waybill_no, self.has_interception_record())
            if has_intercept or interception_status == InterceptionStatus.SUCCESS:
                # 从飞梭历史的回调信息中，发现同一个运单号存在 拦截成功 的记录
                self.write_data_final(
                    InterceptionStatus.INTERCEPTED,
                    error_msg="该运单号曾有过拦截成功的记录",
                    match_error_type=InterceptionExceptionType
                    .MATCH_ERROR_INTERCEPTED.code,  # type: ignore[attr-defined]
                    match_error_msg=InterceptionExceptionType
                    .MATCH_ERROR_INTERCEPTED.value
                )
                return JobStatus.SUCCEED, None
            cache.set(self.cache_key(), self.cache_value(), timeout=60 * 60 * 24 * self.cache_timeout_days())
            self.write_data_final(
                InterceptionStatus.INTERCEPTION_CREATE_SUCCESS,
                suggestion="重复拦截, 请等待确认拦截状态",
                error_msg=match_error.err_msg,
                match_error_type=InterceptionExceptionType
                .MATCH_ERROR_INTERCEPTED_AND_WAIT_STATUS.code,  # type: ignore[attr-defined]
                match_error_msg=InterceptionExceptionType
                .MATCH_ERROR_INTERCEPTED_AND_WAIT_STATUS.value,
                # 重复拦截的场景，无法确认真实的拦截发起时间，不能过滤掉此次发起拦截之前的物流轨迹信息
                intercept_at=None,
            )
            return JobStatus.SUCCEED, None
        elif match_error.error_type.is_unknown():
            self.write_data_final(
                InterceptionStatus.INTERCEPTION_CREATE_FAILED,
                match_error.suggestion,
                match_error.err_msg,
                match_error_type=match_error.error_type.code,  # type: ignore[attr-defined]
                match_error_msg=match_error.error_type.value,
                # 拦截发起失败，intercept_at 无意义
                intercept_at=None,
            )
            return JobStatus.FAILED, match_error.err_msg
        elif match_error.error_type.is_retry_error():
            # 网点未签约/快件未揽收等可以重试的报错，步骤可以失败，可以重试
            self.write_data_final(
                InterceptionStatus.INTERCEPTION_CREATE_FAILED,
                match_error.suggestion,
                match_error.err_msg,
                match_error_type=match_error.error_type.code,  # type: ignore[attr-defined]
                match_error_msg=match_error.error_type.value,
                # 可重试的错误，记录一下每次拦截发起的时间作为参考
                intercept_at=arrow.now().format("YYYY-MM-DD HH:mm:ss")
            )
            return JobStatus.FAILED, match_error.suggestion
        # 已签收/已代收等不可重试的报错，步骤成功，写入失败的原因
        self.write_data_final(
            InterceptionStatus.INTERCEPTION_CREATE_FAILED,
            match_error.suggestion,
            match_error.err_msg,
            match_error_type=match_error.error_type.code,  # type: ignore[attr-defined]
            match_error_msg=match_error.error_type.value,
            # 拦截的物流已经签收，但是需要记录一下拦截发起的时间
            intercept_at=arrow.now().format("YYYY-MM-DD HH:mm:ss")
        )
        return JobStatus.SUCCEED, None

    def cache_key(self):
        waybill_no = self.input_params().waybill_no
        prefix = self.logistics_intercept_cache_prefix()
        return f"{prefix}{waybill_no}"

    def cache_value(self):
        return f"{self.job.business_order_id}#{str(self.job.id)}"

    def has_interception_record(self):
        return cache.get(self.cache_key()) is not None

    def before_check(self):
        waybill_no = self.input_params().waybill_no
        logistics_type = self.input_params().logistics_type
        if not (waybill_no and logistics_type):
            return False, "运单号和物流公司不得为空！"
        return True, None

    def process(self) -> t.Tuple[JobStatus, t.Union[str, Exception, None]]:
        is_ok, error_msg = self.before_check()
        if not is_ok:
            return JobStatus.FAILED, error_msg

        logistics_type = self.input_params().logistics_type
        logistics_domain = AbstractLogisticsIntercept(LogisticsType(logistics_type)).dispatch()
        logistics_domain.init(self.init_logistics_domain())

        # 如果存在缓存，则说明已经执行过了至少一次该任务。
        # 如果是极兔，则在此去查询 callback，检测是否拦截发起成功。
        if cache.get(self.cache_key()) and \
                LogisticsType(logistics_type) == LogisticsType.JT:
            # 查询最近的一次回调。
            latest_callback_message_payload = logistics_domain.get_latest_callback_message_payload(
                self.create_interception_adapter()
            )
            result = logistics_domain.parse_jt_callback_payload(latest_callback_message_payload)
            # 可能尚未收到回调，判断为成功，经由确认拦截状态来进行确认。
            if result is None:
                # 重写一遍拦截发起成功，避免短时间内有相同的订单号记录重复进入，导致没有第一次写入。
                self.interception_create_success()
                return JobStatus.SUCCEED, None
            # 如果回调结果为“拦截成功”或“拦截进行中”，则认为拦截发起成功。
            if result.interceptResult in ("success", "processing"):
                # 重写一遍拦截发起成功，避免短时间内有相同的订单号记录重复进入，导致没有第一次写入。
                self.interception_create_success()
                return JobStatus.SUCCEED, None
            else:
                if result.errorDesc == "系统中存在待审核/已审核的申请记录，请勿重复申请！":
                    self.write_data_final(
                        InterceptionStatus.INTERCEPTION_CREATE_SUCCESS,
                        suggestion="重复拦截, 请等待确认拦截状态",
                        error_msg=result.errorDesc,
                        match_error_type=InterceptionExceptionType
                        .MATCH_ERROR_INTERCEPTED_AND_WAIT_STATUS.code,  # type: ignore[attr-defined]
                        match_error_msg=InterceptionExceptionType
                        .MATCH_ERROR_INTERCEPTED_AND_WAIT_STATUS.value,
                        # 重复拦截的场景，无法确认真实的拦截发起时间，不能过滤掉此次发起拦截之前的物流轨迹信息
                        intercept_at=None,
                    )
                    cache.set(self.cache_key(), self.cache_value(),
                              timeout=60 * 60 * 24 * self.cache_timeout_days())
                    return JobStatus.SUCCEED, None
                if result.errorDesc == "你所查找的运单不存在":
                    self.write_data_final(
                        InterceptionStatus.INTERCEPTION_CREATE_FAILED,
                        "请检查运单号是否正确；如果正确，请等待一段时间，快件揽收后即可进行拦截。",
                        "你所查找的运单不存在，或尚未揽收",
                        match_error_type=InterceptionExceptionType
                        .MATCH_ERROR_UNPICKED.code,  # type: ignore[attr-defined]
                        match_error_msg=InterceptionExceptionType.MATCH_ERROR_UNPICKED.value,
                        # 可重试的错误，记录一下每次拦截发起的时间作为参考
                        intercept_at=arrow.now().format("YYYY-MM-DD HH:mm:ss")
                    )
                    # 删除缓存键，避免重试后继续进入这段逻辑。
                    cache.delete(self.cache_key())
                    return JobStatus.FAILED, "你所查找的运单不存在"
                if result.errorDesc == "运单已签收，无法申请！":
                    self.write_data_final(
                        InterceptionStatus.INTERCEPTION_CREATE_FAILED,
                        "与客户联络退回。",
                        "运单已签收，无法申请！",
                        match_error_type=InterceptionExceptionType
                        .MATCH_ERROR_SIGN.code,  # type: ignore[attr-defined]
                        match_error_msg=InterceptionExceptionType.MATCH_ERROR_SIGN.value,
                        intercept_at=None
                    )
                    # 删除缓存键，避免重试后继续进入这段逻辑。
                    cache.delete(self.cache_key())
                    return JobStatus.FAILED, "运单已签收，无法申请！"
                cache.delete(self.cache_key())
                return JobStatus.FAILED, result.errorDesc
        # 下发拦截指令
        is_ok, err_msg, resp = logistics_domain.create_interception(self.create_interception_adapter())
        if not is_ok:
            match_error = logistics_domain.exception_manager.match_error(resp, err_msg)
            # 根据错误来做一些处理，主要是根据报错判断是否可以重试步骤
            return self.process_by_match_error(match_error, logistics_domain)
        else:
            # 成功下发拦截指令
            self.interception_create_success(resp)
            if LogisticsType(logistics_type) == LogisticsType.JT:
                from robot_processor.business_order.tasks import package_jobs

                package_jobs.send_with_options(args=(self.job.business_order_id, self.job.id),
                                               delay=1000 * app_config.JT_INTERCEPTION_WAIT_CALLBACK_SECONDS)
                return JobStatus.RUNNING, None
            else:
                return JobStatus.SUCCEED, None
