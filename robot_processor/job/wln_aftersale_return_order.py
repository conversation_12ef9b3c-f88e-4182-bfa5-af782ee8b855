import typing as t

from dramatiq import Retry

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.wln import WlnClient
from rpa.erp.wln.schemas import WlnAfterSaleReturnOrder
from rpa.erp.wln.schemas import WlnAfterSaleReturnOrderResp


class WlnAfterSaleReturnOrderExecutor(AutoJobControllerBase):
    job_type = JobType.WLN_AFTERSALE_RETURN_ORDER
    output_model = WlnAfterSaleReturnOrder

    def process(self) -> t.<PERSON><PERSON>[JobStatus, t.Union[str, Exception, None]]:
        bill_code: str | None = self.args.get_arg_by_task_arg_name("bill_code")
        if not bill_code:
            return JobStatus.FAILED, "未填写售后单号"

        wln_client = WlnClient.init_by_sid(self.shop.sid)

        resp: WlnAfterSaleReturnOrderResp = wln_client.get_after_sale_return_order(bill_code)

        if resp.code == 801:
            raise Retry(resp.message, delay=10000)

        return_order: WlnAfterSaleReturnOrder | None = None

        for order in resp.data:
            if order.bill_code == bill_code:
                return_order = order
                break

        if return_order is None:
            return JobStatus.FAILED, "未找到指定售后单号的信息"

        self.states.write_job_widget_collection(return_order.dict())
        return JobStatus.SUCCEED, None
