from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.constants import JDL_INTERCEPT_CACHE_KEY_PREFIX
from robot_processor.enums import JobType
from robot_processor.job.confirm_logistics_intercept import \
    ConfirmLogisticsInterceptExecutor
from robot_processor.job.logistics_intercept import LogisticsInterceptExecutor


class JdlInterceptExecutor(LogisticsInterceptExecutor):
    from robot_processor.job.logistics_intercept import InterceptInputModel

    job_type = JobType.JDL_INTERCEPT

    def logistics_intercept_cache_prefix(self):
        return JDL_INTERCEPT_CACHE_KEY_PREFIX

    def init_logistics_domain(self):
        credentials = self.shop.get_logistic_grant_records(LogisticsType.JDL)
        return {"keys": credentials}

    def input_params(self) -> "InterceptInputModel":
        from robot_processor.job.logistics_intercept import InterceptInputModel
        return InterceptInputModel(
            waybill_no=self.args.get_arg_by_task_arg_name('waybillNo'),
            logistics_type=LogisticsType.JDL.value
        )

    def create_interception_adapter(self):
        return {"waybill_no": self.args.get_arg_by_task_arg_name('waybillNo')}


class JdlConfirmInterceptExecutor(ConfirmLogisticsInterceptExecutor):
    from robot_processor.job.confirm_logistics_intercept import \
        InterceptInputModel

    def logistics_intercept_cache_prefix(self):
        return JDL_INTERCEPT_CACHE_KEY_PREFIX

    job_type = JobType.JDL_CONFIRM_INTERCEPT

    def init_logistics_domain(self):
        credentials = self.shop.get_logistic_grant_records(LogisticsType.JDL)
        return {"keys": credentials}

    def input_params(self) -> "InterceptInputModel":
        from robot_processor.job.confirm_logistics_intercept import \
            InterceptInputModel

        return InterceptInputModel(
            waybill_no=self.args.get_arg_by_task_arg_name('waybillNo'),
            logistics_type=LogisticsType.JDL.value
        )
