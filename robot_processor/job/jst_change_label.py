from pydantic import BaseModel
from dramatiq.errors import Retry

from robot_processor.enums import JobStatus, JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.job.jst_trade import TradesFinder
from rpa.erp.jst import JstNewSDK, JstLimitError


class JobBaseOutput(BaseModel):
    succeed: bool


class JstChangeLabelExecutor(AutoJobControllerBase):
    job_type = JobType.JST_CHANGE_LABEL
    output_model = JobBaseOutput

    def process(self):
        o_id = self.args.get_arg_by_task_arg_name("o_id")
        tid = self.args.get_trades()[0].tid
        try:
            if o_id:
                shop_id = None
            else:
                # 只有tid的话需要查shop_id
                shop_id = TradesFinder(tid=tid, shop=self.shop, is_reissue="0").process()[0].shop_id

            labels = self.args.get_arg_as_str_by_task_arg_name("labels").split(",")
            action_type = int(self.args.get_arg_by_task_arg_name("action_type"))

            JstNewSDK(self.shop.sid or "").update_label(o_id, shop_id, tid, labels=labels, action_type=action_type)
        except JstLimitError as e:
            raise Retry(str(e), delay=1000)
        except Exception as e:
            return JobStatus.FAILED, str(e)
        return JobStatus.SUCCEED, None
