from robot_processor.business_order.models import InterceptEvent
from robot_processor.client.logistics_clients.enum import (
    LogisticsType,
    InterceptionStatus,
)
from robot_processor.enums import JobType
from robot_processor.ext import cache
from robot_processor.job.logistics_intercept import (
    LogisticsInterceptExecutor,
    InterceptInputModel,
)


class YTOInterceptReportExecutor(LogisticsInterceptExecutor):
    job_type = JobType.YTO_INTERCEPT_REPORT

    def logistics_intercept_cache_prefix(self):
        pass

    def input_params(self):
        return InterceptInputModel(
            waybill_no=self.args.get_arg_by_task_arg_name("waybill_no") or "",
            logistics_type=LogisticsType.YTO.value,
        )

    def init_logistics_domain(self):
        if self.shop.channel_id is None:
            return {"credentials": []}
        auth_account: str | None = self.args.get_arg_by_task_arg_name("auth_account")
        customer_keys = self.shop.get_logistic_grant_records(LogisticsType.YTO)
        if auth_account:
            customer_keys = [customer_key for customer_key in customer_keys if customer_key.get("partner_key")]
        return {"credentials": customer_keys}

    def interception_create_success(self, resp=None):
        InterceptEvent.record_wait_report_event(
            self.input_params().waybill_no,
            self.input_params().logistics_type,
            self.order.id,
        )
        cache.set(self.cache_key(), self.cache_value(),
                  timeout=60 * 60 * 24 * self.cache_timeout_days())
        self.write_data_final(InterceptionStatus.INTERCEPTION_CREATE_SUCCESS)

    def create_interception_adapter(self):
        return {
            "waybill_no": self.input_params().waybill_no,
            "wanted_desc": self.args.get_arg_by_task_arg_name("wanted_desc") or "拦截",
        }
