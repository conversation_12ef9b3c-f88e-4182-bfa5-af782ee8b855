from pydantic import BaseModel
from result import Ok, Err

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.client import doudian_cloud
from robot_processor.client.schema import DoudianAfterSaleListResp
from robot_processor.error.job_process import (
    ShopNotFound,
    PlatformNotSupported
)


class DoudianRefundInfoOutput(BaseModel):
    return_logistics_company_name: str | None  # 退货物流公司名称
    return_logistics_code: str | None  # 退货物流单号
    after_sales_status_zh: str | None  # 售后状态
    refund_status_zh: str | None  # 退款状态


class DoudianRefundInfoExecutor(AutoJobControllerBase):
    job_type = JobType.DOUDIAN_REFUND_INFO
    output_model = DoudianRefundInfoOutput

    def process(self):
        if not (shop := self.job_wrapper.shop):
            return JobStatus.FAILED, ShopNotFound()
        if not shop.is_doudian():
            return JobStatus.FAILED, PlatformNotSupported()
        after_sales_id = self.args.get_arg_by_task_arg_name("aftersales_id")
        if not after_sales_id:
            return JobStatus.FAILED, "售后单号未设定"
        try:
            resp = doudian_cloud.get_aftersale_list(store_id=shop.sid, aftersale_id=after_sales_id)
        except Exception as e:
            return JobStatus.FAILED, str(e)
        match self._construct_refund_info(resp):
            case Ok(output):
                self.states.write_job_widget_collection(output.dict())
                return JobStatus.SUCCEED, None
            case Err(error):
                return JobStatus.FAILED, error

    @staticmethod
    def _construct_refund_info(refund_response: DoudianAfterSaleListResp):
        from robot_processor.form.event.doudian_refund import convert_refund_status
        from robot_processor.form.event.doudian_refund import convert_after_sales_status
        if refund_response.total == 0:
            return Err("未找到对应的售后单信息")
        refund_info = refund_response.items[0]

        return Ok(DoudianRefundInfoOutput(
            return_logistics_company_name=refund_info.aftersale_info.return_logistics_company_name,
            return_logistics_code=refund_info.aftersale_info.return_logistics_code,
            after_sales_status_zh=convert_after_sales_status(refund_info.aftersale_info.aftersale_status),
            refund_status_zh=convert_refund_status(refund_info.aftersale_info.refund_status)
        ))
