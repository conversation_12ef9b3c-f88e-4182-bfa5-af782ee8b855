from typing import Dict, Any, List, Optional

from pydantic import BaseModel


class BasicForm(BaseModel):
    pass


class BasicArgument(BaseModel):
    def check(self) -> bool:
        return True

    def to_dict(self) -> Dict[str, Any]:
        if self.check():
            return self.dict(exclude_none=True)
        else:
            raise ValueError("缺少必要参数")

    @classmethod
    def get_all_fields(cls, by_alias: bool = True, prefix: Optional[str] = None) -> List[str]:
        """
        获取所有字段，多层结构的，会返回带前缀的字段信息。

        :param by_alias:
        :param prefix:
        :return:
        """
        fields = []
        for value in cls.__fields__.values():
            t = value.outer_type_
            if by_alias and value.has_alias:
                field = value.alias
            else:
                field = value.name
            if issubclass(t, BasicArgument):
                fields += t.get_all_fields(prefix=field)
            else:
                if prefix is not None:
                    fields.append("{}.{}".format(prefix, field))
                else:
                    fields.append(field)
        return fields

    @classmethod
    def parse_form(cls, form) -> "BasicArgument":
        raise NotImplementedError


class BasicResponse(BaseModel):
    pass
