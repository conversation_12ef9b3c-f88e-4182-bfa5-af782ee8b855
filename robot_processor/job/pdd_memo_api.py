import typing as t

from dramatiq import Retry
from loguru import logger

from robot_processor.client import pdd_bridge_client
from robot_processor.client.schema import PddOrderInformationGet
from robot_processor.enums import JobStatus, JobType, UpdateMemoType
from robot_processor.error.job_process import ShopNotFound
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.utils import text_append_now_datetime


class PDDMemoAPIExecutor(AutoJobControllerBase):
    job_type = JobType.PDD_MEMO_API

    def process(self):
        shop = self.job_wrapper.shop
        if not shop:
            return JobStatus.FAILED, ShopNotFound()
        sid = shop.sid
        seller_words = self.args.get_arg_by_task_arg_name('content')
        if not seller_words:
            return JobStatus.FAILED, "未配置备注内容"
        seller_words = t.cast(str, seller_words)
        time_flag = self.args.get_arg_by_task_arg_name('time_flag')
        time_formatter = self.args.get_arg_by_task_arg_name(
            "time_formatter") or 'MM-DD HH:mm'

        if time_flag:
            seller_words = text_append_now_datetime(seller_words,
                                                    time_formatter)

        trades = self.args.get_trades()
        tid = trades[0].tid if trades else ''
        try:
            logger.info("order_sn: {}", tid)
            if not tid:
                return JobStatus.FAILED, "订单号为空，无法备注"
            try:
                order_info = pdd_bridge_client.order_information_get(
                    store_id=sid,
                    payload=PddOrderInformationGet(order_sn=tid)
                ).order_info_get_response.order_info
            except Exception as error:
                logger.opt(exception=error).error(
                    f"获取订单信息失败, {tid=}, {error=}")
                return JobStatus.FAILED, f"无法找到原始订单: {tid}"
            old_memo = order_info.remark
            logger.info("旧备注: {}", old_memo)
            raw_mode = self.args.get_arg_by_task_arg_name("mode")
            if not raw_mode:
                return JobStatus.FAILED, "未提供备注类型"
            mode = UpdateMemoType[t.cast(str, raw_mode)]
            logger.info("备注类型: {}", mode.name)
            new_memo = self._generate_new_memo(mode, old_memo, seller_words)
            tag_name = self._get_tag_name()
            pdd_bridge_client.update_memo(
                store_id=sid,
                order_sn=tid,
                memo=new_memo,
                tag_name=tag_name
            )
            return JobStatus.SUCCEED, ""
        except Exception as ex:
            if "两次备注间隔时长需大于1秒" in str(ex):
                raise Retry(str(ex), 3000)
            return JobStatus.FAILED, str(ex)

    def _generate_new_memo(self, mode: UpdateMemoType,
                           old_memo: t.Optional[str],
                           new_memo: str
                           ) -> str:
        # 拼多多商家备注最多支持300个字符
        max_memo_size = 300
        if mode == UpdateMemoType.OVERWRITE:
            return new_memo
        if old_memo:
            ret = f"{old_memo}\n{new_memo}"
        else:
            ret = new_memo
        if mode == UpdateMemoType.COVER_OLD_PRIOR:
            ret = ret[:max_memo_size]
        elif mode == UpdateMemoType.COVER_NEW_PRIOR:
            start_idx = max(len(ret) - max_memo_size, 0)
            ret = ret[start_idx:]
        return ret

    def _get_tag_name(self) -> t.Optional[str]:
        remark_type = self.args.get_arg_by_task_arg_name("remarkTag") or "KEEP"
        remark_type = t.cast(str, remark_type)
        logger.info("插旗选项: {}", remark_type)
        option_mapper = {
            "KEEP": None,
            "YELLOW": "黄色",
            "PURPLE": "紫色",
            "GREEN": "绿色",
            "BLUE": "蓝色",
            "RED": "红色"
        }
        if remark_type not in option_mapper:
            raise Exception(f"非法的插旗选项: {remark_type}")
        return option_mapper[remark_type]
