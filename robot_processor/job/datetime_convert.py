from robot_types.core import TypeSpec
from robot_types.core import Value
from robot_types.core import Var
from robot_types.helper import ValueResolver
from robot_types.helper import serialize

from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.utils import raise_exception


class DatetimeConvertExecutor(AutoJobControllerBase):
    job_type = JobType.DATETIME_CONVERT

    def process(self):
        value_resolver = ValueResolver(self.order.data.copy())
        from_as_value = Value(
            TypeSpec("datetime"), var=Var(path=self.args.key_map["convert_from"]["key"])
        )
        from_datetime = (
            from_as_value.with_resolver(value_resolver)
            .resolve()
            .unwrap_or_else(raise_exception)
        )
        match self.args.key_map["convert_to"]["type"]:
            case "date":
                value = from_datetime.date()
            case "time":
                value = from_datetime.time()
            case _:
                raise ValueError(
                    f"Invalid convert type: {self.args.key_map['convert_to']['type']}"
                )
        self.states.write_job_output({"convert_to": serialize(value)})
        return JobStatus.SUCCEED, None
