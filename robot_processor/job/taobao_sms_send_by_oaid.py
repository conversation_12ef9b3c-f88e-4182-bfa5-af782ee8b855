from typing import List

import arrow
from flask import current_app
from loguru import logger
from pydantic import BaseModel
from result import Ok, Err

from robot_processor.client import lxk_taobao_client
from robot_processor.db import db
from robot_processor.enums import JobStatus, JobType
from robot_processor.job.auto_job import <PERSON>JobControllerBase
from robot_processor.job.taobao_trade import TradeDict
from robot_processor.plugin.trade_api import get_taobao_trade
from robot_processor.sms.models import SMSSendRecord


class SMSSendOutputModel(BaseModel):
    uid: str


class TaobaoSMSSendByOaidExecutor(AutoJobControllerBase):
    job_type = JobType.TAOBAO_SMS_SEND_BY_OAID
    output_model = SMSSendOutputModel

    def process(self):
        tid = self.args.get_trades()[0].tid
        oid = self.args.get_trades()[0].oid
        msg_rules = self.args.get_send_message_arguments()
        if msg_rules.skip:
            logger.info("已配置跳过消息发送")
            return JobStatus.SUCCEED, None
        access_tokens = current_app.config.get("LXK_ACCESS_TOKENS", {})
        if self.shop.sid not in access_tokens:
            return JobStatus.FAILED, "店铺缺少授权"
        access_token = access_tokens.get(self.shop.sid)
        sms_sign = current_app.config.get("SMS_SIGN", {})
        if self.shop.sid not in sms_sign:
            return JobStatus.FAILED, "找不到店铺的短信签名"
        sms_free_sign_name = sms_sign.get(self.shop.sid)
        template_code = current_app.config.get("SMS_TEMPLATE_CODE", "")
        trades: List[TradeDict] = get_taobao_trade(self.job_wrapper.shop.sid,
                                                   [tid])
        if not trades:
            return JobStatus.FAILED, "找不到订单信息"
        trade: TradeDict = trades[0]
        if not oid:
            oid = trade["orders"][0]["oid"]
        msg = msg_rules.content
        match lxk_taobao_client.sms_send_by_oaid(
            access_token, oid, sms_free_sign_name, template_code,
            trade["oaid"], {"content": msg}
        ):
            case Ok(result):
                now = arrow.now()
                ts = int(now.timestamp())
                uid = result["module"]
                record = SMSSendRecord(
                    org_id=self.order.org_id,
                    uid=uid,
                    send_ts=ts,
                    job_id=self.job.id,
                    phone_number=oid,
                    msg=msg
                )
                db.session.add(record)
                db.session.commit()
                self.states.write_job_widget_collection(SMSSendOutputModel(
                    uid=uid).dict())
                return JobStatus.SUCCEED, ""
            case Err(exception):
                return JobStatus.FAILED, str(exception)
