from pydantic import BaseModel

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.wdt import WdtClient, \
    StockinOrderQueryRefundResp


def status_to_readable_str(status: int):
    match status:
        case 10:
            return "已取消"
        case 20:
            return "编辑中"
        case 30:
            return "待审核"
        case 60:
            return "待结算"
        case 80:
            return "已完成"
    return str(status)


class Product(BaseModel):
    TID: str
    OID: str
    SPU: str
    SKU: str
    SPU_OUTER: str
    SKU_OUTER: str
    COUNT: int
    TITLE: str
    PRICE: float
    PICTURE: str


class WdtStockinOrderQueryRefundByRefundNoExecutor(AutoJobControllerBase):
    job_type = JobType.WDT_STOCKIN_ORDER_QUERY_REFUND_BY_REFUND_NO
    """
    旺店通企业版根据ERP退换单号获取退货入库单
    """

    def process(self):
        src_order_no = self.args.get_arg_by_task_arg_name("src_order_no")
        if not src_order_no:
            return JobStatus.FAILED, "没有ERP退换单号"
        resp: StockinOrderQueryRefundResp = WdtClient(
            self.shop.sid).stockin_order_query_refund(src_order_no)
        # 过滤掉已取消的，剩下的等到全部到已完成
        stockin_list = [stockin for stockin in resp.response.stockin_list if
                        stockin.status != 10]
        if len(stockin_list) == 0 or any(stockin.status not in [60, 80] for
                                         stockin in stockin_list):
            return JobStatus.FAILED, "没有获取到符合条件的退货入库单"
        products = []
        for stockin in resp.response.stockin_list:
            for detail in stockin.details_list:
                product = Product(
                    TID=stockin.tid if stockin.tid else "",
                    OID=stockin.oid if stockin.oid else "",
                    SPU="",
                    SKU="",
                    SPU_OUTER=detail.goods_no,
                    SKU_OUTER=detail.spec_no,
                    COUNT=int(detail.goods_count),
                    TITLE=detail.goods_name,
                    PICTURE="",
                    PRICE=detail.price
                )
                products.append(product)
        self.states.write_job_output({
            "status_str": status_to_readable_str(stockin_list[0].status),
            "skus": [product.dict() for product in products]})
        return JobStatus.SUCCEED, ""
