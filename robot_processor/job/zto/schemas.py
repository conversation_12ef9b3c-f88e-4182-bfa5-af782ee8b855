import typing
from typing import Optional, Dict, Any, List
from datetime import datetime

from loguru import logger
from pydantic import BaseModel, Field, root_validator, validator

from robot_processor.job.basic.schemas import BasicForm, BasicResponse, BasicArgument as Argument
from robot_processor.job.zto.enums import InterceptType, \
    CustomerType, IncidentType, PackageType, IncidentExpectedStatus, \
    InterceptExpectedStatus, ZTORPAType
from robot_processor.utils import combine_address_with_town


class KeyInfo(BaseModel):
    app_key: str = Field(min_length=5)
    app_secret: str = Field(min_length=5)


class Form(BasicForm):
    bill_code: str


class Response(BasicResponse):
    status: Optional[bool]
    status_code: Optional[str] = Field(alias="statusCode")
    message: Optional[str]


"""
发起拦截所需要的相关信息（表单、参数、响应）。
"""


class CreateInterceptForm(Form):
    class ReceiveInfo(BaseModel):
        name: str
        mobile: str
        address: str
        town: Optional[str]
        zone: str
        city: str
        state: str

    biz_type: int
    customer_name: Optional[str]
    receive_info: Optional[ReceiveInfo]

    @root_validator
    def check_receive_details(
            cls, # noqa
            content: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        针对特定的拦截类型来进行部分字段的校验。
        """
        if content.get("biz_type") in [
            InterceptType.MODIFY_RECEIVER_ADDRESS,
            InterceptType.RETURN_TO_SPECIFIED_ADDRESS,
        ]:
            error_keys = []
            for key, value in content.items():
                if key.startswith("receive") and value is None:
                    error_keys.append(key)
            if len(error_keys) != 0:
                logger.info("当前传入的信息为：{}".format(content))
                raise ValueError("缺少必填信息: " + ",".join(error_keys))
        return content


class CreateInterceptArgument(Argument):
    """
    发起拦截所需要的参数。
    """
    billCode: str = Field(alias="bill_code", description="运单号")
    requestId: Optional[str] = Field(alias="request_id", description="请求ID（幂等校验）")
    customerId: Optional[str] = Field(alias="customer_id", description="集团客户编码")
    customerName: Optional[str] = Field(alias="customer_name", description="客户名称")
    receivePhone: Optional[str] = Field(alias="receive_phone", description="修改收件人电话（拦截类型为3或4必填）")
    receiveUsername: Optional[str] = Field(alias="receive_username", description="修改收件人姓名（拦截类型为3或4必填）")
    receiveAddress: Optional[str] = Field(alias="receive_address", description="修改详细地址（拦截类型为3或4必填）")
    receiveDistrict: Optional[str] = Field(alias="receive_district", description="修改地址区（拦截类型为3或4必填）")
    receiveCity: Optional[str] = Field(alias="receive_city", description="修改地址市（拦截类型为3或4必填）")
    receiveProvince: Optional[str] = Field(alias="receive_province", description="修改地址省（拦截类型为3或4必填）")
    destinationType: InterceptType = Field(
        alias="destination_type",
        description="拦截类型:1 返回收件网点；2 返回寄件人地址；3 修改派送地址；4 退回指定地址（必填）"
    )
    thirdBizNo: Optional[str] = Field(alias="third_biz_no", description="外部业务单号")

    @root_validator
    def check_receive_details(
            cls, # noqa
            content: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        针对特定的拦截类型来进行部分字段的校验。
        """
        if content.get("destinationType") in [
            InterceptType.MODIFY_RECEIVER_ADDRESS,
            InterceptType.RETURN_TO_SPECIFIED_ADDRESS,
        ]:
            error_keys = []
            for key, value in content.items():
                if key.startswith("receive") and value is None:
                    error_keys.append(key)
            if len(error_keys) != 0:
                raise ValueError("缺少必填信息: " + ",".join(error_keys))
        return content

    def set_request_id_and_biz_no(self, request_id: str, biz_no: typing.Optional[str] = None) -> None:
        self.requestId = request_id
        self.thirdBizNo = biz_no or request_id

    def check(self) -> bool:
        if self.requestId is None or self.thirdBizNo is None:
            return False
        else:
            return True

    @classmethod
    def parse_form(cls, form: CreateInterceptForm) -> "CreateInterceptArgument":
        if form.biz_type not in [
            InterceptType.MODIFY_RECEIVER_ADDRESS,
            InterceptType.RETURN_TO_SPECIFIED_ADDRESS,
        ]:
            return cls(
                bill_code=form.bill_code,
                customer_name=form.customer_name,
                destination_type=form.biz_type,
            )
        else:
            return cls(
                bill_code=form.bill_code,
                customer_name=form.customer_name,
                receive_phone=form.receive_info and form.receive_info.mobile,
                receive_username=form.receive_info and form.receive_info.name,
                receive_address=combine_address_with_town(
                    form.receive_info and form.receive_info.town,
                    form.receive_info and form.receive_info.address,
                ),
                receive_district=form.receive_info and form.receive_info.zone,
                receive_city=form.receive_info and form.receive_info.city,
                receive_province=form.receive_info and form.receive_info.state,
                destination_type=form.biz_type,
            )


class CreateInterceptResponse(Response):
    class Data(BaseModel):
        center_biz_no: Optional[str] = Field(alias="centerBizNo")

    data: Optional[Data]


"""
创建售后工单所需要的相关信息（表单、参数、响应）。
"""


class CreateIncidentForm(Form):
    biz_type: str
    incident_desc: Optional[str]
    incident_mobile: Optional[str]
    incident_commodity: Optional[str]
    expected_compensation_amount: Optional[str]
    compensation_channel: Optional[str]
    collection_customer_mobile: Optional[str]
    collection_customer_account_name: Optional[str]
    collection_customer_account_code: Optional[str]


class CreateIncidentArgument(Argument):
    class Customer(Argument):
        name: str = Field(description="客户姓名")
        type: Optional[CustomerType] = Field(description="客户类型：0=其他，1=下单客户，2=发件客户，3=收件客户")

    class Incident(Argument):
        desc: str = Field(description="事件描述")
        mobile: Optional[str] = Field(description="反馈电话")
        commodity: Optional[str] = Field(description="快件内物名称")
        type: IncidentType = Field(description="中通售后工单类型")
        billCode: str = Field(alias="bill_code", description="中通运单号")

    class ExtraInfo(Argument):
        expectedCompensationAmount: Optional[str] = Field(
            alias="expected_compensation_amount", description="期望赔付金额（破损、短少、遗失场景使用）"
        )
        compensationChannel: Optional[str] = Field(
            alias="compensation_channel", description="赔付渠道（破损、短少、遗失场景使用）"
        )
        collectionCustomerMobile: Optional[str] = Field(
            alias="collection_customer_mobile", description="收款客户电话（破损、短少、遗失场景使用）"
        )
        collectionCustomerAccountName: Optional[str] = Field(
            alias="collection_customer_account_name", description="客户收款账号名称（破损、短少、遗失场景使用）"
        )
        collectionCustomerAccountCode: Optional[str] = Field(
            alias="collection_customer_account_code", description="客户收款账号（破损、短少、遗失场景使用）"
        )

    traceId: Optional[str] = Field(alias="trace_id", description="请求唯一ID")
    customer: Optional[Customer] = Field(description="客户信息")
    incident: Optional[Incident] = Field(description="工单信息")
    extraInfo: Optional[ExtraInfo] = Field(alias="extra_info", description="其他售后信息")

    def set_trace_id_and_customer_name(self, trace_id: str, customer_name: str) -> None:
        self.traceId = trace_id
        self.customer = self.Customer(name=customer_name, type=CustomerType.SENDER)

    def check(self) -> bool:
        if self.traceId is None or self.customer is None:
            return False
        else:
            return True

    @staticmethod
    def get_label_by_biz_type(biz_type: str) -> str:
        match biz_type:
            case "event-urge":
                return "催件"
            case "event-sign-not-receive":
                return "核实签收未收到"
            case "damage-claim":
                return "理赔短少"
            case "lose-claim":
                return "理赔丢失"
            case "damage-compensation":
                return "破损赔付"
        return ""

    @classmethod
    def parse_form(cls, form: CreateIncidentForm) -> "CreateIncidentArgument":
        return cls(
            incident=cls.Incident(
                desc=form.incident_desc,
                mobile=form.incident_mobile,
                commodity=form.incident_commodity,
                type=form.biz_type,
                bill_code=form.bill_code,
            ),
            extra_info=cls.ExtraInfo(
                expected_compensation_amount=form.expected_compensation_amount,
                compensation_channel=form.compensation_channel,
                collection_customer_mobile=form.collection_customer_mobile,
                collection_customer_account_name=form.collection_customer_account_name,
                collection_customer_account_code=form.collection_customer_account_code,
            )
        )


class CreateIncidentResponse(Response):
    class Result(BaseModel):
        biz_no: Optional[str] = Field(alias="bizNo")

    result: Optional[Result]
    trace_id: Optional[str] = Field(alias="traceId")


"""
查询重量所需要的相关信息（表单、参数、响应）。
"""


class GetOrderInfoForm(Form):
    biz_type: int


class GetOrderInfoArgument(Argument):
    type: PackageType = Field(description="0=预约件; 1=全网件。当查询全网件订单信息只能使用运单号")
    orderCode: Optional[str] = Field(alias="order_code", description="预约件订单号")
    billCode: Optional[str] = Field(alias="bill_code", description="运单号")

    @root_validator
    def check_receive_details(
            cls, # noqa
            content: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        针对特定的包裹类型来进行部分字段的校验。
        """
        if content.get("type") == PackageType.DEFAULT and \
                content.get("billCode") is None:
            raise ValueError("缺少必填信息: bill_code")
        return content

    @classmethod
    def parse_form(cls, form: GetOrderInfoForm) -> "GetOrderInfoArgument":
        return cls(
            type=PackageType.DEFAULT,
            bill_code=form.bill_code,
        )


class GetOrderInfoResponse(Response):
    class Result(BaseModel):
        parcel_weight: Optional[int] = Field(alias="parcelWeight")
        bill_code: Optional[str] = Field(alias="billCode")

    result: Optional[List[Result]]


"""
查询拦截状态所需要的相关信息（表单、参数、响应）。
"""


class QueryInterceptForm(Form):
    expected_status: InterceptExpectedStatus


class QueryInterceptArgument(Argument):
    billCode: Optional[str] = Field(alias="bill_code")

    @classmethod
    def parse_form(cls, form: QueryInterceptForm) -> "QueryInterceptArgument":
        return cls(
            bill_code=form.bill_code,
        )


class QueryInterceptResponse(Response):
    class Data(BaseModel):
        bill_code: Optional[str] = Field(alias="billCode")
        service_code: Optional[str] = Field(alias="serviceCode")
        status: Optional[str]
        status_name: Optional[str] = Field(alias="statusName")

    data: Optional[Data]


"""
查询售后工单状态所需要的相关信息（表单、参数、响应）。
"""


class QueryIncidentForm(Form):
    biz_no: str
    expected_status: IncidentExpectedStatus


class QueryIncidentArgument(Argument):
    billCode: str = Field(alias="bill_code")
    bizNo: str = Field(alias="biz_no")

    @classmethod
    def parse_form(cls, form: QueryIncidentForm) -> "QueryIncidentArgument":
        return cls(
            bill_code=form.bill_code,
            biz_no=form.biz_no
        )


class QueryIncidentResponse(Response):
    class Result(BaseModel):
        class HandleRecord(BaseModel):
            handle_time: Optional[datetime] = Field(alias="handleTime")
            reply: Optional[str]

            @validator("handleTime", allow_reuse=True, check_fields=False)
            def convert_time(cls, value): # noqa
                if value:
                    if isinstance(value, int):
                        return value / 1000
                    else:
                        return value

        biz_no: Optional[str] = Field(alias="bizNo")
        bill_code: Optional[str] = Field(alias="billCode")
        incident_status: Optional[str] = Field(alias="incidentStatus")
        incident_message: Optional[str] = Field(alias="incidentMessage")
        handle_records: Optional[List[HandleRecord]] = Field(alias="handleRecords")

    result: Optional[Result]


"""
取消拦截所需要的相关信息（表单、参数、响应）。
"""


class CancelInterceptForm(Form):
    third_biz_no: str


class CancelInterceptArgument(Argument):
    thirdBizNo: str = Field(alias="third_biz_no")
    billCode: str = Field(alias="bill_code")

    @classmethod
    def parse_form(cls, form: CancelInterceptForm) -> "CancelInterceptArgument":
        return cls(
            third_biz_no=form.third_biz_no,
            bill_code=form.bill_code,
        )


class CancelInterceptResponse(Response):
    data: Any


"""
RPA 客户端
"""


class RPACreateIncidentForm(BaseModel):
    biz_type: str
    bill_code: str
    zto_id: str
    description: Optional[str]


class RPACreateIncidentArgument(Argument):
    orderType: ZTORPAType = Field(alias="order_type")
    billCode: str = Field(alias="bill_code")
    description: Optional[str]

    @classmethod
    def parse_form(cls, form: RPACreateIncidentForm) -> "RPACreateIncidentArgument":
        return cls(
            order_type=form.biz_type,
            bill_code=form.bill_code,
            description=form.description if form.description is not None else ""
        )


class RPACreateIncidentResponse(BaseModel):
    class Result(BaseModel):
        success: Optional[bool]
        message: Optional[str]

    success: bool
    result: Optional[Result]


class RPAQueryIncidentForm(BaseModel):
    biz_type: str
    bill_code: str
    zto_id: str


class RPAQueryIncidentArgument(Argument):
    billCode: str = Field(alias="bill_code")

    @classmethod
    def parse_form(cls, form: RPAQueryIncidentForm) -> "RPAQueryIncidentArgument":
        return cls(bill_code=form.bill_code)


class ProcessNodeInfo(BaseModel):
    order_type_code: Optional[str] = Field(alias="orderTypeCode")
    process_node_messages: Optional[str] = Field(alias="processNodeMessages")


class RPAQueryIncidentResponse(BaseModel):
    class Result(BaseModel):
        success: Optional[bool]
        data: Optional[List[ProcessNodeInfo]]
        message: Optional[str]

    success: bool
    result: Optional[Result]
