from typing import Tuple, Optional, Dict, Any, Union, cast
from base64 import b64encode
from hashlib import md5
import json
from uuid import uuid4
from datetime import timezone, timedelta
from functools import cached_property

import requests
from pydantic import ValidationError
from loguru import logger

from robot_processor.enums import JobStatus, JobType
from robot_processor.client_mixins import Session
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.client.conf import app_config

from robot_processor.job.zto.enums import API, InterceptType, IncidentType, OrderInfoType, \
    IncidentExpectedStatus, InterceptExpectedStatus
from robot_processor.job.zto.schemas import KeyInfo, Response, \
    CreateIncidentForm, CreateIncidentArgument, CreateIncidentResponse,\
    CreateInterceptForm, CreateInterceptArgument, CreateInterceptResponse, \
    GetOrderInfoForm, GetOrderInfoArgument, GetOrderInfoResponse, \
    QueryIncidentForm, QueryIncidentArgument, QueryIncidentResponse, \
    QueryInterceptForm, QueryInterceptArgument, QueryInterceptResponse, \
    CancelInterceptForm, CancelInterceptArgument, CancelInterceptResponse


class GetAppKeyException(Exception):
    pass


class ZTOAPIExecutor(AutoJobControllerBase):
    session = Session()
    bill_code: Optional[str] = None
    third_biz_no: Optional[str] = None

    @cached_property
    def customer_keys(self) -> Dict[str, Any]:
        return json.loads(app_config.ZTO_CUSTOMER_KEYS)

    @staticmethod
    def generate_data_and_digest(body: Dict[str, Any], secret: str) -> Tuple[str, str]:
        """
        生成发送请求所需的请求体和签名。
        中通方面在获取到请求体后，可能会去除多余的的空格，然后基于处理后的请求体来校验数字签名。

        :param body:
        :param secret:
        :return:
        """
        data = json.dumps(body, separators=(",", ":"), ensure_ascii=False)
        content = data + secret
        result = b64encode(
            md5(content.encode("utf-8")).digest()
        ).decode()
        return data, result

    def get_api(self) -> Optional[API]:
        """
        解析并获取中通的 API 名称，并设定接口所需要的类型。

        :return:
        """
        raise NotImplementedError

    def get_request_body(self) -> Dict[str, Any]:
        """
        获取请求体所需要的参数。
        :return:
        """
        raise NotImplementedError

    def get_app_key_and_secret(self) -> Tuple[str, str]:
        """
        todo
        从 kiosk 进行信息获取。
        获取客户的 app_key 和 app_secret。

        :return:
        """
        shop = self.job.shop
        if shop is None:
            raise GetAppKeyException("job: {} 未找到对应的店铺信息".format(self.job.id))
        customer_key = self.customer_keys.get(shop.org_id)  # type: ignore[arg-type]
        if not isinstance(customer_key, dict):
            raise GetAppKeyException("org: {} 未正确设定中通密钥对".format(shop.org_id))
        try:
            key_info: KeyInfo = KeyInfo.parse_obj(customer_key)
            return key_info.app_key, key_info.app_secret
        except ValidationError:
            raise GetAppKeyException("org: {} 未正确设定中通密钥对".format(shop.org_id))

    def process(self) -> Tuple[JobStatus, str]:
        # 生成 URL。
        api = self.get_api()
        if api is None:
            return JobStatus.FAILED, "未找到指定的售后类型"
        url = "/".join([app_config.ZTO_DOMAIN, api])
        # 获取请求体参数。
        try:
            body = self.get_request_body()
        except ValidationError as e:
            return JobStatus.FAILED, f"参数配置不正确, 异常为 {e}"
        except Exception as e:
            return JobStatus.FAILED, str(e)

        # 获取 app_key 和 app_secret。
        try:
            app_key, app_secret = self.get_app_key_and_secret()
        except GetAppKeyException as e:
            return JobStatus.FAILED, str(e)

        # 根据 app_secret 和需要发送的信息进行组合，得到原生请求体和数字签名。
        data, digest = self.generate_data_and_digest(
            body, app_secret
        )
        # 组合请求头。
        headers = {
            "Content-Type": "application/json",
            "x-appKey": app_key,
            "x-dataDigest": digest,
        }

        # 发送请求。
        try:
            resp_content = self.post(url, data, headers)
        except Exception as e:
            return self.handle_request_exception(e)

        # 处理响应。
        success, result = self.handle_response(resp_content)

        if not success:
            return JobStatus.FAILED, cast(Response, result).json(ensure_ascii=False, sort_keys=True)

        self.states.write_job_output(result)  # type: ignore

        # 检测任务执行结果，可以进行一些特殊的判断处理。
        return self.check_result()

    def post(self, url: str, data: str, headers: dict) -> Dict[str, Any]:
        """
        发送请求，获取响应。

        :param url:
        :param data:
        :param headers:
        :return:
        """
        logger.info("job: {} 请求中通的内容为：{}".format(
            self.job.id,
            data
        ))
        # 发送请求。
        resp = self.session.post(
            url=url,
            data=data.encode("utf-8"),
            headers=headers
        )
        resp.raise_for_status()
        logger.info("job: {} 中通响应为：{}".format(
            self.job.id,
            resp.content.decode("utf-8")
        ))
        # 解析响应内容。
        resp_content: Dict[str, Any] = resp.json()
        return resp_content

    def handle_request_exception(self, err: Exception) -> Tuple[JobStatus, str]:
        """
        请求异常处理。

        :param err:
        :return:
        """
        if isinstance(err, requests.exceptions.Timeout):
            if isinstance(err, requests.exceptions.Timeout) and self.get_api() in [
                API.CREATE_INTERCEPT,
                API.CREATE_INCIDENT,
                API.CANCEL_INTERCEPT,
            ]:
                return JobStatus.FAILED, "请求中通接口服务超时，请自行核验（或咨询中通方面）是否创建成功。"
            return JobStatus.FAILED, "请求中通接口服务超时"
        elif isinstance(err, requests.exceptions.JSONDecodeError):
            return JobStatus.FAILED, "解析响应内容时出错"
        else:
            return JobStatus.FAILED, f"请求发生异常, 异常为 {err}"

    def handle_response(self, resp_content: Dict[str, Any]) -> Tuple[bool, Union[Dict[str, Any], Response]]:
        raise NotImplementedError

    def check_result(self) -> Tuple[JobStatus, str]:
        return JobStatus.SUCCEED, ""


class ZTOInterceptExecutor(ZTOAPIExecutor):
    """
    发起拦截。
    """
    job_type = JobType.ZTO_INTERCEPT

    def get_api(self) -> Optional[API]:
        raw_biz_type = self.args.get_arg_by_task_arg_name("biz_type")
        try:
            InterceptType(int(raw_biz_type))
        except ValueError:
            return None

        return API.CREATE_INTERCEPT

    def get_request_body(self) -> Dict[str, Any]:
        """
        生成"发起拦截"所需的请求体内容。
        :return:
        """
        data = {}
        for field in CreateInterceptForm.__fields__.keys():
            data.update({
                field: self.args.get_arg_by_task_arg_name(field)
            })
        form = CreateInterceptForm(**data)
        arg = CreateInterceptArgument.parse_form(form)
        self.bill_code = form.bill_code
        self.third_biz_no = str(uuid4())
        # 插入待补充值。
        arg.set_request_id_and_biz_no(
            request_id=self.third_biz_no,
        )
        return arg.to_dict()

    def handle_response(
            self, resp_content: Dict[str, Any],
    ) -> Tuple[bool, Union[Dict[str, Any], Response]]:
        """
        处理"发起拦截"的响应内容。

        :param resp_content:
        :return:
        """
        response: CreateInterceptResponse = CreateInterceptResponse.parse_obj(resp_content)
        if response.status and response.data:
            return True, {"biz_no": response.data.center_biz_no, "third_biz_no": self.third_biz_no}
        else:
            return False, response


class ZTOIncidentExecutor(ZTOAPIExecutor):
    """
    发起售后。
    """
    job_type = JobType.ZTO_INCIDENT

    def get_api(self) -> Optional[API]:
        raw_biz_type = self.args.get_arg_by_task_arg_name("biz_type")
        try:
            IncidentType(raw_biz_type)
        except ValueError:
            return None

        return API.CREATE_INCIDENT

    def get_request_body(self) -> Dict[str, Any]:
        """
        生成"发起售后"所需的请求体内容。
        :return:
        """
        data = {}
        for field in CreateIncidentForm.__fields__.keys():
            data.update({
                field: self.args.get_arg_by_task_arg_name(field)
            })
        form = CreateIncidentForm(**data)
        if form.incident_desc is None:
            form.incident_desc = \
                CreateIncidentArgument.get_label_by_biz_type(form.biz_type)
        arg = CreateIncidentArgument.parse_form(form)
        self.bill_code = form.bill_code
        # 查询任务对应的店铺名。
        customer_name = self.shop.title or ""
        trace_id = str(uuid4())
        # 插入待补充值。
        arg.set_trace_id_and_customer_name(
            trace_id=trace_id,
            customer_name=customer_name,
        )
        return arg.to_dict()

    def handle_response(
            self, resp_content: Dict[str, Any],
    ) -> Tuple[bool, Union[Dict[str, Any], Response]]:
        """
        处理"发起售后"的响应内容。

        :param resp_content:
        :return:
        """
        response: CreateIncidentResponse = CreateIncidentResponse.parse_obj(resp_content)
        if response.status and response.result:
            return True, {"biz_no": response.result.biz_no}
        else:
            return False, response


class ZTOGetOrderInfoExecutor(ZTOAPIExecutor):
    """
    查询运单详情。
    """
    job_type = JobType.ZTO_GET_ORDER_INFO

    def get_api(self) -> Optional[API]:
        raw_biz_type = self.args.get_arg_by_task_arg_name("biz_type")
        try:
            OrderInfoType(int(raw_biz_type))
        except ValueError:
            return None

        return API.GET_ORDER_INFO

    def get_request_body(self) -> Dict[str, Any]:
        """
        生成"查询运单详情"所需的请求体内容。
        :return:
        """
        data = {}
        for field in GetOrderInfoForm.__fields__.keys():
            data.update({
                field: self.args.get_arg_by_task_arg_name(field)
            })
        form = GetOrderInfoForm(**data)
        arg = GetOrderInfoArgument.parse_form(form)
        return arg.to_dict()

    def handle_response(
            self, resp_content: Dict[str, Any],
    ) -> Tuple[bool, Union[Dict[str, Any], Response]]:
        """
        处理"查重量"的响应。

        :param resp_content:
        :return:
        """
        response = GetOrderInfoResponse.parse_obj(resp_content)
        if response.status:
            result = response.result
            if not result:
                return True, {}
            # 目前只获取列表中第一项的数据信息
            weight = result[0].parcel_weight
            return True, {
                "weight": str(weight),
            }
        else:
            return False, response


class ZTOQueryExecutor(ZTOAPIExecutor):
    expected_status: Optional[str] = None
    zto_current_status: Optional[str] = None

    def check_result(self) -> Tuple[JobStatus, str]:
        if self.zto_current_status != self.expected_status:
            message = "中通工单当前状态：{}, 客户期望状态：{}，未达到预期。".format(
                self.zto_current_status,
                self.expected_status,
            )
            return JobStatus.FAILED, message
        return super().check_result()


class ZTOQueryIncidentExecutor(ZTOQueryExecutor):
    """
    查询售后工单。
    """
    job_type = JobType.ZTO_QUERY_INCIDENT
    expected_status: Optional[IncidentExpectedStatus] = None
    zto_current_status: Optional[str] = None

    def get_api(self) -> Optional[API]:
        return API.QUERY_INCIDENT

    def get_request_body(self) -> Dict[str, Any]:
        """
        生成"查询售后工单"所需的请求体内容。
        :return:
        """
        data = {}
        for field in QueryIncidentForm.__fields__.keys():
            data.update({
                field: self.args.get_arg_by_task_arg_name(field)
            })
        form = QueryIncidentForm(**data)
        self.expected_status = form.expected_status
        arg = QueryIncidentArgument.parse_form(form)
        return arg.to_dict()

    def handle_response(
            self, resp_content: Dict[str, Any],
    ) -> Tuple[bool, Union[Dict[str, Any], Response]]:
        """
        处理"查询售后工单"的响应。

        :param resp_content:
        :return:
        """
        response: QueryIncidentResponse = QueryIncidentResponse.parse_obj(resp_content)
        if response.status:
            result = response.result
            if result is None:
                return True, {
                    "actions": "暂无消息",
                    "incident_message": "暂无消息"
                }
            self.zto_current_status = result.incident_status
            actions = "\n".join([
                "时间：{} 信息：{}".format(
                    r.handle_time.astimezone(  # type: ignore
                        timezone(timedelta(hours=8))
                    ).strftime('%Y-%m-%d %H:%M:%S'),
                    r.reply or "无"
                ) for r in result.handle_records  # type: ignore
            ])
            return True, {
                "actions": actions,
                "incident_message": result.incident_message,
            }
        else:
            return False, response


class ZTOQueryInterceptExecutor(ZTOQueryExecutor):
    """
    查询拦截工单。
    """
    job_type = JobType.ZTO_QUERY_INTERCEPT
    expected_status: Optional[InterceptExpectedStatus] = None
    zto_current_status: Optional[str] = None

    def get_api(self) -> Optional[API]:
        return API.QUERY_INTERCEPT

    def get_request_body(self) -> Dict[str, Any]:
        """
        生成"查询拦截工单"所需的请求体内容。
        :return:
        """
        data = {}
        for field in QueryInterceptForm.__fields__.keys():
            data.update({
                field: self.args.get_arg_by_task_arg_name(field)
            })
        form = QueryInterceptForm(**data)
        self.expected_status = form.expected_status
        arg = QueryInterceptArgument.parse_form(form)
        return arg.to_dict()

    def handle_response(
            self, resp_content: Dict[str, Any],
    ) -> Tuple[bool, Union[Dict[str, Any], Response]]:
        """
        处理"查询拦截工单"的响应。

        :param resp_content:
        :return:
        """
        response: QueryInterceptResponse = QueryInterceptResponse.parse_obj(resp_content)
        if response.status:
            data = response.data
            if data is None:
                return True, {
                    "status_name": "中通服务未返回相关信息"
                }
            self.zto_current_status = data.status
            return True, {
                "status_name": data.status_name if data.status_name is not None else "",
            }
        else:
            return False, response


class ZTOCancelInterceptExecutor(ZTOAPIExecutor):
    """
    取消拦截。
    """
    job_type = JobType.ZTO_CANCEL_INTERCEPT

    def get_api(self) -> Optional[API]:
        return API.CANCEL_INTERCEPT

    def get_request_body(self) -> Dict[str, Any]:
        """
        生成"取消拦截"所需的请求体内容。
        :return:
        """
        third_biz_no = str(uuid4())
        form = CancelInterceptForm(
            bill_code=self.args.get_arg_by_task_arg_name("bill_code"),
            third_biz_no=third_biz_no
        )
        arg = CancelInterceptArgument.parse_form(form)
        return arg.to_dict()

    def handle_response(
            self, resp_content: Dict[str, Any],
    ) -> Tuple[bool, Union[Dict[str, Any], Response]]:
        """
        处理"取消拦截"的响应。

        :param resp_content:
        :return:
        """
        response: CancelInterceptResponse = CancelInterceptResponse.parse_obj(resp_content)
        if response.status:
            return True, {
                "status": "成功发起取消",
            }
        else:
            return False, response
