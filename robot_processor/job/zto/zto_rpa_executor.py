import re
from typing import <PERSON><PERSON>

from loguru import logger
from pydantic import ValidationError
from result import Ok, Err

from robot_processor.enums import JobType, JobStatus
from robot_processor.job import AutoJobControllerBase
from robot_processor.job.zto.enums import ZTORPAType, ZTORPAMethod
from robot_processor.job.zto.schemas import RPACreateIncidentForm, RPACreateIncidentArgument, \
    RPAQueryIncidentForm, RPAQueryIncidentArgument, RPAQueryIncidentResponse, ProcessNodeInfo
from rpa.mola import MolaClient


class ZTORPACreateIncidentExecutor(AutoJobControllerBase):
    """
    RPA 客户端创建售后工单。
    """
    job_type: JobType = JobType.ZTO_RPA_CREATE_INCIDENT

    def process(self) -> Tuple[JobStatus, str]:
        # 获取请求参数。
        data = {}
        for field in RPACreateIncidentForm.__fields__.keys():
            data.update({field: self.args.get_arg_by_task_arg_name(field)})
        logger.info("创建售后单 data: {}".format(data))
        try:
            form = RPACreateIncidentForm(**data)
            # 选取一个中通运单号字段作为 Mola 的 payload 所需要的 id。
            arg = RPACreateIncidentArgument.parse_form(form)
            request_data = arg.to_dict()
        except ValidationError as e:
            return JobStatus.FAILED, f"参数配置不正确, 异常为: {e}"
        # 发送 Mola 请求。
        resp = MolaClient(form.zto_id).call_namespace_method('zto-vip', ZTORPAMethod.CREATE, request_data)
        match resp:
            case Ok():
                return JobStatus.SUCCEED, ""
            case Err(error_message):
                return JobStatus.FAILED, error_message


class ZTORPAQueryIncidentExecutor(AutoJobControllerBase):
    """
    查询售后单处理情况。

    """
    job_type: JobType = JobType.ZTO_RPA_QUERY_INCIDENT

    def process(self) -> Tuple[JobStatus, str]:
        # 获取请求参数。
        data = {}
        for field in RPAQueryIncidentForm.__fields__.keys():
            data.update({field: self.args.get_arg_by_task_arg_name(field)})
        logger.info("查询售后单 data: {}".format(data))
        try:
            form = RPAQueryIncidentForm(**data)
            arg = RPAQueryIncidentArgument.parse_form(form)
            request_data = arg.to_dict()
        except ValidationError as e:
            return JobStatus.FAILED, f"参数配置不正确, 异常为: {e}"
        # 发送 Mola 请求。
        mola_resp = MolaClient(form.zto_id).call_namespace_method('zto-vip', ZTORPAMethod.QUERY, request_data)
        match mola_resp:
            case Err(error_message):
                return JobStatus.FAILED, error_message
            case Ok(body):
                resp = RPAQueryIncidentResponse(**body)
                if resp.result is None:
                    return JobStatus.FAILED, "Mola 功能响应内容为空"
                process_nodes: list[ProcessNodeInfo] = resp.result.data or []
                info = next((node for node in process_nodes if node.order_type_code == form.biz_type), None)
                if not info:
                    return JobStatus.FAILED, "尚未成功获取到有效信息，请检查是否已基于 RPA 客户端或者快递管家创建了售后单。如果已经创建，请稍后重试。"

                if info.process_node_messages is None:
                    logger.warning(f"中通 RPA mola response: {body}")
                    return JobStatus.FAILED, "尚未获取到信息，请稍后重试或者咨询中通客服。"

                output = info.process_node_messages
                weight_pattern = r'(([1-9]\d*\.?\d*)|(0\.\d*[1-9])).*'
                if form.biz_type == ZTORPAType.CHECK_GOODS_WEIGHT:
                    # 如果是查询重量，会进行一次正则匹配。
                    if r := re.search(weight_pattern, info.process_node_messages):
                        output = r.group()

                self.states.write_job_output({'process_node_messages': output})
                return JobStatus.SUCCEED, ''
