from io import BytesIO

import pandas as pd
import requests
from pydantic import BaseModel

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase


class Spec(BaseModel):
    color: str
    size: str
    number: str
    out_of_stock_days: str


class Goods(BaseModel):
    goods_no: str
    spec_list: list[Spec]


class Supplier(BaseModel):
    supplier_name: str
    goods_list: list[Goods]


class Record(BaseModel):
    date: str
    supplier_list: list[Supplier]


class Output(BaseModel):
    records: list[Record]


class CustomizeOrg254Executor(AutoJobControllerBase):
    job_type = JobType.CUSTOMIZE_ORG_254
    output_model = Output

    def process(self) -> tuple[JobStatus, str | None]:
        attachment = self.args.get_arg_by_task_arg_name("attachment")
        attachment = attachment[0]
        if not attachment or not attachment.get("url"):
            return JobStatus.FAILED, "没有找到附件"
        url = attachment["url"]
        response = requests.get(url)
        data = BytesIO(response.content)
        excel_data = pd.read_excel(data)
        excel_data[["日期", "供应商", "款式"]] = excel_data[
            ["日期", "供应商", "款式"]].fillna(method='ffill')

        # 初始化结果列表
        result = []

        # 遍历 Excel 数据,构建 JSON 格式
        for date, group in excel_data.groupby('日期'):
            supplier_list = []
            for supplier, supplier_group in group.groupby('供应商'):
                goods_list = []
                for goods, style_group in supplier_group.groupby('款式'):
                    spec_list = []
                    for _, row in style_group.iterrows():
                        spec = Spec(
                            color=str(row['颜色']),
                            size=str(row['尺码']),
                            number=str(row['数量']),
                            out_of_stock_days="" if str(
                                row['缺货天数']) == "nan" else str(
                                row['缺货天数'])
                        )
                        spec_list.append(spec)
                    goods = Goods(
                        goods_no=str(goods),
                        spec_list=spec_list
                    )
                    goods_list.append(goods)
                supplier = Supplier(
                    supplier_name=str(supplier),
                    goods_list=goods_list
                )
                supplier_list.append(supplier)
            record = Record(
                date=str(date),
                supplier_list=supplier_list
            )
            result.append(record)
        self.states.write_job_widget_collection(Output(records=result).dict())
        return JobStatus.SUCCEED, ""
