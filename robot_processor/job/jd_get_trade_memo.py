import typing as t

from pydantic import BaseModel, Field
from requests.exceptions import Timeout

from robot_processor.client import jd_lyzr_client
from robot_processor.client.jd import PopOrderQueryVenderRemarkResponse
from robot_processor.enums import JobStatus, JobType, ShopPlatform
from robot_processor.job.auto_job import AutoJobControllerBase


class Result(BaseModel):
    order_id: str | None = Field(description="订单编号")
    remark: str | None = Field(description="备注信息")
    created: str | None = Field(description="备注创建时间")
    modified: str | None = Field(description="备注最后修改时间")
    flag: str | None = Field(description="标识颜色")
    success: bool = Field(description="是否执行成功")
    result_describe: str = Field(description="结果描述")


class JdGetTradeMemoExecutor(AutoJobControllerBase):
    job_type = JobType.JD_GET_TRADE_MEMO
    output_model = Result

    def process(self) -> t.<PERSON>[JobStatus, t.Union[str, Exception, None]]:
        shop = self.job_wrapper.shop
        if not shop:
            return JobStatus.FAILED, "缺失店铺信息"
        sid = shop.sid
        if not sid:
            return JobStatus.FAILED, "缺失店铺 ID"
        if shop.platform != ShopPlatform.JD:
            return JobStatus.FAILED, "店铺非京东平台"

        trades = self.args.get_trades()
        if len(trades) == 0:
            return JobStatus.FAILED, "未输入订单号"

        tid = trades[0].tid
        if not tid:
            return JobStatus.FAILED, "未输入订单号"

        try:
            resp: PopOrderQueryVenderRemarkResponse = jd_lyzr_client.query_vender_remark(
                store_id=sid,
                order_id=tid,
            )
        except Timeout:
            return JobStatus.FAILED, "请求京东超时"

        if resp.venderRemarkQueryResult is None \
                or resp.venderRemarkQueryResult.api_jos_result is None:
            return JobStatus.FAILED, "缺失响应内容"

        success = bool(resp.venderRemarkQueryResult.api_jos_result.success)

        result = Result(
            success=success,
            result_describe=resp.venderRemarkQueryResult.api_jos_result.result_describe or "",
        )
        if success and (
            resp.venderRemarkQueryResult.vender_remark is None
            or resp.venderRemarkQueryResult.vender_remark.remark is None
        ):
            result = Result(
                success=False,
                result_describe="未能获取到订单备注",
            )
            self.states.write_job_widget_collection(result.dict())
            return JobStatus.SUCCEED, ""

        if resp.venderRemarkQueryResult.vender_remark is not None:
            result.order_id = resp.venderRemarkQueryResult.vender_remark.order_id
            result.remark = resp.venderRemarkQueryResult.vender_remark.remark
            result.created = resp.venderRemarkQueryResult.vender_remark.created_time
            result.modified = resp.venderRemarkQueryResult.vender_remark.modified_time
            result.flag = resp.venderRemarkQueryResult.vender_remark.flag_zh
        self.states.write_job_widget_collection(result.dict())
        return JobStatus.SUCCEED, ""
