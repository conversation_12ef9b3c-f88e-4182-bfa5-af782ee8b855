from pydantic import BaseModel

from robot_processor.client.jackyun_org_1935 import AfterSaleUploadRequest, \
    ReturnChange, ReturnChangeDetail, JackyunOrg1935Client
from robot_processor.enums import JobStatus, JobType
from robot_processor.job.auto_job import AutoJobControllerBase


class OutputModel(BaseModel):
    return_change_no: str


class JackyunOrg1935AftersaleUploadExecutor(AutoJobControllerBase):
    job_type = JobType.JACKYUN_ORG_1935_AFTERSALE_UPLOAD
    output_model = OutputModel

    def process(self):
        tid = self.args.get_trades()[0].tid
        remark = self.args.get_arg_by_task_arg_name("remark")
        reason = self.args.get_arg_by_task_arg_name("reason")
        barcode = self.args.get_arg_by_task_arg_name("barcode")
        count = self.args.get_arg_by_task_arg_name("count")
        warehouse_name = self.args.get_arg_by_task_arg_name("warehouse_name")
        request = AfterSaleUploadRequest(
            returnChange=ReturnChange(
                returnChangeDetails=[
                    ReturnChangeDetail(
                        price=0,
                        barcode=barcode,
                        reasonDesc="",
                        sendCount=count
                    )
                ],
                onlineTradeNo=tid,
                resendType="1",
                shopName=self.shop.nick,
                refundTypeCode="1",
                settlementType="1",
                reasonDesc="补发",
                sendOnly="1"
            )
        )
        if warehouse_name:
            request.returnChange.sendWarehouseName = warehouse_name
        if reason:
            request.returnChange.reasonDesc = reason
        if remark:
            request.returnChange.sellerMemo = remark
        resp = JackyunOrg1935Client().aftersale_upload(request)
        if resp.code == 200:
            output = OutputModel(
                return_change_no=resp.result.data.returnChange.returnChangeNo
            )
            self.states.write_job_widget_collection(output.dict())
            return JobStatus.SUCCEED, ""
        return JobStatus.FAILED, resp.msg
