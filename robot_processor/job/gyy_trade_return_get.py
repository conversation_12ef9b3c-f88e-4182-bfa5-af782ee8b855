import typing as t

from pydantic import BaseModel

from robot_processor.enums import JobType, JobStatus
from robot_processor.job import AutoJobControllerBase
from rpa.erp.guanyiyunapi.sdk import GyySDK
from rpa.erp.guanyiyunapi.schemas import TradeReturnGetRequest, TradeReturnOrder


class TradeReturnGetOutput(BaseModel):
    orders: list[TradeReturnOrder]


class GYYTradeReturnGetExecutor(AutoJobControllerBase):
    job_type = JobType.GYY_TRADE_RETURN_GET
    output_model = TradeReturnGetOutput

    def process(self) -> t.<PERSON><PERSON>[JobStatus, t.Union[str, Exception, None]]:
        trades = self.args.get_trades()
        code = self.args.get_arg_by_task_arg_name("code")

        if len(trades) == 0 and not code:
            return JobStatus.FAILED, "订单号和单据编号必填一项"

        tid = trades[0].tid if len(trades) < 0 else None
        created_at: str = self.args.get_arg_by_task_arg_name("created_at")

        req = TradeReturnGetRequest(
            platform_code=tid,
            code=code,
            start_create=created_at,
        )

        gyy_sdk = GyySDK(self.shop.sid)
        resp = gyy_sdk.trade_return_get(req)

        self.states.write_job_widget_collection({
            "orders": resp.tradeReturns or []
        })
        return JobStatus.SUCCEED, ""
