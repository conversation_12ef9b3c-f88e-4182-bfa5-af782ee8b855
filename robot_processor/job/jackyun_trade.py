from typing import Tuple, Union, List

from pydantic import BaseModel

from robot_processor.enums import JobType, JobStatus
from robot_processor.job import AutoJobControllerBase
from rpa.erp.jackyun.schemas import JackyunOrder
from rpa.erp.jackyun.sdk import JackyunQmSDK


class JackyunOrderOutput(BaseModel):
    orders: List[JackyunOrder]


class JackyunTradeExecutor(AutoJobControllerBase):
    job_type = JobType.JACKYUN_TRADE
    output_model = JackyunOrderOutput

    def process(self) -> Tuple[JobStatus, Union[str, Exception, None]]:
        trades = self.args.get_trades()
        tid = trades[0].tid
        if not tid:
            return JobStatus.FAILED, "订单号不能为空"
        trade_no = self.args.get_arg_by_task_arg_name("trade_no")
        source_after_no = self.args.get_arg_by_task_arg_name("source_after_no")
        trade_type = self.args.get_arg_by_task_arg_name("trade_type")
        # 快递单号
        logistic_no = self.args.get_arg_by_task_arg_name("logistic_no")
        jackyun_orders = JackyunQmSDK(self.job_wrapper.shop.sid).order_list(
            tid).response.jackyunData.trades
        if trade_no:
            jackyun_orders = [jackyun_order for jackyun_order in
                              jackyun_orders if jackyun_order.tradeNo ==
                              trade_no]
        if source_after_no:
            jackyun_orders = [jackyun_order for jackyun_order in
                              jackyun_orders if jackyun_order.sourceAfterNo
                              == source_after_no]
        if trade_type:
            jackyun_orders = [jackyun_order for jackyun_order in
                              jackyun_orders if jackyun_order.tradeType ==
                              trade_type]
        if logistic_no:
            jackyun_orders = [
                jackyun_order for jackyun_order in jackyun_orders
                if jackyun_order.mainPostid == logistic_no
            ]
        if self.shop.org_id == "320":
            for order in jackyun_orders:
                for detail in order.goodsDetail:
                    parts = detail.goodsName.split("-")
                    detail.supplier_name = parts[0] if parts else ""
        if not jackyun_orders:
            return JobStatus.FAILED, "没有找到订单"
        res = self.states.write_job_widget_collection({"orders": [i.dict()
                                                                  for i in
                                                                  jackyun_orders]})
        if res.missing:
            missing_str = ' / '.join(arg.readable for arg in res.missing)
            raise Exception(f"获取输出字段失败: {missing_str}")
        return JobStatus.SUCCEED, None
