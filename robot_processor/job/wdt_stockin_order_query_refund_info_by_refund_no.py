from typing import List

from pydantic import BaseModel

from robot_processor.enums import JobType, JobStatus
from robot_processor.shop.models import ErpInfo, ErpType
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.wdt import WdtClient, \
    StockinOrderQueryRefundResp, Stockin


def status_to_readable_str(status: int):
    match status:
        case 10:
            return "已取消"
        case 20:
            return "编辑中"
        case 30:
            return "待审核"
        case 60:
            return "待结算"
        case 80:
            return "已完成"
    return str(status)


class Output(BaseModel):
    stockin_list: List[Stockin]


class WdtStockinOrderQueryRefundInfoByRefundNoExecutor(AutoJobControllerBase):
    job_type = JobType.WDT_STOCKIN_ORDER_QUERY_REFUND_INFO_BY_REFUND_NO
    output_model = Output
    """
    旺店通企业版根据ERP退换单号获取退货入库单输出当前表单
    """

    def get_rate_limit_setting(self, conf: dict) -> tuple[str, str] | None:
        # 查询店铺绑定的旺店通企业版的授权
        erp_info: ErpInfo | None = self.job_wrapper.shop.erps.filter(
            ErpInfo.erp_type == ErpType.WDT
        ).order_by(
            ErpInfo.id.desc()
        ).first()
        if erp_info is None:
            return None

        wdt_sid = erp_info.meta.get("sid")
        if not wdt_sid:
            return None

        wdt_sid_limit_key = f"JOB_LIMITER_{self.job_type}_WDT_SID_{wdt_sid}"
        default_limit_key = f"JOB_LIMITER_{self.job_type}"
        conf_keys = [wdt_sid_limit_key, default_limit_key]
        for conf_key in conf_keys:
            if limit_value := conf.get(conf_key):
                return wdt_sid_limit_key, limit_value
        return None

    def process(self):
        src_order_no = self.args.get_arg_by_task_arg_name("src_order_no")
        if not src_order_no:
            return JobStatus.FAILED, "没有ERP退换单号"
        goods_titles_str = self.args.get_arg_by_task_arg_name("goods_titles")
        goods_titles = []
        if goods_titles_str:
            goods_titles = goods_titles_str.split("/")
        resp: StockinOrderQueryRefundResp = WdtClient(
            self.shop.sid).stockin_order_query_refund(src_order_no)
        # 过滤掉已取消的，剩下的等到全部到已完成
        stockin_list = [stockin for stockin in resp.response.stockin_list if
                        stockin.status != 10]
        if len(stockin_list) == 0 or any(stockin.status not in [60, 80] for
                                         stockin in stockin_list):
            return JobStatus.FAILED, "没有获取到符合条件的退货入库单"
        filter_list = []
        for stockin in stockin_list:
            if any(any(goods_title in details.goods_name for goods_title in
                       goods_titles) for details in
                   stockin.details_list):
                continue
            filter_list.append(stockin)
        for stockin in filter_list:
            for detail in stockin.details_list:
                detail.diff = int(detail.goods_count) - int(detail.num)
            stockin.diff_sum = sum(
                detail.diff for detail in stockin.details_list)
        self.states.write_job_widget_collection(
            {"stockin_list": [stockin.dict() for stockin in filter_list]})
        return JobStatus.SUCCEED, ""
