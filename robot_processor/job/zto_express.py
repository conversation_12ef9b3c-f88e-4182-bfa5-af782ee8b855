import json

import arrow
from pydantic import BaseModel
from pydantic import Field

from robot_processor.client.conf import app_config
from robot_processor.client.logistics_clients.enum import LogisticsType
from robot_processor.client.logistics_clients.schema import ZTOQueryLogisticItem
from robot_processor.client.logistics_clients.schema import ZTOQueryLogisticOutput
from robot_processor.client.logistics_clients.utils import get_filtered_traces
from robot_processor.client.logistics_clients.zto_domain import ZTOExpressClient
from robot_processor.constants import ZTO_INTERCEPT_CACHE_KEY_PREFIX
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.ext import cache
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.job.confirm_logistics_intercept import ConfirmLogisticsInterceptExecutor
from robot_processor.job.confirm_logistics_intercept import InterceptInputModel as ConfirmInterceptInputModel
from robot_processor.job.logistics_intercept import InterceptInputModel
from robot_processor.job.logistics_intercept import InterceptionStatus
from robot_processor.job.logistics_intercept import InterceptOutputModel
from robot_processor.job.logistics_intercept import LogisticsInterceptExecutor
from robot_processor.shop.models import Shop


def get_customer_key_by_shop(shop: Shop):
    customer_keys = shop.get_logistic_grant_records(LogisticsType.ZTO)
    if len(customer_keys) == 0:
        raise Exception("当前店铺未绑定任一中通密钥对")
    return {
        "keys": customer_keys,
        "org_id": shop.org_id,
    }


class ZTOQueryLogisticOutputModel(BaseModel):
    result: list[ZTOQueryLogisticItem] = Field(default_factory=list)


class ZTOQueryLogisticExecutor(AutoJobControllerBase):
    """
    中通查询物流轨迹。
    """

    job_type = JobType.ZTO_QUERY_LOGISTIC
    output_model = ZTOQueryLogisticOutputModel

    @property
    def customer_keys(self) -> dict[str, dict[str, str]]:
        return json.loads(app_config.ZTO_CUSTOMER_KEYS)

    def process(self) -> tuple[JobStatus, str | None]:
        waybill_no = self.args.get_arg_by_task_arg_name("waybill_no")
        if waybill_no is None:
            return JobStatus.FAILED, "缺少运单号"

        get_trace_records_method = self.args.get_arg_by_task_arg_name("get_trace_records_method")

        shop: Shop | None = self.job.shop
        if shop is None:
            return JobStatus.FAILED, "job: {} 未找到对应的店铺信息".format(self.job.id)

        customer_keys = get_customer_key_by_shop(shop)

        # 请求中通物流轨迹接口，查询结果。
        zto_client = ZTOExpressClient(LogisticsType.ZTO)
        zto_client.init(customer_keys)
        response = zto_client.query_trace({"bill_code": waybill_no})
        query_logistic_trace = ZTOQueryLogisticOutput(**response)
        if not query_logistic_trace.status:
            return JobStatus.FAILED, query_logistic_trace.message

        # 获取符合传入的运单号的物流轨迹。
        waybill_traces = [detail for detail in query_logistic_trace.result if detail.bill_code == waybill_no]
        # 如果没有获取到物流轨迹则直接返回成功
        if len(waybill_traces) == 0:
            self.states.write_job_widget_collection({})
            return JobStatus.SUCCEED, ""

        query_logistic_trace.result = waybill_traces

        filtered_traces, err = get_filtered_traces(
            query_logistic_trace.result,
            logistic_type=LogisticsType.ZTO,
            get_trace_records_method=get_trace_records_method,
        )
        if err:
            return JobStatus.FAILED, err
        query_logistic_trace.result = filtered_traces

        self.states.write_job_widget_collection(self.output_model(result=query_logistic_trace.result).dict())
        return JobStatus.SUCCEED, ""


class ZTOCancelLogisticExecutor(LogisticsInterceptExecutor):
    """
    物流中台 - 中通下发撤单拦截
    """

    job_type = JobType.ZTO_CANCEL_LOGISTIC

    def init_logistics_domain(self) -> dict:
        return get_customer_key_by_shop(self.shop)

    def before_check(self):
        waybill_no = self.args.get_arg_by_task_arg_name("waybill_no")
        if waybill_no is None:
            return False, "缺少运单号"
        return True, None

    def logistics_intercept_cache_prefix(self) -> str:
        return ZTO_INTERCEPT_CACHE_KEY_PREFIX

    def input_params(self) -> "InterceptInputModel":

        return InterceptInputModel(
            waybill_no=self.args.get_arg_by_task_arg_name("waybill_no"), logistics_type=LogisticsType.ZTO.value
        )

    def create_interception_adapter(self):
        return {
            "waybill_no": self.args.get_arg_by_task_arg_name("waybill_no"),
            # 拦截类型:1 返回收件网点；2 返回寄件人地址；
            "destination_type": self.args.get_arg_by_task_arg_name("destination_type"),
        }

    def interception_create_success(self, resp=None):
        """
        中通需要新增输出参数：ztoCenterBizNo，用于取消拦截。

        中通响应内容如下：（json 格式）
        {"data":{"centerBizNo":"TP1657345031754746903"},"message":null,"status":true,"statusCode":null}
        """
        if isinstance(resp, dict):
            center_biz_no = (resp.get("data") or {}).get("centerBizNo")
        else:
            center_biz_no = None
        data = InterceptOutputModel(
            interceptStatus=InterceptionStatus.INTERCEPTION_CREATE_SUCCESS.value,
            interceptAt=arrow.now().format("YYYY-MM-DD HH:mm:ss"),
            ztoCenterBizNo=center_biz_no,
        )
        cache.set(self.cache_key(), self.cache_value(), timeout=60 * 60 * 24 * self.cache_timeout_days())
        self.states.write_job_widget_collection(data.dict())


class ZTOConfirmInterceptExecutor(ConfirmLogisticsInterceptExecutor):
    """
    物流中台 - 中通确认拦截状态
    """

    job_type = JobType.ZTO_CONFIRM_INTERCEPT

    def logistics_intercept_cache_prefix(self):
        return ZTO_INTERCEPT_CACHE_KEY_PREFIX

    def init_logistics_domain(self) -> dict:
        return get_customer_key_by_shop(self.shop)

    def input_params(self) -> "ConfirmInterceptInputModel":

        return ConfirmInterceptInputModel(
            waybill_no=self.args.get_arg_by_task_arg_name("waybill_no"), logistics_type=LogisticsType.ZTO.value
        )
