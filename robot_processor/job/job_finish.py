from loguru import logger

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase


class JobFinishExecutor(AutoJobControllerBase):
    job_type = JobType.JOB_FINISH

    def process(self):
        job_status = self.args.get_arg_by_task_arg_name("job_status")
        logger.info(f"job_finish job_status:{job_status}")
        # 目前该自动化应用的配置中，job_status 没有分支条件，所以这里直接返回成功
        return JobStatus.SUCCEED, ''
