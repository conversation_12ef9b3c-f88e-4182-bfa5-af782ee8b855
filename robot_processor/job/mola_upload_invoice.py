from typing import Any

import arrow
from result import Err, Ok

from pydantic import BaseModel

from robot_processor.job import AutoJobControllerBase
from robot_processor.enums import JobType, JobStatus
from robot_processor.utils import filter_none
from rpa.mola.client import MolaClient


class OutputModel(BaseModel):
    result: str


class MolaUploadInvoiceExecutor(AutoJobControllerBase):
    job_type = JobType.MOLA_UPLOAD_INVOICE
    output_model = OutputModel

    def process(self) -> tuple[JobStatus, str]:
        # 获取请求参数。
        trades = self.args.get_trades()
        if len(trades) == 0:
            return JobStatus.FAILED, "未提供订单号"
        tid = trades[0].tid
        # 发票 pdf 下载地址
        invoice_url = self.args.get_arg_by_task_arg_name("invoice_url")
        # 千牛统一开票序号 例如'E5235555258P'
        invoice_serial_no = self.args.get_arg_by_task_arg_name("invoice_serial_no")
        # 发票号码 全电普票 20
        invoice_no = self.args.get_arg_by_task_arg_name("invoice_no")
        # 开票日期
        invoice_date = self.args.get_arg_by_task_arg_name("invoice_date")
        # 申请日期
        start_time = self.args.get_arg_by_task_arg_name("start_time")

        if not (invoice_url and isinstance(invoice_url, str)):
            return JobStatus.FAILED, "无效的参数：转存的发票 URL"
        if not (invoice_serial_no and isinstance(invoice_serial_no, str)):
            return JobStatus.FAILED, "无效的参数：发票序列号"
        if invoice_date and isinstance(invoice_date, str):
            invoice_date = arrow.get(invoice_date).format("YYYY-MM-DD")

        body: dict[str, str] = filter_none({
            "tid": tid,
            "invoiceUrl": invoice_url,
            "serialNo": invoice_serial_no,
            "invoiceNo": invoice_no,
            "invoiceDate": invoice_date,
            "startTime": start_time,
        })

        # 目前仅支持 天猫和淘宝 平台。
        if self.shop.platform == "TMALL":
            body.update({"platformCode": "TM"})
        elif self.shop.platform == "TAOBAO":
            body.update({"platformCode": "TB"})
        else:
            return JobStatus.FAILED, "暂不支持的平台"

        # 发送 Mola 请求。
        response = MolaClient(self.shop.sid).call_namespace_method(
            namespace="qianniu",
            method="invoice-submit-manual",
            data=body,
        )

        # 处理响应。
        match response:
            case Err(error_message):
                return JobStatus.FAILED, error_message
            case Ok(resp):
                result: dict[str, Any] = resp.get("result") or {}
                message = result.get("message")
                if message == "操作成功":
                    self.states.write_job_widget_collection({"result": "操作成功"})
                    return JobStatus.SUCCEED, ""
                else:
                    return JobStatus.FAILED, message or "发票上传失败"
