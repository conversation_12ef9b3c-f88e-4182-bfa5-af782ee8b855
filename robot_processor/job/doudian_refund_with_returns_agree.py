from pydantic import BaseModel

from robot_processor.client import doudian_cloud
from robot_processor.client.errors import DoudianCloudServiceError
from robot_processor.client.schema import DoudianAftersaleOperateResp, \
    DoudianAfterSaleOperateResult
from robot_processor.enums import JobStatus, JobType
from robot_processor.job import AutoJobControllerBase


class DoudianRefundWithReturnsAgreeOutput(BaseModel):
    execution_result: str


class DoudianRefundWithReturnsAgree(AutoJobControllerBase):
    job_type = JobType.DOUDIAN_REFUND_WITH_RETURNS_AGREE
    output_model = DoudianRefundWithReturnsAgreeOutput

    def process(self):
        aftersale_id = self.args.get_arg_by_task_arg_name("aftersale_id")
        if aftersale_id is None:
            return JobStatus.FAILED, "缺失售后单号"
        ignore_repetitive_operation_error = self.args.get_arg_by_task_arg_name(
            "ignore_repetitive_operation_error")
        resp: DoudianAftersaleOperateResp | None = None
        try:
            resp = doudian_cloud.doudian_refund_agree(
                store_id=self.shop.sid, aftersale_id=aftersale_id,
                operate_type=101)
        except DoudianCloudServiceError as e:
            # FIXME(<EMAIL>): 先查询售后单
            if "售后状态错误" in str(e):
                resp = doudian_cloud.doudian_refund_agree(
                    store_id=self.shop.sid, aftersale_id=aftersale_id,
                    operate_type=111)
            else:
                return JobStatus.FAILED, str(e)
        result: DoudianAfterSaleOperateResult = resp.items.pop()
        if result.status_code == 0:
            self.states.write_job_widget_collection({"execution_result": "成功"})
            return JobStatus.SUCCEED, ""
        if ignore_repetitive_operation_error == "true" and \
                "所审核售后单均已完结" in result.status_msg:
            self.states.write_job_widget_collection(
                {"execution_result": "售后单已被处理"})
            return JobStatus.SUCCEED, ""
        return JobStatus.FAILED, result.status_msg
