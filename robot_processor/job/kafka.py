import json

from loguru import logger
from result import is_err

from robot_processor.business_order.models import Job
from robot_processor.business_order.tasks import package_jobs
from robot_processor.constants import AUTO_BUSINESS_TO_ROBOT_TOPIC, RPA_EXECUTION_RESULT
from robot_processor.enums import JobStatus, JobType
from robot_processor.ext import kafka_consumer, auto_retry_business_producer
from robot_processor.logging import vars as log_vars
from robot_processor.utils import unwrap_optional
from robot_processor.job.rpa_async_job import RpaAsyncJobController
from robot_processor.job import all_auto_job_controllers

auto_business_to_root_event = kafka_consumer.subscribe(AUTO_BUSINESS_TO_ROBOT_TOPIC)
rpa_execution_event = kafka_consumer.subscribe(RPA_EXECUTION_RESULT)


@logger.catch
@auto_business_to_root_event.connect
def job_auto_retry(_, record):
    log_vars.BusinessOrderId.set(record.get('bo_id'))
    log_vars.JobId.set(record.get('job_id'))
    logger.info(f"auto retry record: {record}")

    try:
        params = json.loads(record["params"])
        bo_id = params.get("bo_id")
        job_id = params.get("job_id")
        job: Job | None = Job.query.get(job_id)
        if not job or job.status == JobStatus.SUCCEED:
            return

        from robot_processor.job.trade import TradeExecutor
        if (
            job.task_type == JobType.TRADE.value
            and is_err(TradeExecutor(job).check_rate_limit(consume_rate_limit_token=False))
        ):
            logger.info(f"auto retry limit, send kafka: {record}")
            auto_retry_business_producer(record)
        else:
            package_jobs.send(bo_id, unwrap_optional(job.prev).id)

    except Exception as error:
        logger.exception(f"auto retry kafka ext failed. {error=}")


@rpa_execution_event.connect
def rpa_client_result(_, record):
    from robot_processor.client import rpa_client
    from robot_processor.logging import vars as log_vars
    from robot_processor.ext import cache

    execution_id = record["meta"]["execution_id"]
    execution_cache_key = rpa_client.execution_cache_key(execution_id)
    logger.debug(f"rpa execution event: {record}")

    job_id = cache.get(execution_cache_key)
    if not job_id:
        logger.info(f"rpa execution event without job_id {execution_id}")
        cache.set(execution_cache_key, record, timeout=60)
        return
    job = Job.query.get(job_id)
    if not job:
        logger.error('job {} not exist', job_id)
        return
    log_vars.Job.set(job)
    executor = get_executor_by_job(job)
    if executor:
        executor.rpa_client_ack(record)


def get_executor_by_job(job: Job):
    auto_job_type = job.raw_step_v2.get("task", {}).get("task_type")
    if not auto_job_type:
        logger.error(f"自动步骤 {job.format} 未设置任务类型")
        return None
    if auto_job_type not in all_auto_job_controllers:
        logger.error(f"不支持的自动任务类型: {auto_job_type}")
        return None
    executor = all_auto_job_controllers[auto_job_type](job)
    if not isinstance(executor, RpaAsyncJobController):
        logger.error("当前job不属于RpaAsyncJobController: {}", job.id)
        return None
    return executor
