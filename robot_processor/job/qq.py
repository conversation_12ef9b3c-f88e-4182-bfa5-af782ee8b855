import random

from loguru import logger
from result import Err, Ok

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.mola import MolaClient


class QQExecutor(AutoJobControllerBase):
    job_type = JobType.SEND_QQ_GROUP

    def process(self):
        msg_rules = self.args.get_send_message_arguments()
        if msg_rules.skip:
            logger.info("已配置跳过消息发送")
            return JobStatus.SUCCEED, None

        qq_nick = msg_rules.qq_nick
        if not msg_rules.qq_group:
            return JobStatus.FAILED, "QQ群名为空"
        if not msg_rules.qq_id:
            return JobStatus.FAILED, "QQ号为空"
        # 兼容已有数据
        qq_id_list = [msg_rules.qq_id] if isinstance(msg_rules.qq_id, str) else msg_rules.qq_id
        query_response = MolaClient('').query_online_qq_accounts(qq_id_list)
        match query_response:
            case Err(error_message):
                return JobStatus.FAILED, error_message
            case Ok(online_qq_ids):
                if not online_qq_ids:
                    return JobStatus.FAILED, "客户端不在线"

                chosen_qq_id = random.choice(online_qq_ids)
                content = msg_rules.content
                send_res = MolaClient(chosen_qq_id).send_qq_message(msg_rules.qq_group,
                                                                    messages=[content],
                                                                    images=msg_rules.image_urls)
                match send_res:
                    case Ok():
                        logger.info(f"发送QQ消息成功: {chosen_qq_id=} {qq_nick=} {online_qq_ids=}"
                                    f" all_qq_ids={msg_rules.qq_id} {content=}")
                        return JobStatus.SUCCEED, None
                    case Err(error_message):
                        return JobStatus.FAILED, (f"发送QQ消息失败: {chosen_qq_id=} {qq_nick=} {online_qq_ids=}"
                                                  f" all_qq_ids={msg_rules.qq_id} {content=} {error_message=}")
