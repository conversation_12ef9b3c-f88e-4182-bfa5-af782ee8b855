from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from robot_processor.client import doudian_cloud
from robot_processor.client.errors import DoudianCloudServiceError
from pydantic import BaseModel


class DoudianDecryptOutput(BaseModel):
    receiver_name: str
    receiver_phone: str
    receiver_address: str


class DoudianDecryptAPIExecutor(AutoJobControllerBase):
    job_type = JobType.DOUDIAN_DECRYPT_API
    output_model = DoudianDecryptOutput

    def process(self):
        trades = self.args.get_trades()
        if not trades:
            return JobStatus.FAILED, "订单号为空，无法解密订单信息"
        if not self.shop.sid:
            return JobStatus.FAILED, "店铺ID为空"
        tid = trades[0].tid
        try:
            res = doudian_cloud.receiver_info(store_id=self.shop.sid, order_id=tid)
        except DoudianCloudServiceError as ex:
            return JobStatus.FAILED, str(ex)

        output = self.output_model(
            receiver_name=res.receiver_name,
            receiver_phone=res.receiver_phone,
            receiver_address=res.full_address
        )
        self.states.write_job_widget_collection(output.dict())
        return JobStatus.SUCCEED, None


class DoudianDecryptAddressExecutor(AutoJobControllerBase):
    job_type = JobType.DOUDIAN_DECRYPT_ADDRESS_API

    def process(self):
        trades = self.args.get_trades()
        if not trades:
            return JobStatus.FAILED, "订单号为空，无法解密订单信息"
        if not self.shop.sid:
            return JobStatus.FAILED, "店铺ID为空"
        tid = trades[0].tid
        try:
            res = doudian_cloud.receiver_info(store_id=self.shop.sid, order_id=tid)
        except DoudianCloudServiceError as ex:
            return JobStatus.FAILED, str(ex)
        address = self.args.get_arg_by_task_arg_name("address")
        address.update({
            "name": res.receiver_name,
            "mobile": res.receiver_phone,
            "address": res.full_address
        })
        self.states.write_job_output({"address": address})
        return JobStatus.SUCCEED, None
