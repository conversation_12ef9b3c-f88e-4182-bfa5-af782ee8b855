#  Copyright 2023 Leyantech Ltd. All Rights Reserved.

from robot_processor.enums import JobType, JobStatus
from robot_processor.job.auto_job import AutoJobControllerBase
from rpa.erp.baisheng import BaiShengClient


class BaishengOrderQrExecutor(AutoJobControllerBase):
    job_type = JobType.BAISHENG_ORDER_QR

    def process(self):
        order_sn = self.args.get_arg_by_task_arg_name("order_sn")
        if not order_sn:
            return JobStatus.FAILED, "系统单号为空"
        resp = BaiShengClient(sid=self.job_wrapper.shop.sid).order_qr(order_sn)
        if not resp.ok:
            return JobStatus.FAILED, f"网络问题 {resp.status_code}"
        resp_obj = resp.json()
        if resp_obj.get("status") == "api-success" \
                and resp_obj.get("message") == "success":
            if resp_obj.get("data").get("status"):
                return JobStatus.SUCCEED, None
            else:
                return JobStatus.FAILED, resp_obj.get("data").get("message")
        else:
            return JobStatus.FAILED, resp_obj.get("message")
