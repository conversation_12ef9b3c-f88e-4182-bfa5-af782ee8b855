from enum import Enum

from loguru import logger
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.db import db
from robot_processor.business_order.models import BusinessOrder
from robot_processor.form.models import WidgetInfo
from robot_processor.refund.schemas import TaobaoRefundInfo


def update_refund_info(
    refund_info: TaobaoRefundInfo,
    form_id: int,
    refund_id_widget_key: str,  # 工单模板退款信息对应的组件 key
    update_widget_list: list[WidgetInfo.View.RawStep],  # 需要更新数据的组件
):
    bo_list = BusinessOrder.query.filter(
        BusinessOrder.form_id == form_id,
        BusinessOrder.data[refund_id_widget_key] == refund_info.refund_id
    ).all()
    if not bo_list:
        logger.warning(f"未找到匹配的退款信息 refund_id={refund_info.refund_id}")
        return

    def get_update_data():
        update_data: dict = {}
        refund_data = refund_info.dict(exclude_none=True)
        for widget_info in update_widget_list:
            WidgetInfo.Utils.fill_widget_info_by_data_binding(
                widget_info,
                update_data,
                refund_data,
                WidgetInfo.Schema.WidgetRefAndLeafFromRootPathPair(pair=[])
            )
            # 将 Enum 类型转换成字符串
            if (
                widget_info.key in update_data
                and isinstance(update_data[widget_info.key], Enum)
            ):
                update_data[widget_info.key] = update_data[widget_info.key].label
            # 将字符串转换成单选
            if widget_info.type in ["radio-tile", "radio-dropdown"]:
                value = update_data[widget_info.key]
                update_data[widget_info.key] = [dict(label=value, value=value)]

        return update_data

    def get_old_data(bo: BusinessOrder):
        old_data = {}
        for widget_info in update_widget_list:
            old_data[widget_info.key] = bo.data.get(widget_info.key)
        return old_data

    def get_diff(old: dict, new: dict):
        diff = []
        for key in new:
            if new[key] != old.get(key):
                diff.append(dict(key=key, old_value=old.get(key), new_value=new[key]))
        return diff

    update_data = get_update_data()
    for bo in bo_list:
        diff = get_diff(get_old_data(bo), update_data)
        if not diff:
            continue
        logger.info(f"update refund info {diff}")
        bo.data.update(update_data)
        flag_modified(bo, "data")
    db.session.commit()
