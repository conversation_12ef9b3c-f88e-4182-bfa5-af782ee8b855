import arrow
from loguru import logger
from pydantic import BaseModel
from pydantic import parse_raw_as
from tcron_jobs import runner

from robot_processor.constants import TIME_ZONE
from robot_processor.ext import cache
from robot_processor.form.models import WidgetInfo
from robot_processor.refund.models import TaobaoRefund
from robot_processor.refund.services import update_refund_info
from robot_processor.t_cron import wrap_tcron_job


class UpdateRefundInfoTaskInfo(BaseModel):
    """上次执行记录"""

    jdp_modified: str

    @classmethod
    def build_default(cls):
        now_str = arrow.now(TIME_ZONE).format("YYYY-MM-DD HH:mm:ss")
        return cls(jdp_modified=now_str)


def get_last_update_refund_info_task_info(form_id: int):
    cache_key = f"UPDATE_REFUND_INFO_TASK_{form_id}"
    cache_info_raw = cache.get(cache_key)
    if cache_info_raw is None:
        return UpdateRefundInfoTaskInfo.build_default()
    else:
        return UpdateRefundInfoTaskInfo.validate(cache_info_raw)


def set_last_update_refund_info_task_info(
    form_id: int, last_info: UpdateRefundInfoTaskInfo
):
    cache_key = f"UPDATE_REFUND_INFO_TASK_{form_id}"
    cache.set(cache_key, dict(last_info), timeout=60 * 60 * 24)


@runner.register
@wrap_tcron_job
def update_refund_info_task(
    seller_nicks: str, form_id: int, refund_id_widget_key: str, widget_list_json: str
):
    seller_nick_list = seller_nicks.split(",")
    widget_list = parse_raw_as(list[WidgetInfo.View.RawStep], widget_list_json)

    last_info = get_last_update_refund_info_task_info(form_id)
    current_info = UpdateRefundInfoTaskInfo.build_default()
    set_last_update_refund_info_task_info(form_id, current_info)

    time_range = (
        arrow.get(last_info.jdp_modified, tzinfo=TIME_ZONE).datetime,
        arrow.get(current_info.jdp_modified, tzinfo=TIME_ZONE).datetime,
    )
    taobao_refund_list: list[TaobaoRefund] = TaobaoRefund.query.filter(
        TaobaoRefund.seller_nick.in_(seller_nick_list),
        TaobaoRefund.jdp_modified.between(*time_range),
    ).all()
    logger.info(
        f"update_refund_info_task. seller_nick: {seller_nick_list}, cnt: {len(taobao_refund_list)}"
    )
    refund_info_list = [refund.get_refund_info() for refund in taobao_refund_list]
    for refund_info in refund_info_list:
        try:
            update_refund_info(refund_info, form_id, refund_id_widget_key, widget_list)
        except Exception as e:
            logger.opt(exception=e).error(
                f"update_refund_info_task fail. {refund_info=}"
            )
