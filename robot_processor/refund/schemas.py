from enum import StrEnum

from pydantic import BaseModel

from robot_processor.utils import make_fields_optional


class RefundGoodStatus(StrEnum):
    """货物状态"""

    BUYER_NOT_RECEIVED = "BUYER_NOT_RECEIVED", "买家未收到货"
    BUYER_RECEIVED = "BUYER_RECEIVED", "买家已收到货"
    BUYER_RETURNED_GOODS = "BUYER_RETURNED_GOODS", "买家已退货"

    def __new__(cls, value, *args):
        self = str.__new__(cls, value)
        self._value_ = value
        return self

    def __init__(self, _, label: str):
        self.label = label


class OrderStatus(StrEnum):
    """交易状态

    References:
        https://open.taobao.com/docV3.htm?spm=a219a.7386797.0.0.b8c9669avJS4AV&source=search&docId=102856&docType=1
    """
    WAIT_BUYER_PAY = "WAIT_BUYER_PAY", "等待买家付款"
    WAIT_SELLER_SEND_GOODS = "WAIT_SELLER_SEND_GOODS", "等待卖家发货"  # 即:买家已付款
    SELLER_CONSIGNED_PART = "SELLER_CONSIGNED_PART", "卖家部分发货"
    WAIT_BUYER_CONFIRM_GOODS = "WAIT_BUYER_CONFIRM_GOODS", "等待买家确认收货"  # 即:卖家已发货
    TRADE_BUYER_SIGNED = "TRADE_BUYER_SIGNED", "买家已签收（货到付款专用）"
    TRADE_FINISHED = "TRADE_FINISHED", "交易成功"
    TRADE_CLOSED = "TRADE_CLOSED", "交易关闭"
    TRADE_CLOSED_BY_TAOBAO = "TRADE_CLOSED_BY_TAOBAO", "交易被淘宝关闭"
    TRADE_NO_CREATE_PAY = "TRADE_NO_CREATE_PAY", "没有创建支付宝交易"
    WAIT_PRE_AUTH_CONFIRM = "WAIT_PRE_AUTH_CONFIRM", "余额宝0元购合约中"
    PAY_PENDING = "PAY_PENDING", "外卡支付付款确认中"
    ALL_WAIT_PAY = "ALL_WAIT_PAY", "所有买家未付款的交易"  # 包含：WAIT_BUYER_PAY、TRADE_NO_CREATE_PAY
    ALL_CLOSED = "ALL_CLOSED", "所有关闭的交易"  # 包含：TRADE_CLOSED、TRADE_CLOSED_BY_TAOBAO
    PAID_FORBID_CONSIGN = "PAID_FORBID_CONSIGN", "该状态代表订单已付款但是处于禁止发货状态"

    def __new__(cls, value, *args):
        self = str.__new__(cls, value)
        self._value_ = value
        return self

    def __init__(self, _, label: str):
        self.label = label


class RefundStatus(StrEnum):
    """退款状态

    References:
        https://open.taobao.com/help?spm=a219a.7386797.0.0.152f669ajRKyJ2&source=search&docId=4469&docType=14
    """
    WAIT_SELLER_AGREE = "WAIT_SELLER_AGREE", "买家已经申请退款，等待卖家同意"
    WAIT_BUYER_RETURN_GOODS = "WAIT_BUYER_RETURN_GOODS", "卖家已经同意退款，等待买家退货"
    WAIT_SELLER_CONFIRM_GOODS = "WAIT_SELLER_CONFIRM_GOODS", "买家已经退货，等待卖家确认收货"
    SELLER_REFUSE_BUYER = "SELLER_REFUSE_BUYER", "卖家拒绝退款"
    CLOSED = "CLOSED", "退款关闭"
    SUCCESS = "SUCCESS", "退款成功"
    WAIT_BUYER_CONFIRM_EXCHANGE_SELLER_SEND_GOODS = "WAIT_BUYER_CONFIRM_EXCHANGE_SELLER_SEND_GOODS", "卖家已发货,等待买家确认收货"
    EXCHANGE_TRANSFORM_TO_REFUND = "EXCHANGE_TRANSFORM_TO_REFUND", "换货关闭转退款"
    WAIT_SELLER_CONSIGN_GOODS = "WAIT_SELLER_CONSIGN_GOODS", "商家确认收货等待商家发货"
    ZERO_SEC_REFUND_SUCCESS = "ZERO_SEC_REFUND_SUCCESS", "0秒退款成功"
    WAIT_BUYER_CONFIRM_REDO_SEND_GOODS = "WAIT_BUYER_CONFIRM_REDO_SEND_GOODS", "等待买家确认补寄商品"
    EXCHANGE_WAIT_BUYER_CONFIRM_GOODS = "EXCHANGE_WAIT_BUYER_CONFIRM_GOODS", "卖家已发货,等待卖家和买家确认收货"


    def __new__(cls, value, *args):
        self = str.__new__(cls, value)
        self._value_ = value
        return self

    def __init__(self, _, label: str):
        self.label = label


class DisputeType(StrEnum):
    """退款类型

    References:
        https://open.taobao.com/v2/doc?spm=a219a.7629140.0.0.7ae275fe9ZQJL4#/apiFile?docType=2&docId=53
    """
    REFUND = "REFUND", "退款不退货"
    REFUND_AND_RETURN = "REFUND_AND_RETURN", "退货退款"
    TMALL_EXCHANGE = "TMALL_EXCHANGE", "天猫换货"
    TAOBAO_EXCHANGE = "TAOBAO_EXCHANGE", "淘宝换货"
    REPAIR = "REPAIR", "维修"
    RESHIPPING = "RESHIPPING", "补寄"
    OTHERS = "OTHERS", "其他"
    RETURN_GOODS_POSTAGE = "RETURN_GOODS_POSTAGE", "退运费"

    def __new__(cls, value, *args):
        self = str.__new__(cls, value)
        self._value_ = value
        return self

    def __init__(self, _, label: str):
        self.label = label


@make_fields_optional
class TaobaoRefundInfo(BaseModel):
    """
    淘宝退款信息

    References:
        https://open.taobao.com/v2/doc?spm=a219a.7629140.0.0.7ae275fe9ZQJL4#/apiFile?docType=2&docId=53
    """

    address: str  # 卖家收货地址
    alipay_no: str  # 支付宝交易号
    buyer_nick: str  # 买家昵称
    buyer_open_uid: str  # 买家open_uid
    company_name: str  # 物流公司名称
    created: str  # 退款申请时间。格式: yyyy-MM-dd HH:mm:ss
    desc: str  # 退款说明
    dispute_type: DisputeType  # 退款类型
    good_status: RefundGoodStatus  # 货物状态
    good_return_time: str  # 退货时间。格式: yyyy-MM-dd HH:mm:ss
    modified: str  # 更新时间。格式:yyyy-MM-dd HH:mm:ss
    num: int  # 商品数量
    num_iid: str  # 申请退款的商品数字编号
    oid: int  # 子订单号
    order_status: OrderStatus  # 退款对应的订单交易状态
    outer_id: str  # 商品外部商家编码
    payment: str  # 支付给卖家的金额(交易总金额-退还给买家的金额)。精确到2位小数;单位:元。如:200.07，表示:200元7分
    price: str  # 商品价格。精确到2位小数;单位:元。如:200.07，表示:200元7分
    reason: str  # 退款原因
    refund_id: str  # 退款单号
    refund_fee: str  # 退还金额(退还给买家的金额)。精确到2位小数;单位:元。如:200.07，表示:200元7分
    refund_version: int  # 退款信息版本
    refund_phase: str  # 退款阶段
    seller_nick: str  # 卖家昵称
    sid: str  # 退货快递单号
    sku: str  # 商品SKU信息 Examples: 30004447689|颜色分类:军绿色;尺码:XS
    status: RefundStatus  # 退款状态
    tid: int  # 淘宝交易单号
    title: str  # 商品标题
    total_fee: str  # 交易总金额。精确到2位小数;单位:元。如:200.07，表示:200元7分
    attribute: str  # 退款单内部属性
