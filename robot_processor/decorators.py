import functools
import time
from hashlib import md5
from typing import Callable

from flask import abort, current_app, make_response, request, jsonify
from loguru import logger

from robot_processor.currents import g
from robot_processor.ext import cache
from robot_processor.logging import vars as log_vars
from robot_processor.shop.models import Shop
from robot_processor import auth


def load_shop():
    if not g.auth.store_id:
        return
    if g.auth.channel_id:
        return Shop.query.filter_by(channel_id=g.auth.channel_id).first()
    else:
        return Shop.query.filter_by(sid=g.auth.store_id).first()


def org_required(fn):
    @functools.wraps(fn)
    def _org_required(*args, **kwargs):
        if not auth.has_jwt(request):
            logger.bind(status=401).warning("缺少 jwt")
            abort(make_response(jsonify(code="auth token missing"), 401))
        org_id = g.auth.org_id
        if not org_id:
            logger.bind(status=401).warning("缺少 org_id")
            abort(make_response(jsonify(code="org_id missing"), 401))
        g.org_id = str(org_id)
        shop_query = Shop.query.filter_by(org_id=str(org_id))
        g.org_sids = [shop.sid for shop in shop_query.all()]
        return fn(*args, **kwargs)

    return _org_required


def admin_user_required(fn):
    """
    只有运营中台相关接口能使用这个deco
    """
    @functools.wraps(fn)
    def _user_required(*args, **kwargs):
        if not auth.has_jwt(request):
            logger.bind(status=401).warning("缺少 jwt")
            abort(make_response(jsonify(code="auth token missing"), 401))
        login_user = g.login_user_detail
        if not login_user or not login_user.user_nick:
            # kiosk签发的乐言用户信息，使用user_nick判断登录用户
            logger.bind(status=401).warning("缺少登录用户信息")
            abort(make_response(jsonify(code="login user missing"), 401))
        return fn(*args, **kwargs)

    return _user_required


def shop_required(fn):
    @functools.wraps(fn)
    def _shop_required(*args, **kwargs):
        if not auth.has_jwt(request):
            logger.bind(status=401).warning("缺少 jwt")
            abort(make_response(jsonify(code="auth token missing"), 401))
        current_shop = load_shop()
        if current_shop is None or not hasattr(current_shop, "id"):
            logger.bind(status=401).warning(f"找不到 shop_id={g.auth.store_id} 的店铺")
            abort(make_response(jsonify(code="shop not found"), 401))
        g.shop = current_shop
        # todo to be deleted @yzq
        g.nick = g.auth.nick_name or g.auth.nick
        return fn(*args, **kwargs)

    return _shop_required


def service_required(fn):
    @functools.wraps(fn)
    def wrapper(*args, **kwargs):
        token = request.headers.get("Authentication-Token")
        if not token:
            logger.bind(status=401).warning("缺少 Authentication-Token")
            abort(make_response(jsonify(code="Authentication-Token missing"), 401))
        if token.strip() != current_app.config.get("SERVICE_TOKEN"):
            logger.bind(status=401).warning("Authentication-Token 错误")
            abort(make_response(jsonify(code="Authentication-Token unmatch"), 401))
        return fn(*args, **kwargs)

    return wrapper


def buyer_table_service_required(fn):
    """
    buyer_table 服务使用。
    :param fn:
    :return:
    """
    @functools.wraps(fn)
    def wrapper(*args, **kwargs):
        token: str | None = request.headers.get("Authentication-Token")
        if token is None:
            logger.bind(status=401).warning("缺少 Authentication-Token")
            abort(make_response(jsonify(code="Authentication-Token missing"), 401))
        if token.strip() != current_app.config.get("SERVICE_BUFFET_TOKEN"):
            logger.bind(status=401).warning("Authentication-Token 错误")
            abort(make_response(jsonify(code="Authentication-Token unmatch"), 401))
        return fn(*args, **kwargs)

    return wrapper


def mini_app_required(fn):
    @functools.wraps(fn)
    def _mini_app_required(*args, **kwargs):
        mini_app_id = getattr(g, "mini_app_id", None)
        user_nick = getattr(g, "user_nick", None)
        if not mini_app_id:
            logger.bind(status=401).warning("缺少 mini_app_id")
            abort(401)
        current_val: dict = cache.get(mini_app_id)
        if not current_val:
            current_val = cache_mini_app_info(mini_app_id)

        g.shop = current_val["shop"]
        g.org_sids = current_val["org_sids"]
        g.org_id = current_val["shop"]["org_id"]
        log_vars.Sid.set(g.shop['sid'])  # type: ignore[index]
        log_vars.LoginUserNick.set(user_nick)
        return fn(*args, **kwargs)

    return _mini_app_required


def cache_mini_app_info(mini_app_id):
    shop = Shop.get_by_app_id(mini_app_id)
    if not shop:
        logger.bind(status=401).warning(f"找不到 mini_app_id={mini_app_id} 的店铺")
        abort(401)
    org_sids = [s.sid for s in Shop.query.filter_by(org_id=shop.org_id)]

    shop_dict = shop.brief()

    # add pic
    record = shop.get_recent_record()
    if record and record.access_token:
        (
            record.taobao_client.shop_seller_get(record.access_token, fields="pic_path")
            .map(lambda response: response["shop"]["pic_path"])
            .map(lambda relative_pic_path: f'http://logo.taobao.com/shop-logo{relative_pic_path}')
            .map(lambda pic_url: shop_dict.update(pic_url=pic_url))
        )

    cache_val = {
        "shop": shop_dict,
        "org_sids": org_sids
    }
    cache.set(mini_app_id, cache_val, timeout=86400)
    return cache_val


def retry(count: int, func: Callable):
    assert (count > 0), "调用次数必须大于0"

    def outer(fn):
        @functools.wraps(fn)
        def call(*args, **kwargs):
            resp = None
            for i in range(count):
                logger.debug(f"重试次数：{i + 1}")
                resp = fn(*args, **kwargs)
                if func(resp):
                    return resp
                time.sleep((i + 1) * 1)
            return resp

        return call

    return outer


def make_api_hash_key(headers, query_string, body, *args, **kwargs):
    key = md5(request.full_path.encode())
    if headers:
        for header in headers:
            key.update(str(request.headers.get(header, '')).encode())
    if query_string:
        key.update(request.query_string)
    if body:
        key.update(request.data)
    if args:
        key.update(str(args).encode())
    if kwargs:
        key.update(str(kwargs).encode())
    return key.hexdigest()
