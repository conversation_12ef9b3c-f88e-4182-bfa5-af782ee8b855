from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import <PERSON><PERSON>

from loguru import logger
from pydantic import BaseModel
from pydantic import Field

from robot_processor.business_order.schema import NextJobAssistant
from robot_processor.enums import Creator
from robot_processor.enums import PlanWhenAssignException
from robot_processor.error.base import BizError


class ApiRes(BaseModel):
    success: bool = True
    message: Optional[str] = None


class JobBriefInReq(BaseModel):
    business_order_id: int
    job_id: int


class AssigneeAssistantInReq(BaseModel):
    user_type: Creator
    user_id: int


class BatchActionRes(BaseModel):
    class ActionErrorInfo(BaseModel):
        business_order_id: int
        form_name: str
        step_name: str
        error_message: str

    success: bool
    message: Optional[str]
    error: Optional[List[ActionErrorInfo]]

    @classmethod
    def build_for_batch_action_req(cls, errors: List[Tuple[JobBriefInReq, BizError]]):
        from sqlalchemy.orm import Load

        from robot_processor.business_order.models import Job

        _errors_info = []
        for job_info, e in errors:
            try:
                job = Job.query.options(Load(Job).joinedload(Job.business_order)).get(
                    job_info.job_id
                )
                form = job.step.form  # type: ignore[union-attr]
                _errors_info.append(
                    cls.ActionErrorInfo(
                        business_order_id=job_info.business_order_id,
                        form_name=form.name,
                        step_name=job.raw_step_v2.get("name", ""),  # type: ignore[union-attr]
                        error_message=e.biz_display,
                    )
                )
            except Exception as e:
                logger.exception(
                    f"工单: {job_info.business_order_id} 任务: {job_info.job_id} 处理失败: {e}"
                )
                _errors_info.append(
                    cls.ActionErrorInfo(
                        business_order_id=job_info.business_order_id,
                        form_name="",
                        step_name="",
                        error_message=f"工单: {job_info.business_order_id} 任务: {job_info.job_id} 处理失败。",
                    )
                )

        return cls(success=False, error=_errors_info)


class JobCommonActionReq(BaseModel):
    job: JobBriefInReq
    reason: str = ""
    skip_data: Optional[dict]
    specified_reject_id: Optional[int]
    assign_account_exception: PlanWhenAssignException = Field(
        default=PlanWhenAssignException.ENTER_EXCEPTION_POOL
    )
    job_assistant: Optional[NextJobAssistant] = None


class BatchJobCommonActionReq(BaseModel):
    jobs: List[JobBriefInReq]
    reason: str = ""
    assign_account_exception: PlanWhenAssignException = Field(
        default=PlanWhenAssignException.ENTER_EXCEPTION_POOL
    )
    job_assistant: Optional[NextJobAssistant] = None


class JobAssignActionReq(BaseModel):
    job: JobBriefInReq
    reason: str = ""

    assignee: AssigneeAssistantInReq


class BatchJobAssignActionReq(BaseModel):
    jobs: List[JobBriefInReq]
    reason: str = ""

    assignee: AssigneeAssistantInReq


class JobWithDataActionReq(BaseModel):
    job: JobBriefInReq
    reason: str = ""

    data: dict = Field(default_factory=dict)
    force: bool = True


class BatchData(BaseModel):
    business_order_id: int
    job_id: int
    widget_key: str
    value: Any


class BatchJobWithDataActionReq(BaseModel):
    # old schema @deprecated
    jobs: List[JobBriefInReq] = Field(default_factory=list)
    data: dict = Field(default_factory=dict)
    reason: str = ""

    batch_data: List[BatchData] = Field(default_factory=list)
    force: bool = True


class JobAcceptActionReq(BaseModel):
    job: JobBriefInReq
    reason: str = ""

    data: dict = Field(default_factory=dict)
    assignee: Optional[AssigneeAssistantInReq]
    force: bool = True


class BatchJobAcceptActionReq(BaseModel):
    reason: str = ""
    assignee: Optional[AssigneeAssistantInReq]

    # new schema
    batch_data: List[BatchData] = Field(default_factory=list)
    force: bool = True


class StepCandidateAssistantsReq(BaseModel):
    step_uuid: str


class StepUISchemaReq(BaseModel):
    job_id: int


class StepUISchemaRes(BaseModel):
    ui_schema: List[dict]


class StepCandidateAssistantsRes(BaseModel):
    class AssistantInfo(BaseModel):
        user_id: int
        user_type: int
        user_nick: Optional[str]
        enable: Optional[int]

    success: bool = True
    data: Dict[str, List[AssistantInfo]]


class GetBusinessOrderActionsReqeust(BaseModel):
    business_order_id: int


class GetBatchBusinessOrderActionsRequest(BaseModel):
    business_order_ids: List[int]
