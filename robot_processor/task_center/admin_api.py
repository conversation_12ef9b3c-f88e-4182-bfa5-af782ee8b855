"""管理员任务中心"""

import copy
from collections import defaultdict
from decimal import Decimal
from typing import Any
from typing import Dict
from typing import List
from typing import Tuple

import arrow
from flask import Blueprint
from flask import jsonify
from flask import request
from leyan_grpc.utils import get_stats_client
from loguru import logger
from pydantic import BaseModel

from robot_processor.base_schemas import Failed
from robot_processor.business_order.encryption.address_widget import AddressWidgetMask
from robot_processor.business_order.encryption.exceptions import AddressWidgetError
from robot_processor.business_order.errors import BusinessOrderError
from robot_processor.business_order.errors import BusinessOrderValidateError
from robot_processor.business_order.job_action import JobAction
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import Job
from robot_processor.business_order.schema import FilterSchema
from robot_processor.business_order.schema import UnmaskBoDataReqSchema
from robot_processor.business_order.utils.compute_number import get_number_widget_values
from robot_processor.business_order.utils.compute_number import get_step_name_to_number_widget_titles
from robot_processor.business_order.utils.compute_number import get_step_name_to_widget_header_mapping
from robot_processor.business_order.utils.compute_number import get_step_name_to_widget_titles_mapping
from robot_processor.business_order.utils.operate_debounce import JobOperateLock
from robot_processor.client import kiosk_client
from robot_processor.currents import g
from robot_processor.database_util import QueryExplainer
from robot_processor.db import db
from robot_processor.decorators import shop_required
from robot_processor.enums import Action
from robot_processor.enums import WidgetValueUniqueCheckType
from robot_processor.error.base import BizError
from robot_processor.error.errors import BusinessOrderNeedEnterExceptionalPoolError
from robot_processor.form.models import Form
from robot_processor.logging import vars as log_vars
from robot_processor.task_center import errors
from robot_processor.task_center import schema
from robot_processor.task_center.getter import BatchGetBusinessOrderBrief
from robot_processor.task_center.utils import generate_business_order_query
from robot_processor.utils import get_nearest_prev_human_job_id
from robot_processor.utils import unwrap_optional
from robot_processor.validator import validate

api = Blueprint("task-center", __name__)

_stats_client = get_stats_client("robot-processor.job-action")


def _get_begin_end_ts(date):
    arrow_date = arrow.get(date).replace(tzinfo="Asia/Shanghai")
    start = int(arrow_date.floor("day").timestamp())
    end = int(arrow_date.ceil("day").timestamp())
    return start, end


@api.errorhandler(BizError)
def error_handler(e: BizError):
    if isinstance(e, BusinessOrderNeedEnterExceptionalPoolError):
        Job.Utils.mark_failed(Job.query.get(e.job_id), str(e))  # type: ignore[arg-type]
    logger.debug(f"[BizError] {e}")

    return jsonify(success=False, code=e.status_code, message=e.biz_display)


@api.errorhandler(AddressWidgetError)
def bad_request_handler(e: AddressWidgetError):
    return jsonify(success=False, code=e.code, message=e.description)


@api.errorhandler(BusinessOrderError)
def form_validator_validate_error(err):
    if isinstance(err, BusinessOrderValidateError):
        errors = err.to_response()
        for error in errors:
            if error["check_type"] == WidgetValueUniqueCheckType.CREATE_FORBIDDEN:
                check_type = WidgetValueUniqueCheckType.CREATE_FORBIDDEN
                break
        else:
            check_type = WidgetValueUniqueCheckType.REMIND
        return jsonify(
            success=False,
            message=str(err),
            errors=errors,
            check_type=check_type,
            debug_log=err.to_log(),
        )
    else:
        return jsonify(success=False, message=str(err))


@api.before_request
@shop_required
def auth():
    try:
        _sid = g.shop.sid
        request_path = request.path.split("/")
        if request_path[-1] == "batch":
            _action = request_path[-2]
            _stats = f"target.{_action}.batch.{_sid}"
        else:
            _action = request_path[-1]
            _stats = f"target.{_action}.single.{_sid}"
        _stats_client.incr(_stats)
    except:  # noqa
        ...


@api.put("/business-orders/remind")
@api.put("/business-orders/close")
@api.put("/business-orders/reopen")
@api.put("/business-orders/reject")
@api.put("/business-orders/recall")
@api.put("/business-orders/pause")
@api.put("/business-orders/unpause")
@api.put("/business-orders/delete")
@api.put("/business-orders/recover")
@api.put("/business-orders/skip")
@api.put("/business-orders/complete")
@api.put("/business-orders/approve")
@api.put("/business-orders/overrule")
@validate
def business_order_action(body: schema.JobCommonActionReq) -> schema.ApiRes:
    from robot_processor.business_order.fsm import BusinessOrderStatusController

    assert g.login_user_detail
    operate_assistant = g.login_user_detail

    action = request.path.split("/")[-1]
    try:
        action = Action(action)
    except:  # noqa
        return schema.ApiRes(success=False, message="不支持的操作")

    log_vars.BusinessOrderId.set(body.job.business_order_id)
    log_vars.JobId.set(body.job.job_id)

    business_order: BusinessOrder | None = BusinessOrder.query.filter(
        BusinessOrder.id == body.job.business_order_id
    ).first()
    if business_order is None:
        return schema.ApiRes(success=False, message="未找到该工单")

    job_instance: Job | None = Job.query.filter(Job.id == body.job.job_id).first()
    if job_instance is None:
        return schema.ApiRes(success=False, message="未找到任务")

    # 实例化工单状态控制器。
    match action:
        case Action.reject:
            specified_reject_id = body.specified_reject_id
            # 不指定则默认退回到前一个人工步骤
            if specified_reject_id is None:
                specified_reject_id = get_nearest_prev_human_job_id(job_instance)
            controller = BusinessOrderStatusController(
                business_order=business_order,
                operator=operate_assistant,
                revert_to_job_id=specified_reject_id,
                is_admin=True,
            )
        case Action.recall:
            controller = BusinessOrderStatusController(
                business_order=business_order,
                operator=operate_assistant,
                revert_to_job_id=body.job.job_id,
                is_admin=True,
            )
        case _:
            controller = BusinessOrderStatusController(
                business_order=business_order, operator=operate_assistant, is_admin=True
            )

    # 检测是否可操作。
    ok, reason = controller.can_do_by_action(action)
    if not ok:
        return schema.ApiRes(success=False, message=reason)

    job_operate_lock = JobOperateLock(
        bo_id=body.job.business_order_id, current_job_id=body.job.job_id, body=body.dict(), action=action
    )
    job_action = JobAction(
        job_id=body.job.job_id,
        job=job_instance,
        business_order_status_controller=controller,
        operate_lock=job_operate_lock,
    )

    match action:
        case Action.remind:
            job_action.remind(operate_assistant=operate_assistant)
        case Action.close:
            job_action.close(operate_assistant=operate_assistant, operate_reason=body.reason)
        case Action.complete:
            job_action.complete(operate_assistant=operate_assistant, operate_reason=body.reason)
        case Action.reopen:
            job_action.reopen(operate_assistant=operate_assistant, operate_reason=body.reason)
        case Action.reject:
            assignee_assistant = body.job_assistant.to_account_detail() if body.job_assistant else None
            job_action.reject(
                operate_assistant=operate_assistant,
                operate_reason=body.reason,
                specified_reject_id=body.specified_reject_id,
                pofa=body.assign_account_exception,
                assignee_assistant=assignee_assistant,
            )
        case Action.recall:
            job_action.recall(operate_assistant=operate_assistant, operate_reason=body.reason)
        case Action.pause:
            job_action.pause(operate_assistant=operate_assistant, operate_reason=body.reason)
        case Action.unpause:
            job_action.unpause(operate_assistant=operate_assistant, operate_reason=body.reason)
        case Action.delete:
            job_action.delete(operate_assistant=operate_assistant, operate_reason=body.reason)
        case Action.recover:
            job_action.recover(operate_assistant=operate_assistant, operate_reason=body.reason)
        case Action.skip:
            job_action.skip(
                operate_assistant=operate_assistant,
                operate_reason=body.reason,
                extra_data=body.skip_data,
            )
        case Action.approve:
            job_action.approve(
                operate_assistant=operate_assistant,
                operate_reason=body.reason,
            )
        case Action.overrule:
            job_action.overrule(
                operate_assistant=operate_assistant,
                operate_reason=body.reason,
            )

    return schema.ApiRes()


@api.put("/business-orders/remind/batch")
@api.put("/business-orders/close/batch")
@api.put("/business-orders/reopen/batch")
@api.put("/business-orders/reject/batch")
@api.put("/business-orders/recall/batch")
@api.put("/business-orders/pause/batch")
@api.put("/business-orders/unpause/batch")
@api.put("/business-orders/delete/batch")
@api.put("/business-orders/recover/batch")
@api.put("/business-orders/skip/batch")
@api.put("/business-orders/complete/batch")
@api.put("/business-orders/pick/batch")
@api.put("/business-orders/approve/batch")
@api.put("/business-orders/overrule/batch")
@validate
def batch_business_order_action(
    body: schema.BatchJobCommonActionReq,
) -> schema.BatchActionRes:
    from robot_processor.business_order.fsm import BatchBusinessOrderStatusController
    from robot_processor.business_order.job_action import JobAction

    assert g.login_user_detail
    operate_assistant = g.login_user_detail

    action = request.path.split("/")[-2]
    try:
        action = Action(action)
    except:  # noqa
        return schema.BatchActionRes(success=False, message="不支持的操作")

    business_order_ids = [j.business_order_id for j in body.jobs]

    business_orders = BusinessOrder.query.filter(BusinessOrder.id.in_(business_order_ids)).all()

    # 实例化工单状态控制器。
    match action:
        case Action.recall:
            controller = BatchBusinessOrderStatusController(
                business_orders=business_orders,
                operator=operate_assistant,
                is_admin=True,
                bo_revert_to_job_id_mapping={j.business_order_id: j.job_id for j in body.jobs},
            )
        case Action.pick:
            # 如果是“领取”操作，则说明是非后台过来的请求，需要重新校验身份。
            controller = BatchBusinessOrderStatusController(
                business_orders=business_orders,
                operator=operate_assistant,
            )
        case _:
            controller = BatchBusinessOrderStatusController(
                business_orders=business_orders,
                operator=operate_assistant,
                is_admin=True,
            )

    business_orders_can_do_mapping = controller.get_all_business_orders_can_do_by_action(action)

    jobs = controller.get_all_jobs()

    bos_controllers_mapping = {c.business_order.id: c for c in controller.get_all_business_order_status_controllers()}

    errors = []

    for job in body.jobs:
        # 检测是否可操作。
        if (result := business_orders_can_do_mapping.get(job.business_order_id)) is not None:
            ok, reason = result
            if not ok:
                errors.append((job, BizError(reason)))
                continue
        else:
            errors.append((job, BizError(f"未找到工单: {job.business_order_id}")))
            continue

        # 获取 Job 实例，降低数据库查询。
        job_instance = jobs.get(job.job_id)
        if job_instance is None:
            errors.append((job, BizError(f"未找到工单关联的任务: {job.job_id}")))
            continue

        job_operate_lock = JobOperateLock(
            bo_id=job.business_order_id, current_job_id=job.job_id, body=body.dict(), action=action
        )
        job_action = JobAction(
            job_id=job.job_id,
            job=job_instance,
            business_order_status_controller=bos_controllers_mapping.get(job.business_order_id),
            operate_lock=job_operate_lock,
        )
        log_vars.JobId.set(job.job_id)
        log_vars.BusinessOrderId.set(job.business_order_id)
        try:
            match action:
                case Action.remind:
                    job_action.remind(operate_assistant=operate_assistant)
                case Action.close:
                    job_action.close(operate_assistant=operate_assistant, operate_reason=body.reason)
                case Action.complete:
                    job_action.complete(operate_assistant=operate_assistant, operate_reason=body.reason)
                case Action.reopen:
                    job_action.reopen(operate_assistant=operate_assistant, operate_reason=body.reason)
                case Action.reject:
                    assignee_assistant = body.job_assistant.to_account_detail() if body.job_assistant else None
                    # 批量退回默认退回到上一个可重复执行人工步骤
                    job_action.reject(
                        operate_assistant=operate_assistant,
                        operate_reason=body.reason,
                        specified_reject_id=None,
                        pofa=body.assign_account_exception,
                        assignee_assistant=assignee_assistant,
                    )
                case Action.recall:
                    job_action.recall(operate_assistant=operate_assistant, operate_reason=body.reason)
                case Action.pause:
                    job_action.pause(operate_assistant=operate_assistant, operate_reason=body.reason)
                case Action.unpause:
                    job_action.unpause(operate_assistant=operate_assistant, operate_reason=body.reason)
                case Action.delete:
                    job_action.delete(operate_assistant=operate_assistant, operate_reason=body.reason)
                case Action.recover:
                    job_action.recover(operate_assistant=operate_assistant, operate_reason=body.reason)
                case Action.skip:
                    job_action.skip(
                        operate_assistant=operate_assistant,
                        operate_reason=body.reason,
                        extra_data=None,  # 批量跳过的都是不用补充信息的
                    )
                case Action.pick:
                    job_action.pick(
                        operate_assistant=operate_assistant,
                        operate_reason=body.reason,
                    )
                case Action.approve:
                    job_action.approve(
                        operate_assistant=operate_assistant,
                        operate_reason=body.reason,
                    )
                case Action.overrule:
                    job_action.overrule(
                        operate_assistant=operate_assistant,
                        operate_reason=body.reason,
                    )
        except BizError as e:
            errors.append((job, e))

    if len(errors) > 0:
        return schema.BatchActionRes.build_for_batch_action_req(errors)

    return schema.BatchActionRes(success=True)


@api.put("/business-orders/deliver")
@api.put("/business-orders/assign")
@validate
def job_assign(body: schema.JobAssignActionReq) -> schema.ApiRes:
    from robot_processor.business_order.fsm import BusinessOrderStatusController
    from robot_processor.business_order.job_action import JobAction
    from robot_processor.error.base import BizError

    action = request.path.split("/")[-1]
    try:
        action = Action(action)
    except:  # noqa
        return schema.ApiRes(success=False, message="不支持的操作")

    assert g.login_user_detail
    operate_assistant = g.login_user_detail
    log_vars.JobId.set(body.job.job_id)
    log_vars.BusinessOrderId.set(body.job.business_order_id)
    try:
        assignee_assistant = kiosk_client.get_user_by_id(body.assignee.user_type, body.assignee.user_id)
        assert assignee_assistant
    except AssertionError:
        raise BizError("选择的指派客服信息获取失败")

    business_order = BusinessOrder.query.filter(BusinessOrder.id == body.job.business_order_id).first()
    if business_order is None:
        return schema.ApiRes(success=False, message="未找到该工单")

    controller = BusinessOrderStatusController(business_order=business_order, operator=operate_assistant, is_admin=True)

    ok, reason = controller.can_do_by_action(action)
    if not ok:
        return schema.ApiRes(success=False, message=reason)

    job_instance = controller.all_jobs.get(body.job.job_id)
    if job_instance is None:
        return schema.ApiRes(success=False, message="Job ID 未与工单绑定")

    job_action = JobAction(
        job_id=body.job.job_id,
        job=job_instance,
        business_order_status_controller=controller,
    )
    match action:
        case Action.assign:
            job_action.assign(
                assignee_assistant=assignee_assistant,
                operate_assistant=operate_assistant,
                operate_reason=body.reason,
            )
        case Action.deliver:
            job_action.deliver(
                deliver_to_assistant=assignee_assistant,
                operate_assistant=operate_assistant,
                operate_reason=body.reason,
            )

    return schema.ApiRes()


@api.put("/business-orders/deliver/batch")
@api.put("/business-orders/assign/batch")
@validate
def batch_job_assign(body: schema.BatchJobAssignActionReq) -> schema.BatchActionRes:
    from robot_processor.business_order.fsm import BatchBusinessOrderStatusController
    from robot_processor.business_order.job_action import JobAction
    from robot_processor.error.base import BizError

    action = request.path.split("/")[-2]
    try:
        action = Action(action)
    except:  # noqa
        return schema.BatchActionRes(success=False, message="不支持的操作")

    assert g.login_user_detail
    operate_assistant = g.login_user_detail
    errors = []

    try:
        assignee_assistant = kiosk_client.get_user_by_id(body.assignee.user_type, body.assignee.user_id)
        assert assignee_assistant
    except AssertionError:
        raise BizError("选择的指派客服信息获取失败")

    business_order_ids = [j.business_order_id for j in body.jobs]

    business_orders = BusinessOrder.query.filter(BusinessOrder.id.in_(business_order_ids)).all()

    # 实例化工单状态控制器。
    controller = BatchBusinessOrderStatusController(
        business_orders=business_orders, operator=operate_assistant, is_admin=True
    )

    business_orders_can_do_mapping = controller.get_all_business_orders_can_do_by_action(action)

    bos_controllers_mapping = {c.business_order.id: c for c in controller.get_all_business_order_status_controllers()}

    jobs = controller.get_all_jobs()

    for job in body.jobs:
        # 检测是否可操作。
        if (result := business_orders_can_do_mapping.get(job.business_order_id)) is not None:
            ok, reason = result
            if not ok:
                errors.append((job, BizError(reason)))
                continue
        else:
            errors.append((job, BizError(f"未找到工单: {job.business_order_id}")))
            continue

        # 获取 Job 实例，降低数据库查询。
        job_instance = jobs.get(job.job_id)
        if job_instance is None:
            errors.append((job, BizError(f"未找到工单关联的任务: {job.job_id}")))
            continue

        log_vars.JobId.set(job.job_id)
        log_vars.BusinessOrderId.set(job.business_order_id)
        try:
            job_action = JobAction(
                job_id=job.job_id,
                job=job_instance,
                business_order_status_controller=bos_controllers_mapping.get(job.business_order_id),
            )
            match action:
                case Action.assign:
                    job_action.assign(
                        assignee_assistant=assignee_assistant,
                        operate_assistant=operate_assistant,
                        operate_reason=body.reason,
                    )
                case Action.deliver:
                    job_action.deliver(
                        deliver_to_assistant=assignee_assistant,
                        operate_assistant=operate_assistant,
                        operate_reason=body.reason,
                    )
        except BizError as e:
            errors.append((job, e))

    if errors:
        return schema.BatchActionRes.build_for_batch_action_req(errors)
    else:
        return schema.BatchActionRes(success=True)


@api.put("/business-orders/accept")
@validate
def job_accept(body: schema.JobAcceptActionReq) -> schema.ApiRes:
    from robot_processor.business_order.fsm import BusinessOrderStatusController
    from robot_processor.business_order.job_action import JobAction

    log_vars.JobId.set(body.job.job_id)
    log_vars.BusinessOrderId.set(body.job.business_order_id)

    assert g.login_user_detail
    operate_assistant = g.login_user_detail
    job_operate_lock = JobOperateLock(
        bo_id=body.job.business_order_id,
        current_job_id=body.job.job_id,
        body=body.dict(),
        action=Action.accept,
    )
    if body.assignee:
        try:
            assignee_assistant = kiosk_client.get_user_by_id(body.assignee.user_type, body.assignee.user_id)
            assert assignee_assistant
        except AssertionError:
            raise BizError("选择的指派客服信息获取失败")
    else:
        assignee_assistant = None

    business_order = BusinessOrder.query.filter(BusinessOrder.id == body.job.business_order_id).first()
    if business_order is None:
        return schema.ApiRes(success=False, message="未找到该工单")

    controller = BusinessOrderStatusController(business_order=business_order, operator=operate_assistant, is_admin=True)

    ok, reason = controller.can_do_by_action(Action.accept)
    if not ok:
        return schema.ApiRes(success=False, message=reason)

    job_instance = controller.all_jobs.get(body.job.job_id)
    if job_instance is None:
        return schema.ApiRes(success=False, message="Job ID 未与工单绑定")

    job_action = JobAction(
        job_id=body.job.job_id,
        job=job_instance,
        business_order_status_controller=controller,
        operate_lock=job_operate_lock,
    )

    job_action.accept(
        data_for_accept=body.data,
        next_job_assignee_assistant=assignee_assistant,
        operate_assistant=operate_assistant,
        operate_reason=body.reason,
        ignore_check_type_remind_error=body.force,
    )

    return schema.ApiRes()


@api.put("/business-orders/accept/batch")
@validate
def batch_job_accept(body: schema.BatchJobAcceptActionReq) -> schema.BatchActionRes:
    from robot_processor.business_order.fsm import BatchBusinessOrderStatusController
    from robot_processor.business_order.job_action import JobAction

    assert g.login_user_detail
    operate_assistant = g.login_user_detail
    errors = []

    if body.assignee:
        try:
            assignee_assistant = kiosk_client.get_user_by_id(body.assignee.user_type, body.assignee.user_id)
            assert assignee_assistant
        except AssertionError:
            raise BizError("选择的指派客服信息获取失败")
    else:
        assignee_assistant = None

    business_order_ids = [d.business_order_id for d in body.batch_data]

    business_orders = BusinessOrder.query.filter(BusinessOrder.id.in_(business_order_ids)).all()

    # 实例化工单状态控制器。
    controller = BatchBusinessOrderStatusController(
        business_orders=business_orders, operator=operate_assistant, is_admin=True
    )

    business_orders_can_do_mapping = controller.get_all_business_orders_can_do_by_action(Action.accept)

    bos_controllers_mapping = {c.business_order.id: c for c in controller.get_all_business_order_status_controllers()}

    jobs = controller.get_all_jobs()

    _data_for_accept_group: Dict[Tuple[int, int], Dict[str, Any]] = defaultdict(dict)
    for _job_widget_data in body.batch_data:
        _job_key = (_job_widget_data.business_order_id, _job_widget_data.job_id)
        _data_for_accept_group[_job_key][_job_widget_data.widget_key] = _job_widget_data.value

    for _job_key, _data_for_accept in _data_for_accept_group.items():
        _business_order_id, _job_id = _job_key
        _job = schema.JobBriefInReq(business_order_id=_business_order_id, job_id=_job_id)
        log_vars.JobId.set(_job.job_id)
        log_vars.BusinessOrderId.set(_job.business_order_id)

        # 检测是否可操作。
        if (result := business_orders_can_do_mapping.get(_job.business_order_id)) is not None:
            ok, reason = result
            if not ok:
                errors.append((_job, BizError(reason)))
                continue
        else:
            errors.append((_job, BizError(f"未找到工单: {_job.business_order_id}")))
            continue

        # 获取 Job 实例，降低数据库查询。
        job_instance = jobs.get(_job.job_id)
        if job_instance is None:
            errors.append((_job, BizError(f"未找到工单关联的任务: {_job.job_id}")))
            continue

        try:
            job_operate_lock = JobOperateLock(
                bo_id=unwrap_optional(job_instance.business_order_id),
                current_job_id=_job.job_id,
                body=body.dict(),
                action=Action.accept,
            )
            _job_action = JobAction(
                job_id=_job.job_id,
                job=job_instance,
                business_order_status_controller=bos_controllers_mapping.get(_job.business_order_id),
                operate_lock=job_operate_lock,
            )
            _job_action.accept(
                data_for_accept=_data_for_accept,
                next_job_assignee_assistant=assignee_assistant,
                operate_assistant=operate_assistant,
                operate_reason=body.reason,
                ignore_check_type_remind_error=body.force,
            )
        except BizError as _e:
            errors.append((_job, _e))

    if errors:
        return schema.BatchActionRes.build_for_batch_action_req(errors)
    else:
        return schema.BatchActionRes(success=True)


@api.put("/business-orders/upgrade")
@api.put("/business-orders/save")
@validate
def job_upgrade(body: schema.JobWithDataActionReq) -> schema.ApiRes:
    from robot_processor.business_order.fsm import BusinessOrderStatusController
    from robot_processor.business_order.job_action import JobAction

    action = request.path.split("/")[-1]
    try:
        action = Action(action)
    except:  # noqa
        return schema.ApiRes(success=False, message="不支持的操作")

    assert g.login_user_detail
    operate_assistant = g.login_user_detail

    log_vars.JobId.set(body.job.job_id)
    log_vars.BusinessOrderId.set(body.job.business_order_id)

    business_order = BusinessOrder.query.filter(BusinessOrder.id == body.job.business_order_id).first()
    if business_order is None:
        return schema.ApiRes(success=False, message="未找到该工单")

    controller = BusinessOrderStatusController(business_order=business_order, operator=operate_assistant, is_admin=True)

    # 检测是否可操作。
    ok, reason = controller.can_do_by_action(action)
    if not ok:
        return schema.ApiRes(success=False, message=reason)

    job = controller.all_jobs.get(body.job.job_id)
    if job is None:
        return schema.ApiRes(success=False, message="Job ID 未与工单绑定")

    job_action = JobAction(job_id=body.job.job_id, job=job, business_order_status_controller=controller)

    match action:
        case Action.upgrade:
            job_action.upgrade(
                data_for_upgrade=body.data,
                operate_assistant=operate_assistant,
                operate_reason=body.reason,
                ignore_check_type_remind_error=body.force,
            )
        case Action.save:
            job_action.save(
                data_for_save=body.data,
                operate_assistant=operate_assistant,
                operate_reason=body.reason,
                ignore_check_type_remind_error=body.force,
            )

    return schema.ApiRes()


@api.put("/business-orders/save/batch")
@validate
def batch_job_save(body: schema.BatchJobWithDataActionReq) -> schema.BatchActionRes:
    from robot_processor.business_order.fsm import BatchBusinessOrderStatusController
    from robot_processor.business_order.job_action import JobAction

    operate_assistant = unwrap_optional(g.login_user_detail)
    errors = []

    business_order_ids = [d.business_order_id for d in body.batch_data]

    business_orders = BusinessOrder.query.filter(BusinessOrder.id.in_(business_order_ids)).all()

    # 实例化工单状态控制器。
    controller = BatchBusinessOrderStatusController(
        business_orders=business_orders, operator=operate_assistant, is_admin=True
    )

    business_orders_can_do_mapping = controller.get_all_business_orders_can_do_by_action(Action.save)

    bos_controllers_mapping = {c.business_order.id: c for c in controller.get_all_business_order_status_controllers()}

    jobs = controller.get_all_jobs()

    _data_for_save_group: Dict[Tuple[int, int], Dict[str, Any]] = defaultdict(dict)
    for _job_widget_data in body.batch_data:
        _job_key = (_job_widget_data.business_order_id, _job_widget_data.job_id)
        _data_for_save_group[_job_key][_job_widget_data.widget_key] = _job_widget_data.value

    for _job_key, _data_for_save in _data_for_save_group.items():
        _business_order_id, _job_id = _job_key
        _job = schema.JobBriefInReq(business_order_id=_business_order_id, job_id=_job_id)

        # 检测是否可操作。
        if (result := business_orders_can_do_mapping.get(_job.business_order_id)) is not None:
            ok, reason = result
            if not ok:
                errors.append((_job, BizError(reason)))
                continue
        else:
            errors.append((_job, BizError(f"未找到工单: {_job.business_order_id}")))
            continue

        # 获取 Job 实例，降低数据库查询。
        job_instance = jobs.get(_job.job_id)
        if job_instance is None:
            errors.append((_job, BizError(f"未找到工单关联的任务: {_job.job_id}")))
            continue

        try:
            _job_action = JobAction(
                job_id=_job.job_id,
                job=job_instance,
                business_order_status_controller=bos_controllers_mapping.get(_job.business_order_id),
            )
            log_vars.JobId.set(_job.job_id)
            log_vars.BusinessOrderId.set(_job.business_order_id)
            _job_action.save(
                data_for_save=_data_for_save,
                operate_assistant=operate_assistant,
                operate_reason=body.reason,
                ignore_check_type_remind_error=body.force,
            )
        except BizError as _e:
            errors.append((_job, _e))

    if errors:
        return schema.BatchActionRes.build_for_batch_action_req(errors)
    else:
        return schema.BatchActionRes(success=True)


@api.get("/jobs/candidate-assistants")
@validate
def list_job_candidate_assistants(
    query: schema.StepCandidateAssistantsReq,
) -> schema.StepCandidateAssistantsRes:
    from sqlalchemy.sql.operators import in_op

    from robot_processor.form.models import Step

    step_uuid_list = list(set(query.step_uuid.split(",")))
    step_query = Step.Queries.step_ids(is_dirty=False, step_uuid=step_uuid_list)
    steps: List[Step] = Step.query.filter(in_op(Step.id, step_query)).all()
    assistants = dict()
    for step in steps:
        step = step.wraps(g.shop)
        assistant_info = step.get_assistants_v2()
        assistants[step.step_uuid] = [
            account.dict() for account in assistant_info.get_latest_assignee_account_details(g.shop)
        ]

    return schema.StepCandidateAssistantsRes(data=assistants)


@api.get("/jobs/ui-schema")
@validate
def get_step_ui_schema(query: schema.StepUISchemaReq) -> schema.StepUISchemaRes:
    job = Job.query.get(query.job_id)
    if job is None:
        raise BizError(f"工单步骤找不到, job_id: {query.job_id}", status_code=404)

    return schema.StepUISchemaRes(ui_schema=copy.deepcopy(job.raw_step_v2.get("ui_schema", []) or []))


@api.post("/business-orders/list")
@validate
def list_business_orders(body: FilterSchema):
    assert g.shop.org_id
    main_query, plain_query, err = generate_business_order_query(g.shop.org_id, g.auth.user_id, body, need_sort=True)
    match err:
        case errors.NOT_FOUND_BOS_BY_KEYWORD:
            return jsonify(
                data=[],
                pages=0,
                total=0,
                per_page=body.per_page,
                page=body.page,
                is_empty=plain_query.limit(1).first() is None,
            )
        case errors.QUERY_COUNT_TOO_LARGE:
            explainer = QueryExplainer(main_query)
            return Failed(reason=f"当前查询数据量大于上限{explainer.row_limit}条，建议您增加筛选条件缩小范围后查询").as_response()
        case errors.NOT_FOUND_BOS:
            plain_query_empty = not db.ro_session.query(plain_query.exists()).scalar()
            return jsonify(
                data=[],
                pages=0,
                total=0,
                per_page=body.per_page,
                page=body.page,
                is_empty=plain_query_empty,
            )

    query_pagination = main_query.paginate(page=body.page, per_page=body.per_page, cached=True, timeout=10, error_out=False)  # type: ignore[attr-defined]  # noqa: E501

    if query_pagination.total == 0:
        data = []
    else:
        data = BatchGetBusinessOrderBrief(int(g.shop.org_id), query_pagination.items).batch_get()

    is_empty = query_pagination.total == 0
    if is_empty is True:
        is_empty = plain_query.limit(1).first() is None

    return jsonify(
        data=AddressWidgetMask().mask_batch(data),
        pages=query_pagination.pages,
        total=query_pagination.total,
        per_page=query_pagination.per_page,
        page=query_pagination.page,
        is_empty=is_empty,
    )


@api.post("/business-orders/statistics")
@validate
def business_orders_statistics(body: FilterSchema):
    assert g.shop.org_id
    if not body.form_id:
        return jsonify({})

    main_query, plain_query, err = generate_business_order_query(
        g.shop.org_id,
        g.auth.user_id,
        body,
    )
    match err:
        case errors.NOT_FOUND_BOS_BY_KEYWORD:
            return jsonify(
                {
                    "success": False,
                    "data": {},
                    "reason": errors.NOT_FOUND_BOS_BY_KEYWORD,
                }
            )
        case errors.QUERY_COUNT_TOO_LARGE:
            return jsonify({"success": False, "data": {}, "reason": errors.QUERY_COUNT_TOO_LARGE})
        case errors.NOT_FOUND_BOS:
            return jsonify({"success": False, "data": {}, "reason": errors.NOT_FOUND_BOS})

    step_name_to_widget_header_mapping = get_step_name_to_widget_header_mapping(body.form_id)
    step_name_to_widget_titles_mapping = get_step_name_to_widget_titles_mapping(step_name_to_widget_header_mapping)
    step_name_to_number_widget_titles = get_step_name_to_number_widget_titles(step_name_to_widget_titles_mapping)

    field_to_value_data_mapping: dict[str, list[Decimal]] = {}

    for business_order in main_query:
        for (
            step_name,
            number_widget_titles,
        ) in step_name_to_number_widget_titles.items():
            for number_widget_title in number_widget_titles:
                number_widget_values = get_number_widget_values(number_widget_title, business_order.data)
                field = "{}_{}".format(
                    step_name,
                    "_".join([i.widget_name.widget_label for i in number_widget_title]),
                )
                value_data = field_to_value_data_mapping.get(field) or []
                field_to_value_data_mapping.update({field: number_widget_values + value_data})

    result = {
        key: {
            "count": str(len(value)),
            "sum": str(Decimal(sum(value)).quantize(Decimal("1.00"))),
            "mean": str(Decimal(sum(value) / len(value)).quantize(Decimal("1.00"))),
        }
        for key, value in field_to_value_data_mapping.items()
        if len(value) != 0
    }
    return jsonify(data=result, success=True)


class BusinessOrderDetailReq(BaseModel):
    business_order_id: int


@api.get("/business-orders/info")
@validate
def business_order_detail(query: BusinessOrderDetailReq):
    from robot_processor.business_order.models import BusinessOrder
    from robot_processor.form.models import FormVersion
    from robot_processor.task_center.getter import get_business_order_detail

    if (business_order := BusinessOrder.query.get(query.business_order_id)) is None:
        return jsonify(success=False, code=404, message="工单实例不存在")

    version_view = FormVersion.View.BusinessOrderBasic.from_orm(business_order.get_form_version())

    assert g.login_user_detail
    data = get_business_order_detail(
        business_order=business_order,
        form=business_order.form.wraps(business_order.shop or g.shop),  # type: ignore[union-attr]
        operate_assistant=g.login_user_detail,
        version=version_view.dict(),
    )

    return jsonify(data=data)


@api.post("/business_order_actions")
@validate
def get_business_order_actions(body: schema.GetBusinessOrderActionsReqeust):
    """
    查询管理后台中，操作人可执行工单的操作。
    :param body:
    :return:
    """
    from robot_processor.business_order.fsm import BusinessOrderStatusController

    operator = g.login_user_detail
    if operator is None:
        return jsonify(msg="未检测到登录用户"), 401

    business_order = BusinessOrder.query.filter(BusinessOrder.id == body.business_order_id).first()
    if business_order is None:
        return jsonify(data={}, msg="未找到工单"), 200

    bsc = BusinessOrderStatusController(
        business_order=business_order,
        operator=operator,
        operator_logged_shop_sid=g.shop.sid,
        is_admin=True,
    )

    bo_actions = bsc.get_actions()

    return jsonify(data=bo_actions)


@api.post("/batch_business_order_actions")
@validate
def get_batch_business_order_actions(body: schema.GetBatchBusinessOrderActionsRequest):
    """
    查询管理后台中，操作人对于多笔工单可执行的操作。
    """
    from robot_processor.business_order.fsm import BatchBusinessOrderStatusController

    operator = g.login_user_detail
    if operator is None:
        return jsonify(msg="未检测到登录用户"), 401

    business_orders = BusinessOrder.query.filter(BusinessOrder.id.in_(body.business_order_ids)).all()

    if len(business_orders) == 0:
        return jsonify(data={}, msg="未找到工单")

    bbsc = BatchBusinessOrderStatusController(
        business_orders=business_orders,
        operator=operator,
        operator_logged_shop_sid=g.shop.sid,
        is_admin=True,
    )
    bo_actions = bbsc.get_all_business_order_actions()

    return jsonify(data=bo_actions)


@api.post("/business-orders/unmask-data")
@validate
def unmask_bo_data(body: UnmaskBoDataReqSchema):
    unmasked = AddressWidgetMask().unmask(body.id, body.widget_key, body.fields)
    return jsonify(unmasked)


class ListStepOptionsBody(BaseModel):
    form_id_list: list[int]


@api.post("/business_order_steps")
@validate
def list_step_options(body: ListStepOptionsBody):
    form_list = Form.query.filter(Form.id.in_(body.form_id_list)).all()
    step_names: set[str] = set()
    for form in form_list:
        for step in form.job_steps:
            step_names.add(step.name)
    return jsonify(data=[dict(label=step_name, value=step_name) for step_name in step_names])
