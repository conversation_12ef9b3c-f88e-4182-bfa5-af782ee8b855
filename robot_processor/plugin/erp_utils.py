from typing import List

from leyan_proto.digismart.trade.dgt_jd_trade_pb2 import JdTradeInfo
from leyan_proto.digismart.trade.dgt_ks_trade_pb2 import CommonTradeInfo
from loguru import logger

from robot_processor.client import trade_client
from robot_processor.enums import ShopPlatform
from robot_processor.error.client_request import WdtRequestError
from robot_processor.job.jst_trade import TradesFinder as JstTradesFinder
from robot_processor.plugin.erp_schemas import ErpCommonCombineItem
from robot_processor.plugin.erp_schemas import ErpCommonSkuItem
from robot_processor.plugin.erp_schemas import ErpCommonSpuItem
from robot_processor.plugin.erp_schemas import ErpOriginOrderItem
from robot_processor.plugin.erp_schemas import OrderSkuInfo
from robot_processor.plugin.erp_schemas import SkuInfo
from robot_processor.plugin.erp_schemas import SkuInventory
from robot_processor.shop.kiosk_models import KioskShop
from robot_processor.shop.models import Shop
from robot_processor.utils import unwrap_optional
from rpa.erp.jackyun.schemas import JackyunOrder
from rpa.erp.jackyun.sdk import JackyunQmSDK
from rpa.erp.jackyun.sdk import JackyunSDK
from rpa.erp.jst import JstLimitError
from rpa.erp.jst import JstNewSDK
from rpa.erp.jst import JstRequestError
from rpa.erp.jst.schemas import InventoryQueryResp
from rpa.erp.jst.schemas import JstBaseOrder as JstOrder
from rpa.erp.jst.schemas import WmsPartnerQueryResp
from rpa.erp.wdgj.schemas import WdgjOrder
from rpa.erp.wdgj.sdk import WdgjQmSDK
from rpa.erp.wdt import WdtClient
from rpa.erp.wdt import WdtOpenAPIClient
from rpa.erp.wdt.schemas import Trade as WdtOrder
from rpa.erp.wdtulti import WdtQmRateLimitError
from rpa.erp.wdtulti import WdtUltiOpenAPIClient
from rpa.erp.wdtulti import WdtUltiQM
from rpa.erp.wdtulti.schemas import WdtultiOrderModel as WdtUltiOrder

ERP_LIMIT_ERROR = "短时间内请求次数过多，触发 ERP 限流。"
INVALID_ERP_RESPONSE = "未能从 ERP 开放平台获取到数据。"
ERP_REQUEST_ERROR = "ERP 接口请求失败！"
EMPTY_QUERY_CONDITIONS = "查询条件不能全部为空。"
UNKNOWN_ERROR = "未知错误。"

MAX_QUERY_SKUS_LENGTH = 100


def jst_format_order_items(
    order: JstOrder,
) -> tuple[set[str], set[str], list[ErpOriginOrderItem]]:
    # 统计主订单下包含的所有组合商品、普通商品以及需要输出的商品。
    need_query_combine_item_ids_set: set[str] = {item.sku_id for item in order.items if item.sku_type == "combine"}
    need_query_single_item_ids_set: set[str] = {
        item.sku_id
        for item in order.items
        if item.sku_type == "normal" and item.src_combine_sku_id not in need_query_combine_item_ids_set
    }
    need_output_sku_items: List[ErpOriginOrderItem] = []

    for item in order.items:
        if item.sku_id is not None:
            if item.sku_type == "combine":
                need_output_sku_items.append(
                    ErpOriginOrderItem(
                        qty=(item.qty or 0),
                        is_combine=True,
                        outer_sku_id=item.sku_id,
                        payment=item.item_pay_amount,
                        tid=order.so_id,
                        oid=item.outer_oi_id,
                    )
                )
            elif item.sku_type == "normal" and item.src_combine_sku_id not in need_query_combine_item_ids_set:
                need_output_sku_items.append(
                    ErpOriginOrderItem(
                        qty=(item.qty or 0),
                        is_combine=False,
                        outer_sku_id=item.sku_id,
                        payment=item.item_pay_amount,
                        tid=order.so_id,
                        oid=item.outer_oi_id,
                    )
                )
    return (
        need_query_combine_item_ids_set,
        need_query_single_item_ids_set,
        need_output_sku_items,
    )


def jst_query_combine_items(
    jst_new_sdk: JstNewSDK,
    need_query_combine_sku_ids_set: set[str],
) -> tuple[dict[str, tuple[ErpCommonCombineItem, ErpCommonSpuItem]], set[str]]:
    combine_item_id_to_item_map: dict[str, tuple[ErpCommonCombineItem, ErpCommonSpuItem]] = {}
    sub_item_ids_set: set[str] = set()

    # 查询所有组合商品的最新信息。
    combine_skus_resp = jst_new_sdk.query_combine_skus_by_combine_sku_ids(list(need_query_combine_sku_ids_set))
    for combine_sku in combine_skus_resp.data.datas:
        sub_items = [
            ErpCommonCombineItem.SubItem(outer_sku_id=item.src_sku_id or "", qty=float(item.qty))
            for item in combine_sku.items
            if combine_sku.items
        ]

        combine_item_id_to_item_map.update(
            {
                combine_sku.sku_id: (
                    ErpCommonCombineItem(
                        outer_sku_id=combine_sku.sku_id or "",
                        outer_sku_short_name=combine_sku.short_name,
                        outer_sku_name=combine_sku.name,
                        outer_sku_pic_url=combine_sku.pic,
                        outer_price=combine_sku.sale_price,
                        outer_properties=combine_sku.properties_value,
                        outer_cost_price=combine_sku.cost_price,
                        sub_items=sub_items,
                    ),
                    ErpCommonSpuItem(
                        outer_spu_id=combine_sku.i_id or "",
                        outer_spu_name=None,
                        outer_spu_short_name=None,
                    ),
                )
            }
        )
        # 将组合装下属的单品的 sku_id 放入需要查询的普通商品中。
        for item in combine_sku.items:
            if item.src_sku_id:
                sub_item_ids_set.add(str(item.src_sku_id))

    return combine_item_id_to_item_map, sub_item_ids_set


def jst_query_single_items(
    jst_new_sdk: JstNewSDK,
    need_query_single_sku_ids_set: set[str],
) -> dict[str, tuple[ErpCommonSkuItem, ErpCommonSpuItem]]:
    single_item_id_to_item_map: dict[str, tuple[ErpCommonSkuItem, ErpCommonSpuItem]] = {}

    # 查询所有单品信息。
    single_skus_resp = jst_new_sdk.query_skus_by_sku_ids(list(need_query_single_sku_ids_set))
    for single_sku in single_skus_resp.data.datas:
        single_item_id_to_item_map.update(
            {
                single_sku.sku_id: (
                    ErpCommonSkuItem(
                        outer_sku_id=single_sku.sku_id or "",
                        outer_sku_short_name=single_sku.short_name,
                        outer_sku_name=single_sku.name,
                        outer_sku_pic_url=single_sku.pic,
                        outer_price=single_sku.sale_price,
                        outer_properties=single_sku.properties_value,
                        outer_cost_price=single_sku.cost_price,
                    ),
                    ErpCommonSpuItem(
                        outer_spu_id=single_sku.i_id or "",
                        outer_spu_name=None,
                        outer_spu_short_name=None,
                    ),
                )
            }
        )
    return single_item_id_to_item_map


def jst_get_order_skus(
    shop: Shop,
    jst_new_sdk: JstNewSDK,
    tid: str,
    oid: str | None = None,
) -> tuple[bool, list[OrderSkuInfo] | None]:
    kiosk_shop = KioskShop.query.get(shop.channel_id)
    assert kiosk_shop is not None
    if kiosk_shop.tags and any(tag["status"] for tag in kiosk_shop.tags if tag["name"] == "新聚水潭授权"):
        origin_order_items = []
        match shop.platform:
            case ShopPlatform.TMALL | ShopPlatform.TAOBAO:
                trade = trade_client.get_trade_by_tid(shop.sid, tid)
                if not trade.trade_id:
                    return True, None
                if oid:
                    orders = [order for order in trade.orders if order.oid == oid]
                else:
                    orders = trade.orders
                if len(orders) == 0:
                    return True, None
                origin_order_items.extend(
                    [
                        ErpOriginOrderItem(
                            qty=order.quantity,
                            is_combine=False,
                            outer_sku_id=order.outer_sku_id,
                            payment=order.payment,
                            tid=tid,
                            oid=order.oid,
                        )
                        for order in orders
                    ]
                )
            case ShopPlatform.PDD:
                orders_resp = trade_client.get_trade_by_tid_and_channel([tid])
                orders = [trade_info.pdd_trade_info for trade_info in orders_resp.trade_info_list]
                if len(orders) == 0:
                    return True, None
                origin_order_items.extend(
                    [
                        ErpOriginOrderItem(
                            qty=item.goods_count,
                            is_combine=False,
                            outer_sku_id=item.outer_id,
                            payment=item.goods_price,
                            tid=tid,
                            oid=tid,
                        )
                        for order in orders
                        for item in order.item_list
                    ]
                )
            case ShopPlatform.DOUDIAN:
                orders_resp = trade_client.get_trade_by_tid_and_channel([tid], ShopPlatform.DOUDIAN.value)
                orders = [trade_info.dy_trade_info for trade_info in orders_resp.trade_info_list]
                # todo(39): 确认子订单过滤条件是否正确
                if len(orders) == 0:
                    return True, None
                origin_order_items.extend(
                    [
                        ErpOriginOrderItem(
                            qty=sku.item_num,
                            is_combine=False,
                            outer_sku_id=sku.out_sku_id,
                            payment=sku.pay_amount,
                            tid=tid,
                            oid=str(sku.sku_order_id),
                        )
                        for order in orders
                        for sku in order.sku_order_list
                        if not oid or (oid and sku.sku_order_id == oid)
                    ]
                )
            case ShopPlatform.ALIBABA:
                orders_resp = trade_client.get_trade_by_tid_and_channel(
                    [tid], ShopPlatform.ALIBABA.value, org_id=shop.org_id
                )
                orders = [trade_info.alibaba_trade_info for trade_info in orders_resp.trade_info_list]
                if len(orders) == 0:
                    return True, None
                origin_order_items.extend(
                    [
                        ErpOriginOrderItem(
                            qty=sku.quantity,
                            is_combine=False,
                            outer_sku_id=sku.cargo_number,
                            payment=sku.item_amount,
                            tid=tid,
                            oid=sku.sub_item_id,
                        )
                        for order in orders
                        for sku in order.product_items
                        if not oid or (oid and sku.sub_item_id == oid)
                    ]
                )
            case ShopPlatform.JD:
                orders_resp = trade_client.get_trade_by_tid_and_channel(
                    [tid], ShopPlatform.JD.value, org_id=shop.org_id
                )
                jd_orders: list[JdTradeInfo] = [trade_info.jd_trade_info for trade_info in orders_resp.trade_info_list]
                if len(jd_orders) == 0:
                    return True, None
                for jd_order in jd_orders:
                    for jd_item in jd_order.jd_items:
                        origin_order_items.append(
                            ErpOriginOrderItem(
                                qty=float(jd_item.qty),
                                is_combine=False,
                                outer_sku_id=jd_item.outer_sku_id,
                                payment=jd_item.jd_price,
                                tid=tid,
                                oid=jd_item.tid,
                            )
                        )
            case ShopPlatform.KUAISHOU:
                access_token = shop.get_access_token()
                if not access_token:
                    return True, None
                orders_resp = trade_client.get_trade_by_tid_and_channel(
                    [tid],
                    ShopPlatform.KUAISHOU.value,
                    sid=shop.sid,
                    token=access_token,
                    org_id=shop.org_id,
                )
                ks_orders: list[CommonTradeInfo] = [
                    trade_info.ks_trade_info for trade_info in orders_resp.trade_info_list
                ]
                if len(ks_orders) == 0:
                    return True, None
                for ks_order in ks_orders:
                    for ks_item in ks_order.item_list:
                        origin_order_items.append(
                            ErpOriginOrderItem(
                                qty=float(ks_item.goods_count),
                                is_combine=False,
                                outer_sku_id=ks_item.outer_id,
                                payment=ks_item.goods_price,
                                tid=tid,
                                oid=tid,
                            )
                        )
            case _:
                return True, None
        outer_sku_ids = {item.outer_sku_id for item in origin_order_items}
        combine_item_id_to_item_map, sub_item_ids_set = jst_query_combine_items(jst_new_sdk, outer_sku_ids)
        single_item_id_to_item_map = jst_query_single_items(jst_new_sdk, outer_sku_ids | sub_item_ids_set)
        # 平台订单没有is_combine的信息，在这里修正
        has_missed1 = False
        for item in origin_order_items:
            if item.outer_sku_id in combine_item_id_to_item_map:
                item.is_combine = True
            elif item.outer_sku_id in single_item_id_to_item_map:
                item.is_combine = False
            else:
                has_missed1 = True
        has_missed2, output_skus = get_order_output_items(
            origin_order_items, combine_item_id_to_item_map, single_item_id_to_item_map
        )
        return has_missed1 or has_missed2, output_skus
    else:
        current_orders = JstTradesFinder(tid=tid, shop=shop).try_get_orders()
        if oid:
            for co in current_orders:
                co.items = [item for item in co.items if item.outer_oi_id == oid]
        if len(current_orders) == 0:
            return True, None
        sorted_current_orders = sorted(current_orders, key=lambda trade: int(trade.o_id))
        main_order = sorted_current_orders[0]
        # 过滤换掉的商品
        main_order.items = [item for item in main_order.items if item.item_status != "Replaced"]
        (
            need_query_combine_item_ids_set,
            need_query_single_item_ids_set,
            need_output_items,
        ) = jst_format_order_items(main_order)

        if need_query_combine_item_ids_set:
            combine_item_id_to_item_map, sub_item_ids_set = jst_query_combine_items(
                jst_new_sdk, need_query_combine_item_ids_set
            )
        else:
            combine_item_id_to_item_map, sub_item_ids_set = {}, set()

        if single_item_ids_set := (need_query_single_item_ids_set | sub_item_ids_set):
            single_item_id_to_item_map = jst_query_single_items(jst_new_sdk, single_item_ids_set)
        else:
            single_item_id_to_item_map = {}

    return get_order_output_items(
        need_output_items,
        combine_item_id_to_item_map,
        single_item_id_to_item_map,
        merge_sku=False,
    )


def jst_get_warehouses(
    jst_new_sdk: JstNewSDK,
    size: int = 1000,
) -> tuple[list[WmsPartnerQueryResp.Data.WmsPartner], str | None]:
    """
    聚水潭查询仓库。
    :param jst_new_sdk:
    :param size:
    :return:
    """
    try:
        resp = jst_new_sdk.wms_partner_query(page_size=size)
        if not resp.data:
            return [], INVALID_ERP_RESPONSE
        return resp.data.datas, None
    except JstLimitError:
        return [], ERP_LIMIT_ERROR
    except JstRequestError as e:
        return [], ERP_REQUEST_ERROR if e.message is None else e.message
    except Exception as e:
        logger.error("聚水潭查询仓库信息失败", e)
        return [], UNKNOWN_ERROR


def jst_query_skus(
    jst_new_sdk: JstNewSDK,
    *,
    sku_ids: list[str] | None = None,
    spu_ids: list[str] | None = None,
    name: str | None = None,
    is_exactly: bool = True,
    page_index: int = 1,
    page_size: int = 10,
) -> tuple[dict[str, SkuInfo], str | None]:
    """
    聚水潭查询商品信息。

    :param jst_new_sdk:
    :param sku_ids:
    :param spu_ids:
    :param name:
    :param is_exactly:
    :param page_index:
    :param page_size:
    :return:
    """
    sku_id_to_sku_info_mapping: dict[str, SkuInfo] = {}
    try:
        if sku_ids:
            # 通过 sku id 来获取。
            resp = jst_new_sdk.query_skus_by_sku_ids(sku_ids, page_index=page_index, page_size=page_size)
            if not resp.data:
                return {}, INVALID_ERP_RESPONSE
            for data in resp.data.datas:
                if not data.enabled:
                    continue
                sku_id_to_sku_info_mapping.update(
                    {
                        data.sku_id: SkuInfo(
                            sku=data.sku_id,
                            spu=data.i_id,
                            name=data.name,
                            properties=data.properties_value,
                        )
                    }
                )
        elif spu_ids:
            # 通过 spu id 来获取。
            resp = jst_new_sdk.query_skus_by_spu_ids(spu_ids, page_index=page_index, page_size=page_size)
            if not resp.data:
                return {}, INVALID_ERP_RESPONSE
            for data in resp.data.datas:
                for sku in data.skus:
                    if not sku.enabled:
                        continue
                    sku_id_to_sku_info_mapping.update(
                        {
                            sku.sku_id: SkuInfo(
                                sku=sku.sku_id,
                                spu=sku.i_id,
                                name=sku.name,
                                properties=sku.properties_value,
                            )
                        }
                    )
        elif name:
            # 通过商品名称来获取。
            resp = jst_new_sdk.query_skus_by_name(name, is_exactly, page_index=page_index, page_size=page_size)
            if not resp.data:
                return {}, INVALID_ERP_RESPONSE
            for data in resp.data.datas:
                if not data.enabled:
                    continue
                sku_id_to_sku_info_mapping.update(
                    {
                        data.sku_id: SkuInfo(
                            sku=data.sku_id,
                            spu=data.i_id,
                            name=data.name,
                            properties=data.properties_value,
                        )
                    }
                )
        else:
            return {}, EMPTY_QUERY_CONDITIONS
    except JstLimitError:
        return {}, ERP_LIMIT_ERROR
    except JstRequestError as e:
        return {}, ERP_REQUEST_ERROR if e.message is None else e.message
    except Exception as e:
        logger.error("聚水潭查询商品信息失败", e)
        return {}, UNKNOWN_ERROR
    return sku_id_to_sku_info_mapping, None


def jst_compute_available_qty(inventory: InventoryQueryResp.Data.Inventory) -> int:
    """
    聚水潭计算可发库存。
    :param inventory:
    :return:
    """
    if any(
        [
            inventory.qty is None,
            inventory.order_lock is None,
            inventory.virtual_qty is None,
            inventory.purchase_qty is None,
            inventory.in_qty is None,
            inventory.return_qty is None,
        ]
    ):
        # 日志统计下一定时间内的库存缺少字段的数量
        logger.error("聚水潭无法计算可发库存 {}", inventory)
    # 若某一字段没有获取到，则按照 0 计算。
    qty = inventory.qty or 0
    order_lock = inventory.order_lock or 0
    virtual_qty = inventory.virtual_qty or 0
    purchase_qty = inventory.purchase_qty or 0
    in_qty = inventory.in_qty or 0
    return_qty = inventory.return_qty or 0

    available_qty = qty - order_lock + virtual_qty + purchase_qty + in_qty - return_qty
    return available_qty


def jst_match_skus_inventories(
    jst_new_sdk: JstNewSDK,
    sku_id_to_sku_info_mapping: dict[str, SkuInfo],
    warehouses: list[WmsPartnerQueryResp.Data.WmsPartner],
) -> tuple[dict[str, SkuInfo], str | None]:
    sku_ids: list[str] = list(sku_id_to_sku_info_mapping.keys())
    try:
        # 按照聚水潭查询库存限制的最大商品数量来批量查询。
        for i in range((len(sku_ids) // MAX_QUERY_SKUS_LENGTH) + 1):
            current_sku_ids = sku_ids[(i * MAX_QUERY_SKUS_LENGTH) : ((i + 1) * MAX_QUERY_SKUS_LENGTH)]
            # 聚水潭按照仓库 id 来分批查询
            for warehouse in warehouses:
                resp = jst_new_sdk.inventory_query(current_sku_ids, warehouse.wms_co_id)
                if resp.data:
                    # 更新商品的库存信息。
                    for inventory in resp.data.inventorys:
                        if sku_info := sku_id_to_sku_info_mapping.get(unwrap_optional(inventory.sku_id)):
                            sku_info.inventories.append(
                                SkuInventory(
                                    sku_id=inventory.sku_id,
                                    warehouse_name=warehouse.name,
                                    qty=inventory.qty,
                                    available_qty=jst_compute_available_qty(inventory),
                                )
                            )
    # 如果发生了异常，则返回当前已经获取到的商品信息。
    except JstLimitError:
        return sku_id_to_sku_info_mapping, ERP_LIMIT_ERROR
    except JstRequestError as e:
        return sku_id_to_sku_info_mapping, (ERP_REQUEST_ERROR if e.message is None else e.message)
    except Exception as e:
        logger.error("聚水潭查询商品库存信息失败", e)
        return sku_id_to_sku_info_mapping, UNKNOWN_ERROR
    return sku_id_to_sku_info_mapping, None


def wdt_format_order_items(
    order: WdtOrder,
) -> tuple[set[str], set[str], list[ErpOriginOrderItem]]:
    # 统计主订单下包含的所有组合商品、普通商品以及需要输出的商品。
    need_query_combine_item_ids_set: set[str] = {
        item.suite_no for item in order.goods_list if item.suite_no is not None and item.suite_no != ""
    }
    need_query_single_item_ids_set: set[str] = {
        item.spec_no
        for item in order.goods_list
        if (item.suite_no is None or item.suite_no == "") and item.suite_no not in need_query_combine_item_ids_set
    }
    need_output_items: list[ErpOriginOrderItem] = []

    for item in order.goods_list:
        if item.suite_no is not None and item.suite_no != "":
            need_output_items.append(
                ErpOriginOrderItem(
                    qty=float(item.num or 0),
                    is_combine=True,
                    outer_sku_id=item.suite_no,
                    payment=item.price,
                    tid=item.src_tid,
                    oid=item.src_oid,
                )
            )
        elif (
            item.spec_no is not None
            and (item.suite_no is None or item.suite_no == "")
            and item.suite_no not in need_query_combine_item_ids_set
        ):
            need_output_items.append(
                ErpOriginOrderItem(
                    qty=float(item.num or 0),
                    is_combine=False,
                    outer_sku_id=item.spec_no,
                    payment=item.price,
                    tid=item.src_tid,
                    oid=item.src_oid,
                )
            )

    return (
        need_query_combine_item_ids_set,
        need_query_single_item_ids_set,
        need_output_items,
    )


def wdt_query_combine_items(
    wdt_sdk: WdtOpenAPIClient,
    need_query_combine_sku_ids_set: set[str],
) -> tuple[dict[str, tuple[ErpCommonCombineItem, ErpCommonSpuItem]], set[str]]:
    combine_item_id_to_item_map: dict[str, tuple[ErpCommonCombineItem, ErpCommonSpuItem]] = {}
    sub_item_ids_set: set[str] = set()

    # 查询所有组合商品的最新信息。
    for combine_item_id in need_query_combine_sku_ids_set:
        combine_skus_resp = wdt_sdk.query_combine_skus_by_combine_sku_id(combine_item_id)
        for combine_sku in combine_skus_resp.suites:
            sub_items = [
                ErpCommonCombineItem.SubItem(outer_sku_id=item.spec_no or "", qty=float(item.num))
                for item in combine_sku.specs_list
                if combine_sku.specs_list
            ]

            combine_item_id_to_item_map.update(
                {
                    combine_sku.suite_no: (
                        ErpCommonCombineItem(
                            outer_sku_id=combine_sku.suite_no or "",
                            outer_sku_short_name=combine_sku.short_name,
                            outer_sku_name=combine_sku.suite_name,
                            outer_sku_pic_url=None,
                            outer_price=combine_sku.retail_price,
                            outer_properties=None,
                            sub_items=sub_items,
                        ),
                        ErpCommonSpuItem(
                            outer_spu_id="",
                            outer_spu_name=None,
                            outer_spu_short_name=None,
                        ),
                    )
                }
            )
            # 将组合装下属的单品的 sku_id 放入需要查询的普通商品中。
            for item in combine_sku.specs_list:
                if item.spec_no:
                    sub_item_ids_set.add(str(item.spec_no))

    return combine_item_id_to_item_map, sub_item_ids_set


def wdt_query_single_items(
    wdt_sdk: WdtOpenAPIClient,
    need_query_single_sku_ids_set: set[str],
) -> dict[str, tuple[ErpCommonSkuItem, ErpCommonSpuItem]]:
    single_item_id_to_item_map: dict[str, tuple[ErpCommonSkuItem, ErpCommonSpuItem]] = {}

    # 查询所有单品信息。
    for single_sku_id in need_query_single_sku_ids_set:
        single_skus_resp = wdt_sdk.query_skus_by_sku_id(single_sku_id)
        for single_spu in single_skus_resp.goods_list:
            for single_sku in single_spu.spec_list:
                single_item_id_to_item_map.update(
                    {
                        single_sku.spec_no: (
                            ErpCommonSkuItem(
                                outer_sku_id=single_sku.spec_no or "",
                                outer_sku_short_name=None,
                                outer_sku_name=None,
                                outer_sku_pic_url=single_sku.img_url,
                                outer_price=single_sku.retail_price,
                                outer_properties=single_sku.spec_name,
                            ),
                            ErpCommonSpuItem(
                                outer_spu_id=single_spu.goods_no or "",
                                outer_spu_name=single_spu.goods_name,
                                outer_spu_short_name=single_spu.short_name,
                            ),
                        )
                    }
                )
    return single_item_id_to_item_map


def wdt_get_order_skus(
    wdt_client: WdtClient,
    wdt_sdk: WdtOpenAPIClient,
    tid: str,
    oid: str | None,
) -> tuple[bool, list[OrderSkuInfo] | None]:
    current_orders = wdt_client.trade_query(tid).response.trades
    if len(current_orders) == 0:
        return True, None
    if oid:
        for current_order in current_orders:
            current_order.goods_list = [goods for goods in current_order.goods_list if goods.src_oid == oid]
    current_orders = [current_order for current_order in current_orders if current_order.trade_type == 1]
    sorted_current_orders = sorted(current_orders, key=lambda trade: int(trade.trade_id))

    has_missed: bool = False
    output_items: list[OrderSkuInfo] = []
    src_oid_to_need_output_items_map: dict[str, list[ErpOriginOrderItem]] = {}

    # 提取出每笔订单中的商品，并根据 src_oid 进行归类。
    for main_order in sorted_current_orders:
        (
            _,
            _,
            need_output_items,
        ) = wdt_format_order_items(main_order)

        for need_output_item in need_output_items:
            if need_output_item.oid is None:
                continue
            if current_src_oid_items := src_oid_to_need_output_items_map.get(need_output_item.oid):
                for current_src_oid_item in current_src_oid_items:
                    if current_src_oid_item.outer_sku_id == need_output_item.outer_sku_id:
                        current_src_oid_item.qty += need_output_item.qty
                if need_output_item.outer_sku_id not in [csoi.outer_sku_id for csoi in current_src_oid_items]:
                    current_src_oid_items.append(need_output_item)
                    src_oid_to_need_output_items_map[need_output_item.oid] = current_src_oid_items
            else:
                src_oid_to_need_output_items_map[need_output_item.oid] = [need_output_item]

    # 将每个 src_oid 下的商品统一放到一个列表中
    all_need_output_items: list[ErpOriginOrderItem] = []
    for _, need_output_items in src_oid_to_need_output_items_map.items():
        all_need_output_items.extend(need_output_items)

    # 需要查询的所有组合装的 outer_sku_id 组合成的集合
    need_query_combine_item_ids_set: set[str] = {item.outer_sku_id for item in all_need_output_items if item.is_combine}
    # 需要查询的所有单品的 outer_sku_id 组合成的集合
    need_query_single_item_ids_set: set[str] = {
        item.outer_sku_id for item in all_need_output_items if not item.is_combine
    }

    # 查询组合装
    combine_item_id_to_item_map, sub_item_ids_set = wdt_query_combine_items(wdt_sdk, need_query_combine_item_ids_set)

    # 查询单品及组合转下属单品。
    single_item_id_to_item_map = wdt_query_single_items(wdt_sdk, need_query_single_item_ids_set | sub_item_ids_set)

    # 计算得出需要输出的商品
    has_missed, order_output_items = get_order_output_items(
        all_need_output_items, combine_item_id_to_item_map, single_item_id_to_item_map
    )

    # 对组合装商品进行下单商品数量的重新计算
    for order_output_item in order_output_items:
        # 如果是组合装，则需要重新计算一下真实的下单商品数量
        if order_output_item.is_combine:
            child_sku_qty = sum([child_sku.qty for child_sku in order_output_item.child_skus])
            if child_sku_qty == 0 or (order_output_item.qty % child_sku_qty) != 0:
                logger.error(
                    "无法正确计算出组合装商品: {} 的下单商品数量, oid: {}, qty: {}, child_sku_qty: {}".format(
                        order_output_item.outer_sku_id, oid, order_output_item.qty, child_sku_qty
                    )
                )
                has_missed = True
                continue
            else:
                # 原 qty 存储的是 num 这个字段的总和。
                order_output_item.qty = order_output_item.qty // child_sku_qty
        output_items.append(order_output_item)

    return has_missed, output_items


def wdt_query_skus(
    wdt_sdk: WdtOpenAPIClient,
    *,
    sku_id: str | None = None,
    spu_id: str | None = None,
) -> tuple[dict[str, SkuInfo], str | None]:
    sku_id_to_sku_info_mapping: dict[str, SkuInfo] = {}
    try:
        if sku_id:
            resp = wdt_sdk.query_skus_by_sku_id(sku_id)
        elif spu_id:
            resp = wdt_sdk.query_skus_by_spu_id(spu_id)
        else:
            return {}, EMPTY_QUERY_CONDITIONS
        for goods in resp.goods_list:
            for spec in goods.spec_list:
                sku_id_to_sku_info_mapping.update(
                    {
                        spec.spec_no: SkuInfo(
                            sku=spec.spec_no,
                            spu=goods.goods_no,
                            name=goods.goods_name,
                            properties=spec.spec_name,
                        )
                    }
                )
    except WdtRequestError as e:
        return {}, ERP_REQUEST_ERROR if not e.message else e.message
    except Exception as e:
        logger.error("旺店通企业版查询商品信息失败", e)
        return {}, UNKNOWN_ERROR
    return sku_id_to_sku_info_mapping, None


def wdt_match_skus_inventories(
    wdt_client: WdtClient,
    sku_id_to_sku_info_mapping: dict[str, SkuInfo],
    warehouse_no: str | None = None,
) -> tuple[dict[str, SkuInfo], str | None]:
    try:
        for sku_id, sku_info in sku_id_to_sku_info_mapping.items():
            resp = wdt_client.stock_query(spec_no=sku_id, warehouse_no=warehouse_no)
            for stock in resp.response.stocks:
                sku_info.inventories.append(
                    SkuInventory(
                        sku_id=sku_id,
                        warehouse_name=stock.warehouse_name,
                        qty=stock.stock_num,
                        available_qty=stock.avaliable_num,
                    )
                )
    except WdtRequestError as e:
        return sku_id_to_sku_info_mapping, (ERP_REQUEST_ERROR if not e.message else e.message)
    except Exception as e:
        logger.error("旺店通企业版查询库存信息失败", e)
        return sku_id_to_sku_info_mapping, UNKNOWN_ERROR
    return sku_id_to_sku_info_mapping, None


def wdt_ulti_format_order_items(
    order: WdtUltiOrder,
) -> tuple[set[str], set[str], list[ErpOriginOrderItem]]:
    # 统计主订单下包含的所有组合商品、普通商品以及需要输出的商品。
    need_query_combine_item_ids_set: set[str] = {
        item.suite_no for item in order.detail_list if item.suite_no is not None and item.suite_no != ""
    }
    need_query_single_item_ids_set: set[str] = {
        item.spec_no
        for item in order.detail_list
        if (item.suite_no is None or item.suite_no == "") and item.suite_no not in need_query_combine_item_ids_set
    }
    need_output_items: list[ErpOriginOrderItem] = []

    for item in order.detail_list:
        if item.suite_no is not None and item.suite_no != "":
            need_output_items.append(
                ErpOriginOrderItem(
                    qty=float(item.num or 0),
                    is_combine=True,
                    outer_sku_id=item.suite_no,
                    payment=item.price,
                    tid=item.src_tid,
                    oid=item.src_oid,
                )
            )
        elif (
            item.spec_no is not None
            and (item.suite_no is None or item.suite_no == "")
            and item.suite_no not in need_query_combine_item_ids_set
        ):
            need_output_items.append(
                ErpOriginOrderItem(
                    qty=float(item.num or 0),
                    is_combine=False,
                    outer_sku_id=item.spec_no,
                    payment=item.price,
                    tid=item.src_tid,
                    oid=item.src_oid,
                )
            )

    return (
        need_query_combine_item_ids_set,
        need_query_single_item_ids_set,
        need_output_items,
    )


def wdt_ulti_query_combine_items(
    wdt_ulti_sdk: WdtUltiOpenAPIClient,
    need_query_combine_sku_ids_set: set[str],
) -> tuple[dict[str, tuple[ErpCommonCombineItem, ErpCommonSpuItem]], set[str]]:
    combine_item_id_to_item_map: dict[str, tuple[ErpCommonCombineItem, ErpCommonSpuItem]] = {}
    sub_item_ids_set: set[str] = set()

    # 查询所有组合商品的最新信息。
    for combine_item_id in need_query_combine_sku_ids_set:
        combine_skus_resp = wdt_ulti_sdk.query_combine_skus_by_combine_sku_id(combine_item_id)
        for combine_sku in combine_skus_resp.data.suite_list:
            sub_items = [
                ErpCommonCombineItem.SubItem(outer_sku_id=item.spec_no or "", qty=float(item.num))
                for item in combine_sku.detail_list
                if combine_sku.detail_list
            ]

            combine_item_id_to_item_map.update(
                {
                    combine_sku.suite_no: (
                        ErpCommonCombineItem(
                            outer_sku_id=combine_sku.suite_no or "",
                            outer_sku_short_name=combine_sku.short_name,
                            outer_sku_name=combine_sku.suite_name,
                            outer_sku_pic_url="",
                            outer_price=combine_sku.retail_price,
                            outer_properties="",
                            sub_items=sub_items,
                        ),
                        ErpCommonSpuItem(
                            outer_spu_id="",
                            outer_spu_name=None,
                            outer_spu_short_name=None,
                        ),
                    )
                }
            )
            # 将组合装下属的单品的 sku_id 放入需要查询的普通商品中。
            for item in combine_sku.detail_list:
                if item.spec_no:
                    sub_item_ids_set.add(str(item.spec_no))

    return combine_item_id_to_item_map, sub_item_ids_set


def wdt_ulti_query_single_items(
    wdt_ulti_sdk: WdtUltiOpenAPIClient,
    need_query_single_sku_ids_set: set[str],
) -> dict[str, tuple[ErpCommonSkuItem, ErpCommonSpuItem]]:
    single_item_id_to_item_map: dict[str, tuple[ErpCommonSkuItem, ErpCommonSpuItem]] = {}

    # 查询所有单品信息。
    for single_sku_id in need_query_single_sku_ids_set:
        single_skus_resp = wdt_ulti_sdk.query_skus_by_sku_id(single_sku_id)
        for single_spu in single_skus_resp.data.goods_list:
            for single_sku in single_spu.spec_list:
                single_item_id_to_item_map.update(
                    {
                        single_sku.spec_no: (
                            ErpCommonSkuItem(
                                outer_sku_id=single_sku.spec_no or "",
                                outer_sku_short_name=None,
                                outer_sku_name=None,
                                outer_sku_pic_url=single_sku.img_url,
                                outer_price=single_sku.retail_price,
                                outer_properties=single_sku.spec_name,
                            ),
                            ErpCommonSpuItem(
                                outer_spu_id=single_spu.goods_no or "",
                                outer_spu_name=single_spu.goods_name,
                                outer_spu_short_name=single_spu.short_name,
                            ),
                        )
                    }
                )
    return single_item_id_to_item_map


def wdt_ulti_get_order_skus(
    wdt_ulti_qm_sdk: WdtUltiQM,
    wdt_ulti_sdk: WdtUltiOpenAPIClient,
    tid: str,
    oid: str | None,
) -> tuple[bool, list[OrderSkuInfo] | None]:
    current_orders = wdt_ulti_qm_sdk.get_orders(src_tid=tid).order
    if len(current_orders) == 0:
        return True, None
    if oid:
        for current_order in current_orders:
            current_order.detail_list = [detail for detail in current_order.detail_list if detail.src_oid == oid]
    current_orders = [current_order for current_order in current_orders if current_order.trade_type == 1]
    sorted_current_orders = sorted(current_orders, key=lambda trade: int(trade.trade_id))

    has_missed: bool = False
    output_items: list[OrderSkuInfo] = []
    src_oid_to_need_output_items_map: dict[str, list[ErpOriginOrderItem]] = {}

    for main_order in sorted_current_orders:
        (
            _,
            _,
            need_output_items,
        ) = wdt_ulti_format_order_items(main_order)

        for need_output_item in need_output_items:
            if need_output_item.oid is None:
                continue
            if current_src_oid_items := src_oid_to_need_output_items_map.get(need_output_item.oid):
                for current_src_oid_item in current_src_oid_items:
                    if current_src_oid_item.outer_sku_id == need_output_item.outer_sku_id:
                        current_src_oid_item.qty += need_output_item.qty
                if need_output_item.outer_sku_id not in [csoi.outer_sku_id for csoi in current_src_oid_items]:
                    current_src_oid_items.append(need_output_item)
                    src_oid_to_need_output_items_map[need_output_item.oid] = current_src_oid_items
            else:
                src_oid_to_need_output_items_map[need_output_item.oid] = [need_output_item]

    # 将每个 src_oid 下的商品统一放到一个列表中
    all_need_output_items: list[ErpOriginOrderItem] = []
    for _, need_output_items in src_oid_to_need_output_items_map.items():
        all_need_output_items.extend(need_output_items)

    # 需要查询的所有组合装的 outer_sku_id 组合成的集合
    need_query_combine_item_ids_set: set[str] = {item.outer_sku_id for item in all_need_output_items if item.is_combine}
    # 需要查询的所有单品的 outer_sku_id 组合成的集合
    need_query_single_item_ids_set: set[str] = {
        item.outer_sku_id for item in all_need_output_items if not item.is_combine
    }

    combine_item_id_to_item_map, sub_item_ids_set = wdt_ulti_query_combine_items(
        wdt_ulti_sdk, need_query_combine_item_ids_set
    )

    single_item_id_to_item_map = wdt_ulti_query_single_items(
        wdt_ulti_sdk, need_query_single_item_ids_set | sub_item_ids_set
    )

    has_missed, order_output_items = get_order_output_items(
        all_need_output_items, combine_item_id_to_item_map, single_item_id_to_item_map
    )

    # 对组合装商品进行下单商品数量的重新计算
    for order_output_item in order_output_items:
        # 如果是组合装，则需要重新计算一下真实的下单商品数量
        if order_output_item.is_combine:
            child_sku_qty = sum([child_sku.qty for child_sku in order_output_item.child_skus])
            if child_sku_qty == 0 or (order_output_item.qty % child_sku_qty) != 0:
                logger.error(
                    "无法正确计算出组合装商品: {} 的下单商品数量, oid: {}, qty: {}, child_sku_qty: {}".format(
                        order_output_item.outer_sku_id, oid, order_output_item.qty, child_sku_qty
                    )
                )
                has_missed = True
                continue
            else:
                # 原 qty 存储的是 num 这个字段的总和。
                order_output_item.qty = order_output_item.qty // child_sku_qty
        output_items.append(order_output_item)

    return has_missed, output_items


def wdt_ulti_query_skus(
    wdt_ulti_sdk: WdtUltiOpenAPIClient,
    *,
    sku_id: str | None = None,
    spu_id: str | None = None,
) -> tuple[dict[str, SkuInfo], str | None]:
    sku_id_to_sku_info_mapping: dict[str, SkuInfo] = {}
    try:
        if sku_id:
            resp = wdt_ulti_sdk.query_skus_by_sku_id(sku_id)
        elif spu_id:
            resp = wdt_ulti_sdk.query_skus_by_spu_id(spu_id)
        else:
            return {}, EMPTY_QUERY_CONDITIONS
        for goods in resp.data.goods_list:
            for spec in goods.spec_list:
                sku_id_to_sku_info_mapping.update(
                    {
                        spec.spec_no: SkuInfo(
                            sku=spec.spec_no,
                            spu=goods.goods_no,
                            name=goods.goods_name,
                            properties=spec.spec_name,
                        )
                    }
                )
    except WdtRequestError as e:
        return {}, ERP_REQUEST_ERROR if not e.message else e.message
    except Exception as e:
        logger.error("旺店通旗舰版查询商品信息失败", e)
        return {}, UNKNOWN_ERROR
    return sku_id_to_sku_info_mapping, None


def wdt_ulti_match_skus_inventories(
    wdt_ulti_client: WdtUltiOpenAPIClient,
    sku_id_to_sku_info_mapping: dict[str, SkuInfo],
    warehouse_no: str | None = None,
) -> tuple[dict[str, SkuInfo], str | None]:
    sku_ids = list(sku_id_to_sku_info_mapping.keys())
    try:
        resp = wdt_ulti_client.query_sku_stocks(sku_ids=sku_ids, warehouse_no=warehouse_no)
        for stock in resp.data.detail_list:
            if sku_info := sku_id_to_sku_info_mapping.get(stock.spec_no):
                sku_info.inventories.append(
                    SkuInventory(
                        sku_id=stock.spec_no,
                        warehouse_name=stock.warehouse_name,
                        qty=stock.stock_num,
                        available_qty=stock.available_send_stock,
                    )
                )
    except WdtQmRateLimitError:
        return sku_id_to_sku_info_mapping, ERP_LIMIT_ERROR
    except WdtRequestError as e:
        return sku_id_to_sku_info_mapping, (ERP_REQUEST_ERROR if not e.message else e.message)
    except Exception as e:
        logger.error("旺店通旗舰版查询库存信息失败", e)
        return sku_id_to_sku_info_mapping, UNKNOWN_ERROR
    return sku_id_to_sku_info_mapping, None


def wdgj_format_order_items(
    orders: List[WdgjOrder],
) -> tuple[set[str], set[str], list[ErpOriginOrderItem]]:
    # 网店管家不支持子订单
    need_query_combine_item_ids_set: set[str] = {
        item.goodsno for order in orders for item in order.goodslist if item.bfit == "True"
    }
    need_query_single_item_ids_set: set[str] = {
        item.goodsno for order in orders for item in order.goodslist if item.bfit == "False"
    }
    need_output_sku_id_to_item_map: dict[str, ErpOriginOrderItem] = {}
    for order in orders:
        for item in order.goodslist:
            if item.bfit == "True":
                need_output_sku_id_to_item_map.update(
                    {
                        item.goodsno: ErpOriginOrderItem(
                            qty=float(item.goodscount or 0),
                            is_combine=True,
                            outer_sku_id=item.goodsno,
                            payment=float(item.sharemoney),
                            tid=item.relationno,
                        )
                    }
                )
            else:
                need_output_sku_id_to_item_map.update(
                    {
                        item.goodsno: ErpOriginOrderItem(
                            qty=float(item.goodscount or 0),
                            is_combine=False,
                            outer_sku_id=item.goodsno,
                            payment=float(item.sharemoney),
                            tid=item.relationno,
                        )
                    }
                )
    return (
        need_query_combine_item_ids_set,
        need_query_single_item_ids_set,
        list(need_output_sku_id_to_item_map.values()),
    )


def wdgj_query_combine_items(
    wdgj_sdk: WdgjQmSDK,
    need_query_combine_sku_ids_set: set[str],
) -> tuple[dict[str, tuple[ErpCommonCombineItem, ErpCommonSpuItem]], set[str]]:
    combine_item_id_to_item_map: dict[str, tuple[ErpCommonCombineItem, ErpCommonSpuItem]] = {}
    sub_item_ids_set: set[str] = set()
    # 查询所有组合商品的最新信息。
    for combine_sku_id in need_query_combine_sku_ids_set:
        combine_skus_resp = wdgj_sdk.query_fit_goods_by_goods_no(combine_sku_id)
        if combine_skus_resp.response.datalist:
            fit_goods = combine_skus_resp.response.datalist[0]
            sub_items = [
                ErpCommonCombineItem.SubItem(outer_sku_id=item.goodsno or "", qty=float(item.goodscount))
                for item in fit_goods.goodslist
                if fit_goods.goodslist
            ]
            combine_item_id_to_item_map.update(
                {
                    fit_goods.goodsno: (
                        ErpCommonCombineItem(
                            outer_sku_id=fit_goods.goodsno or "",
                            outer_sku_short_name=None,
                            outer_sku_name=fit_goods.goodsname,
                            outer_sku_pic_url=None,
                            outer_price=(float(fit_goods.pricedetail) if fit_goods.pricedetail else 0.0),
                            outer_properties=None,
                            sub_items=sub_items,
                        ),
                        ErpCommonSpuItem(
                            outer_spu_id=fit_goods.goodsno or "",
                            outer_spu_name=fit_goods.goodsname,
                            outer_spu_short_name=None,
                        ),
                    )
                }
            )
            for item in fit_goods.goodslist:
                sub_item_ids_set.add(item.goodsno)
    return combine_item_id_to_item_map, sub_item_ids_set


def wdgj_query_single_items(
    wdgj_sdk: WdgjQmSDK,
    need_query_single_sku_ids_set: set[str],
) -> dict[str, tuple[ErpCommonSkuItem, ErpCommonSpuItem]]:
    single_item_id_to_item_map: dict[str, tuple[ErpCommonSkuItem, ErpCommonSpuItem]] = {}
    # 查询所有单品信息。
    for single_sku_id in need_query_single_sku_ids_set:
        single_sku_resp = wdgj_sdk.query_goods_by_goods_no(single_sku_id)
        for single_sku in single_sku_resp.response.datalist:
            single_item_id_to_item_map.update(
                {
                    single_sku.goodsno: (
                        ErpCommonSkuItem(
                            outer_sku_id=single_sku.goodsno,
                            outer_sku_short_name=None,
                            outer_sku_name=single_sku.speclist[0].specname,
                            outer_sku_pic_url=None,
                            outer_price=(float(single_sku.pricedetail) if single_sku.pricedetail else 0.0),
                            outer_properties=None,
                        ),
                        ErpCommonSpuItem(
                            outer_spu_id=single_sku.goodsno or "",
                            outer_spu_name=single_sku.goodsname,
                            outer_spu_short_name=None,
                        ),
                    )
                }
            )
    return single_item_id_to_item_map


def jackyun_format_order_items(
    order: JackyunOrder,
) -> tuple[set[tuple[str, str]], set[tuple[str, str]], list[ErpOriginOrderItem]]:
    # 统计主订单下包含的所有组合商品、普通商品以及需要输出的商品。
    need_query_combine_item_ids_set: set[tuple[str, str]] = {
        (item.goodsNo, item.specName) for item in order.goodsDetail if item.isFit == "1"
    }
    need_query_single_item_ids_set: set[tuple[str, str]] = {
        (item.goodsNo, item.specName) for item in order.goodsDetail if item.isFit == "0"
    }
    need_output_sku_id_to_item_map: dict[str, ErpOriginOrderItem] = {}

    for item in order.goodsDetail:
        if item.goodsNo is not None:
            if item.isFit == "1":
                need_output_sku_id_to_item_map.update(
                    {
                        item.goodsNo: ErpOriginOrderItem(
                            qty=float(item.sellCount or 0),
                            is_combine=True,
                            outer_spu_id=item.goodsNo,
                            outer_sku_id=item.specName,
                            payment=float(item.sellPrice or 0),
                            tid=order.online_trade_no,
                            oid=item.sourceSubtradeNo,
                        )
                    }
                )
            else:
                need_output_sku_id_to_item_map.update(
                    {
                        item.goodsNo: ErpOriginOrderItem(
                            qty=float(item.sellCount or 0),
                            is_combine=False,
                            outer_spu_id=item.goodsNo,
                            outer_sku_id=item.specName,
                            payment=float(item.sellPrice or 0),
                            tid=order.online_trade_no,
                            oid=item.sourceSubtradeNo,
                        )
                    }
                )
    return (
        need_query_combine_item_ids_set,
        need_query_single_item_ids_set,
        list(need_output_sku_id_to_item_map.values()),
    )


def jackyun_query_combine_items(
    jackyun_sdk: JackyunSDK,
    need_query_combine_sku_ids_set: set[tuple[str, str]],
) -> tuple[dict[tuple[str, str], tuple[ErpCommonCombineItem, ErpCommonSpuItem]], set[tuple[str, str]],]:
    combine_item_id_to_item_map: dict[tuple[str, str], tuple[ErpCommonCombineItem, ErpCommonSpuItem]] = {}
    sub_item_ids_set: set[tuple[str, str]] = set[tuple[str, str]]()
    goods_nos = [sku[0] for sku in need_query_combine_sku_ids_set]

    # 查询所有组合商品的最新信息。
    combine_skus_resp = jackyun_sdk.query_combine_skus_by_combine_sku_ids(list(goods_nos))
    for sku in need_query_combine_sku_ids_set:
        goods_no = sku[0]
        sku_name = sku[1]
        for combine_sku in combine_skus_resp.result.data:
            if combine_sku.goods_no == goods_no:
                sub_items = [
                    ErpCommonCombineItem.SubItem(
                        outer_spu_id=item.goods_no,
                        outer_sku_id=item.sku_properites_name,
                        qty=float(item.goods_amount),
                    )
                    for item in combine_sku.goods_package_detail
                    if combine_sku.goods_package_detail
                ]
                retail_prices = combine_sku.retail_price
                if jackyun_sdk.erp_info and jackyun_sdk.erp_info.meta["erp_account"] == "********":
                    retail_prices = 0.0
                combine_item_id_to_item_map.update(
                    {
                        (combine_sku.goods_no, sku_name): (
                            ErpCommonCombineItem(
                                outer_sku_id=sku_name or "",
                                outer_sku_short_name=None,
                                outer_sku_name=sku_name,
                                outer_sku_pic_url=None,
                                outer_price=retail_prices,
                                outer_properties=None,
                                sub_items=sub_items,
                            ),
                            ErpCommonSpuItem(
                                outer_spu_id=combine_sku.goods_no or "",
                                outer_spu_name=combine_sku.goods_name,
                                outer_spu_short_name=None,
                            ),
                        )
                    }
                )
                # 将组合装下属的单品的 sku_id 放入需要查询的普通商品中。
                for item in combine_sku.goods_package_detail:
                    if item.goods_no:
                        sub_item_ids_set.add((str(item.goods_no), item.sku_properites_name))

    return combine_item_id_to_item_map, sub_item_ids_set


def jackyun_query_single_items(
    jackyun_sdk: JackyunSDK,
    need_query_single_sku_ids_set: set[tuple[str, str]],
) -> dict[tuple[str, str], tuple[ErpCommonSkuItem, ErpCommonSpuItem]]:
    single_item_id_to_item_map: dict[tuple[str, str], tuple[ErpCommonSkuItem, ErpCommonSpuItem]] = {}

    # 查询所有单品信息。
    for goods_no, sku_name in need_query_single_sku_ids_set:
        single_sku_resp = jackyun_sdk.query_skus_by_sku_id(goods_no)
        for goods in single_sku_resp.result.data.goods:
            if goods.goodsNo == goods_no and goods.skuName == sku_name:
                retail_prices = goods.retail_price
                if jackyun_sdk.erp_info and jackyun_sdk.erp_info.meta["erp_account"] == "********":
                    retail_prices = 0.0
                single_item_id_to_item_map.update(
                    {
                        (goods.goodsNo, goods.skuName): (
                            ErpCommonSkuItem(
                                outer_sku_id=goods.skuName or "",
                                outer_sku_short_name=None,
                                outer_sku_name=goods.skuName,
                                outer_sku_pic_url=goods.get_sku_img_url(),
                                outer_price=retail_prices,
                                outer_properties=None,
                            ),
                            ErpCommonSpuItem(
                                outer_spu_id=goods.goodsNo or "",
                                outer_spu_name=goods.goodsName,
                                outer_spu_short_name=None,
                            ),
                        )
                    }
                )
    return single_item_id_to_item_map


def jackyun_get_order_skus(
    jackyun_qm_sdk: JackyunQmSDK,
    jackyun_sdk: JackyunSDK,
    tid: str,
    oid: str | None,
) -> tuple[bool, list[OrderSkuInfo] | None]:
    current_orders = jackyun_qm_sdk.order_list(tid).response.jackyunData.trades
    if len(current_orders) == 0:
        return True, None
    if oid:
        for current_order in current_orders:
            current_order.goodsDetail = [goods for goods in current_order.goodsDetail if goods.sourceSubtradeNo == oid]
    sorted_current_orders = sorted(current_orders, key=lambda trade: int(trade.tradeId))
    main_order = sorted_current_orders[0]

    (
        need_query_combine_item_ids_set,
        need_query_single_item_ids_set,
        need_output_items,
    ) = jackyun_format_order_items(main_order)

    if need_query_combine_item_ids_set:
        combine_item_id_to_item_map, sub_item_ids_set = jackyun_query_combine_items(
            jackyun_sdk, need_query_combine_item_ids_set
        )
    else:
        combine_item_id_to_item_map, sub_item_ids_set = {}, set()

    if single_item_ids_set := (need_query_single_item_ids_set | sub_item_ids_set):
        single_item_id_to_item_map = jackyun_query_single_items(jackyun_sdk, single_item_ids_set)
    else:
        single_item_id_to_item_map = {}

    return get_order_output_items_for_jackyun(
        need_output_items, combine_item_id_to_item_map, single_item_id_to_item_map
    )


def wdgj_get_order_skus(
    wdgj_sdk: WdgjQmSDK,
    tid: str,
    oid: str | None,
) -> tuple[bool, list[OrderSkuInfo] | None]:
    current_orders = wdgj_sdk.query_trades(
        {
            "pageno": "1",
            "pagesize": "10",
            "operationtype": "零售业务",
            "searchtype": "0",
            "relationno": tid,
        }
    )
    if len(current_orders) == 0:
        return True, None
    # 网店管家不支持oid
    (
        need_query_combine_item_ids_set,
        need_query_single_item_ids_set,
        need_output_items,
    ) = wdgj_format_order_items(current_orders)
    if need_query_combine_item_ids_set:
        combine_item_id_to_item_map, sub_item_ids_set = wdgj_query_combine_items(
            wdgj_sdk, need_query_combine_item_ids_set
        )
    else:
        combine_item_id_to_item_map, sub_item_ids_set = {}, set()
    if single_item_ids_set := (need_query_single_item_ids_set | sub_item_ids_set):
        single_item_id_to_item_map = wdgj_query_single_items(wdgj_sdk, single_item_ids_set)
    else:
        single_item_id_to_item_map = {}
    return get_order_output_items(need_output_items, combine_item_id_to_item_map, single_item_id_to_item_map)


def get_order_output_items_for_jackyun(
    need_output_items: list[ErpOriginOrderItem],
    combine_item_id_to_item_map: dict[tuple[str, str], tuple[ErpCommonCombineItem, ErpCommonSpuItem]],
    single_item_id_to_item_map: dict[tuple[str, str], tuple[ErpCommonSkuItem, ErpCommonSpuItem]],
) -> tuple[bool, list[OrderSkuInfo]]:
    output_items: dict[tuple[str, str], OrderSkuInfo] = dict[tuple[str, str], OrderSkuInfo]()
    has_missed: bool = False
    for item in need_output_items:
        if saved_item := output_items.get((item.outer_spu_id, item.outer_sku_id)):  # type: ignore[arg-type]
            saved_item.qty += item.qty
        else:
            if item.is_combine:
                combine_item = combine_item_id_to_item_map.get((unwrap_optional(item.outer_spu_id), item.outer_sku_id))
                if combine_item is None:
                    has_missed = True
                    continue
                combine_sku_info = combine_item[0]
                combine_spu_info = combine_item[1]
                child_skus = []
                is_full = True
                for sub_item in combine_sku_info.sub_items:
                    single_item = single_item_id_to_item_map.get(
                        (unwrap_optional(sub_item.outer_spu_id), sub_item.outer_sku_id)
                    )
                    if single_item is None:
                        is_full = False
                        continue
                    single_sku_info = single_item[0]
                    single_spu_info = single_item[1]
                    child_skus.append(
                        OrderSkuInfo(
                            outer_sku_id=single_sku_info.outer_sku_id,
                            outer_spu_id=single_spu_info.outer_spu_id,
                            sku_id="",
                            spu_id="",
                            qty=sub_item.qty,
                            is_combine=False,
                            outer_properties=single_sku_info.outer_properties,
                            outer_sku_short_name=single_sku_info.outer_sku_short_name,
                            outer_spu_short_name=single_spu_info.outer_spu_short_name,
                            outer_sku_name=single_sku_info.outer_sku_name,
                            outer_spu_name=single_spu_info.outer_spu_name,
                            outer_sku_picture_url=single_sku_info.outer_sku_pic_url,
                            outer_spu_picture_url=single_spu_info.outer_spu_picture_url,
                            outer_price=single_sku_info.outer_price,
                            outer_cost_price=single_sku_info.outer_cost_price,
                        )
                    )
                saved_item = OrderSkuInfo(
                    outer_sku_id=combine_sku_info.outer_sku_id,
                    outer_spu_id=combine_spu_info.outer_spu_id,
                    sku_id="",
                    spu_id="",
                    qty=item.qty,
                    is_combine=True,
                    child_skus=child_skus,
                    is_full=is_full,
                    tid=item.tid,
                    oid=item.oid,
                    payment=item.payment,
                    outer_properties=combine_sku_info.outer_properties,
                    outer_sku_short_name=combine_sku_info.outer_sku_short_name,
                    outer_spu_short_name=combine_spu_info.outer_spu_short_name,
                    outer_sku_name=combine_sku_info.outer_sku_name,
                    outer_spu_name=combine_spu_info.outer_spu_name,
                    outer_sku_picture_url=combine_sku_info.outer_sku_pic_url,
                    outer_spu_picture_url=combine_spu_info.outer_spu_picture_url,
                    outer_price=combine_sku_info.outer_price,
                    outer_cost_price=combine_sku_info.outer_cost_price,
                )
            else:
                single_item = single_item_id_to_item_map.get(
                    (item.outer_spu_id, item.outer_sku_id)  # type: ignore[arg-type]
                )
                if single_item is None:
                    has_missed = True
                    continue
                single_sku_info = single_item[0]
                single_spu_info = single_item[1]
                saved_item = OrderSkuInfo(
                    outer_sku_id=single_sku_info.outer_sku_id,
                    outer_spu_id=single_spu_info.outer_spu_id,
                    sku_id="",
                    spu_id="",
                    qty=item.qty,
                    is_combine=False,
                    tid=item.tid,
                    oid=item.oid,
                    payment=item.payment,
                    outer_properties=single_sku_info.outer_properties,
                    outer_sku_short_name=single_sku_info.outer_sku_short_name,
                    outer_spu_short_name=single_spu_info.outer_spu_short_name,
                    outer_sku_name=single_sku_info.outer_sku_name,
                    outer_spu_name=single_spu_info.outer_spu_name,
                    outer_sku_picture_url=single_sku_info.outer_sku_pic_url,
                    outer_spu_picture_url=single_spu_info.outer_spu_picture_url,
                    outer_price=single_sku_info.outer_price,
                    outer_cost_price=single_sku_info.outer_cost_price,
                )
        output_items.update({(item.outer_spu_id, item.outer_sku_id): saved_item})  # type: ignore[dict-item]
    return has_missed, list(output_items.values())


def get_order_output_items(
    need_output_items: list[ErpOriginOrderItem],
    combine_item_id_to_item_map: dict[str, tuple[ErpCommonCombineItem, ErpCommonSpuItem]],
    single_item_id_to_item_map: dict[str, tuple[ErpCommonSkuItem, ErpCommonSpuItem]],
    merge_sku: bool = True,  # 新增参数，默认为True
) -> tuple[bool, list[OrderSkuInfo]]:
    output_items: list[OrderSkuInfo] = []  # 使用列表存储OrderSkuInfo对象
    has_missed: bool = False
    for item in need_output_items:
        if merge_sku:
            # 检查是否已经存在具有相同outer_sku_id的OrderSkuInfo对象
            existing_item = next(
                (oi for oi in output_items if oi.outer_sku_id == item.outer_sku_id),
                None,
            )
            if existing_item:
                existing_item.qty += item.qty
                continue

        if item.is_combine:
            combine_item = combine_item_id_to_item_map.get(item.outer_sku_id)
            if combine_item is None:
                has_missed = True
                continue
            combine_sku_info = combine_item[0]
            combine_spu_info = combine_item[1]
            child_skus = []
            is_full = True
            for sub_item in combine_sku_info.sub_items:
                single_item = single_item_id_to_item_map.get(sub_item.outer_sku_id)
                if single_item is None:
                    is_full = False
                    continue
                single_sku_info = single_item[0]
                single_spu_info = single_item[1]
                child_skus.append(
                    OrderSkuInfo(
                        outer_sku_id=single_sku_info.outer_sku_id,
                        outer_spu_id=single_spu_info.outer_spu_id,
                        sku_id="",
                        spu_id="",
                        qty=sub_item.qty,
                        is_combine=False,
                        outer_properties=single_sku_info.outer_properties,
                        outer_sku_short_name=single_sku_info.outer_sku_short_name,
                        outer_spu_short_name=single_spu_info.outer_spu_short_name,
                        outer_sku_name=single_sku_info.outer_sku_name,
                        outer_spu_name=single_spu_info.outer_spu_name,
                        outer_sku_picture_url=single_sku_info.outer_sku_pic_url,
                        outer_spu_picture_url=single_spu_info.outer_spu_picture_url,
                        outer_price=single_sku_info.outer_price,
                        outer_cost_price=single_sku_info.outer_cost_price,
                    )
                )
            saved_item = OrderSkuInfo(
                outer_sku_id=combine_sku_info.outer_sku_id,
                outer_spu_id=combine_spu_info.outer_spu_id,
                sku_id="",
                spu_id="",
                qty=item.qty,
                is_combine=True,
                child_skus=child_skus,
                is_full=is_full,
                tid=item.tid,
                oid=item.oid,
                payment=item.payment,
                outer_properties=combine_sku_info.outer_properties,
                outer_sku_short_name=combine_sku_info.outer_sku_short_name,
                outer_spu_short_name=combine_spu_info.outer_spu_short_name,
                outer_sku_name=combine_sku_info.outer_sku_name,
                outer_spu_name=combine_spu_info.outer_spu_name,
                outer_sku_picture_url=combine_sku_info.outer_sku_pic_url,
                outer_spu_picture_url=combine_spu_info.outer_spu_picture_url,
                outer_price=combine_sku_info.outer_price,
                outer_cost_price=combine_sku_info.outer_cost_price,
            )
        else:
            single_item = single_item_id_to_item_map.get(item.outer_sku_id)
            if single_item is None:
                has_missed = True
                continue
            single_sku_info = single_item[0]
            single_spu_info = single_item[1]
            saved_item = OrderSkuInfo(
                outer_sku_id=single_sku_info.outer_sku_id,
                outer_spu_id=single_spu_info.outer_spu_id,
                sku_id="",
                spu_id="",
                qty=item.qty,
                is_combine=False,
                tid=item.tid,
                oid=item.oid,
                payment=item.payment,
                outer_properties=single_sku_info.outer_properties,
                outer_sku_short_name=single_sku_info.outer_sku_short_name,
                outer_spu_short_name=single_spu_info.outer_spu_short_name,
                outer_sku_name=single_sku_info.outer_sku_name,
                outer_spu_name=single_spu_info.outer_spu_name,
                outer_sku_picture_url=single_sku_info.outer_sku_pic_url,
                outer_spu_picture_url=single_spu_info.outer_spu_picture_url,
                outer_price=single_sku_info.outer_price,
                outer_cost_price=single_sku_info.outer_cost_price,
            )
        output_items.append(saved_item)  # 将新的OrderSkuInfo对象添加到列表中
    return has_missed, output_items
