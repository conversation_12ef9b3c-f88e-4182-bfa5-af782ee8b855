from enum import StrEnum
from typing import Optional

from pydantic import BaseModel
from pydantic import Field


class SkuInventory(BaseModel):
    """
    sku 库存信息
    """

    sku_id: str
    warehouse_name: str
    qty: int
    # 可发库存，需要计算得出
    available_qty: int


class SkuInventoryResponseData(BaseModel):
    inventories: list[SkuInventory]


class ErpSkuQueryMethod(StrEnum):
    """
    ERP 查询 SKU 的方式
    """

    SKU = "SKU"
    SPU = "SPU"
    NAME = "NAME"


class ErpSkuInventoriesQueryParams(BaseModel):
    """
    查询 sku 库存信息的 query 参数
    """

    class Warehouse(BaseModel):
        warehouse_code: str
        warehouse_name: str

    query_method: ErpSkuQueryMethod
    keyword: str
    warehouse: Warehouse | None = None


class QuerySkuSourceType(StrEnum):
    ERP = "erp"
    PLATFORM = "platform"


class ErpSkuQueryParams(BaseModel):
    class Trade(BaseModel):
        tid: str
        oid: str | None

    trades: list[Trade]
    sid: str
    query_source: QuerySkuSourceType

    def get_final_trades(self) -> list[Trade]:
        final_trades: list[ErpSkuQueryParams.Trade] = []

        tid_to_oids_map: dict[str, list[None | str]] = {}
        for trade in self.trades:
            oids = tid_to_oids_map.get(trade.tid) or []
            oids.append(trade.oid)
            tid_to_oids_map.update({trade.tid: oids})
        for tid, oids in tid_to_oids_map.items():
            if None in oids:
                final_trades.append(ErpSkuQueryParams.Trade(tid=tid))
                continue
            else:
                for oid in oids:
                    final_trades.append(
                        ErpSkuQueryParams.Trade(
                            tid=tid,
                            oid=oid,
                        )
                    )

        return final_trades


class SkuInfo(BaseModel):
    """
    SKU 信息。
    """

    sku: str
    spu: str
    name: str
    properties: str
    # 库存信息
    inventories: list[SkuInventory] = Field(default_factory=list)


class ErpSkuInventoriesResponseData(BaseModel):
    sku_infos: list[SkuInfo]
    is_completed: bool = True
    reason: str = ""


class ErpOriginOrderItem(BaseModel):
    qty: float = Field(description="订单内所表明的该商品的下单数量")
    is_combine: bool = Field(description="该商品是否为组合装")
    outer_spu_id: str | None = Field(description="")
    outer_sku_id: str = Field(description="")
    payment: float | None = Field(description="订单中记录的实付金额")
    tid: str | None = Field(description="订单号")
    oid: str | None = Field(description="子订单号")


class ErpCommonSkuItem(BaseModel):
    outer_sku_id: str
    outer_sku_short_name: str | None
    outer_sku_name: str | None
    outer_sku_pic_url: str | None
    outer_price: float | None
    outer_properties: str | None
    outer_cost_price: float | None = Field(description="成本价")

    qty: float = 1.0


class ErpCommonSpuItem(BaseModel):
    outer_spu_id: str
    outer_spu_name: str | None
    outer_spu_short_name: str | None
    outer_spu_picture_url: str | None


class ErpCommonCombineItem(ErpCommonSkuItem):
    class SubItem(BaseModel):
        outer_spu_id: Optional[str]
        outer_sku_id: str
        qty: float

    sub_items: list[SubItem] = Field(default_factory=list)


class OrderSkuInfo(BaseModel):
    outer_sku_id: str = Field("", description="ERP 商品编码")
    outer_spu_id: str = Field("", description="ERP 货品编码")
    sku_id: str = Field("", description="平台商品编码")
    spu_id: str = Field("", description="平台货品编码")
    qty: float = Field(description="数量")
    is_combine: bool = Field(False, description="是否为组合商品")
    child_skus: list["OrderSkuInfo"] = Field(default_factory=list, description="子商品")
    is_full: bool = Field(True, description="用于表示组合商品是否抓取完整")

    tid: str | None = Field(description="所属订单")
    oid: str | None = Field(description="所属子订单")
    payment: float | None = Field(description="商品实付金额")

    outer_properties: str | None = Field(description="ERP 商品规格")
    outer_sku_short_name: str | None = Field(description="ERP 商品简称")
    outer_spu_short_name: str | None = Field(description="ERP 货品简称")
    outer_sku_name: str | None = Field(description="ERP 商品名称")
    outer_spu_name: str | None = Field(description="ERP 货品名称")
    outer_sku_picture_url: str | None = Field(description="ERP 商品图片链接")
    outer_spu_picture_url: str | None = Field(description="ERP 货品图片链接")
    outer_price: float | None = Field(description="ERP 商品价格")
    outer_cost_price: float | None = Field(description="ERP 商品成本价")

    properties: str | None = Field(description="平台商品规格")
    sku_short_name: str | None = Field(description="平台商品简称")
    spu_short_name: str | None = Field(description="平台货品简称")
    sku_name: str | None = Field(description="平台商品名称")
    spu_name: str | None = Field(description="平台货品名称")
    sku_picture_url: str | None = Field(description="平台商品图片链接")
    spu_picture_url: str | None = Field(description="平台货品图片链接")
    price: float | None = Field(description="平台商品价格")


class ErpSkuResponseData(BaseModel):
    class Order(BaseModel):
        tid: str
        oid: str | None
        products: list[OrderSkuInfo]
        has_missed: bool = False

    trades: list[Order] = Field(default_factory=list)


OrderSkuInfo.update_forward_refs()
