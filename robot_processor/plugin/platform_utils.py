from decimal import Decimal

from google.protobuf.json_format import MessageToDict
from leyan_proto.digismart.trade.dgt_jd_trade_pb2 import JdTradeInfo

from robot_processor.client import trade_client
from robot_processor.enums import ShopPlatform
from robot_processor.plugin.erp_schemas import OrderSkuInfo


def taobao_get_order_skus(
        tid: str,
        sid: str,
        oid: str | None = None,
) -> tuple[bool, list[OrderSkuInfo] | None]:
    trade = trade_client.get_trade_by_tid(sid, tid)
    if not trade.trade_id:
        return True, None

    orders = trade.orders

    if len(orders) == 0:
        return True, None

    output_skus: dict[str, OrderSkuInfo] = {}

    for item in orders:
        if oid:
            if item.oid != oid:
                continue
        item_dict = MessageToDict(item)
        if saved_sku := output_skus.get(item.sku_id):
            saved_sku.qty += item.quantity
        else:
            # todo(39): 确认取值逻辑是否正确
            saved_sku = OrderSkuInfo(
                outer_sku_id=item.outer_sku_id,
                outer_spu_id=item.outer_spu_id,
                sku_id=item.sku_id,
                spu_id=item.spu_id,
                qty=item.quantity,
                properties=item.sku_description,
                tid=tid,
                oid=item.oid,
                sku_short_name=None,
                spu_short_name=None,
                sku_name=None,
                spu_name=None,
                sku_picture_url=item.pic_path,
                spu_picture_url=None,
                price=item_dict.get("price"),
                payment=item_dict.get("payment"),
            )
        output_skus.update({
            item.sku_id: saved_sku
        })

    return True, list(output_skus.values())


def pdd_get_order_skus(
        tid: str,
        oid: str | None = None,
) -> tuple[bool, list[OrderSkuInfo] | None]:
    orders_resp = trade_client.get_trade_by_tid_and_channel(
        [tid]
    )
    current_orders = [trade_info.pdd_trade_info for trade_info in
                      orders_resp.trade_info_list]

    if len(current_orders) == 0:
        return True, None

    assert len(current_orders) == 1

    main_order = current_orders[0]
    output_skus: dict[str, OrderSkuInfo] = {}

    for item in main_order.item_list:
        # 拼多多没有oid的概念
        item_dict = MessageToDict(item)
        if saved_sku := output_skus.get(item.sku_id):
            saved_sku.qty += item.goods_count
        else:
            # todo(39): 确认取值逻辑是否正确
            saved_sku = OrderSkuInfo(
                outer_sku_id=item.outer_id,
                outer_spu_id=item.outer_goods_id,
                sku_id=item.sku_id,
                spu_id=item.goods_id,
                qty=item.goods_count,
                properties=item.goods_spec,
                tid=main_order.trade_id,
                oid=main_order.trade_id,
                sku_short_name=None,
                spu_short_name=None,
                sku_name=None,
                spu_name=item.goods_name,
                sku_picture_url=None,
                spu_picture_url=None,
                price=item_dict.get("goods_price"),
            )
        output_skus.update({
            item.sku_id: saved_sku
        })

    return True, list(output_skus.values())


def dy_get_order_skus(
        tid: str,
        oid: str | None,
) -> tuple[bool, list[OrderSkuInfo] | None]:
    orders_resp = trade_client.get_trade_by_tid_and_channel(
        [tid], ShopPlatform.DOUDIAN.value
    )
    current_orders = [trade_info.dy_trade_info for trade_info in
                      orders_resp.trade_info_list]

    if len(current_orders) == 0:
        return True, None

    sorted_current_orders = sorted(current_orders,
                                   key=lambda trade: trade.order_id)
    main_order = sorted_current_orders[0]
    output_skus: dict[str, OrderSkuInfo] = {}

    for item in main_order.sku_order_list:
        if oid:
            if item.sku_order_id != oid:
                continue
        if saved_sku := output_skus.get(item.sku_id):
            saved_sku.qty += item.item_num
        else:
            # todo(39): 确认取值逻辑是否正确
            saved_sku = OrderSkuInfo(
                outer_sku_id=item.out_sku_id,
                outer_spu_id=item.out_product_id,
                sku_id=item.sku_id,
                spu_id=item.product_id,
                qty=item.item_num,
                properties=None,
                tid=item.parent_order_id,
                oid=item.order_id,
                sku_short_name=None,
                spu_short_name=None,
                sku_name=None,
                spu_name=item.product_name,
                sku_picture_url=None,
                spu_picture_url=None,
                price=float(Decimal(item.goods_price) / 100),
            )
        output_skus.update({
            item.sku_id: saved_sku
        })

    return True, list(output_skus.values())


def alibaba_get_order_skus(
        tid: str,
        oid: str | None,
        org_id: str | None,
) -> tuple[bool, list[OrderSkuInfo] | None]:
    orders_resp = trade_client.get_trade_by_tid_and_channel(
        [tid], ShopPlatform.ALIBABA.value, org_id=org_id
    )
    current_orders = [trade_info.alibaba_trade_info for
                      trade_info in
                      orders_resp.trade_info_list]

    if len(current_orders) == 0:
        return True, None

    assert len(current_orders) == 1
    main_order = current_orders[0]
    output_skus: dict[str, OrderSkuInfo] = {}

    for item in main_order.product_items:
        if oid:
            if item.sub_item_id != oid:
                continue
        if saved_sku := output_skus.get(item.sku_id):
            saved_sku.qty += item.item_num
        else:
            # todo(39): 确认取值逻辑是否正确
            saved_sku = OrderSkuInfo(
                outer_sku_id=item.cargo_number,
                outer_spu_id="",
                sku_id=item.sku_id,
                spu_id=item.product_id,
                qty=item.quantity,
                properties=None,
                tid=tid,
                oid=item.sub_item_id,
                sku_short_name=None,
                spu_short_name=None,
                sku_name=None,
                spu_name=item.name,
                sku_picture_url=None,
                spu_picture_url=None,
                price=item.price
            )
        output_skus.update({
            item.sku_id: saved_sku
        })

    return True, list(output_skus.values())


def jd_get_order_skus(
    tid: str,
    sid: str,
) -> tuple[bool, list[OrderSkuInfo] | None]:
    orders_resp = trade_client.get_trade_by_tid_and_channel(
        tids=[tid],
        channel_type=ShopPlatform.JD.value,
        sid=sid,
    )
    current_orders: list[JdTradeInfo] = [
        trade_info.jd_trade_info
        for trade_info in orders_resp.trade_info_list
    ]

    if len(current_orders) == 0:
        return True, None

    sorted_current_orders = sorted(
        current_orders,
        key=lambda trade: trade.tid
    )
    main_order = sorted_current_orders[0]
    output_skus: dict[str, OrderSkuInfo] = {}

    for item in main_order.jd_items:
        if saved_sku := output_skus.get(item.sku_id):
            saved_sku.qty += float(item.qty)
        else:
            saved_sku = OrderSkuInfo(
                outer_sku_id=item.outer_sku_id,
                outer_spu_id="",
                sku_id=item.sku_id,
                spu_id=item.ware_id,
                qty=float(item.qty),
                properties=item.property,
                tid=item.tid,
                oid=None,
                sku_short_name=None,
                spu_short_name=None,
                sku_name=None,
                spu_name=None,
                sku_picture_url=None,
                spu_picture_url=None,
                price=float(Decimal(item.jd_price)),
            )
        output_skus.update({
            item.sku_id: saved_sku
        })

    return True, list(output_skus.values())
