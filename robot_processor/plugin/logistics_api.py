from flask import Blueprint, jsonify
from robot_processor.validator import validate

from robot_processor.client import logistics_client
from robot_processor.currents import g
from robot_processor.decorators import org_required, shop_required
from robot_processor.plugin.logistics_schema import LogisticsQuerySchema

api = Blueprint("logistics-plugin-api", __name__)


@api.before_request
@org_required
def logistics_before_request():
    pass


@api.route("/logistics", methods=["POST"])
@shop_required
@validate
def get_logistics_list(body: LogisticsQuerySchema):
    """获取物流列表"""
    record = g.shop.get_recent_record()
    if not record or not record.access_token:
        return jsonify(logistics_list=[], reason="缺失授权记录"), 400
    token: str = record.access_token
    if not body.buyer_nick and not body.trade_ids:
        return jsonify(reason="参数缺失"), 400
    logistics_list = logistics_client.get_logistics_list(
        token, body.buyer_nick, body.trade_ids,
        body.page_no, body.page_size)
    return jsonify(logistics_list)
