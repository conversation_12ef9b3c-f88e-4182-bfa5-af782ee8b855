from enum import StrEnum
from typing import List
from typing import Optional

from pydantic import BaseModel
from pydantic import Field
from pydantic import root_validator

from robot_processor.enums import QueryErpItemsType
from robot_processor.symbol_table.models import TypeSpec
from robot_processor.symbol_table.models import Value


class UpdateMemo(BaseModel):
    content: str
    memo_flag: bool
    flag: int


class HighLevelSearchItemSchema(BaseModel):
    # 在这份schema中，四个id都有可能以逗号分隔，来提供批量查询的能力
    sku: str = ""
    spu: str = ""
    outer_spu: str = ""
    outer_sku: str = ""
    title: str = ""
    sku_props: str = ""


class Spu(BaseModel):
    query_param: str = ""
    source: str = "platform"
    page_no: int = 1
    page_size: int = 30

    # 兼容侧边栏。。
    sku: Optional[str]
    spu: Optional[str]
    outer_spu: Optional[str]
    outer_sku: Optional[str]
    title: Optional[str]
    sku_props: Optional[str]
    query_entity: Optional[HighLevelSearchItemSchema]
    query_type: str = ""

    @root_validator(pre=True)
    def pre_fill(cls, values):
        values["query_entity"] = HighLevelSearchItemSchema(
            sku=values.get("sku", ""),
            spu=values.get("spu", ""),
            outer_spu=values.get("outer_spu", ""),
            outer_sku=values.get("outer_sku", ""),
            title=values.get("title", ""),
            sku_props=values.get("sku_props", ""),
        )
        return values


class RatesSchema(BaseModel):
    trade_ids: List[str]


class HistorySchema(BaseModel):
    contact: Optional[str]
    count: int
    buyer_open_uid: Optional[str]
    ccode: Optional[str]  # 千牛的对话id


class PlatformItemSchema(BaseModel):
    sku_id: Optional[str]
    spu_id: Optional[str]
    outer_spu_id: Optional[str]
    outer_sku_id: Optional[str]
    item_is_single_flag: Optional[bool] = False


class OperatorItem(BaseModel):
    label: str
    expect: TypeSpec
    operator: str


class Condition(BaseModel):
    a: Value
    o: list[OperatorItem]
    b: Value | None


class ErpItemFilter(BaseModel):
    enable: bool = False
    conditions: list[Condition]
    relation: str


class ErpSkuListSchema(BaseModel):
    query_entity: Optional[HighLevelSearchItemSchema]
    # todo 侧边栏不会主动刷新，所以schema等上线之后再清理一次，以下四个字段都得清理
    items: List[PlatformItemSchema] = Field(default_factory=list)
    keywords: str = ""
    sku_ids: str = ""
    outer_sku_ids: str = ""
    page_no: int
    page_size: int
    query_type: str = QueryErpItemsType.FUZZY.value
    filter: ErpItemFilter | None


class ErpItemSkuListSchema(BaseModel):

    channel_no: Optional[str]  # 如果 HTTP 请求中没有提供，则从 jwt token 中获取
    channel_type: Optional[str]  # 如果 HTTP 请求中没有提供，则从 jwt token 中获取
    # todo 待删除
    items: List[PlatformItemSchema] = Field(default_factory=list)
    keywords: str = ""
    # 待废弃
    sku_ids: str = ""
    # 待废弃
    # 用户主动检索、提供更多的搜选项
    query_entity: Optional[HighLevelSearchItemSchema]
    outer_sku_ids: str = ""
    page_no: int
    page_size: int
    query_type: Optional[str] = QueryErpItemsType.FUZZY.value
    filter: ErpItemFilter | None


class ImageCompressAckRequest(BaseModel):
    url: str


class ImageCompressAckResponse(BaseModel):
    ack: bool
    success: bool = True


class AuthEnableCheckRequest(BaseModel):
    rpa_id: Optional[int]
    step_id: Optional[int]


class TradesSort(StrEnum):
    CREATED_AT_DESC = "CREATEDESC"
    CREATED_AT_ASC = "CREATEASC"
