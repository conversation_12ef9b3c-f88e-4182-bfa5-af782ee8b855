import json
from typing import List
from typing import Optional

import arrow
from flask import jsonify
from flask import request
from flask.globals import current_app as app
from google.protobuf.json_format import MessageToDict
from leyan_proto.digismart.dgt_common_pb2 import DgtResponseCode
from leyan_proto.digismart.trade.dgt_trade_common_pb2 import TradeChannelInfo
from leyan_proto.digismart.trade.dgt_trade_pb2 import API_SUCCESS
from loguru import logger
from pydantic import BaseModel
from result import Err
from result import Ok

from robot_processor.client import app_config
from robot_processor.client import buyer_client
from robot_processor.client import doudian_cloud
from robot_processor.client import get_buyer_nick
from robot_processor.client import hyws_taobao_client
from robot_processor.client import trade_client
from robot_processor.client.errors import DoudianCloudServiceError
from robot_processor.client.errors import PddServiceError
from robot_processor.currents import g
from robot_processor.decorators import org_required
from robot_processor.decorators import shop_required
from robot_processor.enums import ErpType
from robot_processor.plugin.schema import RatesSchema
from robot_processor.plugin.schema import TradesSort
from robot_processor.plugin.schema import UpdateMemo
from robot_processor.shop.models import App
from robot_processor.shop.models import ErpInfo
from robot_processor.shop.models import GrantRecord
from robot_processor.shop.models import Shop
from robot_processor.utils import is_buyer_nick_encrypt
from robot_processor.validator import validate
from rpa.doudian import doudian_client
from rpa.erp.duohong import DuohongClient
from rpa.erp.jst import JstNewSDK
from rpa.erp.jst import JstQmSDK
from rpa.erp.jst import JstSDK
from rpa.erp.wdt import WdtClient
from rpa.erp.wdtulti import QueryTradeLogReq
from rpa.erp.wdtulti import WdtUltiOpenAPIClient
from rpa.erp.wdtulti import WdtUltiQM
from rpa.mola import MolaClient
from rpa.pdd.trade_info import get_decrypted_receiver_info

from ..job.jst_trade import TradesFinder
from ..job.smartcall import SmartCallExecutor
from . import plugin_api
from .api import get_sid
from .trade_utils import ErpTradeManager
from .trade_utils import PlatformTradeManager


class GetShopInfoRequest(BaseModel):
    tid: str


class GetShopInfoRequestV2(BaseModel):
    tid: Optional[str]
    refund_id: Optional[str]
    logistics_no: Optional[str]
    return_logistics_no: Optional[str]


@plugin_api.get("/shops/info/query-by-trade")
@plugin_api.get("/shops/info/by-trade")
@org_required
@validate
def get_shop_info_by_tid(query: GetShopInfoRequest):
    from leyan_proto.digismart.dgt_common_pb2 import ChannelType

    from robot_processor.client import trade_client

    org_id_list = app.config.get("SEARCH_SHOP_BY_ERP", [])
    if g.org_id in org_id_list:
        for erp_info in ErpInfo.get_distinct_erp_by_org_id(g.org_id):
            credential_res = erp_info.get_credential()
            shop: None | Shop = None
            match erp_info.erp_type:
                case ErpType.JST:
                    jst = JstQmSDK(credential=credential_res.expect(""))
                    jst_orders = (
                        jst.query_orders({"so_ids": query.tid}).orders
                        or jst.query_orders({"so_ids": query.tid, "archive": "true"}).orders
                    )
                    if not jst_orders:
                        continue
                    shop = ErpTradeManager.detect_shop(int(g.org_id), erp_info, jst_orders[0])
                case ErpType.WDT:
                    wdt = WdtClient(credential=credential_res.expect(""))
                    if not (wdt_orders := wdt.trade_query(query.tid).response.trades):
                        continue
                    shop = ErpTradeManager.detect_shop(int(g.org_id), erp_info, wdt_orders[0])
                case ErpType.WDTULTI:
                    wdtulti = WdtUltiQM(erp_info.shop.sid)
                    if not (wdtulti_orders := wdtulti.get_orders(query.tid).order):
                        continue
                    shop = ErpTradeManager.detect_shop(int(g.org_id), erp_info, wdtulti_orders[0])
            if shop is not None:
                return dict(succeed=True, error=None, data=dict(shop_sid=shop.sid, shop_platform=shop.platform.lower()))

    # ERP如果找不到也可以通过平台订单再找一下
    res = trade_client.get_channel_info_by_tid_list([query.tid], g.org_id)
    # TODO(<EMAIL>): 快手的逻辑是错误的.
    if not res:
        # 京东目前只有RPA登录，需要遍历全部登录的RPA店铺，所以在其他平台都无效的情况下，再来兜底判断京东.
        jd_shops = Shop.query.filter_by(org_id=g.org_id, platform="JD").all()
        for jd_shop in jd_shops:
            mola_res = MolaClient(jd_shop.sid).jd_query_trade(query.tid, check_rate_limit=False)
            match mola_res:
                case Err(_):
                    pass
                case Ok(result):
                    if result:
                        res = [
                            TradeChannelInfo(
                                channel_type=ChannelType.JD,
                                channel_no=jd_shop.sid,
                                channel_name=jd_shop.title,  # type: ignore[arg-type]
                            )
                        ]
                        break
        if not res:
            return dict(succeed=False, error="无匹配店铺", data=None)
    channel_info = res[0]
    # 淘宝只有 seller nick
    if not channel_info.channel_no and channel_info.seller_nick:
        shop = Shop.query.filter(
            Shop.platform.in_(["TAOBAO", "TMALL"]),
            Shop.nick == channel_info.seller_nick,
        ).first()
        sid = shop.sid if shop else ""
    else:
        sid = channel_info.channel_no
    return dict(
        succeed=True,
        error=None,
        data=dict(shop_sid=sid, shop_platform=ChannelType.Name(channel_info.channel_type)),
    )


@plugin_api.get("/shops/info/query-by-trade-v2")
@plugin_api.get("/shops/info/by-trade-v2")
@org_required
@validate
def get_shop_info_by_tid_v2(query: GetShopInfoRequestV2):
    ret: dict[int, list[str]] = {}
    if query.tid:
        # 优先使用平台订单，可降低ERP API的调用，防止被限流影响
        res = trade_client.get_channel_info_by_tid_list([query.tid], g.org_id)
        if res:
            channel_info = res[0]
            # 淘宝只有 seller nick
            if not channel_info.channel_no and channel_info.seller_nick:
                shop = Shop.query.filter(
                    Shop.org_id == g.org_id,
                    Shop.platform.in_(["TAOBAO", "TMALL"]),
                    Shop.nick == channel_info.seller_nick,
                ).first()
            else:
                shop = Shop.query.filter(
                    Shop.org_id == g.org_id,
                    Shop.sid == channel_info.channel_no,
                ).first()
            return dict(
                succeed=True,
                error=None,
                data=[
                    dict(
                        shop_sid=shop.sid,  # type: ignore[union-attr]
                        shop_platform=shop.platform,  # type: ignore[union-attr]
                        shop_title=shop.title,  # type: ignore[union-attr]
                        tids=[query.tid],
                    )
                ],
            )

    for erp_info in ErpInfo.get_distinct_erp_by_org_id(g.org_id):
        if query.tid:
            ret = ErpTradeManager.detect_shop_by_tid(int(g.org_id), erp_info, query.tid)
        elif query.refund_id:
            ret = ErpTradeManager.detect_shop_by_refund_id(int(g.org_id), erp_info, query.refund_id)
        elif query.logistics_no:
            ret = ErpTradeManager.detect_shop_by_logistics_no(int(g.org_id), erp_info, query.logistics_no)
        elif query.return_logistics_no:
            ret = ErpTradeManager.detect_shop_by_return_logistics_no(int(g.org_id), erp_info, query.return_logistics_no)
        if ret:
            shops = Shop.query.filter(Shop.org_id == g.org_id).filter(Shop.id.in_(list(ret.keys()))).all()
            return dict(
                succeed=True,
                error=None,
                data=[
                    dict(shop_sid=shop.sid, shop_platform=shop.platform, shop_title=shop.title, tids=ret[shop.id])
                    for shop in shops
                ],
            )
    return dict(succeed=False, error="无匹配店铺", data=None)


def get_taobao_trade(sid, tids: List[str]) -> List:
    trades = []
    for tid in tids:
        # todo refactory trade 能否支持批量查询
        trade = trade_client.get_trade_by_tid(sid, tid)
        if is_buyer_nick_encrypt(trade.buyer_nick):
            nick = get_buyer_nick(sid, buyer_open_uid=trade.buyer_open_uid, tid=trade.trade_id)
            if nick:
                trade.buyer_nick = nick
        trades.append(MessageToDict(trade, preserving_proto_field_name=True, including_default_value_fields=True))
    return trades


@plugin_api.route("/trades")
def get_trades():
    """获取买家最近订单，
    如果通过订单ID查询：为了漏掉订单数据调用trade接口依次查询历史库、同步库
    、淘宝api，查到订单数据立即返回，否则查询下一个数据源"""
    uid: str = request.args.get("uid", "")
    buyer_open_uid: str = request.args.get("buyer_open_uid", "")
    trades_sort: str = request.args.get("trades_sort") or TradesSort.CREATED_AT_DESC
    sid: str = get_sid()
    tid: str = request.args.get("tid", "")
    if not (uid or buyer_open_uid or tid):
        # 无uid或open_uid或者无tid 无法判断查询范围
        return jsonify(reason="缺失买家信息或订单ID"), 400
    if not sid:
        return jsonify(reason="缺失店铺id"), 400
    if g.shop.is_taobao():
        record = g.shop.get_recent_record()
        if not record or not record.access_token:
            return jsonify(trades=[], reason="缺失授权记录"), 400
        token: str = record.access_token
        trades = {}
        if not tid:
            trades = trade_client.get_trades_by_buyer(str(sid), g.shop.nick, uid, buyer_open_uid, token)
        else:
            trade_array = []
            trade_info = trade_client.get_trade_from_multi_plat_by_tid(str(sid), g.shop.nick, tid, token)
            if trade_info is not None:
                trade_array.append(trade_info)

            trades["trades"] = trade_array
            logger.info(f"get_trades. trades={trades}")

        if trade_array := trades.get("trades"):  # type: ignore[assignment]
            if isinstance(trade_array, list):
                match trades_sort:
                    case TradesSort.CREATED_AT_DESC:
                        sorted_trades = sorted(
                            trade_array, key=lambda trade: trade.get("created_at") or "", reverse=True
                        )
                    case TradesSort.CREATED_AT_ASC:
                        sorted_trades = sorted(trade_array, key=lambda trade: trade.get("created_at") or "")
                    case _:
                        sorted_trades = trade_array
                trades.update({"trades": sorted_trades})
        return jsonify(trades)
    else:
        return jsonify({})


@plugin_api.route("/trades/<tid>")
def get_trade(tid):
    """
    获取指定的订单(前端获取补发商品使用)
    TODO(<EMAIL>): refactor trade client
        with abstract factory pattern for multiple source.
    """
    sid: str = get_sid()
    if not sid:
        return jsonify(reason="缺失店铺id"), 400
    trade = {}
    if g.shop.is_taobao():
        trades = get_taobao_trade(sid, [tid])
        trade_dict = trades[0] if trades else {}
        return jsonify(trade_dict)
    elif ErpType.DUOHONG in g.shop.erp_types():
        logger.info(f"get tid from duohong {tid}")
        trade = DuohongClient(g.shop.sid).query_trade_by_tid(tid)
        return jsonify(trade)
    elif g.shop.is_doudian():
        trade_res = doudian_client.get_trade_by_tid(g.shop.sid, tid)
        match trade_res:
            case Ok(trade):
                return jsonify(trade)
            case Err():
                return jsonify({})
    elif g.shop.is_ks():
        trades, err = get_ks_trade(g.shop, tid)
        if err:
            return jsonify(reason=err), 400
        return jsonify(trades[0] if trades else {})
    elif ErpType.JST in g.shop.erp_types():
        logger.info(f"get tid from jushuitan {tid}")
        trade = JstSDK(g.shop.sid).get_order_of_nonetb([tid])
    return jsonify(trade)


@plugin_api.route("/batch/trades")
def batch_get_trade():
    """
    获取指定的订单(前端获取补发商品使用)
    """

    def trade_by_platform(tid):
        trade = None
        try:
            if g.shop.is_pdd():
                result = trade_client.get_trade_by_tid_and_channel([tid])
                trade_dict = MessageToDict(
                    result, preserving_proto_field_name=True, including_default_value_fields=True
                )
                if trade_dict and len(trade_dict.get("trade_info_list", [])) > 0:
                    trade_info_list = trade_dict.get("trade_info_list", [])
                    trade = trade_info_list[0].get("pdd_trade_info", {})
            elif ErpType.DUOHONG in g.shop.erp_types():
                trade = DuohongClient(g.shop.sid).query_trade_by_tid(tid)
            elif g.shop.is_doudian():
                trade_res = doudian_client.get_trade_by_tid(g.shop.sid, tid)
                trade = trade_res.map_or({}, lambda x: x)
            elif g.shop.is_ks():
                trades, err = get_ks_trade(g.shop, tid)
                if err:
                    logger.error(f"get trade by platform error: {err}")
                    return None
                trade = trades[0]
            elif ErpType.JST in g.shop.erp_types():
                trade = JstSDK(g.shop.sid).get_order_of_nonetb([tid])
        except BaseException as e:
            logger.error(f"get trade by platform error: {str(e)}")
            return None
        return trade

    tid_list = request.args.getlist("tid", str)

    if len(tid_list) > 10:
        return jsonify(data=None, msg="查询太多的订单，仅支持10条"), 400
    if g.shop.is_taobao():
        trades = get_taobao_trade(g.shop.sid, tid_list)
    else:
        trades = [trade for tid in tid_list if (trade := trade_by_platform(tid))]

    return jsonify(data=trades, msg="")


@plugin_api.route("/trade-info/<tid>")
def get_trade_info(tid=None):
    """
    获取指定的订单，只根据店铺平台判断(插件栏、网页版工单，查询订单信息回填订单组件使用)
    # todo 支持抖音订单
    """
    sid: str = get_sid()
    if not sid:
        return jsonify(reason="缺失店铺id"), 400
    trades = []
    if g.shop.is_taobao():
        trades = get_taobao_trade(sid, [tid])
    elif g.shop.is_pdd():
        trade = trade_client.get_trade_by_tid_and_channel([tid])
        trade_dict = MessageToDict(trade, preserving_proto_field_name=True, including_default_value_fields=True)
        if trade_dict:
            trade_info_list = trade_dict.get("trade_info_list", [])
            trades = [trade_info.get("pdd_trade_info", {}) for trade_info in trade_info_list]
    elif g.shop.is_doudian():
        trade = trade_client.get_trade_by_tid_and_channel([tid], g.shop.platform)
        trade_dict = MessageToDict(trade, preserving_proto_field_name=True, including_default_value_fields=True)
        if trade_dict:
            trade_info_list = trade_dict.get("trade_info_list", [])
            trades = [trade_info.get("dy_trade_info", {}) for trade_info in trade_info_list]
    elif g.shop.is_alibaba():
        trade = trade_client.get_trade_by_tid_and_channel([tid], g.shop.platform, org_id=g.shop.org_id)
        trade_dict = MessageToDict(trade, preserving_proto_field_name=True, including_default_value_fields=True)
        if trade_dict:
            trade_info_list = trade_dict.get("trade_info_list", [])
            trades = [trade_info.get("alibaba_trade_info", {}) for trade_info in trade_info_list]
    elif g.shop.is_ks():
        trades, err = get_ks_trade(g.shop, tid)
        if err:
            return jsonify(reason=err), 400
    return jsonify(trades[0] if trades else {})


@plugin_api.route("/batch-trade-info/<tids>")
def batch_get_trade_info(tids):
    """
    批量获取 获取指定的订单，只根据店铺平台判断(插件栏、网页版工单，查询订单信息回填订单组件使用)
    @:return trades- 订单的信息, is_match- 订单是否是属于当前sid的，人工操作的订单有可能搞错，需要校验
    """
    tids_str = tids
    tids = list(set(tids.split(",")))
    sid: str = get_sid()
    if not sid:
        return jsonify(reason="缺失店铺id"), 400
    # 前端会在每次输入订单号时，调用这个接口，每次只传一个订单号，判断订单是否属于当前店铺
    # is_match 的结果只会影响 前端一些提示性tips
    erp_label = None
    version = request.args.get("version") or "1"
    source = request.args.get("source") or "platform"
    erps = ErpInfo.query.filter_by(shop_id=g.shop.id).all()
    if source == "erp" and len(erps) == 0:
        return jsonify(reason="店铺没有授权erp"), 400
    if source == "erp" and len(erps) == 1 and erps[0].erp_type == ErpType.WANLINIU:
        if "app_key" not in (erps[0].meta or {}):
            # 万里牛授权信息不全,用平台数据兜底
            source = "platform"
    # 兼容旧的前端
    if version == "1":
        # 旧版的strategy和source不一致
        if source == "platform" or (erps and erps[0].erp_type != ErpType.JST):
            strategy = "platform"
            trades, is_match = PlatformTradeManager.process(g.shop, tids, version)
            return jsonify(
                trades=trades, is_match=is_match, platform=g.shop.platform, erp_label=erp_label, strategy=strategy
            )
        elif source == "erp":
            strategy = "erp"
            erp_label = erps[0].erp_type.label  # type: ignore[union-attr]
            trades, code, reason = ErpTradeManager.process(g.shop, tids_str, version)
            return jsonify(
                trades=trades,
                is_match=True,
                reason=reason,
                code=code,
                platform=g.shop.platform,
                erp_label=erp_label,
                strategy=strategy,
            )

    elif version == "2":
        if source == "platform" or (
            erps
            and erps[0].erp_type
            not in [
                ErpType.JST,
                ErpType.WDT,
                ErpType.WDTULTI,
                ErpType.KUAIMAI,
                ErpType.WANLINIU,
                ErpType.GUANYIYUN,
                ErpType.JACKYUN,
            ]
        ):

            trades, is_match = PlatformTradeManager.process(g.shop, tids, version)
            return jsonify(
                trades=trades, is_match=is_match, platform=g.shop.platform, erp_label=erp_label, strategy="platform"
            )
        elif source == "erp":
            erp_label = erps[0].erp_type.label  # type: ignore[union-attr]
            trades, code, reason = ErpTradeManager.process(g.shop, tids_str, version)
            return jsonify(
                trades=trades,
                is_match=True,
                reason=reason,
                code=code,
                platform=g.shop.platform,
                erp_label=erp_label,
                strategy="erp",
            )
    else:
        return jsonify(
            trades=[],
            is_match=False,
            reason=f"未知版本 {version}",
            platform=g.shop.platform,
            erp_label=erp_label,
            strategy=source,
        )


@plugin_api.get("/order_address/<tid>")
def get_address_by_tid(tid):
    """
    查询订单的收货地址
    """
    _logger = logger.bind(tid=tid)
    if g.shop.is_pdd():
        if g.shop.is_pdd_auth_valid():
            try:
                return jsonify(get_decrypted_receiver_info(g.shop.sid, tid))
            except PddServiceError as exc:
                _logger.error(str(exc))
                return jsonify(reason=str(exc)), 400
        else:
            return jsonify(reason="店铺无授权"), 400
    elif g.shop.is_taobao():
        try:
            sid = g.shop.sid
            nick = g.shop.nick
            platform = g.shop.platform
            tokens: dict = json.loads(app_config.HYWS_TOKENS)
            if access_token := tokens.get(sid):
                trade = trade_client.get_trade_by_tid(sid, tid)
                res = hyws_taobao_client.oaid_decrypt(access_token, trade.oaid, tid)
                match res:
                    case Ok(api_resp):
                        if not api_resp["receiver_list"]:
                            return jsonify(reason="查询地址失败"), 400
                        api_receiver = api_resp["receiver_list"][0]
                        if handle_mobile := SmartCallExecutor.get_valid_phone_number(api_receiver["mobile"]):
                            address = {
                                "address": api_receiver.get("address_detail") or "",
                                "city": api_receiver.get("city") or "",
                                "country": api_receiver.get("country") or "",
                                "district": api_receiver.get("district") or "",
                                "mobile": handle_mobile,
                                "name": api_receiver.get("name") or "",
                                "state": api_receiver.get("state") or "",
                                "town": api_receiver.get("town") or "",
                                "zip": "",
                            }
                            return jsonify(address)
                    case Err(_):
                        return jsonify(reason="查询地址失败"), 400
            else:
                resp = buyer_client.get_receiver_info(sid, nick, platform, tid)
                if resp and resp.code == DgtResponseCode.OK:
                    receiver = resp.receiver_info
                    if receiver.receiver_mobile:
                        # 不清楚为什么有的手机号会带有国家码，这里做一下处理
                        if handle_mobile := SmartCallExecutor.get_valid_phone_number(receiver.receiver_mobile):
                            receiver.receiver_mobile = handle_mobile
                    address = {
                        "address": receiver.receiver_address,
                        "city": receiver.receiver_city,
                        "country": receiver.receiver_country,
                        "district": receiver.receiver_district,
                        "mobile": receiver.receiver_mobile,
                        "name": receiver.receiver_name,
                        "state": receiver.receiver_state,
                        "town": receiver.receiver_town,
                        "zip": receiver.receiver_zip,
                    }
                else:
                    return jsonify(reason=resp.error_msg), 400
        except BaseException as exc:
            _logger.error(str(exc))
            return jsonify(reason="查询地址失败"), 400
        return jsonify(address)
    elif ErpType.DUOHONG in g.shop.erp_types():
        try:
            match DuohongClient(g.shop.sid).query_address(tid):
                case Ok(address_data):
                    if address_data.get("mobile"):
                        address_data["mobile"] = address_data["mobile"].replace("86-", "")
                        address = {
                            "country": address_data.get("country"),
                            "state": address_data.get("state"),
                            "city": address_data.get("city"),
                            "district": address_data.get("district"),
                            "town": address_data.get("town"),
                            "address": address_data.get("address"),
                            "zone": address_data.get("zone"),
                            "name": address_data.get("name"),
                            "mobile": address_data.get("mobile"),
                        }
                    else:
                        logger.warning(f"获取多鸿地址不包含有效的电话: {address_data}")
                        address = {}
                case Err(error_message):
                    logger.warning(f"获取多鸿地址失败 {error_message}")
                    address = {}
        except BaseException as exc:
            address = {}
            _logger.exception(str(exc))
        return jsonify(address)
    elif g.shop.is_doudian():
        try:
            if doudian_address_use_api(g.shop):
                if not g.shop.sid:
                    raise DoudianCloudServiceError("店铺ID不存在，无法解密")
                r = doudian_cloud.receiver_info(store_id=g.shop.sid, order_id=tid)
                return {
                    "state": r.province,
                    "city": r.city,
                    "district": r.town,
                    "town": r.street,  # 抖音street信息和前端town对应
                    "address": r.detail,
                    "name": r.receiver_name,
                    "mobile": r.receiver_phone,
                }
            sid = g.shop.sid
            order_detail = doudian_client.order_decrypt(sid, tid)
            if not order_detail.is_success:
                return jsonify(reason=f"获取抖店订单地址失败 {order_detail.error_msg}"), 400
            if not order_detail.receive_info:
                return jsonify(reason="未获取到明文地址"), 400

            address = order_detail.receive_info.to_standard_output()
            return jsonify(address)
        except DoudianCloudServiceError as exc:
            _logger.exception(str(exc))
            return jsonify(reason=str(exc)), 400
        except BaseException as exc:
            address = {}
            _logger.exception(str(exc))
        return jsonify(address)
    else:
        return jsonify(reason="因平台安全限制，未获取到明文地址，需手动输入"), 400


@plugin_api.put("/trades/<trade_id>/memo")
@validate
def update_memo(trade_id, body: UpdateMemo):
    sid = get_sid()
    if not sid:
        return jsonify(reason="缺失店铺id"), 400
    user_nick = request.args.get("user_nick")
    if not user_nick:
        return jsonify(reason="缺失客服昵称"), 400

    record = g.shop.get_recent_record()
    if not record or not record.access_token:
        return jsonify(trades=[], reason="缺失授权记录"), 400

    body_dict = body.dict()
    if memo_flag := body_dict.pop("memo_flag"):
        body_dict["content"] += f" {user_nick} "

    code, msg = trade_client.update_memo(
        trade_id, record.access_token, body_dict["content"], body_dict["flag"], memo_flag
    )
    status_code, is_ok = (400, False) if code != API_SUCCESS else (200, True)

    return jsonify(succeed=is_ok, memo=body_dict["content"]), status_code


@plugin_api.route("/trade/trade-rates", methods=["POST"])
@shop_required
@validate
def trade_rates(body: RatesSchema):
    """获取评价信息"""
    if g.shop.platform in ["TAOBAO"]:
        record = g.shop.get_recent_record()
        if not record or not record.access_token:
            return jsonify(rates=[], reason="缺失授权记录"), 400
        token: str = record.access_token
        rates = trade_client.get_rate_by_tid_list(body.trade_ids, token)
        if rates["code"] != "API_SUCCESS":
            msg = rates["msg"]
            logger.error(f"get_rate_by_tid_list fail. msg {msg}")
            return jsonify(trade_rate_info=[], reason=msg), 500
        else:
            return jsonify(trade_rate_info=rates["trade_rate_info"]), 200
    return jsonify(trade_rate_info=[]), 200


def get_ks_trade(shop: Shop, tid: str):
    access_token = shop.get_access_token()
    if not access_token:
        return None, f"快手店铺 {shop.nick} 未授权"
    trade = trade_client.get_trade_by_tid_and_channel([tid], shop.platform, shop.sid, access_token)
    trade_dict = MessageToDict(trade, preserving_proto_field_name=True, including_default_value_fields=True)
    trades = []
    if trade_dict:
        trade_info_list = trade_dict.get("trade_info_list", [])
        trades = [trade_info.get("ks_trade_info", {}) for trade_info in trade_info_list]
    return trades, None


@plugin_api.get("/trade/upload/log/<string:order_id>")
@shop_required
def get_trade_update_log(order_id):
    """获取ERP的订单操作日志"""
    erp_info = ErpInfo.get_newest_erp_by_sid(g.shop.sid)
    if not erp_info:
        return jsonify({})
    match erp_info.erp_type:
        case ErpType.JST:
            orders = TradesFinder(tid=order_id, shop=g.shop, is_reissue="0").process()
            if not orders:
                return jsonify({})
            order = sorted(orders, key=lambda x: x.o_id)[0]
            jst_resp = JstNewSDK(g.shop.sid).order_action_query(order.o_id)
            ret_actions = [
                {"time": action.created, "name": action.name, "remark": action.remark, "operator": action.creator_name}
                for action in jst_resp.data.datas or []
            ]
            weight = order.f_weight
            if g.shop.org_id == "2655":
                weight = order.weight
            return jsonify(
                {
                    "logistics_company": order.logistics_company,
                    "logistics_no": order.l_id,
                    "weight": weight,
                    "o_id": order.o_id,
                    "outer_oi_id": "",
                    "actions": ret_actions,
                }
            )
        case ErpType.WDTULTI:
            trades = WdtUltiQM(g.shop.sid).get_orders(src_tid=order_id).order
            if not trades:
                return jsonify({})
            origin_trade = sorted(trades, key=lambda x: x.trade_no)[0]
            wdtulti_resp = WdtUltiOpenAPIClient(g.shop.sid).query_trade_log(
                QueryTradeLogReq(trade_no=origin_trade.trade_no)
            )
            ret_actions = [
                {
                    "time": action.created,
                    "name": WdtUltiOpenAPIClient.trade_log_type_to_str(action.type),
                    "operator": action.shortname,
                }
                for action in wdtulti_resp.data.detail_list
            ]
            return jsonify(
                {
                    "logistics_company": origin_trade.logistics_name,
                    "logistics_no": origin_trade.logistics_no,
                    "weight": origin_trade.detail_list[0].weight,
                    "o_id": origin_trade.trade_no,
                    "outer_oi_id": "",
                    "actions": ret_actions,
                }
            )
        case _:
            return jsonify({})


def pdd_order_address_use_api(shop: Shop) -> bool:
    # 拼多多店铺，且有爱图西授权
    use_api = shop.is_pdd() and shop.is_pdd_auth_valid()
    return use_api


def doudian_address_use_api(shop: Shop) -> bool:
    # 抖音店铺，且有有效的小柚子授权
    gr = GrantRecord.query.filter(
        GrantRecord.shop_id == shop.id,
        GrantRecord.app == App.DOUDIAN_XYZ,
        GrantRecord.expires_at_ms > arrow.utcnow().to("Asia/Shanghai").int_timestamp * 1000,
    ).first()
    if gr and shop.is_doudian():
        return True
    return False


@plugin_api.get("/refund-info/<refund_id>")
@shop_required
def get_refund_info(refund_id: str):
    from robot_processor.ext import db
    from robot_processor.refund.models import TaobaoRefund
    from robot_processor.utils import unwrap_optional

    if g.shop.platform in ["TAOBAO", "TMALL"]:
        try:
            taobao_refund_id = int(refund_id)
            taobao_refund = db.session.get(TaobaoRefund, taobao_refund_id)
            if not taobao_refund:
                return jsonify(succeed=False, msg="售后单号不存在")
            taobao_refund_info = unwrap_optional(taobao_refund).jdp_response["refund_get_response"]["refund"]
            return jsonify(succeed=True, data=dict(taobao_refund=taobao_refund_info))
        except ValueError:
            return jsonify(succeed=False, msg="售后单号格式错误")
    else:
        return jsonify(succeed=False, msg="暂不支持的店铺类型")
