from typing import List, Any

import arrow
from loguru import logger
from pydantic import BaseModel

from robot_processor.business_order.business_order_manager import \
    BusinessManager
from robot_processor.business_order.schema import BusinessOrderSchema, \
    CreateAction
from robot_processor.client import doudian_cloud
from robot_processor.client.errors import DoudianCloudServiceError
from robot_processor.client.schema import DoudianOrderLogistics
from robot_processor.enums import Creator, FromType
from robot_processor.ext import cache
from robot_processor.form.models import Form
from robot_processor.shop.models import Shop
from robot_processor.utils import ResultUtil, db_session_rollback


def forms_by_sid(sid):
    forms = Form.Queries.by_sids([sid]).all()
    return forms


class Product(BaseModel):
    TID: str
    OID: str
    SPU: str
    SKU: str
    SPU_OUTER: str
    SKU_OUTER: str
    COUNT: int
    TITLE: str
    PRICE: float
    PICTURE: str
    DESCRIPTION: str


def do_create_bo(
        shop: Shop, tid: list[dict[str, str]], aftersale_id: str,
        products: List[Product], aftersale_num: int, refund_amount: float,
        reason: str, return_logistics_company_name: str,
        return_logistics_code: str, aftersale_type_text: str,
        logistics_company_name: str, logistics_code: str) -> bool:
    forms = forms_by_sid(shop.sid)
    forms = list(filter(lambda form: form.name == '退货退款（自动获取）', forms))
    if not forms:
        logger.error(f"在店铺{shop.sid}中找不到工单模版")
        return False
    form = forms[0]
    widgets_info = form.get_form_first_step_widgets_info()
    data: dict[Any, Any] = {
        widgets_info.get("订单/子订单"): tid,
        widgets_info.get("退款单号"): aftersale_id,
        widgets_info.get("购买商品"): [product.dict() for product in products]
    }
    if widgets_info.get("售后数量"):
        data[widgets_info.get("售后数量")] = aftersale_num
    if widgets_info.get("退款金额"):
        data[widgets_info.get("退款金额")] = refund_amount
    if widgets_info.get("退款原因"):
        data[widgets_info.get("退款原因")] = reason
    if widgets_info.get("退货快递公司"):
        data[widgets_info.get("退货快递公司")] = return_logistics_company_name
    if widgets_info.get("退货快递单号"):
        data[widgets_info.get("退货快递单号")] = return_logistics_code
    if widgets_info.get("售后类型"):
        data[widgets_info.get("售后类型")] = aftersale_type_text
    if widgets_info.get("发货快递公司"):
        data[widgets_info.get("发货快递公司")] = logistics_company_name
    if widgets_info.get("发货快递单号"):
        data[widgets_info.get("发货快递单号")] = logistics_code
    body = BusinessOrderSchema(
        user_nick='飞梭机器人',
        sid=shop.sid,
        form_id=form.id,
        create_action=CreateAction.SUBMIT.value,
        creator_type=Creator.RPA.value,
        data=data,
        from_type=FromType.LEYAN,
        uid="飞梭机器人",
    )
    res = BusinessManager.create_customize_business_order(shop.org_id, body)
    if ResultUtil.not_success(res):
        return False
    return True


def doudian_aftersale_refund_with_return(shop_id: int) -> bool:
    shop: Shop | None = Shop.query.get(shop_id)
    assert shop is not None

    now = arrow.now()

    last_ts = cache.get(f"doudian_aftersale_refund_with_return_{shop_id}_ts")
    if not last_ts:
        logger.warning("没有找到上一次的执行记录，从1天前开始恢复")
        last_ts = int(now.shift(days=-1).timestamp())

    now_ts = int(now.timestamp())
    start_ts = last_ts

    while start_ts <= now_ts:
        next_ts = start_ts + 3600 if start_ts + 3600 <= now_ts else now_ts
        page_size = 100
        page_no = 0

        while True:
            try:
                resp = doudian_cloud.get_aftersale_list(
                    store_id=shop.sid,
                    update_start_time=start_ts, update_end_time=next_ts, aftersale_type=0,
                    page=page_no, size=page_size)
                for aftersale in resp.items:
                    aftersale_id = aftersale.aftersale_info.aftersale_id
                    if aftersale.aftersale_info.refund_status != 1:
                        continue
                    if aftersale.aftersale_info.aftersale_status == 7:
                        continue
                    if cache.get(f"doudian_aftersale_refund_with_return"
                                 f"_{shop_id}_created_aftersale_id"
                                 f"_{aftersale_id}") is None:
                        tid = [{"tid": aftersale.order_info.shop_order_id,
                                "oid": order.sku_order_id} for order in
                               aftersale.order_info.related_order_info]
                        products = []
                        order_detail = doudian_cloud.get_order_detail(
                            store_id=shop.sid,
                            order_id=aftersale.order_info.shop_order_id)
                        for order in aftersale.order_info.related_order_info:
                            skus = [sku for sku in
                                    order_detail.sku_order_list if
                                    sku.code == order.shop_sku_code]
                            sku_id = skus[0].sku_id if len(skus) > 0 else ""
                            product = Product(
                                TID=aftersale.order_info.shop_order_id,
                                OID=order.sku_order_id,
                                SPU=order.product_id,
                                SKU=sku_id,
                                SPU_OUTER="",
                                SKU_OUTER=order.shop_sku_code,
                                COUNT=order.item_num,
                                TITLE=order.product_name,
                                PRICE=float(order.price) / 100,
                                PICTURE=order.product_image,
                                DESCRIPTION=";".join([spec.name + ": " +
                                                      spec.value for
                                                      spec in
                                                      order.sku_spec])
                            )
                            products.append(product)
                            # 抖店支持多包裹，这里可能是多个物流单号，现在只支持1个
                        order_logistic: DoudianOrderLogistics = aftersale.aftersale_info.order_logistics[0]
                        if not do_create_bo(shop, tid, aftersale_id, products,
                                            aftersale.aftersale_info.aftersale_num,
                                            float(
                                                aftersale.aftersale_info.refund_amount) / 100.0,
                                            aftersale.text_part.reason_text,
                                            aftersale.aftersale_info.return_logistics_company_name,
                                            aftersale.aftersale_info.return_logistics_code,
                                            aftersale.text_part.aftersale_type_text,
                                            order_logistic.company_name,
                                            order_logistic.tracking_no,
                                            ):
                            logger.error(
                                f"创建工单失败 aftersale id: {aftersale.aftersale_info.aftersale_id}  tid: {tid}")
                        cache.set(
                            f"doudian_aftersale_refund_with_return_{shop_id}_created_aftersale_id"
                            f"_{aftersale_id}", 1,
                            timeout=86400 * 30)
                if page_no * page_size >= resp.total:
                    break
            except DoudianCloudServiceError as e:
                logger.warning(e)
                break
            except Exception as e:
                logger.opt(exception=e).error(
                    f"处理时发生异常， 入参: {page_no} {page_size} {start_ts} {next_ts} 异常: {str(e)}")
                db_session_rollback()
            finally:
                page_no = page_no + 1
        start_ts = next_ts + 1
    cache.set(f"doudian_aftersale_refund_with_return_{shop_id}_ts", now_ts,
              86400 * 100)
    return True
