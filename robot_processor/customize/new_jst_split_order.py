import time
from functools import lru_cache
from typing import Dict

import arrow
from loguru import logger

from robot_processor.business_order.business_order_manager import \
    BusinessManager
from robot_processor.business_order.schema import BusinessOrderSchema, \
    CreateAction
from robot_processor.client import get_buyer_nick, kiosk_client
from robot_processor.enums import Creator, FromType, ErpType
from robot_processor.error.client_request import JstRequestError
from robot_processor.ext import cache
from robot_processor.form.models import Form
from robot_processor.shop.models import Shop
from robot_processor.utils import ResultUtil, db_session_rollback
from rpa.erp.jst import JstQmSDK, JstBaseOrder, OrdersSingleQueryResp, JstNewSDK


def strftime(ts):
    time_struct = time.localtime(ts)
    return time.strftime("%Y-%m-%d %H:%M:%S", time_struct)


def forms_by_sid(sid):
    forms = Form.Queries.by_sids([sid]).all()
    return forms


@lru_cache()
def get_sid_by_erp_shop(erp_shop_id: int) -> str:
    resp = kiosk_client.auth_shop_mapping_by_erp_shop(ErpType.JST.name,
                                                      erp_shop_id)
    return resp["channelNo"]


def do_create_bo(shop: Shop, tid: str, logistics_info: str):
    forms = forms_by_sid(shop.sid)
    forms = list(
        filter(lambda form: form.name == '拆单发货-通知发货明细', forms))
    if not forms:
        logger.error(f"在店铺{shop.sid}中找不到工单模版")
        return False
    form = forms[0]
    widgets_info = form.get_form_first_step_widgets_info()
    buyer_nick = '无昵称'
    if shop.platform == 'TAOBAO' or shop.platform == 'TMALL':
        buyer_nick = get_buyer_nick(shop.sid, tid=tid)
    body = BusinessOrderSchema(
        user_nick='飞梭机器人',
        sid=shop.sid,
        form_id=form.id,
        create_action=CreateAction.SUBMIT,
        creator_type=Creator.RPA,
        data={
            widgets_info.get("买家昵称"): buyer_nick,
            widgets_info.get("订单/子订单"): [{"tid": tid}],
            widgets_info.get("物流信息"): logistics_info
        },
        from_type=FromType.LEYAN,
        uid="飞梭机器人",
    )
    res = BusinessManager.create_customize_business_order(shop.org_id, body)
    if ResultUtil.not_success(res):
        print(ResultUtil.get_reason(res))
        return False
    return True


def new_jst_split_order(org_id: int) -> bool:
    shop = Shop.query.filter_by(org_id=org_id).first()
    assert shop

    now = arrow.now()

    last_ts = cache.get(f"jst_split_order_{org_id}_ts")
    if not last_ts:
        logger.warning("没有找到上一次的执行记录，从1个小时前开始恢复")
        last_ts = int(now.shift(hours=-1).timestamp())

    now_ts = int(now.timestamp())
    start_ts = last_ts
    jst_qm_sdk = JstQmSDK(sid=shop.sid)
    jst_new_sdk = JstNewSDK(sid=shop.sid)

    logisitics_companies = jst_new_sdk.all_logisticscompany_query().data.datas

    order_map: Dict[str, list[JstBaseOrder]] = {}
    while start_ts <= now_ts:
        next_ts = start_ts + 3600 if start_ts + 3600 <= now_ts else now_ts
        start_time = strftime(start_ts)
        end_time = strftime(next_ts)
        page_size = 100
        page_no = 1
        while True:
            resp: OrdersSingleQueryResp | None = None
            try:
                resp = jst_qm_sdk.query_orders(
                    {"start_time": start_time,
                     "end_time": end_time,
                     "status": "Sent",
                     "date_type": 3
                     },
                    page_index=str(page_no),
                    page_size=str(page_size)
                )
            except JstRequestError as e:
                logger.warning(f"聚水潭查询订单失败 {e}")
            page_no = page_no + 1
            if not resp:
                continue
            if len(resp.orders) == 0:
                break
            for trade in resp.orders:
                try:
                    if not trade.order_from or 'SPLIT' not in trade.order_from:
                        continue
                    if org_id == 2550 and arrow.get(trade.send_date).shift(
                            days=1) < arrow.now():
                        continue
                    if trade.l_id is None or trade.logistics_company is None:
                        continue  # type: ignore[unreachable]
                    if (cache.get(
                            f"jst_split_order_{org_id}_so_id_{trade.so_id}")
                            is None):
                        all_orders_resp = jst_qm_sdk.query_orders({
                            "so_ids": trade.so_id,
                            "order_types": "普通订单,供销Plus"
                        })
                        if not all(order.status in ['Sent', 'Merged',
                                                    'Split', 'Cancelled']
                                   for order in all_orders_resp.orders):
                            continue
                        if not all(order.l_id is not None and 
                                   order.logistics_company is not None for order \
                                in all_orders_resp.orders if \
                                order.status == 'Sent'):
                            continue
                        if trade.so_id not in order_map:
                            order_map[trade.so_id] = [
                                order for order in all_orders_resp.orders if
                                order.status == 'Sent']
                except Exception as e:
                    logger.bind(tid=trade.tid).opt(exception=e).error(
                        f"处理订单 {trade.tid} 发生异常")
                    db_session_rollback()
        start_ts = next_ts + 1
    for so_id in order_map.keys():
        trades = order_map[so_id]
        if len(trades) == 0:
            continue
        for trade in trades:
            companies = [company for company in logisitics_companies if
                         company.lc_id == trade.lc_id]
            if companies:
                trade.logistics_company = companies[0].lc_name
        logistics_info = "\n".join(
            [f"{trade.logistics_company}:{trade.l_id.replace('@', '')}" for
             trade in trades])
        try:
            sid = get_sid_by_erp_shop(trades[0].shop_id)
            order_shop = Shop.query.filter_by(sid=sid).first()
            if order_shop:
                success = do_create_bo(order_shop, so_id, logistics_info)
                if not success:
                    logger.error(f"创建工单失败 tid: {so_id}")
                else:
                    logger.info(f"创建工单成功 {so_id}")
            else:
                logger.error(
                    f"租户 {org_id} 聚水潭店铺 {trades[0].shop_id} 无法找到对应的飞梭店铺")
            for trade in trades:
                cache.set(f"jst_split_order_{org_id}_so_id_{trade.so_id}", 1,
                          timeout=86400 * 30)
        except KeyError:
            logger.error(f"通过聚水潭店铺{trades[0].shop_id}找不到飞梭店铺")
            continue
    cache.set(f"jst_split_order_{org_id}_ts", now_ts, 86400 * 100)
    return True
