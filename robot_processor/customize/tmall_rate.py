import json

from flask import current_app
from loguru import logger
from mq_http_sdk.mq_client import MQClient
from mq_http_sdk.mq_exception import MQExceptionBase
from result import Ok

from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.business_order.schema import BusinessOrderSchema, CreateAction
from robot_processor.client import taobao_client, get_buyer_nick
from robot_processor.customize.create_bo_by_trade_event import MemoAutoBoCreator
from robot_processor.enums import Creator, FromType
from robot_processor.form.models import Form
from robot_processor.shop.models import Shop, GrantRecord


def forms_by_sid(sid):
    forms = Form.Queries.by_sids([sid]).all()
    return forms


def do_create_bo(shop: Shop, tid: str, oid: str, spu_id, content: str, created_at: str):
    forms = forms_by_sid(shop.sid)
    forms = list(filter(lambda form: form.name == '自动抓取差评登记', forms))
    if not forms:
        logger.error(f"在店铺{shop.sid}中找不到工单模版")
        return False
    form = forms[0]
    widgets_info = MemoAutoBoCreator.get_form_first_step_widgets_info(Form.query.get(form.id))
    buyer_nick = get_buyer_nick(shop.sid, tid=tid)
    body = BusinessOrderSchema(
        user_nick='飞梭机器人',
        sid=shop.sid,
        form_id=form.id,
        create_action=CreateAction.SUBMIT.value,
        creator_type=Creator.RPA.value,
        data={
            widgets_info.get("买家昵称"): buyer_nick,
            widgets_info.get("订单号"): [{"tid": tid, "oid": oid}],
            widgets_info.get("货品ID"): [{"spu": spu_id}],
            widgets_info.get("评价内容"): content,
            widgets_info.get("评价时间"): created_at
        },
        from_type=FromType.LEYAN,
        uid="飞梭机器人",
    )
    BusinessManager.create_customize_business_order(shop.org_id, body)


def tmall_rate():
    mq_client = MQClient(
        current_app.config.get("ROCKET_MQ_ENDPOINT"),
        current_app.config.get("ROCKET_MQ_ACCESS_KEY"),
        current_app.config.get("ROCKET_MQ_ACCESS_SECRET"))

    # 消息所属的Topic，在消息队列RocketMQ版控制台创建。
    topic_name = "jst_sync_rate"
    # 您在消息队列RocketMQ版控制台创建的Group ID。
    group_id = current_app.config.get("ROCKET_MQ_CONSUMER_GROUP_ID")
    # Topic所属的实例ID，在消息队列RocketMQ版控制台创建。
    # 若实例有命名空间，则实例ID必须传入；若实例无命名空间，则实例ID传入空字符串。实例的命名空间可以在消息队列RocketMQ版控制台的实例详情页面查看。
    instance_id = current_app.config.get("ROCKET_MQ_INSTANCE_ID")

    consumer = mq_client.get_consumer(instance_id, topic_name, group_id)
    while True:
        msgs = []
        try:
            msgs = consumer.consume_message(10, 5)
            for msg in msgs:
                trade_rate = json.loads(msg.message_body)
                if not trade_rate["topic"] == "taobao_trade_TradeRated":
                    continue
                content = json.loads(trade_rate["content"])
                if content["rater"] == "seller":
                    continue
                tid = str(content["tid"])
                oid = str(content["oid"])
                iid = str(content["iid"])
                seller_nick = content["seller_nick"]
                seller_nicks = current_app.config.get("TMALL_RATE_SELLER_NICKS", [])
                if seller_nick in seller_nicks:
                    shop = Shop.query.filter_by(nick=seller_nick).first()
                    if shop:
                        grant_record: GrantRecord | None = GrantRecord.query.filter_by(shop_id=shop.id).first()
                        if grant_record:
                            match taobao_client.tmall_traderate_feeds_get(grant_record.access_token, oid):
                                case Ok(feed):
                                    logger.info(f"feed: {feed}")
                                    if feed and feed["tmall_rate_info"]["has_negtv"]:
                                        do_create_bo(
                                            shop, tid, oid, iid,
                                            feed["tmall_rate_info"]["content"],
                                            feed["tmall_rate_info"]["comment_time"]
                                        )
        except MQExceptionBase as e:
            logger.warning(str(e))
            pass
        try:
            receipt_handle_list = [msg.receipt_handle for msg in msgs]
            consumer.ack_message(receipt_handle_list)
        except MQExceptionBase as e:
            logger.warning(str(e))
            pass
