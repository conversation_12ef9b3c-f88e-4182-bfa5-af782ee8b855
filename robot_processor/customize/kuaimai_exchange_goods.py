import time

import arrow
from loguru import logger

from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.business_order.schema import BusinessOrderSchema, CreateAction
from robot_processor.client import get_buyer_nick
from robot_processor.customize.create_bo_by_trade_event import MemoAutoBoCreator
from robot_processor.enums import Creator, FromType
from robot_processor.ext import cache
from robot_processor.form.models import Form
from robot_processor.shop.models import Shop
from robot_processor.utils import ResultUtil
from rpa.erp.kuaimai import KuaimaiQmSDK, KmSDK


def strftime(ts):
    time_struct = time.localtime(ts)
    return time.strftime("%Y-%m-%d %H:%M:%S", time_struct)


def forms_by_sid(sid):
    forms = Form.Queries.by_sids([sid]).all()
    return forms


def do_create_bo(shop: Shop, tid, sid):
    forms = forms_by_sid(shop.sid)
    forms = list(filter(lambda form: form.name == '换货通知', forms))
    if not forms:
        logger.error(f"在店铺{shop.sid}中找不到工单模版")
        return False
    form = forms[0]
    widgets_info = MemoAutoBoCreator.get_form_first_step_widgets_info(Form.query.get(form.id))
    buyer_nick = '无昵称'
    if shop.platform == 'TAOBAO' or shop.platform == 'TMALL':
        buyer_nick = get_buyer_nick(shop.sid, tid=tid)
    body = BusinessOrderSchema(
        user_nick='飞梭机器人',
        sid=shop.sid,
        form_id=form.id,
        create_action=CreateAction.SUBMIT.value,
        creator_type=Creator.RPA.value,
        data={
            widgets_info.get("买家昵称"): buyer_nick,
            widgets_info.get("订单/子订单"): [{"tid": tid}],
            widgets_info.get("快麦系统单号"): sid
        },
        from_type=FromType.LEYAN,
        uid="飞梭机器人",
    )
    res = BusinessManager.create_customize_business_order(shop.org_id, body)
    if ResultUtil.not_success(res):
        return False
    return True


def kuaimai_exchange_goods() -> bool:
    shop = Shop.query.filter_by(org_id=1108).first()
    assert shop

    now = arrow.now()

    last_ts = cache.get("kuaimai_exchange_goods_1108_ts")
    if not last_ts:
        logger.warning("没有找到上一次的执行记录，从1个小时前开始恢复")
        last_ts = int(now.shift(hours=-1).timestamp())

    now_ts = int(now.timestamp())
    start_ts = last_ts

    while start_ts <= now_ts:
        next_ts = start_ts + 3600 if start_ts + 3600 <= now_ts else now_ts
        start_time = strftime(start_ts)
        end_time = strftime(next_ts)
        page_size = 100
        page_no = 1
        while True:
            try:
                resp = KuaimaiQmSDK(shop.sid).get_all_orders_by_time(
                    page_no, page_size, start_time=start_time, end_time=end_time)
                for trade in (resp.trades or []):
                    types = trade.type.split(",")
                    if "13" in types and trade.sysStatus == "SELLER_SEND_GOODS":
                        if cache.get(f"kuaimai_exchange_goods_1108_sid_{trade.sid}") is None:
                            shop_resp = KmSDK(shop=shop).get_shop_by_user_id(trade.userId)
                            order_shop = Shop.query.filter_by(nick=shop_resp["title"]).first()
                            if order_shop:
                                success = do_create_bo(order_shop, trade.tid, trade.sid)
                                if not success:
                                    logger.error(f"创建工单失败 tid: {trade.tid,} sid: {trade.sid}")
                            else:
                                logger.error(f"快麦店铺 {shop_resp['title']} 无法找到对应的飞梭店铺")
                            cache.set(f"kuaimai_exchange_goods_1108_sid_{trade.sid}", 1, timeout=86400 * 30)
                if not resp.hasNextPage:
                    break
            except Exception as e:
                logger.opt(exception=e).error(
                    f"处理时发生异常， 入参: {page_no} {page_size} {start_time} {end_time} 异常: {str(e)}")
            finally:
                page_no = page_no + 1
        start_ts = next_ts + 1

    cache.set("kuaimai_exchange_goods_1108_ts", now_ts, 86400 * 100)
    return True
