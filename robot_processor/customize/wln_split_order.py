import time
from functools import lru_cache
from typing import Dict

import arrow
from loguru import logger

from robot_processor.business_order.business_order_manager import \
    BusinessManager
from robot_processor.business_order.schema import BusinessOrderSchema, \
    CreateAction
from robot_processor.client import get_buyer_nick, kiosk_client
from robot_processor.enums import Creator, FromType, ErpType
from robot_processor.ext import cache
from robot_processor.form.models import Form
from robot_processor.shop.models import Shop, ErpInfo
from robot_processor.utils import ResultUtil
from rpa.erp.wln import WlnClient, TradeResp, Order
from robot_processor.client import limiter


def strftime(ts):
    time_struct = time.localtime(ts)
    return time.strftime("%Y-%m-%d %H:%M:%S", time_struct)


def forms_by_sid(sid):
    forms = Form.Queries.by_sids([sid]).all()
    return forms


@lru_cache()
def get_sid_by_erp_shop(erp_shop_id: int) -> str:
    resp = kiosk_client.auth_shop_mapping_by_erp_shop(ErpType.JST.name,
                                                      erp_shop_id)
    return resp["channelNo"]


def do_create_bo(shop: Shop, tid: str, logistics_info: str):
    forms = forms_by_sid(shop.sid)
    forms = list(
        filter(lambda form: form.name == '拆单发货-通知发货明细', forms))
    if not forms:
        logger.error(f"在店铺{shop.sid}中找不到工单模版")
        return False
    form = forms[0]
    widgets_info = form.get_form_first_step_widgets_info()
    buyer_nick = '无昵称'
    if shop.platform == 'TAOBAO' or shop.platform == 'TMALL':
        buyer_nick = get_buyer_nick(shop.sid, tid=tid)
    body = BusinessOrderSchema(
        user_nick='飞梭机器人',
        sid=shop.sid,
        form_id=form.id,
        create_action=CreateAction.SUBMIT,
        creator_type=Creator.RPA,
        data={
            widgets_info.get("买家昵称"): buyer_nick,
            widgets_info.get("订单/子订单"): [{"tid": tid}],
            widgets_info.get("物流信息"): logistics_info
        },
        from_type=FromType.LEYAN,
        uid="飞梭机器人",
    )
    res = BusinessManager.create_customize_business_order(shop.org_id, body)
    if ResultUtil.not_success(res):
        print(ResultUtil.get_reason(res))
        return False
    return True


def wln_split_order(org_id: int) -> bool:
    shop = Shop.query.filter_by(org_id=org_id).first()
    assert shop
    erp_info: ErpInfo | None = ErpInfo.query.filter(
        ErpInfo.shop_id == shop.id,
        ErpInfo.erp_type == ErpType.WANLINIU
    ).first()
    if erp_info is None or erp_info.meta is None or not erp_info.meta.get("app_key"):
        logger.error("店铺尚未授权万里牛")
    now = arrow.now()
    last_ts = cache.get(f"wln_split_order_{org_id}_ts")
    if not last_ts:
        logger.warning("没有找到上一次的执行记录，从24个小时前开始恢复")
        last_ts = int(now.shift(hours=-24).timestamp())
    now_ts = int(now.timestamp())
    start_ts = last_ts
    wln_client = WlnClient.init_by_sid(shop.sid)
    order_map: Dict[str, list[Order]] = {}
    start_time = strftime(start_ts)
    end_time = strftime(now_ts)
    page_size = 200
    page_no = 0
    limit_key = "erp:wln:{}".format(erp_info.meta.get("app_key"))  # type: ignore[union-attr]
    while True:
        page_no = page_no + 1
        resp: TradeResp | None = None
        try:
            if limiter.acquire_permission(limit_key, "100/minute"):
                resp = wln_client.query_sent_trades(start_time, end_time,
                                                    page_no, page_size)
        except Exception as e:
            logger.warning(f"万里牛查询订单失败 {e}")
            break
        if not resp:
            continue
        if not resp.data:
            break
        for order in resp.data:
            if (cache.get(
                    f"wln_split_order_{org_id}_trade_no_{order.trade_no}")
                    is
                    None):
                if order.tp_tid in order_map:
                    order_map[order.tp_tid].append(order)
                else:
                    order_map[order.tp_tid] = [order]
    for tp_id in order_map.keys():
        orders = order_map[tp_id]
        logistics_info = "\n".join(
            [f"{order.logistic_name}:{order.express_code}" for
             order in orders])

        sid = orders[0].oln_shop_id
        order_shop = Shop.query.filter_by(sid=sid).first()
        if order_shop:
            success = do_create_bo(order_shop, tp_id, logistics_info)
            if not success:
                logger.error(f"创建工单失败 tid: {tp_id}")
            else:
                logger.info(f"创建工单成功 {tp_id}")
        else:
            logger.error(
                f"租户 {org_id} 万里牛店铺 {sid} 无法找到对应的飞梭店铺")
        for order in orders:
            cache.set(f"wln_split_order_{org_id}_o_id_{order.trade_no}", 1,
                      timeout=86400 * 30)
    cache.set(f"wln_split_order_{org_id}_ts", now_ts, 86400 * 100)
    return True
