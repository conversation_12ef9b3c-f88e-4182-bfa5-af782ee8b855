import time
from functools import lru_cache

import arrow
from loguru import logger

from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.business_order.schema import BusinessOrderSchema, CreateAction
from robot_processor.client import get_buyer_nick, kiosk_client
from robot_processor.customize.create_bo_by_trade_event import MemoAutoBoCreator
from robot_processor.enums import Creator, FromType, ErpType
from robot_processor.ext import cache
from robot_processor.form.models import Form
from robot_processor.shop.models import Shop
from robot_processor.utils import ResultUtil, db_session_rollback
from rpa.erp.jst import JstQmSDK


def strftime(ts):
    time_struct = time.localtime(ts)
    return time.strftime("%Y-%m-%d %H:%M:%S", time_struct)


def forms_by_sid(sid):
    forms = Form.Queries.by_sids([sid]).all()
    return forms


@lru_cache()
def get_sid_by_erp_shop(erp_shop_id: int) -> str:
    resp = kiosk_client.auth_shop_mapping_by_erp_shop(ErpType.JST.name,
                                                      erp_shop_id)
    return resp["channelNo"]


def do_create_bo(shop: Shop, tid: str, o_id: str):
    forms = forms_by_sid(shop.sid)
    forms = list(filter(lambda form: form.name == '异常单(临时快递停发)通知', forms))
    if not forms:
        logger.error(f"在店铺{shop.sid}中找不到工单模版")
        return False
    form = forms[0]
    widgets_info = MemoAutoBoCreator.get_form_first_step_widgets_info(Form.query.get(form.id))
    buyer_nick = '无昵称'
    if shop.platform == 'TAOBAO' or shop.platform == 'TMALL':
        buyer_nick = get_buyer_nick(shop.sid, tid=tid)
    body = BusinessOrderSchema(
        user_nick='飞梭机器人',
        sid=shop.sid,
        form_id=form.id,
        create_action=CreateAction.SUBMIT,
        creator_type=Creator.RPA,
        data={
            widgets_info.get("买家昵称"): buyer_nick,
            widgets_info.get("订单/子订单"): [{"tid": tid}],
            widgets_info.get("聚水潭内部单号"): o_id
        },
        from_type=FromType.LEYAN,
        uid="飞梭机器人",
    )
    res = BusinessManager.create_customize_business_order(shop.org_id, body)
    if ResultUtil.not_success(res):
        return False
    return True


def jst_abnormal_order(org_id: int) -> bool:
    shop = Shop.query.filter_by(org_id=org_id).first()
    assert shop

    now = arrow.now()

    last_ts = cache.get(f"jst_abnormal_order_{org_id}_ts")
    if not last_ts:
        logger.warning("没有找到上一次的执行记录，从1个小时前开始恢复")
        last_ts = int(now.shift(hours=-1).timestamp())

    now_ts = int(now.timestamp())
    start_ts = last_ts
    jst_qm_sdk = JstQmSDK(sid=shop.sid)

    while start_ts <= now_ts:
        next_ts = start_ts + 3600 if start_ts + 3600 <= now_ts else now_ts
        start_time = strftime(start_ts)
        end_time = strftime(next_ts)
        page_size = 100
        page_no = 1
        while True:
            try:
                resp = jst_qm_sdk.query_orders(
                    {"start_time": start_time,
                     "end_time": end_time,
                     "status": "Question"},
                    page_index=str(page_no),
                    page_size=str(page_size)
                )
                for trade in resp.orders:
                    if trade.question_type == "临时快递停发":
                        logger.info(f"检查订单 {trade.so_id} {trade.o_id}")
                        if cache.get(f"jst_abnormal_order_{org_id}_o_id"
                                     f"_{trade.o_id}") is None:
                            try:
                                sid = get_sid_by_erp_shop(trade.shop_id)
                            except KeyError:
                                logger.error(f"通过聚水潭店铺{trade.shop_id}找不到飞梭店铺")
                                continue
                            order_shop = Shop.query.filter_by(sid=sid).first()
                            if order_shop:
                                logger.info(f"创建工单 {trade.so_id} {trade.o_id}")
                                success = do_create_bo(order_shop, trade.so_id, trade.o_id)
                                if not success:
                                    logger.error(f"创建工单失败 tid: {trade.so_id}, o_id: {trade.o_id}")
                                else:
                                    logger.info(f"创建工单成功 {trade.so_id}"
                                                f" {trade.o_id}")
                            else:
                                logger.error(f"租户 {org_id} 聚水潭店铺"
                                             f" {trade.shop_id} "
                                             f"无法找到对应的飞梭店铺")
                            cache.set(f"jst_abnormal_order_{org_id}_o_id_{trade.o_id}", 1, timeout=86400 * 30)
                if len(resp.orders) == 0:
                    break
            except Exception as e:
                logger.opt(exception=e).error(
                    f"处理时发生异常， 入参: {page_no} {page_size} {start_time} {end_time} 异常: {str(e)}")
                db_session_rollback()
            finally:
                page_no = page_no + 1
        start_ts = next_ts + 1

    cache.set(f"jst_abnormal_order_{org_id}_ts", now_ts, 86400 * 100)
    return True
