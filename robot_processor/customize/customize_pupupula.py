from typing import cast, Callable
from enum import Enum, auto
import json

from pydantic import BaseModel
from loguru import logger

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.business_order.business_order_manager import BusinessManager
from robot_processor.db import db
from robot_processor.enums import UserType, FromType
from robot_processor.form.models import Form, WidgetInfo
from robot_processor.kafka_event.trade_event import TradeEventRecord
from robot_processor.shop.models import Shop
from robot_processor.types.common import BuyerInfo
from robot_processor.utils import jmespath_search


class Strategy(Enum):
    IGNORE = auto()
    LOG_ONLY = auto()
    CREATE_ORDER = auto()


class TradeEventConfig(BaseModel):
    form_id: int  # 匹配后需要创建的工单模板
    filter_fn: Callable[[TradeEventRecord], tuple[Strategy, str]]  # 过滤函数


def get_pupupula_config():
    """https://redmine.leyantech.com/issues/610574"""

    def if_need_process(record: TradeEventRecord):
        record = record.copy()
        record["jdp_response"] = json.loads(record["jdp_response"])
        seller_nick_white_list = ["北京小魔怪科技有限公司", "pupupula旗舰店"]
        seller_nick = jmespath_search("seller_nick", record)
        if seller_nick not in seller_nick_white_list:
            return Strategy.IGNORE, "非当前店铺订单，忽略处理"

        status = jmespath_search("status", record)
        if status not in ["AGENCY_SIGNED", "SIGNED", "TRADE_FINISHED"]:
            return Strategy.LOG_ONLY, f"订单状态不符合要求 status={status}"

        payment = jmespath_search(
            "jdp_response.trade_fullinfo_get_response.trade.payment", record
        )
        if float(payment) < 1000:
            return Strategy.LOG_ONLY, f"订单金额不符合要求 payment={payment}"

        return Strategy.CREATE_ORDER, "订单满足创建工单条件"

    return TradeEventConfig(
        form_id=1217129,
        filter_fn=if_need_process,
    )


def process(record: TradeEventRecord):
    tid = record["tid"]
    configs = [get_pupupula_config()]
    for config in configs:
        strategy, reason = config.filter_fn(record)
        if strategy == Strategy.IGNORE:
            continue
        elif strategy == Strategy.LOG_ONLY:
            logger.bind(tid=tid).debug(f"订单不满足创建工单条件, {reason}")
            continue
        else:
            logger.bind(tid=tid).debug("订单满足创建工单条件，创建工单")
            return do_create(record, config)


def do_create(record: TradeEventRecord, config: TradeEventConfig):
    record["jdp_response"] = json.loads(record["jdp_response"])
    shop = db.ro_session.query(Shop).filter(Shop.nick == record["seller_nick"]).first()
    assert shop is not None
    shop = cast(Shop, shop)
    form = db.ro_session.get(Form, config.form_id)
    assert form is not None
    form = cast(Form, form)
    creator = AccountDetailV2(user_id=0, user_type=UserType.RPA, user_nick="飞梭机器人")
    manager = BusinessManager(form, shop, creator, FromType.KAFKA_TRADE_EVENT)
    buyer_info = BuyerInfo(
        open_uid=jmespath_search(
            "jdp_response.trade_fullinfo_get_response.trade.buyer_open_uid", record
        ),
        nick=jmespath_search(
            "jdp_response.trade_fullinfo_get_response.trade.buyer_nick", record
        ),
    )
    data = dict()
    for widget_info_obj in manager.ui_schema_for_create:
        widget_info = WidgetInfo.View.RawStep.parse_obj(widget_info_obj)
        if data_binding := widget_info.option_value.get("data_binding"):
            if value := jmespath_search(data_binding["expression"], record):
                data[widget_info.key] = value

    return manager.pipeline_create_order(data, buyer_info).expect("创建工单失败")


logger.info("加载 pupupula 订单处理策略成功")
