import time

import arrow
from google.protobuf.json_format import MessageToDict
from loguru import logger

from robot_processor.business_order.business_order_manager import \
    BusinessManager
from robot_processor.business_order.schema import BusinessOrderSchema, \
    CreateAction
from robot_processor.client import get_buyer_nick, trade_client
from robot_processor.enums import Creator, FromType
from robot_processor.ext import cache
from robot_processor.form.models import Form
from robot_processor.shop.models import Shop
from robot_processor.utils import ResultUtil


def strftime(ts):
    time_struct = time.localtime(ts)
    return time.strftime("%Y-%m-%d %H:%M:%S", time_struct)


def forms_by_sid(sid):
    forms = Form.Queries.by_sids([sid]).all()
    return forms


def do_create_bo(shop: Shop, tid: str, product_id: str, num: int):
    forms = forms_by_sid(shop.sid)
    forms = list(filter(lambda form: form.name == '阳澄江南-退款重拍', forms))
    if not forms:
        logger.error(f"在店铺{shop.sid}中找不到工单模版")
        return False
    form = forms[0]
    widgets_info = form.get_form_first_step_widgets_info()
    buyer_nick = '无昵称'
    if shop.platform == 'TAOBAO' or shop.platform == 'TMALL':
        buyer_nick = get_buyer_nick(shop.sid, tid=tid)
    body = BusinessOrderSchema(
        user_nick='飞梭机器人',
        sid=shop.sid,
        form_id=form.id,
        create_action=CreateAction.SUBMIT,
        creator_type=Creator.RPA,
        data={
            widgets_info.get("买家昵称"): buyer_nick,
            widgets_info.get("订单/子订单"): [{"tid": tid}],
            widgets_info.get("商品id"): product_id,
            widgets_info.get("商品数量"): float(num)
        },
        from_type=FromType.LEYAN,
        uid="飞梭机器人",
    )
    res = BusinessManager.create_customize_business_order(shop.org_id, body)
    if ResultUtil.not_success(res):
        return False
    return True


def customize_redmine_652891(shop_id: int) -> bool:
    shop = Shop.query.get(shop_id)
    assert shop

    now = arrow.now()
    last_ts = cache.get(f"customize_redmine_652891_{shop_id}_ts")
    if not last_ts:
        logger.warning("没有找到上一次的执行记录，从1个小时前开始恢复")
        last_ts = int(now.shift(hours=-1).timestamp())
    now_ts = int(now.timestamp())
    page_no = 1
    page_size = 100
    start_time = last_ts
    end_time = now_ts
    has_next = True
    while has_next:
        pb_resp = trade_client.get_period_trade_list_by_page(
            page_no,
            page_size,
            start_time,
            end_time,
            platform="DOUDIAN",
            sid=shop.sid
        )
        resp = MessageToDict(pb_resp, preserving_proto_field_name=True,
                             including_default_value_fields=True)
        for trade in resp["trade_info_list"]:
            if trade["dy_trade_info"]["pay_time"]:
                if cache.get(
                        f"customize_redmine_652891_{shop_id}_tid"
                        f"_{trade['dy_trade_info']['order_id']}") is None:
                    do_create_bo(shop,
                                 trade["dy_trade_info"]["order_id"],
                                 ",".join([sku["product_id"] for sku in
                                           trade["dy_trade_info"][
                                               "sku_order_list"]]),
                                 sum([sku["item_num"] for sku in
                                      trade["dy_trade_info"][
                                          "sku_order_list"]]))
                    cache.set(f"customize_redmine_652891_{shop_id}_tid"
                              f"_{trade['dy_trade_info']['order_id']}", 1,
                              86400 * 100)
        page_no = page_no + 1
        if not pb_resp.has_more:
            break

    cache.set(f"customize_redmine_652891_{shop_id}_ts", now_ts, 86400 * 100)
    return True
