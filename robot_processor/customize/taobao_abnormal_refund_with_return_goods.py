import arrow
from loguru import logger

from robot_processor.business_order.business_order_manager import \
    BusinessManager
from robot_processor.business_order.schema import BusinessOrderSchema, \
    CreateAction
from robot_processor.client.aliyun_logistics_client import \
    AliyunLogisticsClient, QueryLogisticsResp
from robot_processor.customize.create_bo_by_trade_event import MemoAutoBoCreator
from robot_processor.enums import Creator, FromType
from robot_processor.ext import cache
from robot_processor.form.models import Form
from robot_processor.refund.models import TaobaoRefund
from robot_processor.refund.schemas import RefundStatus
from robot_processor.shop.models import Shop
from robot_processor.utils import ResultUtil


def forms_by_sid(sid):
    forms = Form.Queries.by_sids([sid]).all()
    return forms


def do_create_bo(shop: Shop, tid: str, order_status: str,
                 refund_status: str, company_name: str, sid: str):
    forms = forms_by_sid(shop.sid)
    forms = list(filter(lambda form: form.name == '异常退货退款', forms))
    if not forms:
        logger.error(f"在店铺{shop.sid}中找不到工单模版")
        return False
    form = forms[0]
    widgets_info = MemoAutoBoCreator.get_form_first_step_widgets_info(
        Form.query.get(form.id))
    body = BusinessOrderSchema(
        user_nick='飞梭机器人',
        sid=shop.sid,
        form_id=form.id,
        create_action=CreateAction.SUBMIT,
        creator_type=Creator.RPA,
        data={
            widgets_info.get("订单/子订单"): [{"tid": tid}],
            widgets_info.get("订单状态"): order_status,
            widgets_info.get("售后状态"): refund_status,
            widgets_info.get("物流公司"): company_name,
            widgets_info.get("物流单号"): sid
        },
        from_type=FromType.LEYAN,
        uid="飞梭机器人",
    )
    res = BusinessManager.create_customize_business_order(shop.org_id, body)
    if ResultUtil.not_success(res):
        return False
    return True


def create_bo(shop: Shop, org_id: int, refund_id: str, tid: str,
              order_status: str, refund_status: str, company_name: str,
              sid: str):
    is_success = do_create_bo(shop, tid, order_status, refund_status,
                              company_name, sid)
    if is_success:
        cache.set(f"taobao_abnormal_refund_with_return_goods_"
                  f"{org_id}_o_id_{refund_id}",
                  1, 86400 * 30)
        logger.info(f"创建工单成功 refund_id: {refund_id}")
    else:
        logger.error(
            f"创建工单失败 refund_id: {refund_id}")


def is_stop(resp: QueryLogisticsResp):
    if resp.msg == '没有信息' or len(resp.result.list) == 0:
        return True
    return False


def is_not_same_return_address(resp: QueryLogisticsResp):
    if any('已签收' in logistic.status for logistic in resp.result.list):
        return not any(
            ('已签收' in logistic.status or '派件' in logistic.status or
             '到达' in logistic.status or
             '派送' in logistic.status) and '南昌' in logistic.status for
            logistic in resp.result.list)
    return False


def is_before(good_return_time: str):
    return arrow.get(good_return_time).shift(days=3) <= arrow.now()


def taobao_abnormal_refund_with_return_goods(org_id: int) -> bool:
    # 仓库已签收未退款的订单
    shops = Shop.query.filter_by(org_id=org_id).all()
    shops = [shop for shop in shops if shop.is_taobao()]
    assert shops

    now = arrow.now()
    now_ts = int(now.timestamp())
    start_ts = now_ts - 86400 * 4
    next_ts = now_ts
    seller_nicks = [shop.nick for shop in shops]

    time_range = (
        arrow.get(start_ts).datetime,
        arrow.get(next_ts).datetime
    )
    taobao_refund_list: list[TaobaoRefund] = TaobaoRefund.query.filter(
        TaobaoRefund.seller_nick.in_(seller_nicks),
        TaobaoRefund.jdp_modified.between(*time_range)
    ).all()
    refund_info_list = [refund.get_refund_info() for refund in
                        taobao_refund_list]
    for refund_info in refund_info_list:
        if refund_info.dispute_type != "REFUND_AND_RETURN":
            continue
        if refund_info.status not in [RefundStatus.SUCCESS,
                                      RefundStatus.WAIT_SELLER_CONFIRM_GOODS]:
            continue
        if not refund_info.sid:
            continue
        if cache.get(
                f"taobao_abnormal_refund_with_return_goods_{org_id}_o_id_{refund_info.refund_id}") is None:
            shop = [shop for shop in shops if shop.nick ==
                    refund_info.seller_nick][0]
            resp = AliyunLogisticsClient().query_logistics(
                refund_info.sid)
            if (resp.status == '0') or (resp.status == '205' and resp.msg
                                        == '没有信息'):
                if ((refund_info.status == RefundStatus.SUCCESS and
                     is_stop(resp)) or (is_not_same_return_address(resp))
                        or (is_stop(resp) and is_before(
                            refund_info.good_return_time))):
                    create_bo(shop, org_id, refund_info.refund_id,
                              refund_info.tid,
                              refund_info.order_status.label,
                              refund_info.status.label,
                              refund_info.company_name,
                              refund_info.sid)
                if not is_not_same_return_address(resp):
                    # 有物流轨迹，且已签收，且收货地址为目标地址，不需要再轮询.
                    cache.set(f"taobao_abnormal_refund_with_return_goods_"
                              f"{org_id}_o_id_{refund_info.refund_id}",
                              1, 86400 * 30)
    return True
