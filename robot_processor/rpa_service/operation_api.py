"""
运营中台house调用
"""
from typing import List, Optional

import sqlalchemy as sa
from flask import Blueprint, jsonify
from robot_processor.currents import g
from pydantic import BaseModel, Field

from robot_processor.utils import unwrap_optional
from robot_processor.validator import validate

from robot_processor.decorators import admin_user_required
from robot_processor.enums import JobType
from robot_processor.ext import cache, db
from robot_processor.form.schemas import RpaArgOutputSchema, RpaArgInputSchema
from robot_processor.rpa_service.enums import RpaStatusEnum
from robot_processor.rpa_service.models import Rpa, RpaLimit, RpaRuleType
from robot_processor.job import all_auto_job_output_schemas, all_auto_job_controllers
from pydantic import root_validator
import copy


operation_api = Blueprint("robot-operation-api", __name__, url_prefix="/v1/operation")


class AllReq(BaseModel):
    search: Optional[str] = None
    tag: Optional[str] = None
    status: Optional[RpaStatusEnum] = None
    page_no: int = 1
    page_size: int = 10


class Argument(BaseModel):
    """
    新版RPA argument字段，不支持mixed
    """
    input: List[RpaArgInputSchema]
    output: List[RpaArgOutputSchema]


class ReadRpaLimitSchema(BaseModel):
    id: int
    rpa_id: int
    rule_type: RpaRuleType
    rule_value: str

    class Config:
        orm_mode = True


class CreateRpaLimitSchema(BaseModel):
    rule_type: RpaRuleType
    rule_value: str = Field(max_length=64)


class ListRpaSchema(BaseModel):
    id: int = Field(description="自动化应用 ID")
    name: str = Field(description="自动化应用名称")
    description: Optional[str] = Field(description="自动化应用描述", default=None)
    tag: Optional[str] = Field(description="自动化应用标签", default=None)
    guid: str = Field(description="唯一ID")
    task: JobType = Field(description="job task type")
    can_auto_retry: bool = Field(description="是否支持自动重试")
    status: RpaStatusEnum = Field(description="rpa应用发布状态")
    created_at: int
    updated_at: int
    creator: Optional[str]
    updater: Optional[str]

    class Config:
        orm_mode = True


class GetOutputSchema(BaseModel):
    task: Optional[JobType]
    rpa_id: Optional[int]

    @root_validator
    def validate_task_and_rpa_id(cls, values):
        task = values.get('task')
        rpa_id = values.get('rpa_id')
        if task is None and rpa_id is None:
            raise ValueError("task和rpa_id不能同时为空")
        return values


class DetailRpaSchema(ListRpaSchema):
    ext_info: "Rpa.Schema.ExtraInfo" = Field(
        default_factory=lambda: Rpa.Schema.ExtraInfo()
    )
    limits: List[ReadRpaLimitSchema]
    input: List[RpaArgInputSchema]


class CreateRpaSchema(BaseModel):
    name: str = Field(description="自动化应用名称")
    description: Optional[str] = Field(description="自动化应用描述", default=None)
    tag: Optional[str] = Field(description="自动化应用标签", default=None)
    guid: str = Field(description="唯一ID")
    ext_info: "Rpa.Schema.ExtraInfo" = Field(
        default_factory=lambda: Rpa.Schema.ExtraInfo()
    )
    task: JobType = Field(description="job task type")
    can_auto_retry: bool = Field(description="是否支持自动重试")
    limits: List[CreateRpaLimitSchema] = Field(description="限制", default=list)  # type: ignore[assignment]
    input: List[RpaArgInputSchema]
    output: List[RpaArgOutputSchema]


class UpdateRpaSchema(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    tag: Optional[str] = None
    guid: Optional[str] = None
    ext_info: Optional[Rpa.Schema.ExtraInfo] = None
    task: Optional[JobType] = None
    can_auto_retry: Optional[bool] = None
    limits: Optional[List[CreateRpaLimitSchema]] = None
    input: Optional[List[RpaArgInputSchema]] = None
    output: Optional[List[RpaArgOutputSchema]]
    status: Optional[RpaStatusEnum] = None


@operation_api.get('/rpa/tags')
@admin_user_required
def list_all_tags():
    """运营中台查询 RPA 应用标签"""
    cache_key = "robot_processor.rpa.all-tag-list"
    rpa_tag = cache.get(cache_key)
    if not rpa_tag:
        rpa_tag = [dict(name=name) for name in Rpa.Queries.all_tags]
        cache.set(cache_key, rpa_tag, timeout=-1)
    return jsonify(
        succeed=True,
        data=rpa_tag
    )


@operation_api.get("/rpa/tasks")
@admin_user_required
def list_all_rpa_tasks():
    """
    运营中台查询 RPA 应用task_type列表
    """
    return jsonify(
        success=True,
        data=[x.value for x in all_auto_job_controllers.keys()]
    )


@operation_api.get('/rpa')
@admin_user_required
@validate
def list_all_rpas(query: AllReq):
    """运营中台获取所有自动化应用"""
    stmt = Rpa.Queries.present().order_by(
        Rpa.updated_at.desc()
    )
    if query.search:
        conditions = [
            Rpa.name.like(f'%{query.search}%'),
            Rpa.task.like(f'%{query.search}%'),
        ]
        stmt = stmt.filter(
            sa.or_(*conditions)
        )
    if query.tag:
        stmt = stmt.filter(Rpa.tag == query.tag)
    if query.status:
        stmt = stmt.filter(Rpa.status == query.status)
    pagination = stmt.paginate(page=query.page_no, per_page=query.page_size)
    items = [ListRpaSchema.from_orm(x).dict() for x in pagination.items]
    return jsonify(
        success=True,
        data=items,
        page_no=pagination.page,
        page_size=pagination.per_page,
        total=pagination.total
    )


@operation_api.get("/rpa/<int:rpa_id>")
@admin_user_required
def get_rpa_detail(rpa_id: int):
    instance: Optional[Rpa] = Rpa.Queries.present().filter(
        Rpa.id == rpa_id
    ).first()
    if not instance:
        return jsonify(
            success=False,
            error=f"Unable to find rpa: {rpa_id}"
        )
    # rpa argument->output字段有两个版本
    # rpa输出改造只支持新版output
    detail = DetailRpaSchema(
        id=instance.id,
        name=instance.name,
        description=instance.description,
        tag=instance.tag,
        guid=instance.guid,
        task=instance.task,
        can_auto_retry=instance.can_auto_retry,
        status=instance.status,
        ext_info=Rpa.Schema.ExtraInfo.parse_obj(instance.ext_info),
        input=[RpaArgInputSchema.parse_obj(x) for x in instance.input],
        limits=[ReadRpaLimitSchema.from_orm(x) for x in instance.limits],
        created_at=instance.created_at,
        updated_at=instance.updated_at,
        creator=instance.creator,
        updater=instance.updater
    )
    return jsonify(
        success=True,
        data=detail
    )


@operation_api.get("/rpa/output")
@validate
@admin_user_required
def get_output_args_by_task(query: GetOutputSchema):
    rpa: Optional[Rpa] = None
    if query.rpa_id:
        rpa = Rpa.Queries.present().filter(
            Rpa.id == query.rpa_id
        ).first()
        if not rpa:
            return jsonify(
                success=False,
                error=f"找不到RPA: {query.rpa_id}"
            )
        task = rpa.task
    else:
        task = unwrap_optional(query.task)
    if task not in all_auto_job_output_schemas:
        return jsonify(
            success=False,
            error=f"{task}暂未完成rpa输出改造，无法获取输出参数"
        )
    output = copy.deepcopy(all_auto_job_output_schemas[task])  # type: ignore[index]
    if rpa:
        output = _merge_output(rpa, output)
    return jsonify(
        success=True,
        output=output
    )


@operation_api.post("/rpa")
@validate
@admin_user_required
def create_rpa(body: CreateRpaSchema):
    """
    创建rpa记录
    """
    user = g.login_user_detail
    assert user is not None
    body.output = _filter_selected_fields(body.output)
    body.input = [RpaArgInputSchema.generate_constants(x) for x in body.input]
    instance = Rpa(
        name=body.name,
        description=body.description,
        tag=body.tag,
        guid=body.guid,
        ext_info=body.ext_info.dict(),
        can_auto_retry=body.can_auto_retry,
        task=body.task.value,
        argument={
            "input": body.dict()["input"],
            "output": body.dict()["output"]
        },
        creator=user.user_nick
    )
    db.session.add(instance)
    db.session.flush()
    if body.limits:
        _create_limits_for_rpa(instance, body.limits)
    db.session.commit()
    return jsonify(
        success=True,
        data=ListRpaSchema.from_orm(instance).dict(include={"id"})
    )


@operation_api.put("/rpa/<int:rpa_id>")
@validate
@admin_user_required
def update_rpa(rpa_id: int, body: UpdateRpaSchema):
    """
    更新rpa，包括上线/下线
    """
    assert g.login_user_detail is not None
    if body.output is not None:
        body.output = _filter_selected_fields(body.output)
    if body.input is not None:
        body.input = [RpaArgInputSchema.generate_constants(x) for x in body.input]
    instance: Optional[Rpa] = Rpa.Queries.present().filter(
        Rpa.id == rpa_id
    ).first()
    if not instance:
        return jsonify(
            success=False,
            error=f"Unable to find rpa: {rpa_id}"
        )
    data = body.dict()
    # limits单独处理
    data.pop("limits", None)
    for k, v in data.items():
        if v is not None:
            setattr(instance, k, v)
    if body.limits is not None:
        _create_limits_for_rpa(instance, body.limits)
    instance.updater = g.login_user_detail.user_nick
    db.session.commit()
    return jsonify(
        success=True,
        data=ListRpaSchema.from_orm(instance).dict(include={"id"})
    )


@operation_api.delete("/rpa/<int:rpa_id>")
@admin_user_required
def delete_rpa(rpa_id: int):
    """
    删除rpa，仅支持删除已下线的rpa
    """
    assert g.login_user_detail is not None
    rpa: Optional[Rpa] = Rpa.Queries.present().filter(
        Rpa.id == rpa_id
    ).first()
    if not rpa:
        return jsonify(
            success=True,
            error=None
        )
    if rpa.status != RpaStatusEnum.OFFLINE:
        return jsonify(
            success=False,
            error="Only offline rpa can be deleted."
        )
    rpa.status = RpaStatusEnum.DELETED
    rpa.updater = g.login_user_detail.user_nick
    db.session.commit()
    return jsonify(
        success=True,
        error=None
    )


def _create_limits_for_rpa(rpa: Rpa, limits: List[CreateRpaLimitSchema]):
    # 先检查老的limits和新limits是否一致
    old = {(x.rule_type, x.rule_value) for x in rpa.limits}
    new = {(x.rule_type, x.rule_value) for x in limits}
    if old == new:
        return
    # 删除之前的limits
    del_stmt = RpaLimit.__table__.delete().where(RpaLimit.rpa_id == rpa.id)  # type: ignore[attr-defined]
    db.session.execute(del_stmt)
    # 新建limits
    data = [RpaLimit(
        rpa_id=rpa.id,
        rule_type=limit.rule_type,
        rule_value=limit.rule_value
    ) for limit in limits]
    db.session.bulk_save_objects(data)


def _merge_output(rpa: Rpa, generated_output: List[RpaArgOutputSchema]) -> List[RpaArgOutputSchema]:
    try:
        saved_output = [RpaArgOutputSchema.parse_obj(x) for x in rpa.output]
    except ValueError:
        # 无法解析output，表明output是老版schema
        saved_output = []
    _set_selected(generated_output, saved_output)
    return generated_output


def _set_selected(all_fields: List[RpaArgOutputSchema],
                  saved_fields: List[RpaArgOutputSchema]):
    save_fields_mapper = {x.name: x for x in saved_fields}
    for field in all_fields:
        saved_field = save_fields_mapper.get(field.name)
        if saved_field:
            field.selected = _is_same_field(field, saved_field)
            _merge_post_defined_fields(field, saved_field)
        else:
            field.selected = False
        saved_field_children = [] if not saved_field or not saved_field.children else saved_field.children
        if field.children:
            # table类型，需要解析子字段
            _set_selected(field.children, saved_field_children)


def _filter_selected_fields(fields: List[RpaArgOutputSchema]) -> List[RpaArgOutputSchema]:
    """
    移除未选择的输出字段，用于创建、更新rpa output时使用
    """
    ret = []
    for field in fields:
        if field.selected:
            ret.append(field)
        if field.children:
            field.children = _filter_selected_fields(field.children)
    return ret


def _is_same_field(generated: RpaArgOutputSchema, saved: RpaArgOutputSchema):
    return (generated.name == saved.name  # key相同
            and generated.default_type == saved.default_type  # 类型相同
            and generated.data_binding.expression == saved.data_binding.expression  # 层级相同
            )


def _merge_post_defined_fields(generated: RpaArgOutputSchema, saved: RpaArgOutputSchema):
    # label和desc是后续定义的，只会存储在数据库里，不会在代码中有体现，所以需要合并数据
    generated.label = saved.label
    generated.desc = saved.desc
