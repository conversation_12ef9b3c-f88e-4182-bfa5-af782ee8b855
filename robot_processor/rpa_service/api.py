from typing import List, Optional

from flask import Blueprint
from pydantic import BaseModel
from sqlalchemy.orm import Load

from robot_processor.base_schemas import Response
from robot_processor.client import action_client
from robot_processor.currents import g
from robot_processor.decorators import org_required, service_required
from robot_processor.ext import cache
from robot_processor.rpa_service.models import Rpa, RpaConfig
from robot_processor.utils import GenericResponse
from robot_processor.validator import validate
from robot_processor.rpa_service.enums import RpaConfigCategory
from robot_processor.rpa_service.schemas import RpaConfigSchema
from robot_processor.ext import db
from robot_processor.shop.models import Shop

api = Blueprint("robot-service-api", __name__, url_prefix="/v1/service")


class ListRpaRequest(BaseModel):
    tag: Optional[str] = None


class RpaList(BaseModel):
    rpa: List[Rpa.View.RpaList]


ListRpaResponse = GenericResponse[RpaList]


class SubscribeReq(BaseModel):
    rpa: List[int]  # 订阅的 RPA 列表


class SubscribedItem(BaseModel):
    id: int
    name: str


class SubscribeRes(BaseModel):
    subscribed: List[SubscribedItem]


class UnSubscribeRes(BaseModel):
    unsubscribed: List[SubscribedItem]


class CreateOrUpdateRpaConfigsRequest(BaseModel):
    config: RpaConfigSchema


class GetRpaConfigsRequest(BaseModel):
    sids: list[str]
    category: RpaConfigCategory


class ReferBy(BaseModel):
    form_id: int
    form_name: str
    step_id: int
    step_name: str
    sid: str
    shop_title: str
    shop_platform: str


class Refer(BaseModel):
    id: int
    name: str
    has_running_business_order: bool
    refer_by: List[ReferBy]


class UnSubscribeDryRunRes(BaseModel):
    referred: List[Refer]


@api.get('/rpa/tags')
@validate
def list_tags() -> Response[dict]:
    """查询 RPA 应用标签"""
    cache_key = "robot_processor.rpa.tag-list"
    rpa_tag = cache.get(cache_key)
    if not rpa_tag:
        rpa_tag = [dict(name=name) for name in Rpa.Queries.online_tags]
        cache.set(cache_key, rpa_tag, timeout=-1)
    return Response(dict(tag=rpa_tag))


def sort_rpa_list(rpa_list: List[Rpa.View.RpaList]) -> List[Rpa.View.RpaList]:
    def get_rpa_sort_key(rpa):
        tag_list = ["平台相关", "发消息", "支付交易", "物流系统对接", "ERP相关", "通用类"]
        if rpa.tag in tag_list:
            return tag_list.index(rpa.tag)
        return 99

    # 产品设计需求：已订阅/可订阅的应用排序靠前，已订阅/可订阅的自动化应用按照标签排序
    available = sorted([
        rpa for rpa in rpa_list if rpa.subscribe_info.available
    ], key=get_rpa_sort_key)
    unavailable = [rpa for rpa in rpa_list if not rpa.subscribe_info.available]
    return available + unavailable


@api.get('/rpa')
@org_required
@validate
def list_all_subscribed_rpas(query: ListRpaRequest) -> ListRpaResponse:
    """获取当前页面下的自动化应用"""
    rpa_list: List[Rpa] = Rpa.Queries.by_tag(query.tag).options(
        Load(Rpa).load_only(Rpa.id, Rpa.name, Rpa.description, Rpa.tag, Rpa.ext_info)
    ).all()

    tasks = []
    for rpa in rpa_list:
        subscribe_info = Rpa.Utils.get_org_subscribe_info(rpa=rpa, org_id=str(g.auth.org_id))
        if subscribe_info.need_hidden:  # 不满足租户限制的，不会在页面上展示
            continue
        view = Rpa.View.RpaList.from_(rpa=rpa, subscribe_info=subscribe_info)
        tasks.append(view)
    tasks = sort_rpa_list(tasks)
    return ListRpaResponse.Success(RpaList(rpa=tasks))


@api.get('/rpa/recommend')
@org_required
@validate
def top_10() -> ListRpaResponse:
    """获取 Top10 的自动化应用"""
    import json
    from robot_processor.client.conf import app_config

    top10_name = json.loads(app_config.RPA_TOP10)
    rpa_list: List[Rpa] = Rpa.Queries.by_names(names=top10_name).options(
        Load(Rpa).load_only(Rpa.id, Rpa.name, Rpa.description, Rpa.tag, Rpa.ext_info)
    ).all()

    tasks = []
    for rpa in rpa_list:
        subscribe_info = Rpa.Utils.get_org_subscribe_info(rpa=rpa, org_id=str(g.auth.org_id))
        if subscribe_info.need_hidden:  # 不满足租户限制的，不会在页面上展示
            continue
        view = Rpa.View.RpaList.from_(rpa=rpa, subscribe_info=subscribe_info)
        tasks.append(view)
    tasks = sorted(tasks, key=lambda rpa: top10_name.index(rpa.name))
    return ListRpaResponse.Success(RpaList(rpa=tasks))


@api.post('/rpa/subscribe')
@org_required
@validate
def subscribe(body: SubscribeReq) -> Response[SubscribeRes]:
    subscribe_log = []  # 最后打包记录操作日志
    response = SubscribeRes(subscribed=[])
    rpa_list: List[Rpa] = (Rpa.Queries.by_ids(body.rpa)
                           .options(Rpa.Options.defer_argument)
                           .all())
    assert g.login_user_detail
    for rpa in rpa_list:
        Rpa.Utils.subscribe(rpa=rpa, org_id=g.org_id)
        subscribe_log.append(rpa.create_operation_log(
            org_id=g.org_id, sid=g.auth.store_id or '', user=g.login_user_detail.user_nick or '',
            operator="subscribe", from_type="UserSubscribe"
        ))
        response.subscribed.append(SubscribedItem(id=rpa.id, name=rpa.name))

    for log in subscribe_log:
        action_client.create_action_log_by_kafka(log)
    return Response(response)


@api.post('/rpa/unsubscribe')
@org_required
@validate
def unsubscribe(body: SubscribeReq) -> Response[UnSubscribeRes]:
    assert g.login_user_detail
    user_nick = g.login_user_detail.user_nick
    unsubscribe_log = []
    response = UnSubscribeRes(unsubscribed=[])

    rpa_list: List[Rpa] = (Rpa.Queries.by_ids(body.rpa)
                           .options(Rpa.Options.defer_argument)
                           .all())
    for rpa in rpa_list:
        Rpa.Utils.unsubscribe(rpa=rpa, org_id=g.org_id, update_user=user_nick)
        unsubscribe_log.append(rpa.create_operation_log(
            org_id=g.org_id, sid=g.auth.store_id or '', user=user_nick or '',
            operator="unsubscribe", from_type="UserSubscribe"))
        response.unsubscribed.append(SubscribedItem(id=rpa.id, name=rpa.name))

    for log in unsubscribe_log:
        action_client.create_action_log_by_kafka(log)
    return Response(response)


@api.post('/rpa/unsubscribe/dry-run')
@org_required
@validate
def unsubscribe_dry_run(body: SubscribeReq) -> Response[UnSubscribeDryRunRes]:
    rpa_list = Rpa.Queries.by_ids(body.rpa).options(Rpa.Options.defer_argument).all()
    response = UnSubscribeDryRunRes(referred=[])
    for rpa in rpa_list:
        refer_by_list = Rpa.Utils.refer_by_scope_org(rpa=rpa, org_id=g.org_id)
        rpa_refer_info = Refer(
            id=rpa.id,
            name=rpa.name,
            has_running_business_order=False,
            refer_by=[ReferBy(
                form_id=refer_by.form.id,
                form_name=refer_by.form.name,
                step_id=refer_by.step.id,
                step_name=refer_by.step.name,
                sid=refer_by.form.shop.sid,
                shop_title=refer_by.form.shop.title,
                shop_platform=refer_by.form.shop.platform,
            ) for refer_by in refer_by_list]
        )
        response.referred.append(rpa_refer_info)

    return Response(response)


@api.post("/rpa/configs")
@org_required
@validate
def create_or_update_rpa_configs(body: CreateOrUpdateRpaConfigsRequest):
    """
    新增、更新 RPA 客户端配置。
    :param body:
    :return:
    """
    rpa_config: RpaConfig | None = RpaConfig.query.filter(
        RpaConfig.org_id == g.org_id,
    ).first()
    if rpa_config is not None:
        rpa_config.config = body.config.dict()
    else:
        rpa_config = RpaConfig(
            org_id=g.org_id,
            config=body.config.dict()
        )
    db.session.add(rpa_config)
    db.session.commit()
    return Response.Success(data=rpa_config.config)


@api.get("/rpa/configs")
@org_required
def get_rpa_configs():
    rpa_config: RpaConfig | None = RpaConfig.query.filter(
        RpaConfig.org_id == g.org_id,
    ).first()
    if rpa_config is None:
        return Response.Success(data={})
    return Response.Success(data=rpa_config.config)


@api.get("/rpa/shop_config")
@service_required
@validate
def query_rpa_config_by_conditions(query: GetRpaConfigsRequest):
    # 获取所有 sid 对应的、有效的 org_id 和 sid 信息。直接走索引，无需回表。
    shops = Shop.query.with_entities(Shop.sid, Shop.org_id).filter(
        Shop.sid.in_(query.sids)
    ).all()
    if len(shops) == 0:
        return Response.Success(data={})
    org_shop_mapping: dict[str, list[str]] = {}
    for shop in shops:
        org_shops = org_shop_mapping.get(shop.org_id) or []
        org_shops.append(shop.sid)
        org_shop_mapping.update({shop.org_id: org_shops})

    rpa_config_instances = RpaConfig.query.filter(
        RpaConfig.org_id.in_(list(org_shop_mapping.keys()))
    ).all()
    if len(rpa_config_instances) == 0:
        return Response.Success(data={})
    # 格式化配置信息。
    rpa_configs: dict[str, RpaConfigSchema] = {
        r.org_id: RpaConfigSchema.parse_obj(r.config) for r in rpa_config_instances
    }

    result = {}

    for org_id, rpa_config in rpa_configs.items():
        sids = org_shop_mapping.get(org_id) or []
        match query.category:
            case RpaConfigCategory.qianniu_deliver_message:
                result.update(rpa_config.get_qianniu_deliver_message_config_by_sids(sids))
            case RpaConfigCategory.pdd_deliver_message:
                result.update(rpa_config.get_pdd_deliver_message_config_by_sids(sids))
    return Response.Success(data=result)
