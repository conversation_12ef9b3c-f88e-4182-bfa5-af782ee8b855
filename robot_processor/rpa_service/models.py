import json
import time
from enum import Enum
from enum import StrEnum
from enum import auto
from typing import List
from typing import Literal
from typing import Named<PERSON><PERSON><PERSON>
from typing import Optional
from typing import cast

import sqlalchemy as sa
from loguru import logger
from more_itertools import first
from pydantic import BaseModel
from pydantic import Field
from pydantic import validator
from robot_extension.util.decorator import classproperty
from robot_extension.util.mixin import LabeledEnum
from sqlalchemy import select
from sqlalchemy.orm import Load
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy_utils import ChoiceType

from robot_processor.db import BasicMixin
from robot_processor.db import DbBaseModel
from robot_processor.db import in_transaction
from robot_processor.ext import db
from robot_processor.rpa_service.enums import RpaStatusEnum
from robot_processor.shop.models import Shop
from robot_processor.utils import omit_none_fields_before_validate
from robot_processor.utils import unwrap_optional


class SubscribeModel(str, LabeledEnum):
    RPA = "RPA", "自动化应用"


class SubscribeOperator(str, LabeledEnum):
    SUBSCRIBE = "SUBSCRIBE", "订阅"
    UNSUBSCRIBE = "UNSUBSCRIBE", "取消订阅"
    # RENEW = "RENEW", "续订"


class RpaSource(StrEnum):
    """Rpa 的来源，用于 Rpa.guid 前缀"""

    fs = "fs"  # "飞梭"
    rpa = "rpa"  # "rpa"


class RpaRuleType(StrEnum):
    ERP = "ERP"  # "依赖 ERP 能力的 RPA"
    Platform = "PLATFORM"  # "平台限定 RPA"
    Org = "Org"  # "限定租户使用的 RPA"
    Pay = "PAY"  # "收费的 RPA"
    TradetimeLimit = "TradetimeLimit"  # "受订单3个月限制影响"


def _default_rpa_updater(context):
    return context.get_current_parameters()["creator"]


class Rpa(DbBaseModel):

    __table_args__ = (sa.Index("ix_guid", "guid"),)
    created_at: Mapped[int] = mapped_column(sa.Integer, default=time.time)
    updated_at: Mapped[int] = mapped_column(sa.Integer, default=time.time, onupdate=time.time)

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    # 任务名称
    name: Mapped[str | None] = mapped_column(sa.String(32))
    # 任务描述
    description: Mapped[str | None] = mapped_column(sa.Text)
    # 任务标签
    tag: Mapped[str | None] = mapped_column(sa.String(64))
    # 全局唯一标识
    guid: Mapped[str | None] = mapped_column(sa.String(128))
    # 扩展信息
    # todo RpaExtInfo
    ext_info: Mapped[dict | None] = mapped_column(sa.JSON)
    # 是否自动重试
    can_auto_retry: Mapped[bool] = mapped_column(sa.Boolean, default=False)
    # 关联任务和实现
    task: Mapped[str | None] = mapped_column(sa.String(64))
    status: Mapped[RpaStatusEnum] = mapped_column(
        ChoiceType(choices=RpaStatusEnum, impl=sa.String(32)), default=RpaStatusEnum.OFFLINE
    )  # noqa: E501
    argument: Mapped[dict] = mapped_column(sa.JSON, default=lambda: dict(input=[], output=[], mixed=[]))

    # creator
    creator: Mapped[str | None] = mapped_column(sa.String(64), default=None, nullable=True)

    # updater
    updater: Mapped[str | None] = mapped_column(sa.String(64), default=_default_rpa_updater, nullable=True)

    limits: Mapped[list["RpaLimit"]] = relationship(
        lambda: RpaLimit, back_populates="rpa", cascade="all, delete-orphan"
    )

    @classmethod
    def gen_guid(cls, source: RpaSource = RpaSource.fs, identity=None):
        from uuid import uuid4

        tmpl = "{prefix}:{identity}"
        identity = identity or uuid4()

        return tmpl.format(prefix=source, identity=identity)

    @property
    def arguments(self):
        """ "flatten argument"""
        return [
            *self.argument.get("input", []),
            *self.argument.get("output", []),
            *self.argument.get("mixed", []),
        ]

    @property
    def input(self) -> List[dict]:
        return self.argument.get("input", [])

    @input.setter
    def input(self, value: List[dict]):
        self.argument["input"] = value
        flag_modified(self, "argument")

    @property
    def output(self) -> List[dict]:
        return self.argument.get("output", [])

    @output.setter
    def output(self, value: List[dict]):
        self.argument["output"] = value
        flag_modified(self, "argument")

    @property
    def rpa_client_metadata(self):
        import re

        if match := re.match(r"rpa_client:(?P<template_id>\d+):(?P<ident>\S+)", self.guid):  # type: ignore[arg-type]
            return self.Schema.RpaClientMetadata(**match.groupdict())

    def create_operation_log(
        self,
        org_id: str,
        sid: str,
        user: str,
        operator: Literal["subscribe", "unsubscribe"],
        from_type: Literal["UserSubscribe", "TaskTemplate"],
        form_name: str = "",
    ):
        raw_json = dict(rpa_id=self.id, rpa_name=self.name, from_type=from_type)
        if form_name:
            raw_json["form_name"] = form_name

        return dict(
            org_id=org_id,
            sid=sid,
            user=user,
            label=self.name,
            operator=operator,
            model="rpa_service",
            object_id=str(self.id),
            operate_ts=int(time.time()),
            raw_json=json.dumps(raw_json, ensure_ascii=False),
        )

    class Utils:
        @staticmethod
        @in_transaction()
        def subscribe(rpa: "Rpa", org_id: str):
            from robot_processor.client import rpa_client
            from robot_processor.shop.models import SubscribeInfo

            # 记录订阅信息
            subscribe_record = SubscribeInfo()
            subscribe_record.org_id = org_id
            subscribe_record.subscribe_model = SubscribeModel.RPA  # type: ignore[assignment]
            subscribe_record.subscribe_operator = SubscribeOperator.SUBSCRIBE  # type: ignore[assignment]
            subscribe_record.model_target = rpa.id
            db.session.add(subscribe_record)

            # 初始化租户 RPA 订阅信息
            rpa_context = RpaContext.Queries.by_org_rpa_id(org_id, rpa.id).first()
            if rpa_context is None:
                rpa_context = RpaContext()
                rpa_context.org_id = org_id
                rpa_context.rpa_id = rpa.id
                db.session.add(rpa_context)
            rpa_context.enabled = True

            # 初始化 RPA 客户端的运行时上下文
            if not rpa.rpa_client_metadata:
                # 没有 rpa client metadata，不需要请求rpa control初始化workflow
                return
            if not (workflow_id := (rpa_context.common or {}).get("workflow_id")):
                template_id = rpa.rpa_client_metadata.template_id
                res = rpa_client.subscribe_workflow(template_id=template_id, org_id=int(org_id))
                if not res.success:
                    err_msg = res.error
                    logger.error(f"subscribe rpa_client failed. {template_id=}, {org_id=}, {err_msg=}")
                    return
                workflow_id = unwrap_optional(res.data).workflow_id
                rpa_context.common = {**(rpa_context.common or {}), "workflow_id": workflow_id}
            rpa_client.enable_workflow(workflow_id)

        @staticmethod
        @in_transaction()
        def unsubscribe(rpa: "Rpa", org_id: str, update_user: Optional[str] = None):
            from robot_processor.client import rpa_client
            from robot_processor.shop.models import SubscribeInfo

            # 记录订阅信息
            subscribe_record = SubscribeInfo()
            subscribe_record.org_id = org_id
            subscribe_record.subscribe_model = SubscribeModel.RPA  # type: ignore[assignment]
            subscribe_record.subscribe_operator = SubscribeOperator.UNSUBSCRIBE  # type: ignore[assignment]
            subscribe_record.model_target = rpa.id
            db.session.add(subscribe_record)

            # 初始化租户 RPA 订阅信息
            rpa_context = RpaContext.Queries.by_org_rpa_id(org_id, rpa.id).first()
            if rpa_context:
                rpa_context.enabled = False

            if not rpa.rpa_client_metadata:
                return
            if rpa_context and (workflow_id := (rpa_context.common or {}).get("workflow_id")):
                rpa_client.disable_workflow(workflow_id)

            # 清理工单模板引用
            refer_by_list = Rpa.Utils.refer_by_scope_org(rpa, org_id)
            for refer_by in refer_by_list:
                step = refer_by.step
                step.update(update_user=update_user, task_id=None)

        @staticmethod
        def get_broken_rpa_limits_for_shop(rpa: "Rpa", shop: "Shop") -> list[RpaRuleType]:
            """检查店铺是否满足 rpa 的 RuleType.Org 和 RuleType.Platform 要求"""

            rpa_limits = cast(List[RpaLimit], rpa.limits)
            break_limits = []
            # 检查 platform
            platform_limits = RpaLimit.Utils.extract_specified_rule_type_in_limits(rpa_limits, RpaRuleType.Platform)
            if RpaLimit.Utils.check(platform_limits, [shop.platform]).is_failed:
                break_limits.append(RpaRuleType.Platform)
            # 检查 erp
            erp_limits = RpaLimit.Utils.extract_specified_rule_type_in_limits(rpa_limits, RpaRuleType.ERP)
            shop_erp = [erp.erp_type.name for erp in shop.erps]  # type: ignore[union-attr]
            if RpaLimit.Utils.check(erp_limits, shop_erp).is_failed:
                break_limits.append(RpaRuleType.ERP)
            # 检查 org
            org_limits = RpaLimit.Utils.extract_specified_rule_type_in_limits(rpa_limits, RpaRuleType.Org)
            if RpaLimit.Utils.check(org_limits, [shop.org_id]).is_failed:  # type: ignore[list-item]
                break_limits.append(RpaRuleType.Org)

            return break_limits

        @staticmethod
        def get_org_subscribe_info(rpa: "Rpa", org_id: str) -> "Rpa.Schema.SubscribeInfo":
            from robot_processor.shop.models import Shop

            # 已订阅
            if RpaContext.query.filter(RpaContext.rpa == rpa, RpaContext.org_id == org_id, RpaContext.enabled).first():
                return Rpa.Schema.SubscribeInfo(has_subscribed=True)

            # 还没有订阅，判断是否可以订阅
            rpa_limits = cast(List[RpaLimit], rpa.limits)
            shops = Shop.Queries.org_shops_by_org_id(org_id).options(Shop.Options.joined_load_erp).all()
            break_limits = []
            # 检查 org
            org_limits = RpaLimit.Utils.extract_specified_rule_type_in_limits(rpa_limits, RpaRuleType.Org)
            if RpaLimit.Utils.check(org_limits, [org_id]).is_failed:
                break_limits.append(RpaRuleType.Org)
            # 检查 platform
            platform_limits = RpaLimit.Utils.extract_specified_rule_type_in_limits(rpa_limits, RpaRuleType.Platform)
            org_platform = [shop.platform for shop in shops]
            if RpaLimit.Utils.check(platform_limits, org_platform).is_failed:
                break_limits.append(RpaRuleType.Platform)
            # 检查 erp
            erp_limits = RpaLimit.Utils.extract_specified_rule_type_in_limits(rpa_limits, RpaRuleType.ERP)
            org_erp = [erp.erp_type.name for shop in shops for erp in shop.eager_erps]
            if RpaLimit.Utils.check(erp_limits, org_erp).is_failed:
                break_limits.append(RpaRuleType.ERP)

            if break_limits:
                return Rpa.Schema.SubscribeInfo(
                    has_subscribed=False,
                    can_subscribe=False,
                    break_limit_rules=break_limits,
                    available=False,
                )
            else:
                return Rpa.Schema.SubscribeInfo(
                    has_subscribed=False,
                    can_subscribe=True,
                    available=True,
                )

        @staticmethod
        def refer_by_scope_org(rpa: "Rpa", org_id: str):
            """获取指定租户下的所有使用 RPA 的工单模板及具体步骤"""
            from robot_processor.form.models import Form
            from robot_processor.form.models import FormWrapper
            from robot_processor.form.models import Step
            from robot_processor.shop.models import Shop

            class ReferBy(NamedTuple):
                form: FormWrapper
                step: Step

            refer_by = []
            forms = (
                Form.Queries.by_org_id(org_id)
                .join(Shop.Options.from_form_shop)
                .filter(Form.Filters.subscribed)
                .filter(Shop.Filters.accessible)
                .all()
            )
            form_map = {form.id: form for form in forms}
            form_steps = Step.Queries.steps(form_id=list(form_map.keys()), deleted=False)
            for step in filter(lambda step: step.rpa_id == rpa.id, form_steps):
                form: Form = form_map[step.form_id]
                shop: Shop = first(form.subscribed_shops)
                refer_by.append(ReferBy(form=form.wraps(shop), step=step))

            return refer_by

    class Queries:
        __slots__ = ()

        @classproperty  # noqa
        @classmethod
        def online_tags(cls):
            """获取 已上线RPA 标签列表"""
            return (
                db.session.execute(select(Rpa.tag).where(Rpa.status == RpaStatusEnum.ONLINE).distinct()).scalars().all()
            )

        @classproperty
        @classmethod
        def all_tags(cls):
            """获取 所有RPA 标签列表"""
            return (
                db.session.execute(select(Rpa.tag).distinct().where(Rpa.status != RpaStatusEnum.DELETED))
                .scalars()
                .all()
            )

        @classmethod
        def online(cls):
            # online约束规则
            # 1. 在自动化应用列表需要隐藏
            # 2. 在制作工单模板时，可引用的自动化应用列表需要隐藏
            # 3. 获取所有rpa tags时，需要过滤掉非online的rpa
            # 相关代码逻辑在查询rpa的时候，均需使用online query
            return Rpa.query.filter(Rpa.status == RpaStatusEnum.ONLINE)

        @classmethod
        def present(cls):
            return Rpa.query.filter(Rpa.status != RpaStatusEnum.DELETED)

        @classmethod
        def by_org(cls, org_id: str):
            return cls.online().join(RpaContext.rpa).filter(RpaContext.org_id == org_id).filter(RpaContext.enabled)

        @classmethod
        def by_tag(cls, tag: Optional[str]):
            """获取 RPA 列表"""
            query = cls.online().options(Rpa.Options.joined_load_limits)
            if tag is not None:
                query = query.filter(Rpa.tag == tag)
            return query

        @classmethod
        def by_names(cls, names: List[str]):
            return cls.online().options(Rpa.Options.joined_load_limits).filter(Rpa.name.in_(names))

        @classmethod
        def by_ids(cls, ids: List[int]):
            return cls.present().options(Rpa.Options.joined_load_limits).filter(Rpa.id.in_(ids))

    class Options:
        @classproperty  # noqa
        @staticmethod
        def defer_argument():
            return Load(Rpa).defer(Rpa.argument)

        @classproperty  # noqa
        @staticmethod
        def joined_load_limits():
            return Load(Rpa).joinedload(Rpa.limits)

    class Schema:
        class RpaClientMetadata(BaseModel):
            template_id: int = Field(description="模板id")
            ident: str = Field(description="模板标识")

        class Argument(BaseModel):
            input: List[dict] = Field(default_factory=list, description="输入参数")
            output: List[dict] = Field(default_factory=list, description="输出参数")
            mixed: List[dict] = Field(default_factory=list, description="混合参数")

        class ExtraInfo(BaseModel):
            is_new_icon: Optional[bool] = Field(default=None, description="展示 new 的标识")
            icon: Optional[str] = Field(default=None, description="图标")
            dependency: Optional[str] = Field(default=None, description="展示前置依赖")
            with_widget_collection: Optional[bool] = Field(default=False, description="是否在当前步骤输出表单")
            deprecated: Optional[bool] = Field(default=False, description="是否已废弃")

        class FormEditorSubscribeInfo(BaseModel):
            """在工单模板编辑页展示的订阅信息"""

            has_subscribed: bool = Field(default=False, description="是否已订阅")

        @omit_none_fields_before_validate("break_limit_rules")
        class SubscribeInfo(BaseModel):
            has_subscribed: bool = Field(default=False, description="是否已订阅")
            can_subscribe: bool = Field(default=False, description="是否可以订阅")

            break_limit_rules: List[RpaRuleType] = Field(default_factory=list, description="不满足的限制条件")
            available: bool = Field(default=False, description="是否满足限制条件")

            @property
            def need_hidden(self):
                """一些 rpa 应用应该在页面上不展示"""
                return RpaRuleType.Org in self.break_limit_rules

    class View:
        @omit_none_fields_before_validate("ext_info")
        class RpaList(BaseModel):
            """在自动化应用展示页"""

            class Config:
                orm_mode = True

            id: int = Field(description="自动化应用 ID")
            name: str = Field(description="自动化应用名称")
            description: Optional[str] = Field(description="自动化应用描述")
            tag: Optional[str] = Field(description="自动化应用标签")
            ext_info: "Rpa.Schema.ExtraInfo" = Field(default_factory=lambda: Rpa.Schema.ExtraInfo())
            subscribe_info: "Rpa.Schema.SubscribeInfo"

            @classmethod
            def from_(cls, *, rpa: "Rpa", subscribe_info: "Rpa.Schema.SubscribeInfo"):
                return cls(
                    id=rpa.id,
                    name=rpa.name,
                    description=rpa.description,
                    tag=rpa.tag,
                    ext_info=rpa.ext_info,
                    subscribe_info=subscribe_info,
                )

        @omit_none_fields_before_validate("ext_info")
        class FormEditor(BaseModel):
            """在工单模板编辑页提供的自动化应用信息"""

            class Config:
                orm_mode = True

            id: int = Field(description="自动化应用 ID")
            name: str = Field(description="自动化应用名称")
            description: Optional[str] = Field(description="自动化应用描述")
            tag: Optional[str] = Field(description="自动化应用标签")
            arguments: List[dict] = Field(default_factory=list, description="RPA 参数")
            argument: Optional["Rpa.Schema.Argument"] = Field(default_factory=lambda: Rpa.Schema.Argument())
            ext_info: "Rpa.Schema.ExtraInfo" = Field(default_factory=lambda: Rpa.Schema.ExtraInfo())
            can_auto_retry: bool = Field(default=False, description="是否支持自动重试")
            subscribe_info: "Rpa.Schema.FormEditorSubscribeInfo"
            task_type: str | None = Field(default=None, description="自动化应用唯一表示")

            @validator("can_auto_retry", pre=True)
            def set_can_auto_retry(cls, can_auto_retry):
                return can_auto_retry if can_auto_retry is not None else cls.__fields__["can_auto_retry"].default

            @classmethod
            def from_(cls, *, rpa: "Rpa", has_subscribed: bool):
                return cls(
                    id=rpa.id,
                    name=rpa.name,
                    description=rpa.description,
                    tag=rpa.tag,
                    arguments=rpa.arguments,
                    argument=rpa.argument,
                    ext_info=rpa.ext_info,
                    can_auto_retry=rpa.can_auto_retry,
                    subscribe_info=Rpa.Schema.FormEditorSubscribeInfo(has_subscribed=has_subscribed),
                    task_type=rpa.task,
                )

        class RawStep(BaseModel):
            """自动化任务在 raw step 中保存的信息"""

            id: int = Field(description="自动化应用 ID")
            name: str = Field(description="自动化应用名称")
            task_type: str = Field(alias="task", description="RPA 任务类型")
            description: Optional[str] = Field(description="自动化应用描述")
            tag: Optional[str] = Field(description="自动化应用标签")
            arguments: List[dict] = Field(default_factory=list, description="RPA 参数")
            argument: Optional["Rpa.Schema.Argument"] = Field(default_factory=lambda: Rpa.Schema.Argument())
            context: Optional[dict] = Field(default_factory=dict, alias="common", description="RPA 运行时上下文信息")


class RpaLimit(DbBaseModel):
    __table_args__ = (sa.Index("ix_rule", "rule_type", "rule_value"),)
    created_at: Mapped[int] = mapped_column(sa.Integer, default=time.time)
    updated_at: Mapped[int] = mapped_column(sa.Integer, default=time.time, onupdate=time.time)

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    # 关联的 rpa
    rpa_id: Mapped[int | None] = mapped_column(sa.Integer, sa.ForeignKey("rpa.id"))
    rpa: Mapped[Rpa] = relationship(Rpa, back_populates="limits")
    # 规则
    rule_type: Mapped[RpaRuleType | None] = mapped_column(sa.Enum(RpaRuleType))
    rule_value: Mapped[str | None] = mapped_column(sa.String(64))

    class Utils:
        __slots__ = ()

        @staticmethod
        def check(limits: List["RpaLimit"], target: List[str]):
            """检查 target 是否满足给定的限制条件

            Examples:
                >>> limits_ = [
                ...     RpaLimit(rule_type=RpaRuleType.ERP, rule_value="JST"),
                ...     RpaLimit(rule_type=RpaRuleType.ERP, rule_value="WDT"),
                ...  ]
                >>> target_ = ["BAISHENG"]
                >>> check(limits_, target_).is_succeed
                False
                >>> target_ = ["JST", "BAISHENG"]
                >>> check(limits_, target_).is_succeed
                True
            """

            class Result(Enum):
                SUCCEED = auto()
                FAILED = auto()

                @property
                def is_succeed(self):
                    return self == Result.SUCCEED

                @property
                def is_failed(self):
                    return self == Result.FAILED

            if not limits:  # 当前 RPA 没有这个类型的规则，校验通过
                return Result.SUCCEED
            expect = set(map(lambda limit: limit.rule_value, limits))
            if expect & set(target):  # 满足任一规则限制，校验通过
                return Result.SUCCEED
            else:
                return Result.FAILED

        @staticmethod
        def extract_specified_rule_type_in_limits(limits: List["RpaLimit"], rule_type: RpaRuleType):
            return list(filter(lambda limit: limit.rule_type == rule_type, limits))


class RpaContext(DbBaseModel):
    __table_args__ = (sa.Index("ix_org_rpa", "org_id", "rpa_id"),)
    created_at: Mapped[int] = mapped_column(sa.Integer, default=time.time)
    updated_at: Mapped[int] = mapped_column(sa.Integer, default=time.time, onupdate=time.time)

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    # 租户
    org_id: Mapped[str | None] = mapped_column(sa.String(32))
    # 关联的 rpa
    rpa_id: Mapped[int | None] = mapped_column(sa.Integer, sa.ForeignKey("rpa.id"))
    rpa: Mapped[Rpa] = relationship(Rpa)
    # rpa 启用状态
    enabled: Mapped[bool] = mapped_column(sa.Boolean, default=True)
    # 租户级配置
    common: Mapped[dict] = mapped_column(sa.JSON, default=dict)

    class Queries:
        @staticmethod
        def by_org_rpa_id(org_id: str, rpa_id: int):
            return RpaContext.query.filter(RpaContext.org_id == org_id, RpaContext.rpa_id == rpa_id)

    class CommonData(BaseModel):
        workflow_id: int | None = Field(description="rpa workflow id")
        retry_delay: int | None = Field(description="该自动化任务的重试时间间隔，单位为 ms")

    def get_common_data(self) -> "RpaContext.CommonData":
        common_data: RpaContext.CommonData = RpaContext.CommonData.validate(self.common)
        return common_data


def update_forward_refs():
    Rpa.View.RawStep.update_forward_refs()
    Rpa.View.FormEditor.update_forward_refs()
    Rpa.View.RpaList.update_forward_refs()


update_forward_refs()


class RpaConfig(DbBaseModel, BasicMixin):
    """
    租户下的 RPA 客户端配置。
    """

    # 租户
    org_id: Mapped[str] = mapped_column(sa.String(32), unique=True)
    # 配置信息
    config: Mapped[dict] = mapped_column(sa.JSON, default=dict)

    @staticmethod
    def get_config_by_shop_sids(sids: list[str]) -> list["RpaConfig"]:
        return (
            RpaConfig.query.join(Shop, Shop.org_id == RpaConfig.org_id)
            .filter(
                Shop.sid.in_(sids),
            )
            .all()
        )
