from pydantic import BaseModel
from pydantic.fields import ModelField
import typing as t


class GenerateSchemaError(Exception):
    def __init__(self, model_cls: t.Type[BaseModel], field: ModelField, msg: str) -> None:
        self.msg = msg
        self.model_cls = model_cls
        self.field = field
        super().__init__()

    def __repr__(self) -> str:
        return f"Model: {self.model_cls}, field: {self.field.name}, annotation: {self.field.annotation} " + self.msg

    def __str__(self) -> str:
        return f"Model: {self.model_cls}, field: {self.field.name}, annotation: {self.field.annotation} " + self.msg
