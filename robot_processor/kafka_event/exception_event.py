import arrow
from loguru import logger

from robot_processor.assistant.schema import AssistantV2
from robot_processor.business_order.models import Job, BusinessOrder
from robot_processor.business_order.notice.models import Notice, \
    NoticeUserException, NotifyOrgException
from robot_processor.db import in_transaction
from robot_processor.enums import NoticeCategory, NoticeStatus, NoticeScope
from robot_processor.ext import db
from robot_processor.form.api.form_editor_schema import StepNotifyConfig, NotifyChannel, ReceiverType, \
    ReceiverShortCut, NotifyTrigger
from robot_processor.form.models import StepExceptionNotify, Step
from robot_processor.notify.services import create_exception_task_notify
from robot_processor.job.job_model_wrapper import JobArguments


class ExceptionEventHelper:
    @staticmethod
    def process(job_id: int, notify_trigger: NotifyTrigger = NotifyTrigger.status):
        job: Job | None = Job.query.get(job_id)
        if not job:
            logger.info(f"Job {job_id} not found")
            return
        logger.bind(job_id=job_id, business_order_id=job.business_order_id)
        logger.info(f"Processing Exception Event {job_id}")
        if job.step_id is None:
            logger.warning(f"任务 {job_id} 没有对应的步骤信息。")
            return
        notify_configs = ExceptionEventHelper.get_enabled_step_notify_configs(job.step_id)
        if len(notify_configs) == 0:
            logger.info(f"config of Step {job.step_id} not found")
            return
        for notify_config in notify_configs:
            if not notify_config.enable:
                # 未启用的规则直接跳过。
                logger.info(f"Step {job.step_id} notify config is not enabled")
                continue

            config = StepNotifyConfig(**notify_config.config)
            if config.notify_trigger != notify_trigger:
                # 非指定的触发条件的规则直接跳过。
                continue

            with in_transaction():
                ExceptionEventHelper.process_by_step_notify_config(job, config)

    @staticmethod
    def process_by_step_notify_config(job: Job, config: StepNotifyConfig):
        for channel in config.notify_channels:
            if channel == NotifyChannel.in_app:
                ExceptionEventHelper._save_user_notice_by_in_app_config(
                    job.business_order, config, job)
            else:
                # 根据原始的配置信息，生成仅为当前 channel 使用的配置信息。
                current_channel_config = config.copy(deep=True)
                current_channel_config.notify_channels = [channel]
                ExceptionEventHelper._save_org_notice_by_outer_app_config(
                    job.shop.org_id, current_channel_config, job)

    @staticmethod
    def get_enabled_step_notify_configs(step_id: int) -> list[StepExceptionNotify]:
        """
        获取指定的 step 上配置的所有通知规则。
        :param step_id:
        :return:
        """
        step = Step.query.get(step_id)
        if not step:
            return []
        return StepExceptionNotify.query.filter_by(
            step_uuid=step.step_uuid,
            enable=True
        ).all()

    @staticmethod
    def _save_org_notice_by_outer_app_config(org_id: str | None, config: StepNotifyConfig, job: Job):
        if org_id is None:
            logger.error(f"任务 {job.id} 对应的店铺缺失 org_id 信息")
            return
        try:
            org_id_int = int(org_id)
        except Exception:
            logger.error(f"任务 {job.id} 对应的店铺的 org_id 信息不正确：{org_id}")
            return
        args = JobArguments.extract_from_job_orm(job)
        title = args._render_text_template(config.title)  # noqa
        content = args._render_text_template(config.content)  # noqa
        # 站外通知存储到NoticeOrgMapping,因为都是按租户发消息的
        notice = Notice(
            title=title,
            content=content,
            category=NoticeCategory.EXCEPTION_TASK,
            scope=NoticeScope.ORG,
            exception_notify_config=config.dict()
        )
        db.session.add(notice)
        db.session.flush()
        # 生成一个站外预通知
        nom = NotifyOrgException(
            notice_id=notice.id,
            org_id=org_id_int,
            is_informed=False,
            job_id=job.id
        )
        db.session.add(notice)
        db.session.add(nom)
        logger.info(f"生成notice {notice}和NotifyOrgException {nom}")

    @staticmethod
    def _save_user_notice_by_in_app_config(bo: BusinessOrder, config: StepNotifyConfig, job: Job):
        args = JobArguments.extract_from_job_orm(job)
        title = args._render_text_template(config.title)  # noqa
        content = args._render_text_template(config.content)  # noqa
        # 站内通知存储到NoticeUserMapping，因为都是按人发消息的
        notice = Notice(
            title=title,
            content=content,
            category=NoticeCategory.EXCEPTION_TASK,
            scope=NoticeScope.ORG,
            exception_notify_config=config.dict(),
            status=NoticeStatus.RELEASED
        )
        db.session.add(notice)
        db.session.flush()
        logger.info(f"生成站内通知的 notice {notice}")
        for user_id, user_type, user_nick in ExceptionEventHelper._get_users_from_config(bo, config):
            user_notice = NoticeUserException(
                notice_id=notice.id,
                user_id=user_id,
                user_type=user_type,
                is_informed=False,
                job_id=job.id
            )
            db.session.add(user_notice)
            db.session.flush()
            logger.info(f"生成notice {notice}NoticeUserException {user_notice}")
            create_exception_task_notify(config, arrow.now().int_timestamp, user_nick, job)

    @staticmethod
    def _get_users_from_config(
        business_order: BusinessOrder,
        notify_config: StepNotifyConfig
    ) -> list[tuple]:
        """返回(user id, user_type)列表， 从kiosk获取"""
        if notify_config.receive_type != ReceiverType.specified:
            logger.warning("站内通知不支持分发给所有人")
            return []
        receivers = []
        match notify_config.receiver_short_cut:
            case ReceiverShortCut.creator:
                receivers.append((
                    business_order.creator_user_id,
                    business_order.creator_type.value,
                    business_order.creator_nick()
                ))
            case ReceiverShortCut.current_assistant:
                if current_job := business_order.current_job:
                    receivers.append((
                        current_job.assignee_user_id or 0,
                        current_job.assignee_type.value,  # type: ignore[union-attr]
                        current_job.assignee or ""
                    ))
            case ReceiverShortCut.assistants:
                if ((current_job := business_order.current_job) is not None
                        and (current_step := current_job.current_step()) is not None):
                    assistants = current_step.get_assistants_v2().get_latest_assignee_account_details(
                        business_order.shop)
                    receivers.extend(
                        [(a.user_id, a.user_type, a.user_nick) for a in assistants]
                    )
            case ReceiverShortCut.admin:
                if form := business_order.form:
                    owners = form.get_owners()
                    assistants = owners.get_latest_assignee_account_details(business_order.shop)
                    receivers.extend(
                        [(a.user_id, a.user_type, a.user_nick) for a in assistants]
                    )
            case _:
                pass
        if receivers_in_app := notify_config.receivers_in_app:
            try:
                if receivers_in_app and business_order.shop:
                    assistant = AssistantV2.parse(receivers_in_app)
                    users = assistant.get_latest_assignee_account_details(business_order.shop)
                    receivers.extend([(u.user_id, u.user_type, u.user_nick) for u in users])
            except Exception as ex:
                logger.opt(exception=ex).error('从 kiosk 获取指定发送人的最新信息失败')
        return list(set(receivers))
