import threading

from confluent_kafka import Consumer
from typing_extensions import TypedDict
from loguru import logger

from robot_processor.kafka_event.trade_event import K<PERSON>KA_TOPIC, dispatcher

DISPATCHER = {KAFKA_TOPIC: dispatcher}


class TopicConfig(TypedDict):
    partition: int
    workers: int


def start_consume(config: dict, topics: list):
    _config_kafka_servers = config["KAFKA_SERVERS"]
    _threads_count = int(config.get("TRADE_CHANGE_EVENT_THREADS", 50))

    logger.warning(f"start kafka consumer. consume {topics}")

    consumer = Consumer(
        {
            "bootstrap.servers": ",".join(_config_kafka_servers),
            "group.id": "robot-processor",
            "broker.version.fallback": "0.11.0.1",
            "queued.min.messages": 20,  # 32 (partition+topic) * 20
            "queued.max.messages.kbytes": 15 * 1024,  # 32 (partition) * 15 (MBytes) < 512 Mb
        }
    )
    consumer.subscribe(topics=topics)

    while True:
        messages = []
        current_index = 1
        while current_index < _threads_count:
            try:
                message = consumer.poll(1.0)
            except Exception as e:  # noqa
                logger.exception("kafka consumer poll error.")
                break

            if message is None:
                break
            if message.error():
                logger.error(f"Consumer error: {message.error()}")
                break

            messages.append((message.topic(), message.value()))
            current_index += 1

        threads = []
        for _topic, _value in messages:
            t = threading.Thread(target=DISPATCHER[_topic], args=(_value,))
            t.start()
            threads.append(t)

        for t in threads:
            t.join()
