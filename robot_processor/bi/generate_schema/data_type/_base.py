from abc import ABCMeta, abstractmethod
from dataclasses import dataclass
from typing import cast, Callable, ClassVar, TypeVar, Protocol, Literal, Union, Optional

from leyan_proto.digismart.robot.bi.generate_pb2 import (
    DataDimension,
    DataDimensionDisplay,
    DataStatistics,
    DataRange,
)
from loguru import logger
from sqlalchemy import func as sa_func
from pydantic import BaseModel, parse_obj_as, ValidationError

__all__ = ["ReportDataType", "ReportDataRangeAgent", "ReportModelFieldAgent", "ReportStatisticsAgent"]

from sqlalchemy.sql.functions import GenericFunction, func

from robot_processor.utils import message_to_dict
from robot_processor.bi.database import ReportBusinessOrder, ReportTransferInfo

T = TypeVar("T", covariant=True)
V = TypeVar("V", contravariant=True)


class ValueConverter(Protocol[V, T]):
    def __call__(self, value: V, dimension: DataDimension) -> T: ...


@dataclass
class OperatorBind:
    """作为数据范围时，操作符绑定的方法"""
    operator: Callable
    # 提供给前端使用，在作为数据范围筛选时，该使用哪个前端组件渲染（文本框/下来列表..）
    component_type: "DataRange.ComponentType.ValueType"
    # 将前端返回的 DataRange.value 进行类型校验/转换
    validation_model: type = str
    # 将前端返回的 DataRange.value 进行预处理（该步骤在 validation_model 前执行）
    value_converter: Callable = message_to_dict


class ReportDataTypeMeta(type):
    """报表数据类型基类，提供合并 operators 的能力
    """
    __report_data_type_map: ClassVar[dict["DataDimension.DataType.ValueType", type["ReportDataType"]]] = {}

    @classmethod
    def get_data_type_map(cls) -> dict["DataDimension.DataType.ValueType", type["ReportDataType"]]:
        return cls.__report_data_type_map

    @property
    def logger(cls):
        return logger.bind(class_=cls.__name__)

    def __new__(mcs, name, bases, attrs):
        """将子类根据 `report_data_type` 注册到 `__report_data_type_map`"""
        cls = super().__new__(mcs, name, bases, attrs)
        if "__abstract__" in attrs:
            return cls

        # 检查是否定义了 REPORT_DATA_RANGE_AGENT 属性
        if "DATA_RANGE_AGENT" not in attrs:
            raise RuntimeError(f"{name} 的 REPORT_DATA_RANGE_AGENT 属性未定义")
        # 检查是否定义了 REPORT_DATA_TYPE 属性
        if (REPORT_DATA_TYPE := attrs.get("DATA_TYPE")) is None:
            raise RuntimeError(f"{name} 的 REPORT_DATA_TYPE 属性未定义")
        # 将子类注册到父类的子类列表中
        if REPORT_DATA_TYPE in mcs.__report_data_type_map:
            data_type_str = DataDimension.DataType.Name(REPORT_DATA_TYPE)
            raise RuntimeError(f"{name} 的 REPORT_DATA_TYPE 属性值 {data_type_str} 重复")
        mcs.__report_data_type_map[REPORT_DATA_TYPE] = cast(type[ReportDataType], cls)
        return cls


class ReportDataType(metaclass=ReportDataTypeMeta):
    __abstract__ = True

    DATA_TYPE: ClassVar["DataDimension.DataType.ValueType"]
    # 维护一个操作符到能力的映射
    DATA_RANGE_AGENT: ClassVar[type["ReportDataRangeAgent"]]
    # 维护一个数据维度到数据库字段的映射
    MODEL_FIELD_AGENT: ClassVar[type["ReportModelFieldAgent"]]
    # 维护一个统计数据的映射
    STATISTICS_AGENT: ClassVar[type["ReportStatisticsAgent"]]

    @classmethod
    def __infer_which_class(cls, data_type: "DataDimension.DataType.ValueType") -> type["ReportDataType"]:
        """根据维度返回对应的数据类型"""
        report_data_type_map = cls.get_data_type_map()

        if data_type not in report_data_type_map:
            raise TypeError(f"数据类型 {DataDimension.DataType.Name(data_type)} 未定义")
        return report_data_type_map[data_type]

    @classmethod
    def auto_init_dimension_display(cls, dimension_display: DataDimensionDisplay):
        dimension = dimension_display.dimension
        infer_cls = cls.__infer_which_class(dimension.data_type)
        if "__abstract__" in vars(infer_cls.MODEL_FIELD_AGENT):
            raise RuntimeError(f"{infer_cls} 不能实例化，这是一个抽象类[field]。")
        model_field_agent = infer_cls.MODEL_FIELD_AGENT(dimension=dimension,
                                                        order_by=dimension_display.order_by)

        return infer_cls(
            which="DimensionDisplay",
            dimension=dimension,
            data_range_agent=infer_cls.DATA_RANGE_AGENT.unsupported_class(data_range=DataRange()),
            model_field_agent=model_field_agent,
            statistics_agent=infer_cls.STATISTICS_AGENT.unsupported_class(statistics=DataStatistics())
        )

    @classmethod
    def auto_init_data_range(cls, data_range: DataRange):
        dimension = data_range.dimension
        infer_cls = cls.__infer_which_class(dimension.data_type)
        if "__abstract__" in vars(infer_cls.DATA_RANGE_AGENT):
            raise RuntimeError(f"{infer_cls} 不能实例化，这是一个抽象类[data_range]。")
        if "__abstract__" in vars(infer_cls.MODEL_FIELD_AGENT):
            raise RuntimeError(f"{infer_cls} 不能实例化，这是一个抽象类[field]。")
        model_field_agent = infer_cls.MODEL_FIELD_AGENT(dimension=dimension, order_by=None)
        data_range_agent = infer_cls.DATA_RANGE_AGENT(data_range=data_range)

        return infer_cls(
            which="DataRange", dimension=dimension,
            data_range_agent=data_range_agent,
            model_field_agent=model_field_agent,
            statistics_agent=infer_cls.STATISTICS_AGENT.unsupported_class(statistics=DataStatistics())
        )

    @classmethod
    def auto_init_statistics(cls, statistics: DataStatistics):
        dimension = statistics.dimension
        infer_cls = cls.__infer_which_class(dimension.data_type)
        if "__abstract__" in vars(infer_cls.MODEL_FIELD_AGENT):
            raise RuntimeError(f"{infer_cls} 不能实例化，这是一个抽象类[field]。")
        model_field_agent = infer_cls.MODEL_FIELD_AGENT(dimension=dimension,
                                                        order_by=statistics.order_by)
        statistics_agent = infer_cls.STATISTICS_AGENT(statistics=statistics)

        return infer_cls(
            which="Statistics", dimension=dimension,
            data_range_agent=infer_cls.DATA_RANGE_AGENT.unsupported_class(data_range=DataRange()),
            model_field_agent=model_field_agent,
            statistics_agent=statistics_agent
        )

    def __init__(self,
                 which: Literal["DimensionDisplay", "Statistics", "DataRange"],
                 dimension: DataDimension,
                 data_range_agent: "ReportDataRangeAgent",
                 model_field_agent: "ReportModelFieldAgent",
                 statistics_agent: "ReportStatisticsAgent"):
        self.which = which
        self.dimension = dimension
        self.data_range_agent = data_range_agent
        self.model_field_agent = model_field_agent
        self.statistics_agent = statistics_agent

    def __str__(self):
        return "<ReportDataType[{}]:{}>".format(
            self.which,
            DataDimension.DataType.Name(self.DATA_TYPE)
        )

    def get_where_clause(self):
        field = self.model_field_agent.field_in_where()
        value = self.data_range_agent.validate_and_convert_data_range_value()
        operator_bind = self.data_range_agent.get_operator_bind()

        return operator_bind.operator(field, value)

    def get_select_field(self):
        field = self.model_field_agent.field_in_select()

        if self.which == "DimensionDisplay":
            return field.label(self.model_field_agent.label)
        elif self.which == "Statistics":
            op = self.statistics_agent.get_operator_bind()
            # sum 需要保留两位小数
            if op == func.sum:
                return func.round(op(field), 2).label(self.model_field_agent.label)
            return op(field).label(self.model_field_agent.label)
        else:
            error_tmpl = "不支持作为 select, {}"
            raise TypeError(error_tmpl.format(self.which))

    def get_group_by_field(self):
        if self.which not in ("DimensionDisplay", "Statistics"):
            error_tmpl = "不支持作为 group by, {}"
            raise TypeError(error_tmpl.format(self.which))

        field = self.model_field_agent.field_in_group_by()

        return field

    def get_order_by_field(self):
        if self.which not in ("DimensionDisplay", "Statistics"):
            error_tmpl = "不支持作为 order by, {}"
            raise TypeError(error_tmpl.format(self.which))

        return self.model_field_agent.field_in_order_by()

    def get_query_limit_value(self):
        if self.which != "DataRange":
            error_tmpl = "不支持作为 query limit, {}"
            raise TypeError(error_tmpl.format(self.which))
        value = self.data_range_agent.validate_and_convert_data_range_value()

        return value


class ReportDataRangeMeta(type):
    operators: dict["DataRange.Operator.ValueType", Union[OperatorBind, "ellipsis"]] = {}

    @property
    def logger(cls):
        return logger.bind(class_=cls.__name__)

    @property
    def unsupported_class(cls) -> type["ReportDataRangeAgent"]:
        return ReportDataRangeUnsupported

    @property
    def support_operators(cls):
        """支持的操作符号"""
        from leyan_proto.digismart.robot_web.bi_pb2 import ListReportDataTypeResponse

        operators = []
        OperatorInfo = ListReportDataTypeResponse.DataRangeOperatorInfo

        for operator, operator_bind in cls.operators.items():
            if operator_bind is Ellipsis:
                continue
            operators.append(OperatorInfo(
                operator=operator,
                operator_label={
                    DataRange.OPERATOR_EQUAL: "等于",
                    DataRange.OPERATOR_NOT_EQUAL: "不等于",
                    DataRange.OPERATOR_GREATER_THAN: "大于",
                    DataRange.OPERATOR_GREATER_THAN_OR_EQUAL: "大于等于",
                    DataRange.OPERATOR_LESS_THAN: "小于",
                    DataRange.OPERATOR_LESS_THAN_OR_EQUAL: "小于等于",
                    DataRange.OPERATOR_IN: "包含(在给定范围内)",
                    DataRange.OPERATOR_NOT_IN: "不包含",
                    DataRange.OPERATOR_BETWEEN: "介于",
                    DataRange.OPERATOR_NOT_BETWEEN: "不介于",
                    DataRange.OPERATOR_IS_NULL: "为空",
                    DataRange.OPERATOR_IS_NOT_NULL: "不为空",
                    DataRange.OPERATOR_CONTAINS: "包含(模糊匹配)",
                    DataRange.OPERATOR_NOT_CONTAINS: "不包含",
                    DataRange.OPERATOR_RELATIVE_DATE: "相对日期",
                    DataRange.OPERATOR_EXISTS: "不为空",
                    DataRange.OPERATOR_NOT_EXISTS: "为空",
                    DataRange.OPERATOR_IS_TRUE: "为真",
                    DataRange.OPERATOR_IS_FALSE: "为假",
                }[operator],
                component_type=operator_bind.component_type,
            ))
        return operators

    def __new__(mcs, name, bases, attrs):
        """在类初始化时根据 mro 逆序查找 operators 属性并做合并"""
        if "__abstract__" in attrs:
            return super().__new__(mcs, name, bases, attrs)

        operators: dict["DataRange.Operator.ValueType", Union[OperatorBind, "ellipsis"]] = {}
        for base in reversed(bases):
            if hasattr(base, "operators"):
                operators.update(base.operators)
        # 写入当前类的 operators
        operators.update(attrs.get("operators", {}))
        attrs["operators"] = operators

        return super().__new__(mcs, name, bases, attrs)


class ReportDataRangeAgent(metaclass=ReportDataRangeMeta):
    __abstract__ = True
    OperatorBind: ClassVar = OperatorBind
    operators: ClassVar[dict["DataRange.Operator.ValueType", Union[OperatorBind, "ellipsis"]]]

    def __init__(self, data_range: DataRange):
        self.dimension = data_range.dimension
        self.operator = data_range.operator
        self.data_range_value = data_range.value

    def get_operator_bind(self) -> OperatorBind:
        """获取操作符绑定的方法"""
        if self.operator not in self.operators:
            error_tmpl = "{data_type} 不支持操作符 {operator}"
            raise TypeError(error_tmpl.format(
                data_type=self, operator=DataRange.Operator.Name(self.operator)
            ))
        if (operator_bind := self.operators[self.operator]) is Ellipsis:
            error_tmpl = "{data_type} 操作符 {operator} 未实现"
            raise TypeError(error_tmpl.format(
                data_type=self, operator=DataRange.Operator.Name(self.operator)
            ))

        return operator_bind

    def validate_and_convert_data_range_value(self):
        """根据 operator bind 的 validation model 对 data range 进行类型校验和转换"""
        operator_bind = self.get_operator_bind()
        data_range_value = operator_bind.value_converter(self.data_range_value)

        try:
            value = parse_obj_as(operator_bind.validation_model, data_range_value)
            if isinstance(value, BaseModel) and "__root__" in value.__fields__:
                value = getattr(value, '__root__')
            return value

        except ValidationError as error:
            error_tmpl = "数据范围的值类型不匹配, " \
                         "期望类型: {expect_type}, " \
                         "校验错误: {error}, 实际值: {data_range_value}"
            raise TypeError(error_tmpl.format(
                expect_type=operator_bind.validation_model,
                error=error,
                data_range_value=data_range_value
            ))


class ReportDataRangeUnsupported(ReportDataRangeAgent):
    operators = {
        operator: ...
        for operator in DataRange.Operator.values()
    }


class ReportStatisticsMeta(type):
    operators: set["DataStatistics.Operator.ValueType"] = set()

    @property
    def operators_label_map(cls) -> dict["DataStatistics.Operator.ValueType", str]:
        return {
            DataStatistics.OPERATOR_SUM: "求和",
            DataStatistics.OPERATOR_COUNT: "计数",
            DataStatistics.OPERATOR_AVERAGE: "平均",
            DataStatistics.OPERATOR_RATIO: "占比",
            DataStatistics.OPERATOR_SUM_RATIO: "求和占比",
        }

    @property
    def logger(cls):
        return logger.bind(class_=cls.__name__)

    @property
    def unsupported_class(cls) -> type["ReportStatisticsAgent"]:
        return ReportStatisticsUnsupported

    @property
    def support_operators(cls):
        """支持的操作符号"""
        from leyan_proto.digismart.robot_web.bi_pb2 import ListReportDataTypeResponse

        operators = []
        for operator in cls.operators:
            operators.append(ListReportDataTypeResponse.DataStatisticsOperatorInfo(
                operator=operator,
                operator_label=cls.operators_label_map[operator],
            ))

        return operators


class ReportStatisticsAgent(metaclass=ReportStatisticsMeta):
    __abstract__ = True
    operators: set["DataStatistics.Operator.ValueType"] = set()

    def __init__(self, statistics: DataStatistics):
        self.dimension = statistics.dimension
        self.operator = statistics.operator
        self.order_by = statistics.order_by

    def get_operator_bind(self):
        """获取操作符绑定的方法"""
        if self.operator not in self.operators:
            error_tmpl = "{data_type} 不支持操作符 {operator}"
            raise TypeError(error_tmpl.format(
                data_type=self, operator=DataStatistics.Operator.Name(self.operator)
            ))

        return {
            DataStatistics.OPERATOR_SUM: func.sum,
            DataStatistics.OPERATOR_COUNT: func.count,
            DataStatistics.OPERATOR_AVERAGE: func.avg,
            DataStatistics.OPERATOR_RATIO: ratio,
            DataStatistics.OPERATOR_SUM_RATIO: ratio,
        }[self.operator]


# 站位符
class ratio(GenericFunction):
    def __init__(self, *args, **kwargs):
        super(ratio, self).__init__(*args, **kwargs)




class ReportStatisticsUnsupported(ReportStatisticsAgent):
    operators = {
        DataStatistics.OPERATOR_COUNT
    }


class ReportModelFieldMeta(ABCMeta):
    @property
    def empty_self(cls) -> "ReportModelFieldAgent":
        return cls(dimension=DataDimension())

    @property
    def logger(cls):
        return logger.bind(class_=cls.__name__)

    @property
    def unsupported_class(cls) -> type["ReportModelFieldAgent"]:
        return ReportModelFieldUnsupported


class ReportModelFieldAgent(metaclass=ReportModelFieldMeta):
    __abstract__ = True

    DATA_TYPE: "DataDimension.DataType.ValueType"
    MODEL: ClassVar = ReportBusinessOrder
    TRANSFER_MODEL: ClassVar = ReportTransferInfo

    def __init__(
            self,
            dimension: DataDimension,
            order_by: Optional["DataDimension.OrderBy.ValueType"] = None
    ):
        self.dimension = dimension
        self.order_by = order_by

    def __set_name__(self, owner, name):
        if not issubclass(owner, ReportDataType):
            raise TypeError("FieldAgent 只能绑定在类型 ReportDataType 的子类")
        self.DATA_TYPE = owner.DATA_TYPE

    def __str__(self):
        return "<ModelFieldAgent: data_type={}, dimension={}>".format(
            getattr(self, "DATA_TYPE", "(empty)"),
            self.dimension
        )

    @abstractmethod
    def field_in_select(self):
        """在 SELECT 语句中的字段"""

    def field_in_group_by(self):
        """在 GROUP BY 语句中的字段"""
        return self.literal_label

    def field_in_order_by(self):
        """在 ORDER BY 语句中的字段"""
        from sqlalchemy import asc, desc

        if self.order_by:
            order = {
                DataDimension.ORDER_BY_ASC: asc,
                DataDimension.ORDER_BY_DESC: desc
            }[self.order_by]

            return order(self.literal_label)

    @abstractmethod
    def field_in_where(self):
        """在 WHERE 语句中的字段"""

    def _custom_field(self):
        if (extra_config := self.dimension.custom_field.extra_config) and "parent_node_list" in extra_config.fields:
            parent_node_list = extra_config["parent_node_list"]
            if "ignore_scope_node_list" in extra_config:
                ignore_scope_node_list = extra_config["ignore_scope_node_list"]
            else:
                ignore_scope_node_list = []
            final_key = ""
            for item in parent_node_list:
                if item in ignore_scope_node_list:
                    final_key += f'"{item}".'
                else:
                    final_key += f'"{item}"[*].'
            return sa_func.coalesce(*[
                sa_func.json_unquote(
                    sa_func.json_extract(self.MODEL.data, f'$.{final_key}"{widget_info.key}"')
                )
                for widget_info in self.dimension.custom_field.widgets
            ])
        return sa_func.coalesce(*[
            sa_func.json_unquote(
                sa_func.json_extract(self.MODEL.data, '$."{}"'.format(widget_info.key))
            )
            for widget_info in self.dimension.custom_field.widgets
        ])

    @property
    def literal_label(self):
        """在 GROUP BY / ORDER BY 中可以使用的 label 别名"""
        from sqlalchemy import text

        return text("`" + self.dimension.title + "`")

    @property
    def label(self):
        return self.dimension.title

    @classmethod
    def is_query_limit_dimension(cls, dimension: DataDimension) -> bool:
        """判断这个维度是否是用于查询数据限制"""
        return dimension.data_type == DataDimension.DATA_TYPE_NUMBER_QUERY_LIMIT


class ReportModelFieldUnsupported(ReportModelFieldAgent):
    def field_in_select(self):
        raise NotImplementedError

    def field_in_where(self):
        raise NotImplementedError
