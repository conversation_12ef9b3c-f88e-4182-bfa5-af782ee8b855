import calendar
import datetime
from enum import StrEnum
from typing import NamedTuple, Any

from google.protobuf import struct_pb2
from google.protobuf.json_format import ParseDict
from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimension, DataRange
from sqlalchemy import func as sa_func
from sqlalchemy.sql import operators as sa_op

from robot_processor.bi.generate_schema.data_type._base import (
    ReportStatisticsAgent,
    ReportDataRangeAgent,
)
from robot_processor.bi.generate_schema.data_type.basic.date import (
    ReportDataTypeDate,
    ModelFieldDate,
    DataRangeDate,
)
from robot_processor.bi.generate_schema.data_type.basic.enum import (
    DataRangeEnum,
    ReportDataTypeEnum,
    ModelFieldEnum,
)


class ModelFieldEnumDate(ModelFieldDate, ModelFieldEnum):
    @classmethod
    def equal(cls, field, value):
        date_range = DateRangeWindow[value].date_range
        return sa_op.between_op(field, date_range.start, date_range.end)


class DataRangeEnumDate(DataRangeDate, DataRangeEnum):
    """枚举类型-时间窗口"""

    operators = {
        DataRange.OPERATOR_EQUAL: ReportDataRangeAgent.OperatorBind(
            operator=ModelFieldEnumDate.equal,
            component_type=DataRange.COMPONENT_TYPE_SELECT,
            value_converter=DataRangeEnum.from_pb,
        ),
        # 仅支持 equal
        DataRange.OPERATOR_BETWEEN: ...,
        DataRange.OPERATOR_RELATIVE_DATE: ...,
    }


class ReportDataTypeEnumDate(ReportDataTypeEnum, ReportDataTypeDate):
    """枚举类型-时间窗口"""

    DATA_TYPE = DataDimension.DATA_TYPE_ENUM_DATE
    MODEL_FIELD_AGENT = ModelFieldEnumDate
    DATA_RANGE_AGENT = DataRangeEnumDate
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class

    def get_enum_options(self, data_source):
        enum_options = []
        for option in DateRangeWindow:
            enum_option = ParseDict(
                {"label": option.value, "value": option.name}, struct_pb2.Struct()
            )
            enum_options.append(enum_option)
        return enum_options


class DateRange(NamedTuple):
    start: Any
    end: Any


class DateRangeWindow(StrEnum):
    """筛选时间窗口"""

    last_week = "上周"
    last_month = "上月"
    current_week = "这周"
    current_month = "这月"
    past_week_until_yesterday = "截止至昨天前一周"
    today = "今日"

    @property
    def date_range(self) -> DateRange:

        today = datetime.datetime.today()

        if self == DateRangeWindow.last_week:
            last_week_monday = today - datetime.timedelta(days=today.weekday() + 7)
            last_week_sunday = last_week_monday + datetime.timedelta(days=6)
            return DateRange(last_week_monday.date(), last_week_sunday.date())
        elif self == DateRangeWindow.last_month:
            year = today.year
            month = today.month

            # 上一月的年份和月份
            if month == 1:
                year -= 1
                month = 12
            else:
                month -= 1

            # 获取上一月的第一天和最后一天
            _, last_day = calendar.monthrange(year, month)
            start_of_last_month = datetime.date(year, month, 1)
            end_of_last_month = datetime.date(year, month, last_day)
            return DateRange(start_of_last_month, end_of_last_month)
        elif self == DateRangeWindow.current_week:
            current_week_monday = (
                today - datetime.timedelta(days=today.weekday())
            ).date()
            return DateRange(current_week_monday, sa_func.now())
        elif self == DateRangeWindow.current_month:
            return DateRange(today.replace(day=1).date(), sa_func.now())
        elif self == DateRangeWindow.past_week_until_yesterday:
            yesterday = today - datetime.timedelta(days=1)
            yesterday_past_week = yesterday - datetime.timedelta(days=6)
            return DateRange(yesterday_past_week.date(), yesterday.date())
        elif self == DateRangeWindow.today:
            return DateRange(today.date(), sa_func.now())
