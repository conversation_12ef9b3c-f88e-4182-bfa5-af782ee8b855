import operator
from typing import List

from google.protobuf import struct_pb2
from google.protobuf.json_format import ParseDict
from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimension, DataRange
from sqlalchemy.sql import operators as sa_op

from robot_processor.bi.generate_schema.data_type._base import (
    ReportStatisticsAgent,
    ReportDataRangeAgent,
)
from robot_processor.bi.generate_schema.data_type.basic.enum import (
    DataRangeEnum,
    ReportDataTypeEnum,
)
from robot_processor.bi.generate_schema.data_type.basic.string import (
    DataRangeString,
    ReportDataTypeString, ModelFieldString,
)
from robot_processor.ext import db
from robot_processor.db import no_auto_flush


class ModelFieldEnumString(ModelFieldString):
    def field_in_where(self):
        if (
            self.dimension.dimension_type
            == DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD
        ):
            raise TypeError(f"{self} can't be used as custom field")

        field_mapper = {
            DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_STATUS: self.MODEL.status,
            DataDimension.DATA_DIMENSION_SYSTEM_FIELD_PAYMENT_STATUS: self.TRANSFER_MODEL.status,
            DataDimension.DATA_DIMENSION_SYSTEM_FIELD_SHOP_PLATFORM: self.MODEL.shop_platform,
            DataDimension.DATA_DIMENSION_SYSTEM_FIELD_STEP: self.MODEL.step_name,
            DataDimension.DATA_DIMENSION_SYSTEM_FIELD_FORM: self.MODEL.form_id,
        }
        if self.dimension.system_field not in field_mapper:
            raise TypeError("system field not supported, field_agent: {}".format(self))

        return field_mapper[self.dimension.system_field]

    field_in_select = field_in_where


class DataRangeEnumString(DataRangeString, DataRangeEnum):
    operators = {
        DataRange.OPERATOR_EQUAL: ReportDataRangeAgent.OperatorBind(
            operator=operator.eq,
            component_type=DataRange.COMPONENT_TYPE_SELECT,
            value_converter=DataRangeEnum.from_pb
        ),
        DataRange.OPERATOR_NOT_EQUAL: ReportDataRangeAgent.OperatorBind(
            operator=operator.ne,
            component_type=DataRange.COMPONENT_TYPE_SELECT,
            value_converter=DataRangeEnum.from_pb
        ),
        # data.tid.in_(["a", "b"]) -> data.tid in ("a", "b")
        DataRange.OPERATOR_IN: ReportDataRangeAgent.OperatorBind(
            operator=sa_op.in_op,
            component_type=DataRange.COMPONENT_TYPE_MULTI_SELECT,
            validation_model=List[str],
            value_converter=DataRangeEnum.from_pb
        ),
        DataRange.OPERATOR_NOT_IN: ReportDataRangeAgent.OperatorBind(
            operator=sa_op.not_in_op,
            component_type=DataRange.COMPONENT_TYPE_MULTI_SELECT,
            validation_model=List[str],
            value_converter=DataRangeEnum.from_pb
        ),
        # 默认继承 String，以下的操作符不支持
        DataRange.OPERATOR_CONTAINS: ...,
        DataRange.OPERATOR_NOT_CONTAINS: ...,
    }


class ReportDataTypeEnumString(ReportDataTypeString, ReportDataTypeEnum):
    """枚举类型-字符串"""

    DATA_TYPE = DataDimension.DATA_TYPE_ENUM_STRING
    MODEL_FIELD_AGENT = ModelFieldEnumString
    DATA_RANGE_AGENT = DataRangeEnumString
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class

    def get_enum_options(self, data_source):
        enum_mapper = {
            DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_STATUS: get_enum_options_bo_status,
            DataDimension.DATA_DIMENSION_SYSTEM_FIELD_PAYMENT_STATUS: get_enum_options_payment_status,
            DataDimension.DATA_DIMENSION_SYSTEM_FIELD_SHOP_PLATFORM: get_enum_options_shop_platform,
            DataDimension.DATA_DIMENSION_SYSTEM_FIELD_FORM: get_enum_options_form,
            DataDimension.DATA_DIMENSION_SYSTEM_FIELD_STEP: get_enum_options_step,
        }
        if self.dimension.system_field not in enum_mapper:
            raise TypeError(
                "dimension enum not supported, field_agent: {}".format(self)
            )

        return enum_mapper[self.dimension.system_field](data_source=data_source)  # type: ignore[operator]


def get_enum_options_bo_status(*args, **kwargs):
    """FIXME 硬编码和 transformed_business_order.status 字段一致"""
    status_list = [
        "待提交",
        "进行中",
        "处理中",
        "异常中",
        "已完成",
        "已关闭",
        "暂停中",
        "待受理",
        "待处理",
        "已驳回",
    ]
    enum_options = []
    for status in status_list:
        enum_option = ParseDict({"label": status, "value": status}, struct_pb2.Struct())
        enum_options.append(enum_option)

    return enum_options


def get_enum_options_payment_status(*args, **kwargs):
    """硬编码和 transfer_info.status 字段一致
    """
    from robot_processor.bi.database import ReportTransferInfo

    enum_options = []
    for status_label, status in ReportTransferInfo.get_payment_status_options().items():
        enum_option = ParseDict({"label": status_label, "value": status}, struct_pb2.Struct())
        enum_options.append(enum_option)

    return enum_options


@no_auto_flush()
def get_enum_options_shop_platform(data_source, *args, **kwargs):
    from sqlalchemy import select
    from robot_processor.form.models import Form, FormShop
    from robot_processor.shop.models import Shop
    from robot_processor.bi.database import ReportBusinessOrder

    stmt = (
        select(Shop.platform)
        .select_from(Form)
        .join(Form.form_shops)
        .join(FormShop.shop)
        .where(Form.id.in_(list(data_source.form_ids)))
        .distinct()
    )
    platform_list = db.session.execute(stmt).scalars().all()

    enum_options = []
    for platform in platform_list:
        platform = ReportBusinessOrder.get_platform_in_report(platform)
        enum_option = ParseDict(
            {"label": platform, "value": platform}, struct_pb2.Struct()
        )
        enum_options.append(enum_option)

    return enum_options


@no_auto_flush()
def get_enum_options_form(data_source, *args, **kwargs):
    from sqlalchemy import select
    from sqlalchemy.orm import Load
    from robot_processor.form.models import FormShop

    stmt = (select(FormShop)
            .select_from(FormShop)
            .options(Load(FormShop).joinedload(FormShop.form))
            .options(Load(FormShop).joinedload(FormShop.shop))
            .where(FormShop.form_id.in_(list(data_source.form_ids)))
            .distinct())
    form_shops = db.session.execute(stmt).scalars().all()

    enum_options = []
    for form_shop in form_shops:
        shop = form_shop.shop
        form = form_shop.form.wraps(shop)

        label = "{form_name} [{shop_title}@{shop_platform}]".format(
            form_name=form.name, shop_title=shop.title, shop_platform=shop.platform
        )
        value = form.id
        enum_options.append(
            ParseDict({"label": label, "value": value}, struct_pb2.Struct())
        )

    return enum_options


@no_auto_flush()
def get_enum_options_step(data_source, *args, **kwargs):
    from sqlalchemy import select
    from sqlalchemy.orm import Load
    from robot_processor.form.models import Step, FormShop
    from robot_processor.shop.models import Shop

    stmt = (select(Step, Shop)
            .select_from(FormShop)
            .join(FormShop.shop)
            .join(Step, Step.form_id == FormShop.form_id)
            .where(FormShop.form_id.in_(list(data_source.form_ids)))
            .options(Load(Step).load_only(Step.id, Step.name))
            .options(Load(Shop).load_only(Shop.sid, Shop.platform, Shop.title))
            .distinct())
    records = db.session.execute(stmt).all()

    enum_options = []
    for step, shop in records:
        label = "{step_name} [{shop_title}@{shop_platform}]".format(
            step_name=step.name, shop_title=shop.title, shop_platform=shop.platform
        )
        value = step.id
        enum_options.append(
            ParseDict({"label": label, "value": value}, struct_pb2.Struct())
        )

    return enum_options
