from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimension

from robot_processor.bi.generate_schema.data_type._base import (
    ReportDataType,
    ReportDataRangeAgent, ReportStatisticsAgent,
)
from robot_processor.bi.generate_schema.data_type.basic.object import (
    ModelFieldObject,
)


class ModelFieldObjectReissueProduct(ModelFieldObject):
    def field_in_where(self):
        raise TypeError("订单类型不支持 where 条件")


class ReportDataTypeObjectReissueProduct(ReportDataType):
    """订单"""

    DATA_TYPE = DataDimension.DATA_TYPE_OBJECT_REISSUE_PRODUCT
    MODEL_FIELD_AGENT = ModelFieldObjectReissueProduct
    DATA_RANGE_AGENT = ReportDataRangeAgent.unsupported_class
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class
