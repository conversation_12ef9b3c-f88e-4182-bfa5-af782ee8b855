import operator
from typing import List

from google.protobuf import struct_pb2
from leyan_proto.digismart.robot.bi.generate_pb2 import DataRange, DataDimension
from pydantic import BaseModel
from sqlalchemy import func as sa_func

from robot_processor.bi.generate_schema.data_type._base import (
    ReportModelFieldAgent,
    ReportDataRangeAgent,
    ReportStatisticsAgent,
)
from robot_processor.bi.generate_schema.data_type.basic.enum import (
    DataRangeEnum,
    ReportDataTypeEnum,
)
from robot_processor.bi.generate_schema.data_type.basic.object import DataRangeObject
from robot_processor.bi.utils import debug_log


class ModelFieldEnumObjectShop(ReportModelFieldAgent):
    def field_in_where(self):
        return self.MODEL.sid, self.MODEL.shop_platform

    def field_in_select(self):
        return sa_func.concat(self.MODEL.shop_platform, "·", self.MODEL.shop_title)

    field_in_group_by = field_in_where
    field_in_order_by = field_in_where

    @classmethod
    @debug_log
    def equal(cls, field, shop: "ShopOption"):
        field_sid, field_shop_platform = field
        return operator.and_(
            operator.eq(field_sid, shop.sid),
            operator.eq(field_shop_platform, shop.platform),
        )

    @classmethod
    @debug_log
    def not_equal(cls, field, shop: "ShopOption"):
        field_sid, field_shop_platform = field
        return operator.and_(
            operator.ne(field_sid, shop.sid),
            operator.ne(field_shop_platform, shop.platform),
        )

    @classmethod
    @debug_log
    def in_(cls, field, shop_list: List["ShopOption"]):
        return operator.or_(
            *map(
                lambda shop: cls.equal(field, shop),
                shop_list,
            )
        )

    @classmethod
    @debug_log
    def not_in(cls, field, shop_list: List["ShopOption"]):
        return operator.and_(
            *map(
                lambda shop: cls.not_equal(field, shop),
                shop_list,
            )
        )


class ShopOption(BaseModel):
    sid: str
    platform: str


class DataRangeEnumObjectShop(DataRangeObject, DataRangeEnum):
    operators = {
        DataRange.OPERATOR_EQUAL: ReportDataRangeAgent.OperatorBind(
            operator=ModelFieldEnumObjectShop.equal,
            component_type=DataRange.COMPONENT_TYPE_SELECT,
            validation_model=ShopOption,
            value_converter=DataRangeEnum.from_pb
        ),
        DataRange.OPERATOR_NOT_EQUAL: ReportDataRangeAgent.OperatorBind(
            operator=ModelFieldEnumObjectShop.not_equal,
            component_type=DataRange.COMPONENT_TYPE_SELECT,
            validation_model=ShopOption,
            value_converter=DataRangeEnum.from_pb
        ),
        DataRange.OPERATOR_IN: ReportDataRangeAgent.OperatorBind(
            operator=ModelFieldEnumObjectShop.in_,
            component_type=DataRange.COMPONENT_TYPE_MULTI_SELECT,
            validation_model=List[ShopOption],
            value_converter=DataRangeEnum.from_pb
        ),
        DataRange.OPERATOR_NOT_IN: ReportDataRangeAgent.OperatorBind(
            operator=ModelFieldEnumObjectShop.not_in,
            component_type=DataRange.COMPONENT_TYPE_MULTI_SELECT,
            validation_model=List[ShopOption],
            value_converter=DataRangeEnum.from_pb
        ),
    }


class ReportDataTypeEnumObjectShop(ReportDataTypeEnum):
    DATA_TYPE = DataDimension.DATA_TYPE_ENUM_OBJECT_SHOP
    MODEL_FIELD_AGENT = ModelFieldEnumObjectShop
    DATA_RANGE_AGENT = DataRangeEnumObjectShop
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class

    def get_enum_options(self, data_source):
        from sqlalchemy import select
        from sqlalchemy.orm import Load
        from robot_processor.ext import db
        from robot_processor.form.models import FormShop
        from robot_processor.shop.models import Shop
        from robot_processor.bi.database import ReportBusinessOrder

        with db.session.no_autoflush as session:
            stmt = (
                select(Shop)
                .select_from(Shop)
                .join(Shop.shop_forms)
                .where(FormShop.form_id.in_(list(data_source.form_ids)))
                .options(Load(Shop).load_only(Shop.sid, Shop.platform, Shop.title))
                .distinct()
            )
            records = session.execute(stmt).scalars().all()

        enum_options = []
        for shop in records:
            enum_option = struct_pb2.Struct()
            shop_platform = ReportBusinessOrder.get_platform_in_report(shop.platform)
            enum_option["label"] = "{shop_platform}·{shop_title}".format(
                shop_platform=shop_platform,
                shop_title=shop.title
            )
            enum_option["value"] = {"sid": shop.sid, "platform": shop_platform}
            enum_options.append(enum_option)

        return enum_options
