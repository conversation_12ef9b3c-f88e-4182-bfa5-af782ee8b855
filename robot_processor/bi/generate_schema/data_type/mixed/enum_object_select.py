from typing import List

from google.protobuf import struct_pb2
from google.protobuf.json_format import ParseDict
from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimension, DataRange
from sqlalchemy import func as sa_func, or_ as sa_or, and_ as sa_and

from robot_processor.bi.generate_schema.data_type._base import (
    ReportDataRangeAgent,
    ReportStatisticsAgent,
)
from robot_processor.bi.generate_schema.data_type.basic.enum import (
    ModelFieldEnum,
    DataRangeEnum,
    ReportDataTypeEnum,
)
from robot_processor.bi.generate_schema.data_type.basic.object import (
    ModelFieldObject,
    DataRangeObject,
    ReportDataTypeObject,
)


class ModelFieldEnumObjectSelect(ModelFieldObject, ModelFieldEnum):
    def field_in_select(self):
        """如果指定了选项的层级，则只取指定层级的选项"""
        if "specified_level" not in self.dimension.custom_field.extra_config.fields:
            return super().field_in_select()
        final_key = ""
        if (extra_config := self.dimension.custom_field.extra_config) and "parent_node_list" in extra_config.fields:
            parent_node_list = extra_config["parent_node_list"]
            for item in parent_node_list:
                final_key += f'"{item}"[*].'
        specified_level = int(self.dimension.custom_field.extra_config["specified_level"])

        return sa_func.json_array(*[
            sa_func.coalesce(*[
                # 多选
                *[
                     sa_func.json_extract(
                         sa_func.json_extract(
                             self.MODEL.data,
                             f'$.{final_key}"{widget_info.key}"'
                         ),
                         f"$[0][{level}]",
                     )
                     for widget_info in self.dimension.custom_field.widgets
                 ],
                # 单选
                *[
                    sa_func.json_extract(
                        sa_func.json_extract(
                            self.MODEL.data,
                            f'$.{final_key}"{widget_info.key}"'
                        ),
                        f"$[{level}]",
                    )
                    for widget_info in self.dimension.custom_field.widgets
                ]
            ])
            for level in range(specified_level)
        ])

    @classmethod
    def equal(cls, field, value_list):
        """
        case1: 将 equal 转换为 list[index].value = value_list[index].item
        单选场景 value_list = ['补发', '仅退款']
            and(
                json_unquote(json_extract(field, '$[0].value')) == '补发',
                json_unquote(json_extract(field, '$[1].value')) == '仅退款',
            )

        case2: 将 equal 转换成 json_contains
        多选场景 value_list = ['补发', '退款']
            and(
                json_contains(json_extract(field, '$[*][0]'), json_object('value', '补发')),
                json_contains(json_extract(field, '$[*][1]'), json_object('value', '退款')),
            )
        """
        return sa_or(
            # 无法区分是单选还是多选
            # 按照单选查
            sa_and(*[
                sa_func.json_unquote(sa_func.json_extract(field, f'$[{level}].value')) == value
                for level, value in enumerate(value_list)
            ]),
            # 按照多选查
            sa_and(*[
                sa_func.json_contains(
                    sa_func.json_extract(field, f'$[*][{level}]'),
                    sa_func.json_object("value", value)
                )
                for level, value in enumerate(value_list)
            ])
        )
        # return sa_func.json_contains(
        #     field,
        #     sa_func.json_array(*[
        #         sa_func.json_object("value", value) for value in value_list
        #     ]),
        # )

    @classmethod
    def in_(cls, field, list_of_value_list):
        return sa_or(cls.equal(field, value_list) for value_list in list_of_value_list)  # type: ignore[arg-type]


class DataRangeEnumObjectSelect(DataRangeObject, DataRangeEnum):
    operators = {
        DataRange.OPERATOR_EQUAL: ReportDataRangeAgent.OperatorBind(
            operator=ModelFieldEnumObjectSelect.equal,
            component_type=DataRange.COMPONENT_TYPE_CUSTOM_SELECT,
            validation_model=List[str],
            value_converter=DataRangeEnum.from_pb,
        ),
        DataRange.OPERATOR_IN: ReportDataRangeAgent.OperatorBind(
            operator=ModelFieldEnumObjectSelect.in_,
            component_type=DataRange.COMPONENT_TYPE_CUSTOM_MULTI_SELECT,
            validation_model=List[List[str]],
            value_converter=DataRangeEnum.from_pb
        ),
    }


class ReportDataTypeEnumObjectSelect(ReportDataTypeObject, ReportDataTypeEnum):
    DATA_TYPE = DataDimension.DATA_TYPE_ENUM_OBJECT_SELECT
    MODEL_FIELD_AGENT = ModelFieldEnumObjectSelect
    DATA_RANGE_AGENT = DataRangeEnumObjectSelect
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class

    def get_enum_options(self, data_source):
        from sqlalchemy import select, cast, Integer
        from robot_processor.ext import db
        from robot_processor.form.models import WidgetInfo
        from robot_processor.form.models import Step, FormVersion

        with db.session.no_autoflush as session:
            form_version = (
                session.query(FormVersion)
                .where(FormVersion.form_id.in_(list(data_source.form_ids)))
                .where(FormVersion.version_descriptor == "HEAD")
                .first()
            )
            if not form_version:
                step_ids = Step.Queries.step_ids(form_id=data_source.form_ids[0], is_dirty=False)
            else:
                step_ids = form_version.step_id

            stmt = (
                select(WidgetInfo.option_value)
                .select_from(WidgetInfo)
                .join(
                    Step,
                    cast(Step.widget_collection_id, Integer)
                    == WidgetInfo.widget_collection_id,
                )
                .where(Step.id.in_(step_ids))
                .where(
                    WidgetInfo.key.in_(
                        [item.key for item in self.dimension.custom_field.widgets]
                    ),
                    WidgetInfo.before.isnot(True)
                )
            )
            option_value = session.execute(stmt).scalar_one_or_none()

        enum_options = []
        if option_value:
            for option in option_value.get("options", []):
                specified_level_option = self.get_specified_level_options(option)
                enum_options.append(ParseDict(specified_level_option, struct_pb2.Struct()))

        return enum_options

    def get_specified_level_options(self, raw_option: dict):
        if "specified_level" not in self.dimension.custom_field.extra_config.fields:
            return raw_option
        specified_level = int(self.dimension.custom_field.extra_config["specified_level"])

        def pruning(option: dict, current_level: int):
            if current_level == specified_level:
                option.pop("children", None)
            else:
                for child in option.get("children", []):
                    pruning(child, current_level + 1)

        pruning(raw_option, 1)

        return raw_option
