from typing import List, ClassVar

from google.protobuf import type_pb2
from leyan_proto.digismart.robot.bi.generate_pb2 import DataRange, DataDimension
from leyan_proto.digismart.robot.bi.report_pb2 import ReportDataSource

from robot_processor.bi.generate_schema.data_type._base import (
    ReportDataType,
    ReportDataRangeAgent,
    ReportModelFieldAgent,
    ReportStatisticsAgent,
)
from robot_processor.utils import message_to_dict


class ModelFieldEnum(ReportModelFieldAgent):
    __abstract__ = True


class DataRangeEnum(ReportDataRangeAgent):
    __abstract__ = True

    operators = {
        DataRange.OPERATOR_EQUAL: ...,
        DataRange.OPERATOR_NOT_EQUAL: ...,
        DataRange.OPERATOR_IN: ...,
        DataRange.OPERATOR_NOT_IN: ...,
    }

    @classmethod
    def from_pb(cls, message):
        return cls.extract_value(message_to_dict(message))

    @classmethod
    def extract_value(cls, raw_value):
        """
        前端保存的是完整的 option {label: ..., value: ...}
        所以需要将 option 的 value 提取出来

        Examples:
            >>> DataRangeEnum.extract_value("已关闭")
            '已关闭'
            >>> DataRangeEnum.extract_value(
            ...     {"label": "店铺A",
            ...      "value": {"sid": "123", "platform": "TAOBAO"}}
            ... )
            {'sid': '123', 'platform': 'TAOBAO'}
            >>> DataRangeEnum.extract_value(
            ...     [{"label": "已关闭", "value": "已关闭"}, {"label": "进行中", "value": "进行中"}]
            ... )
            ['已关闭', '进行中']
        """
        if isinstance(raw_value, str):
            return raw_value
        if isinstance(raw_value, dict):
            return raw_value.get("value", raw_value.get("label", None))
        if isinstance(raw_value, list):
            return list(map(cls.extract_value, raw_value))
        return None


class ReportDataTypeEnum(ReportDataType):
    """枚举类型"""
    __abstract__ = True

    DATA_TYPE = DataDimension.DATA_TYPE_ENUM
    DATA_RANGE_AGENT: ClassVar[type[DataRangeEnum]]
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class

    def get_enum_options(self, data_source: ReportDataSource) -> List[type_pb2.Option]:
        raise NotImplementedError(f"{self} not implemented get_options method")
