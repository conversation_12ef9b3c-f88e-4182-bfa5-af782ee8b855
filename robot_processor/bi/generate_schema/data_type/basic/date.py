import datetime
import operator
from typing import Literal, Union, Any

from dateutil.relativedelta import relativedelta
from sqlalchemy import func as sa_func, text as sa_text
from sqlalchemy.sql import operators as sa_op
from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimension, DataRange
from pydantic import BaseModel
from loguru import logger

from robot_processor.bi.utils import debug_log
from robot_processor.bi.generate_schema.data_type._base import (
    ReportDataType,
    ReportDataRangeAgent,
    ReportModelFieldAgent,
    ReportStatisticsAgent,
)


class ModelFieldDate(ReportModelFieldAgent):
    def _system_field(self):
        field_mapper = {
            DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_CREATE_TIME: self.MODEL.created_at,
            DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_UPDATE_TIME: self.MODEL.updated_at,
        }
        if self.dimension.system_field not in field_mapper:
            raise TypeError("system field not supported, field_agent: {}".format(self))

        return sa_func.from_unixtime(field_mapper[self.dimension.system_field])

    def _raw_field(self):
        return {
            DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD: self._system_field,
            DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD: self._custom_field,
        }[self.dimension.dimension_type]()

    def field_in_where(self):
        return sa_func.date_format(self._raw_field(), "%Y-%m-%d")

    field_in_select = field_in_where

    @classmethod
    @debug_log
    def between(cls, field, date_range: Union["DateRange", "DatetimeRange"]):
        return sa_op.between_op(field, date_range.min, date_range.max)

    @classmethod
    @debug_log
    def relative(cls, field, datetime_relative_range: "DatetimeRelativeRange"):
        date_min = DatetimeUtil.relate_date_in_sql(datetime_relative_range)
        return operator.ge(field, date_min)


class DateRange(BaseModel):
    """日期范围筛选"""

    min: Any  # 类型: Union[datetime.date, now, Function]
    max: Any  # 类型: Union[datetime.date, now, Function]


class DatetimeRange(BaseModel):
    """日期时间范围筛选"""

    min: Any  # 类型: Union[datetime.datetime, now, Function]
    max: Any  # 类型: Union[datetime.datetime, now, Function]


class DatetimeRelativeRange(BaseModel):
    """相对时间筛选"""

    unit: Literal["DAY", "WEEK", "MONTH", "YEAR"]
    value: int


class DataRangeDate(ReportDataRangeAgent):
    operators = {
        DataRange.OPERATOR_EQUAL: ReportDataRangeAgent.OperatorBind(
            operator=operator.eq,
            component_type=DataRange.COMPONENT_TYPE_DATE,
            validation_model=datetime.date,
        ),
        DataRange.OPERATOR_BETWEEN: ReportDataRangeAgent.OperatorBind(
            operator=ModelFieldDate.between,
            component_type=DataRange.COMPONENT_TYPE_DATE_RANGE,
            validation_model=DateRange,
        ),
        DataRange.OPERATOR_RELATIVE_DATE: ReportDataRangeAgent.OperatorBind(
            operator=ModelFieldDate.relative,
            component_type=DataRange.COMPONENT_TYPE_DATE_RELATIVE,
            validation_model=DatetimeRelativeRange,
        ),
    }


class ReportDataTypeDate(ReportDataType):
    """日期类型"""

    DATA_TYPE = DataDimension.DATA_TYPE_DATE
    MODEL_FIELD_AGENT = ModelFieldDate
    DATA_RANGE_AGENT = DataRangeDate
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class


class ModelFieldDatetime(ModelFieldDate):
    """继承自 Date，重写不一致的部分就可以了"""

    def field_in_where(self):
        # 重写为 datetime 格式
        return sa_func.date_format(self._raw_field(), "%Y-%m-%d %T")


class DataRangeDatetime(ReportDataRangeAgent):
    operators = {
        DataRange.OPERATOR_BETWEEN: ReportDataRangeAgent.OperatorBind(
            operator=ModelFieldDatetime.between,
            component_type=DataRange.COMPONENT_TYPE_DATETIME_RANGE,
            validation_model=DatetimeRange,
        ),
        DataRange.OPERATOR_RELATIVE_DATE: ReportDataRangeAgent.OperatorBind(
            operator=ModelFieldDatetime.relative,
            component_type=DataRange.COMPONENT_TYPE_DATE_RELATIVE,
            validation_model=DatetimeRelativeRange,
        ),
    }


class ReportDataTypeDatetime(ReportDataType):
    """日期时间类型"""

    DATA_TYPE = DataDimension.DATA_TYPE_DATETIME
    MODEL_FIELD_AGENT = ModelFieldDatetime
    DATA_RANGE_AGENT = DataRangeDatetime
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class


class ModelFieldDateYear(ModelFieldDate):
    def field_in_where(self):
        raise TypeError("年份类型不支持 where 条件")

    def field_in_select(self):
        return sa_func.date_format(self._raw_field(), "%Y")


class ReportDataTypeDateYear(ReportDataType):
    """日期-年"""

    DATA_TYPE = DataDimension.DATA_TYPE_DATE_YEAR
    MODEL_FIELD_AGENT = ModelFieldDateYear
    DATA_RANGE_AGENT = ReportDataRangeAgent.unsupported_class
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class


class ModelFieldDateMonth(ModelFieldDate):
    def field_in_where(self):
        raise TypeError("月份类型不支持 where 条件")

    def field_in_select(self):
        return sa_func.date_format(self._raw_field(), "%Y-%m")


class ReportDataTypeDateMonth(ReportDataType):
    """日期-月"""

    DATA_TYPE = DataDimension.DATA_TYPE_DATE_MONTH
    MODEL_FIELD_AGENT = ModelFieldDateMonth
    DATA_RANGE_AGENT = ReportDataRangeAgent.unsupported_class
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class


class ModelFieldDateWeek(ModelFieldDate):
    def field_in_where(self):
        raise TypeError("周类型不支持 where 条件")

    def field_in_select(self):
        field = self._raw_field()

        return sa_func.concat(
            sa_func.date_format(field, "%Y"), "第", sa_func.week(field), "周"
        )


class ReportDataTypeDateWeek(ReportDataType):
    """日期-周"""

    DATA_TYPE = DataDimension.DATA_TYPE_DATE_WEEK
    MODEL_FIELD_AGENT = ModelFieldDateWeek
    DATA_RANGE_AGENT = ReportDataRangeAgent.unsupported_class
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class


class ModelFieldDateRelativeMonth(ModelFieldDate):
    def field_in_where(self):
        raise TypeError("相对月份类型不支持 where 条件")

    def field_in_select(self):
        return sa_func.timestampdiff(sa_text("MONTH"), self._raw_field(), sa_func.now())


class ReportDataTypeDateRelativeMonth(ReportDataType):
    """相对月份"""

    DATA_TYPE = DataDimension.DATA_TYPE_DATE_RELATIVE_MONTH
    MODEL_FIELD_AGENT = ModelFieldDateRelativeMonth
    DATA_RANGE_AGENT = ReportDataRangeAgent.unsupported_class
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class


class ModelFieldDateRelativeWeek(ModelFieldDate):
    def field_in_where(self):
        raise TypeError("相对周类型不支持 where 条件")

    def field_in_select(self):
        return sa_func.timestampdiff(sa_text("WEEK"), self._raw_field(), sa_func.now())


class ReportDataTypeDateRelativeWeek(ReportDataType):
    """相对周"""

    DATA_TYPE = DataDimension.DATA_TYPE_DATE_RELATIVE_WEEK
    MODEL_FIELD_AGENT = ModelFieldDateRelativeWeek
    DATA_RANGE_AGENT = ReportDataRangeAgent.unsupported_class
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class


class ModelFieldDateRelativeDay(ModelFieldDate):
    def field_in_where(self):
        raise TypeError("相对天类型不支持 where 条件")

    def field_in_select(self):
        return sa_func.timestampdiff(sa_text("DAY"), self._raw_field(), sa_func.now())


class ReportDataTypeDateRelativeDay(ReportDataType):
    """相对天"""

    DATA_TYPE = DataDimension.DATA_TYPE_DATE_RELATIVE_DAY
    MODEL_FIELD_AGENT = ModelFieldDateRelativeDay
    DATA_RANGE_AGENT = ReportDataRangeAgent.unsupported_class
    STATISTICS_AGENT = ReportStatisticsAgent.unsupported_class


class DatetimeUtil:
    logger = logger.bind(class_="DatetimeUtil")

    @classmethod
    @debug_log
    def relate_date_to_timedelta(
        cls, datetime_relative_range: "DatetimeRelativeRange"
    ) -> relativedelta:
        timedelta_mapper = {
            "DAY": lambda: relativedelta(days=-datetime_relative_range.value),
            "WEEK": lambda: relativedelta(weeks=-datetime_relative_range.value),
            "MONTH": lambda: relativedelta(months=-datetime_relative_range.value),
            "YEAR": lambda: relativedelta(years=-datetime_relative_range.value),
        }
        if datetime_relative_range.unit not in timedelta_mapper:
            raise TypeError("不支持的时间单位: {}".format(datetime_relative_range.unit))

        return timedelta_mapper[datetime_relative_range.unit]()

    @classmethod
    def relate_date_in_sql(cls, datetime_relative_range: "DatetimeRelativeRange"):
        date_mapper = {
            "DAY": lambda: sa_func.date_sub(
                sa_func.now(), sa_text(f"INTERVAL {datetime_relative_range.value} DAY")
            ),
            "WEEK": lambda: sa_func.date_sub(
                sa_func.now(), sa_text(f"INTERVAL {datetime_relative_range.value} WEEK")
            ),
            "MONTH": lambda: sa_func.date_sub(
                sa_func.now(), sa_text(f"INTERVAL {datetime_relative_range.value} MONTH")
            ),
            "YEAR": lambda: sa_func.date_sub(
                sa_func.now(), sa_text(f"INTERVAL {datetime_relative_range.value} YEAR")
            ),
        }
        if datetime_relative_range.unit not in date_mapper:
            raise TypeError("不支持的时间单位: {}".format(datetime_relative_range.unit))

        return date_mapper[datetime_relative_range.unit]()
