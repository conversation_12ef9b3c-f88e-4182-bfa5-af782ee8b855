from abc import ABC, abstractmethod
from typing import TypedDict, List, Optional, NamedTuple, Union

from leyan_proto.digismart.common import widget_collection_pb2
from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimension
from leyan_proto.digismart.robot.bi.report_pb2 import ReportDataSource
from leyan_proto.digismart.robot_web.bi_pb2 import \
    ListReportDataDimensionResponse
from more_itertools import first

from robot_processor.form.types import WidgetDataType

DimensionInfo = ListReportDataDimensionResponse.ReportDataDimensionInfo
CustomField = DataDimension.DataDimensionCustomField
WidgetReportView = widget_collection_pb2.Widget.ReportView


class WidgetInfo(TypedDict):
    option_value: dict
    key: str


class BuilderContext(NamedTuple):
    widget_type: str
    sync_widget_key: Optional[str]
    widget_info_list: List[WidgetInfo]
    widget_data_type: WidgetDataType
    parent_node_list: Optional[List[str | None]] = []
    ignore_scope_node_list: list[str] = []


def auto_init_dimension(context: BuilderContext):

    # 检查啥时候应该算是复合组件
    def _check_table_widget(_context):
        # 组件类型是table的是
        if _context.widget_type == "table":
            return True
        # 组件的值类型是table的，且option_value的fields不为空的
        if _context.widget_data_type == "table":
            _table_widget: WidgetInfo = context.widget_info_list[0]
            return _table_widget.get("option_value", {}).get("fields", [])
        return False

    widget_data_type_mapper: dict[str, type[DataTypeBuilderProtocol]] = {
        # 单行输入
        "string": StringBuilder,
        # 多行输入
        "textarea": StringBuilder,
        # 数字输入
        "number": NumberBuilder,
        # 单选/多选
        "select": EnumObjectSelectBuilder,
        # 日期时间
        "datetime": DatetimeBuilder,
        # 买家昵称
        "usernick": StringBuilder,
        # 支付宝账号
        "alipay": StringBuilder,
        # 下单时间
        "order-time": DatetimeBuilder,
        # 订单/子订单
        "order": ObjectOrderBuilder,
        # 商品
        "product": ObjectProductBuilder,
        # 付款时间
        "payment-time": DatetimeBuilder,
        # 发货时间
        "delivery-time": DatetimeBuilder,
        # 订单问题细分
        "order-question": EnumObjectSelectBuilder,
        # 收货地址
        "address": ObjectAddressBuilder,
        # 快递公司
        "contractor": StringBuilder,
        # 快递单号
        "tracking-num": StringBuilder,
        # 订单金额
        "price": NumberBuilder,
        # 实付金额
        "payment": NumberBuilder,
        # 真实姓名
        "realname": StringBuilder,
        # 收款信息
        "payment-method": ObjectPaymentMethodBuilder,
        # 商品编码
        "out-sku-id": StringBuilder,
        # 创建 ERP 补发单
        "reissue-product": ObjectReissueProductBuilder,
        # 评分
        "rate": StringBuilder,
        # 实收金额
        "received-amount": NumberBuilder,
        # 上传组件
        "upload": UploadBuilder,
        "array": ArrayBuilder,
        "collection": CollectionBuilder,
        "boolean": BooleanBuilder,
    }
    if _check_table_widget(context):
        # 如果上下文中没有 parent_node_list，那么它可能是最外层的组件。
        parent_node_list = context.parent_node_list or []
        table_widget: WidgetInfo = context.widget_info_list[0]
        sub_dimensions = []
        for node in table_widget.get("option_value", {}).get("fields", []):
            # 记录复合组件中，父子组件的路径
            current_sub_node_parent_node_list = parent_node_list + [context.sync_widget_key]
            # 递归拿到父子组件的结构
            sub_dimensions.append(auto_init_dimension(BuilderContext(
                widget_type=node.get("type"),
                sync_widget_key=node.get("key"),
                widget_info_list=[WidgetInfo(
                    key=node.get("key"),
                    option_value=node.get("option_value"))
                ],
                widget_data_type=node.get("widget_type"),
                parent_node_list=current_sub_node_parent_node_list,
                ignore_scope_node_list=context.ignore_scope_node_list
            )))
        return TableBuilder(
            widget_type=context.widget_type,
            sync_widget_key=context.sync_widget_key,
            widget_info_list=context.widget_info_list,
            widget_data_type=context.widget_data_type,
            sub_dimensions=sub_dimensions,
            parent_node_list=parent_node_list,
            ignore_scope_node_list=context.ignore_scope_node_list
        ).gather()
    elif context.widget_type == "collection":
        parent_node_list = context.parent_node_list or []
        ignore_scope_node_list = context.ignore_scope_node_list or []
        sub_dimensions = []
        for node in context.widget_info_list[0].get("option_value", {}).get("fields", []):
            current_sub_node_parent_node_list = parent_node_list + [context.sync_widget_key]
            current_sub_node_ignore_scope_node_list = ignore_scope_node_list + [context.sync_widget_key]
            sub_dimensions.append(auto_init_dimension(BuilderContext(
                widget_type=node.get("type"),
                sync_widget_key=node.get("key"),
                widget_info_list=[WidgetInfo(
                    key=node.get("key"),
                    option_value=node.get("option_value"))
                ],
                widget_data_type=node.get("widget_type"),
                parent_node_list=current_sub_node_parent_node_list,
                ignore_scope_node_list=current_sub_node_ignore_scope_node_list  # type: ignore[arg-type]
            )))
        return CollectionBuilder(
            widget_type=context.widget_type,
            sync_widget_key=context.sync_widget_key,
            widget_info_list=context.widget_info_list,
            widget_data_type=context.widget_data_type,
            sub_dimensions=sub_dimensions,
            parent_node_list=parent_node_list,
            ignore_scope_node_list=context.ignore_scope_node_list
        ).gather()
    else:
        builder = widget_data_type_mapper.get(context.widget_type, StringBuilder)
        return builder(
            widget_type=context.widget_type,
            sync_widget_key=context.sync_widget_key,
            widget_info_list=context.widget_info_list,
            widget_data_type=context.widget_data_type,
            parent_node_list=context.parent_node_list,
            ignore_scope_node_list=context.ignore_scope_node_list
        ).gather()


class DataTypeBuilderProtocol(ABC):
    def __init__(
            self,
            widget_type: str,
            sync_widget_key: Optional[str],
            widget_info_list: List[WidgetInfo],
            widget_data_type: WidgetDataType,
            sub_dimensions: Union[list, None] = None,
            parent_node_list: Union[list, None] = None,
            ignore_scope_node_list: list[str] | None = None
    ):
        self.widget_type = widget_type
        self.sync_widget_key = sync_widget_key
        self.widget_info_list = widget_info_list
        self.widget_data_type = widget_data_type
        self.sub_dimensions = sub_dimensions
        self.parent_node_list = parent_node_list
        self.ignore_scope_node_list = ignore_scope_node_list

    @property
    def default_title(self):
        return first(self.widget_info_list)["option_value"]["label"]

    @property
    def custom_field(self):
        from google.protobuf import struct_pb2

        field = CustomField()
        field.widgets.extend(
            [
                WidgetReportView(
                    type=self.widget_type,
                    label=item["option_value"]["label"],
                    key=item["key"],
                    upload_type=str(item["option_value"].get("uploadType", ""))
                )
                for item in self.widget_info_list
            ]
        )
        if self.sync_widget_key:
            field.sync_widget_key.value = self.sync_widget_key
        if self.parent_node_list:
            extra_config = struct_pb2.Struct()
            extra_config.update({
                "parent_node_list": self.parent_node_list,
                "ignore_scope_node_list": self.ignore_scope_node_list
            })
            field.extra_config.update(extra_config)
        return field

    @abstractmethod
    def gather(self) -> DimensionInfo:
        ...


class StringBuilder(DataTypeBuilderProtocol):
    def build_string(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=True,
            data_type=DataDimension.DATA_TYPE_STRING,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )

    def gather(self):
        return DimensionInfo(dimension=self.build_string())


class UploadBuilder(DataTypeBuilderProtocol):
    def build_string(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=True,
            data_type=DataDimension.DATA_TYPE_OBJECT_UPLOAD,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )

    def gather(self):
        return DimensionInfo(dimension=self.build_string())


class NumberBuilder(DataTypeBuilderProtocol):
    def build_number(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=True,
            data_type=DataDimension.DATA_TYPE_NUMBER,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )

    def gather(self):
        return DimensionInfo(dimension=self.build_number())


class DatetimeBuilder(DataTypeBuilderProtocol):
    def gather(self):
        return DimensionInfo(
            dimension=self.build_date(),
            sub_dimensions=[
                self.build_datetime(),
                self.build_date_year(),
                self.build_date_month(),
                self.build_date_week(),
                self.build_date_relative_month(),
                self.build_date_relative_week(),
                self.build_enum_date(),
            ],
        )

    def build_date(self):
        return DataDimension(
            title=f"{self.default_title}-日期",
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=True,
            data_type=DataDimension.DATA_TYPE_DATE,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )

    def build_datetime(self):
        return DimensionInfo(
            dimension=DataDimension(
                title=self.default_title,
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_DATETIME,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=self.custom_field,
            ))

    def build_date_year(self):
        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-年",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_DATE_YEAR,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=self.custom_field,
            ))

    def build_date_month(self):
        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-月",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_DATE_MONTH,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=self.custom_field,
            ))

    def build_date_week(self):
        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-周",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_DATE_YEAR,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=self.custom_field,
            ))

    def build_date_relative_month(self):
        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-相对时间-月",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_DATE_RELATIVE_MONTH,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=self.custom_field,
            ))

    def build_date_relative_week(self):
        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-相对时间-周",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_DATE_RELATIVE_WEEK,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=self.custom_field,
            ))

    def build_date_relative_day(self):
        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-相对时间-天",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_DATE_RELATIVE_DAY,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=self.custom_field,
            ))

    def build_enum_date(self):
        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-固定周期",
                use_in_x_axis=False,
                use_in_y_axis=False,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_ENUM_DATE,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=self.custom_field
            ))


class ObjectAddressBuilder(DataTypeBuilderProtocol):
    def gather(self):
        return DimensionInfo(dimension=self.build_address())

    def build_address(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=False,
            data_type=DataDimension.DATA_TYPE_OBJECT_ADDRESS,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )


class ObjectOrderBuilder(DataTypeBuilderProtocol):
    def gather(self):
        return DimensionInfo(
            dimension=self.build_order(),
            sub_dimensions=[self.build_order_tid(), self.build_order_oid()],
        )

    def build_order(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=False,
            data_type=DataDimension.DATA_TYPE_OBJECT_ORDER,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )

    def build_order_tid(self):
        custom_field = self.custom_field
        custom_field.object_field = CustomField.OBJECT_FIELD_ORDER_TID
        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-订单号",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_OBJECT_STRING_ARRAY,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=custom_field,
            ))

    def build_order_oid(self):
        custom_field = self.custom_field
        custom_field.object_field = CustomField.OBJECT_FIELD_ORDER_TID
        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-子订单号",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_OBJECT_STRING_ARRAY,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=custom_field,
            ))


class ObjectPaymentMethodBuilder(DataTypeBuilderProtocol):
    def gather(self):
        return DimensionInfo(
            dimension=self.build_payment_method(),
            sub_dimensions=[
                self.build_payment_method_taobao_trade_no(),
                self.build_payment_method_alipay_receive_account(),
                self.build_payment_method_alipay_receive_name(),
            ],
        )

    def build_payment_method(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=True,
            data_type=DataDimension.DATA_TYPE_OBJECT_PAYMENT_METHOD,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )

    def build_payment_method_alipay_receive_account(self):
        custom_field = self.custom_field
        custom_field.object_field = (
            CustomField.OBJECT_FIELD_PAYMENT_METHOD_ALIPAY_RECEIVE_ACCOUNT
        )

        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-支付宝-收款账号",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_OBJECT_STRING,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=custom_field,
            ))

    def build_payment_method_alipay_receive_name(self):
        custom_field = self.custom_field
        custom_field.object_field = (
            CustomField.OBJECT_FIELD_PAYMENT_METHOD_ALIPAY_RECEIVE_NAME
        )

        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-支付宝-收款人",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_OBJECT_STRING,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=custom_field,
            ))

    def build_payment_method_taobao_trade_no(self):
        custom_field = self.custom_field
        custom_field.object_field = (
            CustomField.OBJECT_FIELD_PAYMENT_METHOD_ALIPAY_RECEIVE_NAME
        )

        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-淘宝订单号",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_OBJECT_STRING,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=custom_field,
            ))


class ObjectProductBuilder(DataTypeBuilderProtocol):
    def gather(self):
        return DimensionInfo(
            dimension=self.build_product(),
            sub_dimensions=[
                self.build_product_spu(),
                self.build_product_sku(),
                self.build_product_outer_spu(),
                self.build_product_outer_sku(),
            ],
        )

    def build_product(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=False,
            data_type=DataDimension.DATA_TYPE_OBJECT_PRODUCT,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )

    def build_product_spu(self):
        custom_field = self.custom_field
        custom_field.object_field = CustomField.OBJECT_FIELD_PRODUCT_SPU

        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-SPU",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_OBJECT_STRING,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=custom_field,
            ))

    def build_product_sku(self):
        custom_field = self.custom_field
        custom_field.object_field = CustomField.OBJECT_FIELD_PRODUCT_SKU

        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-SKU",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_OBJECT_STRING,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=custom_field,
            ))

    def build_product_outer_sku(self):
        custom_field = self.custom_field
        custom_field.object_field = CustomField.OBJECT_FIELD_PRODUCT_OUTER_SKU

        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-OUTER-SKU",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_OBJECT_STRING,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=custom_field,
            ))

    def build_product_outer_spu(self):
        custom_field = self.custom_field
        custom_field.object_field = CustomField.OBJECT_FIELD_PRODUCT_OUTER_SPU

        return DimensionInfo(
            dimension=DataDimension(
                title=f"{self.default_title}-OUTER-SPU",
                use_in_x_axis=True,
                use_in_y_axis=True,
                use_in_data_range=True,
                data_type=DataDimension.DATA_TYPE_OBJECT_STRING,
                dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                custom_field=custom_field,
            ))


class ObjectReissueProductBuilder(DataTypeBuilderProtocol):
    def gather(self):
        return DimensionInfo(dimension=self.build_reissue_product())

    def build_reissue_product(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=False,
            data_type=DataDimension.DATA_TYPE_OBJECT_REISSUE_PRODUCT,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )


class EnumObjectSelectBuilder(DataTypeBuilderProtocol):
    def gather(self):
        return DimensionInfo(
            dimension=self.build_select(),
            sub_dimensions=self.build_specified_level_select()
        )

    def build_select(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=True,
            data_type=DataDimension.DATA_TYPE_ENUM_OBJECT_SELECT,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )

    def build_specified_level_select(self):
        level = max([item["option_value"].get("level", 1) for item in self.widget_info_list])
        specified_level_select = []
        # ~~从第 1 层开始遍历，不包含最后一层~~
        # ~~实际为 for index in range(0, level-1); specific_level = index + 1~~
        # 2023.10.18 放开限制，在客户使用时可能会找不到最后一层
        for specific_level in range(1, level + 1):
            custom_field = self.custom_field
            custom_field.object_field = CustomField.OBJECT_FIELD_SELECT_SPECIFIED_LEVEL
            custom_field.extra_config["specified_level"] = specific_level
            specified_level_select.append(DimensionInfo(
                dimension=DataDimension(
                    title=f"{self.default_title}-第{specific_level}级",
                    use_in_x_axis=True,
                    use_in_y_axis=True,
                    use_in_data_range=True,
                    data_type=DataDimension.DATA_TYPE_ENUM_OBJECT_SELECT,
                    dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
                    custom_field=custom_field
                )))

        return specified_level_select


class ObjectTracingRecordBuilder(DataTypeBuilderProtocol):
    def gather(self):
        return DimensionInfo(dimension=self.build_tracing_record())

    def build_tracing_record(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=False,
            data_type=DataDimension.DATA_TYPE_OBJECT_TRACING_RECORD,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )


class ObjectRateBuilder(DataTypeBuilderProtocol):
    def gather(self):
        return DimensionInfo(dimension=self.build_rate())

    def build_rate(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=False,
            data_type=DataDimension.DATA_TYPE_OBJECT_RATE,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )


class DAO:
    @classmethod
    def query_widget_info_list(cls, data_source: ReportDataSource):
        from robot_processor.form.models import Form

        if data_source.HasField("sync_form_id"):
            form_ids = list({
                data_source.sync_form_id.value,
                *data_source.form_ids
            })
        else:
            form_ids = list(data_source.form_ids)
        report_widgets = Form.Utils.report_widgets(form_ids)

        widget_info_context = []
        for widget_info_ident, widget_info_list in report_widgets.items():
            represent_widget_info = first(widget_info_list)
            context = BuilderContext(
                widget_type=widget_info_ident.widget_type,
                widget_data_type=widget_info_ident.widget_data_type,
                sync_widget_key=represent_widget_info.key,
                widget_info_list=[
                    WidgetInfo(option_value=widget_info.option_value,
                               key=widget_info.key)
                    for widget_info in widget_info_list
                ]
            )
            widget_info_context.append(context)

        return widget_info_context


class TableBuilder(DataTypeBuilderProtocol):
    def gather(self):
        return DimensionInfo(dimension=self.build_table(),
                             sub_dimensions=self.sub_dimensions)

    def build_table(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=True,
            use_in_data_range=False,
            data_type=DataDimension.DATA_TYPE_TABLE,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )


class ArrayBuilder(DataTypeBuilderProtocol):
    def gather(self):
        return DimensionInfo(dimension=self.build_array())

    def build_array(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=False,
            use_in_data_range=True,
            data_type=DataDimension.DATA_TYPE_ARRAY,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )


class CollectionBuilder(DataTypeBuilderProtocol):
    def gather(self):
        return DimensionInfo(dimension=self.build_collection(), sub_dimensions=self.sub_dimensions)

    def build_collection(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=False,
            use_in_data_range=True,
            data_type=DataDimension.DATA_TYPE_OBJECT,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )


class BooleanBuilder(DataTypeBuilderProtocol):
    def gather(self):
        return DimensionInfo(dimension=self.build_boolean())

    def build_boolean(self):
        return DataDimension(
            title=self.default_title,
            use_in_x_axis=True,
            use_in_y_axis=False,
            use_in_data_range=True,
            data_type=DataDimension.DATA_TYPE_BOOLEAN,
            dimension_type=DataDimension.DATA_DIMENSION_TYPE_CUSTOM_FIELD,
            custom_field=self.custom_field,
        )
