from leyan_proto.digismart.robot.bi.generate_pb2 import DataDimension
from leyan_proto.digismart.robot_web.bi_pb2 import \
    ListReportDataDimensionResponse

DimensionInfo = ListReportDataDimensionResponse.ReportDataDimensionInfo

BO_ID = DataDimension(
    title="工单ID",
    use_in_x_axis=True,
    use_in_y_axis=True,
    use_in_data_range=True,
    data_type=DataDimension.DATA_TYPE_NUMBER,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_ID,
)

BO_CREATE_TIME = DataDimension(
    title="工单创建时间",
    use_in_x_axis=True,
    use_in_y_axis=True,
    use_in_data_range=True,
    data_type=DataDimension.DATA_TYPE_DATETIME,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_CREATE_TIME,
)
BO_CREATE_TIME_YEAR = DimensionInfo(
    dimension=DataDimension(
        title="工单创建时间-年",
        use_in_x_axis=True,
        use_in_y_axis=True,
        use_in_data_range=False,
        data_type=DataDimension.DATA_TYPE_DATE_YEAR,
        dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
        system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_CREATE_TIME,
    ))
BO_CREATE_TIME_MONTH = DimensionInfo(
    dimension=DataDimension(
        title="工单创建时间-月",
        use_in_x_axis=True,
        use_in_y_axis=True,
        use_in_data_range=False,
        data_type=DataDimension.DATA_TYPE_DATE_MONTH,
        dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
        system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_CREATE_TIME,
    ))
BO_CREATE_TIME_WEEK = DimensionInfo(
    dimension=DataDimension(
        title="工单创建时间-周",
        use_in_x_axis=True,
        use_in_y_axis=True,
        use_in_data_range=False,
        data_type=DataDimension.DATA_TYPE_DATE_WEEK,
        dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
        system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_CREATE_TIME,
    ))
BO_CREATE_TIME_DATE = DimensionInfo(
    dimension=DataDimension(
        title="工单创建时间-日期",
        use_in_x_axis=True,
        use_in_y_axis=True,
        use_in_data_range=False,
        data_type=DataDimension.DATA_TYPE_DATE,
        dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
        system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_CREATE_TIME,
    ))
BO_CREATE_TIME_RELATIVE_MONTH = DimensionInfo(
    dimension=DataDimension(
        title="工单创建时间-相对时间-月",
        use_in_x_axis=True,
        use_in_y_axis=True,
        use_in_data_range=False,
        data_type=DataDimension.DATA_TYPE_DATE_RELATIVE_MONTH,
        dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
        system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_CREATE_TIME,
    ))
BO_CREATE_TIME_RELATIVE_WEEK = DimensionInfo(
    dimension=DataDimension(
        title="工单创建时间-相对时间-周",
        use_in_x_axis=True,
        use_in_y_axis=True,
        use_in_data_range=False,
        data_type=DataDimension.DATA_TYPE_DATE_RELATIVE_WEEK,
        dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
        system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_CREATE_TIME,
    ))
BO_CREATE_TIME_RELATIVE_DAY = DataDimension(
    title="工单创建时间-相对时间-日",
    use_in_x_axis=True,
    use_in_y_axis=True,
    use_in_data_range=False,
    data_type=DataDimension.DATA_TYPE_DATE_RELATIVE_DAY,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_CREATE_TIME,
)
BO_CREATE_TIME_ENUM_DATE = DataDimension(
    title="工单创建时间-固定周期",
    use_in_x_axis=False,
    use_in_y_axis=False,
    use_in_data_range=True,
    data_type=DataDimension.DATA_TYPE_ENUM_DATE,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_CREATE_TIME,
)

BO_UPDATE_TIME = DataDimension(
    title="工单最近更新时间",
    use_in_x_axis=True,
    use_in_y_axis=True,
    use_in_data_range=True,
    data_type=DataDimension.DATA_TYPE_DATETIME,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_UPDATE_TIME,
)
BO_UPDATE_TIME_YEAR = DimensionInfo(
    dimension=DataDimension(
        title="工单最近更新时间-年",
        use_in_x_axis=True,
        use_in_y_axis=True,
        use_in_data_range=False,
        data_type=DataDimension.DATA_TYPE_DATE_YEAR,
        dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
        system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_UPDATE_TIME,
    ))
BO_UPDATE_TIME_MONTH = DimensionInfo(
    dimension=DataDimension(
        title="工单最近更新时间-月",
        use_in_x_axis=True,
        use_in_y_axis=True,
        use_in_data_range=False,
        data_type=DataDimension.DATA_TYPE_DATE_MONTH,
        dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
        system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_UPDATE_TIME,
    ))
BO_UPDATE_TIME_WEEK = DimensionInfo(
    dimension=DataDimension(
        title="工单最近更新时间-周",
        use_in_x_axis=True,
        use_in_y_axis=True,
        use_in_data_range=False,
        data_type=DataDimension.DATA_TYPE_DATE_WEEK,
        dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
        system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_UPDATE_TIME,
    ))
BO_UPDATE_TIME_DATE = DimensionInfo(
    dimension=DataDimension(
        title="工单最近更新时间-日期",
        use_in_x_axis=True,
        use_in_y_axis=True,
        use_in_data_range=False,
        data_type=DataDimension.DATA_TYPE_DATE,
        dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
        system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_UPDATE_TIME,
    ))
BO_UPDATE_TIME_RELATIVE_MONTH = DimensionInfo(
    dimension=DataDimension(
        title="工单最近更新时间-相对时间-月",
        use_in_x_axis=True,
        use_in_y_axis=True,
        use_in_data_range=False,
        data_type=DataDimension.DATA_TYPE_DATE_RELATIVE_MONTH,
        dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
        system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_UPDATE_TIME,
    ))
BO_UPDATE_TIME_RELATIVE_WEEK = DimensionInfo(
    dimension=DataDimension(
        title="工单最近更新时间-相对时间-周",
        use_in_x_axis=True,
        use_in_y_axis=True,
        use_in_data_range=False,
        data_type=DataDimension.DATA_TYPE_DATE_RELATIVE_WEEK,
        dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
        system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_UPDATE_TIME,
    ))
BO_UPDATE_TIME_RELATIVE_DAY = DataDimension(
    title="工单最近更新时间-相对时间-日",
    use_in_x_axis=True,
    use_in_y_axis=True,
    use_in_data_range=False,
    data_type=DataDimension.DATA_TYPE_DATE_RELATIVE_DAY,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_UPDATE_TIME,
)
BO_UPDATE_TIME_ENUM_DATE = DataDimension(
    title="工单最近更新时间-固定周期",
    use_in_x_axis=False,
    use_in_y_axis=False,
    use_in_data_range=True,
    data_type=DataDimension.DATA_TYPE_ENUM_DATE,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_UPDATE_TIME,
)

BO_STATUS = DataDimension(
    title="工单状态",
    use_in_x_axis=True,
    use_in_y_axis=True,
    use_in_data_range=True,
    data_type=DataDimension.DATA_TYPE_ENUM_STRING,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_BO_STATUS,
)

BO_PAYMENT_STATUS = DataDimension(
    title="工单支付状态",
    use_in_x_axis=True,
    use_in_y_axis=True,
    use_in_data_range=True,
    data_type=DataDimension.DATA_TYPE_ENUM_STRING,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_PAYMENT_STATUS,
)

SHOP = DataDimension(
    title="店铺",
    use_in_x_axis=True,
    use_in_y_axis=True,
    use_in_data_range=True,
    data_type=DataDimension.DATA_TYPE_ENUM_OBJECT_SHOP,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_SHOP,
)
SHOP_PLATFORM = DimensionInfo(
    dimension=DataDimension(
        title="店铺平台",
        use_in_x_axis=True,
        use_in_y_axis=True,
        use_in_data_range=True,
        data_type=DataDimension.DATA_TYPE_ENUM_STRING,
        dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
        system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_SHOP_PLATFORM,
    ))

FORM = DataDimension(
    title="工单模板",
    use_in_x_axis=True,
    use_in_y_axis=True,
    use_in_data_range=True,
    data_type=DataDimension.DATA_TYPE_ENUM_STRING,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_FORM,
)

STEP = DataDimension(
    title="工单当前步骤",
    use_in_x_axis=True,
    use_in_y_axis=True,
    use_in_data_range=True,
    data_type=DataDimension.DATA_TYPE_ENUM_STRING,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_STEP,
)

CREATOR = DataDimension(
    title="工单创建人",
    use_in_x_axis=True,
    use_in_y_axis=True,
    use_in_data_range=True,
    data_type=DataDimension.DATA_TYPE_ENUM_OBJECT_USER,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    system_field=DataDimension.DATA_DIMENSION_SYSTEM_FIELD_CREATOR,
)
QUERY_LIMIT = DataDimension(
    title="查询数量限制",
    use_in_x_axis=False,
    use_in_y_axis=False,
    use_in_data_range=True,
    data_type=DataDimension.DATA_TYPE_NUMBER_QUERY_LIMIT,
    dimension_type=DataDimension.DATA_DIMENSION_TYPE_SYSTEM_FIELD,
    # 不需要 system field
)


def gather(with_transfer: bool = False):
    """按照数据维度进行聚合，返回列表的顺序为展示顺序，返回报表可选的系统字段数据维度

    Args:
        with_transfer: 是否包含打款相关的信息
    """
    from leyan_proto.digismart.robot_web.bi_pb2 import \
        ListReportDataDimensionResponse

    DimensionInfo = ListReportDataDimensionResponse.ReportDataDimensionInfo

    return list(filter(None, [
        # 工单ID
        DimensionInfo(dimension=BO_ID),
        # 工单状态
        DimensionInfo(dimension=BO_STATUS),
        # 工单支付状态
        DimensionInfo(dimension=BO_PAYMENT_STATUS) if with_transfer else None,
        # 店铺
        DimensionInfo(dimension=SHOP, sub_dimensions=[SHOP_PLATFORM]),
        # 工单创建人
        DimensionInfo(dimension=CREATOR),
        # 当前步骤
        DimensionInfo(dimension=STEP),
        # 工单创建时间-自然
        DimensionInfo(
            dimension=BO_CREATE_TIME,
            sub_dimensions=[
                BO_CREATE_TIME_DATE,  # 日期格式
                BO_CREATE_TIME_YEAR,  # 年
                BO_CREATE_TIME_MONTH,  # 月
                BO_CREATE_TIME_WEEK,  # 周
            ],
        ),
        # 工单创建时间-相对
        DimensionInfo(
            dimension=BO_CREATE_TIME_RELATIVE_DAY,
            sub_dimensions=[
                BO_CREATE_TIME_RELATIVE_WEEK,  # 周
                BO_CREATE_TIME_RELATIVE_MONTH,  # 月
            ],
        ),
        # 工单创建时间-固定周期
        DimensionInfo(
            dimension=BO_CREATE_TIME_ENUM_DATE,
        ),
        # 工单最近更新时间-自然
        DimensionInfo(
            dimension=BO_UPDATE_TIME,
            sub_dimensions=[
                BO_UPDATE_TIME_DATE,  # 日期格式
                BO_UPDATE_TIME_YEAR,  # 年
                BO_UPDATE_TIME_MONTH,  # 月
                BO_UPDATE_TIME_WEEK,  # 周
            ],
        ),
        # 工单最近更新时间-相对
        DimensionInfo(
            dimension=BO_UPDATE_TIME_RELATIVE_DAY,
            sub_dimensions=[
                BO_UPDATE_TIME_RELATIVE_WEEK,  # 周
                BO_UPDATE_TIME_RELATIVE_MONTH,  # 月
            ],
        ),
        # 工单最近更新时间-固定周期
        DimensionInfo(
            dimension=BO_UPDATE_TIME_ENUM_DATE,
        ),
        # 查询条数限制
        DimensionInfo(dimension=QUERY_LIMIT)
    ]))
