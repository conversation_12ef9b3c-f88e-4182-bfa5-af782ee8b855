from typing import TYPE_CHECKING
from typing import Union

from leyan_proto.digismart.common.account_pb2 import Account
from leyan_proto.digismart.robot.bi import generate_pb2
from leyan_proto.digismart.robot.bi import sql_pb2
from leyan_proto.digismart.robot.bi.generate_pb2 import DataStatistics
from result import Err
from result import Ok
from result import Result
from sqlalchemy import CHAR
from sqlalchemy import Select
from sqlalchemy import TextClause
from sqlalchemy import func
from sqlalchemy.sql import label

if TYPE_CHECKING:
    from robot_processor.bi.dashboard import ReportPanel

__all__ = ["AccountMixin", "DatasetMixin"]


class AccountMixin:
    """账号相关"""

    @classmethod
    def account_to_pb2(cls, account_obj: dict):
        account_pb2 = Account.BasicView()
        if account_id := account_obj.get("id"):
            account_pb2.id.value = int(account_id)
        if account_name := account_obj.get("name"):
            account_pb2.name.value = account_name
        if account_type := account_obj.get("type"):
            account_pb2.type = Account.Type.Value(account_type)
        return account_pb2

    @classmethod
    def account_from_pb2(cls, account_pb2: Account.BasicView):
        from robot_processor.utils import message_to_dict

        return message_to_dict(account_pb2)

    def mark_created_by(self, created_by):
        self.created_by = self.account_from_pb2(created_by)

    def mark_updated_by(self, updated_by):
        self.updated_by = self.account_from_pb2(updated_by)


class DatasetMixin:
    """数据集相关的操作"""

    type: "ReportPanel.PanelType"
    mode: "ReportPanel.PanelMode"

    sql_schema: sql_pb2.SQLSchema
    generate_schema: generate_pb2.ReportGenerateSchema

    def get_current_panel_dataset(self: Union["ReportPanel", "DatasetMixin"]) -> Result[list, str]:
        from robot_processor.bi.dashboard.compatible import to_metadata
        from robot_processor.bi.database import Session
        from robot_processor.bi.dataset.etl.pipeline import load_to_echarts_source_from_database
        from robot_processor.bi.dataset.etl.pipeline import load_to_stat_from_database
        from robot_processor.bi.dataset.etl.pipeline import load_to_table_from_database
        from robot_processor.bi.dataset.sql_builder import analysis
        from robot_processor.client import app_config

        if self.dashboard.org_id in app_config.bi_mysql8_whitelist:  # type: ignore[union-attr]
            try:
                metadata = to_metadata(self)  # type: ignore[arg-type]
                sql_builder = analysis.SQLBuilder(metadata)
                title = sql_builder.get_title()
                with Session() as session:
                    data = sql_builder.get_data(session)
                result = [title]
                for item in data:
                    result.append([str(col) for col in item])
                return Ok(result)
            except Exception as e:
                return Err(str(e))
        elif self.type == self.PanelType.TABLE:  # type: ignore[union-attr]
            return load_to_table_from_database(self)  # type: ignore[arg-type]
        elif self.type == self.PanelType.INDICATOR:  # type: ignore[union-attr]
            return load_to_stat_from_database(self)  # type: ignore[arg-type]
        else:
            return load_to_echarts_source_from_database(self)  # type: ignore[arg-type]

    def _check_sql_valid(self, sql) -> Result[None, str]:
        """检查 SQL 语句是否有效"""
        import sqlparse

        if not (parsed := sqlparse.parse(sql)):
            return Err("无效的SQL")
        statement = parsed[0]
        if not statement.get_type() == "SELECT":
            return Err("不是查询语句")
        for token in statement.tokens:
            if token.ttype == sqlparse.tokens.Keyword and token.value.lower() == "from":
                return Err("没有查询字段")
            if isinstance(token, (sqlparse.sql.Identifier, sqlparse.sql.IdentifierList)):
                break
        else:
            return Err("无效的SQL")

        return Ok(None)

    def build_statement_without_limit(self: Union["ReportPanel", "DatasetMixin"]) -> Result[Select | TextClause, str]:
        from sqlalchemy import and_
        from sqlalchemy import cast
        from sqlalchemy import or_
        from sqlalchemy import select
        from sqlalchemy import text

        from robot_processor.bi.generate_schema.data_type import ReportDataType
        from robot_processor.bi.generate_schema.data_type import ReportModelFieldAgent
        from robot_processor.bi.generate_schema.dimension.system import BO_PAYMENT_STATUS
        from robot_processor.utils import use_state

        # SQL 模式
        if self.mode == self.PanelMode.SQL:  # type: ignore[union-attr]
            sql = self.sql_schema.raw_sql
            # 目前 SQL 模式没有特殊处理，直接返回原始 SQL
            return self._check_sql_valid(sql).map(lambda _: text(sql))
        # 设计模式
        select_fields, where_clause, group_by_fields, order_by_fields = [], [], [], []  # type: ignore[var-annotated]
        get_need_join_transfer, set_need_join_transfer = use_state(False)

        # 绑定数据源
        where_clause.append(
            ReportModelFieldAgent.MODEL.form_id.in_(list(self.data_source.form_ids))  # type: ignore[union-attr]
        )
        # 过滤被删除的数据
        where_clause.append(ReportModelFieldAgent.MODEL.deleted.isnot(True))
        # 将维度列加入 select / group by / order by(可选)
        for dimension_display in self.generate_schema.dimensions:
            agent = ReportDataType.auto_init_dimension_display(dimension_display)
            select_fields.append((dimension_display, agent, agent.get_select_field()))
            group_by_field = agent.get_group_by_field()
            if isinstance(group_by_field, tuple):
                group_by_fields.extend(group_by_field)
            else:
                group_by_fields.append(group_by_field)
            if (order_by := agent.get_order_by_field()) is not None:
                if isinstance(order_by, tuple):
                    order_by_fields.extend(order_by)
                else:
                    order_by_fields.append(order_by)

            if agent.dimension == BO_PAYMENT_STATUS:
                set_need_join_transfer(True)

        # 将统计列加入 select / order by(可选)
        for statistics in self.generate_schema.statistics:
            agent = ReportDataType.auto_init_statistics(statistics)
            select_fields.append((statistics, agent, agent.get_select_field()))
            if (order_by := agent.get_order_by_field()) is not None:
                if isinstance(order_by, tuple):
                    order_by_fields.extend(order_by)
                else:
                    order_by_fields.append(order_by)

        if not select_fields:
            return Err("无查询列")

        data_range_groups = self.generate_schema.data_range_groups.data_range_groups
        relation_mapper = {
            generate_pb2.DataRange.RELATION_AND: and_,
            generate_pb2.DataRange.RELATION_OR: or_,
        }
        try:
            where_clause.append(
                relation_mapper[self.generate_schema.data_range_groups.relation](
                    *[
                        relation_mapper[data_range_group.relation](
                            *[
                                ReportDataType.auto_init_data_range(data_range).get_where_clause()
                                for data_range in data_range_group.data_ranges
                                # 在 where 类型的筛选中过滤 limit 的维度
                                if not ReportModelFieldAgent.is_query_limit_dimension(data_range.dimension)
                            ]
                        )
                        for data_range_group in data_range_groups
                    ]
                )
            )
        except Exception as e:
            return Err(f"数据范围配置错误: {e}")

        for data_range_group in data_range_groups:
            for data_range in data_range_group.data_ranges:
                # 判断是否需要 join 打款的表
                if data_range.dimension == BO_PAYMENT_STATUS:
                    set_need_join_transfer(True)

        final_select_fields = []
        for dimension_display_or_statistics, agent, select_field in select_fields:
            if (
                isinstance(dimension_display_or_statistics, DataStatistics)
                and dimension_display_or_statistics.operator == DataStatistics.OPERATOR_RATIO
            ):
                sub_statement = label(
                    agent.model_field_agent.label,
                    func.count(agent.model_field_agent.field_in_select())  # type: ignore[var-annotated]
                    / select(func.count(agent.model_field_agent.field_in_select()))
                    .select_from(ReportModelFieldAgent.MODEL)
                    .where(*where_clause)
                    .subquery(),
                )
                final_select_fields.append(sub_statement)
            elif (
                isinstance(dimension_display_or_statistics, DataStatistics)
                and dimension_display_or_statistics.operator == DataStatistics.OPERATOR_SUM_RATIO
            ):
                sub_statement = label(
                    agent.model_field_agent.label,
                    func.sum(agent.model_field_agent.field_in_select())
                    / select(func.sum(agent.model_field_agent.field_in_select()))
                    .select_from(ReportModelFieldAgent.MODEL)
                    .where(*where_clause)
                    .subquery(),
                )
                final_select_fields.append(sub_statement)
            else:
                final_select_fields.append(select_field)

        statement = (
            select(*final_select_fields)
            .select_from(ReportModelFieldAgent.MODEL)
            .where(*where_clause)
            .group_by(*group_by_fields)
            .order_by(*order_by_fields)
        )
        if get_need_join_transfer():
            statement = statement.join(
                ReportModelFieldAgent.TRANSFER_MODEL,
                ReportModelFieldAgent.TRANSFER_MODEL.business_order_id == cast(ReportModelFieldAgent.MODEL.id, CHAR),
            )
        return Ok(statement)

    def get_statement_query_limit(self) -> int | None:
        from more_itertools import first

        from robot_processor.bi.generate_schema.data_type import ReportDataType
        from robot_processor.bi.generate_schema.data_type import ReportModelFieldAgent

        return first(
            [
                ReportDataType.auto_init_data_range(data_range).get_query_limit_value()
                for data_range_group in self.generate_schema.data_range_groups.data_range_groups
                for data_range in data_range_group.data_ranges
                if ReportModelFieldAgent.is_query_limit_dimension(data_range.dimension)
            ],
            None,
        )

    def build_statement(self: Union["ReportPanel", "DatasetMixin"]) -> Result[Select | TextClause, str]:
        match self.build_statement_without_limit():
            case Ok(TextClause() as text):
                return Ok(text)
            case Ok(Select() as sql_select):
                return Ok(sql_select.limit(self.get_statement_query_limit()))
            case _ as raw:
                return raw
