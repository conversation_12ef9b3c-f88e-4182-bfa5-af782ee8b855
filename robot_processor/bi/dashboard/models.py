import datetime
from dataclasses import dataclass
from enum import IntEnum
from typing import ClassVar, List

import arrow
from google.protobuf import struct_pb2
from google.protobuf.json_format import ParseDict
from leyan_proto.digismart.robot.bi import report_pb2, generate_pb2, sql_pb2
import sqlalchemy as sa
from sqlalchemy.orm import Mapped, mapped_column, relationship

from robot_processor.bi.dashboard.mixins import AccountMixin, DatasetMixin
from robot_processor.constants import timezone
from robot_processor.db import DbBaseModel
from robot_processor.utils import message_to_dict, ensure_mirror_of_pb_enum


@dataclass
class Resource:
    use_in_create: bool = False
    use_in_update: bool = True

    KEY: ClassVar = "resource"


class ReportDashboard(DbBaseModel, AccountMixin):
    __tablename__ = "bi_report_dashboard"

    id: Mapped[int] = mapped_column(
        sa.Integer,
        primary_key=True,
        info={Resource.KEY: Resource(use_in_create=False, use_in_update=False)},
    )
    created_at: Mapped[datetime.datetime] = mapped_column(
        sa.TIMESTAMP,
        nullable=False,
        server_default=sa.func.now(),
        info={Resource.KEY: Resource(use_in_create=False, use_in_update=False)},
    )
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.TIMESTAMP,
        nullable=False,
        server_default=sa.text("CURRENT_TIMESTAMP"),
        info={Resource.KEY: Resource(use_in_create=False, use_in_update=False)},
    )
    org_id: Mapped[str] = mapped_column(
        sa.String(32),
        nullable=False,
        comment="租户ID",
        info={Resource.KEY: Resource(use_in_create=True, use_in_update=False)},
    )
    name: Mapped[str] = mapped_column(
        sa.String(255),
        nullable=False,
        comment="报表名称",
        info={Resource.KEY: Resource(use_in_create=True, use_in_update=True)},
    )
    description: Mapped[str | None] = mapped_column(
        sa.Text,
        comment="报表描述",
        info={Resource.KEY: Resource(use_in_create=True, use_in_update=True)},
    )
    layout_schema: Mapped[dict] = mapped_column(
        sa.JSON,
        nullable=False,
        default=dict,
        comment="在报表视图中的相关信息，如顺序、位置",
        info={Resource.KEY: Resource(use_in_create=True, use_in_update=True)}
    )

    @ensure_mirror_of_pb_enum(report_pb2.ReportDashboard.Status)
    class DashboardStatus(IntEnum):
        STATUS_UNSPECIFIED = 0
        ENABLED = 1
        DISABLED = 2
        DELETED = 3

        @property
        def pb2_value(self):
            return report_pb2.ReportDashboard.Status.Value(self.name)

    status: Mapped[DashboardStatus] = mapped_column(
        sa.Enum(DashboardStatus, native_enum=False, length=32),
        default=DashboardStatus.ENABLED,
        comment="报表状态",
    )
    created_by: Mapped[dict] = mapped_column(
        sa.JSON,
        default=dict,
        comment="创建人信息",
        info={Resource.KEY: Resource(use_in_create=True, use_in_update=True)},
    )
    updated_by: Mapped[dict] = mapped_column(
        sa.JSON,
        default=dict,
        comment="最近更新人信息",
        info={Resource.KEY: Resource(use_in_create=True, use_in_update=True)},
    )
    dataset_last_update_time: Mapped[datetime.datetime] = mapped_column(sa.TIMESTAMP,
                                                                        server_default=sa.text("CURRENT_TIMESTAMP"))

    def to_pb2(self):
        dashboard_pb2 = report_pb2.ReportDashboard()
        dashboard_pb2.id.value = self.id
        dashboard_pb2.created_at.FromDatetime(
            timezone.localize(self.created_at)
            if self.created_at.tzinfo is None
            else self.created_at
        )
        dashboard_pb2.created_by.MergeFrom(self.account_to_pb2(self.created_by))
        dashboard_pb2.updated_at.FromDatetime(
            timezone.localize(self.updated_at)
            if self.updated_at.tzinfo is None
            else self.updated_at
        )
        dashboard_pb2.updated_by.MergeFrom(self.account_to_pb2(self.updated_by))
        dashboard_pb2.name.value = self.name
        if self.description is not None:
            dashboard_pb2.description.value = self.description
        dashboard_pb2.layout_schema.update(self.layout_schema or {})
        dashboard_pb2.status = self.status.pb2_value
        dashboard_pb2.dataset_last_update_time.FromDatetime(
            arrow.now(timezone).datetime
        )

        return dashboard_pb2

    def patch_update(self, new_report_dashboard_pb2: report_pb2.ReportDashboard):
        if new_report_dashboard_pb2.HasField("name"):
            self.name = new_report_dashboard_pb2.name.value
        if new_report_dashboard_pb2.HasField("description"):
            self.description = new_report_dashboard_pb2.description.value
        if new_report_dashboard_pb2.HasField("layout_schema"):
            self.layout_schema = message_to_dict(new_report_dashboard_pb2.layout_schema)
        if new_report_dashboard_pb2.status:
            self.status = self.DashboardStatus(new_report_dashboard_pb2.status)

    def mark_updated_at(self):
        self.updated_at = arrow.now(timezone).datetime


class ReportPanel(DbBaseModel, AccountMixin, DatasetMixin):
    __tablename__ = "bi_report_panel"

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    created_at: Mapped[datetime.datetime] = mapped_column(sa.TIMESTAMP, nullable=False, server_default=sa.func.now())
    created_by: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="创建人信息")
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.TIMESTAMP,
        nullable=False,
        server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    updated_by: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="最近更新人信息")
    dashboard_id: Mapped[int] = mapped_column(sa.ForeignKey("bi_report_dashboard.id"), nullable=False)
    dashboard: Mapped[ReportDashboard] = relationship(ReportDashboard)

    name: Mapped[str] = mapped_column(sa.String(255), nullable=False, comment="报表名称")
    description: Mapped[str | None] = mapped_column(sa.Text, comment="报表描述")

    @ensure_mirror_of_pb_enum(report_pb2.ReportPanel.Mode)
    class PanelMode(IntEnum):
        MODE_UNSPECIFIED = 0
        GENERATE = 1
        SQL = 2

        @property
        def pb2_value(self):
            return report_pb2.ReportPanel.Mode.Value(self.name)

    mode: Mapped[PanelMode] = mapped_column(  # type: ignore[assignment]
        sa.Enum(PanelMode, native_enum=False, length=32),
        default=PanelMode.GENERATE,
        comment="报表模式",
    )

    @ensure_mirror_of_pb_enum(report_pb2.ReportPanel.PanelType)
    class PanelType(IntEnum):
        PANEL_TYPE_UNSPECIFIED = 0
        TABLE = 1
        LINE = 2
        BAR = 3
        PIE = 4
        GAUGE = 5
        INDICATOR = 6
        TEXT = 7

        @property
        def pb2_value(self):
            return report_pb2.ReportPanel.PanelType.Value(self.name)

    type: Mapped[PanelType] = mapped_column(  # type: ignore[assignment]
        sa.Enum(PanelType, native_enum=False, length=32),
        default=PanelType.BAR,
        comment="报表类型",
    )

    @ensure_mirror_of_pb_enum(report_pb2.ReportPanel.Status)
    class PanelStatus(IntEnum):
        STATUS_UNSPECIFIED = 0
        ENABLED = 1
        DISABLED = 2
        DELETED = 3

        @property
        def pb2_value(self):
            return report_pb2.ReportPanel.Status.Value(self.name)

    status: Mapped[PanelStatus] = mapped_column(
        sa.Enum(PanelStatus, native_enum=False, length=32),
        default=PanelStatus.ENABLED,
        comment="报表状态",
    )

    @ensure_mirror_of_pb_enum(report_pb2.ReportPanel.DisplayMode)
    class DisplayMode(IntEnum):
        DISPLAY_MODE_UNSPECIFIED = 0
        DRILL_DOWN = 1
        MULTI_DIMENSION = 2

        @property
        def pb2_value(self):
            return report_pb2.ReportPanel.DisplayMode.Value(self.name)

    display_mode: Mapped[DisplayMode] = mapped_column(
        sa.Enum(DisplayMode, native_enum=False, length=32),
        default=DisplayMode.DISPLAY_MODE_UNSPECIFIED,
        comment="报表数据展示方式"
    )

    @property
    def safe_display_mode(self):
        if self.display_mode is None or self.display_mode == ReportPanel.DisplayMode.DISPLAY_MODE_UNSPECIFIED:
            if self.type == ReportPanel.PanelType.TABLE:
                return ReportPanel.DisplayMode.MULTI_DIMENSION
            else:
                return ReportPanel.DisplayMode.DRILL_DOWN
        return self.display_mode

    ui_schema: Mapped[dict] = mapped_column(sa.JSON, nullable=False, default=dict, comment="报表前端渲染相关的配置信息")
    _generate_schema: Mapped[dict] = mapped_column(
        "generate_schema", sa.JSON, default=dict, comment="设计模式维护的报表维度/数据源/统计数据信息"
    )

    @property
    def generate_schema(self):
        return ParseDict(
            (self._generate_schema or {}),
            generate_pb2.ReportGenerateSchema(),
            ignore_unknown_fields=True,
        )

    @property
    def data_source(self):
        return ParseDict(
            (self._generate_schema or {}).get("data_source", {}),
            report_pb2.ReportDataSource(),
            ignore_unknown_fields=True,
        )

    @data_source.setter
    def data_source(self, new_data_source):
        self._generate_schema = {
            **(self._generate_schema or {}),
            "data_source": message_to_dict(new_data_source),
        }

    @property
    def data_range_groups(self):
        return ParseDict(
            (self._generate_schema or {}).get("data_range_groups", {}),
            generate_pb2.DataRangeGroups(),
            ignore_unknown_fields=True,
        )

    @data_range_groups.setter
    def data_range_groups(self, new_data_range_groups):
        self._generate_schema = {
            **(self._generate_schema or {}),
            "data_range_groups": message_to_dict(new_data_range_groups),
        }

    @property
    def dimensions(self):
        return [
            ParseDict(
                item, generate_pb2.DataDimensionDisplay(), ignore_unknown_fields=True
            )
            for item in (self._generate_schema or {}).get("dimensions", [])
        ]

    @dimensions.setter
    def dimensions(self, new_dimensions: List[generate_pb2.DataDimensionDisplay]):
        from itertools import count

        # 保证 dimension.title 不重复
        title_set = set()
        for dimension_display in new_dimensions:
            dimension = dimension_display.dimension
            if dimension.title in title_set:
                for candidate_index in count(1):
                    candidate_title = f"{dimension.title}-{candidate_index}"
                    if candidate_title not in title_set:
                        dimension.title = candidate_title
                        break
            title_set.add(dimension.title)

        self._generate_schema = {
            **(self._generate_schema or {}),
            "dimensions": list(map(message_to_dict, new_dimensions)),
        }

    @property
    def statistics(self):
        return [
            ParseDict(item, generate_pb2.DataStatistics(), ignore_unknown_fields=True)
            for item in (self._generate_schema or {}).get("statistics", [])
        ]

    @statistics.setter
    def statistics(self, new_statistics):
        from itertools import count
        from robot_processor.bi.generate_schema.data_type._base import ReportStatisticsAgent

        title_set = set()
        # 保证 dimension.title 不重复
        for statistics in new_statistics:
            dimension = statistics.dimension
            operator_label = ReportStatisticsAgent.operators_label_map[statistics.operator]
            if dimension.title.split("-")[-1] == operator_label:
                title = dimension.title
            else:
                title = f"{dimension.title}-{operator_label}"
            if title in title_set:
                for candidate_index in count(1):
                    candidate_title = f"{title}-{candidate_index}"
                    if candidate_title not in title_set:
                        dimension.title = candidate_title
                        break
            else:
                dimension.title = title
            title_set.add(dimension.title)

        self._generate_schema = {
            **(self._generate_schema or {}),
            "statistics": list(map(message_to_dict, new_statistics)),
        }

    _sql_schema: Mapped[dict] = mapped_column("sql_schema", sa.JSON, default=dict, comment="SQL模式")

    @property
    def sql_schema(self):
        return ParseDict(
            (self._sql_schema or {}), sql_pb2.SQLSchema(), ignore_unknown_fields=True
        )

    @sql_schema.setter
    def sql_schema(self, new_sql_schema):
        self._sql_schema = message_to_dict(new_sql_schema)

    _result: Mapped[dict] = mapped_column("result", sa.JSON, nullable=False, default=dict, comment="报表查询结果")

    @property
    def sort_rules(self):
        from itertools import chain

        by, ascending = [], []
        for dimension_info in chain(self.dimensions, self.statistics):
            if not dimension_info.order_by:
                continue
            is_asc = dimension_info.order_by == generate_pb2.DataDimension.ORDER_BY_ASC
            by.append(dimension_info.dimension)
            ascending.append(is_asc)

        return list(zip(by, ascending))

    @property
    def round_rules(self):
        dimension, digits = [], []
        for dimension_info in self.statistics:
            if dimension_info.operator == generate_pb2.DataStatistics.OPERATOR_SUM:
                dimension.append(dimension_info.dimension)
                digits.append(2)
        return list(zip(dimension, digits))

    @property
    def percent_rules(self):
        dimensions = []
        for yAxis in self.statistics:
            if yAxis.operator in [generate_pb2.DataStatistics.OPERATOR_RATIO,
                                  generate_pb2.DataStatistics.OPERATOR_SUM_RATIO]:
                dimensions.append(yAxis.dimension)
        return dimensions

    @property
    def drill_down_status(self):
        if hasattr(self, "_drill_down_status"):
            return self._drill_down_status
        return report_pb2.ReportPanel.DrillDownStatus(
            drill_path=[xAxis.dimension.title for xAxis in self.dimensions],
        )

    @drill_down_status.setter
    def drill_down_status(self, new_drill_down_status: report_pb2.ReportPanel.DrillDownStatus):
        self._drill_down_status = report_pb2.ReportPanel.DrillDownStatus()
        self._drill_down_status.CopyFrom(new_drill_down_status)

    def with_drill_down_status(self, drill_down_status: report_pb2.ReportPanel.DrillDownStatus):
        self.drill_down_status = drill_down_status
        return self

    def get_dataset_as_struct(self):
        dataset = struct_pb2.Struct()
        (
            self.get_current_panel_dataset()
            .map(lambda source: dataset.update({"source": source}))
            .map_err(lambda reason: dataset.update({"source": [], "reason": str(reason)}))
        )

        return dataset

    def to_pb2(self, fill_data_flag=True):
        panel_pb2 = report_pb2.ReportPanel()
        panel_pb2.id.value = self.id

        panel_pb2.created_at.FromDatetime(
            timezone.localize(self.created_at)
            if self.created_at.tzinfo is None
            else self.created_at
        )
        panel_pb2.created_by.MergeFrom(self.account_to_pb2(self.created_by))
        panel_pb2.updated_at.FromDatetime(
            timezone.localize(self.updated_at)
            if self.updated_at.tzinfo is None
            else self.updated_at
        )
        panel_pb2.updated_by.MergeFrom(self.account_to_pb2(self.updated_by))
        panel_pb2.name.value = self.name
        if self.description is not None:
            panel_pb2.description.value = self.description
        # UI
        panel_pb2.mode = self.mode.pb2_value
        panel_pb2.panel_type = self.type.pb2_value
        if self.display_mode is not None:
            panel_pb2.display_mode = self.display_mode.pb2_value
        panel_pb2.status = self.status.pb2_value
        panel_pb2.ui_schema.update(self.ui_schema or {})
        # 数据
        panel_pb2.data_source.MergeFrom(self.data_source)
        # 设计模式
        panel_pb2.dimensions.extend(self.dimensions)
        panel_pb2.statistics.extend(self.statistics)
        panel_pb2.data_range_groups.MergeFrom(self.data_range_groups)
        # SQL 模式
        panel_pb2.sql_schema.MergeFrom(self.sql_schema)
        # 数据
        if fill_data_flag:
            panel_pb2.dataset.MergeFrom(self.get_dataset_as_struct())
            panel_pb2.dataset_last_update_time.FromDatetime(arrow.now(timezone).datetime)

        if self.safe_display_mode == ReportPanel.DisplayMode.DRILL_DOWN:
            panel_pb2.drill_down_status.CopyFrom(self.drill_down_status)

        return panel_pb2

    def patch_update(self, new_report_panel_pb2: report_pb2.ReportPanel):
        if new_report_panel_pb2.HasField("name"):
            self.name = new_report_panel_pb2.name.value
        if new_report_panel_pb2.HasField("description"):
            self.description = new_report_panel_pb2.description.value
        # UI
        if new_report_panel_pb2.mode:
            self.mode = ReportPanel.PanelMode(new_report_panel_pb2.mode)
        if new_report_panel_pb2.panel_type:
            self.type = ReportPanel.PanelType(new_report_panel_pb2.panel_type)
        if new_report_panel_pb2.status:
            self.status = ReportPanel.PanelStatus(new_report_panel_pb2.status)
        if new_report_panel_pb2.HasField("ui_schema"):  # type struct_pb2.Struct
            self.ui_schema = message_to_dict(new_report_panel_pb2.ui_schema)
        if new_report_panel_pb2.display_mode:
            self.display_mode = ReportPanel.DisplayMode(new_report_panel_pb2.display_mode)
        # 数据
        if new_report_panel_pb2.HasField("data_source"):  # property setter
            self.data_source = new_report_panel_pb2.data_source
        # 设计模式
        if new_report_panel_pb2.HasField("data_range_groups"):  # property setter
            self.data_range_groups = new_report_panel_pb2.data_range_groups
        if new_report_panel_pb2.dimensions:  # property setter
            self.dimensions = new_report_panel_pb2.dimensions
        if new_report_panel_pb2.statistics:  # property setter
            self.statistics = new_report_panel_pb2.statistics
        # SQL 模式
        if new_report_panel_pb2.HasField("sql_schema"):  # property setter
            self.sql_schema = new_report_panel_pb2.sql_schema

    def mark_updated_at(self):
        self.updated_at = arrow.now(timezone).datetime
        self.dashboard.mark_updated_at()
