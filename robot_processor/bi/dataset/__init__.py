"""报表查询数据格式，参考 echarts 的数据集

    References:
        https://echarts.apache.org/handbook/zh/concepts/dataset

"""
from typing import Literal, List, TypedDict


class Dimension(TypedDict):
    """常用图表所描述的数据大部分是“二维表”结构"""
    # 维度可以有单独的名字，便于在图表中显示。
    name: str
    # 大多数情况下，我们并不需要去设置维度类型，因为 ECharts 会自动尝试判断。但是如果不足够准确时，可以手动设置维度类型。
    # 维度类型（dimension type）可以取这些值：
    # - number: 默认，表示普通数据。
    # - ordinal: 对于类目、文本这些 string 类型的数据，如果需要能在数轴上使用，须是 'ordinal' 类型。
    # - time: 表示时间数据。设置成 'time' 则能支持自动解析数据成时间戳（timestamp），比如该维度的数据是 '2017-05-10'，会自动被解析。
    # - float: 如果设置成 'float'，在存储时候会使用 TypedArray，对性能优化有好处。
    # - int: 如果设置成 'int'，在存储时候会使用 TypedArray，对性能优化有好处。
    type: Literal["number", "ordinal", "time", "float", "int"]


class Dataset(TypedDict):
    dimensions: List[Dimension]
    # 二维数据表，内层列表的长度同 dimension 长度
    source: List[list]


class xAxis(TypedDict):
    """x 轴维度

    Attributes:
        type: 类型
        name: 名称
    """
    type: Literal["category"]
    name: str


class yAxis(TypedDict):
    """y 轴维度

    Attributes:
        type: 类型
        name: 名称
    """
    type: Literal["value"]
    name: str


class Series(TypedDict):
    """数据序列

    Attributes:
        name: 名称
        type: 类型
    """
    type: Literal["bar"]
    name: str
