from typing import TYPE_CHECKING

import pandas as pd
from result import Result, Ok, Err

from robot_processor.bi.dataset.etl import extract, transform, load

if TYPE_CHECKING:
    from robot_processor.bi.dashboard import ReportPanel


def get_raw_sql(panel: "ReportPanel") -> Result[str, str]:
    return (
        panel.build_statement()
        .map(lambda statement: statement.compile(compile_kwargs={"literal_binds": True}))
        .map(str)
    )


def panel_dataset_from_database(panel: "ReportPanel") -> Result[pd.DataFrame, str]:
    """从数据库中加载数据"""
    dimensions = [each.dimension for each in panel.dimensions]
    statistics = panel.statistics

    def compatible_display_mode(statement):
        from sqlalchemy import TextClause, Select
        match (panel.safe_display_mode, statement):
            case (panel.DisplayMode.DRILL_DOWN, _):
                return Ok(statement)
            case (_, TextClause()):
                return Ok(statement)
            case (_, Select()):
                return Ok(statement.limit(panel.get_statement_query_limit()))
            case _:
                err = TypeError(f"不支持的 display_mode={panel.safe_display_mode}, statement={type(statement)}")
                return Err(err)

    return (
        panel.build_statement_without_limit().and_then(compatible_display_mode)
        .and_then(lambda statement: extract.dataset_from_database(statement))
        # 将 select 组件的多余层级修剪掉
        .map(lambda dataset: transform.widget_object_type_pruning(dataset, dimensions))
        # 将 object 类型转换为 string 类型
        .map(lambda dataset: transform.widget_object_type_readable(dataset, dimensions))
        # 将 array 类型拆分为多行
        .map(
            lambda dataset: transform.widget_array_type_explode(
                dataset, dimensions, statistics
            )
        )
        # 将 decimal 类型转换成 float 类型
        .map(lambda dataset: transform.decimal_type_as_float(dataset, statistics))
        # 将 Nan 和 null 转换为 <未填写>
        .map(transform.nan_and_null_to_unfilled)
    )


def load_to_echarts_source_from_database(panel: "ReportPanel") -> Result[list, str]:
    """将 dataset 保存为 echarts 的 dataset source 格式"""
    if panel.safe_display_mode == panel.DisplayMode.DRILL_DOWN:
        query_limit = panel.get_statement_query_limit()
    else:
        query_limit = None

    return panel_dataset_from_database(panel).map(lambda dataset: load.dataset_to_echarts_source(
        dataset=dataset,
        drill_down_status=panel.drill_down_status if panel.drill_down_status.drill_filters else None,
        values=[statistics.dimension.title for statistics in panel.statistics],
        sort_rules=panel.sort_rules,
        round_rules=panel.round_rules,
        percent_rules=panel.percent_rules,
        query_limit=query_limit
    ))


def load_to_table_from_database(panel: "ReportPanel") -> Result[list, str]:
    """将 dataset 保存为 table 格式"""
    if panel.safe_display_mode == panel.DisplayMode.DRILL_DOWN:
        return load_to_echarts_source_from_database(panel)
    else:
        return panel_dataset_from_database(panel).map(lambda dataset: load.dataset_to_table_source(
            dataset=dataset,
            sort_rules=panel.sort_rules
        ))


def load_to_stat_from_database(panel: "ReportPanel") -> Result[list, str]:
    """将 dataset 保存为 stat 格式"""
    return panel_dataset_from_database(panel).map(lambda dataset: load.dataset_to_stat_source(dataset))
