from datetime import timedelta as _timedelta

from leyan_cloud import HOST_IP as _HOST_IP
from statsd import StatsClient as _StatsClient

_stats = _StatsClient(host=_HOST_IP, prefix="statsd.robot-processor")

# 开票成功（不包括获取回执）
ISSUE_SUCCESS = "invoice.do_issue.success"
# 开票失败（不包括获取回执）
ISSUE_FAILED = "invoice.do_issue.failed"
# 开票超时（不区分开票/获取回执）
ISSUE_TIMEOUT = "invoice.issue_timeout"
# 获取回执成功
FETCH_ISSUED_SUCCESS = "invoice.fetch_issued.success"
# 获取回执失败
FETCH_ISSUED_FAILED = "invoice.fetch_issued.failed"


def timing(stat: str, delta: _timedelta):
    _stats.timing(stat, delta=delta, rate=1)
    _stats.incr(stat, count=1, rate=1)


def incr(stat: str):
    _stats.incr(stat, count=1, rate=1)
