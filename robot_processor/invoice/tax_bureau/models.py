from datetime import date
from datetime import datetime
from decimal import Decimal
from enum import IntEnum
from enum import auto
from typing import TYPE_CHECKING
from typing import Any
from typing import cast

import sqlalchemy as sa
from leyan_proto.digismart.robot.invoice.tax_bureau_pb2 import Corporate as pb_Corporate
from leyan_proto.digismart.robot.invoice.tax_bureau_pb2 import CorporateTaxer as pb_CorporateTaxer
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow
from leyan_proto.digismart.robot_web.invoice.rpa_pb2 import LoginResult as pb_LoginResult
from result import Err
from result import Ok
from sqlalchemy.orm import Load
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy.sql.operators import eq as sql_eq
from sqlalchemy.sql.operators import ne as sql_ne

from robot_processor.db import DbBaseModel
from robot_processor.db import db
from robot_processor.db import in_transaction
from robot_processor.invoice.common.models import OperationLog
from robot_processor.invoice.common.models import PlatformApplySyncMethod
from robot_processor.invoice.configs import invoice_config
from robot_processor.invoice.utils import cron_str_to_struct
from robot_processor.invoice.utils import cron_struct_to_str
from robot_processor.invoice.utils import desensitize
from robot_processor.invoice.workflow.third_party import jd
from robot_processor.invoice.workflow.third_party import pdd
from robot_processor.invoice.workflow.utils import infer_shop_sync_platform_apply_method
from robot_processor.t_cron import scheduler
from robot_processor.utils import ensure_mirror_of_pb_enum


class Corporate(DbBaseModel):
    __tablename__ = "invoice_corporate"
    __table_args__ = (sa.Index("idx_org_id", "org_id"),)

    @ensure_mirror_of_pb_enum(pb_Corporate.Status)
    class Status(IntEnum):
        # 可用
        ACTIVE = 0
        # 已删除
        DELETED = 1

    @ensure_mirror_of_pb_enum(pb_Corporate.TaxBureau)
    class TaxBureau(IntEnum):
        TAX_BUREAU_UNSPECIFIED = 0
        ZHEJIANG = auto()
        SHANGHAI = auto()
        # 安徽
        ANHUI = 3
        # 北京
        BEIJING = 4
        # 重庆
        CHONGQING = 5
        # 大连
        DALIAN = 6
        # 福建
        FUJIAN = 7
        # 广东
        GUANGDONG = 8
        # 广西
        GUANGXI = 9
        # 贵州
        GUIZHOU = 10
        # 甘肃
        GANSU = 11
        # 河北
        HEBEI = 12
        # 黑龙江
        HEILONGJIANG = 13
        # 河南
        HENAN = 14
        # 湖北
        HUBEI = 15
        # 湖南
        HUNAN = 16
        # 海南
        HAINAN = 17
        # 吉林
        JILIN = 18
        # 江苏
        JIANGSU = 19
        # 江西
        JIANGXI = 20
        # 辽宁
        LIAONING = 21
        # 内蒙古
        NEIMENGGU = 22
        # 宁波
        NINGBO = 23
        # 宁夏
        NINGXIA = 24
        # 青岛
        QINGDAO = 25
        # 青海
        QINGHAI = 26
        # 山西
        SHANXI = 27
        # 陕西
        SHAANXI = 28
        # 四川
        SICHUAN = 29
        # 山东
        SHANDONG = 30
        # 深圳
        SHENZHEN = 31
        # 天津
        TIANJIN = 32
        # 厦门
        XIAMEN = 33
        # 西藏
        XIZANG = 34
        # 新疆
        XINJIANG = 35
        # 云南
        YUNNAN = 36

    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)

    """销售方信息"""
    # 销售方识别号; 统一社会信用代码
    credit_id = mapped_column(sa.String(20))
    # 销售方名称; 企业名称
    name = mapped_column(sa.String(256))
    # 销售方地址
    address = mapped_column(sa.String(256))
    # 销售方电话
    phone = mapped_column(sa.String(32))
    # 销售方开户行名称
    bank = mapped_column(sa.String(256))
    # 销售方开户行账号
    bank_account = mapped_column(sa.String(256))
    # 税务系统相关的信息
    competent_tax_bureau = mapped_column(sa.Enum(TaxBureau))
    # 销售方信息绑定的飞梭租户
    org_id = mapped_column(sa.Integer)
    # 状态，用于软删除标记
    status = mapped_column(sa.Enum(Status), default=Status.ACTIVE)
    # 是否为小规模纳税人标识
    is_small_taxpayer = mapped_column(sa.Boolean, default=False)
    # 关联的办税人信息
    taxers = relationship(lambda: CorporateTaxer, back_populates="corporate")
    # 关联的店铺信息
    shops = relationship(lambda: CorporateShop, back_populates="corporate")

    def to_detailed_view(self):
        view = pb_Corporate.DetailedView(
            id=self.id,
            credit_id=self.credit_id,
            name=self.name,
            address=self.address,
            phone=self.phone,
            bank=self.bank,
            bank_account=self.bank_account,
            competent_tax_bureau=self.competent_tax_bureau,
            taxers=[taxer.to_basic_view() for taxer in self.taxers if taxer.status == CorporateTaxer.Status.ACTIVE],
            shops=[shop.to_view() for shop in self.shops],
            is_small_taxpayer=self.is_small_taxpayer,
        )
        return view

    def log_create(self, user, init_data: dict):
        changes = dict(action="create", init=init_data)
        OperationLog.create(OperationLog.Model.CORPORATE, self.id, user, changes)

    def log_update(self, user, diff: dict):
        if not diff:
            return
        changes = dict(action="update", diff=diff)
        OperationLog.create(OperationLog.Model.CORPORATE, self.id, user, changes)

    def log_delete(self, user):
        changes = dict(action="delete")
        OperationLog.create(OperationLog.Model.CORPORATE, self.id, user, changes)

    @classmethod
    @in_transaction()
    def create(cls, org_id: int, view: pb_Corporate.EditableView) -> "Corporate":
        self = cls(org_id=org_id)
        self.update(view)
        db.session.add(self)

        return self

    @in_transaction()
    def update(self, view: pb_Corporate.EditableView) -> dict[str, Any]:
        diff = {}

        if self.credit_id != view.credit_id:
            diff["credit_id"] = dict(old=self.credit_id, new=view.credit_id)
            self.credit_id = view.credit_id
        if self.name != view.name:
            diff["name"] = dict(old=self.name, new=view.name)
            self.name = view.name
        if self.address != view.address:
            diff["address"] = dict(old=self.address, new=view.address)
            self.address = view.address
        if self.phone != view.phone:
            diff["phone"] = dict(old=self.phone, new=view.phone)
            self.phone = view.phone
        if self.bank != view.bank:
            diff["bank"] = dict(old=self.bank, new=view.bank)
            self.bank = view.bank
        if self.bank_account != view.bank_account:
            diff["bank_account"] = dict(old=self.bank_account, new=view.bank_account)
            self.bank_account = view.bank_account
        new_competent_tax_bureau = Corporate.TaxBureau(view.competent_tax_bureau)
        if self.competent_tax_bureau != new_competent_tax_bureau:
            diff["competent_tax_bureau"] = dict(
                old=self.competent_tax_bureau.name if self.competent_tax_bureau else None,
                new=new_competent_tax_bureau.name,
            )
            self.competent_tax_bureau = new_competent_tax_bureau
        if self.is_small_taxpayer != view.is_small_taxpayer:
            diff["is_small_taxpayer"] = dict(old=self.is_small_taxpayer, new=view.is_small_taxpayer)
            self.is_small_taxpayer = view.is_small_taxpayer

        return diff

    @in_transaction()
    def delete(self):
        self.status = self.Status.DELETED

    # Query methods
    @classmethod
    def get_by_org_id(cls, org_id):
        """通过飞梭租户ID获取企业信息

        Args:
            org_id (int): 飞梭租户ID
        Returns:
            list[Corporate]: 飞梭租户已绑定的企业信息，会过滤掉已删除的企业
        """
        stmt = (
            sa.select(cls)
            .where(sql_eq(cls.org_id, org_id))
            .where(sql_eq(cls.status, cls.Status.ACTIVE))
            .options(Load(cls).joinedload(cls.taxers))
            .options(Load(cls).joinedload(cls.shops))
        )
        result = db.session.execute(stmt)
        return result.scalars().unique().all()

    @classmethod
    def get_by_id(cls, corporate_id):
        """通过统一社会信用代码获取企业信息

        Args:
            corporate_id (int): 企业ID
        Returns:
            Corporate: 企业信息
        """
        stmt = (
            sa.select(cls)
            .where(sql_eq(cls.id, corporate_id))
            .where(sql_eq(cls.status, cls.Status.ACTIVE))
            .options(Load(cls).joinedload(cls.taxers))
            .options(Load(cls).joinedload(cls.shops))
        )
        result = db.session.execute(stmt)
        return result.scalars().unique().one_or_none()

    @classmethod
    def get_and_check_corporate(cls, org_id, corporate_id):
        """通过飞梭租户ID和企业ID获取企业信息

        Args:
            org_id (int): 飞梭租户 id
            corporate_id (int): 企业 id
        Returns:
            Ok[Corporate] | Err[Exception]: 成功返回企业信息，失败返回异常
        """
        if not corporate_id:
            return Err(ValueError("企业 id 非法"))
        if not (corporate := cls.get_by_id(corporate_id)):
            return Err(ValueError("未找到企业信息"))
        if corporate.org_id != org_id:
            return Err(PermissionError("未找到企业信息，请确认登录账号是否正确"))
        return Ok(corporate)

    @classmethod
    def get_by_credit_id(cls, org_id, credit_id):
        stmt = (
            sa.select(cls)
            .where(sql_eq(cls.org_id, org_id))
            .where(sql_eq(cls.credit_id, credit_id))
            .where(sql_eq(cls.status, cls.Status.ACTIVE))
        )
        result = db.session.execute(stmt)
        return result.scalar_one()

    @classmethod
    def check_credit_id_exists(cls, org_id, credit_id, self_id):
        """检查统一社会信用代码是否已存在

        Args:
            org_id (int): 飞梭租户ID
            credit_id (str): 统一社会信用代码
            self_id (int | None): 当前企业ID，用于排除自身
        Returns:
            bool: 是否存在
        """
        exists = (
            cls.query.filter(sql_eq(cls.org_id, org_id))
            .filter(sql_eq(cls.credit_id, credit_id))
            .filter(sql_ne(cls.id, self_id))
            .filter(sql_ne(cls.status, cls.Status.DELETED))
            .exists()
        )
        return db.session.query(exists).scalar()

    @classmethod
    def get_by_shop(cls, shop):
        from robot_processor.shop.models import Shop

        shop = cast(Shop, shop)
        stmt = (
            sa.select(cls)
            .select_from(cls)
            .join(cls.shops)
            .where(sql_eq(cls.org_id, shop.org_id))
            .where(sql_eq(cls.status, cls.Status.ACTIVE))
            .where(sql_eq(CorporateShop.sid, shop.sid))
            .where(sql_eq(CorporateShop.platform, shop.platform))
        )
        result = db.session.execute(stmt)
        return result.scalar_one_or_none()

    def get_default_taxer(self):
        if valid := [taxer for taxer in self.taxers if taxer.status == CorporateTaxer.Status.ACTIVE]:
            return cast(CorporateTaxer, valid[0])
        return None

    def populate_seller_info(self, pb_workflow: pb_InvoiceWorkflow.EditableView):
        pb_workflow.seller_name = self.name
        pb_workflow.seller_credit_id = self.credit_id
        if self.address:
            pb_workflow.seller_address.value = self.address
        else:
            pb_workflow.ClearField("seller_address")
        if self.phone:
            pb_workflow.seller_phone.value = self.phone
        else:
            pb_workflow.ClearField("seller_phone")
        if self.bank:
            pb_workflow.seller_bank.value = self.bank
        else:
            pb_workflow.ClearField("seller_bank")
        if self.bank_account:
            pb_workflow.seller_bank_account.value = self.bank_account
        else:
            pb_workflow.ClearField("seller_bank_account")


class CorporateTaxer(DbBaseModel):
    __tablename__ = "invoice_corporate_taxer"
    __table_args__ = (sa.Index("idx_corporate_id", "corporate_id"),)

    @ensure_mirror_of_pb_enum(pb_CorporateTaxer.Role)
    class Role(IntEnum):
        ROLE_UNSPECIFIED = 0
        # 法定代表人
        LEGAL_REPRESENTATIVE = auto()
        # 财务负责人
        FINANCIAL_MANAGER = auto()
        # 办税员
        TAX_AGENT = auto()
        # 开票员
        INVOICE_AGENT = auto()

    @ensure_mirror_of_pb_enum(pb_CorporateTaxer.Status)
    class Status(IntEnum):
        ACTIVE = 0
        DELETED = 1

    # 非业务唯一标识
    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    # 所属企业
    corporate_id = mapped_column(sa.Integer, sa.ForeignKey("invoice_corporate.id"))
    corporate = relationship(lambda: Corporate, back_populates="taxers")
    # 办税人账号信息 - 登录账号
    # 账号中心 - 个人信息管理 - 用户名
    account = mapped_column(sa.String(64))
    # 登录密码
    password = mapped_column(sa.String(128))
    # 办税人基本信息 - 姓名
    name = mapped_column(sa.String(64))
    # 办税人基本信息 - 手机号码
    phone = mapped_column(sa.String(32))
    # 办税人默认登录身份
    role = mapped_column(sa.Enum(Role))
    # 状态，用于软删除标记
    status = mapped_column(sa.Enum(Status), default=Status.ACTIVE)
    # 上次认证时间
    auth_at = mapped_column(sa.DateTime, nullable=True)

    # 登录信息
    runtime_id = mapped_column(sa.Integer, nullable=True)  # 上次登录客户端
    runtime_username = mapped_column(sa.String(64), nullable=True)  # 登录客户端的用户名
    runtime_session = mapped_column(sa.Text, nullable=True)  # 登录客户端的税局会话信息
    runtime_session_created_at = mapped_column(sa.DateTime, nullable=True)  # 登录客户端的上次心跳时间

    def to_basic_view(self, with_decrypt: bool = False):
        view = pb_CorporateTaxer.BasicView(
            id=self.id,
            account=self.account,
            name=self.name or "",
            phone=self.phone,
            role=self.role,
        )
        if not with_decrypt:
            view.name = desensitize.name(view.name)
            view.phone = desensitize.phone(view.phone)

        return view

    def log_create(self, user, init_data):
        changes = dict(action="create", init=init_data)
        OperationLog.create(OperationLog.Model.CORPORATE_TAXER, self.id, user, changes)

    def log_update(self, user, diff: dict):
        if not diff:
            return
        changes = dict(action="update", diff=diff)
        OperationLog.create(OperationLog.Model.CORPORATE_TAXER, self.id, user, changes)

    def log_delete(self, user):
        changes = dict(action="delete")
        OperationLog.create(OperationLog.Model.CORPORATE_TAXER, self.id, user, changes)

    @staticmethod
    def encrypt(password: str) -> str:
        from base64 import b64encode

        from Crypto.Cipher import AES
        from Crypto.Util.Padding import pad

        encrypt_key = bytes.fromhex(invoice_config.encrypt_key)
        cipher = AES.new(encrypt_key, AES.MODE_CBC)
        ct_bytes = cipher.encrypt(pad(password.encode("utf-8"), AES.block_size))
        iv = cipher.iv
        return b64encode(iv + ct_bytes).decode("utf-8")  # type: ignore[operator]

    @staticmethod
    def decrypt_password(encrypted_password: str) -> str:
        from base64 import b64decode

        from Crypto.Cipher import AES
        from Crypto.Util.Padding import unpad

        key = bytes.fromhex(invoice_config.encrypt_key)
        enc = b64decode(encrypted_password)
        iv = enc[: AES.block_size]  # 提取初始化向量
        ct = enc[AES.block_size :]
        cipher = AES.new(key, AES.MODE_CBC, iv)
        decrypted_password = unpad(cipher.decrypt(ct), AES.block_size).decode("utf-8")
        return decrypted_password

    @classmethod
    def get_password(cls, credit_id: str, account: str):
        stmt = (
            sa.select(cls.password)
            .select_from(CorporateTaxer)
            .join(Corporate, Corporate.id == CorporateTaxer.corporate_id)
            .where(Corporate.credit_id == credit_id)
            .where(cls.account == account)
        )
        result = db.session.execute(stmt)
        return result.scalar_one()

    @classmethod
    def get_auth_info(cls, credit_id: str, account: str):
        stmt = (
            sa.select(CorporateTaxer)
            .select_from(CorporateTaxer)
            .join(Corporate, Corporate.id == CorporateTaxer.corporate_id)
            .where(Corporate.credit_id == credit_id)
            .where(cls.account == account)
            .where(cls.status == cls.Status.ACTIVE)
            .options(Load(CorporateTaxer).joinedload(CorporateTaxer.corporate))
        )
        result = db.session.execute(stmt)
        taxer = result.scalar_one()
        return dict(
            competent_tax_bureau=taxer.corporate.competent_tax_bureau.name,
            credit_id=credit_id,
            account=account,
            password=taxer.password,
            role=taxer.role.name,
            runtime_info=taxer.get_runtime_info(),
        )

    def get_runtime_info(self):
        if not self.runtime_session:
            return None
        return dict(id=self.runtime_id, session=self.runtime_session)

    @in_transaction()
    def auth(self, auth_at: datetime):
        self.auth_at = auth_at

    @in_transaction()
    def update_runtime_info(self, taxer_info: pb_LoginResult.TaxerInfo):
        self.runtime_id = taxer_info.runtime_id
        self.runtime_username = taxer_info.runtime_username
        self.runtime_session = taxer_info.runtime_session
        self.runtime_session_created_at = datetime.strptime(taxer_info.runtime_session_created_at, "%Y-%m-%d %H:%M:%S")

    @classmethod
    @in_transaction()
    def create(cls, corporate: Corporate, view: pb_CorporateTaxer.EditableView) -> "CorporateTaxer":
        self = cls()
        self.corporate = corporate
        self.update(view)
        db.session.add(self)

        return self

    @in_transaction()
    def update(self, view: pb_CorporateTaxer.EditableView) -> dict[str, Any]:
        diff = {}

        if self.account != view.account:
            diff["account"] = dict(old=self.account, new=view.account)
            self.account = view.account
        if view.HasField("password"):
            diff["password"] = dict(old=self.password, new=self.encrypt(view.password.value))
            self.password = self.encrypt(view.password.value)
        if self.phone != view.phone:
            diff["phone"] = dict(old=self.phone, new=view.phone)
            self.phone = view.phone
        new_role = CorporateTaxer.Role(view.role)
        if self.role != new_role:
            diff["role"] = dict(old=self.role.name if self.role else None, new=new_role.name)
            self.role = new_role

        return diff

    @in_transaction()
    def delete(self):
        self.status = self.Status.DELETED

    @classmethod
    @in_transaction()
    def delete_by_corporate_id(cls, corporate_id):
        stmt = sa.update(cls).where(cls.corporate_id == corporate_id).values(status=cls.Status.DELETED)
        db.session.execute(stmt)

    @classmethod
    def get_by_id(cls, corporate_taxer_id):
        stmt = sa.select(cls).where(cls.id == corporate_taxer_id).where(sql_eq(cls.status, cls.Status.ACTIVE))
        result = db.session.execute(stmt)
        return result.scalar_one_or_none()

    @classmethod
    def get_by_corporate_account(cls, corporate_id: int, account: str):
        stmt = sa.select(cls).where(sql_eq(cls.corporate_id, corporate_id)).where(sql_eq(cls.account, account))
        result = db.session.execute(stmt)
        return result.scalar_one_or_none()

    @classmethod
    def get_by_org(cls, org_id: int):
        stmt = (
            sa.select(cls)
            .select_from(Corporate)
            .join(Corporate.taxers)
            .where(sql_eq(Corporate.org_id, org_id))
            .where(sql_ne(Corporate.status, Corporate.Status.DELETED))
            .where(sql_ne(CorporateTaxer.status, cls.Status.DELETED))
            .order_by(sa.desc(CorporateTaxer.auth_at))
        )
        result = db.session.execute(stmt)
        return result.scalars().all()

    @classmethod
    def check_account_exists(cls, corporate_id, account, self_id):
        """检查账号是否已存在

        Args:
            corporate_id (int): 企业ID
            account (str): 账号
            self_id (int | None): 当前办税人ID，用于排除自身
        Returns:
            bool: 是否存在
        """
        exists = cls.query.filter(
            sql_eq(cls.corporate_id, corporate_id),
            sql_eq(cls.account, account),
            sql_ne(cls.id, self_id),
            sql_ne(cls.status, cls.Status.DELETED),
        ).exists()
        return db.session.query(exists).scalar()


class CorporateShop(DbBaseModel):
    __tablename__ = "invoice_corporate_shop"
    __table_args__ = (
        sa.Index("idx_corporate_shop", "corporate_id", "sid", "platform", unique=True),
        sa.Index("idx_shop", "sid", "platform"),
    )

    # 非业务唯一标识
    id = mapped_column(sa.Integer, primary_key=True, autoincrement=True)

    # 所属企业
    corporate_id = mapped_column(sa.Integer, sa.ForeignKey("invoice_corporate.id"))
    corporate = relationship(lambda: Corporate, back_populates="shops")

    # 店铺信息
    # sid + platform 确定唯一店铺信息
    # platform 为 upper case 的平台名称
    sid = mapped_column(sa.String(64))
    platform = mapped_column(sa.String(64))

    # 店铺配置信息
    sync_platform_apply = mapped_column(sa.Boolean, default=False, comment="自动同步平台开票申请")
    sync_platform_apply_cron = mapped_column(sa.String(64), nullable=True, comment="同步平台开票申请配置")
    post_back_platform_apply = mapped_column(sa.Boolean, default=False, comment="自动回传平台开票申请")

    # schedule 任务详情
    platform_apply_sync_method = mapped_column(
        sa.Enum(PlatformApplySyncMethod, native_enum=False, length=128), nullable=True
    )
    platform_apply_schedule_id = mapped_column(sa.String(128), nullable=True)
    platform_apply_schedule_cron = mapped_column(sa.String(64), nullable=True, comment="调度表达式")
    platform_apply_schedule_timeout = mapped_column(sa.Integer, nullable=True)
    platform_apply_schedule_name = mapped_column(sa.String(128), nullable=True)
    platform_apply_schedule_executed_at = mapped_column(sa.DateTime, nullable=True)

    if TYPE_CHECKING:
        from robot_processor.shop.models import Shop

        _shop: Shop | None

    @property
    def shop(self):
        from robot_processor.shop.models import Shop

        if hasattr(self, "_shop"):
            return self._shop
        else:
            self._shop = Shop.query.filter_by(
                org_id=str(self.corporate.org_id), sid=self.sid, platform=self.platform
            ).one()
            return self._shop

    def to_view(self):
        if self.sync_platform_apply_cron:
            cron = cron_str_to_struct(self.sync_platform_apply_cron)
        else:
            cron = None
        return pb_Corporate.ShopConfigView(
            sid=self.sid,
            platform=self.platform,
            sync_platform_apply=self.sync_platform_apply,
            post_back_platform_apply=self.post_back_platform_apply,
            sync_platform_apply_cron=cron,
        )

    def log_create(self, user, info):
        changes = dict(action="create", info=info)
        OperationLog.create(OperationLog.Model.CORPORATE_SHOP, self.id, user, changes)

    def log_update(self, user, diff: dict):
        if not diff:
            return
        changes = dict(action="update", diff=diff)
        OperationLog.create(OperationLog.Model.CORPORATE_SHOP, self.id, user, changes)

    def log_delete(self, user):
        changes = dict(action="delete")
        OperationLog.create(OperationLog.Model.CORPORATE_SHOP, self.id, user, changes)

    @classmethod
    @in_transaction()
    def create(cls, corporate: Corporate, shop_view: pb_Corporate.ShopConfigView):
        self = cls()
        self.corporate = corporate
        self.sid = shop_view.sid
        self.platform = shop_view.platform
        self.sync_platform_apply = shop_view.sync_platform_apply
        self.post_back_platform_apply = shop_view.post_back_platform_apply
        try:
            self.sync_platform_apply_cron = cron_struct_to_str(shop_view.sync_platform_apply_cron)
            if shop_view.sync_platform_apply_cron.HasField("interval"):
                match shop_view.sync_platform_apply_cron.unit:
                    case pb_Corporate.SyncPlatformApplyCron.Unit.SECOND:
                        schedule_timeout = shop_view.sync_platform_apply_cron.interval.value
                    case pb_Corporate.SyncPlatformApplyCron.Unit.MINUTE:
                        schedule_timeout = shop_view.sync_platform_apply_cron.interval.value * 60
                    case _:  # 任务超时时间上限为 1 小时
                        schedule_timeout = 3600
            else:  # 不复杂化 crontab 的间隔计算了
                schedule_timeout = 3600
            self.platform_apply_schedule_timeout = schedule_timeout
        except ValueError:
            pass
        # 仅在第一次绑定店铺时初始化 schedule method
        try:
            self.platform_apply_sync_method = infer_shop_sync_platform_apply_method(self.shop)
        except NotImplementedError:
            pass

        db.session.add(self)

        return self

    @in_transaction()
    def update(self, shop_view: pb_Corporate.ShopConfigView):
        diff = dict()
        if self.sync_platform_apply != shop_view.sync_platform_apply:
            diff["sync_platform_appy"] = dict(old=self.sync_platform_apply, new=shop_view.sync_platform_apply)
            self.sync_platform_apply = shop_view.sync_platform_apply
        if self.post_back_platform_apply != shop_view.post_back_platform_apply:
            diff["post_back_platform_apply"] = dict(
                old=self.post_back_platform_apply, new=shop_view.post_back_platform_apply
            )
            self.post_back_platform_apply = shop_view.post_back_platform_apply
        try:
            cron = cron_struct_to_str(shop_view.sync_platform_apply_cron)
        except ValueError:
            cron = None
        if self.sync_platform_apply_cron != cron:
            diff["sync_platform_apply_cron"] = dict(old=self.sync_platform_apply_cron, new=cron)
            self.sync_platform_apply_cron = cron
            if cron:
                match shop_view.sync_platform_apply_cron.unit:
                    case pb_Corporate.SyncPlatformApplyCron.Unit.SECOND:
                        schedule_timeout = shop_view.sync_platform_apply_cron.interval.value
                    case pb_Corporate.SyncPlatformApplyCron.Unit.MINUTE:
                        schedule_timeout = shop_view.sync_platform_apply_cron.interval.value * 60
                    case _:  # 任务超时时间上限为 1 小时
                        schedule_timeout = 3600
            else:
                schedule_timeout = None
            # 仅 schedule_cron 发生变更时，才重新计算任务超时时间
            if self.platform_apply_schedule_timeout != schedule_timeout:
                diff["schedule_timeout"] = dict(old=self.platform_apply_schedule_timeout, new=schedule_timeout)
                self.platform_apply_schedule_timeout = schedule_timeout

        return diff

    @in_transaction()
    def delete(self):
        """物理删除，企业店铺绑定通过日志记录"""
        if self.platform_apply_schedule_id:
            scheduler.close_schedule(self.platform_apply_schedule_id)
        db.session.delete(self)

    @classmethod
    def get_by_corporate_id(cls, corporate_id):
        """查询企业关联的店铺信息

        Args:
            corporate_id (int): 企业ID
        Returns:
            list[CorporateShop]: 企业关联的店铺信息
        """
        stmt = sa.select(cls).where(sql_eq(cls.corporate_id, corporate_id))
        result = db.session.execute(stmt)
        return result.scalars().all()

    @classmethod
    def get_by_org_id(cls, org_id: int) -> list["CorporateShop"]:
        stmt = sa.select(cls).join(cls.corporate).where(sql_eq(Corporate.org_id, org_id))
        result = db.session.execute(stmt)
        return list(result.scalars().all())

    @classmethod
    def get_by_shop(cls, sid, platform):
        """通过店铺信息查询企业店铺绑定信息

        Args:
            sid (str): 店铺ID
            platform (str): 平台名称
        Returns:
            CorporateShop | None: 企业店铺绑定信息
        """
        stmt = sa.select(cls).where(sql_eq(cls.sid, sid), sql_eq(cls.platform, platform))
        result = db.session.execute(stmt)
        return result.scalar_one_or_none()

    @in_transaction()
    def sync_schedule_state(self):
        schedule_name = "{}: {}".format(self.shop.nick, self.platform_apply_sync_method)

        @in_transaction()
        def create_schedule():
            from robot_processor.invoice.workflow.third_party import doudian
            from robot_processor.invoice.workflow.third_party import qianniu

            self.platform_apply_schedule_cron = self.sync_platform_apply_cron
            timeout = self.platform_apply_schedule_timeout
            match self.platform_apply_sync_method:
                case PlatformApplySyncMethod.QN:
                    response = scheduler.create_schedule(
                        qianniu.poll_qn_invoice_list,
                        schedule_name,
                        self.platform_apply_schedule_cron,
                        timeout,
                        shop_id=self.shop.id,
                        timerange="",
                    )
                case PlatformApplySyncMethod.DOUDIAN:
                    response = scheduler.create_schedule(
                        doudian.poll_doudian_invoice_list,
                        schedule_name,
                        self.platform_apply_schedule_cron,
                        timeout,
                        shop_id=self.shop.id,
                        timerange="",
                    )
                case PlatformApplySyncMethod.JD:
                    response = scheduler.create_schedule(
                        jd.poll_jd_invoice_list,
                        schedule_name,
                        self.platform_apply_schedule_cron,
                        timeout,
                        shop_id=self.shop.id,
                        timerange="",
                    )
                case PlatformApplySyncMethod.PDD:
                    response = scheduler.create_schedule(
                        pdd.poll_pdd_invoice_list,
                        schedule_name,
                        self.platform_apply_schedule_cron,
                        timeout,
                        shop_id=self.shop.id,
                        timerange="",
                    )
                case _:
                    return

            self.platform_apply_schedule_id = response.schedule.schedule_id
            self.platform_apply_schedule_timeout = timeout
            self.platform_apply_schedule_name = schedule_name

        @in_transaction()
        def update_schedule():
            self.platform_apply_schedule_cron = self.sync_platform_apply_cron
            response = scheduler.update_schedule(
                self.platform_apply_schedule_id,
                schedule_name,
                self.platform_apply_schedule_cron,
                self.platform_apply_schedule_timeout,
                shop_id=self.shop.id,
                timerange="",
            )
            self.platform_apply_schedule_id = response.schedule.schedule_id

        @in_transaction()
        def stop_schedule():
            if self.platform_apply_schedule_id is None:
                return
            scheduler.close_schedule(self.platform_apply_schedule_id)
            self.platform_apply_schedule_id = None

        if self.sync_platform_apply:
            if self.platform_apply_schedule_id is None:
                create_schedule()
            elif self.platform_apply_schedule_cron != self.sync_platform_apply_cron:
                update_schedule()
        else:
            stop_schedule()


class IssuedAggregation(DbBaseModel):
    __tablename__ = "invoice_issued_aggregation"
    __table_args__ = (
        sa.UniqueConstraint("org_id", "credit_id", "dimension", "aggregation_date", name="uk_issued_aggregation"),
    )

    @ensure_mirror_of_pb_enum(pb_Corporate.IssuedAggregation.Dimension)
    class Dimension(IntEnum):
        DAY = 0
        MONTH = 1
        QUARTER = 2

        @property
        def pb_value(self):
            return pb_Corporate.IssuedAggregation.Dimension.Value(self.name)

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    org_id: Mapped[int]
    credit_id: Mapped[str] = mapped_column(sa.String(20))
    dimension: Mapped[Dimension] = mapped_column(sa.Enum(Dimension, native_enum=False, length=10))
    aggregation_date: Mapped[date]  # 当 dimension=QUARTER 时，aggregation_date 表示季度的第一天
    issued_amount: Mapped[Decimal] = mapped_column(sa.DECIMAL(15, 2), default=0)
    updated_at: Mapped[datetime] = mapped_column(sa.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_pb(self):
        return pb_Corporate.IssuedAggregation(
            dimension=self.dimension.pb_value,
            aggregation_date=self.aggregation_date.strftime("%Y-%m-%d"),
            issued_amount=str(self.issued_amount),
        )

    @classmethod
    @in_transaction()
    def issued(cls, org_id: int, credit_id: str, issue_time: datetime, amount: Decimal):
        issue_date = issue_time.date()
        day = cls.find_or_create(
            org_id=org_id, credit_id=credit_id, dimension=IssuedAggregation.Dimension.DAY, aggregation_date=issue_date
        )
        month = cls.find_or_create(
            org_id=org_id,
            credit_id=credit_id,
            dimension=IssuedAggregation.Dimension.MONTH,
            aggregation_date=cls.get_month_aggregation_date(issue_date),
        )
        quarter = cls.find_or_create(
            org_id=org_id,
            credit_id=credit_id,
            dimension=IssuedAggregation.Dimension.QUARTER,
            aggregation_date=cls.get_quarter_aggregation_date(issue_date),
        )
        day.issued_amount = day.issued_amount + amount if day.issued_amount else amount
        month.issued_amount = month.issued_amount + amount if month.issued_amount else amount
        quarter.issued_amount = quarter.issued_amount + amount if quarter.issued_amount else amount

    @classmethod
    def get_month_aggregation_date(cls, provide_date: date):
        """获取月份的第一天"""
        return date(provide_date.year, provide_date.month, 1)

    @classmethod
    def get_quarter_aggregation_date(cls, provide_date: date):
        """获取季度的第一天"""
        return date(provide_date.year, provide_date.month - (provide_date.month - 1) % 3, 1)
