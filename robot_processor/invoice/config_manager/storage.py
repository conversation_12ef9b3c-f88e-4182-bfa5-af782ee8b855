from typing import Any

from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import Integer
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column

from robot_processor.db import DbBaseModel
from robot_processor.db import db
from robot_processor.db import in_transaction
from robot_processor.invoice.config_manager.defination import ConfigKey


class InvoiceConfigModel(DbBaseModel):
    """发票相关配置"""

    __tablename__ = "invoice_config"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    org_id: Mapped[int]
    config_key: Mapped[ConfigKey] = mapped_column(SQLEnum(ConfigKey, native_enum=False, length=64))
    config_val = mapped_column(JSON)


def list_org_configured_configs(org_id: int) -> dict[ConfigKey, Any]:
    return {config.config_key: config.config_val for config in InvoiceConfigModel.query.filter_by(org_id=org_id).all()}


@in_transaction()
def update_config(org_id: int, config_key: ConfigKey, config_val: Any):
    configured = InvoiceConfigModel.find_or_create(org_id=org_id, config_key=config_key)
    configured.config_val = config_val
    return configured


def is_configured(org_id: int, config_key: ConfigKey) -> bool:
    return db.session.query(InvoiceConfigModel.query.filter_by(org_id=org_id, config_key=config_key).exists()).scalar()
