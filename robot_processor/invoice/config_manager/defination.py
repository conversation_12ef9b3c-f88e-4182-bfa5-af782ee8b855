import uuid
from dataclasses import dataclass
from decimal import Decimal
from enum import StrEnum
from typing import Any
from typing import Callable
from typing import cast

from result import Err
from result import Ok
from robot_types.core import EnumOption
from robot_types.core import TypeSpec
from robot_types.helper import deserialize
from robot_types.helper.symbol import RenderConfigResolver
from robot_types.helper.symbol import SymbolResolver
from robot_types.model.invoice import config_manager


@dataclass
class _ConfigExtraInfo:
    category: "ConfigCategory"
    description: str
    type_spec: TypeSpec
    default_value: Any


class ConfigCategory(StrEnum):
    APPROVAL = "approval", "审批"
    INVOICE = "invoice", "开票"
    TRADE = "trade", "订单联动"
    GOODS = "goods", "商品数据源"
    NOTIFICATION = "notification", "消息通知"

    def __new__(cls, value, *args):
        self = str.__new__(cls, value)
        self._value_ = value
        return self

    def __init__(self, _, label):
        self.label = label


class ConfigKey(StrEnum):
    # 审批规则
    APPROVAL_RULESET = "approval.ruleset", _ConfigExtraInfo(
        ConfigCategory.APPROVAL,
        "配置审批规则后，符合规则的发票申请会使用该规则进行审批",
        config_manager.type_spec.ApprovalRuleset,
        config_manager.ApprovalRuleset([]),
    )
    # 订单数据来源
    TRADE_BUILDER_MODE = "trade-builder.mode", _ConfigExtraInfo(
        ConfigCategory.TRADE,
        "使用订单联动开票/平台开票申请同步功能时，发票关联的订单信息来源",
        config_manager.type_spec.TradeBuilderMode,
        config_manager.TradeBuilderMode.ERP,
    )
    # 商品数据来源
    GOODS_MODE = "goods.mode", _ConfigExtraInfo(
        ConfigCategory.GOODS,
        "发票商品管理模块的商品数据来源",
        config_manager.type_spec.GoodsMode,
        config_manager.GoodsMode.ERP,
    )
    # 平台发票申请同步后的状态
    PLATFORM_APPLY_SYNC_AFTER_ACTION = (
        "platform-apply-sync.after-action",
        _ConfigExtraInfo(
            ConfigCategory.INVOICE,
            "平台发票申请同步创建的发票申请是否需要手动提审",
            config_manager.type_spec.PlatformApplySyncAfterAction,
            config_manager.PlatformApplySyncAfterAction.SAVE,
        ),
    )
    # 企业主体开票金额阈值校验
    CORPORATE_INVOICE_THRESHOLD_RULE = "corporate.invoice-threshold.rule", _ConfigExtraInfo(
        ConfigCategory.INVOICE,
        "校验指定企业主体或全部企业主体在某个时间段内开票金额汇总值超出阈值后校验提醒",
        config_manager.type_spec.CorporateInvoiceThresholdRuleset,
        None,
    )
    # 自动开票时，开票明细策略
    PLATFORM_APPLY_SYNC_ISSUING_ITEM_STRATEGY = (
        "platform-apply-sync.issuing-item.select-strategy",
        _ConfigExtraInfo(
            ConfigCategory.INVOICE,
            "平台发票申请的开票明细策略",
            config_manager.type_spec.IssuingItemStrategy,
            config_manager.IssuingItemStrategy(config_manager.IssuingItemStrategy.Mode.GOODS),
        ),
    )
    # 固定明细开票时，是否带入商品的数量信息
    FIXED_ISSUING_ITEM_SYNC_GOODS_NUM = (
        "platform-apply-sync.issuing-item.fixed-issuing-item.sync-goods-num",
        _ConfigExtraInfo(
            ConfigCategory.INVOICE,
            "固定明细开票时，是否带入商品的数量信息",
            config_manager.type_spec.FixedIssuingItemSyncGoodsNum,
            config_manager.FixedIssuingItemSyncGoodsNum.KEEP_EMPTY,
        ),
    )
    # 自动开票规则
    INVOICE_AUTO_ISSUED_RULE = "invoice.auto-issued.rule", _ConfigExtraInfo(
        ConfigCategory.INVOICE,
        "自动开票规则",
        config_manager.type_spec.AutoIssued,
        config_manager.AutoIssued(False, []),
    )
    # 开票备注默认值
    INVOICE_REMARK_TEMPLATE = "invoice.remark-template", _ConfigExtraInfo(
        ConfigCategory.INVOICE,
        "开票备注默认值",
        config_manager.type_spec.InvoiceRemarkTemplate,
        [],
    )
    # 默认展示购买方地址信息
    SHOW_BUYER_ADDRESS = "invoice.show-buyer-address", _ConfigExtraInfo(
        ConfigCategory.INVOICE,
        "默认展示购买方地址信息",
        config_manager.type_spec.ShowBuyerAddress,
        config_manager.ShowBuyerAddress.HIDE,
    )
    # 默认展示购买方银行信息
    SHOW_BUYER_BANK = "invoice.show-buyer-bank", _ConfigExtraInfo(
        ConfigCategory.INVOICE,
        "默认展示购买方银行信息",
        config_manager.type_spec.ShowBuyerBank,
        config_manager.ShowBuyerBank.HIDE,
    )
    # 默认展示销售方地址信息
    SHOW_SELLER_ADDRESS = "invoice.show-seller-address", _ConfigExtraInfo(
        ConfigCategory.INVOICE,
        "默认展示销售方地址信息",
        config_manager.type_spec.ShowSellerAddress,
        config_manager.ShowSellerAddress.HIDE,
    )
    # 默认展示销售方银行信息
    SHOW_SELLER_BANK = "invoice.show-seller-bank", _ConfigExtraInfo(
        ConfigCategory.INVOICE,
        "默认展示销售方银行信息",
        config_manager.type_spec.ShowSellerBank,
        config_manager.ShowSellerBank.HIDE,
    )
    # 过滤金额=0元的发票项目
    SKIP_ZERO_PRICE_ITEMS = "invoice.skip-zero-price-items", _ConfigExtraInfo(
        ConfigCategory.INVOICE,
        "过滤金额=0元的发票项目",
        config_manager.type_spec.SkipZeroPriceItems,
        True,
    )
    # 消息发送渠道
    NOTIFICATION_CHANNELS = "notification.send-channel", _ConfigExtraInfo(
        ConfigCategory.NOTIFICATION, "消息发送渠道", config_manager.type_spec.NotificationChannel, []
    )
    # 消息模板
    NOTIFICATION_TEMPLATES = "notification.templates", _ConfigExtraInfo(
        ConfigCategory.NOTIFICATION,
        "消息模板",
        config_manager.type_spec.NotificationTemplate,
        [],
    )
    # 自动发消息规则
    NOTIFICATION_RULES = "notification.rules", _ConfigExtraInfo(
        ConfigCategory.NOTIFICATION, "自动发消息规则", config_manager.type_spec.NotificationRules, []
    )

    def __new__(cls, value, _extra_info):
        self = str.__new__(cls, value)
        self._value_ = value
        self._extra_info_ = _extra_info  # type: ignore[attr-defined]
        return self

    def __init__(self, value, *args):
        super().__init__(value)  # type: ignore[call-arg]

    @property
    def extra_info(self) -> _ConfigExtraInfo:
        return self._extra_info_  # type: ignore[attr-defined]

    def get_converter(self) -> Callable[[Any], Any]:
        from robot_types.model.invoice.config_manager import ApprovalRuleset
        from robot_types.model.invoice.config_manager import AutoIssued
        from robot_types.model.invoice.config_manager import CorporateInvoiceThresholdRuleset
        from robot_types.model.invoice.config_manager import FixedIssuingItemSyncGoodsNum
        from robot_types.model.invoice.config_manager import GoodsMode
        from robot_types.model.invoice.config_manager import IssuingItemStrategy
        from robot_types.model.invoice.config_manager import NotificationChannel
        from robot_types.model.invoice.config_manager import NotificationRule
        from robot_types.model.invoice.config_manager import NotificationTemplate
        from robot_types.model.invoice.config_manager import PlatformApplySyncAfterAction
        from robot_types.model.invoice.config_manager import ShowBuyerAddress
        from robot_types.model.invoice.config_manager import ShowBuyerBank
        from robot_types.model.invoice.config_manager import ShowSellerAddress
        from robot_types.model.invoice.config_manager import ShowSellerBank
        from robot_types.model.invoice.config_manager import TradeBuilderMode

        match self:
            case ConfigKey.APPROVAL_RULESET:
                return lambda x: deserialize(x, ApprovalRuleset)
            case ConfigKey.TRADE_BUILDER_MODE:
                return lambda x: TradeBuilderMode(x)
            case ConfigKey.GOODS_MODE:
                return lambda x: GoodsMode(x)
            case ConfigKey.PLATFORM_APPLY_SYNC_AFTER_ACTION:
                return lambda x: PlatformApplySyncAfterAction(x)
            case ConfigKey.CORPORATE_INVOICE_THRESHOLD_RULE:
                return lambda x: deserialize(x, CorporateInvoiceThresholdRuleset)
            case ConfigKey.PLATFORM_APPLY_SYNC_ISSUING_ITEM_STRATEGY:
                return lambda x: deserialize(x, IssuingItemStrategy)
            case ConfigKey.FIXED_ISSUING_ITEM_SYNC_GOODS_NUM:
                return lambda x: FixedIssuingItemSyncGoodsNum(x)
            case ConfigKey.INVOICE_AUTO_ISSUED_RULE:
                return lambda x: deserialize(x, AutoIssued)
            case ConfigKey.SHOW_BUYER_BANK:
                return lambda x: ShowBuyerBank(x)
            case ConfigKey.SHOW_BUYER_ADDRESS:
                return lambda x: ShowBuyerAddress(x)
            case ConfigKey.SHOW_SELLER_BANK:
                return lambda x: ShowSellerBank(x)
            case ConfigKey.SHOW_SELLER_ADDRESS:
                return lambda x: ShowSellerAddress(x)
            case ConfigKey.NOTIFICATION_TEMPLATES:
                return lambda x: deserialize(x, list[NotificationTemplate])
            case ConfigKey.NOTIFICATION_CHANNELS:
                return lambda x: deserialize(x, list[NotificationChannel])
            case ConfigKey.NOTIFICATION_RULES:
                return lambda x: deserialize(x, list[NotificationRule])
            case _:
                return lambda x: x

    def before_update_check(self, org_id: int, config_val):
        from robot_processor.invoice.config_manager import ConfigManager

        def check_notification_templates():
            broker = ConfigManager(org_id=org_id)
            new_template_map = {
                template.id: template
                for template in cast(list[config_manager.NotificationTemplate], self.get_converter()(config_val))
                if not template.id.startswith("system")
            }
            old_template_map = {
                template.id: template
                for template in cast(list[config_manager.NotificationTemplate], broker.get(self))
                if not template.id.startswith("system")
            }
            to_remove_template_ids = set(old_template_map.keys()) - set(new_template_map.keys())
            if not to_remove_template_ids:
                return Ok(None)
            template_rule_mapping = {
                notification.template_id: rule
                for rule in cast(list[config_manager.NotificationRule], broker.get(ConfigKey.NOTIFICATION_RULES))
                for notification in rule.notifications
            }
            in_use = []
            for to_remove_template_id in to_remove_template_ids:
                if to_remove_template_id in template_rule_mapping:
                    rule_name = template_rule_mapping[to_remove_template_id].name
                    template_name = old_template_map[to_remove_template_id].name
                    in_use.append(f"规则 {rule_name} 正在使用通知内容 {template_name}")
            if in_use:
                return Err(PermissionError(";".join(in_use)))
            return Ok(None)

        def check_notification_channel():
            broker = ConfigManager(org_id=org_id)
            new_channel_map = {
                channel.id: channel
                for channel in cast(list[config_manager.NotificationChannel], self.get_converter()(config_val))
                if not channel.id.startswith("system")
            }
            old_channel_map = {
                channel.id: channel
                for channel in cast(list[config_manager.NotificationChannel], broker.get(self))
                if not channel.id.startswith("system")
            }
            to_remove_channel_ids = set(old_channel_map.keys()) - set(new_channel_map.keys())
            template_channel_mapping = {
                notification.channel_id: rule
                for rule in cast(list[config_manager.NotificationRule], broker.get(ConfigKey.NOTIFICATION_RULES))
                for notification in rule.notifications
            }
            in_use = []
            for to_remove_channel_id in to_remove_channel_ids:
                if to_remove_channel_id in template_channel_mapping:
                    rule_name = template_channel_mapping[to_remove_channel_id].name
                    channel_name = old_channel_map[to_remove_channel_id].name
                    in_use.append(f"规则 {rule_name} 正在使用通知渠道 {channel_name}")
            if in_use:
                return Err(PermissionError(";".join(in_use)))
            to_add_channel_ids = set(new_channel_map.keys()) - set(old_channel_map.keys())
            for to_add_channel_id in to_add_channel_ids:
                to_add_channel = new_channel_map[to_add_channel_id]
                if to_add_channel.channel_type == config_manager.NotificationChannelType.EMAIL:
                    if "smtp_password" not in to_add_channel.config:
                        return Err(ValueError(f"渠道 {to_add_channel.name} 未配置授权码"))
            return Ok(None)

        match self:
            case ConfigKey.NOTIFICATION_TEMPLATES:
                return check_notification_templates()
            case ConfigKey.NOTIFICATION_CHANNELS:
                return check_notification_channel()
            case _:
                return Ok(None)

    def update_prepare_process(self, org_id: int, config_val):
        from robot_processor.invoice.config_manager import ConfigManager

        match self:
            case ConfigKey.NOTIFICATION_CHANNELS:
                i = 0
                while i < len(config_val):
                    if config_val[i]["id"].startswith("system"):
                        config_val.pop(i)
                    else:
                        i += 1
                broker = ConfigManager(org_id=org_id)
                exist_email_channel_map = {
                    channel.id: channel
                    for channel in cast(
                        list[config_manager.NotificationChannel], broker.get(ConfigKey.NOTIFICATION_CHANNELS)
                    )
                    if channel.channel_type == config_manager.NotificationChannelType.EMAIL
                }
                for channel in config_val:
                    if channel["id"] not in exist_email_channel_map:
                        continue
                    if "smtp_password" in channel["config"]:
                        continue
                    channel["config"]["smtp_password"] = exist_email_channel_map[channel["id"]].config["smtp_password"]
            case ConfigKey.NOTIFICATION_TEMPLATES:
                i = 0
                while i < len(config_val):
                    if config_val[i]["id"].startswith("system"):
                        config_val.pop(i)
                    else:
                        i += 1


symbol_resolver = SymbolResolver()


def _init_symbol_resolver():
    def _get_credit_id_enum_option(*args, **kwargs):
        from robot_processor.invoice.tax_bureau.models import Corporate

        if not (org_id := kwargs.get("org_id")):
            return None
        options: list[EnumOption] = []
        for corporate in Corporate.get_by_org_id(org_id):
            options.append(EnumOption(corporate.name, corporate.credit_id))
        options.append(EnumOption("所有企业主体", "_all"))
        return options

    def _get_issuing_item_enum_option(*args, **kwargs):
        from robot_processor.invoice.goods.models import IssuingItem

        if not (org_id := kwargs.get("org_id")):
            return None
        options: list[EnumOption] = []
        for issuing_item in IssuingItem.query.filter_by(org_id=org_id):
            options.append(
                EnumOption(
                    "{}(税率: {}% 简称: {})".format(
                        issuing_item.title,
                        issuing_item.tax_rate.quantize(Decimal("0.00")),
                        issuing_item.tax_name_abbr,
                    ),
                    issuing_item.id,
                )
            )
        return options

    symbol_resolver.registry.register(
        [ConfigKey.CORPORATE_INVOICE_THRESHOLD_RULE, "rules", "items", "credit_id"],
        RenderConfigResolver(render_enum=_get_credit_id_enum_option),
    )
    symbol_resolver.registry.register(
        [ConfigKey.PLATFORM_APPLY_SYNC_ISSUING_ITEM_STRATEGY, "fixed_issuing_item"],
        RenderConfigResolver(render_enum=_get_issuing_item_enum_option),
    )
    symbol_resolver.registry.register(
        [ConfigKey.INVOICE_AUTO_ISSUED_RULE, "rules", "items", "credit_id"],
        RenderConfigResolver(render_enum=_get_credit_id_enum_option),
    )


def _register_structure_hook():
    from robot_types.helper import converter

    def approval_ruleset_route_structure(obj, cl):
        if obj is None:
            return None
        if obj.get("auto_mapper") and not obj.get("manual_mapper"):
            mode = config_manager.ApprovalRuleset.Routes.Mode.AUTO
        elif obj.get("manual_mapper") and not obj.get("auto_mapper"):
            mode = config_manager.ApprovalRuleset.Routes.Mode.MANUAL
        elif obj.get("auto_mapper") and obj.get("manual_mapper"):
            raise ValueError("auto and manual cannot be set meanwhile", obj)
        else:
            raise ValueError("approval.ruleset.routes.mode must be set", obj)
        match mode:
            case config_manager.ApprovalRuleset.Routes.Mode.MANUAL:
                manual_mapper = deserialize(
                    obj["manual_mapper"],
                    config_manager.ApprovalRuleset.Routes.ManualMapper,
                )
                if manual_mapper is None:
                    return None
                return cl(
                    status=deserialize(obj["status"], config_manager.ApprovalRuleset.Routes.Status),
                    mode=mode,
                    filter=obj.get("filter") or {"relation": "and", "conditions": []},
                    manual_mapper=manual_mapper,
                )
            case config_manager.ApprovalRuleset.Routes.Mode.AUTO:
                auto_mapper = deserialize(obj["auto_mapper"], config_manager.ApprovalRuleset.Routes.AutoMapper)
                if auto_mapper is None:
                    return None
                return cl(
                    status=deserialize(obj["status"], config_manager.ApprovalRuleset.Routes.Status),
                    mode=mode,
                    filter=obj.get("filter") or {"relation": "and", "conditions": []},
                    auto_mapper=auto_mapper,
                )

    converter.register_structure_hook(config_manager.ApprovalRuleset.Routes, approval_ruleset_route_structure)

    def manual_mapper_structure(obj, cl):
        if not obj:
            return None
        if not obj.get("nodes"):
            return None
        nodes = []
        for node_obj in obj["nodes"]:
            node = deserialize(node_obj, config_manager.ApprovalRuleset.Routes.ManualMapper.Nodes)
            if not node.reviewers:
                continue
            nodes.append(node)
        if not nodes:
            return None
        return cl(nodes)

    converter.register_structure_hook(config_manager.ApprovalRuleset.Routes.ManualMapper, manual_mapper_structure)

    def approval_ruleset_structure(obj, cl):
        if not obj.get("routes"):
            return cl([])
        normalized_routes = []
        for route_obj in obj["routes"]:
            route = deserialize(route_obj, config_manager.ApprovalRuleset.Routes)
            if route is None:
                continue
            normalized_routes.append(route)
        return cl(normalized_routes)

    converter.register_structure_hook(config_manager.ApprovalRuleset, approval_ruleset_structure)

    def enum_structure(obj, cls):
        try:
            return cls(obj)
        except ValueError as e:
            try:
                return cls[obj]
            except KeyError:
                raise e

    def issuing_item_strategy_structure(obj, cls):
        if obj["mode"] == "goods":
            return cls(mode=cls.Mode.GOODS, fixed_issuing_item=None)
        else:
            return cls(mode=cls.Mode.FIXED, fixed_issuing_item=obj["fixed_issuing_item"])

    notification_template_default_hook = converter.get_structure_hook(config_manager.NotificationTemplate, False)

    @converter.register_structure_hook
    def notification_template_structure(obj, cls) -> config_manager.NotificationTemplate:
        if obj.get("id") is None:
            obj["id"] = uuid.uuid4().hex
        return notification_template_default_hook(obj, cls)

    notification_channel_default_hook = converter.get_structure_hook(config_manager.NotificationChannel, False)

    @converter.register_structure_hook
    def notification_channel_structure(obj, cls) -> config_manager.NotificationChannel:
        if obj.get("id") is None:
            obj["id"] = uuid.uuid4().hex
        return notification_channel_default_hook(obj, cls)

    converter.register_structure_hook(config_manager.ApprovalRuleset.Routes.Mode, enum_structure)
    converter.register_structure_hook(config_manager.ApprovalRuleset.Routes.Status, enum_structure)
    converter.register_structure_hook(config_manager.ApprovalRuleset.Routes.AutoMapper.Action, enum_structure)
    converter.register_structure_hook(
        config_manager.ApprovalRuleset.Routes.ManualMapper.Nodes.Strategy,
        enum_structure,
    )
    converter.register_structure_hook(config_manager.GoodsMode, enum_structure)
    converter.register_structure_hook(config_manager.TradeBuilderMode, enum_structure)
    converter.register_structure_hook(config_manager.PlatformApplySyncAfterAction, enum_structure)
    converter.register_structure_hook(config_manager.FixedIssuingItemSyncGoodsNum, enum_structure)
    converter.register_structure_hook(config_manager.IssuingItemStrategy, issuing_item_strategy_structure)


_init_symbol_resolver()
_register_structure_hook()
