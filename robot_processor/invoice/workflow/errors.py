from robot_processor.invoice.errors import InvoiceError


class ApprovalRuleNotConfiguredError(InvoiceError, ValueError):
    def __init__(self):
        super().__init__("未配置审批规则")


class NoActiveApprovalError(InvoiceError, ValueError):
    def __init__(self):
        super().__init__("当前发票申请无进行中的审批")


class NoActiveApprovalNodeError(InvoiceError, ValueError):
    def __init__(self):
        super().__init__("当前审批流程无进行中的审批节点")


class ApprovalNodeNotInApprovalFlowError(InvoiceError, ValueError):
    def __init__(self):
        super().__init__("当前审批节点不在审批流程中")


class ApprovalStopIteration(InvoiceError, StopIteration):
    def __init__(self):
        super().__init__("当前审批节点是最后一个审批节点")


class NoApprovalPermissionError(InvoiceError, PermissionError):
    def __init__(self):
        super().__init__("当前用户无审批权限")


class InvoiceStatusNotSupportError(InvoiceError, PermissionError):
    def __init__(self, workflow):
        super().__init__(f"当前发票申请的状态不支持开票. state={workflow.state.name}, workflow={workflow.id}")


class InvoiceWorkflowAlreadyRequestError(InvoiceError, PermissionError):
    def __init__(self):
        super().__init__("发票申请流程已经关联了未关闭的开票请求")


class InvoiceRequestNotExistError(InvoiceError, ValueError):
    def __init__(self):
        super().__init__("发票申请不存在")


class DoudianInvoiceTypeUnSupportError(InvoiceError, ValueError):
    def __init__(self, invoice_type=None):
        super().__init__(f"不支持的抖店发票类型: {invoice_type}")
