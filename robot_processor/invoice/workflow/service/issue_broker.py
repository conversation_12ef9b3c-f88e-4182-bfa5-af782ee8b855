import threading
import time
from contextlib import contextmanager
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal

from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceIssued as pb_InvoiceIssued
from leyan_proto.digismart.robot.invoice.workflow_pb2 import UserInfo as pb_UserInfo
from loguru import logger
from result import Err
from result import Ok

from robot_processor.db import db
from robot_processor.db import in_transaction
from robot_processor.invoice.common.models import anonymous_user_info
from robot_processor.invoice.rpa.services import rpa_control
from robot_processor.invoice.tax_bureau.models import IssuedAggregation
from robot_processor.invoice.workflow.models import InvoiceIssued
from robot_processor.invoice.workflow.models import InvoiceIssueQueue
from robot_processor.invoice.workflow.models import InvoiceRequest
from robot_processor.invoice.workflow.models import InvoiceWorkflow
from robot_processor.invoice.workflow.models import InvoiceWorkflowRequest
from robot_processor.invoice.workflow.models import InvoiceWorkflowTransition
from robot_processor.invoice.workflow.models import IssuingType
from robot_processor.invoice.workflow.models import ReversingStatus
from robot_processor.logging import log_vars
from robot_processor.utils import message_to_dict


class InvoiceLockAcquiredTimeout(TimeoutError):
    pass


@dataclass
class IssueBroker:
    invoice_request: InvoiceRequest

    @staticmethod
    def provide_redis():
        from robot_processor.client import redis

        return redis

    @property
    def invoice_workflows(self) -> list[InvoiceWorkflow]:
        return self.invoice_request.workflows

    @property
    def invoice_issued(self) -> InvoiceIssued:
        assert self.invoice_request.invoice_issued, "无开票信息"
        return self.invoice_request.invoice_issued

    @property
    def org_id(self):
        return self.invoice_request.org_id

    @contextmanager
    def acquire_lock(self):
        timeout = 5
        stop_event = threading.Event()

        def renew_lock(lock_, token, interval, extend, stop_event_=None):
            lock_.local.token = token
            while not stop_event_.is_set():
                try:
                    lock_.extend(extend)
                except Exception as e:
                    logger.opt(exception=e).error(f"续约锁失败: {lock_.name}")
                stop_event_.wait(interval)

        keys = [f"request:{self.invoice_request.id}"] + [
            f"workflow:{workflow.id}" for workflow in self.invoice_workflows
        ]
        locks = [self.provide_redis().lock(key, timeout=10) for key in keys]
        renew_threads = []
        start = time.time()
        while time.time() - start < timeout:
            acquired = []
            for lock in locks:
                if lock.acquire(blocking=False):
                    acquired.append(lock)
                else:
                    for each in acquired:
                        each.release()
                    break  # 进入下一次循环
            if all(lock.owned() for lock in locks):
                for lock in locks:
                    renew = threading.Thread(
                        target=renew_lock, args=(lock, lock.local.token, 5, 10, stop_event), daemon=True
                    )
                    renew.start()
                    renew_threads.append(renew)
                break
            else:
                time.sleep(0.1)
        else:
            raise InvoiceLockAcquiredTimeout(f"获取锁超时: {keys}")
        try:
            with in_transaction():
                db.session.refresh(self.invoice_request, with_for_update=True)
                for workflow in self.invoice_workflows:
                    db.session.refresh(workflow, with_for_update=True)
                yield
        finally:
            stop_event.set()
            for thread in renew_threads:
                thread.join()
            for lock in locks:
                lock.release()

    def do_issue(self, user_info=anonymous_user_info, debug_mode: bool = False):
        from .validation_broker import CorporateInvoiceThresholdBroker

        with self.acquire_lock():
            # 执行开票前的校验
            for workflow in self.invoice_workflows:
                if (
                    self.invoice_request.state == InvoiceRequest.State.QUEUED
                    and workflow.state == InvoiceWorkflow.State.INVOICING
                ):
                    continue
                InvoiceWorkflowTransition.ensure_valid_state_for_action(workflow, InvoiceWorkflow.Action.ISSUE)

            corporate_invoice_threshold_broker = CorporateInvoiceThresholdBroker(self.org_id)
            check_result = corporate_invoice_threshold_broker.check(self.invoice_request.seller_credit_id)
            if check_result.is_err():
                for workflow in self.invoice_workflows:
                    workflow.mark_invoice_failed(self.invoice_request, check_result.unwrap_err(), user_info=user_info)
                return

            queue = InvoiceIssueQueue.enqueue(self.org_id, self.invoice_request.id, InvoiceIssueQueue.Task.DO_ISSUE)
            if not queue.acquire().acquired:
                for workflow in self.invoice_workflows:
                    workflow.mark_queued(user_info=user_info)
                return
            if self.invoice_workflows[0].tid:
                log_vars.Tid.set(self.invoice_workflows[0].tid)
            issue_result = rpa_control.issue_invoice(self.org_id, self.invoice_request, debug_mode)
            log_vars.context.clear(log_vars.Tid.key)
            if issue_result.is_ok():
                for workflow in self.invoice_workflows:
                    workflow.mark_invoicing(user_info=user_info)
            else:
                queue.release()
                for workflow in self.invoice_workflows:
                    workflow.mark_invoice_failed(self.invoice_request, issue_result.unwrap_err(), user_info=user_info)

    def fetch_issue(self, user_info=anonymous_user_info, debug_mode: bool = False):
        with self.acquire_lock():
            for workflow in self.invoice_workflows:
                InvoiceWorkflowTransition.ensure_valid_state_for_action(
                    workflow, InvoiceWorkflow.Action.REFRESH_INVOICE_RECEIPT
                )
            queue = InvoiceIssueQueue.enqueue(self.org_id, self.invoice_request.id, InvoiceIssueQueue.Task.FETCH_ISSUED)
            if not queue.acquire().acquired:
                queue_position = queue.get_queue_position()
                if queue_position == 0:
                    return Ok(None)
                else:
                    return Err(Exception(f"已进入队列，排队获取发票任务中，当前队列位置: {queue_position}"))

            fetch_result = rpa_control.fetch_issued(self.org_id, self.invoice_request, debug_mode)
            if fetch_result.is_err():
                queue.release()
                for workflow in self.invoice_workflows:
                    workflow.mark_fetch_issued_failed(fetch_result.unwrap_err(), user_info=user_info)
                return Err(fetch_result.unwrap_err())
            return Ok(None)

    def issued(self, user_info: pb_UserInfo, invoice_number: str, issuing_time: datetime):
        with self.acquire_lock():
            invoice_issued = InvoiceIssued(
                org_id=self.org_id,
                invoice_number=invoice_number,
                invoice_type=self.invoice_request.invoice_type,
                issuing_type=self.invoice_request.issuing_type,
                issuing_time=issuing_time,
                total_amount_without_tax=0,
                total_tax_amount=0,
                issuing_amount=0,
                issuing_amount_ch="",
                reversing_status=ReversingStatus.NOT_REVERSED,
                seller_credit_id=self.invoice_request.seller_credit_id,
                seller_name=self.invoice_request.seller_name,
                buyer_type=self.invoice_request.buyer_type,
                buyer_name=self.invoice_request.buyer_name,
                original_invoice_number=self.invoice_request.rush_red_invoice_number,
                rush_red_reason=self.invoice_request.rush_red_reason,
                issuing_items=self.invoice_request.issuing_items,
                is_tax_included=self.invoice_request.is_tax_included,
            )
            db.session.add(invoice_issued)
            db.session.flush()
            self.invoice_request.invoice_issued_id = invoice_issued.id
            for workflow in self.invoice_workflows:
                workflow.invoice_number = invoice_number
                workflow.issuing_time = issuing_time
                workflow.mark_invoiced_waiting_receipt(self.invoice_request, user_info)

    def issued_with_detail(self, view: pb_InvoiceIssued):
        invoice_info = message_to_dict(view)
        with self.acquire_lock():
            self.invoice_issued.receipt = message_to_dict(view.receipt)
            self.invoice_issued.total_amount_without_tax = invoice_info["total_amount_without_tax"]
            self.invoice_issued.total_tax_amount = invoice_info["total_tax_amount"]
            self.invoice_issued.issuing_amount = invoice_info["issuing_amount"]
            self.invoice_issued.issuing_amount_ch = invoice_info["issuing_amount_ch"]
            self.invoice_issued.raw = message_to_dict(view.raw)
            for workflow in self.invoice_workflows:
                workflow.mark_invoiced(self.invoice_request)
                workflow.set_issued(self.invoice_issued)
        IssuedAggregation.issued(
            self.org_id,
            self.invoice_request.seller_credit_id,
            self.invoice_issued.issuing_time,
            Decimal(invoice_info["issuing_amount"]),
        )

    def issue_failed(self, reason: str, user_info=anonymous_user_info):
        with self.acquire_lock():
            for workflow in self.invoice_workflows:
                InvoiceWorkflowTransition.ensure_valid_state_for_action(
                    workflow, InvoiceWorkflow.Action.MARK_INVOICE_FAILED
                )
            for workflow in self.invoice_workflows:
                workflow.mark_invoice_failed(self.invoice_request, reason, user_info=user_info)

    def fetch_issued_failed(self, reason: str, user_info=anonymous_user_info):
        with self.acquire_lock():
            for workflow in self.invoice_workflows:
                InvoiceWorkflowTransition.ensure_valid_state_for_action(
                    workflow, InvoiceWorkflow.Action.MARK_INVOICE_FAILED
                )
            for workflow in self.invoice_workflows:
                workflow.mark_fetch_issued_failed(reason, user_info=user_info)

    def issue_timeout(self):
        with self.acquire_lock():
            for workflow in self.invoice_workflows:
                InvoiceWorkflowTransition.ensure_valid_state_for_action(
                    workflow, InvoiceWorkflow.Action.MARK_INVOICE_FAILED
                )
            for workflow in self.invoice_workflows:
                workflow.mark_invoicing_timeout(self.invoice_request)

    @classmethod
    @in_transaction()
    def init_issue_blue(
        cls,
        invoice_request_view,
        workflows: list[InvoiceWorkflow],
        competent_tax_bureau: str,
        account: str,
    ):
        for workflow in workflows:
            assert workflow.processing_invoice_request() is None
        match result := (InvoiceRequest.create(invoice_request_view, competent_tax_bureau, account)):
            case Ok(request):
                for workflow in workflows:
                    InvoiceWorkflowRequest.create(workflow, request)
                return Ok(IssueBroker.by_request(request))
        return result

    @classmethod
    @in_transaction()
    def init_issue_red(cls, workflow: InvoiceWorkflow, competent_tax_bureau: str, account: str):
        match result := (
            InvoiceRequest.create(
                workflow.to_request_view(),
                competent_tax_bureau,
                account,
                workflow.rush_red_reason,
                workflow.original_workflow_id,
            ).map(cls.by_request)
        ):
            case Ok(self):
                InvoiceWorkflowRequest.create(workflow, self.invoice_request)
        return result

    @classmethod
    def by_request(cls, invoice_request: InvoiceRequest):
        return cls(invoice_request)

    @property
    def logger(self):
        return logger.bind(org_id=self.org_id, invoice_request_id=self.invoice_request.id)

    @classmethod
    def after_workflow_create_hook(cls, workflow: InvoiceWorkflow):
        from robot_processor.invoice.config_manager import ConfigKey
        from robot_processor.invoice.config_manager import ConfigManager
        from robot_processor.invoice.tax_bureau.models import Corporate

        if workflow.state != InvoiceWorkflow.State.PENDING_INVOICING:
            return Ok("不是待开票状态")
        config_manager = ConfigManager(workflow.org_id)
        auto_issued_rule = config_manager.get(ConfigKey.INVOICE_AUTO_ISSUED_RULE)
        if not auto_issued_rule.enabled:
            return Ok("规则未启用")
        for rule in auto_issued_rule.rules:
            if rule.credit_id and ("_all" not in rule.credit_id or workflow.seller_credit_id not in rule.credit_id):
                continue
            if rule.source and workflow.source not in rule.source:
                continue
            if rule.invoice_amount_min is not None and workflow.issuing_items_total_amount < rule.invoice_amount_min:
                continue
            if rule.invoice_amount_max is not None and workflow.issuing_items_total_amount > rule.invoice_amount_max:
                continue
            if rule.buyer_type and workflow.buyer_type not in rule.buyer_type:
                continue
            if rule.invoice_type and workflow.invoice_type not in rule.invoice_type:
                continue
            if rule.issuing_type and workflow.issuing_type not in rule.issuing_type:
                continue
            break
        else:
            return Ok("没有满足的规则")
        corporate = Corporate.get_by_credit_id(workflow.org_id, workflow.seller_credit_id)
        if not (taxer := corporate.get_default_taxer()):
            return Ok("没有默认的开票账号")
        match workflow.issuing_type:
            case IssuingType.BLUE:
                init_result = cls.init_issue_blue(
                    workflow.to_request_view(), [workflow], corporate.competent_tax_bureau.name, taxer.account
                )
            case IssuingType.RED:
                init_result = cls.init_issue_red(workflow, corporate.competent_tax_bureau.name, taxer.account)
            case _:
                raise NotImplementedError()
        if init_result.is_err():
            return init_result
        self = init_result.unwrap()
        self.logger.info("根据规则自动开票")
        try:
            self.do_issue(workflow.get_applicant_info())
            return Ok("已提交开票任务")
        except Exception as e:
            return Err(e)
