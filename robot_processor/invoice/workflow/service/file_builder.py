import re
from collections import defaultdict
from dataclasses import dataclass
from dataclasses import field
from dataclasses import fields
from decimal import Decimal
from functools import lru_cache
from typing import TYPE_CHECKING

import pandas as pd
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow
from leyan_proto.digismart.robot.invoice.workflow_pb2 import UserInfo as pb_UserInfo
from loguru import logger

from robot_processor.db import in_transaction
from robot_processor.ext import db
from robot_processor.utils import unwrap_optional

if TYPE_CHECKING:
    from robot_processor.invoice.tax_bureau.models import Corporate
    from robot_processor.invoice.workflow.models import BuyerType  # noqa: F401
    from robot_processor.invoice.workflow.models import InvoiceBatchRecord
    from robot_processor.invoice.workflow.models import InvoiceType


@dataclass
class InvoiceWorkflowByIssuingItemFileBuilder:
    @dataclass
    class Record:
        sid: str | None = field(metadata={"col": "店铺sid", "dtype": str})
        platform: str | None = field(init=False)
        tid: str | None = field(metadata={"col": "订单号", "dtype": str})
        buyer_type: "BuyerType" = field(metadata={"col": "受票方自然人标识", "dtype": str})
        invoice_type: "InvoiceType" = field(metadata={"col": "*发票类型", "dtype": str})
        buyer_name: str = field(metadata={"col": "*购方名称", "dtype": str})
        buyer_credit_id: str | None = field(metadata={"col": "购方税号", "dtype": str})
        buyer_address: str | None = field(metadata={"col": "地址", "dtype": str})
        buyer_phone: str | None = field(metadata={"col": "电话", "dtype": str})
        buyer_bank: str | None = field(metadata={"col": "开户行", "dtype": str})
        buyer_bank_account: str | None = field(metadata={"col": "账号", "dtype": str})
        mail_address: str | None = field(metadata={"col": "邮箱", "dtype": str})
        seller_name: str = field(metadata={"col": "*销方名称", "dtype": str})

        issuing_item_title: str = field(metadata={"col": "项目名称", "dtype": str})
        issuing_item_num: Decimal | None = field(metadata={"col": "数量", "dtype": Decimal})
        issuing_item_price: Decimal | None = field(metadata={"col": "单价", "dtype": Decimal})
        issuing_item_amount: Decimal = field(metadata={"col": "金额", "dtype": Decimal})
        issuing_item_tax_code: str = field(metadata={"col": "税收分类编号", "dtype": str})
        issuing_item_tax_rate: Decimal = field(metadata={"col": "税率", "dtype": str})
        remark: str | None = field(metadata={"col": "备注", "dtype": str})

        batch: "InvoiceBatchRecord"
        raw: dict
        failure_items: list[str] = field(init=False, default_factory=list)
        issuing_item_id: int | None = field(default=None, init=False)
        corporate: "Corporate" = field(init=False)

        @property
        def has_error(self):
            return len(self.failure_items) > 0

        def build_workflow_editable_view(self):
            from robot_processor.invoice.goods.models import IssuingItem
            from robot_processor.invoice.goods.models import TaxCode
            from robot_processor.invoice.workflow.models import IssuingType
            from robot_processor.utils import int_wrapper
            from robot_processor.utils import pb_value_wrapper
            from robot_processor.utils import string_wrapper

            if self.remark:
                remark_template = pb_value_wrapper(
                    [{"const": {"value": self.remark}, "qualifier": "const", "type_spec": {"type": "string"}}]
                )
            else:
                remark_template = None
            pb_workflow = pb_InvoiceWorkflow.EditableView(
                invoice_type=self.invoice_type.pb_value,
                issuing_type=IssuingType.BLUE.pb_value,
                seller_name=self.corporate.name,
                seller_credit_id=self.corporate.credit_id,
                seller_phone=string_wrapper(self.corporate.phone),
                seller_address=string_wrapper(self.corporate.address),
                seller_bank=string_wrapper(self.corporate.bank),
                seller_bank_account=string_wrapper(self.corporate.bank_account),
                buyer_type=self.buyer_type.pb_value,
                buyer_name=self.buyer_name,
                buyer_credit_id=string_wrapper(self.buyer_credit_id),
                buyer_phone=string_wrapper(self.buyer_phone),
                buyer_address=string_wrapper(self.buyer_address),
                buyer_bank=string_wrapper(self.buyer_bank),
                buyer_bank_account=string_wrapper(self.buyer_bank_account),
                is_tax_included=True,
                issuing_items=[],
                source=pb_InvoiceWorkflow.Source.BATCH,
                batch_id=int_wrapper(self.batch.id),
                remark_template=remark_template,
            )
            if self.tid:
                pb_workflow.trade_info.tid = self.tid
            if self.sid and self.platform:
                pb_workflow.trade_info.sid = self.sid
                pb_workflow.trade_info.platform = self.platform
            if self.issuing_item_id:
                pb_issuing_item = IssuingItem.get_by_id(self.batch.org_id, self.issuing_item_id).to_workflow_pb(
                    is_small_taxpayer=self.corporate.is_small_taxpayer, goods_item=None
                )
                if issuing_item_num := string_wrapper(self.issuing_item_num):
                    pb_issuing_item.num.CopyFrom(issuing_item_num)
                if issuing_item_price := string_wrapper(self.issuing_item_price):
                    pb_issuing_item.price.CopyFrom(issuing_item_price)
                pb_issuing_item.amount = str(self.issuing_item_amount)
            else:
                tax_code = unwrap_optional(db.session.get(TaxCode, self.issuing_item_tax_code))
                pb_issuing_item = pb_InvoiceWorkflow.IssuingItem(
                    title=self.issuing_item_title,
                    tax_name_abbr=tax_code.name_abbr,
                    tax_code=tax_code.code,
                    tax_rate=str(tax_code.tax_rate),
                    num=string_wrapper(self.issuing_item_num),
                    price=string_wrapper(self.issuing_item_price),
                    amount=str(self.issuing_item_amount),
                )
            pb_workflow.issuing_items.append(pb_issuing_item)
            return pb_workflow

        def __post_init__(self):
            from robot_processor.invoice.tax_bureau.models import Corporate
            from robot_processor.invoice.workflow.models import BuyerType
            from robot_processor.invoice.workflow.models import InvoiceType
            from robot_processor.shop.models import Shop

            for f in fields(InvoiceWorkflowByIssuingItemFileBuilder.Record):
                if isinstance(origin_value_str := getattr(self, f.name, None), str):
                    setattr(self, f.name, origin_value_str.strip())

            try:
                self.buyer_type = {"是": BuyerType.INDIVIDUAL, "否": BuyerType.ENTERPRISE, None: BuyerType.ENTERPRISE}[
                    self.buyer_type  # type: ignore[index]
                ]
            except KeyError:
                error = "@{} 内容错误: {}".format(self.col_name("buyer_type"), self.buyer_type)
                self.failure_items.append(error)

            try:
                self.invoice_type = {
                    None: InvoiceType.VAT_GENERAL,
                    "普通发票": InvoiceType.VAT_GENERAL,
                    "增值税专用发票": InvoiceType.VAT_SPECIAL,
                }[
                    self.invoice_type  # type: ignore[index]
                ]
            except KeyError:
                error = "@{} 内容错误，需要填写内容例如：{} / {}".format(
                    self.col_name("invoice_type"), "普通发票", "增值税专用发票"
                )
                self.failure_items.append(error)

            if self.buyer_name is None:
                error = "@{} 必填".format(self.col_name("buyer_name"))  # type: ignore[unreachable]
                self.failure_items.append(error)

            if self.invoice_type == InvoiceType.VAT_SPECIAL and not self.buyer_credit_id:
                error = "@{} 开票类型为增值税专用发票时必填".format(self.col_name("buyer_credit_id"))
                self.failure_items.append(error)

            corporates_map = get_org_corporates_map(self.batch.org_id)
            if self.seller_name not in corporates_map:
                error = "@{} 没有在飞梭后台绑定：{}".format(self.col_name("seller_name"), self.seller_name)
                self.failure_items.append(error)
            else:
                self.corporate = unwrap_optional(db.session.get(Corporate, corporates_map[self.seller_name]))

            if self.issuing_item_amount is None:
                error = "@{} 必填".format(self.col_name("issuing_item_amount"))  # type: ignore[unreachable]
                self.failure_items.append(error)

            if self.issuing_item_tax_rate:
                percent_match = re.match(r"(?P<rate>\d{1,2})%", str(self.issuing_item_tax_rate))
                number_match = re.match(r"0\.\d{1,2}", str(self.issuing_item_tax_rate))
                if percent_match:
                    self.issuing_item_tax_rate = (Decimal(percent_match.groupdict()["rate"]) / Decimal("100")).quantize(
                        Decimal("0.01")
                    )
                elif number_match:
                    self.issuing_item_tax_rate = Decimal(self.issuing_item_tax_rate).quantize(Decimal("0.01"))
                else:
                    error = "@{} 税率格式错误，需要提供百分比格式内容，例如：{}".format(
                        self.col_name("issuing_item_tax_rate"), "13%"
                    )
                    self.failure_items.append(error)
                    # 后面要使用
                    self.issuing_item_tax_rate = None  # type: ignore[assignment]
            if self.sid:
                shop = Shop.Queries.optimal_shop_by_sid(self.sid, org_id=self.batch.org_id)
                if shop is None:
                    error = "@{} 店铺不存在".format(self.col_name("sid"))
                    self.failure_items.append(error)
                else:
                    self.platform = shop.platform

            issuing_item_name_map = defaultdict(dict)  # type: ignore
            for issuing_item in get_org_issuing_items_map(self.batch.org_id):
                pair = (issuing_item.tax_code, Decimal(issuing_item.tax_rate))
                issuing_item_name_map[issuing_item.title][pair] = issuing_item
            if self.issuing_item_title is None:
                error = "@{} 必填".format(self.col_name("issuing_item_title"))  # type: ignore[unreachable]
                self.failure_items.append(error)
            elif self.issuing_item_title not in issuing_item_name_map:
                if self.issuing_item_tax_code is None:
                    error = "@{} 没有在后台绑定时，{} 必填".format(  # type: ignore[unreachable]
                        self.col_name("issuing_item_title"), self.col_name("issuing_item_tax_code")
                    )
                    self.failure_items.append(error)
                if self.issuing_item_tax_rate is None:
                    error = "@{} 没有在后台绑定时，{} 必填".format(  # type: ignore[unreachable]
                        self.col_name("issuing_item_title"), self.col_name("issuing_item_tax_rate")
                    )
                    self.failure_items.append(error)
            else:
                candidate_issuing_items = issuing_item_name_map[self.issuing_item_title]
                match (self.issuing_item_tax_code, self.issuing_item_tax_rate):
                    case (None, None):
                        if len(candidate_issuing_items) == 1:  # type: ignore[unreachable]
                            self.issuing_item_id = list(candidate_issuing_items.values())[0].id
                        else:
                            error = "@{} 在后台存在多个不同税率配置，无法确定使用哪一个: {}".format(
                                self.col_name("issuing_item_title"), self.issuing_item_title
                            )
                            self.failure_items.append(error)
                    case (str(), None):
                        match_tax_code_issuing_items = [  # type: ignore[unreachable]
                            candidate_issuing_items[each]
                            for each in candidate_issuing_items
                            if each[0] == self.issuing_item_tax_code
                        ]
                        if len(match_tax_code_issuing_items) == 1:
                            self.issuing_item_id = match_tax_code_issuing_items[0].id
                        elif len(match_tax_code_issuing_items) == 0:
                            error = "@{} 的税收分类与后台配置不同".format(self.col_name("issuing_item_tax_code"))
                            self.failure_items.append(error)
                        elif len(match_tax_code_issuing_items) > 1:
                            error = "@{} 在后台存在多个不同税率配置，无法确定使用哪一个: {}".format(
                                self.col_name("issuing_item_title"), self.issuing_item_title
                            )
                            self.failure_items.append(error)
                    case (str(), Decimal()):
                        try:
                            self.issuing_item_id = candidate_issuing_items[
                                (self.issuing_item_tax_code, self.issuing_item_tax_rate)
                            ].id
                        except KeyError:
                            error = "@{} 后台配置的税收编码与税率与提供的不匹配 {} {}".format(
                                self.col_name("issuing_item_title"),
                                self.issuing_item_tax_code,
                                self.issuing_item_tax_rate,
                            )
                            self.failure_items.append(error)
                    case (None, Decimal()):
                        match_tax_rate_issuing_items = [  # type: ignore[unreachable]
                            candidate_issuing_items[each]
                            for each in candidate_issuing_items
                            if each[1] == self.issuing_item_tax_rate
                        ]
                        if len(match_tax_rate_issuing_items) == 1:
                            self.issuing_item_id = match_tax_rate_issuing_items[0].id
                        elif len(match_tax_rate_issuing_items) == 0:
                            error = "@{} 的税率与后台配置不同".format(self.col_name("issuing_item_tax_rate"))
                            self.failure_items.append(error)
                        elif len(match_tax_rate_issuing_items) > 1:
                            error = "@{} 后台存在多个不同税收编码的配置，无法确定使用哪一个：{}".format(
                                self.col_name("issuing_item_title"), self.issuing_item_title
                            )
                            self.failure_items.append(error)

        @classmethod
        def init_by_dataframe(cls, df: pd.DataFrame, batch: "InvoiceBatchRecord"):
            records: list[InvoiceWorkflowByIssuingItemFileBuilder.Record] = []
            mapper = {each_field.metadata["col"]: each_field.name for each_field in fields(cls) if each_field.metadata}
            for _, row in df.iterrows():
                record_raw = {mapper[col]: None if pd.isna(val) else val for col, val in row.to_dict().items()}
                record_raw["batch"] = batch
                record_raw["raw"] = row.to_dict()
                records.append(cls(**record_raw))  # type: ignore
            return records

        @classmethod
        def get_dtype(cls):
            return {
                each_field.metadata["col"]: each_field.metadata["dtype"]
                for each_field in fields(cls)
                if each_field.metadata
            }

        @classmethod
        def get_columns(cls):
            return [each_field.metadata["col"] for each_field in fields(cls) if each_field.metadata]

        @classmethod
        def col_name(cls, field_name):
            return {
                record_field.name: record_field.metadata["col"]
                for record_field in fields(InvoiceWorkflowByIssuingItemFileBuilder.Record)
                if record_field.metadata
            }[field_name]

    batch: "InvoiceBatchRecord"
    records: list[Record]

    @classmethod
    def init_batch(cls, org_id: int, file_name: str, key_in_oss: str, user_info: pb_UserInfo.User):
        from io import BytesIO

        from external.oss import config
        from external.oss import get_object
        from robot_processor.invoice.workflow.models import InvoiceBatchRecord

        batch = InvoiceBatchRecord.create(
            org_id, InvoiceBatchRecord.Version.BY_ISSUING_ITEM, file_name, config.oss_base_url() + key_in_oss, user_info
        )
        try:
            df = pd.read_excel(
                BytesIO(get_object(key_in_oss)),
                skiprows=[1, 2],
                usecols=lambda col: col != "字段",
                dtype=cls.Record.get_dtype(),
            )
        except Exception as e:
            batch.mark_failed(f"解析上传文件失败 {e}")
            logger.opt(exception=e).error(f"解析上传文件失败 {e}")
            return cls(batch=batch, records=[])

        if diff := set(cls.Record.get_columns()).difference(df.columns.tolist()):
            batch.mark_failed("表头不一致，缺少：{}".format("；".join(diff)))
            return cls(batch=batch, records=[])
        else:
            records = cls.Record.init_by_dataframe(df, batch)
            if len(records) == 0:
                batch.mark_failed("没有发票信息")
            return cls(batch=batch, records=records)

    def build(self):
        from io import BytesIO

        import oss2

        from external.oss import config
        from external.oss import get_object
        from robot_processor.client import oss_client
        from robot_processor.invoice.workflow.models import InvoiceWorkflow

        from .issue_broker import IssueBroker

        with in_transaction():
            for record in self.records:
                if record.has_error:
                    continue
                workflow = InvoiceWorkflow.create(
                    self.batch.org_id, self.batch.created_user_info, record.build_workflow_editable_view()
                )
                IssueBroker.after_workflow_create_hook(workflow).map(
                    lambda msg: logger.info(f"自动开票 {msg}")
                ).map_err(lambda e: logger.error(f"自动开票失败 {e}"))
        failure = [record for record in self.records if record.has_error]
        if failure:
            origin_key_in_oss = self.batch.origin_file_path[len(config.oss_base_url()) :]
            failure_key_in_oss = origin_key_in_oss.split(".", maxsplit=1)[0] + "_failure_items.xlsx"
            template = pd.read_excel(BytesIO(get_object(origin_key_in_oss)), nrows=2)
            template["错误信息"] = None
            failure_df = pd.DataFrame(
                [{**record.raw, "错误信息": "\n".join(record.failure_items)} for record in failure]
            )
            combined_df = pd.concat([template, failure_df], ignore_index=True)
            failure_data = BytesIO()
            with pd.ExcelWriter(failure_data, engine="openpyxl") as writer:  # type: ignore
                combined_df.to_excel(writer, sheet_name="Sheet1", index=False, header=True)
            failure_data.seek(0)
            headers = {
                "x-oss-storage-class": oss2.BUCKET_STORAGE_CLASS_STANDARD,
                "x-oss-object-acl": config.policy_acl("invoice.file-builder"),
            }
            oss_client.bucket.put_object(failure_key_in_oss, failure_data, headers)
            self.batch.mark_partial_success(config.oss_base_url() + failure_key_in_oss)
            if len(failure) == len(self.records):
                self.batch.mark_failed("全部校验不通过，请下载导入失败文件附件查看原因。")
        else:
            self.batch.mark_success()


@lru_cache()
def get_org_corporates_map(org_id: int):
    from robot_processor.invoice.tax_bureau.models import Corporate

    return {corporate.name: corporate.id for corporate in Corporate.get_by_org_id(org_id)}


@lru_cache()
def get_org_issuing_items_map(org_id: int):
    from sqlalchemy import select

    from robot_processor.invoice.goods.models import IssuingItem

    return [
        issuing_item.to_pb()
        for issuing_item in db.session.scalars(select(IssuingItem).where(IssuingItem.org_id == org_id)).all()
    ]
