from dataclasses import dataclass
from dataclasses import field
from typing import TYPE_CHECKING

from leyan_proto.digismart.robot.symbol_table_pb2 import Filter as pb_Filter
from result import Err
from result import Ok
from robot_types.model.invoice.config_manager import CorporateInvoiceThresholdRuleset

from robot_processor.invoice.config_manager import ConfigKey
from robot_processor.invoice.config_manager import ConfigManager

if TYPE_CHECKING:
    from robot_processor.invoice.workflow.models import InvoiceWorkflow


@dataclass
class CorporateInvoiceThresholdBroker:
    """检查企业主体开票金额阈值"""

    org_id: int
    ruleset: CorporateInvoiceThresholdRuleset | None = field(init=False)

    def __post_init__(self):
        config_manager = ConfigManager(self.org_id)
        self.ruleset = config_manager.get(ConfigKey.CORPORATE_INVOICE_THRESHOLD_RULE)

    def check(self, credit_id: str) -> Ok[Exception | None] | Err[Exception]:
        from sqlalchemy import BinaryExpression
        from sqlalchemy import ColumnElement

        from robot_processor.invoice.tax_bureau.models import Corporate
        from robot_processor.invoice.tax_bureau.models import IssuedAggregation

        if self.ruleset is None:
            return Ok(None)
        rules = [rule for rule in self.ruleset.rules if rule.credit_id == "_all" or rule.credit_id == credit_id]
        rule_check_result: list[tuple[CorporateInvoiceThresholdRuleset.Rules, bool]] = []
        for rule in rules:
            credit_criteria: ColumnElement[bool] | BinaryExpression[bool]
            if rule.credit_id == "_all":
                credit_criteria = IssuedAggregation.credit_id.in_(
                    [corporate.credit_id for corporate in Corporate.get_by_org_id(self.org_id)]
                )
            else:
                credit_criteria = IssuedAggregation.credit_id == rule.credit_id
            start, end = self.get_aggregation_date_range(rule)
            aggregations = IssuedAggregation.query.filter(
                IssuedAggregation.org_id == self.org_id,
                credit_criteria,
                IssuedAggregation.dimension == IssuedAggregation.Dimension(rule.dimension),
                IssuedAggregation.aggregation_date >= start,
                IssuedAggregation.aggregation_date <= end,
            ).all()
            total = sum(aggregation.issued_amount for aggregation in aggregations)
            if rule.operator == pb_Filter.GT:
                rule_check_result.append((rule, total > rule.threshold))
            elif rule.operator == pb_Filter.GTE:
                rule_check_result.append((rule, total >= rule.threshold))
            elif rule.operator == pb_Filter.EQ:
                rule_check_result.append((rule, total == rule.threshold))
            else:
                raise Exception("Unsupported operator")
        if self.ruleset.relation == "and":
            if all(result[1] for result in rule_check_result):
                error = Exception(self.ruleset.check_content)
            else:
                error = None
        else:
            if any(result[1] for result in rule_check_result):
                error = Exception(self.ruleset.check_content)
            else:
                error = None
        if self.ruleset.check_type == CorporateInvoiceThresholdRuleset.CheckType.FORBIDDEN and error:
            return Err(error)
        else:
            return Ok(error)

    @staticmethod
    def get_aggregation_date_range(rule: CorporateInvoiceThresholdRuleset.Rules):
        import arrow

        from robot_processor.invoice.tax_bureau.models import IssuedAggregation

        now = arrow.now()
        span = int(rule.span) - 1
        if span < 0:
            span = 0
        if rule.dimension == CorporateInvoiceThresholdRuleset.Rules.Dimension.QUARTER:
            start = IssuedAggregation.get_quarter_aggregation_date(now.shift(quarters=-span).naive.date())
            end = IssuedAggregation.get_quarter_aggregation_date(now.naive.date())
        elif rule.dimension == CorporateInvoiceThresholdRuleset.Rules.Dimension.MONTH:
            start = IssuedAggregation.get_month_aggregation_date(now.shift(months=-span).naive.date())
            end = IssuedAggregation.get_month_aggregation_date(now.naive.date())
        else:
            start = now.shift(days=-span).naive.date()
            end = now.naive.date()
        return start, end


class PlatformApplyInvoiceAmountChecker:
    """检查申请开票金额和实际开票金额是否一致"""

    @classmethod
    def check(cls, workflow: "InvoiceWorkflow") -> Ok[None] | Err[Exception]:
        from robot_processor.invoice.workflow.models import InvoiceWorkflowSource
        from robot_processor.utils import Amount

        if workflow.source != InvoiceWorkflowSource.PLATFORM_APPLY:
            return Ok(None)
        platform_apply_amount = Amount(workflow.platform_apply_info.invoice_amount)
        actual_invoice_amount = workflow.issuing_items_total_amount
        if platform_apply_amount != actual_invoice_amount:
            return Err(
                ValueError(f"开票金额和平台申请开票金额不一致，平台申请开票金额: {platform_apply_amount.format(2)}")
            )
        return Ok(None)
