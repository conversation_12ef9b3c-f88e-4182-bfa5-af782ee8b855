from dataclasses import dataclass
from typing import TYPE_CHECKING
from typing import ClassVar

from robot_types.helper.predefined import BizType

from robot_processor.db import no_auto_flush
from robot_processor.function.conversion.text_template_render import TextTemplateRender
from robot_processor.shop.models import Shop
from robot_processor.utils import raise_exception

if TYPE_CHECKING:
    from robot_processor.invoice.workflow.models import InvoiceWorkflow


@dataclass
class InvoiceRemarkBroker:
    biz_type: ClassVar[BizType] = BizType.INVOICE_REMARK_TEXT_TEMPLATE
    workflow: "InvoiceWorkflow"

    @no_auto_flush()
    def render_remark_template(self):
        from robot_processor.enums import ShopPlatform
        from robot_processor.invoice.config_manager import ConfigKey
        from robot_processor.invoice.config_manager import ConfigManager

        if self.workflow.remark_template:
            remark_template = self.workflow.remark_template
        else:
            config_manager = ConfigManager(self.workflow.org_id)
            remark_template = config_manager.get_raw(ConfigKey.INVOICE_REMARK_TEMPLATE)
        if remark_template:
            shop_title = ""
            shop_nick = ""
            shop_platform = ""
            if self.workflow.sid:
                shop = Shop.Queries.optimal_shop_by_sid(self.workflow.sid, org_id=self.workflow.org_id)
                if shop:
                    shop_title = shop.title or ""
                    shop_nick = shop.nick
                    try:
                        shop_platform = ShopPlatform(shop.platform).label
                    except ValueError:
                        shop_platform = shop.platform

            context = {
                "shop_title": shop_title,
                "shop_nick": shop_nick,
                "shop_platform": shop_platform,
                "tid": self.workflow.tid,
                "source": self.workflow.source.label,
            }
            text_template_render = TextTemplateRender(
                context=context,
                template=remark_template,
                mode=self.biz_type,
            )
            return text_template_render.call().unwrap_or_else(raise_exception)
        return ""
