from datetime import datetime
from typing import TYPE_CHECKING

import arrow
from result import Err
from result import as_result
from tcron_jobs import runner

from robot_processor.client import doudian_cloud
from robot_processor.client import rpa_control_oss
from robot_processor.db import db
from robot_processor.form.event.paginator import <PERSON><PERSON>ateDataFetcher
from robot_processor.logging import vars as log_vars
from robot_processor.t_cron import wrap_tcron_job


@runner.register
@wrap_tcron_job
def poll_doudian_invoice_list(shop_id: int, timerange: str = "") -> str:
    """拉取店铺的待开票发票列表并创建发票流程"""
    from robot_processor.invoice.tax_bureau.models import CorporateShop
    from robot_processor.shop.models import Shop

    if not (shop := db.session.get(Shop, shop_id)):
        return "店铺不存在"
    log_vars.OrgId.set(shop.org_id)
    log_vars.Sid.set(shop.sid)
    log_vars.Event.set("poll_doudian_invoice_list")
    corporate_shop = CorporateShop.get_by_shop(shop.sid, shop.platform)

    now = arrow.now()
    if timerange:
        start_raw, end_raw = timerange.split("~")
        start = arrow.get(start_raw).naive
        end = arrow.get(end_raw).naive
    else:
        end = now.naive
        start = now.shift(days=-60).naive

    doudian_invoice_paginator = get_data_fetcher(shop.sid, start, end)
    result: dict[str, list] = dict(ok=[], err=[])
    for doudian_invoice in doudian_invoice_paginator.chain_fetch():
        create_invoice_workflow(shop, doudian_invoice).map(result["ok"].append).map_err(result["err"].append)
    corporate_shop.platform_apply_schedule_executed_at = now.naive
    db.session.commit()
    return str(result)


if TYPE_CHECKING:
    from robot_processor.client.doudian import DoudianInvoice  # noqa
    from robot_processor.shop.models import Shop  # noqa


def create_invoice_workflow(shop: "Shop", doudian_invoice: "DoudianInvoice"):
    from sqlalchemy.sql.operators import eq as sql_eq
    from sqlalchemy.sql.operators import ne as sql_ne

    from robot_processor.invoice.workflow.models import InvoiceWorkflow
    from robot_processor.invoice.workflow.service import WorkflowBuilder
    from robot_processor.invoice.workflow.service import WorkflowContext

    org_id = int(shop.org_id)  # type: ignore[arg-type]
    exists = InvoiceWorkflow.query.filter(
        sql_eq(InvoiceWorkflow.org_id, org_id),
        sql_eq(InvoiceWorkflow.platform_apply_id, doudian_invoice.registation_id),
        sql_ne(InvoiceWorkflow.state, InvoiceWorkflow.State.CLOSED),
    ).exists()
    if db.session.query(exists).scalar():
        return Err(ValueError(f"发票流水号 {doudian_invoice.registation_id} 已创建开票申请流程"))
    context = WorkflowContext.DoudianPlatformApply(org_id=org_id, shop=shop, doudian_invoice=doudian_invoice)
    return WorkflowBuilder.create_workflow_with_context(context).as_result()


def get_data_fetcher(sid, start, end):
    from robot_processor.client.doudian import DoudianInvoice  # noqa: F811
    from robot_processor.client.doudian import DoudianInvoiceStatus

    class DoudianInvoiceListDataFetcher(PaginateDataFetcher[DoudianInvoice]):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.start: datetime = start
            self.end: datetime = end

        def _do_fetch(self, page_no, page_size):
            response = doudian_cloud.invoice_list(
                store_id=sid,
                status=DoudianInvoiceStatus.PENDING,
                start_time=int(self.start.timestamp()),
                end_time=int(self.end.timestamp()),
                page_no=page_no,
                page_size=page_size,
            )
            if self.total is None:
                self.total = response.total
            return response.invoice_list

    return DoudianInvoiceListDataFetcher()


if TYPE_CHECKING:
    from robot_processor.invoice.workflow.models import InvoiceWorkflow


@as_result(Exception)
def post_back(invoice_workflow: "InvoiceWorkflow"):
    from base64 import b64encode

    return doudian_cloud.invoice_upload(
        store_id=invoice_workflow.sid,
        order_id=invoice_workflow.tid,
        receipt=b64encode(rpa_control_oss.get_object(invoice_workflow.invoice_issued.receipt["pdf"])).decode("utf-8"),
    )
