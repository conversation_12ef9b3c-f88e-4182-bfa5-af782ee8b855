from typing import TYPE_CHECKING

from robot_processor.invoice.common.models import PlatformApplySyncMethod

if TYPE_CHECKING:
    from robot_processor.shop.models import Shop


def infer_shop_sync_platform_apply_method(shop: "Shop"):
    from robot_processor.shop.models import ErpInfo, ErpType

    if shop.platform in ["TAOBAO", "TMALL"]:
        return PlatformApplySyncMethod.QN
    elif shop.platform == "DOUDIAN":
        return PlatformApplySyncMethod.DOUDIAN
    elif shop.platform == "JD":
        return PlatformApplySyncMethod.JD
    elif shop.platform == "PDD":
        return PlatformApplySyncMethod.PDD
    if ErpInfo.get_by_sid(shop.sid, ErpType.WDT) or ErpInfo.get_by_sid(
        shop.sid, ErpType.WDTULTI
    ):
        return PlatformApplySyncMethod.WDT
    raise NotImplementedError(f"暂不支持的店铺平台 {shop.platform}")
