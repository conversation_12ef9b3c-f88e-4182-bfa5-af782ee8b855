from dataclasses import dataclass
from functools import cached_property
from typing import TYPE_CHECKING
from typing import Any
from typing import Literal
from typing import Named<PERSON>up<PERSON>
from typing import Optional
from typing import cast

import sqlalchemy as sa
import sqlalchemy.sql.operators
from google.protobuf.json_format import ParseDict
from leyan_proto.digismart.robot_web.common_pb2 import EmptyResponse
from leyan_proto.digismart.robot_web.invoice import goods_pb2_grpc
from leyan_proto.digismart.robot_web.invoice.goods_pb2 import CreateOrUpdateGoodsIssuingRuleResponse
from leyan_proto.digismart.robot_web.invoice.goods_pb2 import CreateOrUpdateIssuingItemResponse
from leyan_proto.digismart.robot_web.invoice.goods_pb2 import GetByIssuingItemResponse
from leyan_proto.digismart.robot_web.invoice.goods_pb2 import GetInvoiceGoodsItemsFilterContextResponse
from leyan_proto.digismart.robot_web.invoice.goods_pb2 import ListGoodsIssuingRuleResponse
from leyan_proto.digismart.robot_web.invoice.goods_pb2 import ListGoodsIssuingRuleResponseV2
from leyan_proto.digismart.robot_web.invoice.goods_pb2 import ListIssuingItemsResponse
from leyan_proto.digismart.robot_web.invoice.goods_pb2 import ListItemsResponse
from leyan_proto.digismart.robot_web.invoice.goods_pb2 import ListOrgErpAccountsResponse
from leyan_proto.digismart.robot_web.invoice.goods_pb2 import ListTaxCodeResponse
from leyan_proto.digismart.robot_web.invoice.goods_pb2 import SearchIssuingItemsByKeywordResponse
from result import Err
from result import Ok

from robot_processor.client import erp_item_client
from robot_processor.enums import ErpType
from robot_processor.ext import db
from robot_processor.invoice.goods.models import GoodsIssuingItem
from robot_processor.invoice.goods.models import GoodsIssuingRule
from robot_processor.invoice.goods.models import GoodsItem
from robot_processor.invoice.goods.models import GoodsItemFilterContext
from robot_processor.invoice.goods.models import IssuingItem
from robot_processor.invoice.goods.models import TaxCode
from robot_processor.invoice.goods.schemas import ErpAccount
from robot_processor.invoice.utils import catch_error_as_response
from robot_processor.invoice.utils import load_from_request
from robot_processor.invoice.utils import org_required
from robot_processor.invoice.utils import set_default_paginate
from robot_processor.plugin.schema import HighLevelSearchItemSchema
from robot_processor.symbol_table.assembler import to_predicate_filter
from robot_processor.utils import raise_exception
from robot_processor.utils import unwrap_optional


class InvoiceGoodsServicer(goods_pb2_grpc.InvoiceGoodsServicer):
    @catch_error_as_response(GetInvoiceGoodsItemsFilterContextResponse)
    @org_required
    def GetInvoiceGoodsItemsFilterContext(self, request, context):
        """获取商品列表过滤条件

        Path:
            POST /v1/invoice/goods/filter-context
        """
        org_id = load_from_request().org_id
        namespace = GoodsItemFilterContext.to_namespace(dict(org_id=org_id))
        response = GetInvoiceGoodsItemsFilterContextResponse(succeed=True, data=namespace.to_pb())
        return response

    @catch_error_as_response(ListItemsResponse)
    @org_required
    def ListErpSingleItems(self, request, context):
        """获取 erp 单品商品列表

        Path:
            POST /v1/invoice/goods/erp/{erp_type}/single/list
        """
        from robot_processor.invoice.goods.erp_models import JstItem
        from robot_processor.invoice.goods.erp_models import WdtItem

        set_default_paginate(request.config)
        response = ListItemsResponse(succeed=True)
        items: list[GoodsItem] = []
        match request.erp_type.upper():
            case GoodsItem.Datasource.WDT.name | GoodsItem.Datasource.WDTULTI.name:
                items = WdtItem.Queries.list_single(request.sid, request.filter, request.config)
            case GoodsItem.Datasource.JST.name:
                items = JstItem.Queries.list_single(request.sid, request.filter, request.config)
        response.data.paginate.MergeFrom(request.config)
        response.data.items.extend([item.to_pb() for item in items])
        return response

    @catch_error_as_response(ListItemsResponse)
    @org_required
    def ListErpCombinedItems(self, request, context):
        """获取 erp 组合装商品列表

        Path:
            POST /v1/invoice/goods/erp/{erp_type}/combined/list
        """
        from robot_processor.invoice.goods.erp_models import JstItem
        from robot_processor.invoice.goods.erp_models import WdtItem

        set_default_paginate(request.config)
        response = ListItemsResponse(succeed=True)
        items: list[GoodsItem] = []
        match request.erp_type.upper():
            case GoodsItem.Datasource.WDT.name | GoodsItem.Datasource.WDTULTI.name:
                items = WdtItem.Queries.list_combined(request.sid, request.filter, request.config)
            case GoodsItem.Datasource.JST.name:
                items = JstItem.Queries.list_combined(request.sid, request.filter, request.config)
        response.data.paginate.MergeFrom(request.config)
        response.data.items.extend([item.to_pb() for item in items])
        return response

    @catch_error_as_response(CreateOrUpdateIssuingItemResponse)
    @org_required
    def CreateOrUpdateIssuingItem(self, request, context):
        """新建一个新的开票项目商品

        Path:
            POST /v1/invoice/goods/issuing-items
        """
        org_id = load_from_request().org_id
        response = CreateOrUpdateIssuingItemResponse(succeed=True)
        if request.item.id:
            item = IssuingItem.get_by_id(org_id, request.item.id)
            item.update(request.item)
        else:
            item = IssuingItem.create(org_id, request.item)
        response.data.item.MergeFrom(item.to_pb())
        return response

    @catch_error_as_response(CreateOrUpdateIssuingItemResponse)
    @org_required
    def GetIssuingItem(self, request, context):
        """查看一个开票项目详情

        Path:
            GET /v1/invoice/goods/issuing-items/{id}
        """
        org_id = load_from_request().org_id
        item = IssuingItem.get_by_id(org_id, request.id)
        response = CreateOrUpdateIssuingItemResponse(succeed=True)
        response.data.item.MergeFrom(item.to_pb())
        return response

    @catch_error_as_response(EmptyResponse)
    @org_required
    def DeleteIssuingItem(self, request, context):
        """删除一个开票项目

        Path:
            DELETE /v1/invoice/goods/issuing-items/{id}
        """
        org_id = load_from_request().org_id
        item = IssuingItem.get_by_id(org_id, request.id)
        item.deleted = True
        db.session.commit()
        return EmptyResponse(succeed=True)

    @catch_error_as_response(ListIssuingItemsResponse)
    @org_required
    def ListIssuingItems(self, request, context):
        """当前开票明细列表

        Path:
            POST /v1/invoice/goods/issuing-items/list
        """
        org_id = load_from_request().org_id
        set_default_paginate(request.config)
        response = ListIssuingItemsResponse(succeed=True)
        issuing_items = IssuingItem.list_by_filter_and_paginate(org_id, request.filter, request.config)
        response.data.paginate.MergeFrom(request.config)
        response.data.items.extend([item.to_pb() for item in issuing_items])

        return response

    @org_required
    def UpdateItemTaxInfoByRule(self, request, context):
        """根据税收规则更新商品税收信息

        Path:
            /v1/invoice/goods/tax-info-rule/apply
        """
        return EmptyResponse(succeed=True)

    @catch_error_as_response(ListOrgErpAccountsResponse)
    @org_required
    def ListOrgErpAccounts(self, request, context):
        """当前租户可用的 erp 账号列表

        Path:
            /v1/invoice/goods/erp
        """
        org_id = load_from_request().org_id
        response = ListOrgErpAccountsResponse(succeed=True)
        response.data.erp.extend([erp_account.to_pb() for erp_account in self.get_all_erp_accounts(org_id)])

        return response

    @catch_error_as_response(ListTaxCodeResponse)
    @org_required
    def ListTaxCodeTree(self, request, context):
        """税收编码列表

        Path:
            GET /v1/invoice/goods/tax-code/tree
        """
        parent = request.parent.value if request.HasField("parent") else None
        tax_info_list = TaxCode.get_by_parent(parent)
        response = ListTaxCodeResponse(
            succeed=True,
            data=ListTaxCodeResponse.Data(tax_code=[tax_info.to_pb() for tax_info in tax_info_list]),
        )
        return response

    @catch_error_as_response(ListTaxCodeResponse)
    @org_required
    def ListTaxCodeByKeyword(self, request, context):
        """税收编码列表

        Path:
            GET /v1/invoice/goods/tax-code/by-keyword
        """
        keyword = request.keyword.value if request.HasField("keyword") else None
        tax_info_list = TaxCode.get_by_keyword(keyword)
        response = ListTaxCodeResponse(
            succeed=True,
            data=ListTaxCodeResponse.Data(tax_code=[tax_info.to_pb() for tax_info in tax_info_list]),
        )

        return response

    @catch_error_as_response(ListGoodsIssuingRuleResponse)
    @org_required
    def ListGoodsIssuingRule(self, request, context):
        """查看税收规则列表

        Path:
            GET /v1/invoice/goods/tax-info-rule
        """
        org_id = load_from_request().org_id
        response = ListGoodsIssuingRuleResponse(succeed=True)
        rules = GoodsIssuingRule.get_by_org_id(org_id)
        response.data.rules.extend([rule.to_pb() for rule in rules])

        return response

    @org_required
    def ListGoodsIssuingRuleV2(self, request, context):
        """查看税收规则列表

        Path:
            POST /v1/invoice/goods/issuing-rule/list
        """
        org_id = load_from_request().org_id
        response = ListGoodsIssuingRuleResponseV2(succeed=True)
        rules = GoodsIssuingRule.list_by_filter(org_id, to_predicate_filter(request.filter))
        response.data.rules.extend([rule.to_pb() for rule in rules])
        return response

    @catch_error_as_response(CreateOrUpdateGoodsIssuingRuleResponse)
    @org_required
    def CreateOrUpdateGoodsIssuingRule(self, request, context):
        """创建或更新税收规则列表

        Path:
            POST /v1/invoice/goods/issuing-rule
        """
        org_id, user = load_from_request()
        response = CreateOrUpdateGoodsIssuingRuleResponse(succeed=True)
        diff: dict[str, Any] = dict()
        try:
            IssuingItem.get_by_id(org_id, request.rule.issuing_item.id)
        except Exception:
            raise Exception("开票项目不存在")
        if request.rule.HasField("id"):
            rule = GoodsIssuingRule.get_by_id(org_id, request.rule.id.value)
            if rule is None:
                raise Exception("规则不存在")
            rule.edit(request.rule, diff)
        else:
            rule = GoodsIssuingRule.create(org_id, request.rule, diff)
        GoodsIssuingRule.log_update(user, org_id, diff)
        response.data.rule.MergeFrom(rule.to_pb())
        return response

    @catch_error_as_response(EmptyResponse)
    @org_required
    def DeleteGoodsIssuingRule(self, request, context):
        """删除税收规则列表

        Path:
            DELETE /v1/invoice/goods/issuing-rule/{id}
        """
        org_id, user = load_from_request()
        response = EmptyResponse(succeed=True)
        diff: dict[str, Any] = dict()
        rule = GoodsIssuingRule.get_by_id(org_id, request.id)
        if rule is None:
            raise Exception("规则不存在")
        rule.delete(diff=diff)
        GoodsIssuingRule.log_update(user, org_id, diff)
        return response

    @catch_error_as_response(EmptyResponse)
    @org_required
    def UpdateGoodsIssuingRuleSort(self, request, context):
        """更新税收规则排序

        Path:
            POST /v1/invoice/goods/issuing-rule/sort
        """
        org_id, user = load_from_request()
        response = EmptyResponse(succeed=True)
        diff: dict[str, Any] = dict()
        GoodsIssuingRule.update_sort(org_id=org_id, sort_ids=request.ids, diff=diff)
        GoodsIssuingRule.log_update(user, org_id, diff)
        return response

    @catch_error_as_response(EmptyResponse)
    @org_required
    def UpdateGoodsIssuingRule(self, request, context):
        """更新税收规则列表

        Path:
            POST /v1/invoice/goods/tax-info-rule
        """
        org_id, user = load_from_request()
        diff = GoodsIssuingRule.update(org_id, request.rules)
        GoodsIssuingRule.log_update(user, org_id, diff)
        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def BindGoodsIssuingItem(self, request, context):
        """为商品绑定开票项目

        Path:
            POST /v1/invoice/goods/bind
        """
        datasource = GoodsItem.Datasource(request.datasource)  # type: ignore[call-arg]
        match datasource.datasource_type:
            case GoodsItem.DatasourceType.PLATFORM:
                assert request.goods_item.HasField("sku"), "平台商品缺少 sku"
                item_key = request.goods_item.sku.value
            case GoodsItem.DatasourceType.ERP:
                assert request.goods_item.HasField("sku_outer"), "erp 商品缺少 sku_outer"
                item_key = request.goods_item.sku_outer.value
            case _:
                raise NotImplementedError(f"不支持的数据源类型 {datasource.datasource_type}")
        assert request.HasField("issuing_item"), "缺少需要绑定的发票项目信息"
        GoodsIssuingItem.create_or_update(request.sid, datasource.name, item_key, request.issuing_item.id)
        return EmptyResponse(succeed=True)

    @catch_error_as_response(EmptyResponse)
    @org_required
    def BatchBindGoodsIssuingItem(self, request, context):
        """批量为商品绑定开票项目

        Path:
            POST /v1/invoice/goods/batch-bind
        """
        datasource = GoodsItem.Datasource(request.datasource)  # type: ignore[call-arg]
        assert request.HasField("issuing_item"), "缺少需要绑定的发票项目信息"
        match datasource.datasource_type:
            case GoodsItem.DatasourceType.PLATFORM:

                def get_item_key(item):
                    assert item.HasField("sku"), "平台商品缺少 sku"
                    return item.sku.value

            case GoodsItem.DatasourceType.ERP:

                def get_item_key(item):
                    assert item.HasField("sku_outer"), "erp 商品缺少 sku_outer"
                    return item.sku_outer.value

            case _:
                raise NotImplementedError(f"不支持的数据源类型 {datasource.datasource_type}")
        errors = []
        for item in request.goods_item:
            try:
                GoodsIssuingItem.create_or_update(
                    request.sid, datasource.name, get_item_key(item), request.issuing_item.id
                )
            except Exception as e:
                errors.append(str(e))
        if errors:
            return EmptyResponse(succeed=False, msg=",".join(errors))
        else:
            return EmptyResponse(succeed=True)

    @catch_error_as_response(SearchIssuingItemsByKeywordResponse)
    @org_required
    def SearchIssuingItemsByKeyword(self, request, context):
        """在发票申请页，通过 Keyword 搜索商品

        Path:
            GET /v1/invoice/goods/issuing-items/by-keyword
        """
        from sqlalchemy import or_
        from sqlalchemy import select

        org_id = load_from_request().org_id
        response = SearchIssuingItemsByKeywordResponse(succeed=True)
        # fmt: off
        items = db.session.execute(
            select(IssuingItem).select_from(IssuingItem)
            .where(IssuingItem.org_id.__eq__(org_id))
            .where(IssuingItem.deleted.is_(False))
            .where(or_(
                IssuingItem.tax_code == request.keyword,
                IssuingItem.title.like(f"%{request.keyword}%"),
                IssuingItem.title_template.like(f"%{request.keyword}%"),
                IssuingItem.description.like(f"%{request.keyword}%"),
                IssuingItem.tax_name_abbr.like(f"%{request.keyword}%")
            ))
            .limit(20)
        ).scalars().all()
        # fmt: on
        response.data.items.extend([item.to_pb() for item in items])
        return response

    @classmethod
    def get_all_erp_accounts(cls, org_id):
        from robot_processor.shop.models import ErpInfo
        from robot_processor.shop.models import Shop

        all_erp_info = ErpInfo.query.join(ErpInfo.shop).filter(Shop.org_id == org_id)
        erp_accounts_collection: dict[tuple[str, str], set[str]] = dict()
        for erp_info in all_erp_info:
            if (erp_account_result := ErpAccount.get_by_erp_info(erp_info)).is_ok():
                erp_account = erp_account_result.unwrap()
                erp_accounts_collection.setdefault((erp_account.sid, erp_account.erp_type), set())
                if erp_account.nick:
                    erp_accounts_collection[(erp_account.sid, erp_account.erp_type)].add(erp_account.nick)
        return [
            ErpAccount(sid, erp_type, "/".join(erp_accounts_collection[(sid, erp_type)]))
            for sid, erp_type in erp_accounts_collection
        ]

    @classmethod
    def build_erp_account_mapper(cls, all_erp_accounts: list[ErpAccount]):
        mapper: dict[str, dict[str, ErpAccount]] = {
            "wdt": dict(),
            "jst": dict(),
            "kuaimai": dict(),
        }
        for erp_account in all_erp_accounts:
            match erp_account.erp_type:
                case ErpType.WDT.name | ErpType.WDTULTI.name:
                    mapper["wdt"][erp_account.sid] = erp_account
                case ErpType.JST.name:
                    mapper["jst"][erp_account.sid] = erp_account
                case ErpType.KUAIMAI.name:
                    mapper["kuaimai"][erp_account.sid] = erp_account
                case _:
                    raise NotImplementedError(f"不支持的 erp 类型 {erp_account.erp_type}")
        return mapper

    @catch_error_as_response(GetByIssuingItemResponse)
    @org_required
    def GetByIssuingItem(self, request, context):
        from robot_processor.invoice.tax_bureau.models import CorporateShop

        org_id = load_from_request().org_id
        response = GetByIssuingItemResponse(succeed=True)
        datasource = GoodsItem.Datasource(request.datasource)  # type: ignore[call-arg]
        goods_broker = GoodsBroker.by_datasource(org_id, datasource, request.sid).unwrap_or_else(raise_exception)
        source: Literal["erp", "platform"] = (
            "erp" if datasource.datasource_type == GoodsItem.DatasourceType.ERP else "platform"
        )
        goods_item = goods_broker.get_goods_item(
            source=source,
            sku=request.sku.value,
            spu=request.spu.value,
            sku_outer=request.sku_outer.value,
            spu_outer=request.spu_outer.value,
        ).unwrap_or_else(raise_exception)
        issuing_item = goods_broker.get_issuing_item(source=source, goods_item=goods_item).unwrap_or(IssuingItem())
        if corporate_shop := CorporateShop.get_by_shop(goods_broker.shop.sid, goods_broker.shop.platform):
            is_small_taxpayer = corporate_shop.corporate.is_small_taxpayer
        else:
            is_small_taxpayer = False
        pb_issuing_item = issuing_item.to_workflow_pb(is_small_taxpayer=is_small_taxpayer, goods_item=goods_item)
        response.data.item.MergeFrom(pb_issuing_item)
        return response


if TYPE_CHECKING:
    from robot_processor.shop.models import ErpInfo
    from robot_processor.shop.models import Shop


class DatasourceInfo(NamedTuple):
    datasource: GoodsItem.Datasource
    sid: str


@dataclass
class GoodsBroker:
    shop: "Shop"
    erp_info: Optional["ErpInfo"]

    @cached_property
    def issuing_rules(self) -> list[GoodsIssuingRule]:
        return [
            rule
            for rule in GoodsIssuingRule.get_by_org_id(int(unwrap_optional(self.shop.org_id)))
            if rule.status == GoodsIssuingRule.Status.ENABLED
        ]

    def get_datasource(self, source: Literal["erp", "platform"]):
        if source == "erp":
            if self.erp_info is None:
                return Err(ValueError("缺少 erp 信息"))
            erp_info = unwrap_optional(self.erp_info)
            datasource = GoodsItem.Datasource[unwrap_optional(erp_info.erp_type).name]
            sid = self.erp_info.get_erp_account().unwrap_or_else(raise_exception)
        else:
            datasource = GoodsItem.Datasource[self.shop.platform]
            sid = self.shop.sid
        return Ok(DatasourceInfo(datasource, sid))

    @classmethod
    def by_datasource(cls, org_id: int, datasource: GoodsItem.Datasource, sid: str):
        from robot_processor.shop.models import ErpInfo
        from robot_processor.shop.models import Shop

        if datasource.datasource_type == GoodsItem.DatasourceType.ERP:
            erps = ErpInfo.get_distinct_erp_by_org_id(org_id)
            for erp in erps:
                if (erp_account_result := erp.get_erp_account()).is_ok() and erp_account_result.unwrap() == sid:
                    break
            else:
                return Err(ValueError("未找到商品关联的 erp 信息"))
            return Ok(cls(erp_info=erp, shop=erp.shop))
        else:
            shop = unwrap_optional(Shop.Queries.optimal_shop_by_sid(sid, platform=datasource.name, org_id=str(org_id)))
            return Ok(cls(erp_info=shop.erps.first(), shop=shop))

    @classmethod
    def by_shop(cls, shop: "Shop"):
        return cls(shop=shop, erp_info=shop.erps.first())

    def query_item(self, source: Literal["platform", "erp"], sku, spu, sku_outer, spu_outer):
        from leyan_proto.digismart.item.dgt_erp_item_pb2 import DgtGetErpSkuListResponse

        if source == "platform" and self.shop.is_ks():
            access_token = self.shop.get_access_token()
        else:
            access_token = None

        response = ParseDict(
            erp_item_client.get_erp_sku_list(
                source=source,
                keywords="",
                sku_ids="",
                query_entity=HighLevelSearchItemSchema(
                    sku=sku or "",
                    spu=spu or "",
                    outer_spu=spu_outer or "",
                    outer_sku=sku_outer or "",
                ),
                items=None,
                outer_sku_ids="",
                sid=self.shop.sid,
                channel_type=self.shop.platform,
                seller_nick=self.shop.nick,
                access_token=access_token,
                page_no=1,
                page_size=1,
            ),
            DgtGetErpSkuListResponse(),
        )
        return response

    def get_goods_item(self, source: Literal["platform", "erp"], sku, spu, sku_outer, spu_outer):
        if (datasource_result := self.get_datasource(source)).is_err():
            return Err(datasource_result.unwrap_err())
        datasource, sid = datasource_result.unwrap()
        query_response = self.query_item(source, sku, spu, sku_outer, spu_outer)
        goods_item = GoodsItem(
            datasource=datasource,
            datasource_type=datasource.datasource_type,
            sid=sid,
            title="",
            sku=sku,
            spu=spu,
            sku_outer=sku_outer,
            spu_outer=spu_outer,
        )
        if query_response.item_infos:
            item_info = query_response.item_infos[0]
            goods_item.title = item_info.item_title
            goods_item.picture = item_info.pic_url
            goods_item.short_title = item_info.short_title
            goods_item.description = item_info.sku_infos[0].props
        return Ok(goods_item)

    def get_issuing_item(self, source: Literal["platform", "erp"], goods_item: GoodsItem):
        if (datasource_result := self.get_datasource(source)).is_err():
            return Err(datasource_result.unwrap_err())
        datasource, sid = datasource_result.unwrap()
        if source == "erp":
            item_key = goods_item.sku_outer
        else:
            item_key = goods_item.sku
        bind_info = GoodsIssuingItem.query.filter_by(datasource=datasource.name, sid=sid, goods_item=item_key).first()
        if bind_info:
            issuing_item = db.session.get(IssuingItem, bind_info.issuing_item_id)
            if not issuing_item or issuing_item.deleted:
                return Err(ValueError("商品绑定的开票项目已失效"))
            return Ok(issuing_item)

        return self.matched_issuing_item(goods_item)

    def matched_issuing_item(self, goods_item):
        from robot_types.core import TypeSpec
        from robot_types.core import Value
        from robot_types.helper import ValueResolver

        resolver = ValueResolver(goods_item.dict())
        for rule in self.issuing_rules:
            predicate_result = (
                Value(TypeSpec("boolean"), predicate=rule.to_predicate()).with_resolver(resolver).resolve()
            )
            if predicate_result.is_ok() and predicate_result.unwrap():
                issuing_item = db.session.get(IssuingItem, rule.issuing_item_id)
                if not issuing_item or issuing_item.deleted:
                    continue
                return Ok(issuing_item)
        return Err(ValueError("未匹配到开票项目"))


@dataclass
class IssuingItemService:
    org_id: int

    def batch_get_issuing_item_by_ids(self, issuing_item_ids: list[int]):
        stmt = (
            sa.select(IssuingItem)
            .where(sa.sql.operators.eq(IssuingItem.org_id, self.org_id))
            .where(sa.sql.operators.in_op(IssuingItem.id, issuing_item_ids))
        )
        result = db.session.execute(stmt)
        return cast(list[IssuingItem], result.scalars().all())
