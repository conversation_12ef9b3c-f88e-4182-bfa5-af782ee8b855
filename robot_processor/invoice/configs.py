from lepollo import ApolloConfig, get_config


class InvoiceConfig(ApolloConfig):
    __namespace__ = "application"

    @property
    def encrypt_key(self):
        return self.get_str("invoice.taxer.encrypt-key", "")

    @property
    def login_cache_timeout(self):
        return self.get_duration("invoice.cache-timeout.login", 10 * 60)

    @property
    def login_timeout(self):
        """登录流程超时时间"""
        return self.get_duration("invoice.timeout.login", 5 * 60)

    @property
    def login_wait_qrcode_timeout(self):
        """等待扫码登录超时时间"""
        return self.get_duration("invoice.timeout.login.wait-qrcode", 1 * 60)

    @property
    def auth_timeout(self):
        """人脸验证流程超时时间"""
        return self.get_duration("invoice.timeout.face-auth", 5 * 60)

    @property
    def auth_wait_qrcode_timeout(self):
        """等待扫码人脸验证超时时间"""
        return self.get_duration("invoice.timeout.face-auth.wait-qrcode", 1 * 60)

    @property
    def issue_timeout(self):
        """开票流程超时时间"""
        return self.get_duration("invoice.timeout.issue", 5 * 60)

    @property
    def issue_lock_timeout(self):
        """开票锁的自动释放时间"""
        return self.get_duration("invoice.timeout.issue-lock", 7 * 60)

    @property
    def auth_code_debug(self):
        return self.get_bool("invoice.auth-code.debug", False)


class RpaControlConfig(ApolloConfig):
    __namespace__ = "client"

    @property
    def endpoint(self):
        return self.get_str("invoice-rpa-control.endpoint", "http://replace-me")

    @property
    def request_timeout(self):
        return self.get_int("invoice-rpa-control.request_timeout", 10)


invoice_config = get_config(config_class=InvoiceConfig)
rpa_control_config = get_config(config_class=RpaControlConfig)
