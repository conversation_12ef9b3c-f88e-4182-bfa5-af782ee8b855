import re
from typing import NamedTuple

from leyan_proto.digismart.robot.invoice.tax_bureau_pb2 import Corporate as pb_Corporate
from leyan_proto.digismart.robot.invoice.workflow_pb2 import InvoiceWorkflow as pb_InvoiceWorkflow
from leyan_proto.digismart.robot.invoice.workflow_pb2 import UserInfo as pb_UserInfo
from leyan_proto.digismart.robot_web.common_pb2 import PaginateConfig as pb_PaginateConfig


class desensitize:
    """对数据进行脱敏的工具类"""

    @classmethod
    def name(cls, raw: str) -> str:
        name_length = len(raw)

        if name_length < 2:
            return raw
        else:
            return "*" * (name_length - 1) + raw[-1]

    @classmethod
    def phone(cls, raw: str) -> str:
        if "-" in raw:
            *reserved, phone = raw.split("-")
        else:
            reserved, phone = [], raw

        if len(phone) == 11:
            return "-".join(reserved + [phone[:3] + "*" * 4 + phone[-4:]])
        else:
            return "-".join(reserved + ["*" * (len(phone) - 4) + phone[-4:]])


class LoadFromRequestResult(NamedTuple):
    org_id: int
    user_info: pb_UserInfo


def load_from_request():
    from robot_processor.currents import g
    from robot_processor.utils import unwrap_optional

    # 从 JWT 中获取租户信息
    org_id = int(g.auth.org_id)

    # 从 JWT 中获取用户信息
    if g.login_user_detail is None:
        user_info = pb_UserInfo(user=pb_UserInfo.User(type=pb_UserInfo.User.SYSTEM, nick="未知"))
    else:
        user_info = pb_UserInfo(
            user=pb_UserInfo.User(
                id=unwrap_optional(g.login_user_detail.user_id),
                type=unwrap_optional(g.login_user_detail.user_type),  # type: ignore[arg-type]
                nick=unwrap_optional(g.login_user_detail.user_nick),
            ),
            groups=[
                pb_UserInfo.Group(uuid=group["uuid"], type=group["type"], name=group["name"])
                for group in g.login_user_detail.groups
            ],
        )

    return LoadFromRequestResult(org_id, user_info)


def catch_error_as_response(response_cls):
    from functools import wraps

    from cattrs import BaseValidationError
    from cattrs import transform_error
    from loguru import logger

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except BaseValidationError as e:
                logger.bind(method=func.__name__).opt(exception=e).warning(transform_error(e))
                return response_cls(succeed=False, msg=str(transform_error(e)))
            except Exception as e:
                if len(args) > 1:
                    arg = args[1]
                else:
                    arg = (args, kwargs)
                logger.bind(method=func.__name__).opt(exception=e).warning(f"{arg} {e}")
                # if "PYTEST_CURRENT_TEST" in os.environ
                return response_cls(succeed=False, msg=str(e))

        return wrapper

    return decorator


def org_required(fn):
    from functools import wraps

    from flask import g

    from robot_processor.auth import empty_jwt

    @wraps(fn)
    def wrapper(*args, **kwargs):
        if g.auth is empty_jwt:
            raise Exception("缺少授权信息")

        return fn(*args, **kwargs)

    return wrapper


def set_default_paginate(config: pb_PaginateConfig):
    if not config.HasField("page"):
        config.page.value = 1
    if not config.HasField("per_page"):
        config.per_page.value = 10


def cron_struct_to_str(cron_struct: pb_Corporate.SyncPlatformApplyCron) -> str:
    if cron_struct.HasField("crontab"):
        return cron_struct.crontab.value
    elif cron_struct.HasField("interval") and cron_struct.unit:
        interval = cron_struct.interval.value
        match cron_struct.unit:
            case pb_Corporate.SyncPlatformApplyCron.Unit.SECOND:
                return f"@every {interval}s"
            case pb_Corporate.SyncPlatformApplyCron.Unit.MINUTE:
                return f"@every {interval}m"
            case pb_Corporate.SyncPlatformApplyCron.Unit.HOUR:
                return f"@every {interval}h"
            case pb_Corporate.SyncPlatformApplyCron.Unit.DAY:
                return f"@every {interval * 24}h"
            case pb_Corporate.SyncPlatformApplyCron.Unit.WEEK:
                return f"@every {interval * 24 * 7}h"
            case pb_Corporate.SyncPlatformApplyCron.Unit.MONTH:
                return f"@every {interval * 24 * 30}h"
            case _:
                raise NotImplementedError()
    else:
        raise ValueError("未初始化的 cron 结构体")


def cron_str_to_struct(cron_str: str) -> pb_Corporate.SyncPlatformApplyCron:
    from collections import OrderedDict
    from datetime import timedelta

    from robot_processor.utils import string_wrapper
    from robot_processor.utils import uint_wrapper

    duration_pattern = re.compile(
        "@every "
        r"((?P<months>\d+?)month)?((?P<weeks>\d+?)weeks)?"
        r"((?P<days>\d+?)d)?((?P<hours>\d+?)h)?"
        r"((?P<minutes>\d+?)m)?((?P<seconds>\d+?)s)?"
    )
    if match := duration_pattern.match(cron_str):
        timedelta_kwargs = {name: int(param) for name, param in match.groupdict().items() if param}
        duration = timedelta(**timedelta_kwargs)
        units = OrderedDict(
            (
                (pb_Corporate.SyncPlatformApplyCron.Unit.MONTH, 2592000),
                (pb_Corporate.SyncPlatformApplyCron.Unit.WEEK, 604800),
                (pb_Corporate.SyncPlatformApplyCron.Unit.DAY, 86400),
                (pb_Corporate.SyncPlatformApplyCron.Unit.HOUR, 3600),
                (pb_Corporate.SyncPlatformApplyCron.Unit.MINUTE, 60),
                (pb_Corporate.SyncPlatformApplyCron.Unit.SECOND, 1),
            )
        )
        for unit, seconds in units.items():
            if duration.total_seconds() % seconds == 0:
                interval = int(duration.total_seconds() / seconds)
                return pb_Corporate.SyncPlatformApplyCron(interval=uint_wrapper(interval), unit=unit)
        else:
            raise ValueError(f"未匹配的时间间隔: {duration}")
    else:
        return pb_Corporate.SyncPlatformApplyCron(crontab=string_wrapper(cron_str))


def update_issuing_item_price(issuing_item: pb_InvoiceWorkflow.IssuingItem):
    from decimal import Decimal

    if not issuing_item.HasField("num"):
        return
    amount = Decimal(issuing_item.amount)
    num = Decimal(issuing_item.num.value)
    price = (amount / num).quantize(Decimal(".00000001"))
    issuing_item.price.value = str(price)
