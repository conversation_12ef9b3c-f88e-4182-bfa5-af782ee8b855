from flask_caching import Cache
from leyan_avro.digismart.action_log import ActionEvent
from leyan_avro.digismart.adaptor.sync_rate_adaptor import SyncTransferResultEvent
from leyan_avro.digismart.exceptionalbusinessorder.exception_pool import ExceptionPoolEvent
from leyan_avro.digismart.erp.erp_config_event import OrgErpConfigEvent, ShopErpConfigEvent
from leyan_avro.digismart.job_assign_event import JobAssignEvent
from leyan_avro.digismart.risk_control.risk_control import RiskRecoedEvent
from leyan_avro.digismart.robot.business_order import CreateErrorEvent
from leyan_avro.digismart.robot.form import ChangeEvent
from leyan_avro.digismart.robot.form import PublishEvent
from leyan_kafka import KafkaProducer
from leyan_logging.helpers.flask import setup_flask_logging_and_tracing

from robot_metrics import Stats
from robot_processor.constants import (
    ACTION_LOG_TOPIC,
    ROBOT_JOB_ASSIGN_TOPIC,
    ROBOT_FORM_TOPIC,
    RISK_CONTROL_TOPIC,
    BUSINESS_ORDER_CREATE_ERROR_EVENT_TOPIC, AUTO_BUSINESS_TO_ROBOT_TOPIC,
    ROBOT_FORM_CHANGE_TOPIC, EXCEPTIONAL_BUSINESS_ORDER_TOPIC,
    ORG_ERP_INFO, SHOP_ERP_INFO
)
from robot_processor.dramatiq import task_queue
from robot_processor.kafka import KafkaConsumer, KafkaStringConsumer
from robot_processor.signals import booting

from .db import db

kiosk_db = db

cache = Cache()
kafka_consumer = KafkaConsumer("ROBOT-PROCESSOR")
kafka_producer = KafkaProducer("ROBOT-PROCESSOR")
job_binlog_consumer = KafkaStringConsumer("robot-processor-binlog-job")
bo_binlog_consumer = KafkaStringConsumer("robot-processor-bo-binlog-group")

token_bucket_string_consumer = KafkaStringConsumer("robot-processor-token-bucket-group")
token_bucket_avro_consumer = KafkaConsumer("robot-processor-token-bucket-group")

bo_create_error_event_producer = kafka_producer.register(
    BUSINESS_ORDER_CREATE_ERROR_EVENT_TOPIC, CreateErrorEvent.schema
)
action_log_producer = kafka_producer.register(
    ACTION_LOG_TOPIC, ActionEvent.schema)
job_assign_producer = kafka_producer.register(
    ROBOT_JOB_ASSIGN_TOPIC, JobAssignEvent.schema)
form_producer = kafka_producer.register(
    ROBOT_FORM_TOPIC, PublishEvent.schema)
risk_control_producer = kafka_producer.register(
    RISK_CONTROL_TOPIC, RiskRecoedEvent.schema)
auto_retry_business_producer = kafka_producer.register(
    AUTO_BUSINESS_TO_ROBOT_TOPIC, SyncTransferResultEvent.schema)
robot_form_change_producer = kafka_producer.register(
    ROBOT_FORM_CHANGE_TOPIC, ChangeEvent.schema)
exceptional_business_order_producer = kafka_producer.register(
    EXCEPTIONAL_BUSINESS_ORDER_TOPIC, ExceptionPoolEvent.schema
)
org_erp_info_producer = kafka_producer.register(
    ORG_ERP_INFO, OrgErpConfigEvent.schema
)
shop_erp_info_producer = kafka_producer.register(
    SHOP_ERP_INFO, ShopErpConfigEvent.schema
)


@booting.connect
def init_app(app):
    setup_flask_logging_and_tracing(app)
    db.init_app(app)
    cache.init_app(app)
    task_queue.init_app(app)
    kafka_consumer.init_app(app)
    job_binlog_consumer.init_app(app)
    bo_binlog_consumer.init_app(app)
    kafka_producer.init_app(app)
    Stats.Request.init_app(app)
    token_bucket_string_consumer.init_app(app)
    token_bucket_avro_consumer.init_app(app)
