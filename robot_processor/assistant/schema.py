from copy import deepcopy
from functools import cached_property
from itertools import chain
from typing import TYPE_CHECKING
from typing import List
from typing import Optional

from loguru import logger
from pydantic import BaseModel
from pydantic import Field
from pydantic import validator

from robot_processor.enums import ApproveType
from robot_processor.enums import AssignStrategy
from robot_processor.enums import Creator
from robot_processor.enums import PlanWhenAssignException
from robot_processor.enums import SelectType
from robot_processor.enums import UserStatus

if TYPE_CHECKING:
    from robot_processor.users.models import Role


_cached_properties_of_account_detail_v2_cls: set[str] = set()


class AccountDetailV2(BaseModel):
    class Config:
        arbitrary_types_allowed = True
        keep_untouched = (cached_property, property)

    # Creator.LEYAN or Creator.ASSISTANT
    user_type: Optional[int]
    user_id: Optional[int]
    user_nick: Optional[str]

    # not none when Creator.LEYAN
    phone: Optional[str]
    enable: Optional[int] = Field(default=1)
    disable_type: Optional[int] = Field(default=0)
    status: Optional[int] = Field(default=1)  # 1正常 0禁用 -1删除
    sub_id: Optional[str]

    @validator("user_type")
    def convert_user_type(cls, v):
        # 如果传入的值不为空，则强制转换为 int 类型。
        if v is not None:
            return int(v)
        return v

    @classmethod
    def system_processor(cls):
        return cls(user_type=Creator.SYSTEM, user_id=0, user_nick="系统自动操作")

    @classmethod
    def rpa_processor(cls):
        return cls(
            user_type=Creator.RPA,
            user_id=0,
            user_nick="RPA应用",
        )

    @classmethod
    def openapi_processor(cls, app_key):
        return cls(user_type=Creator.RPA, user_id=0, user_nick="开放平台", sub_id=app_key)

    @property
    def is_assistant(self):
        return self.user_type == Creator.ASSISTANT.value

    @property
    def is_leyan(self):
        return self.user_type == Creator.LEYAN.value

    def get_bound_leyan_user(self) -> Optional["AccountDetailV2"]:
        """获取当前用户的绑定的飞梭账号

        仅平台账号有绑定的飞梭账号
        """
        from robot_processor.client import kiosk_client

        _logger = logger.bind(method="get_bound_leyan_user")
        if self.user_id is None:
            _logger.warning(f"{self} user_id is None")
            return None
        if self.user_type is None:
            return None
        if Creator(self.user_type) == Creator.LEYAN:
            return self
        if Creator(self.user_type) == Creator.ASSISTANT:
            return kiosk_client.get_bound_leyan_user(self.user_id)
        return None

    def get_roles(self) -> tuple[list["Role"], None | str]:
        """
        获取账号所属的有效的角色。
        """
        from robot_processor.client import kiosk_client

        if self.user_id is None:
            return [], "缺失 USER_ID"
        if self.user_type not in [Creator.LEYAN.value, Creator.ASSISTANT.value]:
            return [], "不支持的用户类型"
        if Creator(self.user_type) == Creator.LEYAN:
            roles = kiosk_client.get_roles_for_leyan_user(user_id=self.user_id)
            return roles, None
        if Creator(self.user_type) == Creator.ASSISTANT:
            roles = kiosk_client.get_roles_for_platform_user(user_id=self.user_id)
            return roles, None
        return [], "无法获取到角色信息"

    def get_bound_platform_users(self, channel_id) -> List["AccountDetailV2"]:
        """在指定店铺内查找和当前账号等价的平台账号，等价是指符合以下条件之一：

        - id 与 self.user_id 相同，且 self.user_type 为 ASSISTANT
        - 绑定的飞梭账号与 self.user_id 相同，且 self.user_type 为 LEYAN
        """
        from robot_processor.client import kiosk_client

        return kiosk_client.get_platform_users_by_users_and_groups([self], None, channel_id)

    def __eq__(self, other):
        if isinstance(other, dict):
            try:
                other = AccountDetailV2(**other)
            except:  # noqa
                return False
        if not isinstance(other, AccountDetailV2):
            return False
        if self.user_type == other.user_type and self.user_id == other.user_id:
            return True
        return False

    def __hash__(self):
        return hash(f"{self.user_id}-{self.user_type}")

    def is_valid(self):
        return self.status == UserStatus.NORMAL.value

    def __str__(self):
        return (
            "Account("
            f"user_id={self.user_id}, "
            f"user_nick={self.user_nick}, "
            f"user_type={self.user_type and Creator(self.user_type)}, "
            f"status={self.status and UserStatus(self.status)}"
            ")"
        )

    @cached_property
    def bound_feisuo_user(self) -> Optional["AccountDetailV2"]:
        return self.get_bound_leyan_user()

    @cached_property
    def feisuo_user_id(self) -> int | None:
        if feisuo_user := self.bound_feisuo_user:
            return feisuo_user.user_id
        return None

    @cached_property
    def feisuo_user_nick(self) -> str | None:
        if feisuo_user := self.bound_feisuo_user:
            return feisuo_user.user_nick
        return None

    @cached_property
    def feisuo_user_phone(self) -> str | None:
        if feisuo_user := self.bound_feisuo_user:
            return feisuo_user.phone
        return None

    @cached_property
    def feisuo_groups(self) -> list[dict]:
        if feisuo_user := self.bound_feisuo_user:
            return feisuo_user.groups
        return []

    @cached_property
    def groups(self) -> list[dict]:
        from robot_processor.users.models import GroupUserMapping
        from robot_processor.users.models import UserGroup

        if self.user_type == Creator.ASSISTANT.value:
            user_group_type = UserGroup.TYPE_PLATFORM
        elif self.user_type == Creator.LEYAN.value:
            user_group_type = UserGroup.TYPE_LEYAN
        else:
            return []
        groups_orm = (
            UserGroup.query.join(GroupUserMapping, UserGroup.group_uuid == GroupUserMapping.group_uuid)
            .filter(UserGroup.deleted == 0, UserGroup.status == 1)
            .filter(GroupUserMapping.user_id == self.user_id, UserGroup.type == user_group_type)
            .all()
        )
        result = []
        for group in groups_orm:
            result.append({"uuid": group.group_uuid, "name": group.name, "type": group.type_name})
        return result

    def has_permission(self, permission_code: str) -> bool:
        return permission_code in self.permissions

    @cached_property
    def permissions(self) -> set[str]:
        from robot_processor.client import kiosk_client

        leyan_user = self.bound_feisuo_user
        if leyan_user is None or leyan_user.user_id is None:
            return set()
        return kiosk_client.get_permissions_for_user(leyan_user.user_id)

    def dict(self, *args, **kwargs):
        # HACK: 避免将 bound_feisuo_user 等 cached_property 也序列化
        return super().dict(exclude=_cached_properties_of_account_detail_v2_cls)


for name, value in AccountDetailV2.__dict__.items():
    if isinstance(value, cached_property):
        _cached_properties_of_account_detail_v2_cls.add(name)


class AssigneeGroup(BaseModel):
    group_uuid: str
    enable: int = Field(default=1)
    status: Optional[int] = Field(None)


class AssistantV2(BaseModel):
    select_type: Optional[SelectType] = Field(default=SelectType.all)
    """执行客服"""
    details: Optional[List[AccountDetailV2]]
    assignee_groups: List[AssigneeGroup] = Field(default_factory=list)
    online_only: Optional[bool] = Field(default=False)
    assign_strategy: Optional[AssignStrategy] = Field(default=AssignStrategy.AUTO)
    assign_account_exception: Optional[PlanWhenAssignException] = Field(
        default=PlanWhenAssignException.ENTER_EXCEPTION_POOL
    )
    approve_type: ApproveType | None = Field(default=ApproveType.ANY_ONE_TO_SIGN, description="审批方式，默认为或签")
    channel_accounts: List[AccountDetailV2] = Field(default_factory=list, description="平台客服")
    leyan_accounts: List[AccountDetailV2] = Field(default_factory=list, description="乐言客服")

    @classmethod
    def parse(cls, raw: dict):
        """
        FIXME 这里是兼容历史数据，配置的客服信息可能散落在如下 4 个字段里
            details
            channel_accounts
            channle_accounts
            leyan_accounts
        未来在做步骤客服信息改造时，数据维护在 details 一个字段就足够了
        """
        values = deepcopy(raw or {})
        user_objs = {}
        for user_obj in values.get("details") or []:
            # 没有 user_id 和 user_type 的都是无效数据，过滤掉
            if (user_id := user_obj.get("user_id")) is None:
                continue
            if (user_type := user_obj.get("user_type")) is None:
                continue
            # user_id + user_type 确认唯一的用户信息，重复的过滤掉
            if (ident := (user_id, user_type)) in user_objs:
                continue
            user_objs[ident] = user_obj
        for user_obj in chain(
            (values.get("channel_accounts") or []),
            (values.get("channle_accounts") or []),
        ):
            user_type = Creator.ASSISTANT.value
            if (user_id := user_obj.get("user_id")) is None:
                continue
            # user_id + user_type 确认唯一的用户信息，重复的过滤掉
            if (ident := (user_id, user_type)) in user_objs:
                continue
            user_obj["user_type"] = user_type
            user_objs[ident] = user_obj
        for user_obj in values.get("leyan_accounts") or []:
            user_type = Creator.LEYAN.value
            if (user_id := user_obj.get("user_id")) is None:
                continue
            # user_id + user_type 确认唯一的用户信息，重复的过滤掉
            if (ident := (user_id, user_type)) in user_objs:
                continue
            user_obj["user_type"] = user_type
            user_objs[ident] = user_obj

        # 重新组装成一个 details 字段
        values["details"] = list(user_objs.values())
        values["channel_accounts"] = [
            account for account in values["details"] if account.get("user_type") == Creator.ASSISTANT.value
        ]
        values["leyan_accounts"] = [
            account for account in values["details"] if account.get("user_type") == Creator.LEYAN.value
        ]

        return cls.parse_obj(values)

    @classmethod
    def from_assistants_and_assignee_group(cls, assistant_dict, assignee_group_dict):
        # BEGIN: 这段逻辑和 Step.get_assistants_v2 一致
        assistant_dict.setdefault(
            "assignee_groups",
            [
                {"group_uuid": group_uuid, "enable": assignee_group_dict[group_uuid]}
                for group_uuid in assignee_group_dict
            ],
        )
        return cls.parse(assistant_dict)
        # END: 这段逻辑和 Step.get_assistants_v2 一致

    def get_latest_assignee_account_details(self, shop):
        """通过 grpc 接口获取客服最新的信息"""
        from robot_processor.client import kiosk_client

        if self.select_type == SelectType.all:
            return kiosk_client.get_leyan_users_by_channel_id(channel_id=shop.channel_id)
        else:
            return kiosk_client.get_leyan_users_by_users_and_groups(
                users=self.details or [],
                group_uuids={g.group_uuid for g in self.assignee_groups},
                channel_id=shop.channel_id,
            )


def default_form_owners_config():
    return {
        "details": [],
        "online_only": False,
        "select_type": 2,
        "leyan_accounts": [],
        "assign_strategy": 1,
        "assignee_groups": [],
        "channel_accounts": [],
    }
