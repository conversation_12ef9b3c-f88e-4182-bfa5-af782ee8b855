from hashlib import md5
from typing import Union, List

import arrow
from loguru import logger
from pydantic import BaseModel
from requests_opentracing import SessionTracing

from robot_processor.client.conf import app_config
from robot_processor.db import db
from robot_processor.sms.models import SMSSendRecord


class SendResult(BaseModel):
    code: str
    desc: str
    uid: str


class ReportErrorResult(BaseModel):
    code: str
    desc: str


class ReportDetail(BaseModel):
    appkey: str
    phone: str
    status: str
    desc: str
    uid: str
    report_time: str


class ReportResult(BaseModel):
    data: Union[ReportErrorResult, List[ReportDetail]]


class MoguyunClient:
    """
    蘑菇云
    """
    session = SessionTracing()

    def _sign(self, ts: str):
        return md5((app_config.MOGUYUN_APP_KEY
                    + app_config.MOGUYUN_APP_SECRET
                    + str(ts)).encode()).hexdigest()

    def send_msg(self, phone_number: str, msg: str, uid: str,
                 ts: str) -> SendResult:
        sign = self._sign(ts)
        payload = {
            "sign": sign,
            "timestamp": ts,
            "phone": phone_number,
            "appcode": app_config.MOGUYUN_APP_CODE,
            "appkey": app_config.MOGUYUN_APP_KEY,
            "uid": uid,
            "msg": msg
        }
        resp = self.session.post(
            app_config.MOGUYUN_SEND_URL,
            json=payload
        )
        return SendResult.parse_obj(resp.json())

    def fetch_msg_report(self, num: int) -> ReportResult:
        now = arrow.now()
        ts = int(now.timestamp())
        ts_ms = ts * 1000
        sign = self._sign(str(ts_ms))
        payload = {
            "sign": sign,
            "timestamp": ts_ms,
            "appcode": app_config.MOGUYUN_APP_CODE,
            "appkey": app_config.MOGUYUN_APP_KEY,
            "number": num,
        }
        resp = self.session.post(
            app_config.MOGUYUN_REPORT_URL,
            json=payload
        )
        data = {
            "data": resp.json()
        }
        return ReportResult.parse_obj(data)


def cron_msg_report():
    moguyun_client = MoguyunClient()
    while True:
        result = moguyun_client.fetch_msg_report(50)
        if not isinstance(result.data, list):
            logger.info(f"收到 {result.data} 结束短信回执循环")
            break
        for detail in result.data:
            record = SMSSendRecord.query.filter_by(uid=detail.uid).first()
            if record:
                record.status = int(detail.status)
                record.desc = detail.desc
                db.session.add(record)
                db.session.commit()
        logger.info(f"处理 {len(result.data)} 条短信回执")
