from sqlalchemy.orm import Mapped, mapped_column

import sqlalchemy as sa
from robot_processor.db import DbBaseModel


class SMSSendRecord(DbBaseModel):
    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    org_id: Mapped[int] = mapped_column(sa.Integer, nullable=False, index=True)
    uid: Mapped[str] = mapped_column(sa.String(32), nullable=False,
                                     index=True, unique=True,
                                     comment="短信发送记录唯一标识")
    send_ts: Mapped[int | None] = mapped_column(sa.Integer, nullable=False,
                                                comment="发送时间")
    job_id: Mapped[int | None] = mapped_column(
        sa.ForeignKey("job.id"), comment="关联的job"
    )
    status: Mapped[int] = mapped_column(sa.Integer, default=-1,
                                        comment="状态 -1-未知 0-成功 其他失败")
    desc: Mapped[str] = mapped_column(sa.String(256), default='',
                                      comment="状态码描述")
    msg: Mapped[str] = mapped_column(sa.Text, nullable=False,
                                     comment="短信内容")
    phone_number: Mapped[str] = mapped_column(sa.String(32), nullable=False,
                                              comment="电话号码")
