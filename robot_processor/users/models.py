import datetime
from typing import Optional
from typing import Union

import sqlalchemy as sa
from loguru import logger
from result import Err
from result import Ok
from result import Result
from sqlalchemy import text
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.db import DbBaseModel
from robot_processor.db import in_transaction
from robot_processor.enums import Creator
from robot_processor.enums import PermissionFunctionCode
from robot_processor.ext import kiosk_db as db
from robot_processor.shop.models import Shop
from robot_processor.utils import datetime_now


class LeyanUser(DbBaseModel):
    """乐言账号"""

    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_base_user"
    __table_args__ = (
        sa.UniqueConstraint("phone_number", name="uk_phone_number"),
        sa.Index("idx_org_id", "org_id"),
        sa.Index("t_kiosk_base_user_nick_name_index", "nick_name"),
    )

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    org_id: Mapped[int] = mapped_column(sa.Integer, nullable=False)

    phone: Mapped[str] = mapped_column("phone_number", sa.String(20), nullable=False)
    nickname: Mapped[str] = mapped_column("nick_name", sa.String(128), nullable=False)
    status: Mapped[int] = mapped_column(sa.Integer, default=1, comment="0:解绑, 1:正常")
    locked: Mapped[int] = mapped_column(sa.Integer, default=0, comment="0:正常, 1:已禁用/锁定")

    created_by: Mapped[str | None] = mapped_column(sa.String(128))
    created_at: Mapped[datetime.datetime] = mapped_column(sa.DateTime, server_default=sa.text("CURRENT_TIMESTAMP"))
    updated_by: Mapped[str | None] = mapped_column(sa.String(128))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime, server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
    )  # noqa: E501

    def to_account_detail_v2(self) -> AccountDetailV2:
        normal, disabled, deleted = 1, 0, -1
        if self.status == 0:
            status = deleted
        elif self.locked == 0:
            status = normal
        else:
            status = disabled
        return AccountDetailV2(
            user_id=self.id,
            user_nick=self.nickname,
            phone=self.phone,
            user_type=Creator.LEYAN.value,
            status=status,
            enable=status,
        )

    @classmethod
    def find_by_phone(cls, phone: str) -> Optional["LeyanUser"]:
        return cls.query.filter(cls.phone == phone).first()


class LeyanUserShopMapping(DbBaseModel):
    """乐言账号与店铺的映射关系."""

    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_user_channel_mapping"

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    user_id: Mapped[int] = mapped_column(sa.Integer, nullable=False)
    shop_id: Mapped[int] = mapped_column("channel_id", sa.Integer, nullable=False)  # KioskShop.id
    updated_by: Mapped[str | None] = mapped_column(sa.String(128))
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime, server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
    )


class PlatformUser(DbBaseModel):
    """平台账号"""

    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_plat_user"
    __table_args__ = (
        sa.UniqueConstraint("channel_type", "sid", "sub_id", "nick", name="ix_plat_sub_id_user_nick"),
        sa.Index("channel_id", "channel_id"),
    )

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    channel_id: Mapped[int] = mapped_column(sa.Integer, nullable=False)

    nick: Mapped[str] = mapped_column(sa.String(128), nullable=False)
    sid: Mapped[str] = mapped_column(sa.String(128), nullable=False)
    platform: Mapped[str] = mapped_column("channel_type", sa.String(32), nullable=False)
    status: Mapped[int] = mapped_column(sa.Integer, default=1, comment="1:正常, -1:删除, 2:冻结")
    sub_id: Mapped[str | None] = mapped_column(sa.String(32))

    created_at: Mapped[datetime.datetime] = mapped_column(sa.DateTime, default=datetime_now)
    updated_at: Mapped[datetime.datetime] = mapped_column(sa.DateTime, default=datetime_now, onupdate=datetime_now)

    def to_account_detail_v2(self) -> AccountDetailV2:
        normal, disabled = 1, 0
        status = normal if self.status == 1 else disabled
        return AccountDetailV2(
            user_id=self.id,
            sub_id=self.sub_id,
            user_nick=self.nick,
            user_type=Creator.ASSISTANT.value,
            status=status,
            enable=status,
        )

    @classmethod
    def find_or_create_doudian_user(cls, shop: Shop, nick: str) -> "PlatformUser":
        platform_user: Optional[PlatformUser] = (
            PlatformUser.query.filter(PlatformUser.platform == shop.platform.lower())
            .filter(PlatformUser.sid == shop.sid)
            .filter(PlatformUser.nick == nick)
            .first()
        )
        if not platform_user:
            platform_user = PlatformUser()
            platform_user.channel_id = shop.channel_id  # type: ignore[assignment]
            platform_user.nick = nick
            platform_user.sid = shop.sid
            platform_user.platform = shop.platform.lower()
            platform_user.sub_id = nick
            db.session.add(platform_user)
            db.session.commit()
            logger.info(
                f"create platform user, shop: {shop.nick}@{shop.channel_id}, "
                f"nick: {nick}, platform: {platform_user.id}"
            )

        return platform_user

    @classmethod
    def find_or_create_taobao_user(cls, shop: Shop, nick: str):
        from robot_processor.client import taobao_client

        user_in_db: PlatformUser | None = (
            PlatformUser.query.filter(PlatformUser.platform == shop.platform.lower())
            .filter(PlatformUser.sid == shop.sid)
            .filter(PlatformUser.nick == nick)
            .first()
        )
        if user_in_db:
            return Ok(user_in_db)

        grant_record = shop.get_recent_record()
        if not grant_record:
            error = Exception(f"未找到 {nick} 的平台账号信息，且店铺无授权")
            return Err(error)

        shop_subusers_result = taobao_client.subusers_get(grant_record.access_token, shop.nick)
        if shop_subusers_result.is_err():
            return Err(shop_subusers_result.unwrap_err())

        shop_subusers_response = shop_subusers_result.unwrap()
        subusers = shop_subusers_response["subaccounts"]
        for user_info in subusers:
            if user_info["sub_nick"] == nick:
                logger.info(f"create taobao user {user_info}")
                user = cls.insert_taobao_user(shop, user_info["sub_nick"], user_info["sub_id"], user_info["sub_status"])
                return Ok(user)

        else:
            error = Exception(f"未找到 {nick} 的平台账号信息，请确认账号是否正确")
            return Err(error)

    @classmethod
    def insert_taobao_user(cls, shop: Shop, sub_nick: str, sub_id: int, sub_status: int):
        user = PlatformUser(
            channel_id=shop.channel_id,
            platform=shop.platform.lower(),
            sid=shop.sid,
            nick=sub_nick,
            sub_id=sub_id,
            status=sub_status,
        )
        with in_transaction():
            db.session.add(user)
        return user


class PlatformUserMapping(DbBaseModel):
    """乐言账号与平台账号的映射关系"""

    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_user_account"
    __table_args__ = (
        sa.UniqueConstraint("channel_id", "account", name="uk_channel_id_account"),
        sa.Index("idx_user_id", "user_id"),
        sa.Index("idx_account_id", "account_id"),
    )

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    org_id: Mapped[int] = mapped_column(sa.Integer, nullable=False)
    channel_id: Mapped[int] = mapped_column(sa.Integer, nullable=False)
    user_id: Mapped[int] = mapped_column(sa.ForeignKey("t_kiosk_base_user.id"), nullable=False)
    platform_user_id: Mapped[int] = mapped_column("account_id", sa.ForeignKey("t_kiosk_plat_user.id"), nullable=False)

    platform_nickname: Mapped[str] = mapped_column("account", sa.String(128), nullable=False)

    @classmethod
    def check_user_bind_constraint(cls, user: Union[LeyanUser, PlatformUser], shop: Shop) -> Result[None, str]:
        """一个平台账号只能和一个乐言账号绑定"""
        if isinstance(user, LeyanUser):
            where_clause = cls.user_id == user.id
        else:
            where_clause = cls.platform_user_id == user.id

        exists = cls.query.filter(cls.channel_id == shop.channel_id).filter(where_clause).exists()
        if db.session.query(exists).scalar():
            return Err("用户已绑定")
        return Ok(None)

    @classmethod
    def bind(cls, platform_user: PlatformUser, leyan_user: LeyanUser, shop: Shop) -> Result["PlatformUserMapping", str]:
        if any(
            [
                cls.check_user_bind_constraint(platform_user, shop).is_err(),
                cls.check_user_bind_constraint(leyan_user, shop).is_err(),
            ]
        ):
            return Err("用户已绑定")

        platform_user_mapping = PlatformUserMapping()
        platform_user_mapping.org_id = shop.org_id  # type: ignore[assignment]
        platform_user_mapping.channel_id = shop.channel_id  # type: ignore[assignment]
        platform_user_mapping.user_id = leyan_user.id
        platform_user_mapping.platform_user_id = platform_user.id
        platform_user_mapping.platform_nickname = platform_user.nick
        db.session.add(platform_user_mapping)
        db.session.commit()

        return Ok(platform_user_mapping)


class UserGroup(DbBaseModel):
    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_group"
    TYPE_LEYAN = 1
    TYPE_PLATFORM = 2

    id: Mapped[int] = mapped_column(sa.BigInteger, primary_key=True)
    org_id: Mapped[int] = mapped_column(sa.BigInteger, nullable=False, comment="租户ID")
    group_uuid: Mapped[str] = mapped_column(sa.String(32), nullable=False, default="", comment="组uuid")
    leader_id: Mapped[int] = mapped_column(sa.BigInteger, nullable=False, default=0, comment="组长主键ID(不存在，默认为0)")
    name: Mapped[str] = mapped_column(sa.String(128), nullable=False, default="", comment="组名")
    remark: Mapped[str | None] = mapped_column(sa.String(256), default=None, comment="说明")
    type: Mapped[int] = mapped_column(sa.Integer, nullable=False, default=1, comment="组类型[1:乐言账号组, 2:平台账号组]")
    status: Mapped[int] = mapped_column(sa.Integer, nullable=False, default=1, comment="组状态[1:启用, 0:禁用]")
    deleted: Mapped[int] = mapped_column(
        sa.Integer, nullable=False, default=0, comment="逻辑删除标志[NOT_DELETED(0):未删除,IS_DELETED(1):已删除]"
    )
    created_by: Mapped[str | None] = mapped_column(sa.String(128), default=None, comment="创建人")
    created_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime, nullable=False, default=datetime_now, comment="创建时间"
    )
    updated_by: Mapped[str | None] = mapped_column(sa.String(128), default=None, comment="更新人")
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime, nullable=False, default=datetime_now, onupdate=datetime_now, comment="更新时间"
    )

    @property
    def type_name(self) -> str:
        if self.type == self.TYPE_LEYAN:
            return "LEYAN"
        elif self.type == self.TYPE_PLATFORM:
            return "PLATFORM"
        return "UNKNOWN"


class GroupUserMapping(DbBaseModel):
    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_group_user_mapping"
    id: Mapped[int] = mapped_column(sa.BigInteger, primary_key=True)
    group_uuid: Mapped[str] = mapped_column(sa.String(32), nullable=False, default="", comment="组uuid")
    user_id: Mapped[int] = mapped_column(sa.BigInteger, nullable=False, comment="乐言账号或平台账号ID")
    user_type: Mapped[int] = mapped_column(sa.Integer, nullable=False, comment="账号类型(2：平台客服账号，4：乐言账号)")
    created_by: Mapped[str | None] = mapped_column(sa.String(128), default=None, comment="创建人")
    created_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime, nullable=False, default=datetime_now, comment="创建时间"
    )


class Role(DbBaseModel):
    """角色定义."""

    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_base_role"
    id: Mapped[int] = mapped_column(sa.BigInteger, primary_key=True)
    org_id: Mapped[int] = mapped_column(
        sa.BigInteger, nullable=False, default=0, comment="org_id 租户ID （org_id 为0表示默认的角色）"
    )
    role_code: Mapped[str] = mapped_column(sa.String(32), nullable=False, comment="角色编码")
    role_name: Mapped[str] = mapped_column(sa.String(128), nullable=False, comment="角色名称")
    role_desc: Mapped[str] = mapped_column(sa.String(256), nullable=False, comment="角色描述")
    role_type: Mapped[int] = mapped_column(sa.Integer, nullable=False, default=2, comment="角色类型(1默认角色, 2商家自定义角色)")
    status: Mapped[int] = mapped_column(sa.Integer, nullable=False, default=1, comment="0: 未启用 1:已启用")
    deleted: Mapped[int] = mapped_column(
        sa.Integer, nullable=False, default=0, comment="逻辑删除标志[NOT_DELETED(0):未删除,IS_DELETED(1):已删除]"
    )
    created_by: Mapped[str | None] = mapped_column(sa.String(128), nullable=False, comment="创建人")
    created_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime, nullable=False, default=datetime_now, comment="创建时间"
    )
    updated_by: Mapped[str | None] = mapped_column(sa.String(128), nullable=False, comment="更新人")
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime, nullable=False, default=datetime_now, onupdate=datetime_now, comment="更新时间"
    )


class Permission(DbBaseModel):
    """用户操作权限定义."""

    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_base_function"

    id: Mapped[int] = mapped_column(sa.BigInteger, primary_key=True)
    parent_id: Mapped[int] = mapped_column(sa.BigInteger, nullable=False, default=0, comment="父级编号")
    function_code: Mapped[PermissionFunctionCode] = mapped_column(sa.String(32), nullable=False, comment="功能编码")
    function_type: Mapped[int] = mapped_column(sa.SmallInteger, nullable=False, comment="功能类型[1:模块目录，2菜单，3按钮，4其他]")
    function_name: Mapped[str] = mapped_column(sa.String(128), nullable=False, comment="功能名称")
    function_display_name: Mapped[str] = mapped_column(sa.String(128), nullable=False, comment="功能展示名称")
    function_desc: Mapped[str] = mapped_column(sa.String(256), nullable=False, comment="功能描述")
    intercept_uri_schema: Mapped[dict | None] = mapped_column(sa.JSON, comment="请求拦截的uri列表json")
    module_level: Mapped[str] = mapped_column(sa.String(200), nullable=False, default="", comment="模块目录层级")
    seq: Mapped[int] = mapped_column(sa.Integer, nullable=False, default=0, comment="权限模块在当前层级下的顺序，由小到大")
    deleted: Mapped[int] = mapped_column(
        sa.SmallInteger, nullable=False, default=0, comment="逻辑删除标志[NOT_DELETED(0):未删除,IS_DELETED(1):已删除]"
    )
    created_by: Mapped[str] = mapped_column(sa.String(128), nullable=False, comment="创建人")
    created_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_by: Mapped[str] = mapped_column(sa.String(128), nullable=False, comment="更新人")
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime,
        nullable=False,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )


class RolePermissionMapping(DbBaseModel):
    """角色权限映射表."""

    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_base_role_function_mapping"

    id: Mapped[int] = mapped_column(sa.BigInteger, primary_key=True)
    role_id: Mapped[int] = mapped_column(sa.BigInteger, nullable=False, comment="FK角色编号")
    function_id: Mapped[int] = mapped_column(sa.BigInteger, nullable=False, comment="FK功能编号")
    mapping_type: Mapped[int] = mapped_column(
        sa.SmallInteger, nullable=False, default=1, comment="映射类型，针对默认角色有意义[1:加权限, 0:减权限]"
    )
    edited: Mapped[int] = mapped_column(sa.SmallInteger, nullable=False, default=1, comment="可编辑标志[1:可编辑, 0:不可编辑]")
    created_by: Mapped[str] = mapped_column(sa.String(128), nullable=False, comment="创建人")
    created_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_by: Mapped[str] = mapped_column(sa.String(128), nullable=False, comment="更新人")
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime,
        nullable=False,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )


class UserRoleMapping(DbBaseModel):
    """用户角色映射表."""

    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_base_user_role_mapping"

    id: Mapped[int] = mapped_column(sa.BigInteger, primary_key=True)
    user_id: Mapped[int] = mapped_column(sa.BigInteger, nullable=False, comment="FK用户编号")
    role_id: Mapped[int] = mapped_column(sa.BigInteger, nullable=False, comment="FK角色编号")
    deleted: Mapped[int] = mapped_column(
        sa.SmallInteger,
        nullable=False,
        default=0,
    )
    created_by: Mapped[str] = mapped_column(sa.String(128), nullable=False, comment="创建人")
    created_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间"
    )
    updated_by: Mapped[str] = mapped_column(sa.String(128), nullable=False, comment="更新人")
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime,
        nullable=False,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )


class UserLastAccess(DbBaseModel):
    """记录一个用户最近一次访问 robot-processor web-api 的平台子账号, 接口名和时间，以用户判断用户近期活跃程度.

    changelog:
    1. 2024-4.9 为找出 #329 中异常乐言账号的活跃程度而添加该表，为了不必要的大量数据写入，仅记录 heartbeat 接口的最近访问时间，而不是所有任意接口的访问时间.
    """

    __bind_key__ = "kiosk"
    __table_args__ = (sa.UniqueConstraint("user_id", "platform_user_id", name="uk_userid_platform_user_id"),)
    id: Mapped[int] = mapped_column(sa.BigInteger, primary_key=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(sa.BigInteger, nullable=False, comment="用户ID, 乐言账号 ID")
    platform_user_id: Mapped[int] = mapped_column(
        sa.BigInteger, nullable=False, comment="平台账号 ID, 如果为 0，说明最近这个访问不是通过侧边栏发起的"
    )
    endpoint: Mapped[str] = mapped_column(sa.String(128), nullable=False, comment="api 接口")
    access_ts: Mapped[datetime.datetime] = mapped_column(sa.DateTime, nullable=False, comment="访问时间")

    @classmethod
    @in_transaction()
    def record_http_access(cls):
        """记录当前 HTTP 请求的平台子账号, 接口名和时间，以用户判断用户近期活跃程度."""
        from flask import request
        from sqlalchemy.dialects.mysql import insert

        from robot_processor.currents import g

        auth = g.auth
        user_id = auth.user_id
        if user_id == 0:
            return
        if auth.login_user_type == Creator.LEYAN:
            platform_user_id = 0
        elif auth.login_user_type == Creator.ASSISTANT:
            platform_user_id = auth.login_user_id
        else:
            return

        endpoint = request.endpoint or "unknown"
        endpoint = endpoint[:128]
        access_ts = datetime.datetime.now()
        stmt = (
            insert(cls)
            .values(user_id=user_id, platform_user_id=platform_user_id, endpoint=endpoint, access_ts=access_ts)
            .on_duplicate_key_update(endpoint=endpoint, access_ts=access_ts)
        )
        db.session.execute(stmt)
