from copy import copy
from dataclasses import asdict
from functools import singledispatch

import robot_types.core
import robot_types.helper
from google.protobuf.json_format import ParseDict
from google.protobuf.struct_pb2 import Struct as pb_Struct
from google.protobuf.struct_pb2 import Value as pb_Value
from leyan_proto.digismart.robot.symbol_table_pb2 import Filter as pb_Filter
from leyan_proto.digismart.robot.symbol_table_pb2 import SimpleFilter as pb_SimpleFilter
from leyan_proto.digismart.robot.symbol_table_pb2 import Symbol as pb_Symbol
from robot_types.core import Symbol
from robot_types.core import TypeSpec
from robot_types.helper import deserialize

from robot_processor.utils import get_dict_factory
from robot_processor.utils import message_to_dict


def symbol_to_pb(symbol: Symbol):
    from leyan_proto.digismart.robot.symbol_table_pb2 import EnumOption as pb_EnumOption

    dict_factory = get_dict_factory()
    pb = pb_Symbol()
    pb.type_spec.update(asdict(symbol.type_spec.to_deprecated(), dict_factory=dict_factory))
    pb.name = symbol.name
    if symbol.label:
        pb.label.value = symbol.label
    if symbol.children:
        pb.children.extend([symbol_to_pb(child) for child in symbol.children])
    if symbol.render_config:
        pb.render_config.update(symbol.render_config)
        if "options" in symbol.render_config:
            pb.options.enum.extend(
                [
                    ParseDict(option, pb_EnumOption(), ignore_unknown_fields=True)
                    for option in symbol.render_config["options"]
                ]
            )
    if symbol.extra:
        pb.extra.update(symbol.extra)
    return pb


def type_spec_from_pb(pb_type_spec: pb_Struct):
    from cattrs import structure

    raw = message_to_dict(pb_type_spec)
    match raw:
        case {"type": "array", "spec": [dict() as items]}:
            raw["items"] = items  # type: ignore[unreachable]
        case {"type": "collection", "spec": list() as properties} if all(
            isinstance(p, dict) and "name" in p for p in properties
        ):
            raw["properties"] = {p.pop("name"): p for p in raw["spec"]}
    return structure(raw, TypeSpec)


def type_spec_from_deprecated(deprecated: dict):
    def normalize(item: dict):
        spec: list[dict] | None = item.pop("spec", None)
        if item["type"] == "array" and spec:
            item["items"] = normalize(spec[0])
        elif item["type"] == "collection":
            item["properties"] = {}
            for child in spec or []:
                child_name = child.pop("name")
                item["properties"][child_name] = normalize(child)
        return item

    return deserialize(normalize(deprecated), TypeSpec)


def operator_to_pb(operator: str) -> int:
    return pb_Filter.Operator.Value(operator.upper())


@singledispatch
def to_predicate_filter(filter_) -> robot_types.core.Filter:
    raise NotImplementedError(f"不支持的类型: {type(filter_)} {repr(filter_)}")


@to_predicate_filter.register
def dict_to_predicate_filter(filter_: dict):
    filter_ = copy(filter_)
    if "relation" not in filter_:
        filter_["relation"] = "and"
    if "conditions" not in filter_:
        filter_["conditions"] = []
    return robot_types.helper.deserialize(filter_, robot_types.core.Filter)


@to_predicate_filter.register
def none_to_predicate_filter(filter_: None):
    return robot_types.core.Filter(relation="and", conditions=[])


@to_predicate_filter.register
def pb_value_to_predicate_filter(pb_value: pb_Value):
    return to_predicate_filter(message_to_dict(pb_value))


@to_predicate_filter.register
def pb_filter_to_predicate_filter(pb_filter: pb_Filter):
    return dict_to_predicate_filter(message_to_dict(pb_filter))


@to_predicate_filter.register
def pb_simple_filter_to_predicate_filter(pb_filter: pb_SimpleFilter):
    return dict_to_predicate_filter(message_to_dict(pb_filter))
