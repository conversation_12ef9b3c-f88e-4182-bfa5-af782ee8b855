from dataclasses import asdict
from dataclasses import dataclass
from dataclasses import field
from typing import Literal

import robot_types
from dacite import from_dict
from flask import Blueprint
from pydantic import BaseModel
from robot_types.core.type_spec import _DeprecatedTypeSpec
from robot_types.helper.operator_resolver import Resolver
from robot_types.helper.predefined import BizType

from robot_processor.decorators import org_required
from robot_processor.utils import Response
from robot_processor.utils import filter_none
from robot_processor.validator import validate

bp = Blueprint("symbol-table-api", __name__)


class ValueForOperator(BaseModel):
    type_spec: dict
    qualifier: Literal["var"] = "var"
    var: dict


class GetSymbolOperators(BaseModel):
    biz_type: BizType
    value: ValueForOperator


@bp.post("/symbol-table/operators")
@validate
def get_symbol_operators(body: GetSymbolOperators) -> Response[dict]:
    from robot_types.core import Value
    from robot_types.helper import deserialize
    from robot_types.helper import serialize

    from robot_processor.utils import filter_none

    resolver = body.biz_type.provide_operator_resolver()
    value = deserialize(body.value.dict(), Value)
    operators = resolver.resolve(type_spec=value.type_spec, name=value.var.path)
    if body.biz_type == BizType.QUERY_BUSINESS_ORDER:
        operators.pop("each", None)  # 数据库升级前还未支持 Each 的查询
    operator_entries = [
        filter_none(serialize(operator_entry)) for operator_entry in OperatorEntry.from_operators(operators)
    ]
    response = Response.Success(dict(operators=operator_entries))
    return response


@dataclass
class OperatorEntry:
    operator: str
    label: str
    type_spec: _DeprecatedTypeSpec | None
    extra: dict | None = field(default=None)
    cascade: list["OperatorEntry"] | None = field(default=None)

    @classmethod
    def from_operators(cls, resolver: Resolver):
        entries: list[OperatorEntry] = []
        for name, resolver in resolver.items():
            entry = OperatorEntry(
                name,
                resolver.label,
                resolver.type_spec.to_deprecated() if resolver.type_spec else None,
                resolver.extra,
            )
            if resolver.cascade:
                entry.cascade = cls.from_operators(resolver.cascade)
            entries.append(entry)
        return entries


class ListScopeSymbols(BaseModel):
    biz_type: BizType
    scope: dict | None = None

    class Response(BaseModel):
        symbols: list[dict]


@bp.post("/symbol-table/scope-symbols")
@org_required
@validate
def list_scope_symbols(body: ListScopeSymbols) -> Response[ListScopeSymbols.Response]:
    from flask import g

    try:
        symbol_table = body.biz_type.provide_symbol_table(symbol_context={"org_id": g.org_id})
    except KeyError:
        return Response.Failed("未注册")
    if body.scope:
        scope = from_dict(robot_types.core.Scope, body.scope)
    else:
        scope = symbol_table.root_scope
    symbols = symbol_table.scope_symbols_mapper[scope.id].symbols
    response = ListScopeSymbols.Response(symbols=[filter_none(asdict(symbol.to_deprecated())) for symbol in symbols])
    return Response.Success(response)
