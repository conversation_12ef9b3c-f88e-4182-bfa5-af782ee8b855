from functools import wraps
from typing import ClassVar

from pydantic import BaseModel

from robot_processor.symbol_table import TypeSpec

__all__ = ["_NamedTypeSpec"]


class _NamedTypeSpec(BaseModel):
    __typename__: ClassVar[str]

    @classmethod
    def to_type_spec(cls) -> TypeSpec:
        from robot_processor.symbol_table._model_converter import model_to_typespec

        typespec = model_to_typespec(cls)
        typespec.typename = cls.__typename__
        return typespec

    @wraps(BaseModel.dict)
    def dict(self, *args, **kwargs):
        dict_value = super().dict(*args, **kwargs)
        if "__root__" in self.__fields__:
            return dict_value["__root__"]
        return dict_value
