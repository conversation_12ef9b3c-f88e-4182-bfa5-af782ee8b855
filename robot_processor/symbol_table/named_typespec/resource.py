"""飞梭工单中的资源"""
from robot_processor.symbol_table.named_typespec._base import _NamedTypeSpec


class LeyanUser(_NamedTypeSpec):
    __typename__ = "Leyan.User"

    id: int
    type: str
    nick: str

    @classmethod
    def get_enum_option(cls, context):
        from robot_processor.symbol_table.models import EnumOption
        from robot_processor.users.models import LeyanUser

        users: list[LeyanUser] = LeyanUser.query.filter_by(
            org_id=context["org_id"]
        ).all()

        return [
            EnumOption(
                label=user.nickname or "",
                value=dict(id=user.id, type="LEYAN", nick=user.nickname),
            )
            for user in users
        ]


class Shop(_NamedTypeSpec):
    __typename__ = "Leyan.Shop"

    sid: str
    platform: str

    @classmethod
    def get_enum_option(cls, context):
        from robot_processor.symbol_table.models import EnumOption
        from robot_processor.shop.models import Shop
        shops: list[Shop] = Shop.query.filter(
            Shop.org_id == str(context["org_id"]),
            Shop.Filters.accessible
        ).all()
        return [
            EnumOption(
                label=shop.title or shop.nick or shop.sid,
                value=dict(sid=shop.sid, platform=shop.platform)
            )
            for shop in shops
            if shop.title or shop.nick or shop.sid
        ]
