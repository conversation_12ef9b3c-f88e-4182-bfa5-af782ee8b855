from typing import Optional

from robot_processor.utils import make_fields_optional
from robot_processor.symbol_table.named_typespec._base import _NamedTypeSpec


@make_fields_optional
class ComponentTradeNo(_NamedTypeSpec):
    __typename__ = "Component.TradeNo"

    tid: str
    oid: Optional[str]


class ComponentTradeNoList(_NamedTypeSpec):
    __typename__ = "Component.TradeNoList"
    __root__: list[ComponentTradeNo]

    def __init__(self, __root__):
        super().__init__(__root__=__root__)


@make_fields_optional
class ComponentProduct(_NamedTypeSpec):
    __typename__ = "Component.Product"

    TID: str
    OID: str
    PICTURE: str
    TITLE: str
    DESCRIPTION: str
    SPU: str
    SKU: str
    SKU_NAME: str
    SPU_OUTER: str
    SKU_OUTER: str
    PRICE: float
    INVENTORY: int
    PAYMENT: float
    COMBINE: str
    COUNT: int
    SHORT_TITLE: str


class ComponentProductList(_NamedTypeSpec):
    __typename__ = "Component.ProductList"
    __root__: list[ComponentProduct]

    def __init__(self, __root__):
        super().__init__(__root__=__root__)


class ComponentFile(_NamedTypeSpec):
    __typename__ = "Component.File"

    fileName: str
    url: str


class ComponentFileList(_NamedTypeSpec):
    __typename__ = "Component.FileList"
    __root__: list[ComponentFile]

    def __init__(self, __root__):
        super().__init__(__root__=__root__)
