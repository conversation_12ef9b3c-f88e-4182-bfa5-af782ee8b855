"""TODO 由 site-packages 生成代码"""
from datetime import datetime, date, time
from types import UnionType
from typing import get_origin, get_args, Any, Annotated

from pydantic import BaseModel
from pydantic.fields import <PERSON><PERSON><PERSON>

from robot_processor.symbol_table import TypeSpec


def field_to_typespec(field: <PERSON><PERSON>ield, with_name=False):
    if get_origin(field.outer_type_) is list:
        if not field.sub_fields:
            raise TypeError(f"unexpected anonymous list. {field}")
        type_spec = TypeSpec(
            type=TypeSpec.ARRAY,
            spec=[
                field_to_typespec(sub_field, False) for sub_field in field.sub_fields
            ],
            name=field.name if with_name else None,
        )

    elif get_origin(field.outer_type_) is UnionType:
        if set(get_args(field.type_)) == {float, int}:
            type_spec = TypeSpec(
                type=TypeSpec.NUMBER,
                name=field.name if with_name else None,
            )
        else:
            raise TypeError(f"unexpected union type. {field}")

    elif (field.outer_type_ is Any) or (
            get_origin(field.outer_type_) is Annotated
            and get_args(field.outer_type_)[0] is Any
    ):
        if typespec_enum := field.field_info.extra.get("typespec"):
            type_spec = TypeSpec(
                type=typespec_enum,
                name=field.name if with_name else None,
            )
        else:
            raise TypeError(f"unexpected Any type. {field}")

    elif issubclass(field.outer_type_, BaseModel):
        type_spec = model_to_typespec(field.outer_type_)
        if with_name:
            type_spec.name = field.name

    elif field.outer_type_ is dict:
        raise TypeError(f"unexpected anonymous dict. {field}")

    elif field.outer_type_ is list:
        raise TypeError(f"unexpected anonymous list. {field}")

    else:  # 普通的标量类型
        type_spec = TypeSpec(
            type=_python_type_mapper[field.type_],
            name=field.name if with_name else None,
        )

    return type_spec


def model_to_typespec(model: type[BaseModel]):
    if "__root__" in model.__fields__:
        typespec = field_to_typespec(model.__fields__["__root__"])
    else:
        typespec = TypeSpec(
            type=TypeSpec.COLLECTION,
            spec=[
                field_to_typespec(sub_field, True)
                for sub_field in model.__fields__.values()
            ],
        )

    return typespec


def _python_type_to_typespec(python_type):
    return TypeSpec(type=_python_type_mapper[python_type])


_python_type_mapper = {
    str: TypeSpec.STRING,
    int: TypeSpec.NUMBER,
    float: TypeSpec.NUMBER,
    bool: TypeSpec.BOOLEAN,
    datetime: TypeSpec.DATETIME,
    date: TypeSpec.DATE,
    time: TypeSpec.TIME,
}
