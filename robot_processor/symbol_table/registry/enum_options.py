from cachetools import TTLCache, cached
from robot_types.core.symbol import EnumOption
from robot_types.helper.symbol import RenderConfigResolver

form_id_cache: TTLCache = TTLCache(maxsize=128, ttl=30)


@cached(form_id_cache)
def _form_id_render(*args, **kwargs):
    from collections import defaultdict
    from sqlalchemy import and_
    from robot_processor.form.models import Form, FormShop
    from robot_processor.shop.models import Shop

    if "org_id" not in kwargs:
        raise ValueError("需要提供 org_id")
    org_id = str(kwargs["org_id"])
    if "sids" in kwargs:
        if isinstance(kwargs["sids"], str):
            sids = [kwargs["sids"]]
        elif isinstance(kwargs["sids"], list):
            sids = kwargs["sids"]
        else:
            raise TypeError("sid 必须是 str 或 list")
        shop_criteria = and_(Shop.org_id == org_id, Shop.sid.in_(sids))
    else:
        shop_criteria = Shop.org_id == org_id
    form_shops: list[FormShop] = (
        FormShop.query.join(Shop, Shop.channel_id == FormShop.channel_id)
        .filter(Form.Filters.can_view_in_report, shop_criteria)
        .options(Form.Options.joined_load_form)
        .options(Form.Options.joined_load_shop)
        .all()
    )
    form_mapper: dict[int, Form] = dict()
    form_info_mapper: dict[int, set[tuple]] = defaultdict(set)
    for form_shop in form_shops:
        form_mapper.setdefault(form_shop.form_id, form_shop.form)
        form_info_mapper[form_shop.form_id].add(
            (
                ("status", form_shop.status),
                ("sid", form_shop.shop.sid),
                ("platform", form_shop.shop.platform),
                ("title", form_shop.shop.title),
            )
        )
    options: list[EnumOption] = []
    for form_id in form_mapper:
        form = form_mapper[form_id]
        form_infos = form_info_mapper[form_id]
        options.append(
            EnumOption(
                form.name,
                form.id,
                extra=dict(
                    form_id=form_id,
                    form_name=form.name,
                    form_info=[dict(form_info) for form_info in form_infos]
                )
            )
        )
    return options


form_id_resolver = RenderConfigResolver(render_enum=_form_id_render)


shop_cache: TTLCache = TTLCache(maxsize=128, ttl=30)


@cached(shop_cache)
def _shop_render(*args, **kwargs):
    from robot_processor.shop.models import Shop
    from robot_processor.client import kiosk_client

    if "org_id" not in kwargs:
        raise ValueError("需要提供 org_id")
    org_id = str(kwargs["org_id"])
    shops: list[Shop] = Shop.Queries.org_shops_by_org_id(org_id).all()
    if "leyan_user_id" in kwargs:
        granted_store_ids = kiosk_client.get_granted_store_ids_for_user(
            kwargs["leyan_user_id"],
            kwargs["org_id"]
        )
        shops = [shop for shop in shops if shop.sid in granted_store_ids]
    return [EnumOption(shop.title, dict(sid=shop.sid, platform=shop.platform)) for shop in shops]


shop_resolver = RenderConfigResolver(render_enum=_shop_render)


user_cache: TTLCache = TTLCache(maxsize=128, ttl=30)


@cached(user_cache)
def _user_render(*args, **kwargs):
    from robot_processor.client import kiosk_client
    from robot_processor.enums import UserType
    from robot_processor.utils import unwrap_optional

    if "org_id" not in kwargs:
        raise ValueError("需要提供 org_id")
    org_id = str(kwargs["org_id"])
    users = []
    kiosk_users = kiosk_client.list_users_by_org(int(org_id))
    users.extend(kiosk_users[0])
    users.extend(kiosk_users[1])

    return [
        EnumOption(
            user.user_nick,
            dict(
                id=unwrap_optional(user.user_id),
                type=UserType(unwrap_optional(user.user_type)).name,
                nick=user.user_nick
            )
        )
        for user in users
    ]


user_resolver = RenderConfigResolver(render_enum=_user_render)


rpa_cache: TTLCache = TTLCache(32, ttl=60 * 60)


@cached(rpa_cache)
def _rpa_render_enum(*args, **kwargs):
    from collections import defaultdict
    from robot_processor.rpa_service.models import Rpa

    rpa_list = Rpa.query.with_entities(Rpa.name, Rpa.task).all()
    options = [
        EnumOption("人工步骤", "human"),
        EnumOption("排他网关", "exclusive_gateway"),
        EnumOption("遍历网关", "iterate_gateway_begin"),
        EnumOption("跳转节点", "jump"),
        EnumOption("审批节点", "approve"),
    ]
    rpa_mapper = defaultdict(list)
    for rpa in rpa_list:
        if not rpa.name:
            continue
        rpa_mapper[rpa.task].append(rpa.name)
    options += [EnumOption("/".join(rpa_mapper[rpa_task]), rpa_task) for rpa_task in rpa_mapper]
    return options


rpa_resolver = RenderConfigResolver(render_enum=_rpa_render_enum)
