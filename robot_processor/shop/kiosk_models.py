import datetime

import sqlalchemy as sa
from sqlalchemy.orm import mapped_column, Mapped

from robot_processor.db import db, DbBaseModel


class KioskOrg(DbBaseModel):
    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_base_org"

    id: Mapped[int] = mapped_column(sa.BigInteger, primary_key=True, comment='编号')
    org_name: Mapped[str] = mapped_column(sa.String(4000), default='', comment='租户名称')
    avatar: Mapped[str] = mapped_column(sa.String(256), default='', comment='商户头像链接')
    status: Mapped[int] = mapped_column(sa.Integer, default=1, comment='状态（1:启用、0:禁用）')
    deleted: Mapped[int] = mapped_column(sa.Integer, default=0,
                                         comment='逻辑删除标志[NOT_DELETED(0):未删除,IS_DELETED(1):已删除]')
    created_at: Mapped[datetime.datetime] = mapped_column(sa.DateTime, server_default=sa.func.now(), comment='创建时间')
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime,
        server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment='更新时间'
    )
    package_list: Mapped[str | None] = mapped_column(sa.String(1024), default='', comment='店铺开通的功能包列表')
    master_cid: Mapped[str | None] = mapped_column(sa.String(50), comment='Boss 推送过来的，标识租户唯一')
    org_type: Mapped[int | None] = mapped_column(sa.Integer,
                                                 comment='租户类型 (1乐言官方 2客户, 3员工租户, 4模板租户，5无效商家)')
    zone: Mapped[str | None] = mapped_column(sa.String(50), comment='大区')
    created_by: Mapped[str | None] = mapped_column(sa.String(128), comment='创建人')
    updated_by: Mapped[str | None] = mapped_column(sa.String(128), comment='更新人')
    use_new_version: Mapped[int | None] = mapped_column(sa.Integer, default=0,
                                                        comment='是否可以使用新版接口（1:是、0:否）')

    @classmethod
    def get_master_cid_by_id(cls, org_id: int):
        stmt = sa.select(cls.master_cid).where(cls.id == org_id)
        result = db.session.execute(stmt)
        return result.scalar_one_or_none()

    @classmethod
    def is_org_enabled(cls, org_id: int):
        org_info = cls.query.with_entities(cls.id, cls.status).filter_by(id=org_id).first()
        if not org_info:
            return False
        return org_info.status == 1


class KioskOrgSalesMan(DbBaseModel):
    __bind_key__ = "kiosk"
    __tablename__ = 't_kiosk_base_org_sales_mapping'

    id: Mapped[int] = mapped_column(sa.BigInteger, primary_key=True, comment='编号')
    master_cid: Mapped[str] = mapped_column(sa.String(50), comment='租户编号')
    deleted: Mapped[int] = mapped_column(sa.Integer, default=0,
                                         comment='逻辑删除标志[NOT_DELETED(0):未删除,IS_DELETED(1):已删除]')
    created_at: Mapped[datetime.datetime] = mapped_column(sa.DateTime, server_default=sa.func.now(), comment='创建时间')
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.DateTime,
        server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment='更新时间'
    )
    master_name: Mapped[str | None] = mapped_column(sa.String(4000), comment='租户名称')

    # 飞梭的销售实施
    sales_man: Mapped[str | None] = mapped_column(sa.String(32), comment='销售ldap')
    sales_man_alias: Mapped[str | None] = mapped_column(sa.String(32), comment='销售别名')
    sales_man_name: Mapped[str | None] = mapped_column(sa.String(32), comment='销售姓名')
    implementer: Mapped[str | None] = mapped_column(sa.String(32), comment='实施ldap')
    implementer_alias: Mapped[str | None] = mapped_column(sa.String(32), comment='实施别名')
    implementer_name: Mapped[str | None] = mapped_column(sa.String(32), comment='实施姓名')

    # RPA的销售(销售会不一样，但是实施是一样的)
    sales_man_rpa: Mapped[str | None] = mapped_column(sa.String(32))
    sales_man_alias_rpa: Mapped[str | None] = mapped_column(sa.String(32))
    sales_man_name_rpa: Mapped[str | None] = mapped_column(sa.String(32))

    created_by: Mapped[str | None] = mapped_column(sa.String(128), comment='创建人')
    updated_by: Mapped[str | None] = mapped_column(sa.String(128), comment='更新人')


class KioskShop(DbBaseModel):
    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_channel"

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    org_id: Mapped[int] = mapped_column(sa.Integer, nullable=False)

    sid: Mapped[str] = mapped_column("channel_no", sa.String(64), nullable=False)
    platform: Mapped[str] = mapped_column("channel_type", sa.String(64), nullable=False)
    nick: Mapped[str] = mapped_column("seller_nick", sa.String(64), nullable=False)
    status: Mapped[int] = mapped_column(sa.Integer, default=1, comment='状态（1:启用、0:禁用）')
    deleted: Mapped[int] = mapped_column(sa.Integer, default=0,
                                         comment='逻辑删除标志[NOT_DELETED(0):未删除,IS_DELETED(1):已删除]')
    refund_agree_accounts: Mapped[list | None] = mapped_column(sa.JSON(), comment="用于做淘宝同意退款的子账号列表")
    tags: Mapped[list | None] = mapped_column(
        sa.JSON, default=None, comment="标签")


class RpaThirdShop(DbBaseModel):
    """由客户自己维护的店铺名称列表"""
    __bind_key__ = "kiosk"
    __tablename__ = "t_kiosk_rpa_third_shop"

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    org_id: Mapped[int]
    shop_name: Mapped[str] = mapped_column(sa.String(64))

    def to_response(self):
        return dict(shop_id=self.id, shop_name=self.shop_name, shop_no="", platform="")
