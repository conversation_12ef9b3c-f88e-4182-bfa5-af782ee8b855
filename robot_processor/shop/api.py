import json
import time
from typing import Dict

import jwt
from flask import Blueprint
from flask import current_app
from flask import jsonify
from flask import request
from flask_sqlalchemy.pagination import Pagination
from loguru import logger
from pydantic import BaseModel
from pydantic import Field
from result import Err
from result import Ok
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.base_schemas import Failed
from robot_processor.base_schemas import Response
from robot_processor.base_schemas import Success
from robot_processor.client import kiosk_client
from robot_processor.client.conf import app_config
from robot_processor.currents import g
from robot_processor.database_util import Query
from robot_processor.decorators import org_required
from robot_processor.decorators import service_required
from robot_processor.decorators import shop_required
from robot_processor.enums import AuthType
from robot_processor.enums import ShopStatus
from robot_processor.ext import cache
from robot_processor.ext import db
from robot_processor.logging import vars as logging_vars
from robot_processor.shop.auth_manager import Credentials
from robot_processor.shop.auth_manager import CredentialShopMapping
from robot_processor.shop.auth_manager import MolaShops
from robot_processor.shop.auth_manager import OrgErpInfo
from robot_processor.shop.auth_manager import PowerGroupRelatedShops
from robot_processor.shop.auth_manager import PowerGroups
from robot_processor.shop.kafka import jst_shop_site_dict as platform_to_shop_site_map
from robot_processor.shop.kiosk_models import KioskShop
from robot_processor.shop.kiosk_models import RpaThirdShop
from robot_processor.shop.models import App
from robot_processor.shop.models import ErpInfo
from robot_processor.shop.models import GrantRecord
from robot_processor.shop.models import Shop
from robot_processor.shop.models import TaobaoSubuserGrantRecord
from robot_processor.shop.schema import AUTH_TYPE_TO_ERP_TYPE_MAP
from robot_processor.shop.schema import AUTH_TYPE_TO_MOLA_SITE_MAP
from robot_processor.shop.schema import CheckGrantRecordRequest
from robot_processor.shop.schema import CreateGrantRecordRequest
from robot_processor.shop.schema import GrandRecordSchema
from robot_processor.shop.schema import GrantChannelInfo
from robot_processor.shop.schema import PlatformGrantSchema
from robot_processor.shop.schema import QianniuAuthentication
from robot_processor.shop.schema import QueryGrantRecordParameters
from robot_processor.shop.schema import ShopTokenSchema
from robot_processor.shop.schema import TaobaoCreateOrRefreshAuthTokenResponse
from robot_processor.shop.schema import TaobaoCreateOrRefreshAuthTokenResult
from robot_processor.shop.schema import TaobaoShopAuthentication
from robot_processor.shop.schema import TaobaoShopGetOpenUid
from robot_processor.shop.schema import UpdateGrantRecordRequest
from robot_processor.shop.tasks import init_miniapp
from robot_processor.validator import validate
from rpa.erp.baisheng import BaiShengOpenPlatformAPIClient
from rpa.erp.guanyiyunapi.sdk import GyyOpenPlatformAPIClient
from rpa.erp.jackyun.sdk import JackyunOpenPlatformAPIClient
from rpa.erp.kuaimai.kuaimai import KuaimaiOpenPlatformAPIClient
from rpa.erp.kuaimai.schemas import KMException
from rpa.erp.wdgj.sdk import WDGJOpenPlatformAPIClient
from rpa.erp.wdt.wdt import WDTOpenPlatformAPIClient
from rpa.erp.wdtulti.wdtulti import WdtUltiOpenPlatformAPIClient
from rpa.erp.wln.wln import WlnClient

api = Blueprint("shop-api", __name__)


# 临时店铺 SID，主要用于标识、统计在主账号尚未授权前，就已经进行授权的子账号信息。
TEMP_SHOP = "TEMP_SHOP"


@api.get("/shop/grant-record")
@shop_required
@validate
def get_shop_grant_record():
    """获取店铺的access_token"""
    grant_record = g.shop.get_recent_record()
    if not grant_record:
        return (
            Response.Failed(error="店铺无授权信息", error_display="店铺无授权信息"),
            404,
        )
    token_data = {
        "sid": g.shop.sid,
        "seller_nick": g.shop.nick,
        "platform": g.shop.platform,
        "access_token": grant_record.access_token,
        "app": g.shop.get_mini_app_template_app_id(),
    }
    return GrandRecordSchema(**token_data)


@api.post("/shop/grant")
@validate
def create_shop_by_grant(body: PlatformGrantSchema) -> tuple[dict, int] | Success:
    """
    kiosk 在saveChannel之后会调用这个接口
    """
    # fixme kafka 的方式支持 seller_id 和 title 后就可以干掉这个接口
    body_dict: Dict = body.dict(exclude_unset=True)
    logger.info(f"shop grant body: {body_dict}")
    body_dict["platform"] = body_dict["platform"].upper()
    shop = None
    # 淘宝/天猫/抖店支持未开店时预记录授权信息
    if body_dict["platform"] in ["TAOBAO", "TMALL", "DOUDIAN"]:
        shop = Shop.query.filter(Shop.Filters.status_authorized).filter(Shop.sid == body_dict["sid"]).first()
    shop = shop or Shop.find_or_create(channel_id=body_dict["channel_id"])
    body_dict["title"] = body_dict.get("title") or body_dict.get("nick") or shop.nick
    for attr, value in body_dict.items():
        if value is not None:  # kiosk的信息不完整  可能会覆盖已经有的信息
            setattr(shop, attr, value)
    db.session.commit()
    if not shop.is_valid():
        return dict(reason="飞梭旗舰版功能，请联系店铺销售升级产品类型"), 403

    """更新店铺基本信息缓存"""
    if shop.nick:
        cache.delete(shop.nick)
    if shop.open_id:
        cache.delete(shop.open_id)
    return Success(success=True)


@api.get("/taobao-shop/user_nick")
@validate
def get_user_nick(query: TaobaoShopGetOpenUid):
    app = App.QN_SIDEBAR
    resp = app.taobao_client.get_open_id_by_mix_nick(query.mix_nick)
    match resp:
        case Ok(result):
            open_uid = result.get("open_uid")
            record = TaobaoSubuserGrantRecord.get_by_open_uid(app, open_uid)
            if record is None or record.nick is None:
                return Failed.bad_request(reason="未找到已授权的用户")
            return {
                "user_nick": record.nick,
                "success": True,
            }
        case Err(e):
            return Failed.bad_request(reason=e)


class GetShopGrantedSubUsersQuery(BaseModel):
    sid: str
    app: App = App.FEISUO


@api.get("/taobao-shop/granted-subusers")
@org_required
@validate
def get_shop_granted_subusers(query: GetShopGrantedSubUsersQuery) -> Response[list]:
    """已授权的子账号列表"""
    if query.sid not in g.org_sids:
        return Response.Failed("无权限访问该店铺")
    sub_nicks = list({grant_record.nick for grant_record in TaobaoSubuserGrantRecord.get_by_sid(query.sid, query.app)})
    subusers = [dict(nick=nick) for nick in sub_nicks]
    return Response.Success(subusers)


@api.get("/taobao-shop/grant")
@validate
def get_taobao_shop_token(query: TaobaoShopAuthentication):
    app = App.QN_SIDEBAR
    logger.info(
        "收到淘宝授权回调，URL 为 {}, 查询参数为 {}, headers 信息为 {}".format(
            request.url,
            request.query_string.decode(encoding="utf-8"),
            request.headers,
        )
    )

    resp = app.taobao_client.create_auth_token(query.code)
    match resp:
        case Ok(create_auth_resp):
            tr: TaobaoCreateOrRefreshAuthTokenResponse = TaobaoCreateOrRefreshAuthTokenResponse.parse_obj(
                create_auth_resp
            )
            result: TaobaoCreateOrRefreshAuthTokenResult = TaobaoCreateOrRefreshAuthTokenResult.parse_raw(
                tr.token_result
            )
            result.urldecode_nick()

            main_nick: str = (result.user_nick or "").split(":")[0]

            is_main_nick: bool = ":" not in result.user_nick  # 主账号和子账号的区别是 nick 中是否包含冒号
            shop_nick: str = result.parent_nick or main_nick
            shop: Shop | None = (
                Shop.query.filter(
                    Shop.nick == shop_nick,
                )
                .filter(Shop.Filters.platform_taobao)
                .first()
            )
            record_not_found = False

            match is_main_nick, shop:
                case False, None:
                    # 店铺未初始化时，子账号进行了访问，先将子账号绑定到一个临时店铺下，然后返回错误信息
                    record = TaobaoSubuserGrantRecord.get_by_open_uid(app, result.open_uid)
                    if not record:
                        record_not_found = True
                        # 将账号绑定到临时店铺下。
                        record = TaobaoSubuserGrantRecord(sid=TEMP_SHOP, app=app, open_uid=result.open_uid)
                    record.nick = result.user_nick
                    record.open_uid = result.open_uid
                    record.access_token = result.access_token
                    record.extra_data = json.loads(tr.token_result)
                    flag_modified(record, "extra_data")
                    if record_not_found:
                        db.session.add(record)
                    db.session.commit()
                    return Failed.bad_request(reason="子账号无法授权")
                case False, Shop() as shop:
                    # 店铺已经初始化，子账号进行访问时，记录子账号的授权信息
                    record = TaobaoSubuserGrantRecord.get_by_open_uid(app, result.open_uid)
                    if not record:
                        record_not_found = True
                        record = TaobaoSubuserGrantRecord(app=app, open_uid=result.open_uid)
                    # 设置一下 sid，以此将此前绑定到临时店铺上的账号移动到当前店铺下。
                    record.sid = shop.sid
                    record.nick = result.user_nick
                    record.open_uid = result.open_uid
                    record.access_token = result.access_token
                    record.extra_data = json.loads(tr.token_result)
                    flag_modified(record, "extra_data")
                    if record_not_found:
                        db.session.add(record)
                    db.session.commit()
                    if not shop.get_recent_record(app):
                        # 店铺没有授权的话，进入飞梭后台也没有意义
                        return Failed.bad_request(reason="缺失授权记录")
                case True, None:
                    # 店铺没有初始化，但是主账号包含了店铺的授权信息，需要先记录授权信息
                    # 并且把店铺的状态置为 AUTHORIZED，此时店铺的信息在数据库中存在，但是是不可访问的
                    # 合同信息更新时，会把店铺的状态更新为 ENABLE，变成可访问状态
                    if shop_info := Shop.Utils.get_taobao_shop_info_by_access_token(
                        access_token=result.access_token, nick=shop_nick
                    ):
                        Shop.Queries.insert_shop_grant_record_for_auth_only(
                            nick=shop_nick,
                            open_id=result.parent_open_uid,
                            title=shop_info["title"],
                            platform=shop_info["platform"],
                            sid=shop_info["sid"],
                            app=App.QN_SIDEBAR,
                            access_token=result.access_token,
                        )
                        # 目前 TaobaoSubuserGrantRecord 承载了通过 open_uid 来查询用户 nick 的功能。
                        # 因此主账号也需要插入其中。
                        record = TaobaoSubuserGrantRecord.find_or_create(
                            sid=shop_info["sid"], app=app, open_uid=result.open_uid
                        )
                        record.nick = result.user_nick
                        record.open_uid = result.open_uid
                        record.access_token = result.access_token
                        record.extra_data = json.loads(tr.token_result)
                        flag_modified(record, "extra_data")
                        db.session.commit()
                    return Failed.bad_request(reason="当前店铺未在飞梭应用查询到对应店铺，建议联系店铺销售，先进行后台店铺绑定再授权")

                case True, Shop() as shop:
                    # 店铺已经初始化，主账号进行访问时，记录主账号的授权信息
                    # 查找已经授权过的子账号。
                    sub_user_records: list[TaobaoSubuserGrantRecord] = TaobaoSubuserGrantRecord.query.filter(
                        TaobaoSubuserGrantRecord.sid == TEMP_SHOP,
                        TaobaoSubuserGrantRecord.nick.startswith(shop_nick),
                    ).all()
                    # 记录主账号的授权信息
                    grant_record = GrantRecord.find_or_create(shop_id=shop.id, app=app)
                    grant_record.access_token = result.access_token
                    grant_record.expires_at_ms = result.expire_time
                    grant_record.refresh_token = result.refresh_token
                    # 目前 TaobaoSubuserGrantRecord 承载了通过 open_uid 来查询用户 nick 的功能。
                    # 因此主账号也需要插入其中。
                    record = TaobaoSubuserGrantRecord.find_or_create(sid=shop.sid, app=app, open_uid=result.open_uid)
                    record.nick = result.user_nick
                    record.open_uid = result.open_uid
                    record.access_token = result.access_token
                    record.extra_data = json.loads(tr.token_result)
                    flag_modified(record, "extra_data")
                    # 更新已授权的子账号
                    for sub_user_record in sub_user_records:
                        sub_user_record.sid = shop.sid
                    db.session.commit()

                    # 更新下店铺的信息
                    if shop:
                        if shop.nick != shop_nick:
                            shop.nick = shop_nick
                        db.session.commit()

            return Success(success=True)
        case Err(e):
            return Failed.bad_request(reason=e)


@api.get("/shop/grant")
@validate
def get_shop_token_by_grant(
    query: QianniuAuthentication,
) -> tuple[Failed, int] | ShopTokenSchema:
    """
    注册在千牛服务市场的接口
    淘宝商家在千牛服务市场购买了飞梭应用并点击 “去使用” 的按钮后，会向这个接口发起请求，接口响应提供一个 jwt 用于进入飞梭后台
    接口请求的 query_string 中携带淘宝商家的授权信息，包括 access_token, open_id, user_nick
    飞梭系统通过这个时机来保存用户主账号/子账号的授权信息
    """
    logging_vars.User.set(query.user_nick)
    token: str = query.access_token
    open_id: str = query.open_id
    nick: str = query.user_nick
    main_nick: str = nick.split(":")[0]

    app = App.FEISUO
    is_main_nick: bool = ":" not in nick  # 主账号和子账号的区别是 nick 中是否包含冒号
    shop = Shop.Queries.taobao_shop_by_nick_or_open_id(main_nick, open_id).filter(Shop.Filters.accessible).first()

    match is_main_nick, shop:
        case False, None:
            # 店铺未初始化时，子账号进行了访问，返回错误信息
            return Failed.bad_request(reason="子账号无法授权")

        case False, Shop() as shop:
            # 店铺已经初始化，子账号进行访问时，记录子账号的授权信息
            record = TaobaoSubuserGrantRecord.find_or_create(sid=shop.sid, app=app, access_token=token)
            if record.nick != nick:
                record.nick = nick
                db.session.commit()
            if not shop.get_recent_record(app):
                # 店铺没有授权的话，进入飞梭后台也没有意义
                return Failed.bad_request(reason="缺失授权记录")

        case True, None:
            # 店铺没有初始化，但是主账号包含了店铺的授权信息，需要先记录授权信息
            # 并且把店铺的状态置为 AUTHORIZED，此时店铺的信息在数据库中存在，但是是不可访问的
            # 合同信息更新时，会把店铺的状态更新为 ENABLE，变成可访问状态
            shop_not_exists: bool = not db.session.query(
                Shop.Queries.taobao_shop_by_nick_or_open_id(main_nick, open_id).exists()
            ).scalar()
            if shop_not_exists and (
                shop_info := Shop.Utils.get_taobao_shop_info_by_access_token(access_token=token, nick=main_nick)
            ):
                Shop.Queries.insert_shop_grant_record_for_auth_only(
                    nick=main_nick,
                    open_id=open_id,
                    title=shop_info["title"],
                    platform=shop_info["platform"],
                    sid=shop_info["sid"],
                    app=App.FEISUO,
                    access_token=token,
                )
            return Failed.bad_request(reason="当前店铺未在飞梭应用查询到对应店铺，建议联系店铺销售，先进行后台店铺绑定再授权")

        case True, Shop() as shop:
            # 店铺已经初始化，主账号进行访问时，记录主账号的授权信息
            GrantRecord.find_or_create(shop_id=shop.id, app=app, access_token=token)

    # 更新下店铺的信息
    if shop:
        if shop.open_id != open_id:
            shop.open_id = open_id
        if shop.nick != main_nick:
            shop.nick = main_nick
        db.session.commit()

    template_version = current_app.config.get("MINI_VERSION_TEMPLATE_VERSION")
    init_miniapp.send(shop.sid, template_version)

    token_res = kiosk_client.get_jwt_for_user(nick, shop.sid, shop.platform)
    match token_res:
        case Ok(jwt_payload):
            token = jwt.encode(jwt_payload.dict(), current_app.config["JWT_KEY"], algorithm="HS256")
            return ShopTokenSchema(token=token)
        case Err(err_message):
            return Failed.bad_request(reason=err_message)


class KioskTokenInfo(BaseModel):
    token: str
    expireTime: int  # token 过期时间，精确到毫秒


class QianniuLoginQuery(BaseModel):
    """千牛的侧边栏或者服务市场通过千牛网关调用飞梭服务时必定会携带的身份信息.

    这些信息可以认为已经被千牛验证过，也就是说，当我们收到请求时，可以确定触发这些请求的用户的 open_id 和 user_nick 就是 query 中的 open_id 和 user_nick.

    相关文档：https://open.taobao.com/v2/doc?spm=a219a.7629140.0.0.6d0d75fekIUu2I#/abilityToOpen?docId=118538&docType=1
    """

    user_nick: str = Field(
        ...,
        description="用户的淘宝账号, 可能是主账号或者子账号，主账号也就是店铺名，如 '飞梭旗舰店'," "子账号格式是 <店铺名>:<子账号>， 如 '飞梭旗舰店:飞梭子账号'",
    )
    open_id: str = Field(..., description="用户的淘宝 open_id, 用于标识用户的唯一身份")


class QianniuLoginResult(BaseModel):
    """
    千牛侧边栏登陆时飞梭的返回结果.

    注意，这里的字段设计需要兼容原 kiosk 相关 web 接口的返回结果，所以包含了 code/msg/success 等字段。
    ref: https://git.leyantech.com/digismart/robot-processor/-/issues/308
    ref: https://git.leyantech.com/fed/mini-sidebar/-/issues/7
    ref: https://git.leyantech.com/digismart/kiosk/-/merge_requests/195
    """

    code: str = "0"
    msg: str = ""
    data: KioskTokenInfo | None = None
    success: bool = True


@api.get("/qianniu-plugin/login")
@validate
def qianniu_plugin_login(query: QianniuLoginQuery) -> QianniuLoginResult:
    """
    通过千牛侧边栏登陆飞梭，替代原 kiosk 的 "GET /login/verifyUserV2".

    用户从千牛侧边栏访问飞梭时，所有请求由千牛服务器转发给飞梭注册在千牛的 callback 接口，转发时会携带 user_nick，open_id 等参数。
    这些参数由千牛服务器保证正确性，飞梭收到请求后，根据 user_nick 和 open_id 查询出更具体的用户和店铺信息，构造出 jwt 并返回给侧边栏前端，
    供侧边栏前端后续访问其他接口时使用。

    只有如下条件都被满足，该接口才会返回 jwt：

    1. main_nick 或 open_id 对应的店铺存在且可访问
    2. user_nick(用户平台账号) 存在
    3. 平台账号已经绑定到了某个飞梭账号，且这个飞梭账号没有被禁用
    """
    logging_vars.User.set(query.user_nick)
    main_nick = query.user_nick.split(":")[0]
    shop = (
        Shop.Queries.taobao_shop_by_nick_or_open_id(main_nick, query.open_id)
        .filter(Shop.deleted.isnot(True), Shop.status == ShopStatus.ENABLE)
        .first()
    )
    if not shop:
        logger.warning("没有找到名为 {} 或者 open id 为 {} 的店铺", main_nick, query.open_id)
        return QianniuLoginResult(code="-111", msg="店铺未绑定", success=False)

    token_res = kiosk_client.get_jwt_for_user(query.user_nick, shop.sid, shop.platform)
    match token_res:
        case Ok(jwt_payload):
            token = jwt.encode(jwt_payload.dict(), current_app.config["JWT_KEY"], algorithm="HS256")
            return QianniuLoginResult(data=KioskTokenInfo(token=token, expireTime=jwt_payload.exp * 1000))
        case Err(err_message):
            logger.warning("获取 jwt 失败: {}", err_message)
            return QianniuLoginResult(code="-112", msg=err_message, success=False)


@api.get("/transformer-shops")
@service_required
def get_shop_by_nick():
    nick: str = request.args.get("nick", "")
    open_id: str = request.args.get("openid", "")
    logger.debug(f"transformer-shops request nick: {nick}, open_id: {open_id}")
    if not nick and not open_id:
        return jsonify(reason="缺失参数"), 400

    if not (info := cache.get(nick)):
        if not (info := cache.get(open_id)):
            if nick:
                shop = (
                    Shop.query.filter(Shop.nick == nick)
                    .filter(Shop.platform.in_(["TAOBAO", "TMALL"]))
                    .filter(Shop.deleted.isnot(True), Shop.status == ShopStatus.ENABLE)
                    .first()
                )
                cache_key = nick
            else:
                shop = (
                    Shop.query.filter(Shop.open_id == open_id)
                    .filter(Shop.platform.in_(["TAOBAO", "TMALL"]))
                    .filter(Shop.deleted.isnot(True), Shop.status == ShopStatus.ENABLE)
                    .first()
                )
                cache_key = open_id
            if not shop:
                return jsonify(reason="无效店铺"), 400
            info = shop.brief()
            cache.set(cache_key, info, timeout=86400)
    logger.debug(f"transformer-shops request nick: {nick}, open_id: {open_id}, resp: {info}")
    return jsonify(shop=info)


@api.get("/shop/detail")
@shop_required
def get_shop_detail():
    shop = Shop.query.filter_by(sid=g.shop.sid).first()
    if not shop:
        return jsonify(reason="无效店铺"), 400
    return jsonify(shop=shop.brief())


@api.get("/shops/<string:sid>")
@service_required
def get_shop_info(sid):
    shop = Shop.query.filter_by(sid=sid).first_or_404()
    shop_brief = shop.brief()
    if grant_record := shop.get_recent_record(App.FEISUO):
        shop_brief["access_token"] = grant_record.access_token
    if app_id := shop.get_mini_app_template_app_id():
        shop_brief["app"] = app_id
    return jsonify(shop=shop_brief)


@api.get("/shops/details/<string:sid>")
@service_required
def get_shop_and_record_info(sid):
    shop = Shop.query.filter_by(sid=sid).first_or_404()
    grant_record = shop.get_recent_record(App.FEISUO)
    app_id = shop.get_mini_app_template_app_id()

    if not grant_record:
        return Failed.bad_request("当前店铺主账号未授权，请前往服务市场应用开启授权")
    if not app_id:
        return Failed.bad_request("当前店铺主账号未授权，请前往服务市场应用开启授权")

    shop_brief = shop.brief()
    shop_brief["access_token"] = grant_record.access_token
    shop_brief["app"] = app_id
    return jsonify(shop=shop_brief)


@api.get("/shop/erp")
@shop_required
def get_shop_erp_info():
    erp_type = ""
    erp_infos = g.shop.erp_types()
    if len(erp_infos) > 0:
        erp_type = erp_infos[0].name

    return jsonify({"erp_type": erp_type})


def get_main_shop(credential: Credentials) -> MolaShops | None:
    """
    获取该授权对应的主店信息。
    """
    # 查询该租户下的所有 PowerGroups
    all_power_groups: list[PowerGroups] = PowerGroups.query.filter(PowerGroups.org_id == credential.org_id).all()

    if len(all_power_groups) == 0:
        return None

    # 查询所有 PowerGroups 对应的主店（主店判断逻辑是，在 MolaShops 中的 power_group 列不为空）
    all_power_group_ids: list[int] = [power_group.id for power_group in all_power_groups]

    main_shops: list[MolaShops] = (
        MolaShops.query.filter(MolaShops.power_group.in_(all_power_group_ids)).order_by(MolaShops.id.desc()).all()
    )

    if len(main_shops) == 0:
        return None

    # 一个租户下，应当一个 site 只有一家主店
    for main_shop in main_shops:
        if main_shop.site == AUTH_TYPE_TO_MOLA_SITE_MAP.get(credential.auth_type):
            return main_shop

    return None


def auth_bind_mola_shops(credential: Credentials, channels: list[GrantChannelInfo]) -> None:
    """
    同步 Mola-API 方面的信息。
    """
    # 查询是否已经建有主店
    main_mola_shop = get_main_shop(credential)
    if main_mola_shop is None:
        name = "ORG_{org_id}_{suffix}".format(
            org_id=credential.org_id,
            suffix=AUTH_TYPE_TO_ERP_TYPE_MAP[credential.auth_type].label,
        )
        power_groups = PowerGroups.find_or_create(name=name, org_id=credential.org_id)
        db.session.flush()
        power_groups_id = power_groups.id
    else:
        if main_mola_shop.power_group is None:
            return None
        else:
            power_groups_id = main_mola_shop.power_group

    # 查询已经关联了的记录
    related_shops: list[PowerGroupRelatedShops] = PowerGroupRelatedShops.query.filter(
        PowerGroupRelatedShops.power_group_id == power_groups_id,
    ).all()

    related_shop_ids: list[int] = [related_shop.shop_id for related_shop in related_shops]

    # 查询已经创建且关联过的 MolaShops。
    related_mola_shops: list[MolaShops] = MolaShops.query.filter(
        MolaShops.id.in_(related_shop_ids),
    ).all()

    exists_mola_shop_sid_and_sites: list[tuple[str, str]] = [
        (mola_shop.sid, mola_shop.site) for mola_shop in related_mola_shops
    ]

    for channel in channels:
        site = {
            "tmall": "taobao",
            "doudian": "douyin",
            "1688": "alibaba",
        }.get(channel.channel_type, channel.channel_type)
        sid_and_site = (channel.channel_no, site)
        if sid_and_site not in exists_mola_shop_sid_and_sites:
            # 如果该 sid 和 site 没有创建店铺并绑定过 PowerGroup，则新建店铺。
            ms = MolaShops(
                title=channel.channel_name,
                nickname=channel.channel_name,
                sid=channel.channel_no,
                site=site,
            )
            db.session.add(ms)
            db.session.flush()

            # 然后将店铺 id 绑定到该 PowerGroup 上.
            related_shop = PowerGroupRelatedShops(power_group_id=power_groups_id, shop_id=ms.id)
            db.session.add(related_shop)
            db.session.flush()

    db.session.commit()
    return None


class Page(BaseModel):
    page_num: int = 1
    page_size: int = 20
    total_pages: int = 0
    total_rows: int = 0

    def update_from_pagination(self, pagination: Pagination):
        self.page_num = pagination.page
        self.page_size = pagination.per_page
        self.total_pages = pagination.pages
        self.total_rows = pagination.total or 0


class GetOrgGrantRecordsResponse(BaseModel):
    class Data(BaseModel):
        page: Page = Field(default_factory=Page)
        page_data: list[dict] = Field(default_factory=list)

    success: bool = True
    code: int = 200
    msg: str = ""
    data: Data = Field(default_factory=Data)


@api.get("/org/grant_records")
@org_required
@validate
def get_org_grant_records(
    query: QueryGrantRecordParameters,
) -> GetOrgGrantRecordsResponse:
    """
    获取该租户下的所有授权信息。
    :return:
    """
    response = GetOrgGrantRecordsResponse()

    # 目前前端是分成两个接口来合并处理数据的。
    credentials_query: Query[Credentials] = Credentials.query.filter(
        Credentials.org_id == int(g.org_id),
        Credentials.auth_type.in_(AuthType.get_editable_auth_type_list()),
    )

    if query.auth_type:
        credentials_query = credentials_query.filter(Credentials.auth_type == query.auth_type)

    if query.auth_nick:
        credentials_query = credentials_query.filter(Credentials.auth_nick == query.auth_nick)

    pagination = credentials_query.paginate(page=query.page_num, per_page=query.page_size)
    response.data.page.update_from_pagination(pagination)
    if pagination.total == 0:
        return response
    credentials: list[Credentials] = pagination.items

    # 查询出这个租户下的所有店铺。
    kiosk_shops: list[KioskShop] = KioskShop.query.filter(KioskShop.org_id == int(g.org_id)).all()

    # 前端目前 bind_shops 传过来的数据格式是：“taobao:213123”，这种平台与 sid 拼接成的字符串，需要以此获取到店铺信息。
    platform_and_sid_to_kiosk_shop_map: dict[str, KioskShop] = {
        "{}:{}".format(kiosk_shop.platform, kiosk_shop.sid): kiosk_shop for kiosk_shop in kiosk_shops
    }
    channel_id_to_kiosk_shop_map: dict[int, KioskShop] = {kiosk_shop.id: kiosk_shop for kiosk_shop in kiosk_shops}

    credential_ids: list[int] = [c.id for c in credentials]
    auth_id_to_bind_shops_map: dict[int, list[dict]] = {}

    if query.bind_shops:
        channels = [
            platform_and_sid_to_kiosk_shop_map.get(i)
            for i in query.bind_shops
            if platform_and_sid_to_kiosk_shop_map.get(i) is not None
        ]
        grant_channel_ids = [c.id for c in channels if c is not None]
        csms: list[CredentialShopMapping] = CredentialShopMapping.query.filter(
            CredentialShopMapping.auth_id.in_(credential_ids),
            CredentialShopMapping.channel_id.in_(grant_channel_ids),
        ).all()
    else:
        # 查询授权与店铺的关联关系。
        csms = CredentialShopMapping.query.filter(CredentialShopMapping.auth_id.in_(credential_ids)).all()

    for csm in csms:
        bind_shops = auth_id_to_bind_shops_map.get(csm.auth_id) or []
        kiosk_shop = channel_id_to_kiosk_shop_map.get(csm.channel_id)
        if kiosk_shop is None:
            continue
        bind_shops = bind_shops + [
            {
                "erp_id": csm.erp_id,
                "erp_platform_sid": csm.erp_platform_sid,
                "channel_id": kiosk_shop.id,
                "channel_no": kiosk_shop.sid,
                "channel_type": kiosk_shop.platform,
                "channel_name": kiosk_shop.nick,
            }
        ]
        auth_id_to_bind_shops_map.update({csm.auth_id: bind_shops})

    now = int(time.time())
    for credential in credentials:
        # FIXME: 抖音爱图西只能查询到授权到期时间，需要和超级本要新的接口，拿准确的订购到期时间
        if credential.auth_type != AuthType.DOUYIN_ATX and credential.expire_timestamp:
            is_expire_soon = (now + app_config.AUTH_EXPIRES_DELTA) > credential.expire_timestamp
        else:
            is_expire_soon = False
        data = {
            "auth_id": credential.id,
            "auth_type": credential.auth_type,
            "auth_account": credential.auth_account,
            "auth_nick": credential.auth_nick,
            "auth_status": credential.auth_status,
            "available_status": credential.query_available_status(),
            "auth_extra_data": credential.auth_extra_data,
            "bind_shops": auth_id_to_bind_shops_map.get(credential.id) or [],
            "expire_timestamp": credential.expire_timestamp,
            "created_at": credential.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "is_expire_soon": is_expire_soon,
        }
        response.data.page_data.append(data)
    return response


@api.post("/org/grant_records")
@org_required
@validate
def create_org_grant_record(body: CreateGrantRecordRequest):
    """
    新增授权。
    :param body:
    :return:
    """
    org_id = int(g.org_id)
    if (
        AuthType.has_unique_limit(body.auth_type)
        and db.session.query(
            Credentials.query.filter(Credentials.org_id == org_id, Credentials.auth_type == body.auth_type).exists()
        ).scalar()
    ):
        return jsonify(success=False, message="已存在相同类型的授权")
    match Credentials.create(org_id, body.auth_type, body.auth_nick, body.meta, dry_run=False):
        case Ok(credential):
            return jsonify(success=True, auth_id=credential.id)
        case Err(error):
            return jsonify(success=False, message=str(error))


@api.get("/org/grant_records/<auth_id>")
@org_required
@validate
def get_single_grant_record(auth_id: int):
    org_id = int(g.org_id)
    # 目前前端是分成两个接口来合并处理数据的。
    if not (credential := db.session.get(Credentials, auth_id)):
        return jsonify(success=False, message="未找到授权信息")
    # 查询出这个租户下的所有店铺。
    kiosk_shops: list[KioskShop] = KioskShop.query.filter(KioskShop.org_id == org_id).all()
    channel_id_to_kiosk_shop_map: dict[int, KioskShop] = {kiosk_shop.id: kiosk_shop for kiosk_shop in kiosk_shops}

    auth_id_to_bind_shops_map: dict[int, list[dict]] = {}

    # 查询授权与店铺的关联关系。
    csms = CredentialShopMapping.query.filter(CredentialShopMapping.auth_id == credential.id).all()

    for csm in csms:
        bind_shops = auth_id_to_bind_shops_map.get(csm.auth_id) or []
        kiosk_shop = channel_id_to_kiosk_shop_map.get(csm.channel_id)
        if kiosk_shop is None:
            continue
        bind_shops = bind_shops + [
            {
                "erp_id": csm.erp_id,
                "erp_platform_sid": csm.erp_platform_sid,
                "channel_id": kiosk_shop.id,
                "channel_no": kiosk_shop.sid,
                "channel_type": kiosk_shop.platform,
                "channel_name": kiosk_shop.nick,
            }
        ]
        auth_id_to_bind_shops_map.update({csm.auth_id: bind_shops})
    now = int(time.time())
    # FIXME: 抖音爱图西只能查询到授权到期时间，需要和超级本要新的接口，拿准确的订购到期时间
    if credential.auth_type != AuthType.DOUYIN_ATX and credential.expire_timestamp:
        is_expire_soon = (now + app_config.AUTH_EXPIRES_DELTA) > credential.expire_timestamp
    else:
        is_expire_soon = False
    data = {
        "auth_id": credential.id,
        "auth_type": credential.auth_type,
        "auth_account": credential.auth_account,
        "auth_nick": credential.auth_nick,
        "auth_status": credential.auth_status,
        "available_status": credential.query_available_status(),
        "auth_extra_data": credential.auth_extra_data,
        "bind_shops": auth_id_to_bind_shops_map.get(credential.id) or [],
        "expire_timestamp": credential.expire_timestamp,
        "created_at": credential.created_at.strftime("%Y-%m-%d %H:%M:%S"),
        "is_expire_soon": is_expire_soon,
    }
    return jsonify(success=True, code=200, msg="", data=data)


@api.put("/org/grant_records/<auth_id>")
@org_required
@validate
def org_grant_record_bind_shops(auth_id: int, body: UpdateGrantRecordRequest):
    """
    授权绑定店铺。
    :param auth_id:
    :param body:
    :return:
    """
    if not (credential := db.session.get(Credentials, auth_id)):
        return jsonify(success=False, message="未找到授权信息")
    erp_id: int | None = None
    org_erp_info: OrgErpInfo | None = None
    if body.channel is None:
        return jsonify(success=True, auth_id=auth_id, erp_id=erp_id)

    # 如果 channel 列表不为空，则将授权的店铺进行更新。
    grant_channel_map: dict[int, GrantChannelInfo] = {c.channel_id: c for c in body.channel}

    match credential.auth_type:
        case (
            AuthType.JST
            | AuthType.WDT
            | AuthType.WDTULTI
            | AuthType.KUAIMAI
            | AuthType.WANLINIU
            | AuthType.WDGJ
            | AuthType.GUANYIYUN
            | AuthType.JACKYUN
            | AuthType.BAISHENG
            | AuthType.SF
        ):
            bind_org_erp_info = credential.get_bind_erp_info()
            if bind_org_erp_info is None:
                bind_org_erp_info = OrgErpInfo.create_by_credential(credential)
                bind_org_erp_info.produce_kafka_org_erp_create()
            erp_id = bind_org_erp_info.id
            org_erp_info = bind_org_erp_info

        case AuthType.FS_RPA:
            for channel in body.channel:
                if db.session.query(
                    RpaThirdShop.query.filter_by(org_id=int(g.org_id), shop_name=channel.channel_name).exists()
                ).scalar():
                    continue
                third_shop = RpaThirdShop(org_id=int(g.org_id), shop_name=channel.channel_name)
                db.session.add(third_shop)
                db.session.commit()
            auth_bind_kiosk_channels({c.channel_id: c for c in body.channel}, credential)
            return jsonify(success=True, auth_id=auth_id, erp_id=erp_id)
        case AuthType.YTO | AuthType.ZTO | AuthType.EMS | AuthType.JDL:
            auth_bind_kiosk_channels({c.channel_id: c for c in body.channel}, credential)
            return jsonify(success=True, auth_id=auth_id, erp_id=erp_id)

        case AuthType.KS_LFX | AuthType.JD_YZ | AuthType.JD_FS | AuthType.XHS_ERP:
            auth_bind_kiosk_channels({c.channel_id: c for c in body.channel}, credential)
            return jsonify(success=True, auth_id=auth_id, erp_id=erp_id)
        case _:
            return jsonify(success=False, message=f"暂不支持的授权类型 {credential.auth_type}")

    assert org_erp_info
    # kiosk 方面的绑定
    auth_bind_kiosk_channels(grant_channel_map, credential, org_erp_info)
    # robot 方面的绑定(只有 ERP 需要)
    auth_bind_shops(grant_channel_map, credential, org_erp_info)

    # mola-api 方面的绑定(只有万里牛 ERP 需要)
    if AUTH_TYPE_TO_MOLA_SITE_MAP.get(credential.auth_type):
        auth_bind_mola_shops(credential, body.channel)

    return jsonify(success=True, auth_id=auth_id, erp_id=erp_id)


def auth_bind_kiosk_channels(
    grant_channel_map: dict[int, GrantChannelInfo],
    credential: Credentials,
    org_erp_info: OrgErpInfo | None = None,
) -> None:
    # 查询绑定了该 ERP ID 的所有关联店铺。
    csms: list[CredentialShopMapping] = CredentialShopMapping.query.filter(
        CredentialShopMapping.auth_id == credential.id,
    ).all()
    channel_id_to_csm_map: dict[int, CredentialShopMapping] = {csm.channel_id: csm for csm in csms}

    # 查询该租户下的所有店铺。
    kiosk_shops: list[KioskShop] = KioskShop.query.filter(KioskShop.org_id == credential.org_id).all()
    for kiosk_shop in kiosk_shops:
        credential_shop_mapping = channel_id_to_csm_map.get(kiosk_shop.id)
        if grant_channel := grant_channel_map.get(kiosk_shop.id):
            if credential_shop_mapping is None:
                credential_shop_mapping = CredentialShopMapping(
                    channel_id=kiosk_shop.id,
                    auth_id=credential.id,
                )
                if org_erp_info is not None:
                    credential_shop_mapping.erp_id = org_erp_info.id
                if grant_channel.erp_platform_sid is not None:
                    credential_shop_mapping.erp_platform_sid = grant_channel.erp_platform_sid
                db.session.add(credential_shop_mapping)
            else:
                if grant_channel.erp_platform_sid:
                    credential_shop_mapping.erp_platform_sid = grant_channel.erp_platform_sid
            if org_erp_info is not None:
                org_erp_info.produce_kafka_shop_erp_create(kiosk_shop)
        else:
            if credential_shop_mapping is not None:
                if org_erp_info is not None:
                    org_erp_info.produce_kafka_shop_erp_delete(kiosk_shop)
                db.session.delete(credential_shop_mapping)
    db.session.commit()


def auth_bind_shops(
    grant_channel_map: dict[int, GrantChannelInfo],
    credential: Credentials,
    org_erp_info: OrgErpInfo,
) -> None:
    shops: list[Shop] = Shop.query.filter(Shop.org_id == g.org_id).all()
    shop_ids: list[int] = [shop.id for shop in shops]

    erp_infos: list[ErpInfo] = ErpInfo.query.filter(
        ErpInfo.erp_type == credential.auth_type, ErpInfo.shop_id.in_(shop_ids)
    ).all()
    shop_id_to_erp_info_map: dict[int, ErpInfo] = {
        erp_info.shop_id: erp_info for erp_info in erp_infos if erp_info.shop_id is not None
    }

    for shop in shops:
        erp_info = shop_id_to_erp_info_map.get(shop.id)
        if shop.channel_id in grant_channel_map.keys():
            if org_erp_info is not None and erp_info is None:
                ei = ErpInfo(
                    shop_id=shop.id,
                    erp_type=org_erp_info.erp_type,
                    nick=shop.nick,
                    meta=json.loads(org_erp_info.meta),
                    shop_site=platform_to_shop_site_map.get(shop.platform) or "",
                )
                db.session.add(ei)
        else:
            if erp_info is not None:
                db.session.delete(erp_info)
    db.session.commit()


@api.patch("/org/grant_records/<auth_id>")
@org_required
@validate
def update_org_grant_record(auth_id: int, body: CreateGrantRecordRequest):
    org_id = int(g.org_id)
    erp_id: int | None = None
    if not (credential := db.session.get(Credentials, auth_id)):
        return jsonify(success=False, message="未找到授权信息")
    match credential.update_auth_extra_data(body.meta):
        case Err(error):
            return jsonify(success=False, message=str(error))
    match credential.auth_type:
        case (
            AuthType.JST
            | AuthType.WDT
            | AuthType.WDTULTI
            | AuthType.KUAIMAI
            | AuthType.WANLINIU
            | AuthType.WDGJ
            | AuthType.GUANYIYUN
            | AuthType.JACKYUN
            | AuthType.BAISHENG
        ):
            kiosk_shops = (
                KioskShop.query.join(
                    CredentialShopMapping,
                    CredentialShopMapping.channel_id == KioskShop.id,
                )
                .filter(KioskShop.org_id == org_id, CredentialShopMapping.auth_id == auth_id)
                .all()
            )
            if org_erp_info := credential.get_bind_erp_info():
                org_erp_info.modify_by_credential(credential)
                org_erp_info.produce_kafka_org_erp_modify()
                for kiosk_shop in kiosk_shops:
                    org_erp_info.produce_kafka_shop_erp_modify(kiosk_shop)
            else:
                org_erp_info = OrgErpInfo.create_by_credential(credential)
                org_erp_info.produce_kafka_org_erp_create()
                for kiosk_shop in kiosk_shops:
                    org_erp_info.produce_kafka_shop_erp_create(kiosk_shop)
            erp_id = org_erp_info.id
            erp_info_list: list[ErpInfo] = (
                ErpInfo.query.join(ErpInfo.shop)
                .filter(
                    ErpInfo.erp_type == org_erp_info.erp_type,
                    Shop.org_id == g.org_id,
                    Shop.channel_id.in_([kiosk_shop.id for kiosk_shop in kiosk_shops]),
                )
                .all()
            )
            for erp_info in erp_info_list:
                erp_info.meta = json.loads(org_erp_info.meta)
    db.session.commit()
    return jsonify(success=True, auth_id=auth_id, erp_id=erp_id)


@api.patch("/org/grant_records/<auth_id>/status")
@org_required
@validate
def refresh_org_grant_record(auth_id: int):
    if not (credential := db.session.get(Credentials, auth_id)):
        return jsonify(success=False, message="未找到授权信息")
    match credential.auth_type:
        case AuthType.KUAIMAI:
            kuaimai_client = KuaimaiOpenPlatformAPIClient(credential.auth_extra_data)
            try:
                km_refresh_session_response = kuaimai_client.refresh_session(credential.auth_extra_data["refreshToken"])
            except KMException as e:
                return jsonify(success=False, message=str(e))
            credential.expire_timestamp = int(time.time()) + km_refresh_session_response.session.expires_in
    db.session.commit()
    return jsonify(success=True, auth_id=auth_id)


@api.delete("/org/grant_records/<auth_id>")
@org_required
@validate
def remove_org_grant_record(auth_id: int):
    if not (credential := db.session.get(Credentials, auth_id)):
        return jsonify(success=False, message="未找到授权信息")
    if credential.auth_type not in AuthType.get_editable_auth_type_list():
        return jsonify(success=False, message="暂不支持的授权类型")
    kiosk_shops: list[KioskShop] = (
        KioskShop.query.join(CredentialShopMapping, CredentialShopMapping.channel_id == KioskShop.id)
        .filter(KioskShop.org_id == int(g.org_id), CredentialShopMapping.auth_id == auth_id)
        .all()
    )
    if org_erp_info := credential.get_bind_erp_info():
        for kiosk_shop in kiosk_shops:
            org_erp_info.produce_kafka_shop_erp_delete(kiosk_shop)
        org_erp_info.produce_kafka_org_erp_delete()
        db.session.delete(org_erp_info)
        erp_info_list: list[ErpInfo] = (
            ErpInfo.query.join(ErpInfo.shop)
            .filter(
                ErpInfo.erp_type == org_erp_info.erp_type,
                Shop.org_id == g.org_id,
                Shop.channel_id.in_([kiosk_shop.id for kiosk_shop in kiosk_shops]),
            )
            .all()
        )
        for erp_info in erp_info_list:
            db.session.delete(erp_info)
    credential_shop_mappings: list[CredentialShopMapping] = CredentialShopMapping.query.filter(
        CredentialShopMapping.channel_id.in_([channel.id for channel in kiosk_shops]),
        CredentialShopMapping.auth_id == auth_id,
    ).all()
    for credential_shop_mapping in credential_shop_mappings:
        db.session.delete(credential_shop_mapping)
    db.session.delete(credential)
    db.session.commit()
    return jsonify(success=True)


class UpdateThirdShopsBody(BaseModel):
    shop_name: str


@api.post("/org/grant_records/<auth_id>/third_shops")
@org_required
@validate
def update_third_shops(auth_id: int, body: UpdateThirdShopsBody):
    if not (credentials := db.session.get(Credentials, auth_id)):
        return jsonify(success=False, message="未找到授权记录")
    match credentials.auth_type:
        case AuthType.FS_RPA:
            if db.session.query(
                RpaThirdShop.query.filter_by(org_id=int(g.org_id), shop_name=body.shop_name).exists()
            ).scalar():
                return jsonify(success=False, message=f"{body.shop_name} 已存在，不可重复创建")
            third_shop = RpaThirdShop(org_id=int(g.org_id), shop_name=body.shop_name)
            db.session.add(third_shop)
            db.session.commit()

            return jsonify(success=True, shop=third_shop.to_response())
        case _:
            return jsonify(success=False, message="暂不支持这类授权")


@api.get("/org/grant_records/<auth_id>/third_shops")
@org_required
def get_third_shops(auth_id: int):
    credentials = db.session.get(Credentials, auth_id)
    if not credentials:
        return jsonify(success=False, message="未找到授权记录")
    try:
        match credentials.auth_type:
            case AuthType.WDT:
                wdt_client = WDTOpenPlatformAPIClient(credentials.auth_extra_data)
                wdt_shops = wdt_client.get_shops()
                wdt_platform_id_to_name_map: dict[str, str] = json.loads(app_config.WDT_PLATFORM_ID_TO_NAME_JSON)
                return jsonify(
                    success=True,
                    shops=[
                        {
                            "shop_id": shop.shop_id,
                            "shop_name": shop.shop_name,
                            "shop_no": shop.shop_no,
                            "platform": wdt_platform_id_to_name_map.get(shop.platform_id),
                        }
                        for shop in wdt_shops
                    ],
                )

            case AuthType.WDTULTI:
                wdtulti_client = WdtUltiOpenPlatformAPIClient(credentials.auth_extra_data)
                wdtulti_shops = wdtulti_client.get_shops()
                wdtulti_platform_id_to_name_map: dict[str, str] = json.loads(
                    app_config.WDT_ULTI_PLATFORM_ID_AND_SUB_PLATFORM_ID_TO_NAME_JSON
                )
                return jsonify(
                    success=True,
                    shops=[
                        {
                            "shop_id": shop.shop_id,
                            "shop_name": shop.shop_name,
                            "shop_no": shop.shop_no,
                            "platform": wdtulti_platform_id_to_name_map.get(
                                "{}_{}".format(shop.platform_id, shop.sub_platform_id)
                            ),
                        }
                        for shop in wdtulti_shops
                    ],
                )

            case AuthType.WANLINIU:
                # todo：万里牛的数据目前没办法用于绑定 erp_platform_sid。
                wln_client = WlnClient(credentials.auth_extra_data)
                wln_shops = wln_client.get_shops()
                return jsonify(
                    success=True,
                    shops=[
                        {
                            "shop_id": shop.shop_uid,
                            "shop_name": shop.shop_name,
                            "shop_no": shop.shop_nick,
                        }
                        for shop in wln_shops
                    ],
                )

            case AuthType.KUAIMAI:
                kuaimai_client = KuaimaiOpenPlatformAPIClient(credentials.auth_extra_data)
                kuaimai_shops = kuaimai_client.get_shops()
                km_source_to_platform_name_map: dict[str, str] = json.loads(app_config.KM_SOURCE_TO_PLATFORM_JSON)
                return jsonify(
                    success=True,
                    shops=[
                        {
                            "shop_id": shop.shop_id,
                            "shop_name": shop.title,
                            "shop_no": shop.user_id,
                            "platform": (km_source_to_platform_name_map.get(shop.source) if shop.source else None),
                        }
                        for shop in kuaimai_shops
                    ],
                )

            case AuthType.WDGJ:
                wdgj_client = WDGJOpenPlatformAPIClient(credentials.auth_extra_data)
                wdgj_shops = wdgj_client.get_shops()
                return jsonify(
                    success=True,
                    shops=[
                        {
                            "shop_id": shop.shopid,
                            "shop_name": shop.shopname,
                            "shop_no": shop.shopno,
                            "platform": shop.shoptype,
                        }
                        for shop in wdgj_shops
                    ],
                )

            case AuthType.GUANYIYUN:
                gyy_client = GyyOpenPlatformAPIClient(credentials.auth_extra_data)
                gyy_shops = gyy_client.get_shops()
                return jsonify(
                    success=True,
                    shops=[
                        {
                            "shop_id": shop.id,
                            "shop_name": shop.nick,
                            "shop_no": shop.code,
                            "platform": shop.type_name,
                        }
                        for shop in gyy_shops
                    ],
                )

            case AuthType.JACKYUN:
                jackyun_client = JackyunOpenPlatformAPIClient(credentials.auth_extra_data)
                jackyun_shops = jackyun_client.get_shops()
                return jsonify(
                    success=True,
                    shops=[
                        {
                            "shop_id": shop.channelId,
                            "shop_name": shop.channelName,
                            "shop_no": shop.channelCode,
                            "platform": shop.onlinePlatTypeName,
                        }
                        for shop in jackyun_shops
                    ],
                )

            case AuthType.BAISHENG:
                baisheng_client = BaiShengOpenPlatformAPIClient(credentials.auth_extra_data)
                baisheng_shops = baisheng_client.get_shops()
                return jsonify(
                    success=True,
                    shops=[
                        {
                            "shop_id": shop.id,
                            "shop_name": shop.khmc,
                            "shop_no": shop.khdm,
                        }
                        for shop in baisheng_shops
                    ],
                )

            case AuthType.FS_RPA:
                fs_rpa_shops: list[RpaThirdShop] = RpaThirdShop.query.filter_by(org_id=int(g.org_id)).all()
                return jsonify(success=True, shops=[shop.to_response() for shop in fs_rpa_shops])

            case _:
                return jsonify(success=False, message="暂不支持这类授权")

    except Exception as e:
        logger.error("查询 erp 店铺时出错：{}", e)
        return jsonify(success=False, message="查询 erp 店铺时出错")


@api.patch("/org/grant_records")
@org_required
@validate
def check_grant_record_is_valid(body: CheckGrantRecordRequest):
    try:
        dummy = Credentials(auth_type=body.auth_type, auth_extra_data=body.meta)
        is_valid = dummy.get_authorized_erp_client().check_grant_is_valid()
        return jsonify(success=True, is_valid=is_valid)
    except:  # noqa
        return jsonify(success=True, is_valid=False)
