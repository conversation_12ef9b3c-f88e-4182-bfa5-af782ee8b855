import time
from datetime import datetime
from enum import StrEnum
from typing import TYPE_CHECKING
from typing import Any
from typing import List
from typing import Optional
from typing import Tuple

import jwt
import sqlalchemy as sa
from loguru import logger
from result import Err
from result import Ok
from robot_extension.util.decorator import classproperty
from robot_extension.util.mixin import LabeledEnum
from sqlalchemy.orm import DynamicMapped
from sqlalchemy.orm import Load
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import Query
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship

from robot_processor.db import BasicMixin
from robot_processor.db import DbBaseModel
from robot_processor.enums import AuthType
from robot_processor.enums import ErpType
from robot_processor.enums import ProductCode
from robot_processor.enums import ShopStatus
from robot_processor.ext import db
from robot_processor.utils import unwrap_optional
from rpa.conf import rpa_config

if TYPE_CHECKING:
    from robot_processor.client.logistics_clients.enum import LogisticsType
    from robot_processor.form.models import FormShop


class App(StrEnum):
    FEISUO = "feisuo"  # 飞梭商家后台
    QN_SIDEBAR = "qn-sidebar"  # 千牛新版侧边栏
    PIGEON = "pigeon"  # 飞鸽侧边栏工单
    DOUDIAN_XYZ = "doudian-xyz"  # 小柚子
    PDD_ERP = "pdd-erp"  # 拼多多 ERP

    @property
    def app_key(self):
        from robot_processor.client import app_config
        from rpa.conf import rpa_config

        return {
            App.FEISUO: app_config.TAOBAO_APP_KEY,
            App.QN_SIDEBAR: app_config.TAOBAO_MIX_NICK_APP_KEY,
            App.PIGEON: rpa_config.DOUDIAN_OPEN_API_APP_KEY,
            App.DOUDIAN_XYZ: rpa_config.DOUDIAN_XYZ_APP_KEY,
        }[self]

    @property
    def taobao_client(self):
        from robot_processor import client

        return {
            App.FEISUO: client.taobao_client,
            App.QN_SIDEBAR: client.sidebar_taobao_client,
        }[self]

    @property
    def doudian_openapi_client(self):
        from rpa.doudian import doudian_openapi_doudian_xyz_client
        from rpa.doudian import doudian_openapi_pigeon_client

        return {
            App.PIGEON: doudian_openapi_pigeon_client,
            App.DOUDIAN_XYZ: doudian_openapi_doudian_xyz_client,
        }[self]


class MiniAppTemplate(DbBaseModel, BasicMixin):
    """淘系小程序模板的实例化应用信息

    店铺的小程序模板和授权无关，可以使用店铺的任意 access_token 进行模板的实例化/升级
    买家自助服务台是小程序模板的业务使用场景，会通过 app_id 来提供买家自助服务台请求和飞梭店铺之间的关联关系
    """

    created_at: Mapped[datetime] = mapped_column(sa.DateTime, default=datetime.now)  # type: ignore[assignment]
    updated_at: Mapped[datetime] = mapped_column(  # type: ignore[assignment]
        sa.DateTime, default=datetime.now, onupdate=datetime.now
    )
    shop_id: Mapped[int] = mapped_column(sa.Integer, sa.ForeignKey("shop.id"))
    app_id: Mapped[str] = mapped_column(sa.String(32), index=True)
    app_version: Mapped[str] = mapped_column(sa.String(32))

    @classmethod
    def get_by_shop(cls, shop_id: int) -> Optional["MiniAppTemplate"]:
        # 为了兼容历史数据，一个 shop_id 会对应多个 app_id, 但是后续提供出去的 app_id 需要是固定的
        return cls.query.filter_by(shop_id=shop_id).order_by(cls.id.asc()).first()


class GrantRecord(DbBaseModel, BasicMixin):
    shop_id: Mapped[int | None] = mapped_column(sa.Integer, sa.ForeignKey("shop.id"))
    # 现在飞梭会同时存储多个淘宝应用的授权信息，需要区分不同应用的授权信息
    app: Mapped[App] = mapped_column(
        "app_key",
        sa.Enum(App, native_enum=False, length=32),
        nullable=False,
        default=App.FEISUO,
    )
    expires_at_ms: Mapped[int | None] = mapped_column(sa.BigInteger)
    access_token: Mapped[str] = mapped_column(sa.Text)
    refresh_token: Mapped[str | None] = mapped_column(sa.Text)

    def get_bind_shop(self) -> Optional["Shop"]:
        return Shop.query.filter(Shop.id == self.shop_id).first()

    @property
    def taobao_client(self):
        return self.app.taobao_client

    @classmethod
    def query_if_exists(cls, shop_id: int, access_token: str) -> bool:
        return db.session.query(
            cls.query.filter(cls.shop_id == shop_id, cls.access_token == access_token).exists()
        ).scalar()


class TaobaoSubuserGrantRecord(DbBaseModel, BasicMixin):
    __table_args__ = (sa.Index("ix_sid", "sid"),)
    sid: Mapped[str] = mapped_column(sa.String(32), nullable=False, comment="淘宝店铺ID")
    app: Mapped[App] = mapped_column(sa.Enum(App, native_enum=False, length=32), nullable=False)
    access_token: Mapped[str] = mapped_column(sa.Text, nullable=False, comment="子账号授权token")
    nick: Mapped[str] = mapped_column(sa.String(64), nullable=False, comment="子账号昵称（冗余信息）")
    open_uid: Mapped[str | None] = mapped_column(sa.String(64))
    extra_data: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="额外数据")

    @property
    def taobao_client(self):
        return self.app.taobao_client

    @classmethod
    def get_by_open_uid(cls, app: App, open_uid: str) -> Optional["TaobaoSubuserGrantRecord"]:
        return cls.query.filter(cls.app == app, cls.open_uid == open_uid).first()

    @classmethod
    def get_by_sid(cls, sid: str, app: App | None = None) -> list["TaobaoSubuserGrantRecord"]:
        query = cls.query.filter(cls.sid == sid)
        if app:
            query = query.filter(cls.app == app)
        return query.all()


class ContractInfo(DbBaseModel, BasicMixin):
    org_id: Mapped[str | None] = mapped_column(sa.String(32), index=True, unique=True)
    end_ts: Mapped[int | None] = mapped_column(sa.Integer, index=True)
    product_code: Mapped[ProductCode] = mapped_column(sa.Enum(ProductCode), default=ProductCode.FS_001)
    master_cid: Mapped[str | None] = mapped_column(sa.String(32), index=True)

    @property
    def codes(self) -> List[int]:
        return list(self.product_code.codes)

    @property
    def is_expired(self) -> bool:
        return self.end_ts < int(time.time())  # type: ignore[operator]


class SmartCallInfo(DbBaseModel, BasicMixin):
    __ignore_columns__ = ["shop"]

    shop_id: Mapped[int | None] = mapped_column(sa.Integer, sa.ForeignKey("shop.id"))
    nick: Mapped[str | None] = mapped_column(sa.String(64), comment="店铺主账号")
    platform: Mapped[str | None] = mapped_column(sa.String(64), comment="店铺平台类型")

    shop: Mapped["Shop"] = relationship(
        "Shop",
        back_populates="smartcalls",
        primaryjoin="foreign(SmartCallInfo.shop_id)==Shop.id",
    )
    meta: Mapped[dict] = mapped_column(sa.JSON, default=dict)


class ErpInfo(DbBaseModel, BasicMixin):
    __ignore_columns__ = ["shop"]

    shop_id: Mapped[int | None] = mapped_column(sa.Integer, sa.ForeignKey("shop.id"))
    erp_type: Mapped[ErpType | None] = mapped_column(sa.Enum(ErpType))
    token: Mapped[str | None] = mapped_column(sa.Text)
    nick: Mapped[str | None] = mapped_column(sa.String(64), comment="erp的主账号")

    shop: Mapped["Shop"] = relationship("Shop", back_populates="erps", primaryjoin="foreign(ErpInfo.shop_id)==Shop.id")
    meta: Mapped[dict] = mapped_column(sa.JSON, default=dict)
    shop_site: Mapped[str | None] = mapped_column(sa.String(64), comment="erp的主账号店铺类型")
    erp_id: Mapped[int | None] = mapped_column(sa.Integer, comment="kiosk的erp_id")

    @classmethod
    def get_by_erp_id(cls, erp_id: int):
        return cls.query.join(cls.shop).filter(ErpInfo.erp_id == erp_id).first()

    @classmethod
    def get_by_sid(cls, sid: str, erp_type=ErpType.JST):
        return (
            cls.query.join(cls.shop)
            .filter(ErpInfo.erp_type == erp_type, Shop.sid == sid)
            .order_by(ErpInfo.id.desc())
            .first()
        )

    @classmethod
    def get_all_erp_by_sid(cls, sid: str):
        return cls.query.join(cls.shop).filter(Shop.sid == sid).all()

    @classmethod
    # FIXME(<EMAIL>): 最新的erp不能保证是一个店铺目前在关联的erp，应该保证一个店铺只有一个erp被关联.
    def get_newest_erp_by_sid(cls, sid: str):
        """
        Args:
            sid (str):

        Returns:
            ErpInfo | None:
        """
        return cls.query.join(cls.shop).filter(Shop.sid == sid).order_by(ErpInfo.id.desc()).first()

    @classmethod
    def get_distinct_erp_by_org_id(cls, org_id) -> list["ErpInfo"]:
        from sqlalchemy import func
        from sqlalchemy import select
        from sqlalchemy.sql.operators import eq, ne

        erp_info_ids = db.session.scalars(
            select(func.max(cls.id))
            .select_from(cls)
            .join(cls.shop)
            .where(eq(Shop.org_id, str(org_id)))
            .where(ne(cls.erp_id, None))
            .group_by(cls.erp_id)
        ).all()
        return cls.query.filter(cls.id.in_(erp_info_ids)).all()

    def get_erp_account(self) -> Ok[str] | Err[Exception]:
        try:
            match self.erp_type:
                case ErpType.WDT | ErpType.WDTULTI:
                    return Ok(self.meta["sid"])
                case ErpType.JST:
                    return Ok(self.meta["co_id"])
                case _:
                    return Err(NotImplementedError(f"unsupported erp_type {unwrap_optional(self.erp_type).name}"))

        except KeyError as e:
            return Err(e)

    def get_credential(self):
        from robot_types.model import credential

        match self.erp_type:
            case ErpType.JST:
                if self.meta.get("auth_type") == "isv":
                    partner_id = rpa_config.JST_ISV_APP_KEY
                    partner_key = rpa_config.JST_ISV_APP_SECRET
                else:
                    partner_id = rpa_config.JST_PARTNER_ID
                    partner_key = rpa_config.JST_PARTNER_KEY
                return Ok(
                    credential.JST(
                        token=self.meta["token"],
                        co_id=self.meta["co_id"],
                        partner_id=partner_id,
                        partner_key=partner_key,
                    )
                )
            case ErpType.WDT:
                return Ok(
                    credential.WDT(
                        sid=self.meta["sid"],
                        after_sale_shop_no=self.meta["after_sale_shop_no"],
                        app_key=self.meta["app_key"],
                        app_secret=self.meta["app_secret"],
                    )
                )
            case ErpType.JACKYUN:
                return Ok(
                    credential.Jackyun(
                        app_key=self.meta["app_key"],
                        app_secret=self.meta["app_secret"],
                        customer_id=self.meta["customer_id"],
                        erp_account=self.meta["erp_account"],
                        app_token=self.meta["app_token"],
                    )
                )
        return Err(NotImplementedError(f"未支持的 erp类型: {self.erp_type}"))


def _resolve_form_model():
    from robot_processor.form.models import Form

    return Form


def _resolve_form_shop_model():
    from robot_processor.form.models import FormShop

    return FormShop


class Shop(DbBaseModel, BasicMixin):
    title: Mapped[str | None] = mapped_column(sa.String(128))
    org_id: Mapped[str | None] = mapped_column(sa.String(32), index=True)
    channel_id: Mapped[int | None] = mapped_column(sa.Integer, unique=True)
    sid: Mapped[str] = mapped_column(sa.String(32), index=True)
    seller_id: Mapped[str | None] = mapped_column(sa.String(32))
    nick: Mapped[str] = mapped_column(sa.String(64))
    # 主账号唯一ID
    open_id: Mapped[str | None] = mapped_column(sa.String(128), unique=True)

    records: DynamicMapped[GrantRecord] = relationship(GrantRecord, lazy="dynamic", order_by="GrantRecord.id.desc()")

    smartcalls: DynamicMapped[SmartCallInfo] = relationship(
        SmartCallInfo,
        back_populates="shop",
        lazy="dynamic",
        primaryjoin="foreign(SmartCallInfo.shop_id)==Shop.id",
    )
    eager_erps: Mapped[list[ErpInfo]] = relationship(ErpInfo, back_populates="shop", viewonly=True)
    erps: DynamicMapped[ErpInfo] = relationship(
        ErpInfo,
        back_populates="shop",
        lazy="dynamic",
        primaryjoin="foreign(ErpInfo.shop_id)==Shop.id",
    )

    shop_forms: DynamicMapped["FormShop"] = relationship(
        _resolve_form_shop_model, lazy="dynamic", back_populates="shop"
    )  # noqa: E501

    platform: Mapped[str] = mapped_column(sa.String(64))
    # 特殊逻辑处理; 如大小写；简繁体
    features: Mapped[dict] = mapped_column(sa.JSON, default=dict)
    status: Mapped[ShopStatus] = mapped_column(sa.Enum(ShopStatus), comment="店铺状态", default=ShopStatus.ENABLE)
    deleted: Mapped[bool] = mapped_column(sa.Boolean, default=False)

    @property
    def history_nick_list(self):
        if not isinstance(self.features, dict):
            return []  # type: ignore[unreachable]
        if "history_nick" not in self.features:
            return []
        return self.features["history_nick"]

    class Platform(StrEnum):
        TAOBAO = "TAOBAO"
        TMALL = "TMALL"
        DOUDIAN = "DOUDIAN"
        PDD = "PDD"
        KUAISHOU = "KUAISHOU"
        BEIDIAN = "BEIDIAN"
        ALIBABA = "ALIBABA"
        SUNING = "SUNING"
        XIAOHONGSHU = "XIAOHONGSHU"
        MOGUJIE = "MOGUJIE"
        WEIMOB = "WEIMOB"
        YOUZAN = "YOUZAN"
        VIPSHOP = "VIPSHOP"
        KAOLA = "KAOLA"
        JD = "JD"
        MINIWECHAT = "MINIWECHAT"
        WECHAT_CHANNELS = "WECHAT_CHANNELS"

    def set_org_id(self, value):
        if value:
            self.org_id = value

    def set_open_id(self, value):
        if value:
            self.open_id = value

    def set_access_token(self, value):
        if not value:
            return
        self.records.append(GrantRecord(access_token=value))

    def get_mini_app_template_app_id(self):
        template = MiniAppTemplate.get_by_shop(self.id)
        if template:
            return template.app_id
        return None

    def get_recent_record(self, app: App = App.FEISUO) -> Optional[GrantRecord]:
        return self.records.filter(GrantRecord.app == app).first()

    def get_pigeon_grant_record(self) -> GrantRecord | None:
        """
        获取店铺的飞鸽授权。
        :return:
        """
        return self.records.filter(GrantRecord.app == App.PIGEON).first()

    def get_doudian_xyz_grant_record(self) -> GrantRecord | None:
        """
        获取店铺的抖店小柚子授权。
        :return:
        """
        return self.records.filter(GrantRecord.app == App.DOUDIAN_XYZ).first()

    def get_pdd_erp_grant_record(self) -> GrantRecord | None:
        """
        获取店铺的拼多多 ERP 授权。
        :return:
        """
        return self.records.filter(GrantRecord.app == App.PDD_ERP).first()

    def get_recent_subuser_record(
        self, nick: str | None = None, app: App | None = None
    ) -> TaobaoSubuserGrantRecord | None:
        """获取淘系店铺最近的子账号授权记录

        Args:
            nick: (可选)使用指定子账号的授权记录
            app: 授权应用

        Returns:
            TaobaoSubuserGrantRecord: 返回最近的子账号授权记录
        """
        query = db.session.query(TaobaoSubuserGrantRecord).where(
            TaobaoSubuserGrantRecord.sid == self.sid,
        )
        if nick:
            query = query.where(TaobaoSubuserGrantRecord.nick == nick)
        if app:
            query = query.filter(TaobaoSubuserGrantRecord.app == app)
        query = query.order_by(TaobaoSubuserGrantRecord.id.desc())

        return query.first()

    def get_access_token(self, auth_type: AuthType | None = None):
        from robot_processor.shop.auth_manager import Credentials
        from robot_processor.shop.schema import SuperBookGrantMeta

        if auth_type is None:  # 部分店铺平台可以不指定授权类型
            match self.platform:
                case Shop.Platform.KUAISHOU:
                    auth_type = AuthType.KS_LFX

        match auth_type:
            case AuthType.KS_LFX | AuthType.JD_YZ | AuthType.JD_FS | AuthType.XHS_ERP:
                credential = Credentials.get_by_shop_auth_type(self, auth_type)
                if credential:
                    super_book_grant__meta = SuperBookGrantMeta.validate(credential.auth_extra_data)
                    return super_book_grant__meta.accessToken
        return None

    def get_logistic_grant_records(self, logistic_type: "LogisticsType") -> list[dict]:
        """
        获取该店铺绑定的物流授权。
        """
        from robot_processor.shop.auth_manager import Credentials
        from robot_processor.shop.auth_manager import CredentialShopMapping
        from robot_processor.shop.schema import LOGISTIC_TYPE_TO_AUTH_TYPE_MAP

        auth_type = LOGISTIC_TYPE_TO_AUTH_TYPE_MAP[logistic_type]
        credentials: list[Credentials] = (
            Credentials.query.join(CredentialShopMapping, CredentialShopMapping.auth_id == Credentials.id)
            .filter(CredentialShopMapping.channel_id == self.channel_id)
            .filter(Credentials.auth_type == auth_type)
            .all()
        )
        return [c.auth_extra_data for c in credentials]

    def get_org_logistic_grant_records(self, logistics_type: "LogisticsType") -> list[dict]:
        """获取租户已绑定的物流授权"""
        from robot_processor.shop.auth_manager import Credentials
        from robot_processor.shop.schema import LOGISTIC_TYPE_TO_AUTH_TYPE_MAP

        auth_type = LOGISTIC_TYPE_TO_AUTH_TYPE_MAP[logistics_type]
        credentials = Credentials.query.filter(
            Credentials.auth_type == auth_type,
            Credentials.org_id == self.org_id,
        ).all()
        return [c.auth_extra_data for c in credentials]

    def get_refund_agree_accounts(self) -> list[str] | None:
        """获取店铺用于做退款同意的子账号列表配置"""
        from robot_processor.shop.kiosk_models import KioskShop

        if not self.channel_id:
            return None
        if self.platform not in ["TAOBAO", "TMALL"]:
            return None
        kiosk_shop = db.session.query(KioskShop).filter_by(id=self.channel_id).one()
        accounts = kiosk_shop.refund_agree_accounts
        if accounts:
            accounts = [account for account in accounts if account != "null"]
        return accounts

    @classmethod
    def get_doudian_shop_by_sid(cls, sid):
        """根据 sid 获取抖店店铺

        Args:
            sid (str): 店铺sid
        Returns:
            Shop | None: 返回抖店店铺
        """
        return Shop.query.filter_by(sid=sid, platform=Shop.Platform.DOUDIAN).first()

    @classmethod
    def get_by_app_id(cls, app_id: str) -> Optional["Shop"]:
        return (
            cls.query.join(MiniAppTemplate, MiniAppTemplate.shop_id == cls.id)
            .filter(MiniAppTemplate.app_id == app_id)
            .first()
        )

    @property
    def contract(self) -> Optional[ContractInfo]:
        return ContractInfo.query.filter_by(org_id=self.org_id).first()

    def get_mola_kuaimai_token(self, jwt_key: str):
        """
        example:
        {
            "shop": {
                "id": "***************",
                "title": "德赛集团有限公司",
                "nickname": "德赛集团有限公司",
                "site": "kuaimai",
                "namespace": "grayerp",
                "related": []
            },
            "iat": 1636341104,
            "exp": 1667877104,
            "iss": "gateway"
        }
        """

        def mola_site(platform: str):
            if platform and platform.lower() in ["taobao", "tmall"]:
                return "taobao"
            if platform:
                return platform.lower()
            return None

        payload = {
            "shop": {
                "id": self.sid,
                "title": self.title,
                "nickname": self.nick,
                "site": mola_site(self.platform),
                # "namespace": "grayerp",
                # "related": []
            }
        }
        return jwt.encode(payload, jwt_key, algorithm="HS256")

    def brief(self):
        return {
            "sid": self.sid,
            "nick": self.nick,
            "title": self.title,
            "open_id": self.open_id,
            "org_id": self.org_id,
            "channel_id": self.channel_id,
            "platform": self.platform,
        }

    def get_real_nick(self, nick: str):
        real_nick: Optional[str] = self.features.get("nick")
        if real_nick:
            split_nick = nick.split(":")
            split_nick[0] = real_nick
            return ":".join(split_nick)
        return nick

    def erp_types(self):
        return [erp.erp_type for erp in self.erps]

    def is_taobao(self):
        return self.platform in ["TAOBAO", "TMALL"]

    def is_doudian(self):
        return self.platform == "DOUDIAN"

    def is_alibaba(self):
        return self.platform == "ALIBABA"

    def is_pdd(self):
        return self.platform == "PDD"

    def is_pdd_auth_valid(self):
        from robot_processor.shop.auth_manager import if_shop_pdd_auth_valid

        return if_shop_pdd_auth_valid(self.sid)

    def is_ks(self):
        return self.platform == "KUAISHOU"

    def is_ks_auth_valid(self):
        from robot_processor.shop.auth_manager import Credentials

        credential = Credentials.get_by_shop_auth_type(self, AuthType.KS_LFX)
        if not credential:
            return False
        return credential.expire_timestamp > int(time.time())

    def is_jd(self):
        return self.platform == "JD"

    def get_trades_key(self):
        if self.is_ks():
            return "ks_trade_info"
        if self.is_pdd():
            return "pdd_trade_info"
        if self.is_doudian():
            return "dy_trade_info"

    def is_valid(self):
        if self.deleted or self.status != ShopStatus.ENABLE:
            return False
        if not (contract := self.contract) or contract.is_expired:
            return False
        return True

    def update_receiver_info(self, tid, receiver_address: dict):
        """
        更新买家收货地址信息
        :param tid 订单 id
        :param receiver_address 订单的收货地址, 数据格式同 address widget
        """
        from robot_processor.client import buyer_client

        if (
            not receiver_address
            or not receiver_address.get("address")
            or "*" in receiver_address.get("address", "")
            or not receiver_address.get("mobile")
            or "*" in receiver_address.get("mobile", "")
            or not receiver_address.get("name")
            or "*" in receiver_address.get("name", "")
        ):
            return
        try:
            buyer_client.update_receiver_info(self.sid, self.nick, self.platform, tid, receiver_address)
        except Exception as exc:
            logger.warning(f"failed to update buyer info: {exc} ")

    class Utils:
        @staticmethod
        def taobao_platforms():
            return ["TAOBAO", "TMALL", "1688"]

        @staticmethod
        def infer_taobao_shop_platform_by_title(shop_title: str):
            """根据店铺名称后缀推测店铺平台类型"""
            for suffix in ("旗舰店", "专卖店", "专营店"):
                if shop_title.endswith(suffix):
                    return "TMALL"
            return "TAOBAO"

        @staticmethod
        def get_taobao_shop_info_by_access_token(access_token: str, nick: str) -> dict[str, str] | None:
            """
            通过 access_token 到淘宝获取店铺/卖家信息
            """
            from robot_processor.client import taobao_client

            response = taobao_client.shop_seller_get(access_token, fields="sid,title")
            match response:
                case Ok(shop_res):
                    result: dict[str, str] = {}
                    shop_info = shop_res["shop"]
                    platform = Shop.Utils.infer_taobao_shop_platform_by_title(shop_info["title"])
                    result["sid"] = str(shop_info["sid"])
                    result["title"] = shop_info["title"]
                    result["platform"] = platform

                    sub_users_response = taobao_client.subusers_get(access_token, user_nick=nick)
                    match sub_users_response:
                        case Ok(seller_info):
                            accounts = seller_info["subaccounts"]
                            result["seller_id"] = str(accounts[0]["user_id"])
                            return result
            return None

    class Queries:
        __slots__ = ()

        @staticmethod
        def optimal_shop_by_sid(
            sid: str, *, platform: Optional[str] = None, org_id: str | int | None = None
        ) -> Optional["Shop"]:
            """使用了一些策略的 db query，因为 sid 可能会重复，尽可能地拿到最有效的那个店铺"""

            query = Shop.query.filter(Shop.sid == sid)
            if platform is not None:
                query = query.filter(Shop.platform == platform)
            if org_id is not None:
                query = query.filter(Shop.org_id == str(org_id))
            order_by_status, order_by_deleted, order_by_open_id = Shop.Queries.order_conditions()
            query = query.order_by(order_by_status, order_by_deleted, order_by_open_id)
            shop = query.first()
            return shop

        @staticmethod
        def order_conditions() -> Tuple[Any, Any, Any]:
            from sqlalchemy import case

            # 按删除状态进行排序，会按照升序排列，数字越小优先级越高
            order_by_deleted = case((Shop.deleted.is_(False), 1), (Shop.deleted.is_(True), 3), else_=2)
            # 按状态进行排序
            order_by_status = case(
                (Shop.status == ShopStatus.ENABLE, 1),
                (Shop.status == ShopStatus.DISABLE, 3),
                else_=2,
            )
            # 按照 open_id 是否为空排序。
            order_by_open_id = case((Shop.open_id.is_not(None), 1), (Shop.open_id.is_(None), 3), else_=2)
            return order_by_status, order_by_deleted, order_by_open_id

        @staticmethod
        def org_shops_by_org_id(org_id: str) -> Query:
            return Shop.query.filter(Shop.org_id == org_id).filter(Shop.Filters.accessible)

        @staticmethod
        def forms(shop: "Shop") -> Query:
            return (
                _resolve_form_model()
                .query.join(_resolve_form_shop_model())
                .filter(_resolve_form_shop_model().shop == shop)
            )

        @staticmethod
        def forms_by_wrapper(shop: "Shop") -> Query:
            """
            返回 FormShop 实例，可以减少对数据库的循环查询
            具体见 FormShop.form_wrapper
            """
            return _resolve_form_model().Queries.form_shops_by_shop(shop)

        @staticmethod
        def taobao_shop_by_nick_or_open_id(nick: str, open_id: str):
            """根据 nick 或 open id 查找店铺"""
            from sqlalchemy import case
            from sqlalchemy import or_

            order_by_condition = case((Shop.nick == nick, 1), (Shop.open_id == open_id, 2), else_=3)
            query = (
                Shop.query.filter(or_(Shop.nick == nick, Shop.open_id == open_id))
                .filter(Shop.Filters.platform_taobao)
                .order_by(order_by_condition)
            )
            return query

        @staticmethod
        def insert_shop_grant_record_for_auth_only(
            nick: str,
            open_id: str | None,
            title: str,
            platform: str,
            sid: str,
            app: App,
            access_token: str,
        ):
            """
            对于无飞梭合同的店铺，需要记录下来店铺的授权信息
            所以通过这种方式创建的店铺，状态固定为 AUTHORIZED，仅用于授权信息查询
            当飞梭合同推送过来后，才会将店铺状态置为 ENABLE
            """
            shop = Shop()
            shop.nick = nick
            shop.open_id = open_id
            shop.title = title
            shop.platform = platform
            shop.sid = sid
            shop.status = ShopStatus.AUTHORIZED
            grant_record = GrantRecord()
            grant_record.app = app
            grant_record.access_token = access_token
            shop.records.append(grant_record)
            db.session.add(shop)
            db.session.commit()

            return shop

        @staticmethod
        def grant_record_by_sid(sid: str, platforms: List[str]) -> GrantRecord:
            return (
                GrantRecord.query.join(Shop)  # type: ignore[return-value]
                .filter(Shop.sid == sid, Shop.platform.in_(platforms))
                .order_by(GrantRecord.id.desc())
                .first()
            )

        @staticmethod
        def grant_record_by_nick(nick: str, platforms: List[str]) -> GrantRecord:
            return (
                GrantRecord.query.join(Shop)  # type: ignore[return-value]
                .filter(
                    Shop.nick == nick,
                    GrantRecord.app.not_in([App.QN_SIDEBAR]),
                    Shop.platform.in_(platforms),
                )
                .order_by(GrantRecord.id.desc())
                .first()
            )

        @staticmethod
        def by_channel_id(channel_id: int) -> Optional["Shop"]:
            return Shop.query.filter_by(channel_id=channel_id).first()

        @staticmethod
        def by_platform_tid(tid: str, org_id: str):
            from leyan_proto.digismart.dgt_common_pb2 import ChannelType as pb_ChannelType

            from robot_processor.client import trade_client

            if not (channels := trade_client.get_channel_info_by_tid_list([tid], org_id)):
                return Err(ValueError("无匹配店铺"))
            channel_info = channels[0]
            channel_type = pb_ChannelType.Name(channel_info.channel_type)
            # trade 返回的淘系平台不准确
            if channel_type in ["TAOBAO", "TMALL"]:
                platform = ["TAOBAO", "TMALL"]
            else:
                platform = [channel_type]
            shop_query = Shop.query.filter(Shop.org_id == org_id, Shop.platform.in_(platform))
            if channel_info.channel_no:
                shop = shop_query.filter_by(sid=channel_info.channel_no).first()
            else:
                shop = shop_query.filter_by(nick=channel_info.seller_nick).first()
            if shop is None:
                return Err(ValueError("无匹配店铺"))
            return Ok(shop)

    class Filters:
        __slots__ = ()

        @classproperty  # noqa
        @staticmethod
        def accessible():
            """可以访问的店铺"""
            from sqlalchemy import and_

            return and_(Shop.deleted.isnot(True), ~Shop.Filters.status_authorized)

        @classproperty  # noqa
        @staticmethod
        def platform_taobao():
            return Shop.platform.in_(["TAOBAO", "TMALL"])

        @classproperty  # noqa
        @staticmethod
        def status_authorized():
            return Shop.status == ShopStatus.AUTHORIZED

    class Options:
        __slots__ = ()

        @classproperty  # noqa
        @staticmethod
        def joined_load_erp():
            return Load(Shop).joinedload(Shop.eager_erps)

        @classproperty  # noqa
        @staticmethod
        def from_form_shop():
            return _resolve_form_shop_model().shop


class SubscribeInfo(DbBaseModel):
    __table_args__ = (sa.Index("ix_org_id_subscribe", "org_id", "subscribe_model", "model_target"),)

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    # 租户
    org_id: Mapped[str | None] = mapped_column(sa.String(32))

    class SubscribeModel(str, LabeledEnum):
        RPA = "RPA", "自动化应用"

    class SubscribeOperator(str, LabeledEnum):
        SUBSCRIBE = "SUBSCRIBE", "订阅"
        UNSUBSCRIBE = "UNSUBSCRIBE", "取消订阅"

        # RENEW = "RENEW", "续订"

        @property
        def on(self):
            return self not in {self.UNSUBSCRIBE}

        @property
        def off(self):
            return not self.on

    subscribe_model: Mapped[SubscribeModel | None] = mapped_column(sa.Enum(SubscribeModel))
    model_target: Mapped[int | None] = mapped_column(sa.Integer)
    subscribe_operator: Mapped[SubscribeOperator | None] = mapped_column(sa.Enum(SubscribeOperator))
    created_at: Mapped[int] = mapped_column(sa.Integer, default=time.time)
