from sqlalchemy.event import listens_for, listen

from robot_processor.shop.models import Shop, ContractInfo
from robot_processor.shop.tasks import update_shop_rds


@listens_for(Shop.status, "set", raw=True)
@listens_for(Shop.deleted, "set", raw=True)
def update_rds_info_event(target, *args, **kwargs):
    """店铺状态变更时，更新 rds 订阅状态"""
    if not target.session:
        return
    shop: Shop = target.object
    if shop.platform not in Shop.Utils.taobao_platforms():
        return

    listen(
        target.session,
        "after_commit",
        lambda _: update_shop_rds.send(shop.channel_id),
        once=True
    )


@listens_for(ContractInfo.end_ts, "set", raw=True)
def update_org_rds_info_event(target, *args, **kwargs):
    """合同到期时间变更时，更新 rds 订阅状态"""
    if not target.session:
        return
    contract_info: ContractInfo = target.object
    shops = (Shop.query
             .filter(Shop.org_id == contract_info.org_id)
             .filter(Shop.platform.in_(Shop.Utils.taobao_platforms()))
             .all())

    listen(
        target.session,
        "after_commit",
        lambda _: list(map(update_shop_rds.send, [shop.channel_id for shop in shops])),
        once=True
    )
