from typing import Final, Annotated
import time

from loguru import logger
from result import Err, Ok
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.client import taobao_client
from robot_processor.constants import (
    MINI_APP_CLIENTS,
    MINI_APP_ICON,
    MINI_APP_TEMPLATE_ID
)
from robot_processor.constants import TASK_QUEUE_BACKGROUND_TASK, TaskPriority
from robot_processor.enums import ShopStatus
from robot_processor.ext import task_queue, cache
from robot_processor.logging import vars as log_vars
from robot_processor.db import in_transaction
from robot_processor.shop.models import Shop, GrantRecord, MiniAppTemplate
from robot_processor.ext import db
from robot_processor.shop.schema import TaobaoCreateOrRefreshAuthTokenResponse, TaobaoCreateOrRefreshAuthTokenResult
from robot_processor.client.conf import app_config
from robot_processor.utils import unwrap_optional


@task_queue.actor(queue_name=TASK_QUEUE_BACKGROUND_TASK, priority=TaskPriority.HIGH)
@log_vars.bind_logger_info
def init_miniapp(sid: Annotated[str, log_vars.Sid], template_version):
    """初始化小程序

    Steps:
        complete_seller_config:
            用于通知 taobao 已经完成了飞梭侧的相关设置,
            出现无法在千牛后台的选择飞梭工单的问题，可能是这一步骤没有完成
        jushita_jdp_user_add:
            订阅数据推送服务，
            出现没有店铺的订单数据推送等问题，可能是这一步骤没有完成
        miniapp_template_instantiate:
            创建一个小程序实例，这一步创建的只是预览版，需要配合 onlineapp 进行正式版发布
        miniapp_template_onlineapp:
            将预览版发布为正式版
    """
    shop: Shop | None = Shop.query.filter_by(sid=sid).first()
    if not shop:
        return Err(ValueError(f"找不到店铺 {sid}"))
    if not (grant_record := shop.get_recent_record()):
        return Err(ValueError(f"店铺 {sid} 无授权信息"))
    access_token: str = grant_record.access_token
    mini_app_template = unwrap_optional(MiniAppTemplate.get_by_shop(shop.id) or MiniAppTemplate(shop_id=shop.id))

    # ============ 通知 taobao 已经完成了小程序相关配置
    taobao_client.miniapp_app_seller_config_complete(access_token, app_id="3000000044579412")

    # ============ 给商家订阅 RDS
    update_shop_rds.send(shop.channel_id)

    # ============ 给商家构建实例化应用
    def update_grant_record_and_online_miniapp(app_id, app_version):
        # 初始化或者更新应用成功后需要执行的操作:
        # 1. 更新授权记录
        # 2. 清理缓存
        # 3. 上线应用
        with in_transaction() as trx:
            trx.add(mini_app_template)
            mini_app_template.app_id = app_id
            mini_app_template.app_version = app_version
        cache.delete(app_id)  # usage: robot_processor.decorators.cache_mini_app_info

        taobao_client.miniapp_template_onlineapp(
            access_token, clients=MINI_APP_CLIENTS,
            template_id=MINI_APP_TEMPLATE_ID, template_version=template_version,
            app_id=app_id, app_version=app_version
        )

    # 查询当前已发布的版本，判断模板应用是否需要更新
    online_template_version = taobao_client.miniapp_template_query_latest_online_version(
        access_token, MINI_APP_TEMPLATE_ID, mini_app_template.app_id)
    if not all([mini_app_template.app_id, mini_app_template.app_version, online_template_version]):
        match taobao_client.miniapp_template_instantiate(
            access_token,
            clients=MINI_APP_CLIENTS,
            description="这是一个初始化的步骤",
            ext_json="{}",
            icon=MINI_APP_ICON,
            template_id=MINI_APP_TEMPLATE_ID,
            template_version=template_version,
        ):
            case Ok(init_res):
                update_grant_record_and_online_miniapp(init_res["app_id"], init_res["app_version"])
    elif online_template_version != template_version:
        # do update
        match taobao_client.miniapp_template_updateapp(
            access_token,
            clients=MINI_APP_CLIENTS,
            app_id=mini_app_template.app_id,
            template_id=MINI_APP_TEMPLATE_ID,
            template_version=template_version
        ):
            case Ok(update_res):
                update_grant_record_and_online_miniapp(update_res["app_id"], update_res["app_version"])
    else:
        logger.info("店铺已经初始化过，且使用了最新模板，不需要初始化或者更新")


RDS_NAME: Final[str] = "rm-k2j3yy0h9me7x4k9p"


@task_queue.actor(queue_name=TASK_QUEUE_BACKGROUND_TASK, priority=TaskPriority.NORMAL)
def update_shop_rds(channel_id: int):
    """根据店铺的状态订阅/取消订阅 RDS"""
    shop = Shop.Queries.by_channel_id(channel_id)
    if not shop:
        logger.warning(f"找不到店铺 {channel_id=}")
        return

    log_vars.Sid.set(shop.sid)
    if shop.platform not in ["TAOBAO", "TMALL"]:
        logger.info("不是淘系店铺，不处理 rds 订阅")
        return

    # 需要取消订阅的情况
    if shop.deleted:
        logger.warning('店铺已删除，解除 rds 订阅')
        taobao_client.jushita_jdp_user_delete(shop.nick)
        return
    if shop.status != ShopStatus.ENABLE:
        logger.warning('店铺已关闭，解除 rds 订阅')
        taobao_client.jushita_jdp_user_delete(shop.nick)
        return
    contract = shop.contract
    if not contract or contract.is_expired:
        logger.warning('店铺合同已失效，解除 rds 订阅')
        taobao_client.jushita_jdp_user_delete(shop.nick)
        return

    if not shop.seller_id:
        logger.warning('没有seller id，不处理 rds 订阅')
        return
    response = taobao_client.jushita_jdp_users_get(user_id=shop.seller_id)
    match response:
        case Err(e):
            raise e
        case Ok(res):
            if res["total_results"] != 0:
                logger.info('店铺已订阅，不做任何变更')
            else:
                # 需要订阅的情况
                # 如果没有授权记录，不做任何变更
                grant_record = shop.get_recent_record()
                if not grant_record:
                    logger.warning('没有授权记录，不处理 rds 订阅')
                else:
                    logger.info('添加 rds 订阅')
                    taobao_client.jushita_jdp_user_add(grant_record.access_token, rds_name=RDS_NAME)


def refresh_shop_access_token() -> None:
    """
    将即将过期的 refresh_token 的授权都尝试进行一次更新。

    :return:
    """
    time_delta_ms: int = app_config.REFRESH_TAOBAO_TOKEN_TIME_DELTA * 1000
    now_time_ms: int = int(time.time() * 1000)
    # 查询淘系店铺里，refresh token 即将过期的所有授权。
    grant_records: list[GrantRecord] = db.ro_session.query(
       GrantRecord
    ).join(
        Shop, Shop.id == GrantRecord.shop_id
    ).filter(
        Shop.Filters.platform_taobao
    ).filter(
        Shop.Filters.accessible
    ).filter(
        GrantRecord.expires_at_ms.between(
            now_time_ms - time_delta_ms,
            now_time_ms
        ),
        GrantRecord.refresh_token.is_not(None)
    ).all()
    # 依次处理。（如果没有限流，可以考虑用线程池并发处理）
    for grant_record in grant_records:
        refresh_single_grant_record(grant_record)


def refresh_single_grant_record(
    grant_record: GrantRecord,
) -> None:
    """
    刷新淘系店铺的授权。

    :param grant_record:
    :return:
    """
    # 没有 refresh token 的不处理。
    if grant_record.refresh_token is None:
        return None

    resp = taobao_client.refresh_auth_token(grant_record.refresh_token)
    match resp:
        case Ok(refresh_auth_resp):
            tr: TaobaoCreateOrRefreshAuthTokenResponse = TaobaoCreateOrRefreshAuthTokenResponse.parse_obj(
                refresh_auth_resp
            )
            result: TaobaoCreateOrRefreshAuthTokenResult = TaobaoCreateOrRefreshAuthTokenResult.parse_raw(
                tr.token_result
            )

            # 更新授权和 shop
            with in_transaction():
                grant_record.access_token = result.access_token
                grant_record.expires_at_ms = result.expire_time
                grant_record.refresh_token = result.refresh_token

        case Err(e):
            logger.error(
                "刷新 token 时发生错误：grant_record_id: {}, err_message: {}",
                grant_record.id,
                e
            )


def refresh_ks_lfx_access_token():
    from robot_processor.client import super_book_client
    from robot_processor.enums import AuthType
    from robot_processor.ext import db
    from robot_processor.shop.auth_manager import Credentials
    from robot_processor.logging.vars import Sid

    app_name = super_book_client.AppName.KS_LFX
    ks_credentials = Credentials.query.filter(Credentials.auth_type == AuthType.KS_LFX).all()
    for cred in ks_credentials:
        Sid.set(cred.auth_account)
        try:
            response = super_book_client.query_access_token(app_name, cred.auth_account).unwrap()
            if response.success and response.access_token != cred.auth_extra_data["accessToken"]:
                logger.info("快手授权已更新")
                cred.auth_extra_data["accessToken"] = response.access_token
                cred.auth_extra_data["expiresAt"] = response.expires_at
                cred.expire_timestamp = response.expires_at
                flag_modified(cred, "auth_extra_data")
                db.session.commit()
        except Exception as e:
            logger.error(f"刷新 token 时发生错误: credential_id: {cred.id}, err: {e}")


def refresh_xhs_erp_access_token():
    from robot_processor.client import super_book_client
    from robot_processor.enums import AuthType
    from robot_processor.ext import db
    from robot_processor.shop.auth_manager import Credentials
    from robot_processor.logging.vars import Sid

    app_name = super_book_client.AppName.XHS_ERP
    xhs_credentials = Credentials.query.filter(Credentials.auth_type == AuthType.XHS_ERP).all()
    for cred in xhs_credentials:
        Sid.set(cred.auth_account)
        try:
            response = super_book_client.query_access_token(app_name, cred.auth_account).unwrap()
            if response.success and response.access_token != cred.auth_extra_data["accessToken"]:
                logger.info("小红书授权已更新")
                cred.auth_extra_data["accessToken"] = response.access_token
                cred.auth_extra_data["expiresAt"] = response.expires_at
                cred.expire_timestamp = response.expires_at
                flag_modified(cred, "auth_extra_data")
                db.session.commit()
        except Exception as e:
            logger.error(f"刷新 token 时发生错误: credential_id: {cred.id}, err: {e}")
