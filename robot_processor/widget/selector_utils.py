""" Select 组件工具集

下拉列表的三个状态
    在组件: List[SelectOption]

    在 BusinessOrder 和 defaultValue 中: Union[ List[str], List[List[str]] ]
    ['1', '2'] # level == 1
    [['1', '11'], ['2', '21']] # level > 1

    在批量上传导出的表格中
    '1' or '1\\n2' # level == 1
    '1->11' or '1->11\\n2->21'  # level > 1

不同状态的下拉组件
    单层级➕单选
    {"widget_key": ["value"]}

    多层级➕单选
    {"widget_key": ["level_1_value", "level_2_value"]}

    单层级➕多选
    {"widget_key": [
        ["level_1_value_1"],
        ["level_1_value_2"]
        ]}

    多层级➕多选
    {"widget_key": [
        ["level_1_value_1", "level_2_value_1"],
        ["level_1_value_1", "level_2_value_2"]
        ]}
"""
from typing import Final, TypedDict


class WidgetSelectOption(TypedDict, total=False):
    label: str
    value: str
    children: list['WidgetSelectOption'] | None


SelectOption = str | dict
SelectOptionInBusinessOrder = list[str] | list[list[str]] | list[dict] | list[list[dict]]  # 在 bo 中

OPTION_DELIMITER: Final[str] = "->"  # "," 会被 Excel 当作数组分隔符
OPTION_SEPARATOR: Final[str] = "\n"


def get_selection_label(select_option: SelectOption | WidgetSelectOption) -> str:
    if isinstance(select_option, dict):
        return select_option['label']
    return select_option


def flatten_widget_select_options(select_options: list[WidgetSelectOption]) -> list[list[str]]:
    flatten_select_options = []
    for select_option in select_options:
        selection_label = get_selection_label(select_option)
        if children := select_option.get('children'):
            flatten_children_select_options = flatten_widget_select_options(children)
            for flatten_children_select_option in flatten_children_select_options:
                flatten_select_options.append([selection_label, *flatten_children_select_option])
        else:
            flatten_select_options.append([selection_label])
    return flatten_select_options


def convert_single_select_to_str(options: list[SelectOption]) -> str:
    if not options:
        return ""
    return OPTION_DELIMITER.join([get_selection_label(opt) for opt in options])


def convert_multiple_select_to_str(options: list[list[SelectOption]]) -> str:
    if not options:
        return ""
    return OPTION_SEPARATOR.join([convert_single_select_to_str(opt) for opt in options if opt])


def convert_single_select_from_str(option_str: str) -> list[str]:
    return [opt.strip() for opt in option_str.strip().split(OPTION_DELIMITER)]


def convert_multiple_select_from_str(options_str: str) -> list[list[str]]:
    return [convert_single_select_from_str(opt) for opt in options_str.strip().split(OPTION_SEPARATOR)]


def serialize_all_options(options: list[WidgetSelectOption]) -> list[str]:
    """将所有可能的选项分支序列化成类似 "a->b->c" 格式的字符串.

    例如：
    >>> serialize_all_options([{"label": "a", "value": "a"}, {"label": "b", "value": "b"}])
    ["a", "b"]
    >>> serialize_all_options([{"label": "a", "value": "a", "children": [{"label": "b", "value": "b"}]}])
    [" "a->b"]
    """
    _candidate_options = flatten_widget_select_options(options)
    return [OPTION_DELIMITER.join(_option) for _option in _candidate_options]
