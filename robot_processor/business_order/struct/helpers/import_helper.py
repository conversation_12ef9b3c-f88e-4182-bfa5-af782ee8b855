import datetime
from functools import cached_property
from typing import Any
from typing import cast

from loguru import logger
from sqlalchemy.orm.query import RowReturningQuery

from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import Job
from robot_processor.business_order.struct.contants import ARRAY_ITEM_SUFFIX
from robot_processor.business_order.struct.contants import ENUM_ITEM
from robot_processor.business_order.struct.contants import ENUM_LEVEL_PREFIX
from robot_processor.business_order.struct.contants import FILLED_CHAR
from robot_processor.business_order.struct.contants import PAYMENT_METHOD_ITEM_METHOD
from robot_processor.business_order.struct.contants import RATE_ITEM_TYPE
from robot_processor.business_order.struct.contants import RATE_ITEM_VALUE
from robot_processor.business_order.struct.contants import SPACE
from robot_processor.business_order.struct.entities import AccurateWidgetDetail
from robot_processor.business_order.struct.entities import AccurateWidgetMeta
from robot_processor.business_order.struct.entities import AccurateWidgetTitle
from robot_processor.business_order.struct.entities import WidgetName
from robot_processor.business_order.struct.enums import PreDefinedTitle
from robot_processor.ext import db
from robot_processor.form.models import FormVersion
from robot_processor.form.models import Step
from robot_processor.form.models import Widget
from robot_processor.form.models import WidgetInfo
from robot_processor.form.types import WidgetDataType
from robot_processor.form.types import WidgetId
from robot_processor.form.types import WidgetInfoLabel
from robot_processor.form.types import WidgetType

PRE_DEFINED_TITLES: list[PreDefinedTitle] = [
    PreDefinedTitle.BUSINESS_ORDER_ID,
]


class ImportHelper:
    def __init__(
        self,
        form_id: int,
        form_version_id: int | None = None,
    ) -> None:
        self.form_id = form_id
        self.form_version_id = form_version_id

    @cached_property
    def widget_id_to_widget_type_mapping(self) -> dict[WidgetId, WidgetType]:
        return cast(
            dict[WidgetId, WidgetType],
            dict(db.ro_session.query(Widget.id, Widget.schema["type"]).all()),
        )

    @cached_property
    def widget_type_to_widget_id_mapping(self) -> dict[WidgetType, WidgetId]:
        return {
            widget_type: widget_id
            for widget_id, widget_type in cast(
                dict[WidgetId, WidgetType],
                dict(db.ro_session.query(Widget.id, Widget.schema["type"]).all()),
            ).items()
        }

    @cached_property
    def widget_id_to_widget_meta_mapping(self) -> dict[WidgetId, list]:
        return cast(
            dict[WidgetId, list],
            {
                widget_id: widget_meta or []
                for widget_id, widget_meta in db.ro_session.query(Widget.id, Widget.widget_meta).all()
            },
        )

    @cached_property
    def widget_id_to_widget_data_type_mapping(self) -> dict[WidgetId, WidgetDataType]:
        widgets: list[Widget] = db.ro_session.query(Widget).all()
        return {cast(WidgetId, widget.id): cast(WidgetDataType, widget.schema.get("widget_type")) for widget in widgets}

    def get_steps(self):
        if self.form_version_id is not None:
            form_version: FormVersion | None = (
                db.ro_session.query(FormVersion)
                .filter(
                    FormVersion.id == self.form_version_id,
                )
                .first()
            )
            if form_version is not None:
                return db.ro_session.query(Step).filter(Step.id.in_(form_version.step_id)).all()
        latest_form_version: FormVersion | None = (
            db.ro_session.query(FormVersion)
            .filter(
                FormVersion.form_id == self.form_id,
            )
            .order_by(FormVersion.id.desc())
            .first()
        )

        if latest_form_version is not None:
            return db.ro_session.query(Step).filter(Step.id.in_(latest_form_version.step_id)).all()
        return []

    @staticmethod
    def get_widget_infos_by_steps(steps: list[Step]) -> list[WidgetInfo]:
        """
        获取指定步骤名下的所有的 widget_info（根据 widget_info 的 key 来去重）。
        :param steps:
        :return:
        """
        # 获取这一批步骤的所有 widget_collection_id
        widget_collection_ids: list[int] = [
            int(step.widget_collection_id) for step in steps if step.widget_collection_id is not None
        ]

        # 获取这一批步骤的所有 widget_info。
        widget_infos: list[WidgetInfo] = (
            db.ro_session.query(WidgetInfo).filter(WidgetInfo.widget_collection_id.in_(widget_collection_ids)).all()
        )
        return widget_infos

    @staticmethod
    def generate_widget_collection_id_to_widget_infos_mapping(
        widget_infos: list[WidgetInfo],
    ) -> dict[str, list[WidgetInfo]]:
        widget_collection_id_to_widget_infos_mapping: dict[str, list[WidgetInfo]] = {}
        for widget_info in widget_infos:
            widget_infos_for_current_widget_collection = (
                widget_collection_id_to_widget_infos_mapping.get(str(widget_info.widget_collection_id)) or []
            )
            widget_infos_for_current_widget_collection = [
                *widget_infos_for_current_widget_collection,
                widget_info,
            ]
            widget_collection_id_to_widget_infos_mapping[
                str(widget_info.widget_collection_id)
            ] = widget_infos_for_current_widget_collection
        return widget_collection_id_to_widget_infos_mapping

    @staticmethod
    def generate_step_name_to_widget_infos_mapping(
        steps: list[Step],
        widget_collection_id_to_widget_infos_mapping: dict[str, list[WidgetInfo]],
    ) -> dict[str, list[WidgetInfo]]:
        step_name_to_widget_infos_mapping: dict[str, list[WidgetInfo]] = {}
        for step in steps:
            if step.widget_collection_id is None:
                continue
            if step.name is None:
                continue
            widget_infos: list[WidgetInfo] | None = widget_collection_id_to_widget_infos_mapping.get(
                step.widget_collection_id,
            )
            if widget_infos is None:
                continue
            step_name_to_widget_infos_mapping[step.name] = widget_infos
        return step_name_to_widget_infos_mapping

    def generate_widget_name_to_widget_info_mapping(
        self,
        widget_infos: list[WidgetInfo],
    ) -> dict[WidgetName, WidgetInfo]:
        widget_name_to_widget_info_mapping: dict[WidgetName, WidgetInfo] = {}
        for widget_info in widget_infos:
            # 如果当前组件是引用的前序组件，并且不可编辑，则直接跳过。
            if widget_info.before and widget_info.option_value.get("beforeEditable") is not True:
                continue
            widget_info_type = self.widget_id_to_widget_type_mapping.get(
                widget_info.widget_id  # type: ignore[arg-type]
            )
            if widget_info_type is None:
                continue
            widget_data_type = self.widget_id_to_widget_data_type_mapping.get(
                widget_info.widget_id  # type: ignore[arg-type]
            )
            # 根据 标签 和 类型 来生成这一组件的 名称。
            current_widget_name: WidgetName = WidgetName(
                widget_label=WidgetInfoLabel(widget_info.label),
                widget_type=widget_info_type,
                widget_data_type=widget_data_type,
            )
            widget_name_to_widget_info_mapping[current_widget_name] = widget_info
        return widget_name_to_widget_info_mapping

    def generate_step_name_to_widget_item_mapping(
        self,
        step_name_to_widget_infos_mapping: dict[str, list[WidgetInfo]],
    ) -> dict[str, dict[WidgetName, WidgetInfo]]:
        """
        获取同步骤名下，同名同类型的组件构成的名称与组件详情列表的映射。
        并将其与步骤名进行关联，从而返回。
        :return:
        """
        step_name_to_widget_item_mapping: dict[str, dict[WidgetName, WidgetInfo]] = {}

        for step_name, widget_infos in step_name_to_widget_infos_mapping.items():
            # 按照规则同名、同类型的规则（以及配置的迁移规则），来将组件进行合并。
            widget_name_to_widget_info_mapping = self.generate_widget_name_to_widget_info_mapping(
                widget_infos=widget_infos,
            )
            step_name_to_widget_item_mapping[step_name] = widget_name_to_widget_info_mapping

        return step_name_to_widget_item_mapping

    def extract_sub_widget_infos(self, widget_info: WidgetInfo) -> list[WidgetInfo] | None:
        if widget_info.option_value.get("widget_type") in [
            WidgetDataType.TABLE,
            WidgetDataType.COLLECTION,
            WidgetDataType.ARRAY,
        ] and isinstance(fields := widget_info.option_value.get("fields"), list):

            sub_widget_infos: list[WidgetInfo] = []

            if widget_info.option_value.get("widget_type") in [
                WidgetDataType.TABLE,
            ]:
                sub_widget_infos.append(
                    WidgetInfo(
                        key="serial",
                        widget_id=self.widget_type_to_widget_id_mapping.get(WidgetType("text")),
                        option_value={"label": "序列"},
                        data_schema={},
                    )
                )
            elif widget_info.option_value.get("widget_type") in [
                WidgetDataType.ARRAY,
            ]:
                sub_widget_infos.append(
                    WidgetInfo(
                        key=f"serial{ARRAY_ITEM_SUFFIX}",
                        widget_id=self.widget_type_to_widget_id_mapping.get(WidgetType("text")),
                        option_value={"label": "序列"},
                        data_schema={},
                    )
                )

            for field in fields:
                # 不处理引用组件
                if field.get("before"):
                    continue
                sub_widget_info = WidgetInfo(
                    key=field.get("key"),
                    widget_id=field.get("id"),
                    option_value=field.get("option_value") or {},
                    data_schema=field.get("data_schema") or {},
                )
                sub_widget_infos.append(sub_widget_info)

            return sub_widget_infos
        else:
            return None

    def generate_sub_widget_mapping_by_widget_name(
        self,
        widget_name: WidgetName,
        widget_info_for_current_widget_name: WidgetInfo,
    ) -> dict[WidgetName, AccurateWidgetDetail] | None:
        """
        获取聚合后的同名组件的子组件。
        """
        # 描述说明组件。
        if widget_name.widget_type == "text":
            return None
        # 订单组件
        elif widget_name.widget_type == "order":
            return {
                WidgetName(
                    widget_label="订单号",
                    widget_type=WidgetType("text"),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="tid",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
                WidgetName(
                    widget_label="子订单号",
                    widget_type=WidgetType("text"),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="oid",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
            }
        # 收款信息组件
        elif widget_name.widget_type == "payment-method":
            return {
                WidgetName(
                    widget_label="收款方式",
                    widget_type=WidgetType(PAYMENT_METHOD_ITEM_METHOD),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="payment_method",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
                WidgetName(
                    widget_label="订单号",
                    widget_type=WidgetType("text"),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="tid",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
                WidgetName(
                    widget_label="支付宝交易号",
                    widget_type=WidgetType("text"),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="alipay_no",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
                WidgetName(
                    widget_label="真实姓名",
                    widget_type=WidgetType("text"),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="receive_name",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
                WidgetName(
                    widget_label="支付宝账号",
                    widget_type=WidgetType("text"),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="receive_account",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
            }
        # 地址组件
        elif widget_name.widget_type == "address":
            return {
                WidgetName(
                    widget_label="省份",
                    widget_type=WidgetType("text"),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="state",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
                WidgetName(
                    widget_label="城市",
                    widget_type=WidgetType("text"),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="city",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
                WidgetName(
                    widget_label="区县",
                    widget_type=WidgetType("text"),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="zone",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
                WidgetName(
                    widget_label="乡镇",
                    widget_type=WidgetType("text"),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="town",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
                WidgetName(
                    widget_label="姓名",
                    widget_type=WidgetType("text"),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="name",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
                WidgetName(
                    widget_label="详细地址",
                    widget_type=WidgetType("text"),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="address",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
            }
        # 文件组件
        elif widget_name.widget_type == "upload":
            return {
                WidgetName(
                    widget_label="链接",
                    widget_type=WidgetType("text"),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="url",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                )
            }
        # 级联组件
        elif widget_name.widget_data_type == WidgetDataType.ENUM or widget_name.widget_type in ["order-question"]:
            max_level = (widget_info_for_current_widget_name.option_value or {}).get("level") or 1
            return {
                WidgetName(
                    widget_label=f"{ENUM_LEVEL_PREFIX}{level+1}",
                    widget_type=WidgetType(f"{ENUM_ITEM}{widget_name.widget_type}"),
                    widget_data_type=WidgetDataType.ENUM,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="label",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                )
                for level in range(max_level)
            }
        elif widget_name.widget_type == "rate":
            return {
                WidgetName(
                    widget_label="选项类型",
                    widget_type=WidgetType(RATE_ITEM_TYPE),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="type",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
                WidgetName(
                    widget_label="分值",
                    widget_type=WidgetType(RATE_ITEM_VALUE),
                    widget_data_type=WidgetDataType.STRING,
                ): AccurateWidgetDetail(
                    widget_info=WidgetInfo(
                        key="value",
                        widget_id=WidgetId(0),
                        option_value={},
                        data_schema={},
                    ),
                    sub_widgets_mapping=None,
                ),
            }
        # 复合组件和容器组件
        elif widget_name.widget_data_type in [
            WidgetDataType.TABLE,
            WidgetDataType.COLLECTION,
            WidgetDataType.ARRAY,
        ]:
            # 获取当前组件名下的所有子组件。
            sub_widget_infos_for_current_widget_name: list[WidgetInfo] = []
            if sub_widget_infos := self.extract_sub_widget_infos(widget_info_for_current_widget_name):
                sub_widget_infos_for_current_widget_name = sub_widget_infos_for_current_widget_name + sub_widget_infos
            # 将子组件进行合并。
            sub_widget_name_to_widget_info_mapping = self.generate_widget_name_to_widget_info_mapping(
                widget_infos=sub_widget_infos_for_current_widget_name,
            )

            # 获取子组件名。
            widget_name_to_widget_detail_mapping: dict[WidgetName, AccurateWidgetDetail] = {}
            for (
                sub_widget_name,
                sub_widget_info,
            ) in sub_widget_name_to_widget_info_mapping.items():
                # 该子组件还需要拆分，则进入递归。
                if sub_widget_name.widget_type in [
                    "order",
                    "payment-method",
                    "address",
                    "upload",
                    "rate",
                    "order-question",
                ] or sub_widget_name.widget_data_type in [
                    WidgetDataType.ENUM,
                    WidgetDataType.TABLE,
                    WidgetDataType.COLLECTION,
                    WidgetDataType.ARRAY,
                ]:
                    widget_name_to_widget_detail_mapping.update(
                        {
                            sub_widget_name: AccurateWidgetDetail(
                                widget_info=sub_widget_info,
                                sub_widgets_mapping=self.generate_sub_widget_mapping_by_widget_name(
                                    sub_widget_name, sub_widget_info
                                ),
                            )
                        }
                    )
                # 否则，直接返回。
                else:
                    widget_name_to_widget_detail_mapping.update(
                        {sub_widget_name: AccurateWidgetDetail(widget_info=sub_widget_info, sub_widgets_mapping=None)}
                    )
            return widget_name_to_widget_detail_mapping
        # 其他基础组件
        else:
            return None

    def generate_widget_name_to_widget_detail_mapping(
        self,
        widget_name_to_widget_info_mapping: dict[WidgetName, WidgetInfo],
    ) -> dict[WidgetName, AccurateWidgetDetail]:
        widget_name_to_widget_detail_mapping: dict[WidgetName, AccurateWidgetDetail] = {}
        for widget_name, widget_info in widget_name_to_widget_info_mapping.items():
            widget_name_to_widget_detail_mapping.update(
                {
                    widget_name: AccurateWidgetDetail(
                        widget_info=widget_info,
                        sub_widgets_mapping=self.generate_sub_widget_mapping_by_widget_name(widget_name, widget_info),
                    )
                }
            )
        return widget_name_to_widget_detail_mapping

    def generate_step_name_to_widget_meta_mapping(
        self,
        step_name_to_widget_item_mapping: dict[str, dict[WidgetName, WidgetInfo]],
    ):
        step_name_to_widget_meta_mapping: dict[str, dict[WidgetName, AccurateWidgetDetail]] = {}

        # 拆解步骤，获取组件、子组件以及组件可用的工单模板的版本。
        for (
            step_name,
            widget_name_to_widget_info_mapping,
        ) in step_name_to_widget_item_mapping.items():
            widget_name_to_widget_detail_mapping = self.generate_widget_name_to_widget_detail_mapping(
                widget_name_to_widget_info_mapping,
            )
            step_name_to_widget_meta_mapping.update({step_name: widget_name_to_widget_detail_mapping})

        return step_name_to_widget_meta_mapping

    def generate_widget_titles(
        self,
        widget_name_to_widget_detail_mapping: dict[WidgetName, AccurateWidgetDetail],
        prefix_widget_titles: AccurateWidgetTitle | None = None,
    ) -> list[AccurateWidgetTitle]:
        prefix_widget_titles = (
            prefix_widget_titles if prefix_widget_titles is not None else AccurateWidgetTitle(metas=[])
        )

        widget_titles: list[AccurateWidgetTitle] = []
        for widget_name, widget_detail in widget_name_to_widget_detail_mapping.items():
            if not widget_detail.sub_widgets_mapping:
                widget_titles.append(
                    AccurateWidgetTitle(
                        metas=[
                            *prefix_widget_titles.metas,
                            AccurateWidgetMeta(widget_name=widget_name, widget_detail=widget_detail),
                        ],
                    )
                )
            else:
                sub_widget_titles = self.generate_widget_titles(
                    widget_detail.sub_widgets_mapping,
                    AccurateWidgetTitle(
                        metas=[
                            *prefix_widget_titles.metas,
                            AccurateWidgetMeta(widget_name=widget_name, widget_detail=widget_detail),
                        ],
                    ),
                )
                widget_titles = widget_titles + sub_widget_titles

        return widget_titles

    def generate_step_name_to_widget_titles_mapping(
        self,
        step_name_to_widget_meta_mapping: dict[str, dict[WidgetName, AccurateWidgetDetail]],
    ) -> dict[str, list[AccurateWidgetTitle]]:
        step_name_to_widget_titles_mapping: dict[str, list[AccurateWidgetTitle]] = {}

        for step_name, widget_meta in step_name_to_widget_meta_mapping.items():
            # 生成该步骤的组件的 title。
            widget_titles: list[AccurateWidgetTitle] = self.generate_widget_titles(widget_meta)
            # 补充 show_names 信息。
            for widget_title in widget_titles:
                show_names = widget_title.generate_show_names(step_name)
                widget_title.show_names = show_names
            step_name_to_widget_titles_mapping[step_name] = widget_titles

        return step_name_to_widget_titles_mapping

    @staticmethod
    def filter_widget_titles_by_show_names(
        widget_titles: list[AccurateWidgetTitle],
        widget_title_show_names: list[list[str]],
    ) -> dict[tuple[str, ...], AccurateWidgetTitle]:
        if len(widget_titles) == 0:
            return {}
        if widget_titles[0].actually_show_names_length == 0:
            return {}

        formatted_widget_title_show_names = [
            [show_name for show_name in widget_title_show_name if show_name != FILLED_CHAR]
            for widget_title_show_name in widget_title_show_names
        ]

        filtered_widget_titles: dict[tuple[str, ...], AccurateWidgetTitle] = {}
        for widget_title in widget_titles:
            if widget_title.actually_show_names in formatted_widget_title_show_names:
                key = tuple(widget_title.show_names)
                filtered_widget_titles[key] = widget_title

        if len(filtered_widget_titles) != len(formatted_widget_title_show_names):
            logger.warning("Excel 表头无法完全过滤出实际表头")
            return {}
        return filtered_widget_titles

    def compute_widget_titles(self) -> list[AccurateWidgetTitle]:
        steps: list[Step] = self.get_steps()
        widget_infos: list[WidgetInfo] = self.get_widget_infos_by_steps(steps=steps)
        widget_collection_id_to_widget_infos_mapping = self.generate_widget_collection_id_to_widget_infos_mapping(
            widget_infos=widget_infos,
        )
        step_name_to_widget_infos_mapping = self.generate_step_name_to_widget_infos_mapping(
            steps=steps,
            widget_collection_id_to_widget_infos_mapping=widget_collection_id_to_widget_infos_mapping,
        )
        step_name_to_widget_item_mapping = self.generate_step_name_to_widget_item_mapping(
            step_name_to_widget_infos_mapping=step_name_to_widget_infos_mapping,
        )
        step_name_to_widget_meta_mapping = self.generate_step_name_to_widget_meta_mapping(
            step_name_to_widget_item_mapping=step_name_to_widget_item_mapping,
        )
        step_name_to_widget_titles_mapping = self.generate_step_name_to_widget_titles_mapping(
            step_name_to_widget_meta_mapping=step_name_to_widget_meta_mapping,
        )

        final_widget_titles: list[AccurateWidgetTitle] = [
            AccurateWidgetTitle(
                metas=[],
                show_names=[title.value],
            )
            for title in PRE_DEFINED_TITLES
        ]
        for step_name, widget_titles in step_name_to_widget_titles_mapping.items():
            final_widget_titles.extend(widget_titles)
        return final_widget_titles

    def check_widget_titles_is_valid(self) -> bool:
        """
        如果需要导出的 widget title 属于一个复合组件或者一个列表组件，则必须要有他的兄弟节点存在，否则无法明确到他的真实作用域。
        """
        return False

    @staticmethod
    def compute_widget_key_to_resize_value_length_mapping(
        widget_titles: list[AccurateWidgetTitle],
        widget_value_lengths: list[list[int]],
    ) -> dict[tuple[WidgetName, ...], list[int]]:
        # todo: 修正部分场景下，复合组件的缩放数据异常
        widget_key_to_resize_value_length_mapping: dict[tuple[WidgetName, ...], list[int]] = {}

        for widget_title, widget_value_length in zip(widget_titles, widget_value_lengths):
            keys: list[tuple[WidgetName, ...]] = [
                tuple([meta.widget_name for meta in widget_title.metas][: index + 1])
                for index in range(len(widget_title.metas))
            ]

            # 当前组件的长度信息
            widget_key_to_resize_value_length_mapping[keys[-1]] = widget_value_length

            # 倒推其父组件的 size 信息，并进行长度调整
            for parent_key in keys[:-1][::-1]:
                parent_widget_value_length = widget_key_to_resize_value_length_mapping.get(parent_key)
                # 如果还没有父组件的长度信息，则直接使用该子组件的长度信息去补充
                if parent_widget_value_length is None:
                    widget_key_to_resize_value_length_mapping[parent_key] = widget_value_length
                else:
                    if len(parent_widget_value_length) > len(widget_value_length):
                        widget_key_to_resize_value_length_mapping[parent_key] = widget_value_length
        return widget_key_to_resize_value_length_mapping

    @staticmethod
    def update_bo_data_by_widget_value(
        bo_data: dict[str, Any],
        widget_value: list[Any],
        widget_title: AccurateWidgetTitle,
        widget_key_to_resize_value_length_mapping: dict[tuple[WidgetName, ...], list[int]],
        other_filename_to_url_mapping: dict[str, str] | None = None,
    ) -> tuple[dict[str, Any], bool]:
        max_widget_meta_depth: int = len(widget_title.metas)
        pre_widget_keys: list[tuple[WidgetName, ...]] = []
        pre_widget_resize_value_lengths: list[list[int]] = []
        parent_data: Any = bo_data
        has_modified_data: bool = False

        for widget_meta_depth, widget_meta in enumerate(widget_title.metas, 1):
            widget_meta = AccurateWidgetMeta(
                widget_name=widget_meta.widget_name,
                widget_detail=widget_meta.widget_detail,
            )
            widget_info_key = widget_meta.get_widget_info_key()
            widget_key = tuple([meta.widget_name for meta in widget_title.metas][:widget_meta_depth])
            resize_value_length = widget_key_to_resize_value_length_mapping.get(widget_key)
            if resize_value_length is None:
                break
            if isinstance(parent_data, dict):
                if widget_meta_depth == max_widget_meta_depth:
                    grouped_widget_values = []
                    start_index = 0
                    # 理论上，只有第一行数据才是有效数据，其他的只是填充数据
                    for i in resize_value_length:
                        grouped_widget_values.append(widget_value[start_index])
                        start_index += i

                    if widget_meta.widget_name.widget_type == WidgetType(PAYMENT_METHOD_ITEM_METHOD):
                        for wv in grouped_widget_values:
                            got_value: Any = (
                                {
                                    "通过淘宝会员转账": "1",
                                    "通过填写支付宝账号转账": "2",
                                    "通过淘宝订单号转账": "3",
                                }.get(wv)
                                if wv != SPACE
                                else None
                            )
                            if parent_data.get(widget_info_key) != got_value:
                                has_modified_data = True
                            parent_data[widget_info_key] = got_value
                        break
                    elif widget_meta.widget_name.widget_type == WidgetType("boolean"):
                        logger.info(f"grouped_widget_values: {grouped_widget_values}")
                        for wv in grouped_widget_values:
                            got_value = (
                                {
                                    "是": True,
                                    "否": False,
                                }.get(wv)
                                if wv != SPACE
                                else None
                            )
                            logger.info(f"got_value: {got_value}")
                            if parent_data.get(widget_info_key) != got_value:
                                has_modified_data = True
                            parent_data[widget_info_key] = got_value
                        logger.info(parent_data)
                        break
                    elif widget_meta.widget_name.widget_type == WidgetType("date"):
                        for wv in grouped_widget_values:
                            if isinstance(wv, datetime.datetime):
                                got_value = wv.strftime("%Y-%m-%d")
                            else:
                                got_value = wv
                            if parent_data.get(widget_info_key) != got_value:
                                has_modified_data = True
                            parent_data[widget_info_key] = got_value
                        logger.info(parent_data)
                        break
                    elif widget_meta.get_widget_info_key() == "url":
                        for wv in grouped_widget_values:
                            got_value = ((other_filename_to_url_mapping or {}).get(wv) if wv != SPACE else None) or (
                                str(wv) if wv != SPACE else None
                            )
                            if parent_data.get(widget_info_key) != got_value:
                                has_modified_data = True
                            parent_data[widget_info_key] = got_value
                        break
                    else:
                        for wv in grouped_widget_values:
                            if widget_meta.widget_name.widget_data_type in [WidgetDataType.STRING]:
                                wv = str(wv) if wv is not None else None
                            if widget_info_key not in parent_data:
                                has_modified_data = True
                            elif widget_info_key in parent_data and parent_data[widget_info_key] != wv:
                                has_modified_data = True
                            parent_data[widget_info_key] = wv
                        break

                (current_widget_meta_value, has_drop_data,) = ImportHelper.extract_data_from_dict(
                    data=parent_data,
                    widget_meta=widget_meta,
                    sub_widget_meta=widget_title.metas[widget_meta_depth],
                    resize_value_length=resize_value_length,
                )
                if has_drop_data:
                    has_modified_data = True
                logger.info(f"{widget_title.show_names}, {widget_meta_depth}")
                logger.info(resize_value_length)
                logger.info(current_widget_meta_value)
                logger.info(len(current_widget_meta_value))
                parent_data = current_widget_meta_value
            elif isinstance(parent_data, list):
                if widget_meta_depth == max_widget_meta_depth:
                    if widget_meta.is_serial():
                        serial_length = len(resize_value_length)
                        while len(parent_data) > serial_length:
                            has_modified_data = True
                            parent_data.pop(-1)
                        break
                    # 如果最终节点是列表组件的 item 项
                    elif widget_meta.is_item():
                        grouped_widget_values = []
                        start_index = 0
                        # 理论上，只有第一行数据才是有效数据，其他的只是填充数据
                        for i in resize_value_length:
                            grouped_widget_values.append(widget_value[start_index])
                            start_index += i

                        for i, wv in enumerate(grouped_widget_values):
                            if parent_data[i] != wv:
                                has_modified_data = True
                            parent_data[i] = wv
                        break
                    # 如果最终节点是 ENUM 组件的子项
                    elif widget_meta.widget_name.widget_type.startswith(ENUM_ITEM):
                        # 处理级联组件的子项
                        grouped_widget_values = []
                        start_index = 0
                        # 理论上，只有第一行数据才是有效数据，其他的只是填充数据
                        for i in resize_value_length:
                            grouped_widget_values.append(widget_value[start_index])
                            start_index += i

                        level_string = widget_meta.widget_name.widget_label.split(ENUM_LEVEL_PREFIX)[-1]
                        level = int(level_string)

                        for wv, pd in zip(grouped_widget_values, parent_data):
                            for i in range(level):
                                if i + 1 == level:
                                    if len(pd) == level:
                                        if isinstance(pd, list):
                                            if isinstance(pd[i], dict):
                                                if pd[i].get("value") != wv:
                                                    has_modified_data = True
                                            else:
                                                has_modified_data = True

                                        pd[i] = {
                                            "label": wv,
                                            "value": wv,
                                        }
                                    else:
                                        pd.append(
                                            {
                                                "label": wv,
                                                "value": wv,
                                            }
                                        )
                                        has_modified_data = True
                                elif i + 1 > len(pd):
                                    pd.append({})
                                    has_modified_data = True
                        break

                    elif widget_meta.widget_name.widget_type == WidgetType(PAYMENT_METHOD_ITEM_METHOD):
                        grouped_widget_values = []
                        start_index = 0
                        # 理论上，只有第一行数据才是有效数据，其他的只是填充数据
                        for i in resize_value_length:
                            grouped_widget_values.append(widget_value[start_index])
                            start_index += i

                        for wv, pd in zip(grouped_widget_values, parent_data):
                            got_value = (
                                {
                                    "通过淘宝会员转账": "1",
                                    "通过填写支付宝账号转账": "2",
                                    "通过淘宝订单号转账": "3",
                                }.get(wv)
                                if wv != SPACE
                                else None
                            )
                            if pd.get(widget_info_key) != got_value:
                                has_modified_data = True
                            pd[widget_info_key] = got_value
                        break
                    elif widget_meta.widget_name.widget_type == WidgetType("boolean"):
                        grouped_widget_values = []
                        start_index = 0
                        # 理论上，只有第一行数据才是有效数据，其他的只是填充数据
                        for i in resize_value_length:
                            grouped_widget_values.append(widget_value[start_index])
                            start_index += i

                        for wv, pd in zip(grouped_widget_values, parent_data):
                            logger.info(f"grouped_widget_values: {grouped_widget_values}")
                            got_value = (
                                {
                                    "是": True,
                                    "否": False,
                                }.get(wv)
                                if wv != SPACE
                                else None
                            )
                            logger.info(f"got_value: {got_value}")
                            if pd.get(widget_info_key) != got_value:
                                has_modified_data = True
                            pd[widget_info_key] = got_value
                            logger.info(parent_data)
                        break
                    elif widget_meta.widget_name.widget_type == WidgetType("date"):
                        grouped_widget_values = []
                        start_index = 0
                        # 理论上，只有第一行数据才是有效数据，其他的只是填充数据
                        for i in resize_value_length:
                            grouped_widget_values.append(widget_value[start_index])
                            start_index += i

                        for wv, pd in zip(grouped_widget_values, parent_data):
                            if isinstance(wv, datetime.datetime):
                                got_value = wv.strftime("%Y-%m-%d")
                            else:
                                got_value = wv
                            logger.info(f"got_value: {got_value}")
                            if pd.get(widget_info_key) != got_value:
                                has_modified_data = True
                            pd[widget_info_key] = got_value
                            logger.info(parent_data)
                        break
                    elif widget_meta.get_widget_info_key() == "url":
                        grouped_widget_values = []
                        start_index = 0
                        # 理论上，只有第一行数据才是有效数据，其他的只是填充数据
                        for i in resize_value_length:
                            grouped_widget_values.append(widget_value[start_index])
                            start_index += i

                        for wv, pd in zip(grouped_widget_values, parent_data):
                            got_value = ((other_filename_to_url_mapping or {}).get(wv) if wv != SPACE else None) or (
                                str(wv) if wv != SPACE else None
                            )
                            if pd.get(widget_info_key) != got_value:
                                has_modified_data = True
                            pd[widget_info_key] = got_value
                        break
                    elif widget_meta.widget_name.widget_type == WidgetType(
                        RATE_ITEM_VALUE,
                    ):
                        grouped_widget_values = []
                        start_index = 0
                        # 理论上，只有第一行数据才是有效数据，其他的只是填充数据
                        for i in resize_value_length:
                            grouped_widget_values.append(widget_value[start_index])
                            start_index += i

                        for wv, pd in zip(grouped_widget_values, parent_data):
                            rate_type = pd.get("type")
                            if rate_type == "star":
                                got_value = (
                                    {
                                        "1": "非常差",
                                        "2": "差",
                                        "3": "一般",
                                        "4": "好",
                                        "5": "非常好",
                                    }.get(str(wv))
                                    if wv != SPACE
                                    else None
                                )
                                if pd.get(widget_info_key) != got_value:
                                    has_modified_data = True
                                pd[widget_info_key] = got_value
                            elif rate_type == "flower":
                                got_value = {"0": "好评", "1": "中评", "2": "差评"}.get(str(wv)) if wv != SPACE else None
                                if pd.get(widget_info_key) != got_value:
                                    has_modified_data = True
                                pd[widget_info_key] = got_value
                            else:
                                got_value = wv if wv != SPACE else None
                                if pd.get(widget_info_key) != got_value:
                                    has_modified_data = True
                                pd[widget_info_key] = got_value
                        break
                    else:
                        grouped_widget_values = []
                        start_index = 0
                        # 理论上，只有第一行数据才是有效数据，其他的只是填充数据
                        for i in resize_value_length:
                            grouped_widget_values.append(widget_value[start_index])
                            start_index += i

                        for wv, pd in zip(grouped_widget_values, parent_data):
                            got_value = wv if wv != SPACE else None
                            if (
                                widget_meta.widget_name.widget_data_type in [WidgetDataType.STRING]
                                or got_value is not None
                            ):
                                got_value = str(got_value)
                            if pd.get(widget_info_key) != got_value:
                                has_modified_data = True
                            pd[widget_info_key] = got_value
                        break

                if widget_meta.is_item():
                    if widget_meta.widget_name.widget_data_type == WidgetDataType.COLLECTION:
                        # 如果是列表组件的子组件，则暂时无需处理，直接进入下一层级的处理。
                        continue
                    # 主要是应对列表组件内嵌列表、复合组件的情况。如果是其他类型的组件，基本就已经到最底层了。
                    temp_parent_data = []
                    for data_item, data_item_length in zip(parent_data, resize_value_length):
                        sub_widget_meta = widget_title.metas[widget_meta_depth]
                        if data_item_length > len(data_item):
                            for j in range(data_item_length):
                                if j >= len(data_item):
                                    if sub_widget_meta.widget_name.widget_data_type in [
                                        WidgetDataType.ARRAY,
                                        WidgetDataType.TABLE,
                                    ]:
                                        data_item.append([])
                                    else:
                                        data_item.append({})
                        else:
                            while len(data_item) > data_item_length:
                                has_modified_data = True
                                data_item.pop(-1)
                        temp_parent_data.extend(data_item)
                    parent_data = temp_parent_data
                    continue

                if len(pre_widget_resize_value_lengths) > 0:
                    parent_widget_resize_value_length = pre_widget_resize_value_lengths[-1]
                else:
                    parent_widget_resize_value_length = resize_value_length
                current_widget_meta_value = []
                start_index = 0
                for parent_data_item, parent_data_item_length in zip(parent_data, parent_widget_resize_value_length):
                    current_resize_value_length = []
                    s = 0
                    for i, rvl in enumerate(resize_value_length[start_index:], start_index):
                        s += rvl
                        if s == parent_data_item_length:
                            current_resize_value_length = resize_value_length[start_index : i + 1]
                            start_index = i + 1
                            break

                    (current_widget_meta_value_item, has_drop_data,) = ImportHelper.extract_data_from_dict(
                        data=parent_data_item,
                        widget_meta=widget_meta,
                        sub_widget_meta=widget_title.metas[widget_meta_depth],
                        resize_value_length=current_resize_value_length,
                    )
                    if has_drop_data:
                        has_modified_data = True
                    logger.info(f"{widget_title.show_names}, {widget_meta_depth}")
                    logger.info(current_resize_value_length)
                    logger.info(current_widget_meta_value_item)
                    logger.info(len(current_widget_meta_value_item))
                    current_widget_meta_value.extend(current_widget_meta_value_item)

                parent_data = current_widget_meta_value
            pre_widget_keys.append(widget_key)
            pre_widget_resize_value_lengths.append(resize_value_length)
        return bo_data, has_modified_data

    @staticmethod
    def extract_data_from_dict(
        data: dict,
        widget_meta: AccurateWidgetMeta,
        sub_widget_meta: AccurateWidgetMeta,
        resize_value_length: list[int],
    ) -> tuple[list, bool]:
        has_drop_data = False

        widget_info_key = widget_meta.get_widget_info_key()
        if widget_info_key in data:
            current_widget_meta_value = data.get(widget_info_key)
        else:
            current_widget_meta_value = widget_meta.create_empty_value()
        if widget_meta.widget_name.widget_type in ["order", "upload",] or widget_meta.widget_name.widget_data_type in [
            WidgetDataType.TABLE,
        ]:
            if not isinstance(current_widget_meta_value, list):
                current_widget_meta_value = [current_widget_meta_value]
            if len(resize_value_length) > len(current_widget_meta_value):
                for index, _ in enumerate(resize_value_length):
                    if index >= len(current_widget_meta_value):
                        current_widget_meta_value.append({})
            else:
                while len(current_widget_meta_value) > len(resize_value_length):
                    has_drop_data = True
                    current_widget_meta_value.pop(-1)
            data.update({widget_info_key: current_widget_meta_value})
        elif widget_meta.widget_name.widget_data_type in [
            WidgetDataType.ARRAY,
        ]:
            if not isinstance(current_widget_meta_value, list):
                current_widget_meta_value = [current_widget_meta_value]
            if sub_widget_meta.get_widget_info_key() not in ["serial", "serial_ITEM"]:
                if len(resize_value_length) > len(current_widget_meta_value):
                    for index, _ in enumerate(resize_value_length):
                        if index >= len(current_widget_meta_value):
                            current_widget_meta_value.append(sub_widget_meta.create_empty_value())
                    else:
                        while len(current_widget_meta_value) > len(resize_value_length):
                            has_drop_data = True
                            current_widget_meta_value.pop(-1)
            data.update({widget_info_key: current_widget_meta_value})
        elif widget_meta.widget_name.widget_data_type in [
            WidgetDataType.ENUM,
        ] or widget_meta.widget_name.widget_type in ["order-question"]:
            if not isinstance(current_widget_meta_value, list):
                current_widget_meta_value = [current_widget_meta_value]
            # 多选
            if sub_widget_meta.widget_name.widget_type.startswith(
                ENUM_ITEM
            ) and sub_widget_meta.widget_name.widget_type.split(ENUM_ITEM)[-1] in [
                "select-tile",
                "select-dropdown",
                "order-question",
            ]:
                if len(resize_value_length) > len(current_widget_meta_value):
                    for index, _ in enumerate(resize_value_length):
                        if index >= len(current_widget_meta_value):
                            current_widget_meta_value.append([])
                else:
                    while len(current_widget_meta_value) > len(resize_value_length):
                        has_drop_data = True
                        current_widget_meta_value.pop(-1)
                data.update({widget_info_key: current_widget_meta_value})
            # 单选
            elif sub_widget_meta.widget_name.widget_type.startswith(
                ENUM_ITEM
            ) and sub_widget_meta.widget_name.widget_type.split(ENUM_ITEM)[-1] in [
                "radio-dropdown",
                "radio-tile",
            ]:
                data.update({widget_info_key: current_widget_meta_value})
                current_widget_meta_value = [current_widget_meta_value]
                if len(resize_value_length) > len(current_widget_meta_value):
                    for index, _ in enumerate(resize_value_length):
                        if index >= 1:
                            current_widget_meta_value.append([])
                else:
                    while len(current_widget_meta_value) > len(resize_value_length):
                        has_drop_data = True
                        current_widget_meta_value.pop(-1)
        else:
            data.update({widget_info_key: current_widget_meta_value})
            current_widget_meta_value = [current_widget_meta_value]

        return current_widget_meta_value, has_drop_data

    def get_prev_job_ids_for_business_order(
        self,
        business_order: BusinessOrder,
    ) -> list[int]:
        reversed_job_history = business_order.get_reversed_job_history()
        if business_order.current_job_id not in reversed_job_history:
            return []

        cur_job_index = reversed_job_history.index(business_order.current_job_id)
        prev_job_index = cur_job_index + 1
        if prev_job_index >= len(reversed_job_history):
            return []

        prev_job_ids = reversed_job_history[prev_job_index:]
        return prev_job_ids

    def get_prev_steps_for_business_order(
        self,
        business_order: BusinessOrder,
    ) -> list[Step]:
        prev_job_ids = self.get_prev_job_ids_for_business_order(business_order)
        if len(prev_job_ids) == 0:
            return []

        steps: list[Step] = (
            db.ro_session.query(Step).join(Job, Job.step_id == Step.id).filter(Job.id.in_(prev_job_ids)).all()
        )

        return steps

    def get_prev_step_names_for_business_order(self, business_order: BusinessOrder) -> list[str]:
        prev_job_ids = self.get_prev_job_ids_for_business_order(business_order)
        if len(prev_job_ids) == 0:
            return []

        step_names_query: RowReturningQuery[tuple[str | None]] = (
            db.ro_session.query(Step.name).join(Job, Job.step_id == Step.id).filter(Job.id.in_(prev_job_ids))
        )

        step_names: list[str] = []
        for (step_name,) in step_names_query:
            if step_name is not None:
                step_names.append(step_name)
        return step_names

    def get_current_step_name_for_business_order(
        self,
        business_order: BusinessOrder,
    ) -> str | None:
        current_job_id = business_order.current_job_id
        if current_job_id is None:
            return None
        step: Step | None = (
            db.ro_session.query(Step)
            .join(Job, Job.step_id == Step.id)
            .filter(
                Job.id == current_job_id,
            )
            .first()
        )
        if step is None:
            return None
        return step.name
