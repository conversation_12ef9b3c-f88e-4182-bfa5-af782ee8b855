import io
from typing import <PERSON>ary<PERSON>

from openpyxl import Workbook
from openpyxl import load_workbook
from openpyxl.styles import Alignment
from openpyxl.styles import Border
from openpyxl.styles import Color
from openpyxl.styles import Font
from openpyxl.styles import PatternFill
from openpyxl.styles import Side
from openpyxl.worksheet.worksheet import Worksheet

from robot_processor.business_order.struct.contants import IGNORED_WORKSHEET_TITLE


class ExcelImportDescHelper:
    def __init__(self, excel_bytes_io: BinaryIO):
        self.excel_bytes_io = excel_bytes_io
        self.workbook: Workbook = load_workbook(excel_bytes_io)

        self.pattern_fill = PatternFill(bgColor=Color(rgb="0099CC00"))
        # 必填项的单元格颜色
        self.required_widget_font = Font(
            color=Color(rgb="00FF0000"),
        )
        # 只读项的单元格颜色
        self.read_only_widget_font = Font(
            color=Color(rgb="000000FF"),
        )
        # 边框
        self.border = Border(
            left=Side(border_style="thin"),
            right=Side(border_style="thin"),
            top=Side(border_style="thin"),
            bottom=Side(border_style="thin"),
        )
        # 居中
        self.align = Alignment(horizontal="center", vertical="center", wrap_text=True)

    def generate_import_desc_worksheet(self) -> BinaryIO:
        worksheet: Worksheet = self.workbook.create_sheet(
            title=IGNORED_WORKSHEET_TITLE,
        )
        worksheet.cell(row=1, column=1).value = "导入说明："
        worksheet.cell(row=2, column=1).value = "1.表单字段、工单ID不支持修改，修改后导入数据与工单模板数据不一致，影响导入结果"
        worksheet.cell(row=3, column=1).value = "2.标题带*红色字段表示必填，蓝色字段为只读，不读取 Excel 中填写的内容"
        worksheet.cell(row=4, column=1).value = "3.格式不符或错误的行数据在全部数据导入后，提示部分导入失败，不影响其他行数据导入"
        worksheet.cell(row=5, column=1).value = "4.多行数据支持拆分行&合并行两种写入方式，具体实例如下，请按规范填写"
        worksheet.cell(row=6, column=1).value = "5.组件设置默认值，若表格未填写值（值为空），写入数据时获取组件默认值"

        for row_no in range(1, 7):
            worksheet.merge_cells(
                start_column=1,
                end_column=21,
                start_row=row_no,
                end_row=row_no,
            )

        # 单行示例
        worksheet.cell(row=8, column=1).value = "当前表单实例数据填写示例（取值最新一条工单实例，若无历史工单数据则为空，建议手动创建一条数据）"
        worksheet.merge_cells(
            start_column=1,
            end_column=9,
            start_row=8,
            end_row=8,
        )
        worksheet.cell(row=8, column=1).fill = self.pattern_fill

        worksheet.cell(row=9, column=1).value = ""
        worksheet.merge_cells(
            start_column=1,
            end_column=1,
            start_row=9,
            end_row=11,
        )

        worksheet.cell(row=9, column=2).value = "工单ID"
        worksheet.merge_cells(
            start_column=2,
            end_column=2,
            start_row=9,
            end_row=11,
        )

        worksheet.cell(row=9, column=3).value = "当前步骤"
        worksheet.merge_cells(
            start_column=3,
            end_column=3,
            start_row=9,
            end_row=11,
        )

        worksheet.cell(row=9, column=4).value = "步骤名称"
        worksheet.merge_cells(
            start_column=4,
            end_column=9,
            start_row=9,
            end_row=9,
        )

        worksheet.cell(row=10, column=4).value = "组件A"
        worksheet.merge_cells(
            start_column=4,
            end_column=4,
            start_row=10,
            end_row=11,
        )

        worksheet.cell(row=10, column=5).value = "组件B"
        worksheet.merge_cells(
            start_column=5,
            end_column=5,
            start_row=10,
            end_row=11,
        )

        worksheet.cell(row=10, column=6).value = "组件C"
        worksheet.merge_cells(
            start_column=6,
            end_column=9,
            start_row=10,
            end_row=10,
        )

        worksheet.cell(row=11, column=6).value = "序号"

        worksheet.cell(row=11, column=7).value = "组件D"

        worksheet.cell(row=11, column=8).value = "组件E"

        worksheet.cell(row=11, column=9).value = "组件F"

        worksheet.cell(row=12, column=1).value = "示例值："

        worksheet.cell(row=12, column=2).value = "123"

        worksheet.cell(row=12, column=3).value = "步骤名称"

        worksheet.cell(row=12, column=4).value = "1"

        worksheet.cell(row=12, column=5).value = "2"

        worksheet.cell(row=12, column=6).value = "1"

        worksheet.cell(row=12, column=7).value = "4"

        worksheet.cell(row=12, column=8).value = "3"

        worksheet.cell(row=12, column=9).value = "4"

        worksheet.cell(row=12, column=1).value = "示例值："

        for col in worksheet.iter_cols(min_col=1, max_col=9, min_row=9, max_row=12):
            for cell in col:
                cell.border = self.border
                cell.alignment = self.align

        # 多行示例
        worksheet.cell(row=14, column=1).value = "多行数据填写示范（与当前模版数据无关）"
        worksheet.merge_cells(
            start_column=1,
            end_column=21,
            start_row=14,
            end_row=14,
        )
        worksheet.cell(row=14, column=1).fill = self.pattern_fill

        worksheet.cell(row=15, column=1).value = ""
        worksheet.merge_cells(
            start_column=1,
            end_column=1,
            start_row=15,
            end_row=20,
        )

        worksheet.cell(row=15, column=2).value = "工单 ID"
        worksheet.merge_cells(
            start_column=2,
            end_column=2,
            start_row=15,
            end_row=20,
        )

        worksheet.cell(row=15, column=3).value = "当前步骤"
        worksheet.merge_cells(
            start_column=3,
            end_column=3,
            start_row=15,
            end_row=20,
        )

        worksheet.cell(row=15, column=4).value = "步骤名称3"
        worksheet.merge_cells(
            start_column=4,
            end_column=21,
            start_row=15,
            end_row=15,
        )

        worksheet.cell(row=16, column=4).value = "自动编号"
        worksheet.merge_cells(
            start_column=4,
            end_column=4,
            start_row=16,
            end_row=20,
        )
        worksheet.cell(row=16, column=4).font = self.read_only_widget_font

        worksheet.cell(row=16, column=5).value = "*订单/子订单"
        worksheet.merge_cells(
            start_column=5,
            end_column=6,
            start_row=16,
            end_row=16,
        )
        worksheet.cell(row=16, column=5).font = self.required_widget_font

        worksheet.cell(row=16, column=7).value = "聚水潭出库单信息（无行数限制）"
        worksheet.merge_cells(
            start_column=7,
            end_column=14,
            start_row=16,
            end_row=16,
        )

        worksheet.cell(row=16, column=15).value = "列表组件（无行数限制）"
        worksheet.merge_cells(
            start_column=15,
            end_column=16,
            start_row=16,
            end_row=16,
        )

        worksheet.cell(row=16, column=17).value = "列表套容器（至少2行）"
        worksheet.merge_cells(
            start_column=17,
            end_column=21,
            start_row=16,
            end_row=16,
        )

        worksheet.cell(row=17, column=5).value = "*订单号"
        worksheet.merge_cells(
            start_column=5,
            end_column=5,
            start_row=17,
            end_row=20,
        )
        worksheet.cell(row=17, column=5).font = self.required_widget_font

        worksheet.cell(row=17, column=6).value = "*子订单号"
        worksheet.merge_cells(
            start_column=6,
            end_column=6,
            start_row=17,
            end_row=20,
        )
        worksheet.cell(row=17, column=6).font = self.required_widget_font

        worksheet.cell(row=17, column=7).value = "*序列"
        worksheet.merge_cells(
            start_column=7,
            end_column=7,
            start_row=17,
            end_row=20,
        )
        worksheet.cell(row=17, column=7).font = self.required_widget_font

        worksheet.cell(row=17, column=8).value = "内部单号"
        worksheet.merge_cells(
            start_column=8,
            end_column=8,
            start_row=17,
            end_row=20,
        )

        worksheet.cell(row=17, column=9).value = "实付金额"
        worksheet.merge_cells(
            start_column=9,
            end_column=9,
            start_row=17,
            end_row=20,
        )

        worksheet.cell(row=17, column=10).value = "商品"
        worksheet.merge_cells(
            start_column=10,
            end_column=14,
            start_row=17,
            end_row=17,
        )

        worksheet.cell(row=18, column=10).value = "*序列"
        worksheet.merge_cells(
            start_column=10,
            end_column=10,
            start_row=18,
            end_row=20,
        )
        worksheet.cell(row=18, column=10).font = self.required_widget_font

        worksheet.cell(row=18, column=11).value = "商品名称"
        worksheet.merge_cells(
            start_column=11,
            end_column=11,
            start_row=18,
            end_row=20,
        )

        worksheet.cell(row=18, column=12).value = "商品编码"
        worksheet.merge_cells(
            start_column=12,
            end_column=12,
            start_row=18,
            end_row=20,
        )

        worksheet.cell(row=18, column=13).value = "款式编码"
        worksheet.merge_cells(
            start_column=13,
            end_column=13,
            start_row=18,
            end_row=20,
        )

        worksheet.cell(row=18, column=14).value = "商品数量"
        worksheet.merge_cells(
            start_column=14,
            end_column=14,
            start_row=18,
            end_row=20,
        )

        worksheet.cell(row=17, column=15).value = "*序列"
        worksheet.merge_cells(
            start_column=15,
            end_column=15,
            start_row=17,
            end_row=20,
        )
        worksheet.cell(row=17, column=15).font = self.required_widget_font

        worksheet.cell(row=17, column=16).value = "*附件上传"
        worksheet.cell(row=17, column=16).font = self.required_widget_font

        worksheet.cell(row=18, column=16).value = "*链接"
        worksheet.merge_cells(
            start_column=16,
            end_column=16,
            start_row=18,
            end_row=20,
        )
        worksheet.cell(row=18, column=16).font = self.required_widget_font

        worksheet.cell(row=17, column=17).value = "*序列"
        worksheet.merge_cells(
            start_column=17,
            end_column=17,
            start_row=17,
            end_row=20,
        )
        worksheet.cell(row=17, column=17).font = self.required_widget_font

        worksheet.cell(row=17, column=18).value = "容器组件"
        worksheet.merge_cells(
            start_column=18,
            end_column=21,
            start_row=17,
            end_row=17,
        )

        worksheet.cell(row=18, column=18).value = "单行输入"
        worksheet.merge_cells(
            start_column=18,
            end_column=18,
            start_row=18,
            end_row=20,
        )

        worksheet.cell(row=18, column=19).value = "数值"
        worksheet.merge_cells(
            start_column=19,
            end_column=19,
            start_row=18,
            end_row=20,
        )

        worksheet.cell(row=18, column=20).value = "列表组件"
        worksheet.merge_cells(
            start_column=20,
            end_column=21,
            start_row=18,
            end_row=18,
        )

        worksheet.cell(row=19, column=20).value = "*序列"
        worksheet.merge_cells(
            start_column=20,
            end_column=20,
            start_row=19,
            end_row=20,
        )
        worksheet.cell(row=19, column=20).font = self.required_widget_font

        worksheet.cell(row=19, column=21).value = "日期"
        worksheet.merge_cells(
            start_column=21,
            end_column=21,
            start_row=19,
            end_row=20,
        )

        # 拆分行的数据示例
        worksheet.cell(row=21, column=1).value = "示例值拆分行写入："
        worksheet.merge_cells(
            start_column=1,
            end_column=1,
            start_row=21,
            end_row=24,
        )

        worksheet.cell(row=21, column=2).value = "2076397"

        worksheet.cell(row=21, column=3).value = "步骤名称3"

        worksheet.cell(row=21, column=4).value = ""

        worksheet.cell(row=21, column=5).value = "127272727"

        worksheet.cell(row=21, column=6).value = "127272727"

        worksheet.cell(row=21, column=7).value = "1"
        worksheet.cell(row=22, column=7).value = "1"
        worksheet.cell(row=23, column=7).value = "1"
        worksheet.cell(row=24, column=7).value = "2"

        worksheet.cell(row=21, column=8).value = "27"
        worksheet.cell(row=22, column=8).value = "27"
        worksheet.cell(row=23, column=8).value = "27"
        worksheet.cell(row=24, column=8).value = "28"

        worksheet.cell(row=21, column=9).value = "2"
        worksheet.cell(row=22, column=9).value = "2"
        worksheet.cell(row=23, column=9).value = "2"
        worksheet.cell(row=24, column=9).value = "4"

        worksheet.cell(row=21, column=10).value = "1"
        worksheet.cell(row=22, column=10).value = "1"
        worksheet.cell(row=23, column=10).value = "2"
        worksheet.cell(row=24, column=10).value = "1"

        worksheet.cell(row=21, column=11).value = "测试A"
        worksheet.cell(row=22, column=11).value = "测试A"
        worksheet.cell(row=23, column=11).value = "测试B"
        worksheet.cell(row=24, column=11).value = "测试A"

        worksheet.cell(row=21, column=12).value = "SP01"
        worksheet.cell(row=22, column=12).value = "SP01"
        worksheet.cell(row=23, column=12).value = "SP02"
        worksheet.cell(row=24, column=12).value = "SP01"

        worksheet.cell(row=21, column=13).value = "SP"
        worksheet.cell(row=22, column=13).value = "SP"
        worksheet.cell(row=23, column=13).value = "SP"
        worksheet.cell(row=24, column=13).value = "SP"

        worksheet.cell(row=21, column=14).value = "1"
        worksheet.cell(row=22, column=14).value = "1"
        worksheet.cell(row=23, column=14).value = "2"
        worksheet.cell(row=24, column=14).value = "1"

        worksheet.cell(row=21, column=15).value = "1"
        worksheet.cell(row=22, column=15).value = "1"
        worksheet.cell(row=23, column=15).value = "2"
        worksheet.cell(row=24, column=15).value = "3"

        worksheet.cell(row=21, column=16).value = "图片1.png"
        worksheet.cell(row=22, column=16).value = "图片2.png"
        worksheet.cell(row=23, column=16).value = "图片3.png"

        worksheet.cell(row=21, column=17).value = "1"
        worksheet.cell(row=22, column=17).value = "1"
        worksheet.cell(row=23, column=17).value = "2"

        worksheet.cell(row=21, column=18).value = "单行1"
        worksheet.cell(row=22, column=18).value = "单行1"
        worksheet.cell(row=23, column=18).value = "单行2"

        worksheet.cell(row=21, column=19).value = "2"
        worksheet.cell(row=22, column=19).value = "2"
        worksheet.cell(row=23, column=19).value = "3"

        worksheet.cell(row=21, column=20).value = "1"
        worksheet.cell(row=22, column=20).value = "2"

        worksheet.cell(row=21, column=21).value = "2025/3/2"
        worksheet.cell(row=22, column=21).value = "2025/3/3"

        # 合并行的数据示例
        worksheet.cell(row=25, column=1).value = "示例值合并写入："
        worksheet.merge_cells(
            start_column=1,
            end_column=1,
            start_row=25,
            end_row=28,
        )

        worksheet.cell(row=25, column=2).value = "2076397"
        worksheet.merge_cells(
            start_column=2,
            end_column=2,
            start_row=25,
            end_row=28,
        )

        worksheet.cell(row=25, column=3).value = "步骤名称3"
        worksheet.merge_cells(
            start_column=3,
            end_column=3,
            start_row=25,
            end_row=28,
        )

        worksheet.cell(row=25, column=4).value = ""
        worksheet.merge_cells(
            start_column=3,
            end_column=3,
            start_row=25,
            end_row=28,
        )

        worksheet.cell(row=25, column=5).value = "127272727"
        worksheet.merge_cells(
            start_column=5,
            end_column=5,
            start_row=25,
            end_row=28,
        )

        worksheet.cell(row=25, column=6).value = "127272727"
        worksheet.merge_cells(
            start_column=6,
            end_column=6,
            start_row=25,
            end_row=28,
        )

        worksheet.cell(row=25, column=7).value = "1"
        worksheet.merge_cells(
            start_column=7,
            end_column=7,
            start_row=25,
            end_row=27,
        )
        worksheet.cell(row=28, column=7).value = "2"

        worksheet.cell(row=25, column=8).value = "27"
        worksheet.merge_cells(
            start_column=8,
            end_column=8,
            start_row=25,
            end_row=27,
        )
        worksheet.cell(row=28, column=8).value = "28"

        worksheet.cell(row=25, column=9).value = "2"
        worksheet.merge_cells(
            start_column=9,
            end_column=9,
            start_row=25,
            end_row=27,
        )
        worksheet.cell(row=28, column=9).value = "4"

        worksheet.cell(row=25, column=10).value = "1"
        worksheet.merge_cells(
            start_column=10,
            end_column=10,
            start_row=25,
            end_row=26,
        )
        worksheet.cell(row=27, column=10).value = "2"
        worksheet.cell(row=28, column=10).value = "1"

        worksheet.cell(row=25, column=11).value = "测试A"
        worksheet.merge_cells(
            start_column=11,
            end_column=11,
            start_row=25,
            end_row=26,
        )
        worksheet.cell(row=27, column=11).value = "测试B"
        worksheet.cell(row=28, column=11).value = "测试A"

        worksheet.cell(row=25, column=12).value = "SP01"
        worksheet.merge_cells(
            start_column=12,
            end_column=12,
            start_row=25,
            end_row=26,
        )
        worksheet.cell(row=27, column=12).value = "SP02"
        worksheet.cell(row=28, column=12).value = "SP01"

        worksheet.cell(row=25, column=13).value = "SP"
        worksheet.merge_cells(
            start_column=13,
            end_column=13,
            start_row=25,
            end_row=26,
        )
        worksheet.cell(row=27, column=13).value = "SP"
        worksheet.cell(row=28, column=13).value = "SP"

        worksheet.cell(row=25, column=14).value = "1"
        worksheet.merge_cells(
            start_column=14,
            end_column=14,
            start_row=25,
            end_row=26,
        )
        worksheet.cell(row=27, column=14).value = "2"
        worksheet.cell(row=28, column=14).value = "1"

        worksheet.cell(row=25, column=15).value = "1"
        worksheet.cell(row=26, column=15).value = "2"
        worksheet.cell(row=27, column=15).value = "3"

        worksheet.cell(row=25, column=16).value = "图片1.png"
        worksheet.cell(row=26, column=16).value = "图片2.png"
        worksheet.cell(row=27, column=16).value = "图片3.png"

        worksheet.cell(row=25, column=17).value = "1"
        worksheet.merge_cells(
            start_column=17,
            end_column=17,
            start_row=25,
            end_row=26,
        )
        worksheet.cell(row=27, column=17).value = "2"

        worksheet.cell(row=25, column=18).value = "单行1"
        worksheet.merge_cells(
            start_column=18,
            end_column=18,
            start_row=25,
            end_row=26,
        )
        worksheet.cell(row=27, column=18).value = "单行2"

        worksheet.cell(row=25, column=19).value = "2"
        worksheet.merge_cells(
            start_column=19,
            end_column=19,
            start_row=25,
            end_row=26,
        )
        worksheet.cell(row=27, column=19).value = "3"

        worksheet.cell(row=25, column=20).value = "1"
        worksheet.cell(row=26, column=20).value = "2"

        worksheet.cell(row=25, column=21).value = "2025/3/2"
        worksheet.cell(row=26, column=21).value = "2025/3/3"

        for col in worksheet.iter_cols(min_col=1, max_col=21, min_row=15, max_row=28):
            for cell in col:
                cell.border = self.border
                cell.alignment = self.align

        new_excel_bytes_io = io.BytesIO()
        self.workbook.save(new_excel_bytes_io)
        return new_excel_bytes_io
