import datetime
from typing import Any
from typing import <PERSON><PERSON><PERSON>
from typing import Iterable
from typing import cast

from loguru import logger
from openpyxl import Workbook
from openpyxl.cell import Cell
from openpyxl.cell import MergedCell
from openpyxl.reader.excel import load_workbook
from openpyxl.utils.datetime import from_excel
from openpyxl.worksheet.worksheet import Worksheet

from robot_processor.business_order.struct import errors
from robot_processor.business_order.struct.contants import FILLED_CHAR
from robot_processor.business_order.struct.contants import IGNORED_WORKSHEET_TITLE
from robot_processor.business_order.struct.contants import MIN_ROW_LENGTH
from robot_processor.business_order.struct.contants import SPACE
from robot_processor.business_order.struct.entities import AnalyzedWidgetValue
from robot_processor.business_order.struct.entities import BusinessOrderRows
from robot_processor.business_order.struct.entities import WorksheetInfos
from robot_processor.business_order.struct.entities import WorksheetMetadata
from robot_processor.business_order.struct.enums import ColumnSplitMode
from robot_processor.business_order.struct.enums import ExportScene
from robot_processor.business_order.struct.enums import MultiRowsFormatter
from robot_processor.business_order.struct.enums import PreDefinedTitle


class ExcelAnalyzeHelper:
    def __init__(
        self,
        excel_file_like_object: BinaryIO,
    ) -> None:
        self.workbook: Workbook = load_workbook(excel_file_like_object)
        self.worksheet_title_to_header_depth_mapping: dict[str, int] = {}

    def check_worksheets(self) -> WorksheetInfos:
        """ """
        valid_worksheet_title_to_worksheet_mapping: dict[str, Worksheet] = {}
        invalid_worksheet_title_to_reason_mapping: dict[str, str] = {}
        worksheet_title_to_metadata_mapping: dict[str, WorksheetMetadata] = {}
        for worksheet in self.workbook.worksheets:
            if worksheet.title == IGNORED_WORKSHEET_TITLE:
                invalid_worksheet_title_to_reason_mapping[worksheet.title] = errors.IS_IGNORED_WORKSHEET_TITLE
                continue
            first_cell: Cell | MergedCell = worksheet.cell(row=1, column=1)
            if isinstance(first_cell, MergedCell):
                invalid_worksheet_title_to_reason_mapping[worksheet.title] = errors.FIRST_CELL_IS_NOT_BUSINESS_ORDER_ID
                continue
            if first_cell.value != PreDefinedTitle.BUSINESS_ORDER_ID.value:
                invalid_worksheet_title_to_reason_mapping[worksheet.title] = errors.FIRST_CELL_IS_NOT_BUSINESS_ORDER_ID
                continue
            if first_cell.hyperlink is None:
                invalid_worksheet_title_to_reason_mapping[worksheet.title] = errors.FIRST_CELL_LOSE_METADATA
                continue
            try:
                original_metadata: list[str] = str(first_cell.hyperlink.target).split(".")
                if len(original_metadata) == 2:
                    multi_rows_formatter, export_scene = original_metadata[0], original_metadata[1]
                    column_split_mode: ColumnSplitMode | str = ColumnSplitMode.ALL_SPLIT
                elif len(original_metadata) == 3:
                    multi_rows_formatter, export_scene, column_split_mode = (
                        original_metadata[0],
                        original_metadata[1],
                        original_metadata[2],
                    )
                else:
                    invalid_worksheet_title_to_reason_mapping[worksheet.title] = errors.FIRST_CELL_METADATA_IS_INVALID
                    continue
            except Exception:
                invalid_worksheet_title_to_reason_mapping[worksheet.title] = errors.FIRST_CELL_LOSE_METADATA
                continue
            if export_scene != ExportScene.EDIT:
                invalid_worksheet_title_to_reason_mapping[worksheet.title] = errors.NOT_SUPPORT_EXPORT_SCENE
                continue
            if multi_rows_formatter not in [
                MultiRowsFormatter.SPLIT,
                MultiRowsFormatter.MERGE_CELLS,
            ]:
                invalid_worksheet_title_to_reason_mapping[worksheet.title] = errors.NOT_SUPPORT_MULTI_ROWS_FORMATTER
                continue
            valid_worksheet_title_to_worksheet_mapping[worksheet.title] = worksheet
            worksheet_title_to_metadata_mapping[worksheet.title] = WorksheetMetadata(
                multi_rows_formatter=MultiRowsFormatter(multi_rows_formatter),
                export_scene=ExportScene(export_scene),
                column_split_mode=ColumnSplitMode(column_split_mode),
            )

        worksheet_infos = WorksheetInfos(
            valid_worksheet_title_to_worksheet_mapping=valid_worksheet_title_to_worksheet_mapping,
            invalid_worksheet_title_to_reason_mapping=invalid_worksheet_title_to_reason_mapping,
            worksheet_title_to_metadata_mapping=worksheet_title_to_metadata_mapping,
        )
        return worksheet_infos

    def get_each_business_order_rows_count_by_worksheet(
        self,
        worksheet: Worksheet,
        multi_rows_formatter: MultiRowsFormatter,
    ) -> tuple[dict[int, BusinessOrderRows], None] | tuple[dict[int, BusinessOrderRows], str]:
        business_order_id_column_cells: Iterable[Any] = next(worksheet.iter_cols(1, 1))

        bo_id_to_bo_rows_mapping: dict[int, BusinessOrderRows] = {}
        is_header_cells: bool = True
        header_depth: int = 0
        current_business_order_id: int | None = None
        for cell in business_order_id_column_cells:
            if is_header_cells:
                if isinstance(cell, Cell) and cell.value == PreDefinedTitle.BUSINESS_ORDER_ID.value:
                    header_depth += 1
                    continue
                elif isinstance(cell, MergedCell):
                    header_depth += 1
                    continue
                else:
                    is_header_cells = False
                    try:
                        business_order_id = int(cell.value)
                    except ValueError:
                        return {}, errors.BUSINESS_ORDER_ID_COLUMN_HAS_INVALID_DATA
                    business_order_rows = bo_id_to_bo_rows_mapping.get(business_order_id) or BusinessOrderRows(
                        sheet_name=worksheet.title,
                        start_row=cell.row,
                        end_row=cell.row,
                    )
                    bo_id_to_bo_rows_mapping[business_order_id] = business_order_rows
                    current_business_order_id = business_order_id

                    self.worksheet_title_to_header_depth_mapping[worksheet.title] = header_depth
                    continue
            if multi_rows_formatter == MultiRowsFormatter.SPLIT:
                if isinstance(cell, MergedCell):
                    return {}, errors.BUSINESS_ORDER_ID_COLUMN_HAS_INVALID_DATA
                if isinstance(cell, Cell):
                    if cell.row is None:
                        return {}, errors.BUSINESS_ORDER_ID_COLUMN_HAS_INVALID_DATA  # type: ignore[unreachable]
                    # 如果工单 ID 列的数据为 None，则说明该数据为上一工单 ID 的拆分行。
                    if cell.value is None:
                        if current_business_order_id is None:
                            return {}, errors.BUSINESS_ORDER_ID_COLUMN_HAS_INVALID_DATA
                        business_order_rows = bo_id_to_bo_rows_mapping.get(
                            current_business_order_id
                        ) or BusinessOrderRows(
                            sheet_name=worksheet.title,
                            start_row=cell.row,
                            end_row=cell.row,
                        )
                        business_order_rows.end_row = cell.row
                        bo_id_to_bo_rows_mapping[current_business_order_id] = business_order_rows
                    # 不为空则说明已经进入了其他工单 ID 的数据范围。
                    else:
                        try:
                            business_order_id = int(str(cell.value))
                        except ValueError:
                            return {}, errors.BUSINESS_ORDER_ID_COLUMN_HAS_INVALID_DATA
                        business_order_rows = bo_id_to_bo_rows_mapping.get(business_order_id) or BusinessOrderRows(
                            sheet_name=worksheet.title,
                            start_row=cell.row,
                            end_row=cell.row,
                        )
                        bo_id_to_bo_rows_mapping[business_order_id] = business_order_rows
                        current_business_order_id = business_order_id
            elif multi_rows_formatter == MultiRowsFormatter.MERGE_CELLS:
                if isinstance(cell, MergedCell):
                    if current_business_order_id is None:
                        return {}, errors.BUSINESS_ORDER_ID_COLUMN_HAS_INVALID_DATA
                    if cell.row is not None:
                        business_order_rows = bo_id_to_bo_rows_mapping.get(  # type: ignore[assignment]
                            current_business_order_id
                        )
                        if business_order_rows is None:
                            return {}, errors.BUSINESS_ORDER_ID_COLUMN_HAS_INVALID_DATA  # type: ignore[unreachable]
                        if business_order_rows is not None:
                            business_order_rows.end_row = cell.row
                            bo_id_to_bo_rows_mapping[current_business_order_id] = business_order_rows
                elif isinstance(cell, Cell):
                    try:
                        business_order_id = int(str(cell.value))
                    except ValueError:
                        return {}, errors.BUSINESS_ORDER_ID_COLUMN_HAS_INVALID_DATA
                    business_order_rows = bo_id_to_bo_rows_mapping.get(business_order_id) or BusinessOrderRows(
                        sheet_name=worksheet.title,
                        start_row=cell.row,
                        end_row=cell.row,
                    )
                    bo_id_to_bo_rows_mapping[business_order_id] = business_order_rows
                    current_business_order_id = business_order_id
            else:
                return {}, errors.NOT_SUPPORT_MULTI_ROWS_FORMATTER
        return bo_id_to_bo_rows_mapping, None

    @staticmethod
    def get_original_cell_by_merged_cell(worksheet: Worksheet, merged_cell: MergedCell) -> Cell | None:
        for merged_range in worksheet.merged_cells.ranges:
            if merged_cell.coordinate in merged_range:
                cell = worksheet.cell(row=merged_range.min_row, column=merged_range.min_col)
                if isinstance(cell, Cell):
                    return cell
        return None

    @staticmethod
    def has_sub_widget_titles(worksheet: Worksheet, merged_cell: MergedCell, header_depth: int) -> bool:
        if merged_cell.row is None:
            return False  # type: ignore[unreachable]
        for row in range(merged_cell.row + 1, header_depth + 1):
            if not merged_cell.column:
                continue
            if isinstance(worksheet.cell(row=row, column=merged_cell.column), Cell):
                return True
        return False

    def get_widget_title_show_names(self, worksheet: Worksheet) -> list[list[str]]:
        header_depth: int | None = self.worksheet_title_to_header_depth_mapping.get(worksheet.title)
        if header_depth is None:
            return []

        widget_title_show_names: list[list[str]] = [[] for _ in range(worksheet.max_column)]

        for row in worksheet.iter_rows(min_row=1, max_row=header_depth):
            row = cast(tuple[Any, ...], row)
            for index, cell in enumerate(row):
                cell = cast(Any, cell)
                if isinstance(cell, Cell):
                    widget_title_show_names[index].append(str(cell.value) if cell.value else FILLED_CHAR)
                    continue
                if not self.has_sub_widget_titles(worksheet, cell, header_depth):  # type: ignore[unreachable]
                    widget_title_show_names[index].append(FILLED_CHAR)
                else:
                    if original_cell := self.get_original_cell_by_merged_cell(worksheet, cell):
                        widget_title_show_names[index].append(
                            str(original_cell.value) if original_cell.value else FILLED_CHAR
                        )
                    else:
                        widget_title_show_names[index].append(FILLED_CHAR)

        return widget_title_show_names

    @staticmethod
    def get_bo_id_to_analyzed_widget_values_mapping(
        worksheet: Worksheet,
        bo_id_to_bo_rows_mapping: dict[int, BusinessOrderRows],
        multi_rows_formatter: MultiRowsFormatter,
    ) -> tuple[dict[int, list[AnalyzedWidgetValue]], str] | tuple[dict[int, list[AnalyzedWidgetValue]], None]:
        bo_id_to_analyzed_widget_values_mapping: dict[int, list[AnalyzedWidgetValue]] = {}
        for bo_id, bo_rows in bo_id_to_bo_rows_mapping.items():
            analyzed_widget_values: list[AnalyzedWidgetValue] = []
            for column, _ in enumerate(worksheet.iter_cols(), 1):
                widget_value: list[Any] = []
                resized_widget_value_length: list[int] = []
                if multi_rows_formatter == MultiRowsFormatter.MERGE_CELLS:
                    for row in range(bo_rows.start_row, bo_rows.end_row + 1):
                        cell = worksheet.cell(row=row, column=column)
                        cell = cast(Any, cell)
                        if isinstance(cell, Cell):
                            if cell.is_date:
                                if isinstance(cell.value, datetime.date):
                                    date_value: Any = cell.value
                                elif isinstance(cell.value, datetime.time):
                                    date_value = cell.value
                                elif isinstance(cell.value, datetime.datetime):
                                    date_value = cell.value  # type: ignore[unreachable]
                                else:
                                    try:
                                        date_value = from_excel(cell.value)
                                    except Exception as e:
                                        logger.exception(f"Failed to convert {cell.value} to date: {e}")
                                        date_value = cell.value
                                widget_value.append(date_value)
                                resized_widget_value_length.append(MIN_ROW_LENGTH)
                            else:
                                if cell.value == SPACE:
                                    widget_value.append(None)
                                else:
                                    widget_value.append(cell.value)
                                resized_widget_value_length.append(MIN_ROW_LENGTH)
                        elif isinstance(cell, MergedCell):  # type: ignore[unreachable]
                            if len(resized_widget_value_length) == 0:
                                return {}, errors.BUSINESS_ORDER_DATA_HAS_INVALID_STYLE

                            widget_value.append(Ellipsis)
                            resized_widget_value_length[-1] += MIN_ROW_LENGTH
                elif multi_rows_formatter == MultiRowsFormatter.SPLIT:
                    for row in range(bo_rows.start_row, bo_rows.end_row + 1):
                        cell = worksheet.cell(row=row, column=column)
                        cell = cast(Any, cell)
                        if isinstance(cell, Cell):
                            if cell.is_date:
                                if isinstance(cell.value, datetime.date):
                                    date_value = cell.value
                                elif isinstance(cell.value, datetime.time):
                                    date_value = cell.value
                                elif isinstance(cell.value, datetime.datetime):
                                    date_value = cell.value  # type: ignore[unreachable]
                                else:
                                    try:
                                        date_value = from_excel(cell.value)
                                    except Exception as e:
                                        logger.exception(f"Failed to convert {cell.value} to date: {e}")
                                        date_value = cell.value
                                widget_value.append(date_value)
                                resized_widget_value_length.append(MIN_ROW_LENGTH)
                            else:
                                widget_value.append(cell.value)
                                resized_widget_value_length.append(MIN_ROW_LENGTH)
                        elif isinstance(cell, MergedCell):  # type: ignore[unreachable]
                            widget_value.append(Ellipsis)
                            resized_widget_value_length.append(MIN_ROW_LENGTH)
                else:
                    return {}, errors.NOT_SUPPORT_MULTI_ROWS_FORMATTER
                analyzed_widget_values.append(
                    AnalyzedWidgetValue(
                        widget_value=widget_value,
                        resized_widget_value_length=resized_widget_value_length,
                    )
                )

            bo_id_to_analyzed_widget_values_mapping[bo_id] = analyzed_widget_values
        return bo_id_to_analyzed_widget_values_mapping, None
