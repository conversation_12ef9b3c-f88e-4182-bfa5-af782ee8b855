import io
from datetime import datetime
from typing import Iterable

import pandas as pd
from loguru import logger

from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.struct.contants import FULL_BUSINESS_ORDER_WORKSHEET_TITLE
from robot_processor.business_order.struct.entities import FullDataContainer
from robot_processor.business_order.struct.enums import ColumnSplitMode
from robot_processor.business_order.struct.enums import ExportScene
from robot_processor.business_order.struct.enums import ExportStatus
from robot_processor.business_order.struct.enums import MultiRowsFormatter
from robot_processor.business_order.struct.helpers.extract_helper import ExtractHelper
from robot_processor.business_order.struct.helpers.oss_helper import OssHelper
from robot_processor.business_order.struct.helpers.view_excel_format_helper import ViewExcelFormatHelper
from robot_processor.business_order.struct.schemas import ExportTaskParams
from robot_processor.business_order.struct.utils import update_export_task
from robot_processor.client.conf import app_config


def create_excel_file(
    excel_filename: str,
    full_data_container: FullDataContainer,
    fix_column_width: bool,
    multi_rows_formatter: MultiRowsFormatter,
    export_scene: ExportScene,
    column_split_mode: ColumnSplitMode = ColumnSplitMode.ALL_SPLIT,
):
    try:
        full_dataframe = pd.concat(full_data_container.dataframes)
    except Exception as e:
        logger.exception("数据合并失败: ", e)
        return "", "数据合并失败"

    try:
        bytes_io = io.BytesIO()
        with pd.ExcelWriter(bytes_io, engine="xlsxwriter") as writer:  # type: ignore[abstract]
            full_dataframe.to_excel(writer, sheet_name=FULL_BUSINESS_ORDER_WORKSHEET_TITLE, merge_cells=True)
        bytes_io.seek(0)
    except Exception as e:
        logger.exception("数据写入 Excel 文件失败: ", e)
        return "", "数据写入 Excel 文件失败"

    try:
        excel_write_helper = ViewExcelFormatHelper(
            bytes_io,
            dataframe=full_dataframe,
            business_order_rows=full_data_container.business_order_rows,
            fix_column_width=fix_column_width,
            multi_rows_formatter=multi_rows_formatter,
            export_scene=export_scene,
            column_split_mode=column_split_mode,
        )
        new_bytes_io = excel_write_helper.format_excel()
    except Exception as e:
        logger.exception("Excel 文件格式化失败: ", e)
        return "", "Excel 文件格式化失败"

    url, err = OssHelper.upload_single_file_to_oss(new_bytes_io, excel_filename)
    if err is not None:
        return "", err
    return url, None


def save_data(
    export_task_id: int,
    file_urls: list[str],
    full_data_container: FullDataContainer,
    export_params: ExportTaskParams,
    split_file_number: int,
    in_iteration: bool,
):
    filename = export_params.generate_filename(split_file_number=split_file_number, in_iteration=in_iteration)

    url, err = create_excel_file(
        excel_filename=filename,
        full_data_container=full_data_container,
        fix_column_width=export_params.fixed_column_width,
        multi_rows_formatter=export_params.multi_rows_formatter,
        export_scene=export_params.export_scene,
        column_split_mode=export_params.column_split_mode,
    )
    if err is not None:
        update_export_task(
            export_task_id=export_task_id,
            status=ExportStatus.FAILED,
            message=err,
        )
        return FullDataContainer(), False

    file_urls.append(url)

    # 如果还在遍历中，并且不需要拆分多文件，则直接视为完成。
    if in_iteration:
        if not export_params.is_split_to_multi_files:
            update_export_task(
                export_task_id=export_task_id,
                status=ExportStatus.FINISHED,
                message="已完成",
                file_urls=file_urls,
                finished_time=datetime.now(),
            )
            logger.info(f"导出任务: {export_task_id} 已完成（无需拆分多文件）")
            return FullDataContainer(), False
        else:
            update_export_task(
                export_task_id=export_task_id,
                status=ExportStatus.RUNNING,
                message="部分完成",
                file_urls=file_urls,
            )
            logger.info(f"导出任务: {export_task_id} 已完成文件数: {split_file_number}")
            return FullDataContainer(), True

    update_export_task(
        export_task_id=export_task_id,
        status=ExportStatus.FINISHED,
        message="已完成",
        file_urls=file_urls,
        finished_time=datetime.now(),
    )
    logger.info(f"导出任务: {export_task_id} 已完成")
    return FullDataContainer(), False


def export_full_business_orders(
    business_orders_query: Iterable[BusinessOrder],
    export_task_id: int,
    export_params: ExportTaskParams,
    file_urls: list[str],
):
    # 数据存储对象。
    full_data_container = FullDataContainer()

    split_file_number: int = 1

    # 遍历工单，逐个抽取数据。
    for business_order in business_orders_query:
        try:
            extract_helper = ExtractHelper(business_order, export_params.export_scene, export_params.column_split_mode)
            # 获取工单的步骤的表头信息。
            step_name_to_widget_titles_mapping = extract_helper.compute_widget_titles_for_steps()
            # 提取数据，并转换为 dataframe 的格式。
            dataframe_for_business_order = extract_helper.compute_full_dataframe_for_business_order(
                step_name_to_widget_titles_mapping=step_name_to_widget_titles_mapping,
                needed_widget_title_show_names=export_params.titles,
                multi_rows_formatter=export_params.multi_rows_formatter,
            )
            full_data_container = extract_helper.update_full_data_container(
                dataframe=dataframe_for_business_order,
                full_data_container=full_data_container,
            )

            if full_data_container.rows_count_for_excel > app_config.EXPORT_SINGLE_EXCEL_MAX_ROWS:
                full_data_container, need_continue = save_data(
                    export_task_id=export_task_id,
                    file_urls=file_urls,
                    full_data_container=full_data_container,
                    export_params=export_params,
                    split_file_number=split_file_number,
                    in_iteration=True,
                )
                if need_continue:
                    split_file_number += 1
                else:
                    return None

        except Exception as e:
            logger.exception(f"提取工单 {business_order.id} 的数据失败: {e}")
            continue

    save_data(
        export_task_id=export_task_id,
        file_urls=file_urls,
        full_data_container=full_data_container,
        export_params=export_params,
        split_file_number=split_file_number,
        in_iteration=False,
    )
