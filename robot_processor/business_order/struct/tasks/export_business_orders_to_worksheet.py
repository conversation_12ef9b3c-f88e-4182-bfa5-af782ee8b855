import io
from datetime import datetime
from typing import BinaryIO
from typing import Iterable

import pandas as pd
from loguru import logger

from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.struct.entities import AccurateWidgetTitle
from robot_processor.business_order.struct.entities import WorksheetContainer
from robot_processor.business_order.struct.enums import ColumnSplitMode
from robot_processor.business_order.struct.enums import ExportScene
from robot_processor.business_order.struct.enums import ExportStatus
from robot_processor.business_order.struct.enums import MultiRowsFormatter
from robot_processor.business_order.struct.helpers.edit_excel_format_helper import EditExcelFormatHelper
from robot_processor.business_order.struct.helpers.excel_import_desc_helper import ExcelImportDescHelper
from robot_processor.business_order.struct.helpers.extract_helper import ExtractHelper
from robot_processor.business_order.struct.helpers.oss_helper import OssHelper
from robot_processor.business_order.struct.schemas import ExportTaskParams
from robot_processor.business_order.struct.utils import update_export_task
from robot_processor.client.conf import app_config
from robot_processor.form.models import Form<PERSON><PERSON>ion


def merge_worksheet_dataframes(
    worksheet_tag_to_dataframes_mapping: dict[str, list[pd.DataFrame]],
) -> dict[str, pd.DataFrame]:
    """
    合并多个相同工作的工单数据。
    """
    worksheet_tag_to_merged_dataframe_mapping: dict[str, pd.DataFrame] = {}
    for worksheet_tag, dataframes in worksheet_tag_to_dataframes_mapping.items():
        df = pd.concat(dataframes)
        worksheet_tag_to_merged_dataframe_mapping[worksheet_tag] = df
    return worksheet_tag_to_merged_dataframe_mapping


def generate_worksheet_title_by_form_versions(
    form_versions: set[FormVersion],
) -> str:
    """
    根据工单模板的版本信息，生成工作表的名称。
    """
    sorted_form_versions = sorted(form_versions, key=lambda fv: fv.id, reverse=True)
    latest_form_version: FormVersion = sorted_form_versions[0]
    worksheet_title = "版本 {} {} {}".format(
        str(latest_form_version.version_no),
        str(latest_form_version.id),
        str(latest_form_version.form_id),
    )
    return worksheet_title


def worksheet_dataframe_to_excel(
    worksheet_title_to_merged_dataframe_mapping: dict[str, pd.DataFrame],
) -> BinaryIO:
    """
    使用 pandas 官方方法直接基于 dataframe 去生成 Excel。
    """
    bytes_io = io.BytesIO()
    with pd.ExcelWriter(bytes_io, engine="xlsxwriter") as writer:  # type: ignore[abstract]
        for (
            worksheet_title,
            dataframe,
        ) in worksheet_title_to_merged_dataframe_mapping.items():
            dataframe.to_excel(writer, sheet_name=worksheet_title, merge_cells=True)
    bytes_io.seek(0)
    return bytes_io


def create_excel_file(
    excel_filename: str,
    worksheet_container: WorksheetContainer,
    fix_column_width: bool,
    multi_rows_formatter: MultiRowsFormatter,
    export_scene: ExportScene,
    column_split_mode: ColumnSplitMode = ColumnSplitMode.ALL_SPLIT,
) -> tuple[str, None] | tuple[None, str]:
    """
    基于工作表对应的 dataframes 去生成 Excel 文件。

    返回值为 url 和 err。
    """
    # 将工作表下的 dataframes 进行合并。
    try:
        worksheet_tag_to_merged_dataframe_mapping = merge_worksheet_dataframes(
            worksheet_container.worksheet_tag_to_dataframes_mapping
        )
    except Exception as e:
        logger.exception("数据合并失败: ", e)
        return None, "数据合并失败"

    # 根据工单模板的版本信息，生成工作表名称。
    worksheet_tag_to_worksheet_title_mapping: dict[str, str] = {}
    for worksheet_tag, form_versions in worksheet_container.worksheet_tag_to_form_versions_mapping.items():
        worksheet_title = generate_worksheet_title_by_form_versions(form_versions)
        worksheet_tag_to_worksheet_title_mapping[worksheet_tag] = worksheet_title

    # 以工作表名称来重新构建映射关系。
    worksheet_title_to_merged_dataframe_mapping: dict[str, pd.DataFrame] = {
        worksheet_tag_to_worksheet_title_mapping[worksheet_tag]: merged_dataframe
        for worksheet_tag, merged_dataframe in worksheet_tag_to_merged_dataframe_mapping.items()
    }
    worksheet_title_to_business_order_rows_mapping: dict[str, list[int]] = {
        worksheet_tag_to_worksheet_title_mapping[worksheet_tag]: business_order_rows
        for (
            worksheet_tag,
            business_order_rows,
        ) in worksheet_container.worksheet_tag_to_business_order_rows_mapping.items()
    }
    worksheet_title_to_widget_titles_mapping: dict[str, list[AccurateWidgetTitle]] = {
        worksheet_tag_to_worksheet_title_mapping[worksheet_tag]: widget_titles
        for worksheet_tag, widget_titles in worksheet_container.worksheet_tag_to_widget_titles_mapping.items()
    }

    # 将所有工作表写入 excel 文件。
    try:
        bytes_io = worksheet_dataframe_to_excel(worksheet_title_to_merged_dataframe_mapping)
    except Exception as e:
        logger.exception("数据写入 Excel 文件失败: ", e)
        return None, "数据写入 Excel 文件失败"

    # 如果场景是为了导入，则还需要添加《导入说明》这一个工作表。
    if export_scene == ExportScene.EDIT:
        excel_import_desc_helper = ExcelImportDescHelper(bytes_io)
        bytes_io = excel_import_desc_helper.generate_import_desc_worksheet()

    try:
        # 将 Excel 格式化
        excel_format_helper = EditExcelFormatHelper(
            bytes_io,
            worksheet_title_to_merged_dataframe_mapping,
            worksheet_title_to_business_order_rows_mapping,
            worksheet_title_to_widget_titles_mapping,
            fix_column_width=fix_column_width,
            multi_rows_formatter=multi_rows_formatter,
            export_scene=export_scene,
            column_split_mode=column_split_mode,
        )
        formatted_excel_bytes_io = excel_format_helper.format_excel()
    except Exception as e:
        logger.exception("Excel 文件格式化失败: ", e)
        return None, "Excel 文件格式化失败"

    # 将文件上传到 oss 中。
    url, err = OssHelper.upload_single_file_to_oss(formatted_excel_bytes_io, excel_filename)
    if err is not None:
        return None, err
    assert url is not None
    return url, None


def save_data(
    export_task_id: int,
    file_urls: list[str],
    worksheet_container: WorksheetContainer,
    export_params: ExportTaskParams,
    split_file_number: int,
    in_iteration: bool,
) -> tuple[WorksheetContainer, bool]:
    """
    将数据存入 excel 中。

    返回值为 worksheet_container 和 need_continue。
    """
    # 如果还在遍历中，则需要为文件名追加序号信息。
    filename = export_params.generate_filename(split_file_number=split_file_number, in_iteration=in_iteration)

    # 创建并格式化 Excel。
    url, err = create_excel_file(
        excel_filename=filename,
        worksheet_container=worksheet_container,
        fix_column_width=export_params.fixed_column_width,
        multi_rows_formatter=export_params.multi_rows_formatter,
        export_scene=export_params.export_scene,
        column_split_mode=export_params.column_split_mode,
    )
    if err is not None:
        update_export_task(
            export_task_id=export_task_id,
            status=ExportStatus.FAILED,
            message=err,
        )
        return WorksheetContainer(), False

    assert url is not None
    # 将 excel 的 url 追加。
    file_urls.append(url)

    # 如果还在遍历中，并且不需要拆分多文件，则直接视为完成。
    if in_iteration:
        if not export_params.is_split_to_multi_files:
            update_export_task(
                export_task_id=export_task_id,
                status=ExportStatus.FINISHED,
                message="已完成",
                file_urls=file_urls,
                finished_time=datetime.now(),
            )
            logger.info(f"导出任务: {export_task_id} 已完成（无需拆分多文件）")
            return WorksheetContainer(), False
        else:
            update_export_task(
                export_task_id=export_task_id,
                status=ExportStatus.RUNNING,
                message="部分完成",
                file_urls=file_urls,
            )
            logger.info(f"导出任务: {export_task_id} 已完成文件数: {split_file_number}")
            return WorksheetContainer(), True

    update_export_task(
        export_task_id=export_task_id,
        status=ExportStatus.FINISHED,
        message="已完成",
        file_urls=file_urls,
        finished_time=datetime.now(),
    )
    logger.info(f"导出任务: {export_task_id} 已完成")
    return WorksheetContainer(), False


def export_business_orders_to_worksheet(
    business_orders_query: Iterable[BusinessOrder],
    export_task_id: int,
    export_params: ExportTaskParams,
    file_urls: list[str],
):
    # 数据存储对象。
    worksheet_container = WorksheetContainer()

    split_file_number: int = 1

    # 遍历工单，逐个抽取数据。
    for business_order in business_orders_query:
        try:
            extract_helper = ExtractHelper(business_order, export_params.export_scene, export_params.column_split_mode)
            # 获取工单的步骤的表头信息。
            step_name_to_widget_titles_mapping = extract_helper.compute_widget_titles_for_steps()
            # 提取数据，并转换为 dataframe 的格式。
            (dataframe_for_business_order, filled_widget_titles,) = extract_helper.compute_dataframe_for_business_order(
                step_name_to_widget_titles_mapping=step_name_to_widget_titles_mapping,
                needed_widget_title_show_names=export_params.titles,
                multi_rows_formatter=export_params.multi_rows_formatter,
            )
            # 将数据追加到存储对象中。
            worksheet_container = extract_helper.append_dataframe_to_worksheet(
                dataframe=dataframe_for_business_order,
                widget_titles=filled_widget_titles,
                worksheet_container=worksheet_container,
            )

            if worksheet_container.rows_count_for_excel > app_config.EXPORT_SINGLE_EXCEL_MAX_ROWS:
                worksheet_container, need_continue = save_data(
                    export_task_id=export_task_id,
                    file_urls=file_urls,
                    worksheet_container=worksheet_container,
                    export_params=export_params,
                    split_file_number=split_file_number,
                    in_iteration=True,
                )
                if need_continue:
                    split_file_number += 1
                else:
                    return None

        except Exception as e:
            logger.exception(f"提取工单 {business_order.id} 的数据失败: {e}")
            continue

    save_data(
        export_task_id=export_task_id,
        file_urls=file_urls,
        worksheet_container=worksheet_container,
        export_params=export_params,
        split_file_number=split_file_number,
        in_iteration=False,
    )
    return None
