from enum import Enum
from enum import StrEnum
from typing import Optional

from robot_processor.form.types import WidgetType


class FileType(StrEnum):
    xlsx = "xlsx"
    zip = "zip"


class ExportScene(StrEnum):
    """
    导出场景。
    """

    EDIT = "EDIT"
    VIEW = "VIEW"


class PreDefinedTitle(StrEnum):
    """
    预定义表头。目前只有一个《工单 ID》，主要还是因为系统字段里没有这个东西。
    """

    BUSINESS_ORDER_ID = "工单 ID"


class ExportStatus(StrEnum):
    """
    导出任务的状态
    """

    INIT = "INIT"  # 初始化
    RUNNING = "RUNNING"  # 进行中
    FINISHED = "FINISHED"  # 已完成
    FAILED = "FAILED"  # 导出失败


class ImportStatus(StrEnum):
    INIT = "INIT"  # 初始化
    RUNNING = "RUNNING"  # 进行中
    EXPORTING_FAILED_DATA = "EXPORTING_FAILED_DATA"  # 导出异常数据中
    FINISHED = "FINISHED"  # 已完成
    FAILED = "FAILED"  # 失败


class ImportFileStatus(StrEnum):
    SUCCESS = "SUCCESS"  # 完全成功
    PARTIAL_SUCCESS = "PARTIAL_SUCCESS"  # 部分成功
    INVALID_FILE = "INVALID_FILE"  # 不合规的文件


class MultiRowsFormatter(StrEnum):
    """
    多行的数据格式
    """

    MERGE_CELLS = "MERGE_CELLS"  # 合并单元格
    MERGE_TO_SINGLE_ROW = "MERGE_TO_SINGLE_ROW"  # 合并为一行
    SPLIT = "SPLIT"  # 拆分成多行数据


class WidgetInfoKeyStatus(StrEnum):
    """
    组件 key 的状态，用于让前端去展示问题。
    """

    VALID = "VALID"  # 正常
    INVALID_STEP = "INVALID_STEP"  # 组件处在不可操作的步骤中
    DATA_MODIFIED = "DATA_MODIFIED"  # 组件值有变动


class RateType(StrEnum):
    """
    评分组件的类型。
    """

    star = "star"  # 星星
    flower = "flower"  # 花花


class ColumnSplitMode(StrEnum):
    """
    级联组件和地址组件的拆分模式。
    """

    ALL_SPLIT = "ALL_SPLIT"  # 完全拆分
    PART_SPLIT = "PART_SPLIT"  # 部分拆分
    NOT_SPLIT = "NOT_SPLIT"  # 不拆分


class OrderMode(StrEnum):
    """
    订单组件的形式。
    """

    order = "order"  # 订单
    child = "child"  # 订单 + 子订单


class CommonWidgetType(str, Enum):
    """
    导入导出较为常用的一些组件类型。
    """

    # 文本组件
    text = WidgetType("text")
    # 订单号组件
    order = WidgetType("order")
    # 收款方式组件
    payment_method = WidgetType("payment-method")
    # 地址组件
    address = WidgetType("address")
    # 附件上传类组件
    upload = WidgetType("upload")
    # 评分组件
    rate = WidgetType("rate")
    # 订单问题细分组件
    order_question = WidgetType("order-question")
    # 多选平铺
    select_tile = WidgetType("select-tile")
    # 多选下拉
    select_dropdown = WidgetType("select-dropdown")
    # 单选平铺
    radio_tile = WidgetType("radio-tile")
    # 单选下拉
    radio_dropdown = WidgetType("radio-dropdown")
    # 布尔值
    boolean = WidgetType("boolean")
    # 自动编号
    auto_number = WidgetType("auto-number")

    # 以下为自定义的 WidgetType，主要是为了快速辨别，无法与数据库的信息对应
    # 完整的地址信息
    full_address = WidgetType("full-address")
    # 上传类组件下的链接
    url = WidgetType("url")
    # 收款方式组件下的“收款方式”字段
    payment_method_item_method = WidgetType("payment-method-item-method")
    # 评分组件的类型
    rate_type = WidgetType("rate-item-type")
    # 评分组件的分值
    rate_value = WidgetType("rate-item-value")
    # 复合组件下的序列
    serial = WidgetType("serial")
    # 列表组件下的序列
    serial_item = WidgetType("serial_ITEM")
    # 订单问题细分组件的下属层级组件
    order_question_level = WidgetType("order-question-level")
    # 多选平铺的下属层级组件
    select_tile_level = WidgetType("select-tile-level")
    # 多选下拉的下属层级组件
    select_dropdown_level = WidgetType("select-dropdown-level")
    # 单选平铺的下属层级组件
    radio_tile_level = WidgetType("radio-tile-level")
    # 单选下拉的下属层级组件
    radio_dropdown_level = WidgetType("radio-dropdown-level")

    @classmethod
    def get_enum_widget_type_level(cls, enum_widget_type: WidgetType) -> Optional["CommonWidgetType"]:
        match enum_widget_type:
            case cls.order_question.value:
                return cls.order_question_level
            case cls.select_tile.value:
                return cls.select_tile_level
            case cls.select_dropdown.value:
                return cls.select_dropdown_level
            case cls.radio_tile.value:
                return cls.radio_tile_level
            case cls.radio_dropdown.value:
                return cls.radio_dropdown_level
            case _:
                return None
