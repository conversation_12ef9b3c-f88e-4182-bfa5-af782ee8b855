import io
import json
import string
from urllib.parse import quote_plus
from urllib.request import urlopen

from flask import jsonify
from flask import request
from flask.blueprints import Blueprint
from flask_sqlalchemy.pagination import Pagination
from loguru import logger
from sqlalchemy.orm import load_only

from robot_processor.business_order.fsm import BusinessOrderStatusController
from robot_processor.business_order.job_action import JobAction
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.struct import errors
from robot_processor.business_order.struct.enums import ImportStatus
from robot_processor.business_order.struct.helpers.excel_analyze_helper import Excel<PERSON>nalyzeHelper
from robot_processor.business_order.struct.helpers.oss_helper import Oss<PERSON><PERSON>per
from robot_processor.business_order.struct.helpers.update_bo_data_helper import UpdateBoDataHelper
from robot_processor.business_order.struct.import_tasks import import_excel_data_to_business_orders
from robot_processor.business_order.struct.models import ImportTask
from robot_processor.business_order.struct.schemas import CreateImportTaskBody
from robot_processor.business_order.struct.schemas import ImportTaskExtraData
from robot_processor.business_order.struct.schemas import ImportTaskQuery
from robot_processor.business_order.struct.utils import get_not_current_step_names_for_business_order
from robot_processor.currents import g
from robot_processor.db import in_transaction
from robot_processor.decorators import shop_required
from robot_processor.error.errors import ScenarioError
from robot_processor.ext import db
from robot_processor.validator import validate

api = Blueprint("api", __name__)


@api.post(
    "/import_tasks",
)
@shop_required
@validate
def create_import_task(body: CreateImportTaskBody):
    """
    新建导入任务，并且交由后台进行处理。
    """
    assert g.login_user_detail
    org_id = g.shop.org_id
    creator_id = g.auth.user_id
    creator_nickname = g.auth.login_user_nick
    operator = g.login_user_detail
    is_admin = body.is_admin

    # 解析文件
    file_container, err = OssHelper.parse_file(
        file_name=body.file_name,
        file_type=body.file_type,
        url=body.file_url,
    )
    if err is not None:
        return (
            jsonify(
                success=False,
                message=err,
            ),
            400,
        )

    # 创建导入任务
    import_task = ImportTask(
        org_id=org_id,
        creator_id=creator_id,
        creator_nickname=creator_nickname,
        status=ImportStatus.INIT,
        extra_data=ImportTaskExtraData(
            file_container=file_container,
            operator=operator,
            is_admin=is_admin,
        ).dict(),
    )
    db.session.add(import_task)
    db.session.commit()
    # 发送异步任务。
    import_excel_data_to_business_orders.send_with_options(args=(import_task.id,))
    return jsonify(success=True, import_task_id=import_task.id)


@api.get("/import_tasks")
@shop_required
@validate
def get_import_tasks(query: ImportTaskQuery):
    """
    获取租户下的所有导入任务。
    """
    assert g.login_user_detail

    org_id = g.shop.org_id

    import_tasks_pagination: Pagination = (
        ImportTask.query.options(
            load_only(
                ImportTask.id,
                ImportTask.org_id,
                ImportTask.creator_id,
                ImportTask.creator_nickname,
                ImportTask.created_at,
                ImportTask.status,
                ImportTask.message,
                ImportTask.finished_time,
                ImportTask.exceptional_file_urls,
            )
        )
        .filter(
            ImportTask.org_id == org_id,
        )
        .order_by(ImportTask.id.desc())
        .paginate(
            page=query.page_no,
            per_page=query.page_size,
        )
    )

    return jsonify(
        success=True,
        total=import_tasks_pagination.total,
        data=[import_task.to_data() for import_task in import_tasks_pagination],
    )


@api.get("/import_tasks/<int:import_task_id>")
@shop_required
def get_import_task_by_id(import_task_id: int):
    """
    根据 ID 去获取导入任务详情。
    """
    assert g.login_user_detail

    import_task: ImportTask | None = (
        ImportTask.query.options(
            load_only(
                ImportTask.id,
                ImportTask.org_id,
                ImportTask.creator_id,
                ImportTask.creator_nickname,
                ImportTask.created_at,
                ImportTask.status,
                ImportTask.message,
                ImportTask.finished_time,
                ImportTask.exceptional_file_urls,
            )
        )
        .filter(ImportTask.id == import_task_id)
        .first()
    )
    if import_task is None:
        return jsonify(success=False, message="未能找到导入任务"), 404

    return jsonify(
        success=True,
        data=import_task.to_data(),
    )


@api.post(
    "/update_business_orders",
)
@shop_required
def update_business_orders_by_excel():
    assert g.login_user_detail
    operator = g.login_user_detail
    is_admin = True if request.form.get("is_admin") in [True, "True", "true"] else False

    res = request.files["file"]
    filename = res.filename
    if filename is None:
        return jsonify(success=False, messgae=errors.NO_FILENAME), 400

    if filename.endswith(".zip"):
        temp_dir = OssHelper.unzip_file(res)
        (
            xlsx_filename_to_url_mapping,
            other_filename_to_url_mapping,
        ) = OssHelper.get_files_from_temp_dir(temp_dir)

        if len(xlsx_filename_to_url_mapping) == 0:
            temp_dir.cleanup()
            return jsonify(success=False, message=errors.NOT_FOUND_EXCEL_FILES), 400
    elif filename.endswith(".xlsx"):
        url, err = OssHelper.upload_single_file_to_oss(io.BytesIO(res.read()), filename)
        if err is not None:
            return jsonify(sucess=False, message=errors.INVALID_XLSX_FILE), 400
        assert url is not None
        xlsx_filename_to_url_mapping = {filename: url}
        other_filename_to_url_mapping = None
    else:
        return jsonify(success=False, message=errors.NOT_SUPPORT_FILE), 400

    succeeded_business_order_ids = []
    failed_business_order_ids = []

    invalid_file_urls = []

    for file_name, url in xlsx_filename_to_url_mapping.items():
        logger.info("正在处理 Excel file_name: {}, URL: {}".format(file_name, url))
        quoted_url = quote_plus(url, safe=string.printable)
        try:
            with urlopen(quoted_url) as res:
                excel_analyze_helper = ExcelAnalyzeHelper(excel_file_like_object=io.BytesIO(res.read()))
        except Exception as e:
            invalid_file_urls.append(url)
            logger.error(f"error: {errors.INVALID_XLSX_FILE}, url: {url}")
            logger.exception(e)
            continue
        worksheet_infos = excel_analyze_helper.check_worksheets()
        if len(worksheet_infos.valid_worksheet_title_to_worksheet_mapping) == 0:
            invalid_file_urls.append(url)
            logger.error(f"error: {errors.NOT_FOUND_EXCEL_SHEETS}, url: {url}")
            continue

        for (
            worksheet_title,
            worksheet,
        ) in worksheet_infos.valid_worksheet_title_to_worksheet_mapping.items():
            metadata = worksheet_infos.worksheet_title_to_metadata_mapping[worksheet_title]
            (bo_id_to_bo_rows_mapping, err,) = excel_analyze_helper.get_each_business_order_rows_count_by_worksheet(
                worksheet=worksheet, multi_rows_formatter=metadata.multi_rows_formatter
            )
            if err is not None:
                logger.error(f"error: {err}, worksheet_title: {worksheet_title}, url: {url}")
                continue

            (
                bo_id_to_analyzed_widget_values_mapping,
                err,
            ) = excel_analyze_helper.get_bo_id_to_analyzed_widget_values_mapping(
                worksheet=worksheet,
                bo_id_to_bo_rows_mapping=bo_id_to_bo_rows_mapping,
                multi_rows_formatter=metadata.multi_rows_formatter,
            )
            if err is not None:
                logger.error(f"error: {err}, worksheet_title: {worksheet_title}, url: {url}")
                continue

            needed_widget_title_show_names = excel_analyze_helper.get_widget_title_show_names(
                worksheet=worksheet,
            )
            if len(needed_widget_title_show_names) == 0:
                return (
                    jsonify(success=False, message=errors.NOT_FOUND_WIDGET_TITLES),
                    400,
                )

            business_order_ids: list[int] = list(bo_id_to_bo_rows_mapping.keys())
            grouped_business_order_ids: list[list[int]] = []
            index = 0
            size = 20
            while True:
                current_grouped_bo_ids = business_order_ids[index : index + size]
                if len(current_grouped_bo_ids) == 0:
                    break
                grouped_business_order_ids.append(current_grouped_bo_ids)
                index += size

            for current_grouped_bo_ids in grouped_business_order_ids:
                business_orders: list[BusinessOrder] = (
                    db.ro_session.query(BusinessOrder).filter(BusinessOrder.id.in_(current_grouped_bo_ids)).all()
                )
                if len(business_orders) == 0:
                    continue

                for business_order in business_orders:
                    if business_order.form_id is None:
                        failed_business_order_ids.append({"id": business_order.id, "reason": "工单缺失模板信息"})
                        continue
                    if business_order.current_job_id is None:
                        failed_business_order_ids.append({"id": business_order.id, "reason": "工单缺失当前任务信息"})
                        continue

                    not_current_step_names = get_not_current_step_names_for_business_order(
                        business_order=business_order,
                    )

                    logger.info("工单 ID: {} 所需要无视的步骤名有: {}".format(business_order.id, not_current_step_names))

                    update_bo_data_helper = UpdateBoDataHelper(
                        business_order=business_order,
                        ignored_step_names=not_current_step_names,
                        metadata=metadata,
                    )

                    logger.info(
                        f"工单 ID: {business_order.id} 从 Excel 中获取到的数据为: {bo_id_to_analyzed_widget_values_mapping[business_order.id]}"
                    )

                    bo_data, err = update_bo_data_helper.update_bo_data(
                        needed_widget_title_show_names=needed_widget_title_show_names,
                        analyzed_widget_values=bo_id_to_analyzed_widget_values_mapping[business_order.id],
                        other_filename_to_url_mapping=other_filename_to_url_mapping,
                    )
                    if err is not None:
                        logger.error(f"error: {err}, business_order.id: {business_order.id}")
                        continue

                    logger.info("工单 ID: {} 提取出来的数据为: {}".format(business_order.id, bo_data))

                    try:
                        bo_data = json.loads(json.dumps(bo_data, ensure_ascii=False, default=str))
                    except Exception as e:
                        logger.exception(e)
                        failed_business_order_ids.append({"id": business_order.id, "reason": "数据格式化失败"})
                        continue

                    try:
                        with in_transaction():
                            newest_business_order: BusinessOrder | None = (
                                db.session.query(BusinessOrder)
                                .filter(
                                    BusinessOrder.id == business_order.id,
                                )
                                .first()
                            )
                            if newest_business_order is None:
                                failed_business_order_ids.append({"id": business_order.id, "reason": "工单已删除"})
                                continue
                            bosc = BusinessOrderStatusController(
                                business_order=newest_business_order,
                                operator=operator,
                                is_admin=is_admin,
                            )
                            JobAction(
                                business_order.current_job_id,
                                business_order_status_controller=bosc,
                            ).save(bo_data, operator, "批量更新")
                            succeeded_business_order_ids.append(business_order.id)
                    except ScenarioError as e:
                        failed_business_order_ids.append({"id": business_order.id, "reason": e.biz_display})
                    except Exception as e:
                        logger.exception(f"工单 ID {business_order.id} 更新失败: {e}")
                        failed_business_order_ids.append({"id": business_order.id, "reason": "更新操作执行失败"})
                        continue
    return (
        jsonify(
            success=True,
            succeeded_business_order_ids=succeeded_business_order_ids,
            failed_business_order_ids=failed_business_order_ids,
            invalid_file_urls=invalid_file_urls,
        ),
        200,
    )
