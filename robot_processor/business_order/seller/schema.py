"""API Schema"""
import abc
from typing import Optional, List, Any, Dict
from enum import StrEnum

from loguru import logger
from pydantic import BaseModel, PositiveInt, Field, root_validator

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.business_order.models import BusinessOrder, Job
from robot_processor.client import kiosk_client
from robot_processor.currents import g
from robot_processor.enums import (
    PageSort, Creator, AssigneeRule, BusinessOrderStatus,
    StepType
)
from robot_processor.form.models import WidgetInfo
from robot_processor.utils import ts2date


class SidListMixin(BaseModel, abc.ABC):
    """该 mixin 定义的 sid_list 参数用来为部分查询类请求指定店铺列表，如果没有显式指定，则根据当前登录用户的角色自动填充ta能访问的所有店铺sid。

    changelog:
    1. 2024-3-19 !3500 为了简化去 accessor 改造 #311 ，引入了这个 mixin，命名为 AccessorMixin
    2. 2024-3-28 !3555 引入 sid_list，以更明确地反应 API 业务语义
    3. 2024-4-2 !3588 完成从 accessors 到 sid_list 的转换，删除所有 accessors 字段，该 class 更名为 SidListMixin
    """
    sid_list: List[str] = Field(default_factory=list)

    @root_validator(pre=True)
    def set_sid_list_against_login_user_and_accessors(cls, v: dict) -> dict:
        # NOTE: 完成前端改造后，accessors 的处理可以删除
        accessors = v.get("accessors")
        sid_list = v.get("sid_list")
        logger.info(f"accessors: {accessors}, sid_list: {sid_list}")
        if not accessors:
            if g.auth.login_user_type == Creator.ASSISTANT:
                if not sid_list:
                    # 客服从侧边栏登录时，默认只能看到当前店的工单/工单模板等信息
                    v['sid_list'] = [g.auth.store_id]
            else:
                # 客服从网页工单等入口登录时，除非显式过滤店铺，否则可以看到所在租户的所有店铺的工单/工单模板等信息
                if not sid_list:
                    # 客服从网页工单等场景登录时，默认展示当前用户被授权的所有店铺的工单/工单模板等信息
                    v['sid_list'] = kiosk_client.get_granted_store_ids_for_user(g.auth.user_id, g.auth.org_id)
        elif accessors and not sid_list:
            v["sid_list"] = list(set([a.get('sid') for a in accessors if a.get('sid')]))
        else:
            logger.error("请求参数中同时提供了 accessors 和 sid_list.")
        return v


class BusinessOrdersListRequest(SidListMixin):
    page: Optional[PositiveInt] = 1
    per_page: Optional[PositiveInt] = 10
    tab: str
    buyer_nick: Optional[str] = None
    sort: str = PageSort.DESC.name
    keyword: list[str] | str | None

    form_id: Optional[str]
    form_ids: Optional[List[int]]
    step_name: Optional[str]
    filter_close: Optional[bool] = False
    picked: bool = False  # 是否已领取
    status: Optional[BusinessOrderStatus] = None
    flag: Optional[str] = None  # 工单插旗颜色

    class Config:
        smart_union = True


class BusinessOrdersDetailRequest(SidListMixin):
    tab: str = "ALL"
    picked: bool = False  # 是否已领取
    status: Optional[BusinessOrderStatus]


class BusinessOrdersStatRequest(SidListMixin):
    keyword: Optional[str]


def _unprocessed_job(jobs: List[Job]):
    """待处理"""
    assign_jobs = [job for job in jobs if job.is_unprocessed()]
    return assign_jobs[-1] if assign_jobs else None


def _processed_job(jobs: List[Job]):
    """已处理"""
    assign_jobs = [job for job in jobs if job.is_processed()]
    return assign_jobs[0] if assign_jobs else None


def _assigned_job_info(jobs, process: bool):
    job = _processed_job(jobs) if process else _unprocessed_job(jobs)
    if job:
        return True, {
            "id": job.id,
            "process_mark": job.get_process_mark().value,
            "updated_at": ts2date(job.updated_at)
        }
    return False, {}


def common_to_order_list(bo: BusinessOrder, jobs: list, user_id=None, show_visited=False):
    current_job = bo.current_job

    coordinator_list = job_coordinator_list(bo.form.co_edit, current_job)  # type: ignore[union-attr]
    coordinator_leyan_user_ids = [user.user_id for user in coordinator_list if user.user_type == Creator.LEYAN]
    curr_actionable = user_id in coordinator_leyan_user_ids

    is_unprocessed, unprocessed_job_info = _assigned_job_info(
        jobs, False)
    is_unprocessed = is_unprocessed and curr_actionable
    is_processed, processed_job_info = _assigned_job_info(
        jobs, True)

    if current_job:
        job = current_job.to_dict()
    else:
        job = {}
    form = bo.form_wrapper
    return {
        "id": bo.id,
        "name": form.name,
        "form_deleted": form.deleted,
        "updated_at": ts2date(bo.updated_at),
        "created_at": ts2date(bo.created_at),
        "status": bo.status.value,
        "data": bo.data,
        "is_unprocessed": is_unprocessed,
        "is_processed": is_processed,
        "unprocessed_job_info": unprocessed_job_info if is_unprocessed else {},
        "processed_job_info": processed_job_info,
        "uid": bo.uid,
        "buyer_open_uid": bo.buyer_open_uid,
        "shop": bo.shop.brief(),  # type: ignore[union-attr]
        "deadline_info": bo.deadline_info,
        "current_jobs": [job],
        "extra_data": bo.extra_data or {},
        "visited": current_job.is_visited(user_id) if (show_visited and user_id) else True,  # type: ignore[union-attr]  # noqa: E501
        "curr_actionable": curr_actionable,
        "flag": bo.flag
    }


def select_jobs_by_user_id(bo: BusinessOrder, user_id: int | None):
    """根据用户id筛选工单下的任务"""
    all_jobs: list[Job] = [job for job in bo.all_jobs()]
    # 如果是协作类工单，找出工单下的所有 job
    if bo.form.co_edit:  # type: ignore[union-attr]
        jobs = all_jobs
    # 如果是非协作类工单，找出分派给了指定客服的 job
    elif user_id is None:
        return []
    else:
        jobs = [job for job in all_jobs if job.assignee_user_id == user_id and job.assignee_type == Creator.LEYAN]
    jobs.sort(key=lambda job: (2 if job.is_unprocessed() else 1, job.updated_at), reverse=True)
    return jobs


def to_order_list_by_user_id(bo: BusinessOrder, user_id=None, show_visited=False):
    jobs = select_jobs_by_user_id(bo, user_id)
    return common_to_order_list(bo, jobs, user_id, show_visited=show_visited)


class BuyerBoSortType(StrEnum):
    CREATED_AT_DESC = "CREATEDESC"
    CREATED_AT_ASC = "CREATEASC"
    UPDATED_AT_DESC = "UPDATEDESC"
    UPDATED_AT_ASC = "UPDATEASC"


class BuyerBusinessOrdersRequest(BaseModel):
    """买家相关的工单"""
    buyer_nick: str
    status: List[PositiveInt]
    page: Optional[PositiveInt] = 1
    per_page: Optional[PositiveInt] = 10
    keyword: Optional[str]
    bo_status: Optional[BusinessOrderStatus]
    buyer_open_uid: Optional[str]
    sort_type: BuyerBoSortType | None


def _job_history_info(jobs: List[Job]) -> dict[int, Dict[str, Any]]:
    res = {}
    ui_schema: list[dict] = []
    for job in jobs:
        raw_step_v2 = job.raw_step_v2
        job_ui_schema = raw_step_v2.get("ui_schema", [])
        WidgetInfo.Utils.inplace_before_option_value(job_ui_schema, ui_schema)
        job_info = {
            "id": job.id,
            "name": raw_step_v2.get("name"),
            "uuid": raw_step_v2.get("step_uuid"),
            "next_uuids": raw_step_v2.get("next_step_ids"),
            "prev_uuids": raw_step_v2.get("prev_step_ids"),
            "status": job.status.value,
            "step_type": job.step_type,
            "step_id": job.step_id,
            "process_mark": job.get_process_mark().value,
            "assignee": job.assignee,
            "assignee_type": job.assignee_type.value,  # type: ignore[union-attr]
            "assignee_user_id": job.assignee_user_id,
            "created_at": ts2date(job.created_at),
            "updated_at": ts2date(job.updated_at),
            "data": {},
            "ui_schema": job_ui_schema,
            "notifier_enabled": (raw_step_v2.get("notifier", {}) or {}).get("enabled", False),  # noqa
            "deadline_info": job.deadline_info,
            "display_rule": raw_step_v2.get('display_rule', [])
        }
        if job.step_type == StepType.iterate_gw_begin:
            widget_info = BusinessOrder.Utils.get_widget_info(
                job.business_order, job.widget_ref.key
            )
            if job.widget_ref.type == "array":
                data = job.business_order.data_wrapper.get(job.widget_ref, [])
            else:
                data = job.business_order.data_wrapper.get(job.widget_ref.raw_query, [])
            job_info["iterate_gateway"] = {
                "widget_ref": job.widget_ref.dict(),
                "data": data,
                "ui_schema": widget_info.brief() if widget_info else None,
            }
        if job.step_type == StepType.jump:
            step = job.step
            if step is None:
                logger.error(f"{job.id} 未找到对应步骤")
                job_info["jump"] = {}
            else:
                job_info["jump"] = step.jump
        res.update({job.id: job_info})
        WidgetInfo.Utils.extend_ui_schema(ui_schema, job_ui_schema)
    return res


def job_coordinator_list(form_co_edit: bool, job: Optional[Job]) -> List[AccountDetailV2]:
    if not job:
        return []
    if not (job.assignee_user_id and job.assignee_type):
        # 未分派的任务
        return job.get_candidate_assistants()
    current_assistant = AccountDetailV2(
        user_type=job.assignee_type.value,
        user_id=job.assignee_user_id,
        user_nick=job.assignee,
    )
    if not form_co_edit:
        return [current_assistant]

    assistants = job.get_candidate_assistants()
    if current_assistant not in assistants:  # 指派的客服不在候选列表
        assistants.append(current_assistant)
    return assistants


def to_detail_info(bo: BusinessOrder, job=None) -> 'BusinessOrderDetail':
    """
        详情页
        user_nicks: [2@客服nick, 4@乐言nick]
    """
    from robot_processor.form.models import FormVersion

    curr_job: Job = job if job else bo.current_job

    extra_data = bo.extra_data or {}
    current_job_info = {
        "id": curr_job.id,
        "step_id": curr_job.step_id,
        "assignee": curr_job.assignee,  # 用于判断 login_users 是编辑还是代理
        "account_type": curr_job.assignee_type.value,  # type: ignore[union-attr]
        "assignee_user_id": curr_job.assignee_user_id,
        # 退回/关闭/重启 操作记录及评论
        "operate_action": extra_data.get("operate_action", ""),
        "operate_reason": extra_data.get("operate_reason", ""),
        # 指派操作记录。
        "tips": curr_job.get_tips(),
    } if curr_job else {}

    next_job_info = {}
    if curr_job and curr_job.has_next:
        next_job = curr_job.next
        if next_job and next_job.is_human():
            next_job_info = {
                "id": next_job.id,
                "step_id": next_job.step_id,
                # 兼容老数据，默认自动分派
                "assignee_rule": next_job.raw_step_v2.get("assignee_rule", AssigneeRule.RANDOM.value)
            }
    version = FormVersion.View.BusinessOrderBasic.from_orm(bo.get_form_version())
    job_transition = [item.dict() for item in bo.job_transition_wrapper.views()]

    job_brief_mapping = _job_history_info(bo.all_jobs())
    return BusinessOrderDetail(
        id=bo.id,
        form_id=bo.form_id,
        name=version.meta.get("name") or bo.form.name,  # type: ignore[union-attr]
        created_at=ts2date(bo.created_at),
        created_by=bo.aid,
        updated_at=ts2date(bo.updated_at),
        update_user=bo.update_user,
        status=bo.status.value,
        uid=bo.uid,
        data=bo.data,
        current_job=current_job_info,
        next_job=next_job_info,
        job_history=[job_brief_mapping.get(job_id) for job_id in bo.job_history],
        job_transition=job_transition,
        shop=bo.shop.brief(),  # type: ignore[union-attr]
        deadline_info=bo.deadline_info,
        version=version.dict(),
    )


class FreePickStats(BaseModel):
    UNPICKED_COUNT: int = 0
    PICKED_COUNT: int = 0


class BusinessOrdersListTimeOutsResponse(BaseModel):
    untimeout_count: Optional[int]
    timeout_count: Optional[int]
    unset_count: Optional[int]


class JobBasicInfo(BaseModel):
    id: Optional[int]
    process_mark: Optional[int]
    updated_at: Optional[str]


class JobBriefInfo(BaseModel):
    status: Optional[Any]
    step_name: Optional[Any]
    task_id: Optional[Any]
    step_uuid: Optional[str]
    step_id: Optional[int]
    form_id: Optional[int]
    job_id: Optional[int]
    can_reject: Optional[bool]
    next_step_ids: Optional[List[Any]]
    prev_step_ids: Optional[List[Any]]
    step_status: Optional[Any]
    assignee: Optional[str]
    data: Optional[dict]
    has_tracing_remind: Optional[bool]
    process_mark: Optional[int]


class BusinessOrderExtraData(BaseModel):
    operate_action: Optional[str]
    operate_reason: Optional[str]


class BusinessOrderResponse(BaseModel):
    id: int
    name: Optional[str]
    form_deleted: Optional[bool] = False
    updated_at: Optional[str]
    created_at: Optional[str]
    status: int
    data: Optional[dict]
    is_unprocessed: Optional[bool]
    is_processed: Optional[bool]
    unprocessed_job_info: Optional[JobBasicInfo]
    processed_job_info: Optional[JobBasicInfo]
    uid: Optional[str]
    buyer_open_uid: Optional[str]
    shop: Optional[dict]
    deadline_info: Optional[dict]
    current_jobs: Optional[List[JobBriefInfo]]
    extra_data: Optional[BusinessOrderExtraData]
    visited: Optional[bool] = False
    curr_actionable: Optional[bool] = False
    flag: Optional[str]


class BusinessOrdersListResponse(BaseModel):
    data: List[BusinessOrderResponse]
    pages: int
    per_page: int
    page: int
    total: int
    timeout: Optional[BusinessOrdersListTimeOutsResponse]


class BusinessOrderStats(BaseModel):
    UNPROCESSED: int = 0
    PROCESSED: int = 0
    ALL: int = 0
    CREATED: int = 0
    RELATE: int = 0
    TO_BE_APPROVE: int = 0


class BusinessOrderDataInspectRequest(BaseModel):
    widget_key: str
    value: Any
    form_id: Optional[int]
    sid: Optional[str]


class BusinessOrderDataInspectResponse(BaseModel):
    business_order_ids: List[str] = []


class BatchBusinessOrderBriefRequest(BaseModel):
    business_order_ids: List[int] = []
    step_uuid: Optional[str]


class BusinessOrderDetail(BaseModel):
    id: int
    form_id: int
    name: Optional[str]
    created_at: Optional[str]
    created_by: Optional[str]
    updated_at: Optional[str]
    update_user: Optional[str]
    status: Optional[int]
    uid: Optional[str]
    data: dict
    actions: Optional[dict] = {}
    current_job: Optional[dict] = {}
    next_job: Optional[dict]
    job_history: Optional[List] = []
    shop: Optional[dict] = {}
    deadline_info: Optional[dict]
    job_transition: List[dict]
    version: dict


class GetSingleBusinessOrderDetailResponse(BaseModel):
    data: BusinessOrderDetail


class BatchBusinessOrderBriefResponse(BaseModel):
    data: List[BusinessOrderDetail]


class SkipRequiredInputsResponse(BaseModel):
    data: List[dict]  # widget_info ui_schema
