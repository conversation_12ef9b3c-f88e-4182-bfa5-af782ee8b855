import typing as t

from flask import Blueprint
from flask import jsonify
from loguru import logger
from pydantic import BaseModel
from sqlalchemy import case
from sqlalchemy import or_

from robot_processor.base_schemas import Failed
from robot_processor.business_order.encryption.address_widget import AddressWidgetMask
from robot_processor.business_order.encryption.exceptions import AddressWidgetError
from robot_processor.business_order.handlers import form_widget_wrapper
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import BusinessOrderTradeMap
from robot_processor.business_order.models import Job
from robot_processor.business_order.models import JobPool
from robot_processor.business_order.seller.enums import MyBusinessOrdersTab
from robot_processor.business_order.seller.query_by_accessor import AccessorQuery
from robot_processor.business_order.seller.schema import BatchBusinessOrderBriefRequest
from robot_processor.business_order.seller.schema import BatchBusinessOrderBriefResponse
from robot_processor.business_order.seller.schema import BusinessOrderDataInspectRequest
from robot_processor.business_order.seller.schema import BusinessOrderDataInspectResponse
from robot_processor.business_order.seller.schema import BusinessOrderDetail
from robot_processor.business_order.seller.schema import BusinessOrdersListRequest
from robot_processor.business_order.seller.schema import BusinessOrdersListResponse
from robot_processor.business_order.seller.schema import BusinessOrdersStatRequest
from robot_processor.business_order.seller.schema import BusinessOrderStats
from robot_processor.business_order.seller.schema import BuyerBoSortType
from robot_processor.business_order.seller.schema import BuyerBusinessOrdersRequest
from robot_processor.business_order.seller.schema import FreePickStats
from robot_processor.business_order.seller.schema import GetSingleBusinessOrderDetailResponse
from robot_processor.business_order.seller.schema import SkipRequiredInputsResponse
from robot_processor.business_order.seller.schema import to_detail_info
from robot_processor.business_order.seller.schema import to_order_list_by_user_id
from robot_processor.client import app_config
from robot_processor.client import asgard_client
from robot_processor.client import heart_beat_client
from robot_processor.currents import g
from robot_processor.decorators import org_required
from robot_processor.decorators import shop_required
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import Creator
from robot_processor.ext import db
from robot_processor.form.models import Form
from robot_processor.job.skip_helper import SkipHelper
from robot_processor.provider.schema import PrefilledReqSchema
from robot_processor.provider.schema import PrefilledRespSchema
from robot_processor.shop.models import Shop
from robot_processor.users.models import UserLastAccess
from robot_processor.utils import unwrap_optional
from robot_processor.validator import validate

api = Blueprint("seller-business-order-api", __name__)


@api.errorhandler(AddressWidgetError)
def bad_request_handler(e: AddressWidgetError):
    return jsonify(reason=e.description), 400


@api.post("/business_orders")
@org_required
@validate
def get_list(body: BusinessOrdersListRequest) -> tuple[dict, int] | BusinessOrdersListResponse:
    """我的工单列表"""
    logger.bind(k=str(body.tab)).info("获取工单列表")
    try:
        page_tab = MyBusinessOrdersTab[body.tab.upper()]
    except KeyError:
        return dict(reason="非法tab页"), 400

    sort_name = body.sort.upper()
    finder = AccessorQuery(body.sid_list, g.auth.user_id, g.auth.org_id)
    error, data, timeout = finder.business_orders(
        page_tab,
        body.keyword,
        body.filter_close,
        sort_name,
        body.form_id,
        body.form_ids,
        body.step_name,
        body.page,
        body.per_page,
        picked=body.picked,
        status=body.status,
        flag=body.flag,
    )
    if error:
        return dict(reason=error), 400
    data = data or []
    return (
        dict(
            data=AddressWidgetMask().mask_batch(data),
            pages=100,
            per_page=body.per_page,
            page=body.page,
            total=1000,
            timeout=timeout,
        ),
        200,
    )


@api.post("/business_orders/<business_order_id>")
@org_required
@validate
def get_detail(business_order_id) -> tuple[dict, int] | GetSingleBusinessOrderDetailResponse:  # noqa
    """工单详情"""
    bo = (
        BusinessOrder.query.filter(BusinessOrder.sid.in_(g.org_sids), BusinessOrder.deleted.isnot(True))
        .filter_by(id=business_order_id)
        .first_or_404()
    )

    assert g.login_user_detail
    data = to_detail_info(bo)
    leyan_user = g.login_user_detail.get_bound_leyan_user() if g.login_user_detail else None
    if leyan_user:
        logger.debug(f"{leyan_user.user_id} visit business_order {business_order_id} job {bo.current_job_id}")
        bo.current_job.visit(leyan_user.user_id)  # type: ignore[union-attr]
    db.session.commit()
    return GetSingleBusinessOrderDetailResponse(data=AddressWidgetMask().mask_single(data))


@api.post("/business_orders/stats")
@org_required
@validate
def get_stat(body: BusinessOrdersStatRequest) -> tuple[dict, int] | BusinessOrderStats:
    """ "工单列表的统计"""
    keyword = body.keyword
    accessor_query = AccessorQuery(body.sid_list, g.auth.user_id, g.auth.org_id)
    return accessor_query.business_orders_stats(keyword)


@api.post("/buyer_business_orders")
@org_required
@validate
def get_buyer_orders_list(body: BuyerBusinessOrdersRequest) -> tuple[dict, int] | BusinessOrdersListResponse:
    """plugin 买家的工单实例"""
    page = body.page
    per_page = body.per_page
    buyer_nick = body.buyer_nick
    buyer_open_uid = body.buyer_open_uid

    sid = g.auth.store_id

    bo_status = [BusinessOrderStatus(value) for value in body.status if BusinessOrderStatus.check(value)]
    if not bo_status:
        return dict(reason="非法status"), 400

    if buyer_open_uid and buyer_nick:
        buyer_filter = or_(BusinessOrder.buyer_open_uid == buyer_open_uid, BusinessOrder.uid == buyer_nick)
    elif buyer_open_uid:
        buyer_filter = BusinessOrder.buyer_open_uid == buyer_open_uid
    else:
        buyer_filter = BusinessOrder.uid == buyer_nick

    query = (
        BusinessOrder.query.filter(BusinessOrder.sid == sid, BusinessOrder.deleted.isnot(True))
        .filter(buyer_filter)
        .filter(BusinessOrder.status.in_(bo_status))
    )

    bo_status_sort_expr = case(
        (BusinessOrder.status == BusinessOrderStatus.RUNNING, 1),
        (BusinessOrder.status == BusinessOrderStatus.SUCCEED, 2),
        (BusinessOrder.status == BusinessOrderStatus.CLOSE, 3),
        (BusinessOrder.status == BusinessOrderStatus.TO_BO_SUBMITTED, 4),
        (BusinessOrder.status == BusinessOrderStatus.IN_EXCEPTION, 5),
        else_=6,
    ).asc()

    sort_func = BusinessOrder.created_at.desc()
    match body.sort_type:
        case BuyerBoSortType.CREATED_AT_ASC:
            sort_func = BusinessOrder.created_at.asc()
        case BuyerBoSortType.CREATED_AT_DESC:
            sort_func = BusinessOrder.created_at.desc()
        case BuyerBoSortType.UPDATED_AT_ASC:
            sort_func = BusinessOrder.updated_at.asc()
        case BuyerBoSortType.UPDATED_AT_DESC:
            sort_func = BusinessOrder.updated_at.desc()

    # 进行中 > 已完成 > 已关闭
    # 每类内部, 创建时间早 > 创建时间晚
    query = query.order_by(bo_status_sort_expr, sort_func)
    if body.keyword:
        keyword = body.keyword.strip()
        query = query.outerjoin(BusinessOrderTradeMap, BusinessOrderTradeMap.business_order_id == BusinessOrder.id)
        query = query.filter(
            or_(
                BusinessOrder.v_oid == keyword,
                BusinessOrder.v_tid == keyword,
                BusinessOrderTradeMap.tid_or_oid == keyword,
            )
        )
    if body.bo_status:
        query = query.filter(BusinessOrder.status == body.bo_status)

    paginate = query.paginate(page=page, per_page=per_page, cached=True, timeout=10)
    data = [to_order_list_by_user_id(bo, g.auth.user_id) for bo in paginate.items]
    return (
        dict(
            data=data,
            pages=paginate.pages,
            per_page=paginate.per_page,
            page=paginate.page,
            total=paginate.total,
        ),
        200,
    )


@api.post("/business_orders/free_pick_stats")
@org_required
@validate
def get_self_pick_stat(body: BusinessOrdersStatRequest) -> FreePickStats:
    """任务中心统计"""
    user_id = g.auth.user_id
    db_query = (
        db.session.query(BusinessOrder.id)
        .select_from(JobPool)
        .join(BusinessOrder, JobPool.business_order_id == BusinessOrder.id)
        .join(Job, JobPool.job_id == Job.id)
        .filter(JobPool.assignee_user_id == user_id, JobPool.assignee_user_type == Creator.LEYAN)
        .filter(BusinessOrder.deleted.isnot(True))
    )

    if body.sid_list:
        db_query = db_query.filter(BusinessOrder.sid.in_(set(body.sid_list)))
    unpicked_query = db_query.filter(
        Job.assignee_user_id.is_(None),
        BusinessOrder.status.in_((BusinessOrderStatus.TO_BE_COLLECTED,)),
    )
    unpicked_count = unpicked_query.count()
    picked_query = db_query.filter(
        Job.assignee_user_id.is_not(None),
        BusinessOrder.status.in_((BusinessOrderStatus.PENDING,)),
    )
    picked_count = picked_query.count()
    return FreePickStats(UNPICKED_COUNT=unpicked_count, PICKED_COUNT=picked_count)


class HeartBeatResponse(BaseModel):
    min_plugin_version: str


@api.put("/heart-beat")
@org_required
@validate
def heat_beat() -> HeartBeatResponse:
    """标记用户在线状态，标记结果可用于筛选"当前在线客服"。

    :return: 返回最低插件版本号，用于提示用户更新千牛侧边栏插件。ref: https://git.leyantech.com/fed/mini-sidebar/-/issues/13
    """
    UserLastAccess.record_http_access()
    heart_beat_client.mark_users_online([g.auth.user_id])
    return HeartBeatResponse(min_plugin_version=app_config.MIN_PLUGIN_VERSION)


@api.post("/business_orders/prefilled")
@shop_required
@validate
def prefilled(body: PrefilledReqSchema) -> PrefilledRespSchema:
    shop: Shop = g.shop
    if not body.check():
        return PrefilledRespSchema()

    if not body.data_keys:
        body.data_keys = [
            PrefilledReqSchema.ProductDataSchema(spu_id=body.spu_id, sku_id=body.sku_id if body.sku_id else "")
        ]

    form = Form.query.get_or_404(body.form_id)
    widgets = form_widget_wrapper(form, only_header=True) if form else []
    provider_configs = []
    for widget in widgets:
        if not (ref_config := widget.get("ref_config")):
            continue
        for config in ref_config:
            config.update({"key": widget["key"]})
        provider_configs.extend(ref_config)
    ret = asgard_client.prefilled(shop.sid, provider_configs, body.dict())
    return PrefilledRespSchema(**ret) if ret else PrefilledRespSchema()


@api.post("/business_orders/data-inspect")
@api.post("/business_orders/<business_order_id>/data-inspect")
@shop_required
@validate
def data_inspect(
    body: BusinessOrderDataInspectRequest, business_order_id=None
) -> tuple[Failed, int] | BusinessOrderDataInspectResponse:  # noqa
    from robot_processor.client import risk_control_client
    from robot_processor.form.getter import get_non_ref_widgets_by_form_id

    sid = body.sid
    if business_order_id:
        bo = BusinessOrder.query.get_or_404(business_order_id)
        widgets = bo.get_non_ref_widgets()
        form_id = unwrap_optional(bo.form_id)
        sid = sid or bo.sid
    elif body.form_id:
        widgets = get_non_ref_widgets_by_form_id(body.form_id)
        form_id = body.form_id
    else:
        return Failed.bad_request("需指定工单模板或者工单")

    sid = sid or g.shop.sid  # 兜底，使用 token 中的 sid 有可能是错误的
    form = Form.query.get(form_id)
    if not form:
        logger.warning(f"form {form_id} 不存在")
        return BusinessOrderDataInspectResponse(business_order_ids=[])
    else:
        result = risk_control_client.check_widget_value_unique(
            g.shop.org_id,
            sid,
            form_id,
            business_order_id,
            {body.widget_key: body.value},  # type: ignore[arg-type]
            widgets=widgets,
        )
        return BusinessOrderDataInspectResponse(business_order_ids=result.get(body.widget_key, []))


@api.post("/business_orders/get_by_step_uuid")
@org_required
@validate
def batch_get_detail(body: BatchBusinessOrderBriefRequest) -> BatchBusinessOrderBriefResponse:
    """批量工单详情"""
    query = (
        db.session.query(BusinessOrder, Job)
        .select_from(BusinessOrder)
        .join(Job, Job.business_order_id == BusinessOrder.id)
        .filter(
            BusinessOrder.sid.in_(g.org_sids),
            BusinessOrder.deleted.isnot(True),
            BusinessOrder.id.in_(body.business_order_ids),
        )
    )
    if body.step_uuid:
        query = query.filter(Job.step_uuid == body.step_uuid)
    data: t.List[BusinessOrderDetail] = []
    for res in query:
        data.append(to_detail_info(res.BusinessOrder, job=res.Job))
    data = t.cast(t.List[BusinessOrderDetail], AddressWidgetMask().mask_batch(data))
    return BatchBusinessOrderBriefResponse(data=data)


@api.get("/skip_inputs/<job_id>")
@validate
def get_skip_required_inputs(job_id=None) -> SkipRequiredInputsResponse:
    """
    获取跳过某步骤时必须的参数
    """
    job = Job.query.get_or_404(job_id)
    required_inputs = list(SkipHelper(job).get_skip_required_inputs(job.step))  # type: ignore[arg-type]
    return SkipRequiredInputsResponse(data=required_inputs)
