from dataclasses import asdict

import robot_types.core
import robot_types.helper
from flask import Blueprint
from flask import make_response
from loguru import logger
from pydantic import BaseModel
from pydantic import root_validator

from robot_processor.currents import g
from robot_processor.decorators import mini_app_required
from robot_processor.decorators import org_required
from robot_processor.form.symbol_table import Value
from robot_processor.function import function_mapper
from robot_processor.logging import to_log
from robot_processor.utils import GenericResponse
from robot_processor.utils import wrap_status_code_in_response_body
from robot_processor.validator import validate

router = Blueprint("function-api", __name__)
mini_app_router = Blueprint("mini-app-function-api", __name__)


class CallFunctionRequest(BaseModel):
    fn: Value.FnValue
    context: dict | None = None

    class Shop(BaseModel):
        sid: str
        platform: str

    shop: Shop | None = None

    @property
    def fn_impl(self):
        fn_def = function_mapper[self.fn.function]
        return robot_types.core.Value(
            type_spec=fn_def.signature.rtype,  # type: ignore[attr-defined]
            fn=robot_types.core.Fn(
                name=self.fn.function,
                params={
                    arg.name: robot_types.helper.deserialize(arg.value.dict(), robot_types.core.Value)
                    for arg in self.fn.args
                },
            ),
        )

    @root_validator(skip_on_failure=True)
    def compatible_for_erp_credential_arg(cls, values):
        from robot_processor.enums import ErpType
        from robot_processor.function import jackyun
        from robot_processor.function import jst
        from robot_processor.function import wdt
        from robot_processor.function import wdtulti
        from robot_processor.function import wln
        from robot_processor.function import baisheng
        from robot_processor.shop.models import ErpInfo
        from rpa.erp.jst import config as jst_config

        # 当缺少 credential 时，从 shop 获取默认配置
        if not values["shop"]:
            return values

        if values["fn"].function == wdt.GetTradeInfo.Signature.name:
            for arg in values["fn"].args:
                if arg.name == wdt.GetTradeInfo.Args.CREDENTIAL.get_name():
                    # 入参有 credential, 无需处理
                    break
            else:
                # 入参无 credential, 从 shop 中获取
                if erp_info := ErpInfo.get_by_sid(values["shop"].sid, ErpType.WDT):
                    values["fn"].args.append(
                        Value.FnValue.Arg(
                            name=wdt.GetTradeInfo.Args.CREDENTIAL.get_name(),
                            value=Value.build_const(wdt.GetTradeInfo.Args.CREDENTIAL.get_type_spec(), erp_info.meta),
                        )
                    )
        elif values["fn"].function == wdtulti.GetTradeInfo.Signature.name:
            for arg in values["fn"].args:
                if arg.name == wdtulti.GetTradeInfo.Args.CREDENTIAL.get_name():
                    # 入参有 credential, 无需处理
                    break
            else:
                # 入参无 credential, 从 shop 中获取
                if erp_info := ErpInfo.get_by_sid(values["shop"].sid, ErpType.WDTULTI):
                    values["fn"].args.append(
                        Value.FnValue.Arg(
                            name=wdtulti.GetTradeInfo.Args.CREDENTIAL.get_name(),
                            value=Value.build_const(
                                wdtulti.GetTradeInfo.Args.CREDENTIAL.get_type_spec(), erp_info.meta
                            ),
                        )
                    )
        elif values["fn"].function == baisheng.GetTradeInfo.Signature.name:
            for arg in values["fn"].args:
                if arg.name == baisheng.GetTradeInfo.Args.CREDENTIAL.get_name():
                    # 入参有 credential, 无需处理
                    break
            else:
                # 入参无 credential, 从 shop 中获取
                if erp_info := ErpInfo.get_by_sid(values["shop"].sid, ErpType.BAISHENG):
                    values["fn"].args.append(
                        Value.FnValue.Arg(
                            name=baisheng.GetTradeInfo.Args.CREDENTIAL.get_name(),
                            value=Value.build_const(
                                baisheng.GetTradeInfo.Args.CREDENTIAL.get_type_spec(), erp_info.meta
                            ),
                        )
                    )
        elif values["fn"].function == wln.GetTradeInfo.Signature.name:
            for arg in values["fn"].args:
                if arg.name == wln.GetTradeInfo.Args.CREDENTIAL.get_name():
                    break
            else:
                if erp_info := ErpInfo.get_by_sid(values["shop"].sid, ErpType.WANLINIU):
                    values["fn"].args.append(
                        Value.FnValue.Arg(
                            name=wln.GetTradeInfo.Args.CREDENTIAL.get_name(),
                            value=Value.build_const(wln.GetTradeInfo.Args.CREDENTIAL.get_type_spec(), erp_info.meta),
                        )
                    )
        elif values["fn"].function in [jst.GetTradeInfo.Signature.name, jst.GetOutTradeInfo.Signature.name]:
            for arg in values["fn"].args:
                if arg.name == jst.GetTradeInfo.Args.CREDENTIAL.get_name():
                    break
            else:
                if erp_info := ErpInfo.get_by_sid(values["shop"].sid, ErpType.JST):
                    credential = erp_info.meta
                    if credential.get("auth_type") == "isv":
                        credential["partner_id"] = jst_config.JST_ISV_APP_KEY
                        credential["partner_key"] = jst_config.JST_ISV_APP_SECRET
                    else:
                        credential["partner_id"] = jst_config.JST_PARTNER_ID
                        credential["partner_key"] = jst_config.JST_PARTNER_KEY

                    values["fn"].args.append(
                        Value.FnValue.Arg(
                            name=jst.GetTradeInfo.Args.CREDENTIAL.get_name(),
                            value=Value.build_const(jst.GetTradeInfo.Args.CREDENTIAL.get_type_spec(), credential),
                        )
                    )
        elif values["fn"].function == jackyun.GetTradeInfo.Signature.name:
            for arg in values["fn"].args:
                if arg.name == jackyun.GetTradeInfo.Args.CREDENTIAL.get_name():
                    # 入参有 credential, 无需处理
                    break
            else:
                # 入参无 credential, 从 shop 中获取
                if erp_info := ErpInfo.get_by_sid(values["shop"].sid, ErpType.JACKYUN):
                    values["fn"].args.append(
                        Value.FnValue.Arg(
                            name=jackyun.GetTradeInfo.Args.CREDENTIAL.get_name(),
                            value=Value.build_const(
                                jackyun.GetTradeInfo.Args.CREDENTIAL.get_type_spec(), erp_info.meta
                            ),
                        )
                    )

        return values


class CallFunctionResponse(BaseModel):
    value: Value


@router.post("/function/call")
@org_required
@validate
def call_function(body: CallFunctionRequest) -> GenericResponse[CallFunctionResponse]:
    if body.fn.function not in function_mapper:
        return GenericResponse.Failed("未找到对应函数")

    fn_args, fn_context = body.fn.args, body.context or dict()
    fn_def = function_mapper[body.fn.function]
    if hasattr(fn_def, "signature") and isinstance(fn_def.signature, robot_types.core.FnSignature):
        value_resolver = robot_types.helper.ValueResolver(context=fn_context, fn_registry=function_mapper)
        result = (
            body.fn_impl.with_resolver(value_resolver)
            .resolve()
            .map(
                lambda r: Value(
                    type_spec=asdict(fn_def.signature.rtype.to_deprecated()),
                    qualifier="const",
                    const=Value.ConstValue(value=robot_types.helper.serialize(r)),
                )
            )
        )
    else:
        fn_impl = fn_def(*fn_args).context(fn_context)  # type: ignore[abstract]
        result = fn_impl.call()
    logger.info("call function with {} return {}".format(to_log(body.dict()), to_log(result)))
    if result.is_err():
        return GenericResponse.Failed(str(result.unwrap_err()))
    else:
        return GenericResponse[CallFunctionResponse].Success(CallFunctionResponse(value=result.unwrap()))


class MiniAppCallFunctionRequest(CallFunctionRequest):
    @root_validator(pre=True)
    def set_shop(cls, values):
        shop_info: dict = g.shop  # type: ignore[assignment]
        values["shop"] = cls.Shop(sid=shop_info["sid"], platform=shop_info["platform"])
        return values


@mini_app_router.post("/function/call")
@mini_app_required
@validate
def mini_app_call_function(body: MiniAppCallFunctionRequest):
    def response(response_data: GenericResponse):
        return wrap_status_code_in_response_body(make_response(response_data.dict()))

    if body.fn.function not in function_mapper:
        return response(GenericResponse.Failed("未找到对应函数"))

    fn_args, fn_context = body.fn.args, body.context
    function = function_mapper[body.fn.function](*fn_args).context(fn_context)  # type: ignore[abstract]
    result = function.call()

    if result.is_err():
        return response(GenericResponse.Failed(str(result.unwrap_err())))

    else:
        return response(GenericResponse[CallFunctionResponse].Success(CallFunctionResponse(value=result.unwrap())))
