# 账号

## 查询任务的当前执行客服是否有效

- 接口

    `GET /jobs/<job_id>/assignee/status`

- 请求头

  | 请求头字段    | 类型   | 是否必须 | 说明           |
  | :------------ | :----- | :------- | :------------- |
  | Authorization | string | **是**   | 用户的身份信息 |

- Path 参数

  | 参数   | 类型 | 是否必须 | 说明                   |
  | :----- | :--- | :------- | :--------------------- |
  | job_id | int  | 是       | 需要查询的 Job 的 id。 |

- 响应示例及参数解析

  - 成功响应

    ```json
    {
      "success": true,
      "enabled": true
    }
    ```

    | 字段名  | 类型 | 是否可能为空 | 说明         |
    | :------ | :--- | :----------- | :----------- |
    | success | bool | 否           | 成功与否     |
    | enabled | bool | **是**       | 账号是否可用 |

  - 错误响应

    ```json
    {
        "success": false,
        "error_code": 404,
        "message": "查询不到该任务"
    }
    ```

    | 字段名     | 类型   | 是否可能为空 | 说明     |
    | :--------- | :----- | :----------- | :------- |
    | success    | bool   | 否           | 成功与否 |
    | error_code | number | **是**       | 错误码   |
    | message    | string | **是**       | 错误信息 |
