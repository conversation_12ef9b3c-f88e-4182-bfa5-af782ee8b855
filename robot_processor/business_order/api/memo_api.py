"""工单备注"""

import typing
from typing import List
from typing import Optional

import arrow
import robot_types.model
from flask import Blueprint
from flask import request
from leyan_logging.context import bind_logger_info
from more_itertools.more import first
from pydantic import BaseModel
from pydantic import Field
from pydantic import root_validator
from result import Err
from result import Ok
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.base_schemas import Response
from robot_processor.business_order.handlers import form_widget_wrapper
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import BusinessOrderMemo
from robot_processor.currents import g
from robot_processor.decorators import shop_required
from robot_processor.error.base import BizError
from robot_processor.ext import db
from robot_processor.function.trade.seller_memo import UpdateTradeSellerMemo
from robot_processor.logging.vars import BusinessOrderId
from robot_processor.utils import unwrap_optional
from robot_processor.validator import validate

memo_api = Blueprint("business-order-memo-api", __name__)


class RequestValidateError(BizError):
    def __init__(self, message: str, /, *, data=None, status_code=400, **kwargs):
        self.message = message
        self.data = data
        self.status_code = status_code
        super().__init__(message)


@memo_api.errorhandler(RequestValidateError)
def error_handler(error: RequestValidateError):
    return Response.Failed(str(error))


@memo_api.before_request
def check_business_order():
    if request.method.upper() == "GET":
        # 工单有可能被归档查询不到，查询工单备注时不需要校验
        return
    bo_id = (request.view_args or {}).get("bo_id")
    if bo_id is None or not BusinessOrder.query.get(bo_id):
        raise RequestValidateError("未找到工单")


class BusinessOrderMemoResponse(BaseModel):
    succeed: bool = Field(default=True)
    error_display: Optional[str] = Field(default=None, description="用于给用户的错误展示")
    data: Optional[BusinessOrderMemo.View.BusinessOrder] = Field(default=None)

    @classmethod
    def Success(cls, data: BusinessOrderMemo.View.BusinessOrder):
        return cls(data=data)

    @classmethod
    def Failed(cls, msg: str, data=None):
        return cls(succeed=False, error_display=msg, data=data)


@memo_api.get("/business_orders/<int:bo_id>/memo")
@bind_logger_info
@shop_required
@validate
def get_business_order_memo(bo_id: typing.Annotated[int, BusinessOrderId]) -> BusinessOrderMemoResponse:
    if memo := BusinessOrderMemo.query.get(bo_id):
        memo_view = BusinessOrderMemo.View.BusinessOrder.from_orm(memo)
    else:
        memo_view = BusinessOrderMemo.View.BusinessOrder.from_blank(bo_id)

    return BusinessOrderMemoResponse.Success(memo_view)


class NewNoteRequest(BaseModel):
    note: Optional[str]
    attachments: Optional[List[BusinessOrderMemo.Schema.Attachment]]
    sync_trade_flag: Optional[bool] = False

    @root_validator(skip_on_failure=True)
    def check_note_empty(cls, values):
        if not any([values.get("note"), values.get("attachments")]):
            raise RequestValidateError("备注内容不能为空")

        return values


def sync_to_trade_seller_memo(business_order: BusinessOrder, content: str):
    shop = unwrap_optional(business_order.shop)
    widgets = form_widget_wrapper(business_order.form, only_header=True)
    trade_no_widget = first(filter(lambda widget: widget["type"] == "order", widgets), None)
    if not trade_no_widget:
        return Err("当前工单模版无订单组件，无法同步订单备注")
    tid_list = [order["tid"] for order in business_order.data.get(trade_no_widget["key"]) or []]
    if not tid_list:
        return Err("当前工单无订单号，无法同步订单备注")
    update_fn = UpdateTradeSellerMemo(
        shop=robot_types.model.resource.Shop(sid=shop.sid, platform=shop.platform),
        content=content,
        update_policy=robot_types.model.resource.RemarkUpdatePolicy.APPEND,
    )
    failed = []
    for tid in tid_list:
        update_fn.tid = tid
        update_result = update_fn.call()
        if update_result.is_err():
            failed.append(str(update_result.unwrap_err()))
    if failed:
        return Err("；".join(failed))
    return Ok(None)


@memo_api.post("/business_orders/<int:bo_id>/memo")
@bind_logger_info
@shop_required
@validate
def create_business_order_memo(
    bo_id: typing.Annotated[int, BusinessOrderId], body: NewNoteRequest
) -> BusinessOrderMemoResponse:
    """工单备注"""
    business_order: BusinessOrder = db.session.get_one(BusinessOrder, bo_id)

    if not (memo := business_order.memo):
        memo = BusinessOrderMemo()
        db.session.add(memo)
        memo.business_order = business_order
        memo.notes = []

    note = BusinessOrderMemo.Schema.Note(user=g.login_user_detail, note=body.note, attachments=body.attachments)
    memo.notes.append(note.dict())
    flag_modified(memo, "notes")
    db.session.commit()
    memo_view = BusinessOrderMemo.View.BusinessOrder.from_orm(memo)
    # 同步至订单
    if body.sync_trade_flag and body.note:
        result = sync_to_trade_seller_memo(business_order, body.note)
        if result.is_err():
            msg = f"备注发表成功，同步至订单备注失败:{result.unwrap_err()}"
            return BusinessOrderMemoResponse.Failed(data=memo_view, msg=msg)
    return BusinessOrderMemoResponse.Success(memo_view)


class ModifyNoteRequest(BaseModel):
    # 筛选条件
    user: AccountDetailV2
    created_at: str

    note: Optional[str]
    attachments: Optional[List[BusinessOrderMemo.Schema.Attachment]]
    sync_trade_flag: Optional[bool] = False


@memo_api.patch("/business_orders/<int:bo_id>/memo")
@bind_logger_info
@shop_required
@validate
def update_business_order_memo(
    bo_id: typing.Annotated[int, BusinessOrderId], body: ModifyNoteRequest
) -> BusinessOrderMemoResponse:
    business_order: BusinessOrder = db.session.get_one(BusinessOrder, bo_id)

    if not (memo := business_order.memo):  # type: ignore[union-attr]
        raise RequestValidateError("未找到备注")

    assert g.login_user_detail
    if not _check_user_equal(g.login_user_detail, body.user):
        raise RequestValidateError(f"当前用户{g.login_user_detail.user_nick}无权限修改{body.user.user_nick}的备注")

    for note in memo.notes:
        if not _filter_by_user_and_created_at(note, body.user, body.created_at):
            continue
        formatted_note = BusinessOrderMemo.Schema.Note.parse_obj(note)
        if body.note is not None:
            formatted_note.note = body.note
        if body.attachments is not None:
            formatted_note.attachments = body.attachments
        formatted_note.updated_at = arrow.now().format("YYYY-MM-DD HH:mm:ss")
        note.update(formatted_note.dict())
        flag_modified(memo, "notes")
        db.session.commit()
        break
    else:
        raise RequestValidateError("未找到需要修改的备注信息")

    memo_view = BusinessOrderMemo.View.BusinessOrder.from_orm(memo)

    # 同步至订单
    if body.sync_trade_flag and body.note:
        result = sync_to_trade_seller_memo(business_order, body.note)
        if result.is_err():
            msg = f"备注发表成功，同步至订单备注失败:{result.unwrap_err()}"
            return BusinessOrderMemoResponse.Failed(data=memo_view, msg=msg)
    return BusinessOrderMemoResponse.Success(memo_view)


class DeleteNoteRequest(BaseModel):
    # 筛选条件
    user: AccountDetailV2
    created_at: str


@memo_api.delete("/business_orders/<int:bo_id>/memo")
@bind_logger_info
@shop_required
@validate
def delete_business_order_memo(
    bo_id: typing.Annotated[int, BusinessOrderId], body: DeleteNoteRequest
) -> BusinessOrderMemoResponse:
    business_order: BusinessOrder | None = BusinessOrder.query.get(bo_id)

    if not (memo := business_order.memo):  # type: ignore[union-attr]
        raise RequestValidateError("未找到备注")

    assert g.login_user_detail
    if not _check_user_equal(g.login_user_detail, body.user):
        raise RequestValidateError(f"当前用户{g.login_user_detail.user_nick}无权限修改{body.user.user_nick}的备注")

    for note in memo.notes:
        if not _filter_by_user_and_created_at(note, body.user, body.created_at):
            continue
        note.update(status=BusinessOrderMemo.Schema.NoteStatus.DELETED.value)
        flag_modified(memo, "notes")
        db.session.commit()
        break
    else:
        raise RequestValidateError("未找到需要删除的备注信息")

    memo_view = BusinessOrderMemo.View.BusinessOrder.from_orm(memo)
    return BusinessOrderMemoResponse.Success(memo_view)


def _filter_by_user_and_created_at(note_dict: dict, user: AccountDetailV2, created_at: str):
    try:
        note = BusinessOrderMemo.Schema.Note.parse_obj(note_dict)
    except:  # noqa
        return False
    if note.user.user_id != user.user_id:
        return False
    if note.user.user_type != user.user_type:
        return False
    if note.created_at != created_at:
        return False
    if note.status == BusinessOrderMemo.Schema.NoteStatus.DELETED.value:
        return False
    return True


def _check_user_equal(login_user, request_user):
    login_user_ident = (login_user.user_id, login_user.user_type)
    request_user_ident = (request_user.user_id, request_user.user_type)

    return login_user_ident == request_user_ident
