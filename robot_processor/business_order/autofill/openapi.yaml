openapi: 3.0.0
info:
  title: 自动填充工单数据
  version: 1.0.0
  description: https://leyan.yuque.com/digismart/manual/ahoeenq5gg7bhys9

servers:
  - url: /robot/v1

components:
  schemas:
    ApiResponse:
      title: 接口响应格式
      type: object
      properties:
        succeed:
          type: boolean
        msg:
          type: string
        data:
          type: object

    AutofillInfo:
      title: 自动填充工单信息
      type: object
      properties:
        status:
          title: 当前状态
          type: string
          enum: [ '处理中', '已完成' ]
        data:
          title: 工单数据
          description: 当 status=已完成时，返回该字段
          type: object

paths:
  /business-order/autofill:
    post:
      summary: 提交一个自动填充工单的任务
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                form_id:
                  title: 工单模板id
                  type: integer
                sid:
                  title: 店铺
                  type: string
                buyer_nick:
                  title: 买家昵称
                  type: string
                buyer_open_uid:
                  title: 买家open_uid
                  type: string

      responses:
        200:
          description: 成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - properties:
                      data:
                        type: object
                        properties:
                          task_id:
                            type: integer

    get:
      summary: 查看任务执行情况
      parameters:
        - name: task_id
          in: query
          required: true
          schema:
            type: string
      responses:
        200:
          description: 成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - properties:
                      data:
                        $ref: '#/components/schemas/AutofillInfo'
