import json
from datetime import datetime
from datetime import timed<PERSON>ta
from enum import StrEnum

from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlalchemy import DateTime
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import Integer
from sqlalchemy import String
from sqlalchemy import Text
from sqlalchemy import text as sql_text
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column

from robot_processor.db import DbBaseModel
from robot_processor.ext import db


class AutoFillFormInfo(DbBaseModel):
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    form_id: Mapped[int]
    optimized: Mapped[bool] = mapped_column(Boolean, default=False)

    # 用于 prompt 中的工单主题
    subject: Mapped[str] = mapped_column(String(64))
    # 需要自动填充的槽位信息
    slots: Mapped[list] = mapped_column(JSON)


class AutoFillRecord(DbBaseModel):
    class Status(StrEnum):
        RUNNING = "进行中"
        FINISHED = "已完成"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    form_id: Mapped[int]
    created_at: Mapped[datetime] = mapped_column(
        DateTime, server_default=sql_text("CURRENT_TIMESTAMP")
    )
    finished_at: Mapped[datetime | None] = mapped_column(DateTime)
    status: Mapped[Status] = mapped_column(
        SQLEnum(Status, native_enum=False, length=12)
    )
    seller_nick: Mapped[str] = mapped_column(String(64))
    buyer_open_uid: Mapped[str] = mapped_column(String(64))
    buyer_nick: Mapped[str] = mapped_column(String(64))
    context: Mapped[dict] = mapped_column(JSON, default=dict)
    chat_history: Mapped[str] = mapped_column(Text)
    raw_response: Mapped[dict | None] = mapped_column(JSON)
    pred: Mapped[dict | None] = mapped_column(JSON)
    order_id: Mapped[int | None]

    @classmethod
    def create(
        cls,
        form_id: int,
        seller_nick: str,
        buyer_nick: str,
        buyer_open_uid: str,
        chat_history: str,
    ):
        self = cls(
            status=AutoFillRecord.Status.RUNNING,
            form_id=form_id,
            seller_nick=seller_nick,
            buyer_open_uid=buyer_open_uid,
            buyer_nick=buyer_nick,
            chat_history=chat_history,
        )
        db.session.add(self)
        db.session.commit()
        return self

    def mark_finished(self, response):
        from robot_processor.business_order.autofill.assembler import \
            enum_from_pred
        from robot_processor.business_order.autofill.assembler import \
            multi_enum_from_pred

        self.finished_at = datetime.now()
        self.status = AutoFillRecord.Status.FINISHED
        self.raw_response = response
        if "data" in response:
            output = response["data"].get("outputs", {})
        elif "outputs" in response:
            output = response["outputs"]
        else:
            self.pred = None
            db.session.commit()
            return
        try:
            pred = json.loads(output["slot_mapping"])
            slots_def = {
                slot["key"]: slot
                for slot in AutoFillFormInfo.query.filter_by(form_id=self.form_id)
                .one()
                .slots
            }
            for key in pred:
                slot_def = slots_def.get(key)
                if not slot_def:
                    continue
                if "enum" not in slot_def:
                    pass
                if slot_def["type"] == "array[string]":
                    pred[key] = multi_enum_from_pred(pred[key])
                elif slot_def["type"] == "string":
                    pred[key] = enum_from_pred(pred[key])
            self.pred = pred
            db.session.commit()
        except Exception:
            self.pred = None

    def fill_order_id(self):
        from robot_processor.business_order.models import BusinessOrder

        order = BusinessOrder.query.filter(
            BusinessOrder.form_id == self.form_id,
            BusinessOrder.uid == self.buyer_nick,
            BusinessOrder.created_at.between(
                self.created_at.timestamp(),
                (self.created_at + timedelta(minutes=5)).timestamp(),
            ),
        ).first()
        if order:
            self.order_id = order.id
        db.session.commit()

    def recurrence(self):
        from robot_processor.business_order.autofill.assembler import \
            slots_to_pred

        form_info = AutoFillFormInfo.query.filter_by(form_id=self.form_id).one()
        print(f"form:\n{form_info.subject}")
        print(f"slots:\n{slots_to_pred(form_info.slots)}")
        print(f"chat:\n{self.chat_history}")
