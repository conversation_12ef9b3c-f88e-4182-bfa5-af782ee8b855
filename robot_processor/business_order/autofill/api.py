from flask import Blueprint
from flask import jsonify
from pydantic import BaseModel

from robot_processor.business_order.autofill.models import AutoFillRecord
from robot_processor.business_order.autofill.service import <PERSON>FillBroker
from robot_processor.ext import db
from robot_processor.utils import unwrap_optional
from robot_processor.validator import validate

route = Blueprint("business-order-autofill", __name__)


class CreatePredTaskBody(BaseModel):
    form_id: int
    buyer_nick: str
    buyer_open_uid: str
    sid: str


@route.post("/business-order/autofill")
@validate
def create_pred_task(body: CreatePredTaskBody):
    from robot_processor.shop.models import Shop

    shop = unwrap_optional(Shop.Queries.optimal_shop_by_sid(body.sid))
    grant_record = shop.get_recent_record()
    access_token = grant_record.access_token if grant_record else None
    broker = AutoFillBroker(
        body.form_id, shop.nick, body.buyer_nick, body.buyer_open_uid, access_token
    )
    result = broker.pred()
    if result.is_err():
        return jsonify(
            status_code=200, succeed=False, msg=str(result.unwrap_err()), data=None
        )
    else:
        return jsonify(
            status_code=200, succeed=True, msg="", data=dict(task_id=result.unwrap().id)
        )


class GetPredTaskResultQuery(BaseModel):
    task_id: int


@route.get("/business-order/autofill")
@validate
def get_pred_task_result(query: GetPredTaskResultQuery):
    record = db.session.get_one(AutoFillRecord, query.task_id)
    return jsonify(
        status_code=200,
        succeed=True,
        msg="",
        data=dict(status=record.status, data=record.pred),
    )
