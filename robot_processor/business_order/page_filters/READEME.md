# 筛选条件

## 筛选条件相关 API

### 查询所有筛选条件

- 接口

    `/pages/filters`

- 请求头

  | 请求头字段    | 类型   | 是否必须 | 说明           |
  | :------------ | :----- | :------- | :------------- |
  | Authorization | string | **是**   | 用户的身份信息 |

- Query 请求参数

  | 请求参数 | 类型   | 是否必须 | 说明                                                                                                                                                                             |
  | :------- | :----- | :------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
  | node     | string | 否       | 节点信息，可选值有：`ALL_TASK`, `ERROR_TASK`, `PRODUCT_CENTER`, `DATA_STATISTIC_ALL`, `PAY_ALL`, `PAY_CHECK`, `PAY_TO_PAY`, `PAY_PAID`, `PAY_CLOSE`。不填写则默认为 `ALL_TASK`。 |

- 响应示例及参数解析

  - 成功响应

    ```json
    {
        "success": true,
        "filters": [
            {
                "id": 1,
                "name": "测试用筛选条件 - 1",
                "condition": {}
            }
        ]
    }
    ```

    | 字段名      | 类型          | 是否可能为空 | 说明                 |
    | :---------- | :------------ | :----------- | :------------------- |
    | success     | bool          | 否           | 成功与否             |
    | filters     | array[object] | 否           | 筛选条件             |
    | > id        | int           | 否           | 筛选条件的 ID        |
    | > name      | string        | 否           | 自定义的筛选条件名称 |
    | > condition | object        | 否           | 筛选条件的相关信息   |

  - 错误响应

    ```json
    {
        "success": false,
        "error_code": 401,
        "message": "权限认证失败"
    }
    ```

    | 字段名     | 类型   | 是否可能为空 | 说明     |
    | :--------- | :----- | :----------- | :------- |
    | success    | bool   | 否           | 成功与否 |
    | error_code | number | **是**       | 错误码   |
    | message    | string | **是**       | 错误信息 |

### 创建用户在该租户下的筛选条件

- 接口

    `/pages/filters`

- 请求头

  | 请求头字段    | 类型   | 是否必须 | 说明               |
  | :------------ | :----- | :------- | :----------------- |
  | Authorization | string | **是**   | 用户的身份信息     |
  | Content-Type  | string | **是**   | `application/json` |

- Body 请求参数及示例

  ```json
  {
      "name": "测试用筛选条件",
      "node": "PAY_ALL",
      "condition": {}
  }
  ```

  | 请求参数  | 类型   | 是否必须 | 说明                                                                                                                                                                |
  | :-------- | :----- | :------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
  | name      | string | **是**   | 筛选条件名称。                                                                                                                                                      |
  | node      | string | **是**   | 筛选条件的应用节点信息，可选值有：`ALL_TASK`, `ERROR_TASK`, `PRODUCT_CENTER`, `DATA_STATISTIC_ALL`, `PAY_ALL`, `PAY_CHECK`, `PAY_TO_PAY`, `PAY_PAID`, `PAY_CLOSE`。 |
  | condition | object | **是**   | 筛选条件。                                                                                                                                                          |

- 响应示例及参数解析

  - 成功响应

    ```json
    {
        "success": true,
        "id": 12
    }
    ```

    | 字段名  | 类型 | 是否可能为空 | 说明          |
    | :------ | :--- | :----------- | :------------ |
    | success | bool | 否           | 成功与否      |
    | id      | int  | **是**       | 筛选条件的 ID |

  - 错误响应

    ```json
    {
        "success": false,
        "error_code": 400,
        "message": "名称重复"
    }
    ```

    | 字段名     | 类型   | 是否可能为空 | 说明     |
    | :--------- | :----- | :----------- | :------- |
    | success    | bool   | 否           | 成功与否 |
    | error_code | number | **是**       | 错误码   |
    | message    | string | **是**       | 错误信息 |

### 删除用户在该租户下的筛选条件

- 接口

    `/pages/filters/{id}`

- 请求头

  | 请求头字段    | 类型   | 是否必须 | 说明           |
  | :------------ | :----- | :------- | :------------- |
  | Authorization | string | **是**   | 用户的身份信息 |

- Path 参数

  | 路径参数 | 类型 | 是否必须 | 说明          |
  | :------- | :--- | :------- | :------------ |
  | id       | int  | **是**   | 筛选条件的 ID |

- 响应示例及参数解析

  - 成功响应

    ```json
    {
        "success": true
    }
    ```

    | 字段名  | 类型 | 是否可能为空 | 说明     |
    | :------ | :--- | :----------- | :------- |
    | success | bool | 否           | 成功与否 |

  - 错误响应

    ```json
    {
        "success": false,
        "error_code": 400,
        "message": "不合法的 ID"
    }
    ```

    ```json
    {
        "success": false,
        "error_code": 403,
        "message": "该账号无权删除"
    }
    ```

    | 字段名     | 类型   | 是否可能为空 | 说明     |
    | :--------- | :----- | :----------- | :------- |
    | success    | bool   | 否           | 成功与否 |
    | error_code | number | **是**       | 错误码   |
    | message    | string | **是**       | 错误信息 |

## 数据库表结构

| 字段名     | 类型         | 约束                              |
| :--------- | :----------- |:--------------------------------|
| id         | INT(11)      | NOT NULL, AUTO_INCREMENT, INDEX |
| created_at | INT(11)      | NOT NULL                        |
| updated_at | INT(11)      | NOT NULL                        |
| user_id    | INT(11)      | NOT NULL, INDEX                 |
| org_id     | VARCHAR(32)  | NOT NULL, INDEX                 |
| node       | VARCHAR(32)  | NOT NULL, INDEX                 |
| name       | VARCHAR(128) | NOT NULL                        |
| condition  | JSON         | NOT NULL                        |
| is_deleted | BOOLEAN      | DEFAULT FALSE                   |

```sql
CREATE TABLE `page_filter` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_at` int(11) NOT NULL,
  `updated_at` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '客服账号 ID',
  `org_id` varchar(32) NOT NULL COMMENT '租户 ID',
  `node` varchar(32) NOT NULL COMMENT '筛选条件应用的节点，根据 NodeType 这个枚举值进行存储',
  `name` varchar(128) NOT NULL COMMENT '筛选条件的名称',
  `condition` json DEFAULT NULL COMMENT '具体的筛选条件',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否被删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_org_id` (`org_id`),
  KEY `idx_node` (`node`)
);
```
