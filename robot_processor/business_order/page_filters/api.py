from typing import Optional

from flask import jsonify, Blueprint

from robot_processor.business_order.page_filters.models import PageFilter
from robot_processor.business_order.page_filters.schemas import QueryArgument, CreateFilterRequest
from robot_processor.currents import g
from robot_processor.decorators import shop_required
from robot_processor.ext import db
from robot_processor.validator import validate
from robot_processor.exceptions import HTTPException
from robot_processor.utils import BaseResponse

api = Blueprint("page-filters-api", __name__)


@api.get('/pages/filters')
@shop_required
@validate
def list_page_filters(query: QueryArgument):
    """
    查询指定节点下的
    :return:
    """
    assert g.login_user_detail
    user_id = g.login_user_detail.feisuo_user_id
    org_id = g.shop.org_id
    # 查询符合条件的筛选器，并以倒序排列。
    filters = PageFilter.query.filter(
        PageFilter.org_id == org_id,
        PageFilter.user_id == user_id,
        PageFilter.node == query.node,
        PageFilter.is_deleted.is_(False),
    ).order_by(PageFilter.id.desc()).all()
    # 数据格式化。
    return jsonify(success=True, filters=[f.brief() for f in filters]), 200


@api.post('/pages/filters')
@shop_required
@validate
def create_page_filter(body: CreateFilterRequest):
    assert g.login_user_detail
    user_id = g.login_user_detail.feisuo_user_id
    org_id = g.shop.org_id
    # 查询是否有名称重复的情况。
    filter_count = PageFilter.query.filter(
        PageFilter.org_id == org_id,
        PageFilter.user_id == user_id,
        PageFilter.is_deleted.is_(False),
        PageFilter.node == body.node,
        PageFilter.name == body.name,
    ).count()
    if filter_count > 0:
        raise HTTPException(error_code=400, message="名称重复")
    # 创建。
    page_filter = PageFilter(
        user_id=user_id,
        org_id=org_id,
        name=body.name,
        node=body.node,
        condition=body.condition,
    )
    db.session.add(page_filter)
    db.session.commit()
    return jsonify(success=True, id=page_filter.id), 201


@api.delete('/pages/filters/<filter_id>')
@shop_required
def delete_page_filter(filter_id: int):
    assert g.login_user_detail
    user_id = g.login_user_detail.feisuo_user_id
    try:
        filter_id = int(filter_id)
    except ValueError:
        raise HTTPException(error_code=400, message="不合法的 ID")
    # 数据查询。
    page_filter: Optional[PageFilter] = PageFilter.query.filter(
        PageFilter.id == filter_id,
        PageFilter.is_deleted.is_(False),
    ).first()
    if page_filter is None:
        return jsonify(success=True), 200
    # 校验删除人和创建人是否一致。
    if page_filter.user_id != user_id:
        raise HTTPException(error_code=403, message="该账号无权删除")
    # 进行软删除。
    page_filter.is_deleted = True
    db.session.commit()
    return jsonify(success=True), 200


@api.post("/pages/filters/<int:filter_id>:set-default")
@shop_required
@validate
def set_default_page_filter(filter_id: int) -> BaseResponse:
    assert g.login_user_detail
    user_id = g.login_user_detail.feisuo_user_id
    page_filter = db.session.get(PageFilter, filter_id)
    if not page_filter:
        return BaseResponse.Failed("未找到常用筛选")
    if page_filter.is_deleted:
        return BaseResponse.Failed("常用筛选不可用")
    if page_filter.user_id != user_id:
        return BaseResponse.Failed("该账号无权删除")
    page_filter.is_default = True
    other_page_filters = PageFilter.query.filter(
        PageFilter.user_id == user_id,
        PageFilter.id != filter_id,
        PageFilter.is_deleted.is_(False),
        PageFilter.is_default.is_(True)
    ).all()
    for other_page_filter in other_page_filters:
        other_page_filter.is_default = False
    db.session.commit()
    return BaseResponse.Success(None)
