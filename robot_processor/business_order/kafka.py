from loguru import logger

from robot_processor.enums import Creator
from robot_processor.ext import kafka_consumer
from robot_processor.business_order.tasks import handle_assignee_status, handle_approver_status
assignee_event = kafka_consumer.subscribe("kiosk-assignee-status")


@logger.catch
@assignee_event.connect
def update_assignee(_, record):
    # 只处理DELETE、DISABLE
    # 将待人工处理且分派人已经失效的job移入异常任务池
    """record
    {
        "shop_assignees": [

            {
                "platform": "xxx",
                "sid": "1",
                "action": "DELETE/DISABLE",
                "leyan_account_id": 1,
                "channel_account_ids": [],
            }
        ]
    }
    """
    logger.info(f"assignee status event record: {record}")
    results = []
    for shop_assignee in record.get('shop_assignees', []):
        action = shop_assignee['action']
        if action not in ('DISABLE', 'DELETE', 'ENABLE'):
            continue
        sid = shop_assignee.get('sid')
        if not sid:
            continue
        if leyan_account_id := shop_assignee.get('leyan_account_id'):
            users = [dict(accessor_user_id=leyan_account_id, accessor_user_type=Creator.LEYAN.value, sid=sid)]
            handle_assignee_status.send(sid, action, users)
            handle_approver_status.send(sid, action, users)
            results.append((sid, action, users))
    return results
