from loguru import logger

from robot_processor.archive.business_order import (
    DefaultBusinessOrderOnlyRegisterStrategy,
    AutoBusinessOrderOnlyRegisterStrategy,
    ShopInvalidStrategy
)
from robot_processor.archive.form_version import (
    FormVersionStrategy
)
from robot_processor.constants import TASK_QUEUE_BACKGROUND_TASK, TaskPriority
from robot_processor.ext import task_queue


@task_queue.actor(queue_name=TASK_QUEUE_BACKGROUND_TASK, priority=TaskPriority.LOW)
def business_order_archive(sids):
    """
    https://leyan.yuque.com/digismart/lqmmke/llk46h
    归档分为以下步骤：
    1. 待归档数据存储到冷数据
    2. 从主库删除待归档的数据
    """

    default_executor = DefaultBusinessOrderOnlyRegisterStrategy()
    default_executor.run(sids)

    auto_business_order_executor = AutoBusinessOrderOnlyRegisterStrategy()
    auto_business_order_executor.run(sids)

    shop_invalid_executor = ShopInvalidStrategy()
    shop_invalid_executor.run(sids)


@task_queue.actor(queue_name=TASK_QUEUE_BACKGROUND_TASK, priority=TaskPriority.LOW)
def clean_invalid_jobs():
    """
    清理无效 job.

    job 无效指的是: job 的 business_order_id 为 None, 或者 job.business_order_id 关联的 business_order 不存在.
    """
    from robot_processor.business_order.models import Job, BusinessOrder
    from robot_processor.ext import db
    from robot_processor.ext import cache

    def get_clean_offset():
        return cache.get("offset:job:clean") or 0

    def set_clean_offset(offset):
        cache.set("offset:job:clean", offset)

    def try_clean(offset: int) -> int:
        scan_end_offset = offset + 10000  # 无效 job 不多时，避免扫描太多行
        logger.info("寻找无效 job: {} .. {}", offset, scan_end_offset)

        query = db.session.query(Job.id) \
            .outerjoin(BusinessOrder, Job.business_order_id == BusinessOrder.id) \
            .filter(Job.id >= offset) \
            .filter(Job.id < scan_end_offset) \
            .filter(BusinessOrder.id.is_(None))
        job_ids = [job.id for job in query.all()]
        if job_ids:
            logger.info("清理 {} 个 job: {} .. {}", len(job_ids), job_ids[0], job_ids[-1])
            db.session.query(Job).filter(Job.id.in_(job_ids)).delete(synchronize_session=False)
            db.session.commit()
        has_more_to_scan = db.session.query(Job.query.filter(Job.id >= scan_end_offset).exists()).scalar()
        if has_more_to_scan:
            return scan_end_offset
        else:
            return 0

    new_offset = try_clean(get_clean_offset())
    set_clean_offset(new_offset)


@task_queue.actor(queue_name=TASK_QUEUE_BACKGROUND_TASK, priority=TaskPriority.LOW)
def clean_invalid_job_pools():
    """
    清理无效 job_pool.

    job_pool 无效指的是: job_pool 的 business_order_id 为 None, 或者 job_pool.business_order_id 关联的 business_order 不存在.
    """
    from robot_processor.business_order.models import JobPool, BusinessOrder
    from robot_processor.ext import db
    from robot_processor.ext import cache

    def get_clean_offset():
        return cache.get("offset:job_pool:clean") or 0

    def set_clean_offset(offset):
        cache.set("offset:job_pool:clean", offset)

    def try_clean(offset: int) -> int:
        scan_end_offset = offset + 10000  # 无效 job_pool 不多时，避免扫描太多行
        logger.info("寻找无效 job_pool: {} .. {}", offset, scan_end_offset)
        query = db.session.query(JobPool.id) \
            .outerjoin(BusinessOrder, JobPool.business_order_id == BusinessOrder.id) \
            .filter(JobPool.id >= offset) \
            .filter(JobPool.id < scan_end_offset) \
            .filter(BusinessOrder.id.is_(None))
        job_pool_ids = [job_pool.id for job_pool in query.all()]
        if job_pool_ids:
            logger.info("清理 {} 个 job_pool: {} .. {}", len(job_pool_ids), job_pool_ids[0], job_pool_ids[-1])
            db.session.query(JobPool).filter(JobPool.id.in_(job_pool_ids)).delete(synchronize_session=False)
            db.session.commit()
        has_more_to_scan = db.session.query(JobPool.query.filter(JobPool.id >= scan_end_offset).exists()).scalar()
        if has_more_to_scan:
            return scan_end_offset
        else:
            return 0

    new_offset = try_clean(get_clean_offset())
    set_clean_offset(new_offset)


@task_queue.actor(queue_name=TASK_QUEUE_BACKGROUND_TASK, priority=TaskPriority.LOW)
def form_version_archive(sids):
    default_executor = FormVersionStrategy()
    default_executor.run(sids)
