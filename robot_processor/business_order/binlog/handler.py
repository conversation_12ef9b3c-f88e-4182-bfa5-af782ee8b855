from json import loads
from typing import cast

from itertools import repeat
from cattrs import structure, unstructure
from loguru import logger

from robot_processor.client import app_config
from robot_processor.logging import vars as log_vars
from robot_processor.ext import job_binlog_consumer
from robot_processor.constants import JOB_BINLOG_TOPIC
from robot_processor.business_order.binlog.types import BinlogMessage
from robot_processor.business_order.binlog.job_processor import exception_rule
from robot_processor.business_order.binlog.job_processor.types import Binlog<PERSON>ob
from robot_processor.utils import dict_diff

binlog_event = job_binlog_consumer.subscribe(JOB_BINLOG_TOPIC)


@binlog_event.connect
def binlog_job_processor(_, record):
    from robot_processor.business_order.binlog.job_processor.auto_retry import \
        AutoRetryRecordManager
    from robot_processor.enums import JobStatus
    binlog_message = cast(
        BinlogMessage[BinlogJob], structure(loads(record), BinlogMessage[BinlogJob])
    )
    if binlog_message.isDdl:
        return
    if binlog_message.type == "DELETE":
        # 进行物理删除操作的 Job 无需处理
        # 在业务上，Job 可以删除的前提条件是工单状态=完成/关闭。
        return

    for binlog_job, old_info in zip(binlog_message.data, binlog_message.old or repeat(None)):
        if binlog_job.archived:
            continue
        old_info = old_info or {}
        diff_info = dict_diff(old_info, unstructure(binlog_job))
        diff_keys = set(diff_info["add"]) | set(diff_info["update"])
        if not diff_keys.intersection(app_config.exception_pool_follow_fields):
            continue
        readable_job = binlog_job.as_readable()
        log_vars.JobId.set(readable_job.id)
        log_vars.BusinessOrderId.set(readable_job.business_order_id)
        process_result = exception_rule.process_by_exception_ruler(readable_job)
        exception_rule.process_for_exception_pool(readable_job, old_info, process_result)

        # 基于job状态变更对重试记录做处理以及自动重试
        try:
            if process_result is None or (not process_result.match_result.is_match()):
                # 非失败任务或者命中兜底规则的任务不需要自动重试
                continue
            if old_info and old_info.get("status") == JobStatus.FAILED.name:
                # 任务状态变更为失败的任务才需要自动重试
                continue
            if readable_job.status not in [JobStatus.FAILED.name, JobStatus.SUCCEED.name]:
                # 直接跳过其他状态的job
                continue
            if "over limit of" in (readable_job.exc_info or ""):
                continue
            AutoRetryRecordManager(
                readable_job,
                process_result.match_result.match_rule
            ).trigger_by_job_status()
        except Exception as e:
            logger.opt(exception=e).error(f"自动重试失败:  {e}")
