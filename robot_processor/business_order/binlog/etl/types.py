from attrs import frozen
from typing import Optional


@frozen
class BinlogBo:
    id: str
    created_at: int
    updated_at: int
    sid: str
    aid: str
    uid: str
    buyer_open_uid: str
    form_id: str
    data: Optional[str]
    creator_type: Optional[str]
    job_history: Optional[str]
    status: str
    update_user: Optional[str]
    from_type: str
    current_job_id: str
    is_timeout: int
    deleted: int
    creator_user_id: Optional[str]
    creator_group: Optional[str]
    feisuo_creator: Optional[str]
    feisuo_creator_user_id: Optional[str]
    feisuo_creator_group: Optional[str]
    updator_id: Optional[str]
    updator_group: Optional[str]
    updator_type: Optional[str]
    feisuo_updator: Optional[str]
    feisuo_updator_id: Optional[str]
    feisuo_updator_group: Optional[str]
    flag: Optional[str]

    def __str__(self):
        return f"id={self.id}, sid={self.sid}, form_id={self.form_id}, data={self.data}"
