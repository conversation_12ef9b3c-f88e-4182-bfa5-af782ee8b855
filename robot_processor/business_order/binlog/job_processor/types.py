import enum
from typing import Optional
from typing import cast

import arrow
from attrs import frozen

from robot_processor.business_order.models import BusinessOrder
from robot_processor.enums import BusinessOrderStatus


@frozen
class BinlogJob:
    id: int
    business_order_id: int
    step_id: int

    status: Optional[str]
    process_mark: Optional[str]
    exc_info: Optional[str]

    created_at: int
    updated_at: int
    message_id: str
    step_uuid: str
    archived: Optional[int]

    def as_readable(self) -> "ReadableJob":
        return ReadableJob(
            id=self.id,
            business_order_id=self.business_order_id,
            step_id=self.step_id,
            status=self.status,
            process_mark=self.process_mark,
            exc_info=self.exc_info,
            created_at=arrow.get(self.created_at, tzinfo="Asia/Shanghai"),
            updated_at=arrow.get(self.updated_at, tzinfo="Asia/Shanghai"),
            message_id=self.message_id,
            step_uuid=self.step_uuid,
            archived=self.archived,
        )


@frozen
class ReadableJob:
    id: int
    business_order_id: int
    step_id: int
    step_uuid: str

    # refer::JobStatus 不同 proc 可能存在代码版本不同，不做枚举类型强校验
    status: Optional[str]
    process_mark: Optional[str]
    exc_info: Optional[str]

    created_at: arrow.Arrow
    updated_at: arrow.Arrow
    message_id: Optional[str] = None
    archived: Optional[int] = None

    @classmethod
    def from_job(cls, job):
        return cls(
            id=job.id,
            business_order_id=job.business_order_id,
            step_id=job.step_id,
            step_uuid=job.step_uuid,
            status=job.status.name,
            process_mark=job.process_mark.name,
            exc_info=job.exc_info,
            created_at=arrow.get(job.created_at, tzinfo="Asia/Shanghai"),
            updated_at=arrow.get(job.updated_at, tzinfo="Asia/Shanghai"),
            message_id=job.message_id,
            archived=job.archived,
        )


class ExceptionStrategy(enum.Enum):
    """异常处理策略"""

    SKIP = enum.auto()
    PROCESSING = enum.auto()
    REMOVE = enum.auto()
    ADD = enum.auto()

    @property
    def label(self):
        return {
            ExceptionStrategy.SKIP: "不处理",
            ExceptionStrategy.PROCESSING: "将工单异常状态置为重试中",
            ExceptionStrategy.REMOVE: "将工单从异常池中移除",
            ExceptionStrategy.ADD: "将工单加入异常池",
        }[self]

    @classmethod
    def which_strategy(cls, job, old_info, process_result):
        """Job 的状态变化，决定了 Job 在异常池中的生命周期

        Status:
            任意状态 -> Failed: ADD
                (实际代码没有直接使用 Job.status，而是使用 process result，是为了判断更灵活一些)
            Failed -> Running: PROCESSING
            (ANY) -> Succeed: REMOVE
            OTHER: SKIP
        ProcessMark:
            (ANY) -> CLOSED: REMOVE
        """
        from robot_processor.enums import JobProcessMark
        from robot_processor.enums import JobStatus

        job = cast(ReadableJob, job)

        # 工单已经归档
        if job.archived:
            return cls.REMOVE

        # 工单关闭
        # 工单关闭时仅修改 Job.process_mark，所以 Job 也是会命中异常规则的，
        # 需要在 ADD 逻辑之前判断是否为关闭/删除操作
        if job.process_mark in (JobProcessMark.CLOSE.name, JobProcessMark.DELETE.name):
            return cls.REMOVE
        if job.status == JobStatus.FAILED.name and job.process_mark == JobProcessMark.RETRY.name:
            return cls.PROCESSING
        # ruler 处理结果不为空，应该加入异常池
        if process_result is not None:
            if BusinessOrder.query.get(job.business_order_id).status not in [BusinessOrderStatus.SUCCEED, BusinessOrderStatus.CLOSE]:  # type: ignore[union-attr]  # noqa: E501
                return cls.ADD
        # 任务执行成功
        if job.status != JobStatus.FAILED.name:
            return cls.REMOVE

        return cls.SKIP
