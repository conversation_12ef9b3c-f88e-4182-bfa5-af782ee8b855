"""由 ExceptionRulerKeeper 维护 exception ruler 的版本，定时检查使用最新的规则"""

from typing import TYPE_CHECKING
from typing import Union

if TYPE_CHECKING:
    from robot_processor.business_order.exception_rule import ExceptionRuler


class ExceptionRulerKeeper:
    __slots__ = (
        "_exception_ruler",
        "_local_version",
    )
    _exception_ruler: Union["ExceptionRuler", None]
    _local_version: str | None

    def __init__(self):
        self._exception_ruler = None
        self._local_version = None

    def init(self):
        """需要 flask 实例初始化完成后使用"""
        self.update_exception_ruler()

    def mark_updated(self):
        """在 exception rule 发生变更时，通过这个方法更新 server_version"""
        import time

        from robot_processor.ext import cache

        cache.set(self.version_key, str(int(time.time())), timeout=0)

    def update_exception_ruler(self):
        """更新 exception ruler, 在 flask app 初始化完成后执行"""
        from robot_processor.business_order.exception_rule import ExceptionRule
        from robot_processor.business_order.exception_rule import ExceptionRuler

        assert self.server_version, "未找到异常规则的版本"
        # 从 db 获取最新的异常规则，用于更新 exception ruler
        self._exception_ruler = ExceptionRuler.load_from_orm(ExceptionRule.query)  # type: ignore[arg-type]
        # 更新本地版本号到最新版本
        self._local_version = self.server_version

    @property
    def version_key(self):
        """当前 exception ruler 版本号在缓存中的 key"""
        from flask import current_app

        return current_app.config.get("EXCEPTION_RULE_VERSION_CACHE_KEY", "exception_ruler_keeper:version")

    @property
    def need_update(self):
        """本地版本号与服务端版本号不一致时，需要更新"""
        return self.local_version != self.server_version

    @property
    def local_version(self):
        """本地版本号

        仅支持通过 `update_exception_ruler` 方式更新 local version
        """
        return self._local_version

    @property
    def server_version(self):
        """服务器上最新版本号

        仅支持通过 `mark_updated` 方式更新 server version
        """
        from robot_processor.ext import cache

        return cache.get(self.version_key)

    @property
    def exception_ruler(self):
        if self._exception_ruler is None:
            self.init()
        return self._exception_ruler


exception_ruler_keeper = ExceptionRulerKeeper()
