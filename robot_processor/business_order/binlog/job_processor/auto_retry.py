from typing import Optional

import arrow
from loguru import logger

from robot_processor.app import app
from robot_processor.business_order.binlog.job_processor.types import \
    ReadableJob
from robot_processor.business_order.exception_rule import ExceptionRule, \
    ExceptionBusinessOrder
from robot_processor.business_order.exception_rule.ruler import \
    fallback_exceptional_rule
from robot_processor.business_order.models import Job, BusinessOrder
from robot_processor.business_order.tasks import execute_job
from robot_processor.enums import JobStatus, \
    AutoRetryRecordStatus, JobProcessMark, BusinessOrderStatus
from robot_processor.ext import db, cache
from robot_processor.form.models import StepAutoRetry, JobAutoRetryRecord
from robot_processor.dramatiq import SkippableMiddleware
from robot_processor.web_grpc_services.enums import AutoRetryStatus
from robot_processor.db import in_transaction

DEBOUNCE_JOB_RETRY_CACHE_KEY = "DEBOUNCE_JOB_CACHE_KEY_"


def get_job_auto_retry_interval() -> int:
    # retry_interval 需要大于debounce_time
    return max(app.config.get("JOB_AUTO_RETRY_INTERVAL", 120), app.config.get("DEBOUNCE_JOB_RETRY_TIMEOUT", 10) + 1)


def is_job_need_auto_retry(job: ReadableJob, match_rule: ExceptionRule.View.Matcher) -> \
        tuple[bool, AutoRetryStatus, int]:
    if job.process_mark in [
        JobProcessMark.DELETE.name,
        JobProcessMark.FINISH.name,
        JobProcessMark.CLOSE.name
    ]:
        return False, AutoRetryStatus.UNSUPPORTED_JOB_STATUS, 0

    if job.status != JobStatus.FAILED.name:
        return False, AutoRetryStatus.UNSUPPORTED_JOB_STATUS, 0
    job_obj = Job.query.get(job.id)

    # 工单必须还在异常中
    if BusinessOrder.query.get(job.business_order_id).status in [  # type: ignore[union-attr]
        BusinessOrderStatus.SUCCEED, BusinessOrderStatus.CLOSE]:  # noqa
        return False, AutoRetryStatus.UNSUPPORTED_BUSINESS_ORDER_STATUS, 0

    # 如果是限流引起的失败，所有rpa都支持重试
    if match_rule.is_rate_limit_error:
        return True, AutoRetryStatus.ENABLED_IN_CYCLE, get_job_auto_retry_interval()

    # 如果是客户端不在线引起的失败，结合apollo配置来判断是否支持重试
    # fixme 临时方案，后续需要将这个配置放到step中
    if match_rule.is_client_offline_error:
        if job_obj.shop.org_id in app.config.get("IGNORE_CLIENT_OFFLINE_RETRY_ORG_IDS", []):  # type: ignore[union-attr]
            return False, AutoRetryStatus.DISABLED, 0
        else:
            return True, AutoRetryStatus.ENABLED_IN_CYCLE, get_job_auto_retry_interval()

    # 其他异常需要根据步骤配置和异常规则配置来判断是否支持重试
    if retry := get_auto_retry_config(job_obj):  # type: ignore[arg-type]
        # 是否开启了自动重试
        if not retry.can_retry:
            return False, AutoRetryStatus.DISABLED, 0
        # 是否超过了重试时限
        if retry.retry_duration:
            record = JobAutoRetryRecord.query.filter_by(job_id=job.id).first()
            if record and record.first_fail_timestamp + retry.retry_duration < arrow.now().int_timestamp:  # type: ignore[operator] # noqa: E501
                return False, AutoRetryStatus.ENABLED_OUT_OF_CYCLE, 0

        # 是否超过了重试次数
        if retry.retry_times:
            current_retry_count = JobAutoRetryRecord.query.filter_by(
                job_id=job.id,
            ).count()
            if current_retry_count >= retry.retry_times:
                return False, AutoRetryStatus.ENABLED_OUT_OF_CYCLE, 0
        # 命中的异常规则是否允许自动重试
        if not match_rule.can_auto_retry:
            return False, AutoRetryStatus.UNSUPPORTED_EXCEPTION_TYPE, 0

        return True, AutoRetryStatus.ENABLED_IN_CYCLE, retry.retry_interval
    else:
        return False, AutoRetryStatus.DISABLED, 0


def get_auto_retry_config(job: ReadableJob) -> Optional[StepAutoRetry]:
    return StepAutoRetry.query.filter_by(step_uuid=job.step_uuid).first()


class AutoRetryRecordManager:
    # todo fixme 不支持多线程
    """
    重试记录的状态更新有两个入口
    1。update_record_by_job_status，由job的状态更新触发
    2。auto_retry的时候自己将pending状态改为running.
    3。job失败，创建一个新的重试记录，状态为pending
    """

    def __init__(self, job: ReadableJob, match_rule: ExceptionRule.View.Matcher):
        self.job = job
        self.match_rule = match_rule
        logger.info(f"初始化重试记录管理器，job: {job}, match_rule: {match_rule}")
        self.exist_pending_record: JobAutoRetryRecord | None = None

    def _update_exist_record_by_job_status(self):
        """
        根据job的状态更新已存在的重试记录的状态
        job failed --> failed
        job succeed --> succeed
        """
        if not self.exist_pending_record:
            logger.info("未找到对应任务的PENDING重试记录， 不需要对以往记录做操作")
            return
        if self.exist_pending_record.enable is False:
            return
        message_id = self.exist_pending_record.message_id
        if self.job.status == JobStatus.FAILED.name:
            self.exist_pending_record.status = AutoRetryRecordStatus.FAILED
            SkippableMiddleware.mark_finished(message_id)
        elif self.job.status == JobStatus.SUCCEED.name:
            self.exist_pending_record.status = AutoRetryRecordStatus.SUCCESS
            SkippableMiddleware.mark_finished(message_id)

    def _create_new_record_if_need(self):
        """
        job失败的时候为其创建一个重试记录
        并将该任务加入延迟执行队列
        """
        if self.job.status != JobStatus.FAILED.name:
            logger.info(f"非失败任务不创建重试记录 {self.job.id} {self.job.status}")
            return None
        need_retry, auto_retry_status, retry_interval = is_job_need_auto_retry(self.job, self.match_rule)
        self._update_exception_order_auto_retry_status(auto_retry_status)
        logger.info(f"是否需要自动重试: {need_retry} 及其状态: {auto_retry_status}")
        latest_record = JobAutoRetryRecord.query.filter_by(
            job_id=self.job.id,
        ).order_by(
            JobAutoRetryRecord.seq.desc()
        ).first()

        seq = latest_record.seq + 1 if latest_record else 1
        first_fail_timestamp = latest_record.first_fail_timestamp if latest_record else arrow.now().int_timestamp
        if need_retry:
            need_record = True
            retry_message = self._auto_retry(retry_interval)
            message_id = retry_message.message_id
            enable = True
            status = AutoRetryRecordStatus.PENDING
            eta = arrow.now().shift(seconds=retry_interval).int_timestamp
        else:
            # 不需要重试的情况，只会在第一次记录（来说明为什么没有自动重试，比如rpa类型不支持）
            need_record = seq == 1
            message_id = self.job.message_id
            enable = False
            status = AutoRetryRecordStatus.NOT_ENABLE
            eta = 0

        if need_record:
            new_record = JobAutoRetryRecord(
                enable=enable,
                description=auto_retry_status.value,
                job_id=self.job.id,
                message_id=message_id,
                seq=seq,
                status=status,
                result={"exc_info": self.job.exc_info},
                first_fail_timestamp=first_fail_timestamp,
                eta=eta
            )
            logger.bind(message_id=message_id).info(f"创建重试记录 {self.job.id} {new_record.seq}")
            db.session.add(new_record)

    def _auto_retry(self, retry_interval: int):
        """
        重试任务
        """
        self._set_debounce(self.job.id)
        retry_result = execute_job.send_with_options(
            args=(self.job.id,),
            kwargs={
                "chain": True,
                "is_auto_retry": True,
            },
            delay=retry_interval * 1000)
        logger.info(f"retry_result: {retry_result} \n"
                    f"自动重试 job: {self.job.id} after {retry_interval} seconds")
        return retry_result

    def _update_exception_order_auto_retry_status(self,
                                                  auto_retry_status: AutoRetryStatus):
        exception_business_order = ExceptionBusinessOrder.query.filter_by(
            current_job_id=str(self.job.id)).first()
        if not exception_business_order:
            logger.warning("未找到对应的异常工单，不需要更新状态")
            return
        exception_business_order.auto_retry_status = auto_retry_status.value

    def trigger_by_job_status(self):
        if self._check_debounce(self.job.id):
            logger.info(f"debounce for job {self.job.id}")
            return
        if self.match_rule == fallback_exceptional_rule:
            logger.info(f"{self.job.id} 命中兜底规则，不进行重试相关操作")
            self._update_exception_order_auto_retry_status(
                AutoRetryStatus.UNSUPPORTED_EXCEPTION_TYPE)
            return
        self.exist_pending_record = JobAutoRetryRecord.query.filter_by(
            job_id=self.job.id,
            status=AutoRetryRecordStatus.PENDING
        ).first()
        with in_transaction():
            self._update_exist_record_by_job_status()
            self._create_new_record_if_need()

    @staticmethod
    def _check_debounce(job_id):
        # SyncJobMessageMiddleware 会立即引起一条仅有message_id变动的binlog
        if cache.get(DEBOUNCE_JOB_RETRY_CACHE_KEY + str(job_id)):
            return True
        return False

    @staticmethod
    def _set_debounce(job_id):
        # timeout < step_auto_retry.retry_interval
        cache.set(DEBOUNCE_JOB_RETRY_CACHE_KEY + str(job_id), 1,
                  timeout=app.config.get("DEBOUNCE_JOB_RETRY_TIMEOUT", 10))
