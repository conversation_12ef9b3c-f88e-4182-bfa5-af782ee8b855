from enum import StrEnum
from pydantic import BaseModel, Field


class Account(BaseModel):
    class AccountType(StrEnum):
        USER = "USER"
        ASSISTANT = "ASSISTANT"
        RPA = "RPA"
        LEYAN = "LEYAN"
        LDAP = "LDAP"

    id: int = 1
    type: AccountType = AccountType.ASSISTANT
    nick: str = ""


class JobBrief(BaseModel):
    class JobNodeType(StrEnum):
        HUMAN = "HUMAN"
        AUTO = "AUTO"

    name: str = ""
    type: JobNodeType = JobNodeType.HUMAN
    job_id: str  # 需要和avro的类型保持一致


class ExceptionInfo(BaseModel):
    cause: str = Field(default="", alias="reason")
    suggestion: str = ""


class BusinessOrderBrief(BaseModel):
    id: str
    name: str
    source_type: str
    creator: Account
    current_job_brief: JobBrief
    exception_info: ExceptionInfo


class ExceptionPoolType(StrEnum):
    ADD = "ADD"
    REMOVE = "REMOVE"


class ExceptionPoolEventMsg(BaseModel):
    type: ExceptionPoolType = ExceptionPoolType.ADD
    event_time: int
    org_id: int
    sid: str
    business_order_brief: BusinessOrderBrief
