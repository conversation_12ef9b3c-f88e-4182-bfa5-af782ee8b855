from enum import Enum
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Union

import loguru
from loguru import logger
from pydantic import BaseModel
from pydantic import Field
from pydantic import PositiveInt
from pydantic import root_validator
from pydantic import validator
from sqlalchemy.sql.operators import op

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.base_schemas import Failed
from robot_processor.business_order.models import BusinessOrderFlag
from robot_processor.business_order.seller.enums import MyBusinessOrdersTab
from robot_processor.client import kiosk_client
from robot_processor.currents import g
from robot_processor.enums import Action
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import ColumnOperator
from robot_processor.enums import Creator
from robot_processor.enums import FromType
from robot_processor.enums import OperatorRelation
from robot_processor.enums import PaymentMethod
from robot_processor.enums import PlanWhenAssignException
from robot_processor.form.models import Form
from robot_processor.shop.models import Shop


class CreateAction(Enum):
    # 创建并提交
    SUBMIT = 1
    # 创建并保存
    SAVE = 2


class NextJobAssistant(BaseModel):
    assistant_type: Creator
    assistant_user_id: Optional[int]

    def to_account_detail(self) -> Optional[AccountDetailV2]:
        if self.assistant_type not in [Creator.ASSISTANT, Creator.LEYAN]:
            return AccountDetailV2(
                user_type=self.assistant_type.value,
                user_id=self.assistant_user_id,
                user_nick=None,
                phone=None,
            )
        assert self.assistant_user_id is not None, f"next_job_assistant.assistant_user_id is none, {self}"
        return kiosk_client.get_user_by_id(self.assistant_type, self.assistant_user_id)


class BusinessOrderQuerySchema(BaseModel):
    leyan_request_id: Optional[str]
    leyan_request_token: Optional[str]


class BusinessOrderSchema(BaseModel):
    uid: Optional[str]
    buyer_open_uid: Optional[str]  # taobao open uid
    form_id: int
    creator_type: Optional[Creator] = Creator.ASSISTANT
    creator_user_id: Optional[int]
    from_type: Optional[FromType] = FromType.ASSISTANT
    data: dict = Field(default_factory=dict)  # {widget_key: widget_value}
    sid: str = ""
    user_nick: Optional[str]
    mid: Optional[str]
    create_action: Optional[CreateAction] = CreateAction.SUBMIT
    leyan_request_id: Optional[str]
    next_job_assistant: Optional[NextJobAssistant] = None
    org_id: str | None = None
    force: bool = True

    @root_validator(skip_on_failure=True)
    def fix_from_type(cls, values):
        """
        在 n8n 创建工单时，没有设置 from_type 的信息，导致使用了默认的 FromType.ASSISTANT
        ref: https://git.leyantech.com/digismart/n8n-nodes-digismart/-/blob/master/src/nodes/FlyingShuttle/FlyingShuttleCreateOrder.node.ts?ref_type=heads#L138  # noqa
        """
        if values.get("creator_type") == Creator.RPA:
            values["from_type"] = FromType.KAFKA_TRADE_EVENT

        return values

    @property
    def form(self):
        if not (_form := Form.query.get(self.form_id)):
            return
        if not (shop := Shop.Queries.optimal_shop_by_sid(self.sid, org_id=self.org_id)):
            return
        return Form.Utils.form_wrapper(form=_form, shop=shop)

    def check_form(self):
        if self.from_type not in [FromType.KAFKA_TRADE_EVENT, FromType.LEYAN] and self.sid not in g.org_sids:
            return False, "非法店铺id"
        form = self.form
        if not form:
            return False, "对应表单不存在"
        if form.deleted is True:
            return False, "抱歉，当前工单模板及任务已被删除，无法创建"
        if not form.enabled:
            return False, "该类型工单未启用"
        return True, None

    def modify_data(self):
        is_ok, _ = self.check_form()
        if not is_ok:
            return
        self.check_uid()
        try:
            payment_widget = self.filter_widget(widget_type="payment-method")
            trade_widget = self.filter_widget(widget_type="order")
            if not (payment_widget and trade_widget):
                return

            payment_widget_data: dict = self.data.get(payment_widget["key"], {})
            payment_method = int(payment_widget_data.get("payment_method", 0))
            if payment_method == PaymentMethod.TB_BIND_NICK.value:
                # 强转成新的打款方式
                payment_widget_data.update({"payment_method": PaymentMethod.TB_ORDER_NO.value})

            if payment_method != PaymentMethod.TB_ORDER_NO.value:
                return
        except Exception as e:
            logger.opt(exception=e).error(f"fill data before create bo@{self.data}")
            return

    def widgets(self):
        from robot_processor.business_order.handlers import form_widget_wrapper

        return form_widget_wrapper(self.form, only_header=True)

    def check(self):
        is_ok, msg = self.check_form()
        if not is_ok:
            return is_ok, msg
        return True, ""

    def filter_widget(self, widget_type="product"):
        data = list(filter(lambda w: w["type"] == widget_type, self.widgets()))
        return data[0] if data else None

    def check_uid(self):
        """
        N8N创建工单时没有昵称
        侧边栏提价工单因为未知原因会与buyer_open_uid对应的昵称不一致
        """
        from robot_processor.client import get_buyer_nick

        if not self.buyer_open_uid:
            return
        usernick_key = (self.filter_widget(widget_type="usernick") or {}).get("key", None)
        uid_in_data, uid = self.data.get(usernick_key) or self.uid, self.uid
        nick = get_buyer_nick(self.sid, buyer_open_uid=self.buyer_open_uid)
        if not nick:
            return

        if not uid or not (uid and (nick == uid == uid_in_data)):
            # 组件的昵称与 系统的买家昵称字段及通过open_uid获取到的昵称需要一致
            logger.warning(f"昵称校验未通过 buyer_open_uid={self.buyer_open_uid} {uid=} {nick=} {uid_in_data=}")
            self.uid = nick
            if usernick_key:
                self.data[usernick_key] = nick

    def handle_product_widget(self):
        from robot_processor.ext import db
        from robot_processor.form.models import Widget
        from robot_processor.form.models import WidgetInfo

        job_steps = self.form.job_steps.all()
        collection_id_list = [step.widget_collection_id for step in job_steps if step.widget_collection_id]

        widget_info_list = WidgetInfo.find_by_collection_id(collection_id_list)
        with db.session.no_autoflush:
            widget_map = {widget.id: Widget.get(widget.id) for widget in Widget.query}

        widget_info_groups: dict[int, list[WidgetInfo]] = {}
        for info in widget_info_list:
            widget_info_groups.setdefault(info.widget_collection_id, []).append(info)  # type: ignore[arg-type]

        for widget_info_group in widget_info_groups.values():
            for info in widget_info_group:
                if info.before:
                    continue
                if widget_map.get(info.widget_id, {}).get("type") != "product":  # type: ignore[arg-type]
                    continue

                widget_key = str(info.key)
                widget_value = self.data.get(widget_key)
                if widget_value is None:
                    return True, None
                if isinstance(widget_value, str):
                    return False, "product data is str...."

                if isinstance(widget_value, list):
                    mode = info.option_value.get("mode") or "spu"
                    self.data[widget_key] = [
                        {
                            "sku": order.get("sku_id") if mode == "sku" else "",
                            "spu": order.get("spu_id"),
                            "mode": mode,
                        }
                        for order in widget_value
                    ]
            return True, None
        return True, None

    def n8n_convert_data(self):
        # 兼容转化对应的schema -- 这里转换的是商品组件
        is_ok, msg = self.check_form()
        if not is_ok:
            return is_ok, msg
        is_ok, err_msg = self.handle_product_widget()
        if not is_ok:
            return is_ok, err_msg
        return True, None


class FilterSchema(BaseModel):
    class WidgetFilter(BaseModel):
        widget_key: List[str]
        keyword: str | list

    class OrderBy(BaseModel):
        fields: list[str]
        strategy: str

    id: Optional[Union[int, List[int]]]
    form_id: Optional[list[int]]
    page: Optional[PositiveInt] = 1
    per_page: Optional[PositiveInt] = 10
    # 和bi一样的传参方式，方便以后直接交接给bi
    # bi的排序： configs: {order_by: ‘field asc’}
    # order by 可以是 list[str] 类型，来支持 order by 多个字段
    configs: Dict[str, Any] = Field(default_factory=dict)
    # 工单状态
    status: Union[Optional[BusinessOrderStatus], Optional[List[BusinessOrderStatus]]]
    # 创建时间
    created_at: Optional[str]
    # 创建人
    creator_id: Optional[int]
    platform_creator_id: Optional[int]
    feisuo_creator_id: Optional[int]
    creator_group_uuid: Optional[List[str]]
    platform_creator_group_uuid: Optional[List[str]]
    feisuo_creator_group_uuid: Optional[List[str]]
    # 当前步骤
    current_step: Optional[str]
    # 最近更新时间
    updated_at: Optional[str]
    # 最近更新人
    updator_id: Optional[int]
    platform_updator_id: Optional[int]
    feisuo_updator_id: Optional[int]
    updator_group_uuid: Optional[List[str]]
    platform_updator_group_uuid: Optional[List[str]]
    feisuo_updator_group_uuid: Optional[List[str]]

    buyer_nick: Optional[str] = None
    from_type: Optional[int] = None

    create_start_at: Optional[int] = None
    create_end_at: Optional[int] = None

    update_start_at: Optional[int] = None
    update_end_at: Optional[int] = None

    widgets: list[WidgetFilter] | dict[str, str] | None = None

    # 是否超时
    is_timeout: Optional[int] = None
    # 当前步骤处理人
    assignee_id: Optional[int] = None
    platform_assignee_id: Optional[int] = None
    feisuo_assignee_id: Optional[int] = None
    assignee_group_uuid: Optional[List[str]] = None
    platform_assignee_group_uuid: Optional[List[str]] = None
    feisuo_assignee_group_uuid: Optional[List[str]] = None
    # 支持跨店
    form_name: Optional[str]
    sid: Optional[List[str]]
    keyword: list[str] | None
    flag: list[BusinessOrderFlag] | None
    # 客户端切换到列表视图时，会使用该接口
    page_tab: Optional[MyBusinessOrdersTab]

    class Config:
        smart_union = True

    @property
    def create_duration(self):
        if not self.create_start_at or not self.create_end_at:
            return
        return [self.create_start_at, self.create_end_at]

    @property
    def update_duration(self):
        if not self.update_start_at or not self.update_end_at:
            return
        return [self.update_start_at, self.update_end_at]

    @property
    def order_by_condition(self) -> list[OrderBy]:
        condition: list[FilterSchema.OrderBy] = []
        if not (order_by := self.configs.get("order_by")):
            return condition
        if isinstance(order_by, str):
            _field, _strategy = order_by.split(" ")
            condition.append(FilterSchema.OrderBy(fields=[_field], strategy=_strategy))
        elif isinstance(order_by, list):
            for each_order_by in order_by:
                condition.append(FilterSchema.OrderBy.parse_obj(each_order_by))
        return condition

    @root_validator(pre=True)
    def validate_status(cls, val):
        status = val.pop("status", [])
        if isinstance(status, (int, str)):
            status = [int(status)]
        val["status"] = status or None
        return val


class GetBusinessOrderByOid(BaseModel):
    oid: str
    sid: Optional[str]


class JobPatchBodySchema(BaseModel):
    data: dict = Field(default_factory=dict)
    action: Action
    force: bool = True

    next_job_assistant: Optional[NextJobAssistant] = None
    prev_job_assistant: Optional[NextJobAssistant] = None  # 上一人工步骤的处理人  在reject时选人用
    # 评论
    operate_reason: Optional[str] = ""
    # 转交/领取
    job_assistant: Optional[NextJobAssistant] = None

    assign_account_exception: PlanWhenAssignException = Field(default=PlanWhenAssignException.ENTER_EXCEPTION_POOL)
    is_admin: bool = Field(
        default=False,
        description="用于界定是否为任务中心侧提交过来的请求。任务中心侧基本上是无需判断用户是否为当前步骤的执行客服的。",
    )


class ColumnFilter(BaseModel):
    column: str
    operator: ColumnOperator
    value: Any

    @validator("operator", pre=True)
    def validate_op(cls, value):
        return ColumnOperator.get(value)

    def to_sql(self, col):
        value_handler = getattr(self.operator, "value_handler", None)
        operation = getattr(self.operator, "op", None)
        if value_handler:
            self.value = value_handler(self.value)
        if operation:
            return operation(col, self.value)
        else:
            return op(col, self.operator.value.operator, self.value)

    def _dict(self, **kwargs):
        data = super().dict(**kwargs)
        loguru.logger.error(data)
        data["operator"] = self.operator.name
        return data


class ListPageRequest(BaseModel):
    filters: List[ColumnFilter] = Field(default_factory=list)
    filter_relation: OperatorRelation = OperatorRelation.AND
    page: Optional[PositiveInt] = 1
    per_page: Optional[PositiveInt] = 10
    order_by: List[str] = Field(default_factory=lambda: ["-updated_at"])


class StepAssistantsResponse(BaseModel):
    success: bool = True
    msg: str | None = None
    data: List[AccountDetailV2]


class StepAssistantsRequest(BaseModel):
    keyword: Optional[str]


class BusinessOrderJobRequest(BaseModel):
    business_order_id: int
    job_id: int


class JobPatchFail(Failed):
    exception_code: Any = None
    step_id: Optional[Any]  # reject是需要告诉前端reject到了哪一步


class ExceptionInfo(BaseModel):
    id: int
    type: str
    reason: str = Field(title="异常原因")
    suggestion: str = Field(title="操作建议")


class ResultNotificationSchema(BaseModel):
    is_success: bool = Field(title="n8n是否执行成功")
    business_order_id: int
    job_id: int
    add_pool: Optional[bool] = Field(False, title="是否需要加入异常池")
    exception_info: Optional[ExceptionInfo]


class GetBusinessOrderActionsReqeust(BaseModel):
    business_order_id: int


class GetBatchBusinessOrderActionsRequest(BaseModel):
    business_order_ids: List[int]


class UnmaskBoDataReqSchema(BaseModel):
    id: int
    widget_key: str
    fields: List[str]
