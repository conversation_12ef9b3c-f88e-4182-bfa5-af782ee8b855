from typing import Optional, List

from pydantic import BaseModel, Field

from robot_processor.enums import NoticeChannel, NoticeCategory, DisplayStyle, NoticeScope, \
    NoticeStatus, NoticeAction


class AdminQueryArgument(BaseModel):
    page: Optional[int]
    size: Optional[int]


class QueryArgument(BaseModel):
    channel: Optional[NoticeChannel] = NoticeChannel.WORKBENCH
    category: Optional[NoticeCategory] = NoticeCategory.SYSTEM
    page: Optional[int]
    size: Optional[int]


class PutArgument(BaseModel):
    ids: Optional[List[int]]
    is_all: bool = False
    category: Optional[NoticeCategory]


class PostArgument(BaseModel):
    title: str
    content: str
    writer: str
    url: Optional[str]
    category: Optional[NoticeCategory] = NoticeCategory.SYSTEM
    display_style: Optional[DisplayStyle] = Field(None, alias="notice_header_image")
    channel: Optional[NoticeChannel] = NoticeChannel.WORKBENCH
    scope: Optional[NoticeScope] = Field(NoticeScope.ALL, alias="notice_receive_type")
    status: Optional[NoticeStatus] = NoticeStatus.SAVED
    org_ids: Optional[List[str]]
    platforms: Optional[List[str]]


class PatchArgument(BaseModel):
    title: Optional[str]
    content: Optional[str]
    writer: Optional[str]
    url: Optional[str]
    category: Optional[NoticeCategory] = NoticeCategory.SYSTEM
    display_style: Optional[DisplayStyle] = Field(None, alias="notice_header_image")
    channel: Optional[NoticeChannel] = NoticeChannel.WORKBENCH
    scope: Optional[NoticeScope] = Field(NoticeScope.ALL, alias="notice_receive_type")
    status: Optional[NoticeStatus] = NoticeStatus.SAVED
    org_ids: Optional[List[str]]
    platforms: Optional[List[str]]


class OperateArgument(BaseModel):
    action: NoticeAction


class SessionInfo(BaseModel):
    user_id: int
    org_id: str
    user_type: int
    platform: str
