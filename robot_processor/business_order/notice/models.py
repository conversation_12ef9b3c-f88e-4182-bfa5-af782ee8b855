import time
from enum import Enum
from typing import List, Any, Optional

import sqlalchemy as sa
from sqlalchemy import ForeignKey
from sqlalchemy.orm import mapped_column, Mapped, relationship

from robot_processor.db import DbBaseModel, BasicMixin
from robot_processor.enums import NoticeChannel, NoticeCategory, NoticeScope, NoticeStatus, \
    DisplayStyle


class Notice(DbBaseModel, BasicMixin):
    title: Mapped[str | None] = mapped_column(sa.String(50), comment="标题")
    content: Mapped[str | None] = mapped_column(sa.Text, comment="正文")
    released_time: Mapped[int] = mapped_column(sa.Integer, default=time.time, nullable=False, comment="发布时间")
    url: Mapped[str | None] = mapped_column(sa.Text, comment="链接")
    category: Mapped[NoticeCategory] = mapped_column(sa.Enum(NoticeCategory), default=NoticeCategory.SYSTEM,
                                                     comment="通知类别")
    display_style: Mapped[DisplayStyle | None] = mapped_column(sa.Enum(DisplayStyle), default=None,
                                                               comment="任务中心通知样式")  # noqa: E501
    channel: Mapped[NoticeChannel] = mapped_column(sa.Enum(NoticeChannel), default=NoticeChannel.WORKBENCH,
                                                   comment="通知渠道")
    scope: Mapped[NoticeScope] = mapped_column(sa.Enum(NoticeScope), default=NoticeScope.ALL, comment="通知范围")
    status: Mapped[NoticeStatus] = mapped_column(sa.Enum(NoticeStatus), default=NoticeStatus.SAVED,
                                                 comment="通知的当前状态")
    writer: Mapped[str | None] = mapped_column(sa.String(50), comment="撰稿人")
    # 以下是异常任务通知专用字段 category = NoticeCategory.EXCEPTION_TASK
    exception_notify_config: Mapped[dict] = mapped_column(sa.JSON,
                                                          comment="异常任务配置信息, 等于StepExceptionNotify.notify_config",
                                                          default=dict)

    def release(self) -> None:
        self.released_time = int(time.time())
        self.status = NoticeStatus.RELEASED

    def reject(self) -> None:
        self.status = NoticeStatus.SAVED

    def remove(self) -> None:
        self.status = NoticeStatus.DELETED

    @property
    def org_ids(self) -> List[str]:
        nos = NoticeOrgMapping.query.filter(
            NoticeOrgMapping.notice_id == self.id,
        ).all()
        org_ids = [n.org_id for n in nos]
        return org_ids  # type: ignore[return-value]

    @property
    def platforms(self) -> List[str]:
        nps = NoticePlatformMapping.query.filter(
            NoticePlatformMapping.notice_id == self.id,
        ).all()
        platforms = [n.platform for n in nps]
        return platforms  # type: ignore[return-value]

    def add_org_ids(self, session: Any, org_ids: Optional[List[str]]) -> None:
        """
        添加租户关联信息。
        :param session:
        :param org_ids:
        :return:
        """
        if org_ids is None:
            return
        session.bulk_insert_mappings(
            NoticeOrgMapping,
            [
                {
                    "notice_id": self.id,
                    "org_id": org_id,
                } for org_id in org_ids
            ]
        )

    def add_platforms(self, session: Any, platforms: Optional[List[str]]) -> None:
        """
        添加平台关联信息。
        :param session:
        :param platforms:
        :return:
        """
        if platforms is None:
            return
        session.bulk_insert_mappings(
            NoticePlatformMapping,
            [
                {
                    "notice_id": self.id,
                    "platform": platform,
                } for platform in platforms
            ]
        )


class NoticeUserMapping(DbBaseModel, BasicMixin):
    """用户已读的通知，系统通知用，因为系统通知数量在用户这个层级数量较大，不预先生成，只记录已读的数据"""
    notice_id: Mapped[int] = mapped_column(sa.Integer, comment="通知 ID")
    user_id: Mapped[int] = mapped_column(sa.Integer, comment="用户 ID")
    user_type: Mapped[int | None] = mapped_column(sa.Integer, comment="用户类型")


class NoticeUserException(DbBaseModel, BasicMixin):
    """需要发送给用户的通知，异常通知用，"""
    notice_id: Mapped[int] = mapped_column(sa.Integer, ForeignKey("notice.id"), comment="通知 ID", index=True)
    notice: Mapped["Notice"] = relationship("Notice",
                                            primaryjoin="foreign(NoticeUserException.notice_id)==Notice.id")
    user_id: Mapped[int] = mapped_column(sa.Integer, comment="用户 ID")
    user_type: Mapped[int | None] = mapped_column(sa.Integer, comment="用户类型")
    is_informed: Mapped[bool] = mapped_column(sa.Boolean, comment="是否已经站外通知过")
    job_id: Mapped[int | None] = mapped_column(sa.Integer, comment="任务 ID")


class NoticeOrgMapping(DbBaseModel, BasicMixin):
    """系统通知用， 指定租户id发的系统通知"""
    notice_id: Mapped[int] = mapped_column(sa.Integer, comment="通知 ID")
    org_id: Mapped[str | None] = mapped_column(sa.String(32), comment="指定租户 ID")


class NotifyOrgException(DbBaseModel, BasicMixin):
    """异常任务的站外通知用"""
    notice_id: Mapped[int] = mapped_column(sa.Integer, ForeignKey("notice.id"), comment="通知 ID")
    notice: Mapped["Notice"] = relationship("Notice",
                                            primaryjoin="foreign(NotifyOrgException.notice_id)==Notice.id")
    org_id: Mapped[int] = mapped_column(sa.Integer, comment="租户 ID")
    is_informed: Mapped[bool] = mapped_column(sa.Boolean, comment="是否已经站外通知过")
    job_id: Mapped[int | None] = mapped_column(sa.Integer, comment="任务 ID")


class NoticePlatformMapping(DbBaseModel, BasicMixin):
    notice_id: Mapped[int] = mapped_column(sa.Integer, comment="通知 ID")
    platform: Mapped[str | None] = mapped_column(sa.String(32), comment="指定的平台")


class ExceptionNotifyChannelEnum(str, Enum):
    dingding = "dingding"
    feishu = "feishu"
    wechat = "wechat"
