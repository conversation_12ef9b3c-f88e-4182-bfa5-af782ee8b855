"""工单操作"""

from dataclasses import dataclass
from typing import Callable
from typing import Optional
from typing import Sequence

from loguru import logger
from result import Err
from result import Ok
from sqlalchemy import func
from sqlalchemy.orm.attributes import flag_modified

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.business_order.exception_rule import ExceptionBusinessOrder
from robot_processor.business_order.fsm import BusinessOrderStatusController
from robot_processor.business_order.fsm import constants
from robot_processor.business_order.models import BusinessOrder
from robot_processor.business_order.models import Job
from robot_processor.business_order.models import JobApprover
from robot_processor.business_order.models import JobPool
from robot_processor.business_order.models import JobRemindRecord
from robot_processor.business_order.tasks import execute_job
from robot_processor.business_order.tasks import handle_circulation_notification
from robot_processor.business_order.tasks import package_jobs
from robot_processor.business_order.utils.operate_debounce import BusinessOrderOperateLock
from robot_processor.client import robot_transfer
from robot_processor.db import in_transaction
from robot_processor.enums import Action
from robot_processor.enums import ApproveType
from robot_processor.enums import AssigneeRule
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import Creator
from robot_processor.enums import FormCategory
from robot_processor.enums import JobProcessMark
from robot_processor.enums import JobStatus
from robot_processor.enums import PaymentStatus
from robot_processor.enums import PlanWhenAssignException
from robot_processor.enums import StepType
from robot_processor.enums import UpdateStrategy
from robot_processor.error.errors import AlipayBusinessOrderStatusNotSupportError
from robot_processor.error.errors import BusinessOrderError
from robot_processor.error.errors import CurrentJobUnMatchError
from robot_processor.error.errors import JobAssigneeAssistantAuthError
from robot_processor.error.errors import JobCandidateAssistantEmptyError
from robot_processor.error.errors import JobCanNotSkip
from robot_processor.error.errors import JobDeletedError
from robot_processor.error.errors import JobNeedAssigneeAssistantError
from robot_processor.error.errors import JobNoRejectToIDError
from robot_processor.error.errors import JobRetryConfigError
from robot_processor.error.errors import JobStatusUnSupportError
from robot_processor.error.errors import JobStepTypeUnSupportError
from robot_processor.error.errors import ScenarioError
from robot_processor.error.validate import ValidateError
from robot_processor.ext import db
from robot_processor.form.models import Step
from robot_processor.job.approve import ApproveJobController
from robot_processor.job.gateway.jump_gateway import JumpException
from robot_processor.job.gateway.jump_gateway import JumpGatewayJobController
from robot_processor.job.human_job import HumanJobController
from robot_processor.job.skip_helper import SkipHelper
from robot_processor.utils import get_nearest_prev_human_job_id
from robot_processor.utils import raise_exception
from robot_processor.utils import unwrap_optional


def _ensure_job_type_and_status(job, expected_step_types: Sequence[StepType], expected_statuses: Sequence[JobStatus]):
    if expected_step_types and StepType(job.raw_step_v2.get("step_type")) not in expected_step_types:
        raise JobStepTypeUnSupportError(job.id, job.raw_step_v2["step_type"], "[非法操作]任务类型不支持")

    if expected_statuses and job.status not in expected_statuses:
        raise JobStatusUnSupportError(job.id, job.status, expected_statuses, f"[非法操作]任务{job.status.label}")


class JobAction:
    __slots__ = ("job", "business_order", "logger", "controller", "operate_lock")

    def __init__(
        self,
        job_id: int,
        job: Optional[Job] = None,
        business_order_status_controller: BusinessOrderStatusController | None = None,
        operate_lock: BusinessOrderOperateLock | None = None,
    ):
        """
        Args:
            job_id: 任务 ID
        """

        job = job or Job.query.get(job_id)
        if not job:
            raise ValidateError("工单步骤不存在", code=404, job_id=job_id)
        self.job = job
        self.business_order: BusinessOrder = self.job.business_order
        if business_order_status_controller is not None:
            self.controller = business_order_status_controller
        else:
            self.controller = BusinessOrderStatusController(
                business_order=self.business_order,
                operator=AccountDetailV2(user_type=Creator.SYSTEM.value, user_id=0, user_nick="SYSTEM"),
                is_admin=True,
            )
        self.logger = logger.bind(k="JobAction", business_order_id=self.job.business_order_id, job_id=job_id)
        # 涉及到表单校验的会在校验通过之后进行 operate lock acquire
        # 其它的 action 会在最开始进行
        self.operate_lock = operate_lock

    def remind(self, operate_assistant: AccountDetailV2):
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.remind)
        if not ok:
            raise ScenarioError(reason)
        self.check_job_can_remind()
        with in_transaction():
            self.do_remind(remind_from_user=operate_assistant)
        # 因为催促日志要做聚合，放到定时任务中去做了

    def check_job_can_remind(self):
        if not self.job.is_human():
            e = JobStepTypeUnSupportError(job_id=self.job.id, step_type=self.job.raw_step_v2["step_type"])
            e.biz_display = "[非法操作]仅支持人工步骤"
            raise e

    def do_remind(self, remind_from_user: AccountDetailV2):
        from robot_processor.notify.services import create_job_remind_notify

        job = self.job
        business_order = job.business_order
        step = job.step
        form = step.form  # type: ignore[union-attr]
        shop = unwrap_optional(business_order.shop)

        if job.is_assignee_valid():  # 客服有效
            assistants = [job.get_assignee_assistant()]
        elif job.need_pick():  # 自由领取
            assistants = job.get_candidate_assistants()
        else:
            e = JobNeedAssigneeAssistantError(job_id=job.id)
            e.biz_display = "当前步骤无执行客服，建议指派客服后再催促"
            raise e

        if len(assistants) == 0:
            raise JobCandidateAssistantEmptyError(job_id=job.id)

        from_user = JobRemindRecord.User(
            user_id=unwrap_optional(remind_from_user.user_id),
            user_type=unwrap_optional(remind_from_user.user_type),
            user_nick=unwrap_optional(remind_from_user.user_nick),
        )
        data = JobRemindRecord.Data(
            business_order_id=job.business_order_id,  # type: ignore[typeddict-item]
            job_id=job.id,
            form_name=form.name,  # type: ignore[typeddict-item]
            step_name=step.name,  # type: ignore[union-attr,typeddict-item]
            sid=shop.sid,
            org_id=str(shop.org_id) if shop.org_id is not None else "",
        )
        message_digest = JobRemindRecord.calculate_digest(data)
        for assistant in assistants:
            to_user = JobRemindRecord.User(
                user_id=assistant.user_id,
                user_type=assistant.user_type,
                user_nick=assistant.user_nick,
            )
            with db.session.no_autoflush:
                (count,) = (
                    db.session.query(func.count(JobRemindRecord.id))
                    .filter(  # type: ignore[misc]
                        JobRemindRecord.v_from_user_id == remind_from_user.user_id,
                        JobRemindRecord.v_to_user_id == assistant.user_id,
                        JobRemindRecord.message_digest == message_digest,
                    )
                    .first()
                )
            if count == 0:
                create_job_remind_notify(
                    job=job,
                    from_user_nick=unwrap_optional(remind_from_user.user_nick),
                    to_user_nick=assistant.user_nick,
                    context=data,
                )
                db.session.add(
                    JobRemindRecord(
                        from_user=from_user,
                        to_user=to_user,
                        data=data,
                        message_digest=message_digest,
                    )
                )
            elif count < 10:
                db.session.add(
                    JobRemindRecord(
                        from_user=from_user,
                        to_user=to_user,
                        data=data,
                        message_digest=message_digest,
                    )
                )
            else:
                raise ScenarioError("您的催促过于频繁，请稍后再试")

    def reject(
        self,
        operate_assistant: AccountDetailV2,
        operate_reason: str,
        specified_reject_id: Optional[int] = None,  # 指定退回步骤
        pofa: PlanWhenAssignException = PlanWhenAssignException.ENTER_EXCEPTION_POOL,
        assignee_assistant: Optional[AccountDetailV2] = None,
    ):
        """退回

        权限：
            管理员
            当前任务执行客服
        """
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.reject)
        if not ok:
            raise ScenarioError(reason)
        # 不指定则默认退回到前一个人工步骤
        if not specified_reject_id:
            specified_reject_id = get_nearest_prev_human_job_id(self.job)
        self.check_job_can_reject(reject_to_job_id=specified_reject_id)

        with in_transaction():
            action_hook = self.do_reject(
                assistant=operate_assistant,
                operate_reason=operate_reason,
                specified_reject_id=specified_reject_id,
                plan_when_assign_exception=pofa,
                assignee_assistant=assignee_assistant,
            )
        if action_hook.after_commit is not None:
            action_hook.after_commit()

    def check_job_can_reject(self, reject_to_job_id):
        self.check_job_can_reject_without_assistant()
        if reject_to_job_id:
            reject_to_job = Job.query.get(reject_to_job_id)
        else:
            raise JobNoRejectToIDError(job_id=self.job.id)

        # 退回步骤必须可重复执行
        if not reject_to_job.step.can_retry:  # type: ignore[union-attr]
            raise JobRetryConfigError(job_id=reject_to_job.id)  # type: ignore[union-attr]

        if reject_to_job.step.deleted:  # type: ignore[union-attr]
            raise JobDeletedError(job_id=reject_to_job.id)  # type: ignore[union-attr]

    def check_job_can_reject_without_assistant(self):
        # 由于此前的 complete 没有将任务设置为 SUCCEED，所以完成的工单的当前任务可能不是 PENDING 或 SUCCEED。
        if not self.business_order.is_completed() and self.job.status not in [
            JobStatus.PENDING,
            JobStatus.SUCCEED,
        ]:  # 非等待、成功状态不能退回
            raise JobStatusUnSupportError(
                job_id=self.job.id,
                current_status=self.job.status,
                expect_status=JobStatus.PENDING,
            )

        # 第一步无法退回
        if not self.job.prev or self.job.prev.step_type == StepType.begin:
            raise JobStepTypeUnSupportError(job_id=self.job.id, step_type=self.job.raw_step_v2["step_type"])

        if len(self.controller.get_can_reject_steps()) == 0:
            raise JobStepTypeUnSupportError(job_id=self.job.id, step_type=self.job.raw_step_v2["step_type"])

    def do_revert(
        self,
        specified_reject_id: int,
        plan_when_assign_exception: PlanWhenAssignException = PlanWhenAssignException.ENTER_EXCEPTION_POOL,
        assignee_assistant: Optional[AccountDetailV2] = None,
    ):
        from robot_processor.assistant.constants import ASSISTANT_DISABLED

        action_hook = ActionHook()
        # 重新指派客服
        self.job.set_assignee_assistant(None)

        self.job.set_status(JobStatus.INIT)
        self.job.end_timing()
        self.job.remove_from_pool()
        self.job.clear_visit()
        current_job: Job | None = self.job
        prev_jobs = self.job.get_prev_jobs()

        for current_job in [current_job] + prev_jobs:  # 找到指定的退回步骤
            if current_job is None:
                break
            if current_job.step_type == StepType.approve:
                approve_job_controller = ApproveJobController(current_job)
                approve_job_controller.clean_archived_operated_approvers()
            current_job.process_mark = JobProcessMark.REJECT
            if current_job.id == specified_reject_id:
                self.business_order.set_current_execute_job(current_job)
                # 如果需要退回到遍历网关外，则需要重置遍历计数。
                try:
                    JumpGatewayJobController(self.job).handle_in_iterate_iteration(
                        unwrap_optional(self.job.step),
                        unwrap_optional(current_job.step),
                    )
                except JumpException as e:
                    Job.Utils.mark_failed(current_job, e)
                    current_job.start_timing()
                    break
                if current_job.step_type == StepType.human:
                    current_job.set_status(JobStatus.PENDING)
                    self.business_order.set_status(BusinessOrderStatus.PENDING)
                    assign_client = HumanJobController(job=current_job)
                    # 如果该步骤的执行客服正常，则跳出循环。
                    if assign_client.current_assignee is not None and assign_client.current_assignee.is_valid():
                        current_job.start_timing()
                        break

                    # 如果该步骤的执行客服异常，则进行以下处理。
                    # 1. 选择进入异常池。
                    if plan_when_assign_exception == PlanWhenAssignException.ENTER_EXCEPTION_POOL:
                        Job.Utils.mark_failed(current_job, ASSISTANT_DISABLED)
                        current_job.start_timing()
                        break
                    # 2. 使用指定的客服进行重新分派。
                    if assignee_assistant is not None:
                        res = assign_client.specify_user_assign(assignee_assistant, is_re_assign=True)
                        match res:
                            case Err(reason):
                                # 如果分配失败，则进入异常池。
                                Job.Utils.mark_failed(current_job, reason)
                            case Ok(assignee) if assignee is not None:
                                current_job.set_assignee_assistant(assignee)
                        current_job.start_timing()
                    else:
                        # 3. 基于策略进行重新分派。
                        assign_client.job.set_assignee_assistant(None)
                        assign_client.run_job()
                    break
                # 自动化任务直接执行
                else:
                    self.business_order.set_status(BusinessOrderStatus.RUNNING)
                    current_job.set_status(JobStatus.INIT)
                    action_hook.after_commit = lambda: execute_job.send_with_options(
                        args=(current_job.id,),
                        kwargs={"chain": True},
                        task_type=self.job.task_type,
                    )
                    current_job.start_timing()
                    break
            else:
                # 对中间的节点进行初始化
                current_job.set_status(JobStatus.INIT)

        return action_hook

    def do_reject(
        self,
        assistant,
        operate_reason,
        specified_reject_id,
        plan_when_assign_exception: PlanWhenAssignException = PlanWhenAssignException.ENTER_EXCEPTION_POOL,
        assignee_assistant: Optional[AccountDetailV2] = None,
    ):
        with db.session.no_autoflush, in_transaction():
            action_hook = self.do_revert(specified_reject_id, plan_when_assign_exception, assignee_assistant)

            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.REJECT,
                assistant=assistant,
                operate_reason=operate_reason,
            )
        return action_hook

    def recall(self, operate_assistant: AccountDetailV2, operate_reason: str):
        """撤回至目标任务

        权限：
            管理员
            目标任务执行客服
        """
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.recall)
        if not ok:
            raise ScenarioError(reason)
        self.check_job_can_recall()
        with in_transaction():
            self.do_recall(assistant=operate_assistant, operate_reason=operate_reason)

    def check_job_can_recall(self):
        job = self.job
        business_order = self.business_order
        transfer_status = None

        def check_transfer_status():
            nonlocal transfer_status
            if transfer_status is None:
                transfer = robot_transfer.find_transfer(self.job.business_order_id).get("transfer", {})
                transfer_status = PaymentStatus(transfer.get("status", PaymentStatus.INIT))
            if transfer_status in [PaymentStatus.PAYING, PaymentStatus.PAY_FINISH]:
                error = AlipayBusinessOrderStatusNotSupportError(transfer_status)
                error.biz_display = "当前工单创建了打款单，且打款单状态无法撤回"
                raise error

        if business_order.current_job.is_auto() and not business_order.is_completed():  # type: ignore[union-attr]
            # 当前任务是自动任务，且是打款但是打款任务已经支付时，禁止撤回
            if business_order.current_job.is_alipay():  # type: ignore[union-attr]
                check_transfer_status()

        if job.status != JobStatus.SUCCEED:
            raise JobStatusUnSupportError(
                job_id=job.id,
                current_status=job.status,
                expect_status=JobStatus.SUCCEED,
            )
        elif not bool(job.raw_step_v2.get("can_retry", False)):
            raise JobRetryConfigError(job.id)

        if not job.is_human():
            raise JobStepTypeUnSupportError(job_id=job.id, step_type=job.raw_step_v2["step_type"])

        current_job = job
        for next_job in current_job.get_next_jobs():
            if next_job is None:
                break
            if current_job.id == business_order.current_job_id:
                # 已经遍历到当前任务了 不需要再往下遍历
                break
            if next_job.status == JobStatus.SUCCEED and not bool(next_job.raw_step_v2.get("can_retry", False)):
                raise JobRetryConfigError(next_job.id)

    def do_recall(self, assistant: AccountDetailV2, operate_reason: str):
        job = self.job
        business_order = self.business_order

        current_job = job
        current_job.clear_visit()

        for next_job in current_job.get_next_jobs():
            if next_job is None:
                break
            with db.session.no_autoflush:
                next_job.set_status(JobStatus.INIT)
                next_job.process_mark = JobProcessMark.UNPROCESSED
                next_job.remove_from_pool()
                if not next_job.is_auto() and next_job.raw_step_assignee_rule == AssigneeRule.MANUAL:
                    next_job.set_assignee_assistant(None)

            if not next_job.is_auto():
                next_job.end_timing()
                break
            else:
                if next_job.is_alipay():
                    # 撤回打款单
                    business_order.modify_transfer("recall", unwrap_optional(assistant.user_nick), operate_reason)

        with db.session.no_autoflush:
            job.set_status(JobStatus.PENDING)
            job.remove_from_pool()
            job.new_task_notify()
            job.start_timing()

            business_order.set_current_execute_job(self.job)
            if business_order.status != BusinessOrderStatus.PAUSED:
                if job.step_type != StepType.human.value:
                    business_order.set_status(BusinessOrderStatus.RUNNING)
                else:
                    business_order.set_status(BusinessOrderStatus.PENDING)
            # 更新信息并发送操作日志。
            job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.RECALL,
                assistant=assistant,
                operate_reason=operate_reason,
            )

    def save(
        self,
        data_for_save: dict,
        operate_assistant: AccountDetailV2,
        operate_reason: str,
        ignore_check_type_remind_error=True,
    ):
        ok, reason = self.controller.can_do_by_action(Action.save)
        if not ok:
            raise ScenarioError(reason)
        self.check_job_can_save()
        form_validator = self.job.get_form_validator()
        form_validator.validate(data_for_save, exclude_bo_id=self.job.business_order_id).or_else(
            form_validator.post_validate(ignore_check_type_remind_error)
        ).or_else(raise_exception)
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        with in_transaction():
            self.do_save(
                data_for_save=data_for_save,
                assistant=operate_assistant,
                operate_reason=operate_reason,
            )
        self.business_order.do_risk_control()

    def check_job_can_save(self):
        _ensure_job_type_and_status(self.job, [StepType.human, StepType.approve], [])
        if self.job.id != self.business_order.current_job_id:
            raise CurrentJobUnMatchError(job_id=self.job.id, expect_job_id=self.business_order.current_job_id)  # type: ignore[arg-type]  # noqa: E501

    def do_save(self, data_for_save: dict, assistant: AccountDetailV2, operate_reason: str):
        from robot_processor.business_order.business_order_manager import BusinessManager
        from robot_processor.form.models import WidgetAutoNumber

        with db.session.no_autoflush:
            self.job.start_timing()
            self.job.set_status(JobStatus.PENDING)
            if (
                self.job.step_type != StepType.approve
                and self.business_order.status == BusinessOrderStatus.TO_BE_COLLECTED
            ):
                self.job.set_assignee_assistant(assistant)

            business_order_data = (self.business_order.data or {}).copy()
            business_order_data.update(data_for_save)
            # 2023.09.01 和前端约定组件无信息时，data 中没有这个组件的 key
            # 在 patch 操作中，需要修改删除某个组件的值，是通过传 {widget_info.key: None} 的方式实现
            business_order_data = {k: v for k, v in business_order_data.items() if v is not None}
            self.business_order.data = business_order_data
            self.business_order.set_status(BusinessOrderStatus.TO_BO_SUBMITTED)
            # 自动编号组件值处理
            WidgetAutoNumber.handle_widget_auto_number(self.business_order)

            # 可以更新订单 需要重建映射
            BusinessManager.update_business_order_trade(data_for_save, self.business_order)
            BusinessManager.merge_tid_oid_uid_from_data(self.business_order)
            flag_modified(self.business_order, "data")
            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.SAVE,
                assistant=assistant,
                operate_reason=operate_reason,
            )

    def accept(
        self,
        data_for_accept: dict,
        next_job_assignee_assistant: Optional[AccountDetailV2],
        operate_assistant: AccountDetailV2,
        operate_reason: str,
        ignore_check_type_remind_error=True,
    ):
        ok, reason = self.controller.can_do_by_action(Action.accept)
        if not ok:
            raise ScenarioError(reason)
        self.check_job_can_accept(next_job_assignee_assistant=next_job_assignee_assistant)
        form_validator = self.job.get_form_validator()
        form_validator.validate(data_for_accept, exclude_bo_id=self.job.business_order_id).or_else(
            form_validator.post_validate(ignore_check_type_remind_error)
        ).or_else(raise_exception)
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)

        with in_transaction():
            self.do_accept(
                data_for_accept=data_for_accept,
                next_job_assignee_assistant=next_job_assignee_assistant,
                assistant=operate_assistant,
                operate_reason=operate_reason,
            )
        package_jobs.send(self.business_order.id, self.job.id, True)
        self.business_order.do_risk_control()

    def check_job_can_accept(self, next_job_assignee_assistant: Optional[AccountDetailV2]):
        self.check_job_can_accept_without_assistant()

        next_job = self.job.next
        if not next_job:
            return
        if not next_job.is_human():
            return

        next_job_latest_step: Step | None = (
            Step.query.filter(
                Step.step_uuid == next_job.step_uuid,
                Step.deleted.isnot(True),
                Step.is_dirty.is_(False),
            )
            .order_by(Step.id.desc())
            .first()
        )
        if not next_job_latest_step:
            return
        next_job_assignee_rule = next_job_latest_step.assignee_rule
        if next_job_assignee_rule != AssigneeRule.MANUAL:
            return
        elif next_job_assignee_assistant is None:
            raise JobNeedAssigneeAssistantError(job_id=next_job.id, biz_display="当前步骤需要预先分配执行人")

        assistants_v2 = next_job_latest_step.get_assistants_v2()
        _next_job_candidate_assistants = assistants_v2.get_latest_assignee_account_details(next_job.shop)
        next_job_assignee_assistant = next_job_assignee_assistant.get_bound_leyan_user()
        if next_job_assignee_assistant not in _next_job_candidate_assistants:
            raise JobAssigneeAssistantAuthError(
                job_id=next_job.id,
                assignee_assistant=next_job_assignee_assistant,
                candidate_assistants=_next_job_candidate_assistants,
                biz_display="指定执行人不在当前步骤候选人中",
            )

    def check_job_can_accept_without_assistant(self):
        if self.job.status != JobStatus.PENDING:
            raise JobStatusUnSupportError(
                job_id=self.job.id,
                current_status=self.job.status,
                expect_status=JobStatus.PENDING,
            )

    def do_accept(self, data_for_accept, next_job_assignee_assistant, assistant, operate_reason):
        from robot_processor.business_order.business_order_manager import BusinessManager
        from robot_processor.form.models import WidgetAutoNumber

        with db.session.no_autoflush:
            self.job.set_status(JobStatus.SUCCEED)
            self.job.clear_visit()

            business_order_data = (self.business_order.data or {}).copy()
            business_order_data.update(data_for_accept)
            # 2023.09.01 和前端约定组件无信息时，data 中没有这个组件的 key
            # 在 patch 操作中，需要修改删除某个组件的值，是通过传 {widget_info.key: None} 的方式实现
            business_order_data = {k: v for k, v in business_order_data.items() if v is not None}
            self.business_order.data = business_order_data

            # 自动编号组件值处理
            WidgetAutoNumber.handle_widget_auto_number(self.business_order)

            if self.job.prev:
                # 非第一步时
                self.business_order.set_status(BusinessOrderStatus.RUNNING)  # 可以更新订单 需要重建映射
            BusinessManager.update_business_order_trade(data_for_accept, self.business_order)
            BusinessManager.merge_tid_oid_uid_from_data(self.business_order)
            flag_modified(self.business_order, "data")
            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.ACCEPT,
                assistant=assistant,
                operate_reason=operate_reason,
            )

            # 如果当前 job 有任务有效期，则结束计时。
            if self.job.deadline_info:
                self.job.end_timing()

            if self.job.get_assignee_assistant() != assistant:
                self.job.set_assignee_assistant(assistant)

            self.job.set_next_assignee(next_job_assignee_assistant)

    def retry(self, operate_assistant: AccountDetailV2, operate_reason: str, delay=None):
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.retry)
        if not ok:
            raise ScenarioError(reason)
        self.check_job_can_rpa_retry()

        with in_transaction():
            # 先清理之前重试的记录
            from robot_processor.form.models import JobAutoRetryRecord

            JobAutoRetryRecord.query.filter_by(job_id=self.job.id).delete()
            # 清理跳转记录或放行。
            JumpGatewayJobController(self.job).recount_or_pass_jump_record()

            self.do_rpa_retry(assistant=operate_assistant, operate_reason=operate_reason)
        if delay:
            package_jobs.send_with_options(args=(self.business_order.id, self.job.id), delay=delay)
        else:
            package_jobs(self.business_order.id, self.job.id)

    def check_job_can_retry(self):
        if self.job.status != JobStatus.SUCCEED:
            return

        if self.job.step_type == StepType.jump:
            return

        if not bool(self.job.raw_step_v2.get("can_retry", False)):
            raise JobRetryConfigError(self.job.id)

    def check_job_can_rpa_retry(self):
        self.check_job_can_retry()
        if self.job.step_type not in [StepType.auto, StepType.jump]:
            raise JobStepTypeUnSupportError(job_id=self.job.id, step_type=self.job.raw_step_v2["step_type"])
        if self.job.status != JobStatus.FAILED:
            raise JobStatusUnSupportError(
                job_id=self.job.id,
                current_status=self.job.status,
                expect_status=[JobStatus.FAILED],
            )

        if self.job.prev is None:
            e = JobRetryConfigError(job_id=self.job.id)
            e.biz_display = "当前步骤是第一步，不支持重试。"
            raise e

    def do_rpa_retry(self, assistant: AccountDetailV2, operate_reason: str):
        with db.session.no_autoflush:
            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.RETRY,
                assistant=assistant,
                operate_reason=operate_reason,
            )

    def update_order(
        self, data: dict, operate_assistant: AccountDetailV2, operate_reason: str, update_strategy: UpdateStrategy
    ):
        """更新工单数据，区别于订正是对历史步骤的表单数据进行更新，这里是对整个工单表单的数据进行更新"""
        from robot_processor.business_order.business_order_manager import BusinessManager

        ok, reason = self.controller.can_do_by_action(Action.update_order)
        if reason == constants.CURRENT_STATUS_NOT_SUPPORT_THIS_ACTION and update_strategy == UpdateStrategy.FORCE:
            ok = True
        if not ok:
            raise ScenarioError(reason)
        business_manager = BusinessManager.from_business_order(self.business_order)
        business_manager.type_check(data).or_else(raise_exception)
        with in_transaction():
            self.do_upgrade(
                data_for_upgrade=data,
                assistant=operate_assistant,
                operate_reason=operate_reason,
            )
        self.business_order.do_risk_control()

    def upgrade(
        self,
        data_for_upgrade: dict,
        operate_assistant: AccountDetailV2,
        operate_reason: str,
        ignore_check_type_remind_error: bool = True,
    ):
        ok, reason = self.controller.can_do_by_action(Action.upgrade)
        if not ok:
            raise ScenarioError(reason)
        self.check_job_can_upgrade()
        form_validator = self.job.get_form_validator()
        form_validator.validate(data_for_upgrade, exclude_bo_id=self.job.business_order_id).or_else(
            form_validator.post_validate(ignore_check_type_remind_error)
        ).or_else(raise_exception)
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        with in_transaction():
            self.do_upgrade(
                data_for_upgrade=data_for_upgrade,
                assistant=operate_assistant,
                operate_reason=operate_reason,
            )
        self.business_order.do_risk_control()

    def check_job_can_upgrade(self):
        if not self.job.is_human():
            raise JobStepTypeUnSupportError(job_id=self.job.id, step_type=self.job.raw_step_v2["step_type"])
        if not self.job.is_success():
            raise JobStatusUnSupportError(
                job_id=self.job.id,
                current_status=self.job.status,
                expect_status=JobStatus.SUCCEED,
            )

    def do_upgrade(self, data_for_upgrade: dict, assistant: AccountDetailV2, operate_reason: str):
        from sqlalchemy.orm.attributes import flag_modified

        from robot_processor.business_order.business_order_manager import BusinessManager

        with db.session.no_autoflush:
            business_order_data = (self.business_order.data or {}).copy()
            business_order_data.update(data_for_upgrade)
            # 2023.09.01 和前端约定组件无信息时，data 中没有这个组件的 key
            # 在 patch 操作中，需要修改删除某个组件的值，是通过传 {widget_info.key: None} 的方式实现
            business_order_data = {k: v for k, v in business_order_data.items() if v is not None}
            self.business_order.data = business_order_data
            # 可以更新订单 需要重建映射
            BusinessManager.update_business_order_trade(data_for_upgrade, self.business_order)
            BusinessManager.merge_tid_oid_uid_from_data(self.business_order)
            flag_modified(self.business_order, "data")
            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.UPGRADE,
                assistant=assistant,
                operate_reason=operate_reason,
            )

    def close(self, operate_assistant: AccountDetailV2, operate_reason: str):
        """工单关闭

        Raises:
            ScenarioError

        """
        from robot_processor.client import robot_transfer
        from robot_processor.enums import PaymentStatus
        from robot_processor.error.errors import BusinessOrderNotFoundError
        from robot_processor.error.errors import ScenarioError
        from robot_processor.error.errors import TransferStatusUnSupportError

        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.close)
        if not ok:
            raise ScenarioError(reason)

        # 仅在实际执行工单关闭操作时，检查打款单状态
        _form = self.business_order.form
        if _form is None:
            raise ScenarioError(
                f"没有找到 id 为 {self.business_order.form_id} 的工单模板",
                status_code=404,
            )
        if _form.name == FormCategory.ALIPAY.name:
            transfer_info = robot_transfer.find_transfer(self.job.business_order_id)
            if not transfer_info:
                raise BusinessOrderNotFoundError(
                    business_order_id=self.job.business_order_id,  # type: ignore[arg-type]
                    biz_display="打款单信息未找到",
                )
            if transfer_status := PaymentStatus(transfer_info.get("status", PaymentStatus.WAIT_APPROVAL)):
                if not transfer_status.can_close():
                    raise TransferStatusUnSupportError(
                        business_order_id=self.job.business_order_id,
                        current_status=transfer_status,
                        expect_status="OTHER",
                        biz_display="打款单状态为支付中，不支持当前操作",
                    )

        with in_transaction():
            self.do_close(assistant=operate_assistant, operate_reason=operate_reason)
        handle_circulation_notification.send(self.job.id, Action.close)

    def do_close(self, assistant: AccountDetailV2, operate_reason: str):
        self.job.clear_visit()
        with db.session.no_autoflush:
            self.job.remove_from_pool()
            JobApprover.clean_by_job_id(self.job.id)
            self.job.end_timing()
            business_order_extra_data = self.business_order.extra_data
            business_order_extra_data["prev_status"] = self.business_order.status.value
            self.business_order.extra_data = business_order_extra_data
            self.business_order.set_status(BusinessOrderStatus.CLOSE)
            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.CLOSE,
                assistant=assistant,
                operate_reason=operate_reason,
            )
            self.business_order.modify_transfer("close", unwrap_optional(assistant.user_nick), operate_reason)

        assert self.job in db.session.dirty, "job 未追踪变更"
        assert self.business_order in db.session.dirty, "business order 未追踪变更"
        ExceptionBusinessOrder.Utils.remove(self.job.business_order_id)

    def reopen(self, operate_assistant: AccountDetailV2, operate_reason: str):
        """

        Raises:
            ScenarioError

        """
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.reopen)
        if not ok:
            raise ScenarioError(reason)

        with in_transaction():
            self.do_reopen(assistant=operate_assistant, operate_reason=operate_reason)

    def do_reopen(self, assistant: AccountDetailV2, operate_reason: str):
        self.job.clear_visit()
        with db.session.no_autoflush:
            self.job.start_timing()
            business_order_extra_data = self.business_order.extra_data
            prev_status = business_order_extra_data.get("prev_status", BusinessOrderStatus.RUNNING)

            self.business_order.status = prev_status
            self.business_order.modify_transfer("recover", unwrap_optional(assistant.user_nick), operate_reason)
            if self.job.step_type == StepType.approve:
                ApproveJobController(self.job).init_approvers()
            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.REOPEN,
                assistant=assistant,
                operate_reason=operate_reason,
            )

        assert self.job in db.session.dirty, "job 未追踪变更"
        assert self.business_order in db.session.dirty, "business order 未追踪变更"

    def assign(
        self,
        assignee_assistant: AccountDetailV2,
        operate_assistant: AccountDetailV2,
        operate_reason: str,
    ):
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.assign)
        if not ok:
            raise ScenarioError(reason)
        self.check_job_can_assign()

        with in_transaction():
            self.do_assign(
                assignee_assistant=assignee_assistant,
                assistant=operate_assistant,
                operate_reason=operate_reason,
            )

    def check_job_can_assign(self):
        if not self.job.is_human():
            raise JobStepTypeUnSupportError(job_id=self.job.id, step_type=self.job.raw_step_v2["step_type"])
        if self.job.status == JobStatus.SUCCEED:
            e = JobStatusUnSupportError(
                job_id=self.job.id,
                current_status=self.job.status,
                expect_status=JobStatus.SUCCEED,
            )
            e.biz_display = "任务已完成"
            raise e

    def do_assign(
        self,
        assignee_assistant: AccountDetailV2,
        assistant: AccountDetailV2,
        operate_reason: str,
    ):
        with db.session.no_autoflush:
            assign_client = HumanJobController(job=self.job)
            match assign_client.specify_user_assign(assignee_assistant):
                case Err(reason):
                    Job.Utils.mark_failed(self.job, reason)
                case Ok(assignee):
                    self.job.set_assignee_assistant(assignee)
                    self.job.set_status(JobStatus.PENDING)
                    self.job.new_task_notify()
                    # 在指派时，工单的状态也要变成"待受理"。
                    self.business_order.set_status(BusinessOrderStatus.PENDING)
                    # 记录指派操作。
                    self.job.record_tips(
                        operator=unwrap_optional(assistant.user_nick),
                        assignee=unwrap_optional(assignee_assistant.user_nick),
                        reason=operate_reason,
                    )

            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.ASSIGN,
                assistant=assistant,
                operate_reason=operate_reason,
            )

    def deliver(
        self,
        deliver_to_assistant: AccountDetailV2,
        operate_assistant: AccountDetailV2,
        operate_reason: str,
    ):
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.deliver)
        if not ok:
            raise ScenarioError(reason)
        self.check_job_can_deliver()

        with in_transaction():
            self.do_deliver(
                deliver_to=deliver_to_assistant,
                assistant=operate_assistant,
                operate_reason=operate_reason,
            )

    def check_job_can_deliver(self):
        if self.job.status != JobStatus.PENDING:  # 非等待人工状态的任务不能转交
            raise JobStatusUnSupportError(
                job_id=self.job.id,
                current_status=self.job.status,
                expect_status=JobStatus.PENDING,
            )
        if not self.job.is_human():  # 非人工状态不能转交
            raise JobStepTypeUnSupportError(job_id=self.job.id, step_type=self.job.raw_step_v2["step_type"])

    def do_deliver(
        self,
        deliver_to: AccountDetailV2,
        assistant: AccountDetailV2,
        operate_reason: str,
    ):
        with db.session.no_autoflush:
            self.job.set_assignee_assistant(deliver_to)
            self.job.set_status(JobStatus.PENDING)

            self.controller.deliver()

            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.DELIVER,
                assistant=assistant,
                operate_reason=operate_reason,
            )

    def pick(self, operate_assistant: AccountDetailV2, operate_reason: str):
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.pick)
        if not ok:
            raise ScenarioError(reason)
        self.check_job_can_pick()

        with in_transaction():
            self.do_pick(assistant=operate_assistant, operate_reason=operate_reason)

    def check_job_can_pick(self):
        self.check_job_can_assign()

    def do_pick(self, assistant: AccountDetailV2, operate_reason: str):
        with db.session.no_autoflush:
            if bound_feisuo_user := assistant.bound_feisuo_user:
                assistant = bound_feisuo_user
            else:
                self.logger.warning(f"找不到飞梭用户: {assistant.user_type}:{assistant.user_id}")

            if self.job.in_pool(assistant):
                self.job.pick_from_pool(assistant)
            else:
                self.job.remove_from_pool()
                job_pool = JobPool(
                    sid=self.business_order.sid,
                    business_order_id=self.business_order.id,
                    job_id=self.job.id,
                    assignee_user_id=assistant.user_id,
                    assignee_user_type=Creator(assistant.user_type or 6),
                )
                db.session.add(job_pool)
            # job.assignee_assistant = assistant pick的时候 还不能完全从任务池中删除
            self.job.set_assignee_assistant(assistant, remove_from_pool=False)
            # 任务被领取后，工单应当进入"待受理"状态。
            self.business_order.set_status(BusinessOrderStatus.PENDING)

            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.PICK,
                assistant=assistant,
                operate_reason=operate_reason,
            )

    def pause(self, operate_assistant: AccountDetailV2, operate_reason: str):
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.pause)
        if not ok:
            raise ScenarioError(reason)
        self.check_job_can_pause()
        with in_transaction():
            self.do_pause(assistant=operate_assistant, operate_reason=operate_reason)
        handle_circulation_notification.send(self.job.id, Action.pause)

    def check_job_can_pause(self):
        _ensure_job_type_and_status(self.job, [StepType.human, StepType.approve], [JobStatus.PENDING])

    def do_pause(self, assistant: AccountDetailV2, operate_reason: str):
        with db.session.no_autoflush:
            business_order = self.business_order
            # "暂停"时，是需要记录一下工单的当前状态，以便"启用"时，进行状态恢复。
            business_order_extra_data = business_order.extra_data
            business_order_extra_data["prev_status"] = business_order.status.value
            business_order.extra_data = business_order_extra_data
            business_order.set_status(BusinessOrderStatus.PAUSED)
            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.PAUSE,
                assistant=assistant,
                operate_reason=operate_reason,
            )

    def unpause(self, operate_assistant: AccountDetailV2, operate_reason: str):
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.unpause)
        if not ok:
            raise ScenarioError(reason)
        self.check_job_can_unpause()
        with in_transaction():
            self.do_unpause(assistant=operate_assistant, operate_reason=operate_reason)

    def check_job_can_unpause(self):
        _ensure_job_type_and_status(self.job, [StepType.human, StepType.approve], [JobStatus.PENDING])

    def do_unpause(self, assistant: AccountDetailV2, operate_reason: str):
        with db.session.no_autoflush:
            # "启用"后，需要按照工单"暂停"前的状态进行恢复。
            business_order_extra_data = self.business_order.extra_data
            prev_status = business_order_extra_data.get("prev_status")
            if prev_status is not None:
                self.business_order.status = prev_status
            else:
                self.logger.warning("进行启用操作时，未发现暂停前状态，工单状态将设置为异常")
                self.business_order.set_status(BusinessOrderStatus.IN_EXCEPTION)
            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.UNPAUSE,
                assistant=assistant,
                operate_reason=operate_reason,
            )
            # 如果当前步骤为审批节点，并且已经满足了审批条件，那么直接通过审批。
            # 满足审批条件的原因可能是账号被禁用、步骤上的审批人变更等。
            if (
                self.job.step_type == StepType.approve
                and ApproveJobController(self.job).check_multi_to_sign_is_approved()
            ):
                # 将当前任务状态设置为 SUCCEED。
                self.job.set_status(JobStatus.SUCCEED)
                self.job.clear_visit()
                JobApprover.clean_by_job_id(self.job.id)
                handle_circulation_notification.send(self.job.id, Action.approve)
                package_jobs.send(self.job.business_order_id, self.job.id, True)
                self.job.update_process_mark_and_record_action(
                    process_mark=JobProcessMark.ENDORSE,
                    assistant=AccountDetailV2(user_type=Creator.SYSTEM, user_id=0, user_nick="系统"),
                    operate_reason="部分审批人账号被禁用，或审批人账号变动，已满足审批条件，自动通过审批。",
                )

    def delete(self, operate_assistant: AccountDetailV2, operate_reason: str):
        """工单删除

        Raises:
            ScenarioError
        """
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.delete)
        if not ok:
            raise ScenarioError(reason)

        with in_transaction():
            self.do_delete(assistant=operate_assistant, operate_reason=operate_reason)
        self.business_order.do_risk_control(action="DELETE")
        # FIXME 这里是一段临时代码，用于处理工单被删除时异常池的清理
        # 原因是现在工单的工单删除操作，前端传入的执行删除时的 job 是错误的，导致无法匹配到异常池中的 job
        # 这个临时方案会在 https://git.leyantech.com/digismart/robot-processor/-/issues/233 完成后移除
        ExceptionBusinessOrder.Utils.remove(self.business_order.id)

    def check_job_can_delete(self):
        if self.business_order.status == BusinessOrderStatus.RUNNING and self.job.status == JobStatus.RUNNING:
            raise JobStatusUnSupportError(
                self.job.id,
                self.job.status,
                "非处理中",
                f"[非法操作]任务{self.job.status.label}",
            )
        transfer = robot_transfer.find_transfer(self.job.business_order_id).get("transfer", {})
        transfer_status = PaymentStatus(transfer.get("status", PaymentStatus.INIT))
        if transfer_status.can_close():
            return
        raise AlipayBusinessOrderStatusNotSupportError(transfer_status, "当前工单创建了打款单，且打款单状态无法删除")

    def do_delete(self, assistant: AccountDetailV2, operate_reason: str):
        self.job.clear_visit()
        with db.session.no_autoflush:
            JobApprover.clean_by_job_id(self.job.id)
            self.job.end_timing()
            self.business_order.deleted = True
            self.business_order.modify_transfer("delete", unwrap_optional(assistant.user_nick), operate_reason)
            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.DELETE,
                assistant=assistant,
                operate_reason=operate_reason,
            )
        assert self.job in db.session.dirty, "job 未追踪变更"
        assert self.business_order in db.session.dirty, "business order 未追踪变更"

    def recover(self, operate_assistant: AccountDetailV2, operate_reason: str):
        """工单恢复

        Raises:
            ScenarioError
        """
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.recover)
        if not ok:
            raise ScenarioError(reason)
        self.check_job_can_recover()

        with in_transaction():
            self.do_recover(assistant=operate_assistant, operate_reason=operate_reason)
        self.business_order.do_risk_control()

    def check_job_can_recover(self):
        _ensure_job_type_and_status(
            self.job,
            [],
            (JobStatus.INIT, JobStatus.PENDING, JobStatus.FAILED, JobStatus.SUCCEED),
        )
        business_order = self.job.business_order
        if not business_order.deleted:
            raise BusinessOrderError("工单未删除，不可恢复")

    def do_recover(self, assistant: AccountDetailV2, operate_reason: str):
        self.job.clear_visit()
        with db.session.no_autoflush:
            self.job.start_timing()
            self.business_order.deleted = False
            self.business_order.modify_transfer("regain", unwrap_optional(assistant.user_nick), operate_reason)
            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.RECOVER,
                assistant=assistant,
                operate_reason=operate_reason,
            )
        assert self.job in db.session.dirty, "job 未追踪变更"
        assert self.business_order in db.session.dirty, "business order 未追踪变更"

    def skip(
        self,
        operate_assistant: AccountDetailV2,
        operate_reason: str,
        extra_data=None,  # 跳过步骤的必填项,批量跳过时为None
        next_job_assignee_assistant=None,  # 下一步需要手动分配客服时传入
    ):
        """跳过某步骤"""
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.skip)
        if not ok:
            raise ScenarioError(reason)
        self.check_job_can_skip(extra_data)
        with in_transaction():
            # 操作日志在skip中实现了
            self.do_skip(
                extra_data=extra_data,
                assistant=operate_assistant,
                operate_reason=operate_reason,
                next_job_assignee_assistant=next_job_assignee_assistant,
            )

    def check_job_can_skip(self, extra_data=None):
        skip_helper = SkipHelper(self.job)
        if not extra_data and skip_helper.get_skip_required_inputs(skip_helper.job.step):
            raise JobCanNotSkip("需要额外数据")

    def do_skip(
        self,
        extra_data: dict,
        assistant: AccountDetailV2,
        operate_reason: str,
        next_job_assignee_assistant=None,
    ):
        SkipHelper(self.job).skip(
            extra_data=extra_data,
            assistant=assistant,
            operate_reason=operate_reason,
            next_job_assignee_assistant=next_job_assignee_assistant,
        )
        # 跳过也需要记录一下 job_transition。
        package_jobs.send(self.job.business_order_id, self.job.id, True)

    def complete(self, operate_assistant: AccountDetailV2, operate_reason: str):
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.complete)
        if not ok:
            raise ScenarioError(reason)
        with in_transaction():
            self.do_complete(assistant=operate_assistant, operate_reason=operate_reason)
        handle_circulation_notification.send(self.job.id, Action.complete)

    def do_complete(self, assistant, operate_reason):
        self.job.clear_visit()
        with db.session.no_autoflush:
            # 将当前任务状态设置为 SUCCEED。
            self.job.set_status(JobStatus.SUCCEED)
            self.job.remove_from_pool()
            JobApprover.clean_by_job_id(self.job.id)
            self.job.end_timing()
            business_order_extra_data = self.business_order.extra_data
            business_order_extra_data["prev_status"] = self.business_order.status.value
            self.business_order.extra_data = dict(business_order_extra_data)
            self.business_order.set_status(BusinessOrderStatus.SUCCEED)
            # 更新信息并发送操作日志。
            logger.info(
                f"do complete, bo_id@{self.business_order.id}, job_id@{self.job.id}"
                f"operate_reason@{operate_reason}, assistant@{assistant}"
            )
            self.business_order.modify_transfer("close", unwrap_optional(assistant.user_nick), operate_reason)
            # 发送人工完成该步骤的操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.FINISH,
                assistant=assistant,
                operate_reason=operate_reason,
            )
            # 发送该工单已经完结的操作日志。
            self.job.business_order.complete_business_order_and_record_log()
        assert self.job in db.session.dirty, "job 未追踪变更"
        assert self.business_order in db.session.dirty, "business order 未追踪变更"
        ExceptionBusinessOrder.Utils.remove(self.job.business_order_id)

    def approve(
        self,
        operate_assistant: AccountDetailV2,
        operate_reason: str,
    ) -> None:
        """
        审批。
        审批操作内会根据是否满足审批条件来进行 package_jobs 操作。
        :return:
        """
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.approve)
        if not ok:
            raise ScenarioError(reason)
        with in_transaction():
            self.do_approve(assistant=operate_assistant, operate_reason=operate_reason)

    def do_approve(
        self,
        assistant: AccountDetailV2,
        operate_reason: str,
    ) -> None:
        """
        审批操作。
        如果是或签，只需要只要任意一人审批，便审批通过。
        会签和依次审批则需要检测是否满足审批条件，未满足条件则只更新审批人信息。
        审批通过，则需要将工单进行流转。
        """
        approve_job_controller = ApproveJobController(self.job)
        all_role_ids: list[int] = [
            i.user_id
            for i in (self.controller.roles | {assistant, assistant.bound_feisuo_user})
            if i is not None and i.user_type == Creator.LEYAN and i.user_id is not None
        ]
        match approve_job_controller.latest_version_step_assistants.approve_type:
            case ApproveType.ANY_ONE_TO_SIGN:
                is_approved = True
            case _:
                for role_id in all_role_ids:
                    job_approver = JobApprover.find_or_create(
                        job_id=self.job.id,
                        step_uuid=self.job.step_uuid,
                        user_id=role_id,
                    )
                    job_approver.is_approved = True
                db.session.flush()
                is_approved = approve_job_controller.check_multi_to_sign_is_approved()

        approve_job_controller.archive_operated_approvers(all_role_ids)
        # 2024.3.11: 按照前端要求，更改审批操作的 process_mark 为 endorse。
        self.job.update_process_mark_and_record_action(
            process_mark=JobProcessMark.ENDORSE,
            assistant=assistant,
            operate_reason=operate_reason,
        )

        if is_approved:
            # 将当前任务状态设置为 SUCCEED。
            self.job.set_status(JobStatus.SUCCEED)
            self.job.clear_visit()
            JobApprover.clean_by_job_id(self.job.id)
            handle_circulation_notification.send(self.job.id, Action.approve)
            package_jobs.send(self.job.business_order_id, self.job.id, True)

    def overrule(self, operate_assistant: AccountDetailV2, operate_reason: str) -> None:
        """
        驳回。
        只要有一人驳回，那便会退回至上一步。
        :param operate_assistant:
        :param operate_reason:
        :return:
        """
        self.operate_lock and self.operate_lock.acquire().or_else(raise_exception)
        ok, reason = self.controller.can_do_by_action(Action.overrule)
        if not ok:
            raise ScenarioError(reason)
        specified_reject_id = get_nearest_prev_human_job_id(self.job)
        if specified_reject_id is None:
            raise ScenarioError(constants.NO_REJECT_STEPS)
        with in_transaction():
            self.do_overrule(
                assistant=operate_assistant,
                operate_reason=operate_reason,
                specified_reject_id=specified_reject_id,
            )
        handle_circulation_notification.send(self.job.id, Action.overrule)

    def do_overrule(self, assistant: AccountDetailV2, operate_reason: str, specified_reject_id: int) -> None:
        """
        驳回操作。
        """
        from robot_processor.business_order.models import JobApprover

        with db.session.no_autoflush:
            JobApprover.clean_by_job_id(self.job.id)
            self.do_revert(
                specified_reject_id,
                PlanWhenAssignException.ENTER_EXCEPTION_POOL,
            )

            # 更新信息并发送操作日志。
            self.job.update_process_mark_and_record_action(
                process_mark=JobProcessMark.OVERRULE,
                assistant=assistant,
                operate_reason=operate_reason,
            )


@dataclass
class ActionHook:
    after_commit: Callable[[], None] | None = None
