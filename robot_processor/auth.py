from typing import Optional

import jwt
from flask import Request
from grpc import ServicerContext
from loguru import logger
from pydantic import BaseModel, ValidationError
from werkzeug.exceptions import Unauthorized

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.enums import UserType
from robot_processor.logging import vars as log_vars


def has_jwt(request: Request) -> bool:
    return request.headers.get('Authorization', "").strip().startswith('Bearer ')


def auth_flask_request(request: Request):
    auth(token=(request.headers.get('Authorization', "").strip()))


def auth_grpc_request(context: ServicerContext):
    metadata = context.invocation_metadata()
    for k, v in metadata:
        if k == 'authorization':
            token = v if isinstance(v, str) else v.decode('utf-8')
            auth(token)
            break
        # service request, skip check
        if k == 'authentication-token':
            break
    else:
        auth('')


def auth(token: str):
    from robot_processor.currents import g

    try:
        token_prefix = 'Bearer '
        if token is None:
            raise Unauthorized('authorization header not found')
        if not token.startswith(token_prefix):
            raise Unauthorized('authorization header not starts with Bear<PERSON>')
        payload_dict = jwt.decode(token[len(token_prefix):], options={"verify_signature": False})
        iss = payload_dict.get('iss')
        if iss == 'kiosk':
            if 'orgId' in payload_dict or 'storeId' in payload_dict:
                logger.warning('这是一条过期的飞鸽 jwt token, 以后将不再支持该格式')
                payload_dict.setdefault('user_id', payload_dict.get('login_user_id', 0))
                payload_dict.setdefault('phone_number', '')
                payload_dict.setdefault('login_type', 'org_user')
                payload_dict.setdefault('channel_type', 'doudian')
                payload_dict.setdefault('login_user_nick', payload_dict.get('nick', ''))

            # 兼容 kiosk 用于调用获取店铺授权信息时, 私签的 token
            if payload_dict.get('nick') == '乐言:kiosk':
                payload_dict.setdefault('user_id', 0),
                payload_dict.setdefault('phone_number', '')
                payload_dict.setdefault('login_type', 'leyan_user')
                payload_dict.setdefault('channel_id', 0)
                payload_dict.setdefault('login_user_type', UserType.LDAP)
                payload_dict.setdefault('login_user_id', 0)
                payload_dict.setdefault('login_user_nick', payload_dict['nick'])

            payload_dict.setdefault('store_name', '')
            # house 中签发的 token 格式与 kiosk token 小有差异，所以在这里做一下兼容
            if payload_dict.get('tool') == 'house':
                payload_dict['login_user_nick'] = payload_dict['nick']
            try:
                payload = KioskJwtPayload.parse_obj(payload_dict)
            except ValidationError as e:
                logger.opt(exception=e).error('invalid kiosk jwt')
                raise Unauthorized('invalid kiosk jwt')
        else:
            raise Unauthorized(f'unknown jwt iss {iss}')
        g.auth = payload
        log_vars.OrgId.set(g.auth.org_id)
        log_vars.Sid.set(g.auth.store_id)
        log_vars.LoginUserNick.set(g.auth.nick)
        g.login_user_detail = payload.get_user_detail()
    except Unauthorized as e:
        # 设置一个空的 auth, 以避免后续逻辑中需要频繁判断 g.auth 是否存在
        logger.bind(status=401).warning('auth token 校验失败 token={} error={}', token, e)
        g.auth = empty_jwt
        g.login_user_detail = None
    except Exception as e:
        logger.opt(exception=e).bind(status=401).error('auth error token={}', token)
        g.auth = empty_jwt
        g.login_user_detail = None


class KioskJwtPayload(BaseModel):
    """由 kiosk 签发的 jwt auth payload."""
    iss: str           # 签发者
    org_id: int        # 租户 id
    user_id: int       # 乐言账号 id, 如果是通过 ldap 登陆，则 user_id 为 0
    nick_name: str     # 乐言账号名
    phone_number: str  # 乐言账号中设置的手机号码
    login_type: str    # leyan_user: 乐言ldap账号用户, org_user: 正常租户内的用户
    channel_id: int    # 店铺id, refer::T_KIOSK_BASE_CHANNEL.ID
    channel_type: str  # 店铺类型，taobao/tmall/pdd/doudian 等
    store_id: str      # sid
    store_name: str    # 店铺名称
    login_user_type: UserType   # NOQA 登陆用户类型，如果是通过千牛侧边栏或者服务市场登陆，则为 Creator.ASSISTANT, 如果是通过短信登陆，则为 Creator.LEYAN, 如果是通过运营工具登陆，则为 Creator.LDAP
    login_user_id: int          # 登陆用户 id，仅对 ASSISTANT/LEYAN 有效
    login_user_nick: str        # 登陆账号名
    nick: str                   # 登陆账号名, 与 login_user_nick 一致
    iat: int                    # token 签发时间, 精确到秒
    exp: int                    # token 过期时间，通常是签发七天后, 精确到秒

    class Config:
        allow_mutation = False
        extra = "ignore"

    def get_user_detail(self) -> Optional[AccountDetailV2]:
        """从 kiosk 获取当前 token 对应用户的实时最新信息."""
        from robot_processor.client import kiosk_client
        if self.login_user_type not in [UserType.LEYAN.value, UserType.ASSISTANT.value]:
            logger.warning(f"用户类型是 {self.login_user_type} 无法从 kiosk 获取实时详细信息")
            return AccountDetailV2(
                user_type=self.login_user_type,
                user_id=self.login_user_id,
                user_nick=self.login_user_nick,
            )
        else:
            return kiosk_client.get_user_by_id(self.login_user_type, self.login_user_id)


# 验证失败时设置到 g.auth 上的 jwt payload
# 设置这个空值，主要时为了避免业务代码中需要频繁判断 g.auth 是否存在
empty_jwt = KioskJwtPayload(iss='', org_id=0, user_id=0, nick_name='', phone_number='', login_type='org_user',
                            channel_id=0, channel_type='taobao', store_id='', store_name='',
                            login_user_type=UserType.ASSISTANT, login_user_id=0, login_user_nick='', nick='',
                            iat=0, exp=0)
