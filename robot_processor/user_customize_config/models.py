import datetime
from enum import Enum

from loguru import logger
from pydantic import BaseModel, Field
import sqlalchemy as sa
from sqlalchemy.orm import mapped_column, Mapped

from robot_processor.db import DbBaseModel, in_transaction
from robot_processor.enums import UserType


class UserCustomizeConfig(DbBaseModel):
    __tablename__ = "user_defined_config"
    __table_args__ = (
        sa.Index(
            "user_defined_config.user_id_and_user_type.idx",
            "user_id",
            "user_type",
            "model",
        ),
    )

    class Type(int, Enum):
        CONFIG_TYPE_UNSPECIFIED = 0
        FORM_QUERY = 1
        ORG_FORM_QUERY = 2
        PLUGIN_LOCAL_STORE = 3
        LAYOUT = 4
        REPORTS_FILTERS_FIELDS = 5
        REPORTS_FILTERS = 6
        PREFERENCE = 7
        INVOICE_WORKFLOW_EXPORT = 8

    id: Mapped[int] = mapped_column(sa.Integer, primary_key=True)
    created_at: Mapped[datetime.datetime] = mapped_column(sa.TIMESTAMP, nullable=False, server_default=sa.func.now())
    updated_at: Mapped[datetime.datetime] = mapped_column(
        sa.TIMESTAMP,
        nullable=False,
        server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
    )

    org_id: Mapped[int] = mapped_column(sa.Integer, nullable=False, comment="租户id")
    sid: Mapped[str] = mapped_column(sa.String(32), nullable=False, comment="店铺id")
    user_id: Mapped[int] = mapped_column(sa.Integer, nullable=False, comment="用户id")
    user_type: Mapped[UserType] = mapped_column(
        sa.Enum(UserType), default=UserType.ASSISTANT, nullable=False, comment="用户类型"
    )
    model: Mapped[Type] = mapped_column(
        sa.Enum(Type), default=Type.FORM_QUERY, nullable=False, comment="店铺id"
    )
    config: Mapped[dict | None] = mapped_column(sa.JSON, nullable=True, comment="用户自定义配置")
    filters: Mapped[dict | None] = mapped_column(sa.JSON, nullable=True, comment="用户自定义筛选条件")

    @classmethod
    def get_org_form_query_config(cls, org_id, user_id, user_type) -> dict:
        org_config = cls.query.filter_by(
            model=UserCustomizeConfig.Type.ORG_FORM_QUERY,
            user_type=user_type, user_id=user_id, org_id=org_id
        ).first()

        if org_config:
            config = org_config.config
            logger.info('找到用户自定义配置 {}', config)
            return dict(
                favor=config.get("favor", []),  # type: ignore[union-attr]
                disable=config.get("disable", []),  # type: ignore[union-attr]
                sorts=config.get("sorts", []),  # type: ignore[union-attr]
            )
        else:
            logger.info('未找到用户自定义配置')
            return dict(favor=[], disable=[], sorts=[])

    @classmethod
    def create_or_update_org_form_query_config(cls, org_id, user_id, user_type, config):
        from robot_processor.ext import db

        org_config = cls.query.filter_by(
            model=UserCustomizeConfig.Type.ORG_FORM_QUERY,
            user_type=user_type, user_id=user_id, org_id=org_id
        ).first()

        if org_config:
            logger.info('更新用户自定义配置 {}', config)
            org_config.config = config
        else:
            logger.info('创建用户自定义配置 {}', config)
            org_config = cls(
                org_id=org_id,
                sid="",  # NOT NULL
                user_id=user_id,
                user_type=user_type,
                model=UserCustomizeConfig.Type.ORG_FORM_QUERY,
                config=config
            )

        db.session.add(org_config)
        db.session.commit()

    class Schemas:
        class FormQueryConfig(BaseModel):
            favor: list[int] = Field(description="常用工单")
            disable: list[int] = Field(description="隐藏工单")
            sorts: list[int] = Field(description="排序")

            @classmethod
            def default(cls):
                return cls(favor=[], disable=[], sorts=[])

    class Queries:
        @staticmethod
        def form_query_config(user_id: int, user_type: UserType, sid: str):
            result = UserCustomizeConfig.query.filter_by(
                model=UserCustomizeConfig.Type.FORM_QUERY,
                user_type=user_type, user_id=user_id, sid=sid
            ).first()

            if result:
                config = result.config
                return dict(
                    favor=config.get("favor", []),  # type: ignore[union-attr]
                    disable=config.get("disable", []),  # type: ignore[union-attr]
                    sorts=config.get("sorts", []),  # type: ignore[union-attr]
                )

        @staticmethod
        @in_transaction()
        def upsert_invoice_workflow_export_config(org_id: int, user_id: int, user_type: int, export_config: dict):
            config = UserCustomizeConfig.find_or_create(
                org_id=org_id, user_id=user_id, user_type=user_type, sid="",
                model=UserCustomizeConfig.Type.INVOICE_WORKFLOW_EXPORT
            )
            config.config = export_config
