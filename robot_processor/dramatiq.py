import time
from functools import wraps
from threading import local

import dramatiq
import opentracing
import prometheus_client as prom
from dramatiq import <PERSON><PERSON><PERSON>nco<PERSON>, set_broker, Actor
from dramatiq import actor as register_actor
from dramatiq.brokers.redis import RedisBroker
from dramatiq.common import current_millis
from dramatiq.errors import Retry
from dramatiq.message import Message, set_encoder
from dramatiq.middleware import Middleware, SkipMessage, CurrentMessage, AgeLimit, TimeLimit, Callbacks, Retries
from leyan_tracing import get_tracer
from loguru import logger

from robot_metrics.prometheus import registry


class ProcHealthCheckMiddleware(Middleware):

    def before_worker_boot(self, broker, worker):
        from robot_processor import utils
        utils.health_check(skip_apollo=True)


class AppContextMiddleware(Middleware):
    state = local()

    def __init__(self, app):
        self.app = app

    def before_process_message(self, broker, message):
        context = self.app.app_context()
        context.push()

        self.state.context = context

    def after_process_message(self, broker, message, *, result=None, exception=None):
        try:
            context = self.state.context
            context.pop(exception)
            del self.state.context
        except AttributeError:
            pass

    after_skip_message = after_process_message


class PipelineMiddleware(Middleware):
    """
    提供pipeline执行任务;
    提供查询状态中止任务;
    """

    @property
    def actor_options(self):
        return {
            "pipe_ignore",
            "pipe_target",
        }

    def after_process_message(
            self,
            broker,
            message,
            *,
            result=None,
            exception=None
    ):
        """Called after a message has been processed."""
        if exception is not None or message.failed:
            return
        from robot_processor.business_order.models import Job

        message_id: str = message.message_id
        job = Job.query.filter_by(message_id=message_id).first()
        if job:
            # 工单 pipeline 不再走middleware
            return

        return self.continue_next_message(broker, message, result)

    def continue_next_message(self, broker, current_message, result):
        actor = broker.get_actor(current_message.actor_name)
        message_data = current_message.options.get("pipe_target")
        if message_data is not None:
            next_message = Message(**message_data)
            pipe_ignore = next_message.options.get(
                "pipe_ignore") or actor.options.get("pipe_ignore")
            if not pipe_ignore:
                next_message = next_message.copy(
                    args=next_message.args + (result,)
                )

            broker.enqueue(
                next_message,
                delay=next_message.options.get("delay")
            )


def retry_when(retries, exception):
    return isinstance(exception, Retry)


class PrometheusMiddleware(Middleware):
    """
    copy 自 dramatiq.middleware.prometheus，之所以使用 copy 的方式，而不是直接使用或是继承的方式，是因为希望统一 robot-processor
    各个 proc 中的 prometheus http server 启动方式，避免使用 multiprocess mode.

    详情请参考: https://git.leyantech.com/digismart/robot-processor/-/issues/139
    """
    # 为什么在这里定义，而不是在 __init__ 中定义？
    # 因为每种 metrics 全局只需要定义一次，而不是每个 middleware 实例上都定义一次

    total_messages = prom.Counter(
        "dramatiq_messages_total",
        "The total number of messages processed.",
        ["queue_name", "actor_name"],
        registry=registry,
    )
    total_errored_messages = prom.Counter(
        "dramatiq_message_errors_total",
        "The total number of errored messages.",
        ["queue_name", "actor_name"],
        registry=registry,
    )
    total_retried_messages = prom.Counter(
        "dramatiq_message_retries_total",
        "The total number of retried messages.",
        ["queue_name", "actor_name"],
        registry=registry,
    )
    total_rejected_messages = prom.Counter(
        "dramatiq_message_rejects_total",
        "The total number of dead-lettered messages.",
        ["queue_name", "actor_name"],
        registry=registry,
    )
    inprogress_messages = prom.Gauge(
        "dramatiq_messages_inprogress",
        "The number of messages in progress.",
        ["queue_name", "actor_name"],
        registry=registry,
        multiprocess_mode="livesum",
    )
    inprogress_delayed_messages = prom.Gauge(
        "dramatiq_delayed_messages_inprogress",
        "The number of delayed messages in memory.",
        ["queue_name", "actor_name"],
        registry=registry,
    )
    message_durations = prom.Histogram(
        "dramatiq_message_duration_milliseconds",
        "The time spent processing messages.",
        ["queue_name", "actor_name"],
        buckets=(5, 10, 25, 50, 75, 100, 250, 500, 750, 1000, 2500, 5000,
                 7500, 10000, 30000, 60000, 600000, 900000, float("inf")),
        registry=registry,
    )

    def __init__(self):
        self.delayed_messages = set()
        self.message_start_times = {}

    def after_nack(self, broker, message):
        labels = (message.queue_name, message.actor_name)
        self.total_rejected_messages.labels(*labels).inc()

    def after_enqueue(self, broker, message, delay):
        if "retries" in message.options:
            labels = (message.queue_name, message.actor_name)
            self.total_retried_messages.labels(*labels).inc()

    def before_delay_message(self, broker, message):
        labels = (message.queue_name, message.actor_name)
        self.delayed_messages.add(message.message_id)
        self.inprogress_delayed_messages.labels(*labels).inc()

    def before_process_message(self, broker, message):
        labels = (message.queue_name, message.actor_name)
        if message.message_id in self.delayed_messages:
            self.delayed_messages.remove(message.message_id)
            self.inprogress_delayed_messages.labels(*labels).dec()

        self.inprogress_messages.labels(*labels).inc()
        self.message_start_times[message.message_id] = current_millis()

    def after_process_message(self, broker, message, *, result=None, exception=None):
        labels = (message.queue_name, message.actor_name)
        message_start_time = self.message_start_times.pop(message.message_id, current_millis())
        message_duration = current_millis() - message_start_time
        self.message_durations.labels(*labels).observe(message_duration)
        self.inprogress_messages.labels(*labels).dec()
        self.total_messages.labels(*labels).inc()
        if exception is not None:
            self.total_errored_messages.labels(*labels).inc()

    after_skip_message = after_process_message


class AutoScaleMiddleware(Middleware):
    total_unprocessed_messages = prom.Gauge(
        "dramatiq_unprocessed_messages_total",
        "The total number of unprocessed processed.",
        labelnames=["queue_name"],
        registry=registry,
    )

    def __init__(self):
        self.sample_count = 0

    def after_process_message(self, broker, message, *, result=None, exception=None):
        self.sample_count += 1
        if self.sample_count % 10 == 0:
            # 每执行十次任务  采样一次 避免过多请求redis
            labels = (message.queue_name,)
            count = broker.client.hlen(f"dramatiq:{message.queue_name}.msgs")
            self.total_unprocessed_messages.labels(*labels).set(count)


class QueueLatencyMetricMiddleware(Middleware):
    task_consumer_duration = prom.Histogram(
        "dramatiq_task_consumer_duration",
        "The task enqueue to consumer duration latency",
        ["queue_name", "actor_name", "job_type"],
        buckets=(50, 100, 200, 500, 1000, 1500, 2000, 3000, 5000, 10000, 25000, 50000,
                 100000, 300000, 600000, 900000, float("inf")),
        registry=registry,
    )

    task_execute_duration = prom.Histogram(
        "dramatiq_task_execute_duration",
        "The task enqueue to finish execute duration latency",
        ["queue_name", "actor_name", "job_type"],
        buckets=(50, 100, 200, 500, 1000, 1500, 2000, 3000, 5000, 10000, 25000, 50000,
                 100000, 300000, 600000, 900000, float("inf")),
        registry=registry,
    )

    def before_enqueue(self, broker, message, delay):
        # 是否要减去 delay 的时间?
        current_millis = int(round(time.time() * 1000))
        # 记录一下本次入队的时间
        message.options.setdefault("first_enqueue_at", current_millis)
        message.options['enqueue_at'] = current_millis

    def before_process_message(self, broker, message):
        # 开始处理这个消息的时间
        current_millis = int(round(time.time() * 1000))
        message.options['processed_at'] = current_millis

    def after_process_message(self, broker, message, *, result=None, exception=None):
        processed_at = message.options.get('processed_at', None)
        if not processed_at:
            logger.warning("message has no execute time")
            return
        labels = (message.queue_name, message.actor_name, message.options.get('task_type', "unknown"))
        # 记录一下从入队到消费完的时间
        enqueue_at = message.options.get('enqueue_at', None)
        if not enqueue_at:
            logger.warning("message has no enqueue time")
        else:
            consumer_millis = processed_at - enqueue_at
            self.task_consumer_duration.labels(*labels).observe(consumer_millis)
        # 不需要 retry ,则记录执行的延迟
        if not self._need_retry(broker, message, result, exception):
            first_enqueue_at = message.options.get("first_enqueue_at", None)
            if not first_enqueue_at:
                logger.warning("message has no first enqueue time")
            else:
                execute_millis = processed_at - first_enqueue_at
                self.task_execute_duration.labels(*labels).observe(execute_millis)

    # 参考 https://github.com/Bogdanp/dramatiq/blob/master/dramatiq/middleware/retries.py
    def _need_retry(self, broker, message, result=None, exception=None):
        if exception is None:
            return False

        actor = broker.get_actor(message.actor_name)
        throws = message.options.get("throws", None) or actor.options.get("throws", None)
        if throws and isinstance(exception, throws):
            return False

        retries = message.options.get("retries", 0)
        max_retries = message.options.get("max_retries", 0) or actor.options.get("max_retries", 20)
        retry_when = actor.options.get("retry_when", None)
        retry_callback_rejected = retry_when is not None and not retry_when(retries, exception)
        retry_times_exceeded = retry_when is None and max_retries is not None and retries >= max_retries
        return not (retry_callback_rejected and retry_times_exceeded)


class DramatiqLoggingMiddleware(Middleware):
    """处理 message 时，为此次处理过程添加全局日志 context, 并在 context 中加入 actor name 和 message id 等 dramatiq 框架相关信息.

    已知的 dramatiq message 中对问题排查有用的包括:
    - message_id  dramatiq worker 处理的 message 的唯一标识
    - actor_name  message 对应的 actor 名称

    对于业务类信息（如 business order id， sid 等），可以在 actor 实现中通过 current.logger_context.set_sid 等 API 进行添加.
    """

    def before_process_message(self, broker, message):
        from robot_processor.logging import vars as log_vars

        log_vars.DramatiqMessage.set(message)
        logger.debug(
            "processing with {} {} {}", message.args, message.kwargs, message.options
        )

    def after_process_message(self, broker, message, *, result=None, exception=None):
        from leyan_logging import context

        context.clear()

    def after_skip_message(self, broker, message):
        self.after_process_message(broker, message, result=None, exception=None)


class TracingMiddleware(Middleware):

    def before_enqueue(self, broker, message, delay):
        tracer = get_tracer()
        scope = tracer.start_active_span(operation_name=message.actor_name)
        tracing_info: dict[str, str] = {}
        try:
            tracer.inject(scope.span.context, opentracing.Format.TEXT_MAP, tracing_info)
        except (opentracing.UnsupportedFormatException,
                opentracing.InvalidCarrierException,
                opentracing.SpanContextCorruptedException) as e:
            logger.error(f'tracer.inject() failed: {e}')
        message.options['tracing_info'] = tracing_info

    def after_enqueue(self, broker, message, delay):
        self.finish_span()

    def before_process_message(self, broker, message):
        tracer = get_tracer()
        tracing_info = message.options.get("tracing_info", None)
        span_context = None
        if tracing_info:
            try:
                span_context = tracer.extract(opentracing.Format.TEXT_MAP, tracing_info)
            except (opentracing.UnsupportedFormatException,
                    opentracing.InvalidCarrierException,
                    opentracing.SpanContextCorruptedException) as e:
                logger.exception(f'tracer.extract() failed: {e}')
        tracer.start_active_span(operation_name=message.actor_name, child_of=span_context)

    def after_process_message(self, broker, message, *, result=None, exception=None):
        self.finish_span()

    def after_skip_message(self, broker, message):
        self.finish_span()

    def finish_span(self):
        scope = get_tracer().scope_manager.active
        if scope:
            scope.close()


def with_app_context(func):
    """装饰器，为函数添加 app context"""

    @wraps(func)
    def wrapper(*args, **kwargs):
        from robot_processor.app import app
        from flask import has_app_context
        if has_app_context():
            return func(*args, **kwargs)
        else:
            with app.app_context():
                return func(*args, **kwargs)

    return wrapper


class SyncJobMessageMiddleware(Middleware):
    @with_app_context
    def after_enqueue(self, broker, message, delay):
        from robot_processor.business_order.models import Job
        from robot_processor.ext import db
        try:
            if message.args and message.actor_name == "execute_job":
                job_id = message.args[0]
                job = Job.query.get(job_id)
                if job:
                    job.message_id = message.message_id
                    db.session.flush()
        except Exception as e:
            logger.error(e)


class SkippableMiddleware(Middleware):
    """ 自动跳过已经完成的 Job """
    @classmethod
    def id_to_key(cls, message_id):
        return f'finished_jobs:{message_id}'

    def before_process_message(self, broker: dramatiq.Broker, message: dramatiq.Message) -> None:
        from robot_processor.ext import cache
        if cache.get(self.id_to_key(message.message_id)) is not None:
            logger.bind(message_id=message.message_id).info("人工干预了job状态，取消pending中的任务成功")
            raise SkipMessage()

    @classmethod
    def mark_finished(cls, message_id, abort_ttl=86400):
        from robot_processor.ext import cache
        cache.set(cls.id_to_key(message_id), 1, timeout=abort_ttl)
        logger.bind(message_id=message_id).info("人工干预了job状态，取消pending中的任务")


class RetriesWithDifferentQueueMiddleware(Retries):
    def after_process_message(self, broker, message, *, result=None, exception=None):
        from robot_processor.client.conf import app_config
        from robot_processor.constants import TASK_QUEUE_JOB_RETRIES
        if exception is None:
            return
        if not isinstance(exception, Retry):
            return super().after_process_message(broker, message, result=result, exception=exception)

        retries = message.options.setdefault("retries", 0)
        if (
            retries > app_config.retry_job_queue_barrier_times
            and message.queue_name != TASK_QUEUE_JOB_RETRIES
        ):
            res = broker.enqueue(message.copy(queue_name=TASK_QUEUE_JOB_RETRIES))
            logger.warning(f"任务达到最大重试次数 {app_config.retry_job_queue_barrier_times} ({retries})，放入重试队列 {res}")
            return
        else:
            return super().after_process_message(broker, message, result=result, exception=exception)


class LazyActor:
    actor: Actor

    def __init__(self, fn, kwargs):
        self.fn = fn
        self.kwargs = kwargs

    def __call__(self, *args, **kwargs):
        return self.fn(*args, **kwargs)

    def __repr__(self):
        return "<{} {}.{}>".format(
            self.__class__.__name__,
            self.fn.__module__, self.fn.__name__,
        )

    def __getattr__(self, name):
        if not hasattr(self, 'actor'):
            raise AttributeError(name)
        return getattr(self.actor, name)

    def register(self, broker):
        from robot_processor.constants import TASK_QUEUE_JOB, TASK_QUEUE_JOB_RETRIES
        self.actor = register_actor(broker=broker, **self.kwargs)(self.fn)
        # 同时注册一个 retries-job 的相同 actor
        if self.kwargs.get("queue_name") == TASK_QUEUE_JOB:
            register_actor(
                broker=broker,
                **{
                    **self.kwargs,
                    "actor_name": f"retry_{self.fn.__name__}",
                    "queue_name": TASK_QUEUE_JOB_RETRIES
                }
            )(self.fn)

    def send(self, *args, **kwargs):
        return self.actor.send(*args, **kwargs)

    def send_with_options(self, *args, **kwargs):
        on_success = kwargs.get("on_success")
        if on_success and isinstance(on_success, LazyActor):
            kwargs["on_success"] = register_actor(on_success.fn)
        on_failure = kwargs.get("on_failure")
        if on_failure and isinstance(on_failure, LazyActor):
            kwargs["on_failure"] = register_actor(on_failure.fn)
        return self.actor.send_with_options(*args, **kwargs)


class TaskQueue:
    broker: RedisBroker

    def __init__(self):
        self.actors = []

    def init_app(self, app):
        # 由于pipeline + shutdown 的中断模式不是很好，需要结合业务
        # 因此这里重新写一个pipeline的中间件
        from robot_processor.client.conf import app_config

        # middleware 排列顺序很重要，不能轻易改动
        middlewares = [
            AppContextMiddleware(app),
            # 1440为目前最大间隔时间6分钟等待6天的次数.
            # 1440 是 message retry 的最大次数，retry job 分队列的阈值由 apollo config 提供
            RetriesWithDifferentQueueMiddleware(retry_when=retry_when, max_retries=1440),
            Callbacks(), TimeLimit(), AgeLimit(), SkippableMiddleware(), SyncJobMessageMiddleware(),
            DramatiqLoggingMiddleware(), TracingMiddleware(), QueueLatencyMetricMiddleware(), AutoScaleMiddleware(),
            PrometheusMiddleware(), PipelineMiddleware(), ProcHealthCheckMiddleware(), CurrentMessage(),
        ]

        self.broker = RedisBroker(url=app_config.TASK_QUEUE_URL, middleware=middlewares)
        set_encoder(PickleEncoder())
        set_broker(self.broker)

        for _actor in self.actors:
            _actor.register(broker=self.broker)

    def actor(self, fn=None, **kwargs):
        def decorator(fn):
            lazy_actor = LazyActor(fn, kwargs)
            self.actors.append(lazy_actor)
            if hasattr(self, 'broker'):
                lazy_actor.register(self.broker)
            return lazy_actor

        if fn:
            return decorator(fn)
        return decorator


task_queue = TaskQueue()
