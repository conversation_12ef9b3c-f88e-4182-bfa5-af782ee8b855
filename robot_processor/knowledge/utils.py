import time
from typing import TYPE_CHECKING
from typing import cast

from sqlalchemy.engine.row import Row
from sqlalchemy.orm import Session
from sqlalchemy.orm import scoped_session
from sqlalchemy.orm.query import Query
from sqlalchemy.sql.expression import and_
from sqlalchemy.sql.expression import or_
from sqlalchemy.sql.functions import func
from sqlalchemy.sql.selectable import Subquery

from robot_processor.db import in_transaction
from robot_processor.enums import Creator
from robot_processor.ext import cache
from robot_processor.knowledge.contants import EDIT_DOCUMENT_PREFIX
from robot_processor.knowledge.entities import ContextVar
from robot_processor.knowledge.entities import DocumentLockInfo
from robot_processor.knowledge.enums import MoveAction
from robot_processor.knowledge.enums import SearchLocation
from robot_processor.knowledge.models import Document
from robot_processor.knowledge.models import KnowledgeBaseRole
from robot_processor.knowledge.models import KnowledgeBaseRoleToStaffMapping
from robot_processor.knowledge.models import Project

if TYPE_CHECKING:
    from robot_processor.currents import RobotProcessorGlobals


def get_context_var(ctx: "RobotProcessorGlobals") -> tuple[ContextVar, None] | tuple[None, str]:
    """
    提取上下文信息。
    """
    if not hasattr(ctx, "login_user_detail"):
        return None, "没有获取用户信息"
    if (user := ctx.login_user_detail) is None:
        return None, "用户未登录"
    if user.user_type not in [Creator.ASSISTANT.value, Creator.LEYAN.value]:
        return None, "尚不支持的用户类型"
    if user.feisuo_user_id is None:
        return None, "用户没有绑定飞梭账号"
    if not hasattr(ctx, "shop"):
        return None, "没有获取店铺信息"
    if (shop := ctx.shop) is None:
        return None, "未查询到店铺信息"  # type: ignore[unreachable]
    if (org_id := shop.org_id) is None:
        return None, "该店铺未绑定租户"
    if (channel_id := shop.channel_id) is None:
        return None, "该店铺尚未在飞梭开店"
    return (
        ContextVar(
            org_id=org_id,
            user=user,
            channel_id=channel_id,
        ),
        None,
    )


def move_project_location(
    current_project_id: int,
    target_project_id: int | None,
    move_action: MoveAction,
    org_id: str,
    query_deleted_project: bool = False,
    target_knowledge_base_id: int | None = None,
) -> tuple[Project, None] | tuple[None, str]:
    """
    移动项目位置。
    """
    if move_action != MoveAction.ADD_CHILD and target_project_id is None:
        return None, "没有指定移动关联的项目"

    if move_action == MoveAction.ADD_CHILD and target_project_id is None and target_knowledge_base_id is None:
        return None, "没有指定知识库、项目等信息"

    # 找到当前项目和目标项目。
    current_project_query: Query = Project.query.filter(
        Project.id == current_project_id,
        Project.org_id == org_id,
    )
    if not query_deleted_project:
        current_project_query = current_project_query.filter(
            Project.deleted_at.is_(None),
        )
    current_project: Project | None = current_project_query.first()
    if current_project is None:
        return None, "未能找到当前项目"

    project_id_to_project_mapping: dict[int, Project] = {current_project.id: current_project}

    # 找到当前项目的前序和后序项目。
    current_prev_and_next_project_query: Query = Project.query.filter(
        Project.id.in_(
            [
                current_project.prev_id,
                current_project.next_id,
            ]
        ),
        Project.org_id == org_id,
    )
    if not query_deleted_project:
        current_prev_and_next_project_query = current_prev_and_next_project_query.filter(
            Project.deleted_at.is_(None),
        )
    current_prev_and_next_project = current_prev_and_next_project_query.all()

    project_id_to_project_mapping.update({project.id: project for project in current_prev_and_next_project})
    current_next_project: Project | None = None
    current_prev_project: Project | None = None
    next_id = 0
    prev_id = 0

    if current_project.next_id != 0:
        current_next_project = project_id_to_project_mapping.get(current_project.next_id)
        if current_next_project is None:
            return None, "未能找到后序项目"
        next_id = current_next_project.id
    if current_project.prev_id != 0:
        current_prev_project = project_id_to_project_mapping.get(current_project.prev_id)
        if current_prev_project is None:
            return None, "未能找到前序项目"
        prev_id = current_prev_project.id

    # 进行坐标移动。

    # 如果是移动到某个项目下，并且项目没有任何子项目。
    if move_action == MoveAction.ADD_CHILD:
        if target_project_id is None:
            if target_knowledge_base_id is None:
                return None, "缺少必要信息"
            parent_id: int = 0
            knowledge_base_id: int = target_knowledge_base_id
        else:
            target_project: Project | None = Project.query.filter(
                Project.id == target_project_id,
                Project.org_id == org_id,
                Project.deleted_at.is_(None),
            ).first()
            if target_project is None:
                return None, "未能找到目标项目"
            parent_id = target_project.id
            knowledge_base_id = target_project.knowledge_base_id

        # 找到目标项目或知识库的最外层的第一个项目。
        outermost_first_project = Project.query.filter(
            Project.knowledge_base_id == knowledge_base_id,
            Project.org_id == org_id,
            Project.parent_id == parent_id,
            Project.prev_id == 0,
            Project.deleted_at.is_(None),
        ).first()

        if current_prev_project is not None:
            current_prev_project.next_id = next_id
        if current_next_project is not None:
            current_next_project.prev_id = prev_id
        current_project.parent_id = parent_id
        current_project.prev_id = 0
        # 如果目标项目或知识库下存在项目
        if outermost_first_project is not None:
            current_project.next_id = outermost_first_project.id
            outermost_first_project.prev_id = current_project.id
        else:
            current_project.next_id = 0
        current_project.knowledge_base_id = knowledge_base_id
        return current_project, None

    # 找到目标项目。
    target_project_query: Query = Project.query.filter(
        Project.id == target_project_id,
        Project.org_id == org_id,
        Project.deleted_at.is_(None),
    )
    target_project = target_project_query.first()
    if target_project is None:
        return None, "未能找到目标项目"

    # 查询一下目标项目的前序和后序。
    target_prev_and_next_project_query: Query = Project.query.filter(
        Project.id.in_(
            [
                target_project.prev_id,
                target_project.next_id,
            ]
        ),
        Project.org_id == org_id,
    )
    if not query_deleted_project:
        target_prev_and_next_project_query = target_prev_and_next_project_query.filter(
            Project.deleted_at.is_(None),
        )
    target_prev_and_next_project = target_prev_and_next_project_query.all()

    project_id_to_project_mapping.update({project.id: project for project in target_prev_and_next_project})

    if current_prev_project is not None:
        current_prev_project.next_id = next_id
    if current_next_project is not None:
        current_next_project.prev_id = prev_id
    # 如果是移动到当前目录队首。
    if move_action == MoveAction.MOVE_BEFORE and target_project.prev_id == 0:
        target_project.prev_id = current_project.id
        current_project.prev_id = 0
        current_project.next_id = target_project.id
        current_project.parent_id = target_project.parent_id
        current_project.knowledge_base_id = target_project.knowledge_base_id
        return current_project, None
    # 如果是移动到指定的项目前，并且指定的项目不在队首。
    elif move_action == MoveAction.MOVE_BEFORE and target_project.prev_id != 0:
        prev_project = project_id_to_project_mapping.get(target_project.prev_id)
        if prev_project is None:
            return None, "未能找到前序项目"
        prev_project.next_id = current_project.id
        current_project.prev_id = prev_project.id
        current_project.next_id = target_project.id
        target_project.prev_id = current_project.id
        current_project.parent_id = target_project.parent_id
        current_project.knowledge_base_id = target_project.knowledge_base_id
        return current_project, None
    # 如果移动到当前目录的队尾。
    elif move_action == MoveAction.MOVE_AFTER and target_project.next_id == 0:
        target_project.next_id = current_project.id
        current_project.prev_id = target_project.id
        current_project.next_id = 0
        current_project.parent_id = target_project.parent_id
        current_project.knowledge_base_id = target_project.knowledge_base_id
        return current_project, None
    # 如果是移动到指定的项目后，并且指定的项目不在队尾。
    elif move_action == MoveAction.MOVE_AFTER and target_project.next_id != 0:
        next_project = project_id_to_project_mapping.get(target_project.next_id)
        if next_project is None:
            return None, "未能找到后序项目"
        target_project.next_id = current_project.id
        current_project.prev_id = target_project.id
        current_project.next_id = next_project.id
        next_project.prev_id = current_project.id
        current_project.parent_id = next_project.parent_id
        current_project.knowledge_base_id = next_project.knowledge_base_id
        return current_project, None
    else:
        return None, "未能匹配"


def check_keyword_is_valid(keyword: str) -> bool:
    if not keyword:
        return False
    if len(keyword.strip()) == 0:
        return False
    return True


def check_keyword_in_text(keyword: str, text: str) -> tuple[bool, str]:
    if not text:
        return False, ""
    if keyword not in text:
        return False, ""
    idx = text.index(keyword)
    prev_idx = idx - 20
    if prev_idx < 0:
        prev_idx = 0
    next_idx = idx + 20
    return True, text[prev_idx:next_idx]


def generate_all_latest_document_ids_query(
    session: scoped_session[Session],
    org_id: str,
    query_dirty_document: bool,
    knowledge_base_ids: list[int] | None = None,
) -> Query:
    # 每个项目的最新的版本的知识内容的 ID。
    all_latest_document_ids_query: Query = (
        session.query(func.max(Document.id).label("id"))
        .join(
            Project,
            Project.id == Document.project_id,
        )
        .filter(
            Project.org_id == org_id,
            Project.deleted_at.is_(None),
        )
    )
    if knowledge_base_ids is not None:
        all_latest_document_ids_query = all_latest_document_ids_query.filter(
            Project.knowledge_base_id.in_(knowledge_base_ids)
        )
    # 如果不需要查询草稿，则将草稿状态的知识过滤。
    if not query_dirty_document:
        all_latest_document_ids_query = all_latest_document_ids_query.filter(
            Document.is_released.is_(True),
        )
    return all_latest_document_ids_query


def search_documents_by_keywords(
    org_id: str,
    keywords: list[str],
    query_dirty_document: bool = False,
    knowledge_base_ids: list | None = None,
    page_no: int | None = 1,
    page_size: int | None = 10,
) -> tuple[int, list[Row[tuple[int, int, int, str, str, str]]]]:
    """
    通过关键词查询文档。

    如果之后数据库的 like 查询性能还是有明显的问题，那就转为 es 全文检索实现。
    """
    # 使用 like 查询条件。（实验了下，MySQL 的全文索引分词不好用）
    name_match_expr = and_(*[Project.name.like(f"%{k}%") for k in keywords]).self_group()
    intro_match_expr = and_(*[Document.intro.like(f"%{k}%") for k in keywords]).self_group()
    preview_match_expr = and_(*[Document.preview.like(f"%{k}%") for k in keywords]).self_group()

    with in_transaction() as session:
        all_latest_document_ids_query = generate_all_latest_document_ids_query(
            session,
            org_id,
            query_dirty_document,
            knowledge_base_ids,
        )

        all_latest_document_ids_subquery: Subquery = all_latest_document_ids_query.group_by(
            Document.project_id,
        ).subquery()
        # 生成查询语句。
        document_query = (
            session.query(
                Project.id,
                Project.knowledge_base_id,
                Project.parent_id,
                Project.name,
                Document.intro,
                Document.preview,
            )
            .join(
                Project,
                Project.id == Document.project_id,
            )
            .join(
                all_latest_document_ids_subquery,
                all_latest_document_ids_subquery.c.id == Document.id,
            )
        )

        document_query = document_query.filter(
            or_(
                name_match_expr,
                intro_match_expr,
                preview_match_expr,
            ).self_group()
        ).order_by(Project.id.desc())

        total = document_query.count()
        # 分页。
        if page_no is not None and page_no > 0 and page_size is not None and page_size > 0:
            document_query = document_query.offset(
                (page_no - 1) * page_size,
            ).limit(page_size)

        items: list[Row[tuple[int, int, int, str, str, str]]] = document_query.all()

    return total, items


def location_keywords_in_text(
    keywords: list[str],
    project_name: str,
    document_intro: str,
    document_preview: str,
) -> tuple[str, SearchLocation | None]:
    """
    定位关键词。
    """
    got_text = ""
    location = None
    for k in keywords:
        # 在标题中查询
        in_text, found_text = check_keyword_in_text(
            k,
            project_name,
        )
        if in_text:
            got_text = found_text
            location = SearchLocation.TITLE
            break

        # 在摘要中查询
        in_text, found_text = check_keyword_in_text(
            k,
            document_intro,
        )
        if in_text:
            got_text = found_text
            location = SearchLocation.INTRO
            break

        # 在正文中查询
        in_text, found_text = check_keyword_in_text(
            k,
            document_preview,
        )
        if in_text:
            got_text = found_text
            location = SearchLocation.CONTENT
            break

    return got_text, location


class DocumentEditLock:
    """
    文档编辑锁。
    """

    LOCK_TIMEOUT = 30

    def __init__(self, user_id: int, document: Document) -> None:
        self.user_id = user_id
        self.document = document
        self.broker = cache

    @property
    def key(self) -> str:
        return EDIT_DOCUMENT_PREFIX.format(str(self.document.project_id))

    def acquire(self) -> tuple[DocumentLockInfo, None] | tuple[None, str]:
        lock_info: DocumentLockInfo | None = self.broker.get(self.key)
        if lock_info and lock_info.get("user_id") != self.user_id:
            return None, "其他人正在编辑该知识"

        if not lock_info:
            lock_info = DocumentLockInfo(
                user_id=self.user_id,
                key=self.key,
                last_updated_at=self.document.updated_at,
            )
        self.broker.set(self.key, lock_info, self.LOCK_TIMEOUT)
        return lock_info, None

    def refresh(self) -> tuple[DocumentLockInfo, None] | tuple[None, str]:
        lock_info, err = self.acquire()
        if err is not None:
            return None, err
        lock_info = cast(DocumentLockInfo, lock_info)
        lock_info["last_updated_at"] = int(time.time())
        self.broker.set(self.key, lock_info, self.LOCK_TIMEOUT)
        return lock_info, None

    def release(self) -> str | None:
        lock_info = self.broker.get(self.key)
        if lock_info and lock_info.get("user_id") != self.user_id:
            return "非当前用户"

        self.broker.delete(self.key)
        return None


def get_user_knowledge_base_roles(
    org_id: str,
    user_id: int,
    groups: list[str],
) -> list[KnowledgeBaseRole]:
    """
    获取账号及其账号组对应的所有知识库角色。
    """
    tags = groups + [str(user_id)]
    roles: list[KnowledgeBaseRole] = (
        KnowledgeBaseRole.query.join(
            KnowledgeBaseRoleToStaffMapping,
            KnowledgeBaseRoleToStaffMapping.role_id == KnowledgeBaseRole.id,
        )
        .filter(
            KnowledgeBaseRole.org_id == org_id,
            KnowledgeBaseRole.deleted_at.is_(None),
        )
        .filter(
            KnowledgeBaseRoleToStaffMapping.tag.in_(tags),
        )
        .all()
    )

    return roles
