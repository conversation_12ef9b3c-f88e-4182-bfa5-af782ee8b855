import time
from typing import Any
from typing import cast

from flask import Blueprint
from flask import jsonify
from flask import request
from flask_sqlalchemy.pagination import Pagination
from flask_sqlalchemy.query import Query as FlaskQuery
from loguru import logger
from sqlalchemy.orm import load_only
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy.orm.query import Query
from sqlalchemy.sql.expression import or_
from sqlalchemy.sql.functions import func
from sqlalchemy.sql.selectable import Subquery
from sqlalchemy.sql.selectable import and_

from robot_processor.currents import g
from robot_processor.db import in_transaction
from robot_processor.decorators import shop_required
from robot_processor.ext import db
from robot_processor.knowledge.entities import ContextVar
from robot_processor.knowledge.entities import KnowledgeBaseHistoryData
from robot_processor.knowledge.entities import ProjectHistoryData
from robot_processor.knowledge.entities import TrashItem
from robot_processor.knowledge.enums import DocumentAction
from robot_processor.knowledge.enums import HistoryAction
from robot_processor.knowledge.enums import ItemType
from robot_processor.knowledge.enums import KnowledgeBaseOrderField
from robot_processor.knowledge.enums import KnowledgeBaseRolePrivilege
from robot_processor.knowledge.enums import KnowledgeBaseSelectType
from robot_processor.knowledge.enums import MoveAction
from robot_processor.knowledge.enums import ProjectStatus
from robot_processor.knowledge.enums import ProjectType
from robot_processor.knowledge.enums import Sort
from robot_processor.knowledge.enums import StaffMold
from robot_processor.knowledge.enums import Tab
from robot_processor.knowledge.fsm import PrivilegeChecker
from robot_processor.knowledge.models import Document
from robot_processor.knowledge.models import KnowledgeBase
from robot_processor.knowledge.models import KnowledgeBaseHistory
from robot_processor.knowledge.models import KnowledgeBaseRole
from robot_processor.knowledge.models import KnowledgeBaseRoleToKnowledgeBaseMapping
from robot_processor.knowledge.models import KnowledgeBaseRoleToStaffMapping
from robot_processor.knowledge.models import MyFavorite
from robot_processor.knowledge.models import Project
from robot_processor.knowledge.models import ProjectHistory
from robot_processor.knowledge.models import complete_user_name
from robot_processor.knowledge.schemas import ApproveDocumentBody
from robot_processor.knowledge.schemas import AutoSaveDocumentBody
from robot_processor.knowledge.schemas import CreateCategoryBody
from robot_processor.knowledge.schemas import CreateDocumentBody
from robot_processor.knowledge.schemas import CreateKnowledgeBaseBody
from robot_processor.knowledge.schemas import CreateOrUpdateKnowledgeBaseRoleBody
from robot_processor.knowledge.schemas import DefaultArgument
from robot_processor.knowledge.schemas import DeleteKnowledgeBaseBody
from robot_processor.knowledge.schemas import DeleteProjectBody
from robot_processor.knowledge.schemas import FindDocumentsArgument
from robot_processor.knowledge.schemas import GetHistoryDetailArgument
from robot_processor.knowledge.schemas import KnowledgeBaseArgument
from robot_processor.knowledge.schemas import ModifyDocumentBody
from robot_processor.knowledge.schemas import ModifyKnowledgeBaseContentBody
from robot_processor.knowledge.schemas import MoveProjectBody
from robot_processor.knowledge.schemas import QueryActionsArgument
from robot_processor.knowledge.schemas import QueryDocumentsArgument
from robot_processor.knowledge.schemas import QueryHistoriesArgument
from robot_processor.knowledge.schemas import QueryLatestProjectArgument
from robot_processor.knowledge.schemas import QueryProjectArgument
from robot_processor.knowledge.schemas import RecoverTrashBody
from robot_processor.knowledge.schemas import RenameProjectBody
from robot_processor.knowledge.schemas import SearchDocumentsArgument
from robot_processor.knowledge.schemas import TagFavoriteBody
from robot_processor.knowledge.utils import DocumentEditLock
from robot_processor.knowledge.utils import check_keyword_is_valid
from robot_processor.knowledge.utils import generate_all_latest_document_ids_query
from robot_processor.knowledge.utils import get_context_var
from robot_processor.knowledge.utils import get_user_knowledge_base_roles
from robot_processor.knowledge.utils import location_keywords_in_text
from robot_processor.knowledge.utils import move_project_location
from robot_processor.knowledge.utils import search_documents_by_keywords
from robot_processor.validator import validate

api = Blueprint("knowledge-api", __name__)


"""
知识库的相关操作。
"""


@api.get("/knowledge_bases")
@shop_required
@validate
def query_knowledge_bases(query: KnowledgeBaseArgument):
    """
    查看所有的知识库。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    knowledge_bases_query: FlaskQuery = KnowledgeBase.query.filter(
        KnowledgeBase.org_id == context_var.get_org_id_in_text_format(),
        KnowledgeBase.deleted_at.is_(None),
    )

    # 排序
    if query.order_field == KnowledgeBaseOrderField.CREATED_AT:
        if query.sort == Sort.DESC:
            knowledge_bases_query = knowledge_bases_query.order_by(
                KnowledgeBase.created_at.desc(),
            )
        else:
            knowledge_bases_query = knowledge_bases_query.order_by(
                KnowledgeBase.created_at.asc(),
            )
    elif query.order_field == KnowledgeBaseOrderField.UPDATED_AT:
        if query.sort == Sort.DESC:
            knowledge_bases_query = knowledge_bases_query.order_by(
                KnowledgeBase.updated_at.desc(),
            )
        else:
            knowledge_bases_query = knowledge_bases_query.order_by(
                KnowledgeBase.updated_at.asc(),
            )

    knowledge_bases = knowledge_bases_query.all()

    # 查询知识库角色。
    roles: list[KnowledgeBaseRole] = KnowledgeBaseRole.query.filter(
        KnowledgeBaseRole.org_id == context_var.get_org_id_in_text_format(),
        KnowledgeBaseRole.deleted_at.is_(None),
    ).all()
    knowledge_base_id_to_roles_mapping: dict[int, list[KnowledgeBaseRole]] = {}
    no_limited_roles = [role for role in roles if role.has_limited_knowledge_base is False]
    for role in roles:
        if role.has_limited_knowledge_base:
            knowledge_base_roles = knowledge_base_id_to_roles_mapping.get(role.id) or []
            knowledge_base_roles.append(role)
            knowledge_base_id_to_roles_mapping[role.id] = knowledge_base_roles

    privilege_checker = PrivilegeChecker(context_var)
    knowledge_base_id_to_private_privileges_mapping = privilege_checker.get_all_private_privileges()

    data = []
    for knowledge_base in knowledge_bases:
        current_data: dict[str, Any] = knowledge_base.to_data()
        rs = knowledge_base_id_to_roles_mapping.get(knowledge_base.id) or []
        current_data.update(
            {
                "roles": [r.to_data() for r in (rs + no_limited_roles)],
            }
        )
        private_privileges = knowledge_base_id_to_private_privileges_mapping.get(knowledge_base.id) or set()
        current_data.update(
            {
                "privileges": list(private_privileges | privilege_checker.public_privileges),
            }
        )

        data.append(current_data)

    return jsonify(
        success=True,
        total=len(knowledge_bases),
        data=data,
    )


@api.post("/knowledge_bases")
@shop_required
@validate
def create_knowledge_base(body: CreateKnowledgeBaseBody):
    """
    新建知识库。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    with in_transaction() as session:
        knowledge_base = KnowledgeBase(
            icon=body.icon,
            name=body.name,
            intro=body.intro,
            org_id=context_var.get_org_id_in_text_format(),
            creator_id=context_var.get_feisuo_user_id(),
            updator_id=context_var.get_feisuo_user_id(),
        )
        session.add(knowledge_base)
        session.flush()

        # 记录操作。
        knowledge_base_history = KnowledgeBaseHistory(
            knowledge_base_id=knowledge_base.id,
            updator_id=context_var.get_feisuo_user_id(),
            action=HistoryAction.CREATE,
            data=KnowledgeBaseHistoryData(
                modify_content=KnowledgeBaseHistoryData.ModifyContent(
                    icon=body.icon,
                    name=body.name,
                    intro=body.intro,
                )
            ).dict(),
        )
        session.add(knowledge_base_history)

    return jsonify(
        success=True,
        id=knowledge_base.id,
    )


@api.get("/knowledge_bases/<int:knowledge_base_id>")
@shop_required
@validate
def get_knowledge_base_detail(knowledge_base_id: int):
    """
    查看单个知识库详情。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)
    if not checker.can_read(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权查看",
        )

    knowledge_base: KnowledgeBase | None = KnowledgeBase.query.filter(
        KnowledgeBase.id == knowledge_base_id,
        KnowledgeBase.org_id == context_var.get_org_id_in_text_format(),
        KnowledgeBase.deleted_at.is_(None),
    ).first()
    if knowledge_base is None:
        return jsonify(
            success=False,
            message="未找到该知识库",
        )

    # 查询知识库角色。
    roles: list[KnowledgeBaseRole] = KnowledgeBaseRole.query.filter(
        KnowledgeBaseRole.org_id == context_var.get_org_id_in_text_format(),
        KnowledgeBaseRole.deleted_at.is_(None),
    ).all()
    private_roles = [role for role in roles if role.has_limited_knowledge_base is True]
    public_roles = [role for role in roles if role.has_limited_knowledge_base is False]

    privilege_checker = PrivilegeChecker(context_var)
    private_privileges = privilege_checker.get_private_privileges(knowledge_base_id)

    data = knowledge_base.to_data()

    data.update(
        {
            "roles": [r.to_data() for r in (private_roles + public_roles)],
            "privileges": list(private_privileges | privilege_checker.public_privileges),
        }
    )

    return jsonify(
        success=True,
        data=data,
    )


@api.put("/knowledge_bases/<int:knowledge_base_id>")
@shop_required
@validate
def modify_knowledge_base(knowledge_base_id: int, body: ModifyKnowledgeBaseContentBody):
    """
    修改知识库内容。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)
    if not checker.can_edit(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权修改",
        )

    if all(
        [
            body.icon is None,
            body.name is None,
            body.intro is None,
        ]
    ):
        return jsonify(
            success=False,
            message="没有填写任何有效信息",
        )

    with in_transaction() as session:
        knowledge_base: KnowledgeBase | None = (
            session.query(KnowledgeBase)
            .filter(
                KnowledgeBase.id == knowledge_base_id,
                KnowledgeBase.org_id == context_var.get_org_id_in_text_format(),
                KnowledgeBase.deleted_at.is_(None),
            )
            .first()
        )
        if knowledge_base is None:
            return jsonify(
                success=False,
                message="未找到该知识库",
            )

        if body.icon is not None:
            knowledge_base.icon = body.icon
        if body.name is not None:
            knowledge_base.name = body.name
        if body.intro is not None:
            knowledge_base.intro = body.intro

        knowledge_base.updator_id = context_var.get_feisuo_user_id()

        # 记录操作。
        knowledge_base_history = KnowledgeBaseHistory(
            knowledge_base_id=knowledge_base.id,
            updator_id=context_var.get_feisuo_user_id(),
            action=HistoryAction.MODIFY_CONTENT,
            data=KnowledgeBaseHistoryData(
                modify_content=KnowledgeBaseHistoryData.ModifyContent(
                    icon=body.icon,
                    name=body.name,
                    intro=body.intro,
                )
            ).dict(),
        )
        session.add(knowledge_base_history)

    return jsonify(
        success=True,
    )


@api.delete("/knowledge_bases/<int:knowledge_base_id>")
@shop_required
@validate
def delete_knowledge_base(knowledge_base_id: int, body: DeleteKnowledgeBaseBody):
    """
    删除知识库。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)
    if not checker.can_delete(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权删除",
        )

    with in_transaction() as session:
        knowledge_base: KnowledgeBase | None = (
            session.query(KnowledgeBase)
            .filter(
                KnowledgeBase.id == knowledge_base_id,
                KnowledgeBase.org_id == context_var.get_org_id_in_text_format(),
                KnowledgeBase.deleted_at.is_(None),
            )
            .first()
        )
        if knowledge_base is None:
            logger.warning("未找到该知识库: {}".format(knowledge_base_id))
            return jsonify(
                success=True,
            )

        # 删除时间
        knowledge_base.deleted_at = int(time.time())

        # 记录操作。
        knowledge_base_history = KnowledgeBaseHistory(
            knowledge_base_id=knowledge_base.id,
            updator_id=context_var.get_feisuo_user_id(),
            action=HistoryAction.DELETE,
            data=KnowledgeBaseHistoryData(
                reason=body.reason,
            ).dict(),
        )
        session.add(knowledge_base_history)

    return jsonify(
        success=True,
    )


"""
项目的相关操作。
"""


@api.get("/projects")
@shop_required
@validate
def query_latest_projects(query: QueryLatestProjectArgument):
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    if query.project_type == ProjectType.DOCUMENT:
        project_types = [ProjectType.DOCUMENT]
    elif query.project_type == ProjectType.CATEGORY:
        project_types = [ProjectType.CATEGORY]
    else:
        project_types = [ProjectType.DOCUMENT, ProjectType.CATEGORY]

    subquery = (
        db.session.query(
            Project.id.label("id"),
            func.rank()
            .over(
                order_by=Project.updated_at.desc(),
                partition_by=Project.knowledge_base_id,
            )
            .label("rnk"),
        )
        .filter(
            Project.org_id == context_var.get_org_id_in_text_format(),
            Project.knowledge_base_id.in_(query.knowledge_base_ids),
            Project.type.in_(project_types),
            Project.deleted_at.is_(None),
        )
        .subquery()
    )

    projects: list[Project] = (
        db.session.query(Project)
        .join(
            subquery,
            subquery.c.id == Project.id,
        )
        .filter(subquery.c.rnk <= 3)
        .all()
    )

    knowledge_base_id_to_projects_mapping: dict[int, list[dict[str, Any]]] = {}
    for project in projects:
        projects_for_knowledge_base_id = knowledge_base_id_to_projects_mapping.get(project.knowledge_base_id) or []
        projects_for_knowledge_base_id.append(project.to_data())
        knowledge_base_id_to_projects_mapping[project.knowledge_base_id] = projects_for_knowledge_base_id

    return jsonify(
        success=True,
        data=knowledge_base_id_to_projects_mapping,
    )


@api.get("/knowledge_bases/<int:knowledge_base_id>/projects")
@shop_required
@validate
def query_projects(knowledge_base_id: int, query: QueryProjectArgument):
    """
    查询某一知识库下的项目。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)
    if not checker.can_read(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权查看",
        )

    project_query: FlaskQuery = Project.query.filter(
        Project.knowledge_base_id == knowledge_base_id,
        Project.org_id == context_var.get_org_id_in_text_format(),
        Project.deleted_at.is_(None),
    )

    if query.parent_id is not None:
        project_query = project_query.filter(
            Project.parent_id == query.parent_id,
        )
    if query.project_type is not None:
        project_query = project_query.filter(
            Project.type == query.project_type,
        )

    if query.page_no is not None and query.page_no > 0 and query.page_size is not None and query.page_size > 0:
        # 分页。
        project_pagination: Pagination = project_query.paginate(
            page=query.page_no,
            per_page=query.page_size,
        )
        total = project_pagination.total
        items = project_pagination.items
    else:
        total = project_query.count()
        items = project_query.all()

    data = [item.to_data() for item in items]

    return jsonify(success=True, total=total, data=data)


@api.put("/knowledge_bases/<int:knowledge_base_id>/projects/<int:current_id>/locations")
@shop_required
@validate
def move_project(knowledge_base_id: int, current_id: int, body: MoveProjectBody):
    """
    移动项目。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)
    if not checker.can_edit(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权修改",
        )

    with in_transaction() as session:
        moved_project, err = move_project_location(
            current_project_id=current_id,
            target_project_id=body.target_id,
            move_action=body.action,
            org_id=context_var.get_org_id_in_text_format(),
        )
        if err is not None:
            return jsonify(success=False, message=err)

        moved_project = cast(Project, moved_project)
        moved_project.updator_id = context_var.get_feisuo_user_id()

        project_history = ProjectHistory(
            project_id=current_id,
            updator_id=context_var.get_feisuo_user_id(),
            action=HistoryAction.MOVE,
            data=ProjectHistoryData(
                location=ProjectHistoryData.Location(
                    target_id=body.target_id,
                    action=body.action,
                )
            ).dict(),
        )
        session.add(project_history)

    return jsonify(
        success=True,
    )


@api.put("/knowledge_bases/<int:knowledge_base_id>/projects/<int:project_id>")
@shop_required
@validate
def update_project_name(knowledge_base_id: int, project_id: int, body: RenameProjectBody):
    """
    对项目进行改名操作。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)
    if not checker.can_edit(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权修改",
        )

    with in_transaction() as session:
        project: Project | None = (
            session.query(Project)
            .filter(
                Project.id == project_id,
                Project.knowledge_base_id == knowledge_base_id,
                Project.org_id == context_var.get_org_id_in_text_format(),
                Project.deleted_at.is_(None),
            )
            .first()
        )
        if project is None:
            return jsonify(
                success=False,
                message="未找到该项目",
            )
        original_project_name = project.name
        project.name = body.name
        project.updator_id = context_var.get_feisuo_user_id()

        # 记录操作。
        project_history = ProjectHistory(
            project_id=project.id,
            updator_id=context_var.get_feisuo_user_id(),
            action=HistoryAction.RENAME,
            data=ProjectHistoryData(
                rename_content=ProjectHistoryData.RenameContent(
                    original_name=original_project_name,
                    modified_name=body.name,
                )
            ).dict(),
        )
        session.add(project_history)

    return jsonify(
        success=True,
    )


@api.delete("/knowledge_bases/<int:knowledge_base_id>/projects/<int:project_id>")
@shop_required
@validate
def delete_project(knowledge_base_id: int, project_id: int, body: DeleteProjectBody):
    """
    删除项目。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)
    if not checker.can_delete(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权删除",
        )

    project: Project | None = Project.query.filter(
        Project.id == project_id,
        Project.knowledge_base_id == knowledge_base_id,
        Project.org_id == context_var.get_org_id_in_text_format(),
        Project.deleted_at.is_(None),
    ).first()
    if project is None:
        return jsonify(
            success=False,
            message="未找到该项目",
        )

    prev_project: Project | None = None
    next_project: Project | None = None

    if project.prev_id != 0 or project.next_id != 0:
        prev_project_and_next_project: list[Project] = Project.query.filter(
            Project.id.in_([project.prev_id, project.next_id]),
            Project.org_id == context_var.get_org_id_in_text_format(),
            Project.deleted_at.is_(None),
        ).all()
        project_id_to_project_mapping = {pr.id: pr for pr in prev_project_and_next_project}
        if project.prev_id != 0 and project.prev_id not in project_id_to_project_mapping:
            return jsonify(
                success=False,
                message="未能找到前序项目",
            )
        if project.next_id != 0 and project.next_id not in project_id_to_project_mapping:
            return jsonify(
                success=False,
                message="未能找到后序项目",
            )
        prev_project = project_id_to_project_mapping.get(project.prev_id)
        next_project = project_id_to_project_mapping.get(project.next_id)

    project.deleted_at = int(time.time())
    project.prev_id = 0
    project.next_id = 0
    if prev_project is not None:
        prev_project.next_id = project.next_id
    if next_project is not None:
        next_project.prev_id = project.prev_id

    project_history = ProjectHistory(
        project_id=project.id,
        updator_id=context_var.get_feisuo_user_id(),
        action=HistoryAction.DELETE,
        data=ProjectHistoryData(
            reason=body.reason,
        ).dict(),
    )
    db.session.add(project_history)
    db.session.commit()

    return jsonify(
        success=True,
    )


"""
分类的相关操作接口。
"""


@api.post("/knowledge_bases/<int:knowledge_base_id>/categories")
@shop_required
@validate
def create_category(knowledge_base_id: int, body: CreateCategoryBody):
    """
    新建分类。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)
    if not checker.can_edit(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权新建",
        )

    with in_transaction() as session:
        current_first_project: Project | None = (
            session.query(Project)
            .filter(
                Project.org_id == context_var.get_org_id_in_text_format(),
                Project.knowledge_base_id == knowledge_base_id,
                Project.parent_id == body.parent_id,
                Project.deleted_at.is_(None),
                Project.prev_id == 0,
            )
            .first()
        )
        if current_first_project is not None:
            next_id = current_first_project.id
        else:
            next_id = 0

        # 新建分类。
        project = Project(
            name=body.name,
            type=ProjectType.CATEGORY,
            org_id=context_var.get_org_id_in_text_format(),
            knowledge_base_id=knowledge_base_id,
            parent_id=body.parent_id,
            prev_id=0,
            next_id=next_id,
            creator_id=context_var.get_feisuo_user_id(),
            updator_id=context_var.get_feisuo_user_id(),
        )
        session.add(project)
        session.flush()
        if current_first_project is not None:
            current_first_project.prev_id = project.id

        project_history = ProjectHistory(
            project_id=project.id,
            updator_id=context_var.get_feisuo_user_id(),
            action=HistoryAction.CREATE,
            data=ProjectHistoryData(
                modify_content=ProjectHistoryData.ModifyContent(
                    name=body.name,
                    knowledge_base_id=knowledge_base_id,
                    parent_id=body.parent_id,
                )
            ).dict(),
        )
        session.add(project_history)

    return jsonify(
        success=True,
        id=project.id,
    )


"""
编辑锁的相关接口。
"""


@api.get("/locks/<int:project_id>")
@shop_required
@validate
def create_lock(project_id: int):
    """
    获取修改知识的锁。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    document: Document | None = (
        Document.query.options(
            load_only(
                Document.id,
                Document.project_id,
                Document.updated_at,
            )
        )
        .join(Project, Document.project_id == Project.id)
        .filter(
            Project.id == project_id,
            Project.org_id == context_var.get_org_id_in_text_format(),
            Project.deleted_at.is_(None),
            Project.type == ProjectType.DOCUMENT,
        )
        .first()
    )
    if document is None:
        return jsonify(
            success=False,
            message="未找到该文档",
        )

    lock = DocumentEditLock(
        user_id=context_var.get_feisuo_user_id(),
        document=document,
    )
    lock_info, err = lock.acquire()
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    return jsonify(
        success=True,
        lock_info=lock_info,
    )


@api.put("/locks/<int:project_id>")
@shop_required
@validate
def refresh_lock(project_id: int):
    """
    更新锁信息。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    document: Document | None = (
        Document.query.options(
            load_only(
                Document.id,
                Document.project_id,
                Document.updated_at,
            )
        )
        .join(Project, Document.project_id == Project.id)
        .filter(
            Project.id == project_id,
            Project.org_id == context_var.get_org_id_in_text_format(),
            Project.deleted_at.is_(None),
            Project.type == ProjectType.DOCUMENT,
        )
        .first()
    )
    if document is None:
        return jsonify(
            success=False,
            message="未找到该文档",
        )

    lock = DocumentEditLock(
        user_id=context_var.get_feisuo_user_id(),
        document=document,
    )
    lock_info, err = lock.refresh()
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    return jsonify(
        success=True,
        lock_info=lock_info,
    )


@api.delete("/locks/<int:project_id>")
@shop_required
@validate
def release_lock(project_id: int):
    """
    释放锁。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    document: Document | None = (
        Document.query.options(
            load_only(
                Document.id,
                Document.project_id,
                Document.updated_at,
            )
        )
        .join(Project, Document.project_id == Project.id)
        .filter(
            Project.id == project_id,
            Project.org_id == context_var.get_org_id_in_text_format(),
            Project.deleted_at.is_(None),
            Project.type == ProjectType.DOCUMENT,
        )
        .first()
    )
    if document is None:
        return jsonify(
            success=False,
            message="未找到该文档",
        )

    lock = DocumentEditLock(
        user_id=context_var.get_feisuo_user_id(),
        document=document,
    )
    err = lock.release()
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    return jsonify(
        success=True,
    )


"""
知识的相关操作接口。
"""


@api.post("/knowledge_bases/<int:knowledge_base_id>/documents")
@shop_required
@validate
def create_document(knowledge_base_id: int, body: CreateDocumentBody):
    """
    新建知识。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)
    if not checker.can_edit(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权新建",
        )

    with in_transaction() as session:
        current_first_project: Project | None = (
            session.query(Project)
            .filter(
                Project.org_id == context_var.get_org_id_in_text_format(),
                Project.knowledge_base_id == knowledge_base_id,
                Project.parent_id == body.parent_id,
                Project.deleted_at.is_(None),
                Project.prev_id == 0,
            )
            .first()
        )
        if current_first_project is not None:
            next_id = current_first_project.id
        else:
            next_id = 0

        # 新建文档。
        project = Project(
            name=body.name,
            type=ProjectType.DOCUMENT,
            org_id=context_var.get_org_id_in_text_format(),
            knowledge_base_id=knowledge_base_id,
            parent_id=body.parent_id,
            prev_id=0,
            next_id=next_id,
            creator_id=context_var.get_feisuo_user_id(),
            updator_id=context_var.get_feisuo_user_id(),
        )
        session.add(project)
        session.flush()
        if current_first_project is not None:
            current_first_project.prev_id = project.id

        # 新建手稿。
        document = Document(
            intro="",
            content={},
            preview="",
            project_id=project.id,
            is_dirty=True,
            updator_id=context_var.get_feisuo_user_id(),
        )
        session.add(document)

        # 记录操作历史。
        project_history = ProjectHistory(
            project_id=project.id,
            updator_id=context_var.get_feisuo_user_id(),
            action=HistoryAction.CREATE,
            data=ProjectHistoryData(
                modify_content=ProjectHistoryData.ModifyContent(
                    name=body.name,
                    knowledge_base_id=knowledge_base_id,
                    parent_id=body.parent_id,
                )
            ).dict(),
        )
        session.add(project_history)

    return jsonify(
        success=True,
        document_id=project.id,
    )


@api.get("/knowledge_bases/<int:knowledge_base_id>/manuscripts/<int:project_id>")
@api.get("/knowledge_bases/<int:knowledge_base_id>/documents/<int:project_id>")
@shop_required
@validate
def get_document_detail(knowledge_base_id: int, project_id: int):
    """
    查看知识详情。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    if not checker.can_read(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权查看",
        )

    project: Project | None = Project.query.filter(
        Project.id == project_id,
        Project.knowledge_base_id == knowledge_base_id,
        Project.org_id == context_var.get_org_id_in_text_format(),
        Project.deleted_at.is_(None),
        Project.type == ProjectType.DOCUMENT,
    ).first()
    if project is None:
        return jsonify(
            success=False,
            message="未找到该文档",
        )

    if "manuscripts" in request.path:
        document: Document | None = (
            Document.query.filter(
                Document.project_id == project.id,
            )
            .order_by(Document.id.desc())
            .first()
        )
        if document is None:
            return jsonify(
                success=False,
                message="未找到文档内容",
            )
    else:
        document = (
            Document.query.filter(
                Document.project_id == project.id,
                Document.is_released.is_(True),
            )
            .order_by(Document.id.desc())
            .first()
        )
        if document is None:
            return jsonify(
                success=False,
                message="未找到已发布的文档内容",
            )

    data = project.to_data() | document.to_data()

    return jsonify(
        success=True,
        data=data,
    )


@api.post("/knowledge_bases/<int:knowledge_base_id>/manuscripts/<int:project_id>")
@api.post("/knowledge_bases/<int:knowledge_base_id>/documents/<int:project_id>")
@shop_required
@validate
def update_document(knowledge_base_id: int, project_id: int, body: ModifyDocumentBody):
    """
    手动保存或发布知识。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)
    if not checker.can_edit(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权修改",
        )

    with in_transaction() as session:
        project: Project | None = (
            session.query(Project)
            .filter(
                Project.id == project_id,
                Project.org_id == context_var.get_org_id_in_text_format(),
                Project.knowledge_base_id == knowledge_base_id,
                Project.deleted_at.is_(None),
                Project.type == ProjectType.DOCUMENT,
            )
            .first()
        )
        if project is None:
            return jsonify(
                success=False,
                message="未找到该文档",
            )

        if project.status in [ProjectStatus.INACTIVE, ProjectStatus.TO_BE_APPROVED]:
            return jsonify(
                success=False,
                message="当前知识状态不支持进行该操作",
            )

        document: Document | None = (
            session.query(Document)
            .filter(Document.project_id == project.id, Document.is_dirty.is_(True))
            .order_by(Document.id.desc())
            .first()
        )
        if document is None:
            document = Document(
                project_id=project.id,
                intro=body.intro,
                content=body.content,
                preview=body.preview,
                is_dirty=True,
                updator_id=context_var.get_feisuo_user_id(),
            )
            session.add(document)
            session.flush()
        else:
            if body.intro is not None:
                document.intro = body.intro
            if body.content is not None:
                document.content = body.content
            if body.preview is not None:
                document.preview = body.preview

        if body.name is not None:
            project.name = body.name
        project.updator_id = context_var.get_feisuo_user_id()
        document.updator_id = context_var.get_feisuo_user_id()
        # 每次手动保存，都是视为一个小版本。
        document.is_dirty = False

        if body.action == DocumentAction.SAVE:
            project.status = ProjectStatus.EDITING
        # 由于暂时不上线审批功能，所以就直接发布。
        elif body.action == DocumentAction.RELEASE:
            project.status = ProjectStatus.RELEASED
            document.is_released = True

        project_history = ProjectHistory(
            project_id=project.id,
            updator_id=context_var.get_feisuo_user_id(),
            action=HistoryAction.MODIFY_CONTENT,
            data=ProjectHistoryData(
                modify_content=ProjectHistoryData.ModifyContent(
                    intro=body.intro,
                    content=body.content,
                    preview=body.preview,
                )
            ).dict(),
        )
        session.add(project_history)

    return jsonify(success=True)


@api.put("/knowledge_bases/<int:knowledge_base_id>/manuscripts/<int:project_id>")
@api.put("/knowledge_bases/<int:knowledge_base_id>/documents/<int:project_id>")
@shop_required
@validate
def auto_save_document(knowledge_base_id: int, project_id: int, body: AutoSaveDocumentBody):
    """
    间隔时间，自动保存。
    如果有压力，可以考虑上缓存。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)
    if not checker.can_edit(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权修改",
        )

    with in_transaction() as session:
        project: Project | None = (
            session.query(Project)
            .filter(
                Project.id == project_id,
                Project.org_id == context_var.get_org_id_in_text_format(),
                Project.knowledge_base_id == knowledge_base_id,
                Project.deleted_at.is_(None),
                Project.type == ProjectType.DOCUMENT,
            )
            .first()
        )
        if project is None:
            return jsonify(
                success=False,
                message="未找到该文档",
            )

        if project.status in [ProjectStatus.INACTIVE, ProjectStatus.TO_BE_APPROVED]:
            return jsonify(
                success=False,
                message="当前知识状态不支持进行该操作",
            )

        document_not_found: bool = True

        document: Document | None = (
            session.query(Document)
            .filter(Document.project_id == project.id, Document.is_dirty.is_(True))
            .order_by(Document.id.desc())
            .first()
        )
        if document is None:
            logger.warning("未找到草稿")
            document = Document(
                project_id=project.id,
                updator_id=context_var.get_feisuo_user_id(),
                intro=body.intro,
                content=body.content,
                preview=body.preview,
                is_dirty=True,
            )
            session.add(document)
        else:
            document_not_found = False
            original_updator_id = document.updator_id
            original_intro = document.intro
            original_content = document.content
            original_preview = document.preview
            original_updated_at = document.updated_at

            if body.intro is not None:
                document.intro = body.intro
            if body.content is not None:
                document.content = body.content
            if body.preview is not None:
                document.preview = body.preview

        project.updator_id = context_var.get_feisuo_user_id()
        project.status = ProjectStatus.EDITING
        document.updator_id = context_var.get_feisuo_user_id()

        # todo: 确认以下操作日志的记录逻辑是否正确。
        # 如果当前账号并非上一次修改者。
        if context_var.get_feisuo_user_id() != document.updator_id and not document_not_found:
            # 记录前一次临时关闭的记录。
            before_project_history = ProjectHistory(
                created_at=original_updated_at,
                project_id=project.id,
                updator_id=original_updator_id,
                action=HistoryAction.MODIFY_CONTENT,
                data=ProjectHistoryData(
                    modify_content=ProjectHistoryData.ModifyContent(
                        intro=original_intro,
                        content=original_content,
                        preview=original_preview,
                    )
                ).dict(),
            )
            session.add(before_project_history)

            # 本次修改。
            new_project_history = ProjectHistory(
                project_id=project.id,
                updator_id=context_var.get_feisuo_user_id(),
                action=HistoryAction.MODIFY_CONTENT,
                data=ProjectHistoryData(
                    modify_content=ProjectHistoryData.ModifyContent(
                        intro=body.intro,
                        content=body.content,
                        preview=body.preview,
                    )
                ).dict(),
            )
            session.add(new_project_history)

    return jsonify(success=True)


@api.patch("/knowledge_bases/<int:knowledge_base_id>/manuscripts/<int:project_id>")
@api.patch("/knowledge_bases/<int:knowledge_base_id>/documents/<int:project_id>")
@shop_required
@validate
def approve_document(knowledge_base_id: int, project_id: int, body: ApproveDocumentBody):
    """
    对知识进行审批，决定是审批通过还是驳回。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)
    if not checker.can_approve(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权审批",
        )

    with in_transaction() as session:
        project: Project | None = (
            session.query(Project)
            .filter(
                Project.id == project_id,
                Project.org_id == context_var.get_org_id_in_text_format(),
                Project.knowledge_base_id == knowledge_base_id,
                Project.deleted_at.is_(None),
                Project.type == ProjectType.DOCUMENT,
            )
            .first()
        )
        if project is None:
            return jsonify(
                success=False,
                message="未找到该文档",
            )

        if project.status != ProjectStatus.TO_BE_APPROVED:
            return jsonify(
                success=False,
                message="该文档无需审批",
            )

        document: Document | None = (
            session.query(Document)
            .filter(
                Document.project_id == project.id,
            )
            .order_by(Document.id.desc())
            .first()
        )
        if not document:
            return jsonify(
                success=False,
                message="未找到需要审核的知识内容",
            )

        # 审批通过
        if body.action == DocumentAction.PASSTHROUGH:
            project.status = ProjectStatus.RELEASED
            history_action = HistoryAction.PASSTHROUGH
            document.is_released = True
            document.is_dirty = False
        # 驳回
        elif body.action == DocumentAction.OVERRULE:
            project.status = ProjectStatus.REVERTED
            history_action = HistoryAction.OVERRULE

        project_history = ProjectHistory(
            project_id=project.id,
            updator_id=context_var.get_feisuo_user_id(),
            action=history_action,
            data=ProjectHistoryData(
                reason=body.reason,
            ).dict(),
        )
        session.add(project_history)

    return jsonify(
        success=True,
    )


@api.get("/knowledge_bases/<int:knowledge_base_id>/suggestions/manuscripts")
@api.get("/knowledge_bases/<int:knowledge_base_id>/suggestions/documents")
@shop_required
@validate
def search_documents_by_knowledge_base(knowledge_base_id: int, query: SearchDocumentsArgument):
    """
    对文档进行关键词查询。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    if not checker.can_read(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权查看",
        )

    if not check_keyword_is_valid(query.keyword):
        return jsonify(
            success=False,
            message="没有传递有效关键词",
        )

    # 基于空格切割关键词
    keywords = [k for k in query.keyword.split(" ") if len(k) != 0]

    if "manuscripts" in request.path:
        query_dirty_document = True
    else:
        query_dirty_document = False

    total, items = search_documents_by_keywords(
        org_id=context_var.get_org_id_in_text_format(),
        keywords=keywords,
        query_dirty_document=query_dirty_document,
        knowledge_base_ids=[knowledge_base_id],
        page_no=query.page_no,
        page_size=query.page_size,
    )

    data = []

    # 将查询结果中的命中内容进行标记。
    for item in items:
        project_id, knowledge_base_id, parent_id, project_name, document_intro, document_preview = item
        got_text, location = location_keywords_in_text(
            keywords=keywords,
            project_name=project_name,
            document_intro=document_intro,
            document_preview=document_preview,
        )
        current_data = {
            "project_id": project_id,
            "knowledge_base_id": knowledge_base_id,
            "parent_id": parent_id,
            "project_name": project_name,
            "location": location,
            "found_text": got_text,
        }
        if "manuscripts" in request.path:
            current_data.update(
                {
                    "intro": document_intro,
                }
            )
        data.append(current_data)

    return jsonify(success=True, total=total, data=data)


@api.get("/suggestions/manuscripts")
@api.get("/suggestions/documents")
@shop_required
@validate
def search_documents(query: SearchDocumentsArgument):
    """
    对文档进行关键词查询。（不限定知识库）
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    if not check_keyword_is_valid(query.keyword):
        return jsonify(
            success=False,
            message="没有传递有效关键词",
        )

    knowledge_base_ids = None

    if KnowledgeBaseRolePrivilege.KNOWLEDGE_READ not in checker.public_privileges:
        knowledge_base_id_to_private_privileges_mapping = checker.get_all_private_privileges()
        role_limited_knowledge_base_ids_set = {
            knowledge_base_id
            for knowledge_base_id, private_privileges in knowledge_base_id_to_private_privileges_mapping.items()
            if KnowledgeBaseRolePrivilege.KNOWLEDGE_READ in private_privileges
        }
        knowledge_base_ids = list(role_limited_knowledge_base_ids_set)

    # 基于空格切割关键词
    keywords = [k for k in query.keyword.split(" ") if len(k) != 0]

    if "manuscripts" in request.path:
        query_dirty_document = True
    else:
        query_dirty_document = False

    total, items = search_documents_by_keywords(
        org_id=context_var.get_org_id_in_text_format(),
        keywords=keywords,
        query_dirty_document=query_dirty_document,
        knowledge_base_ids=knowledge_base_ids,
        page_no=query.page_no,
        page_size=query.page_size,
    )

    data = []

    # 将查询结果中的命中内容进行标记。
    for item in items:
        project_id, knowledge_base_id, parent_id, project_name, document_intro, document_preview = item
        got_text, location = location_keywords_in_text(
            keywords=keywords,
            project_name=project_name,
            document_intro=document_intro,
            document_preview=document_preview,
        )
        current_data = {
            "project_id": project_id,
            "knowledge_base_id": knowledge_base_id,
            "parent_id": parent_id,
            "project_name": project_name,
            "location": location,
            "found_text": got_text,
        }
        if "manuscripts" in request.path:
            current_data.update(
                {
                    "intro": document_intro,
                }
            )
        data.append(current_data)

    return jsonify(success=True, total=total, data=data)


@api.get("/manuscripts")
@api.get("/documents")
@shop_required
@validate
def find_documents(query: FindDocumentsArgument):
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    if KnowledgeBaseRolePrivilege.KNOWLEDGE_READ in checker.public_privileges:
        knowledge_base_ids = None
    else:
        knowledge_base_id_to_private_privileges_mapping = checker.get_all_private_privileges()
        role_limited_knowledge_base_ids_set = {
            knowledge_base_id
            for knowledge_base_id, private_privileges in knowledge_base_id_to_private_privileges_mapping.items()
            if KnowledgeBaseRolePrivilege.KNOWLEDGE_READ in private_privileges
        }
        knowledge_base_ids = list(role_limited_knowledge_base_ids_set)

    if "manuscripts" in request.path:
        query_dirty_document = True
    else:
        query_dirty_document = False

    favorite_project_ids_query = db.session.query(MyFavorite.project_id).filter(
        MyFavorite.user_id == context_var.get_feisuo_user_id(),
    )
    favorite_project_ids = [project_id for (project_id,) in favorite_project_ids_query]

    with in_transaction() as session:
        all_latest_document_ids_query = generate_all_latest_document_ids_query(
            session, context_var.get_org_id_in_text_format(), query_dirty_document, knowledge_base_ids
        )

        all_latest_document_ids_subquery: Subquery = all_latest_document_ids_query.group_by(
            Document.project_id,
        ).subquery()
        # 生成查询语句。
        project_and_documents_query: Query = (
            session.query(
                Project.id,
                Project.name,
                Project.type,
                Project.parent_id,
                Project.prev_id,
                Project.next_id,
                Project.creator_id,
                Project.updator_id,
                Project.created_at,
                Project.updated_at,
                Project.status,
                Project.knowledge_base_id,
                Document.intro,
            )
            .join(
                Document,
                Project.id == Document.project_id,
            )
            .join(
                all_latest_document_ids_subquery,
                all_latest_document_ids_subquery.c.id == Document.id,
            )
            .filter(
                Project.deleted_at.is_(None),
            )
        )
        if query.keyword:
            # 基于空格切割关键词
            keywords = [k for k in query.keyword.split(" ") if len(k) != 0]

            name_match_expr = and_(*[Project.name.like(f"%{k}%") for k in keywords]).self_group()
            intro_match_expr = and_(*[Document.intro.like(f"%{k}%") for k in keywords]).self_group()
            preview_match_expr = and_(*[Document.preview.like(f"%{k}%") for k in keywords]).self_group()

            project_and_documents_query = project_and_documents_query.filter(
                or_(
                    name_match_expr,
                    intro_match_expr,
                    preview_match_expr,
                ).self_group()
            )
        if query.knowledge_base_ids:
            project_and_documents_query = project_and_documents_query.filter(
                Project.knowledge_base_id.in_(query.knowledge_base_ids),
            )
        if query.tab == Tab.CREATED:
            project_and_documents_query = project_and_documents_query.filter(
                Project.updator_id == context_var.get_feisuo_user_id()
            )
        if query.tab == Tab.FAVORITE:
            project_and_documents_query = project_and_documents_query.filter(Project.id.in_(favorite_project_ids))
        if query.tab == Tab.WAITING_APPROVE:
            project_and_documents_query = project_and_documents_query.filter(
                Project.status == ProjectStatus.TO_BE_APPROVED
            )
            if KnowledgeBaseRolePrivilege.KNOWLEDGE_APPROVE not in checker.public_privileges:
                knowledge_base_id_to_private_privileges_mapping = checker.get_all_private_privileges()
                knowledge_base_ids = []
                for knowledge_base_id, private_privileges in knowledge_base_id_to_private_privileges_mapping.items():
                    if KnowledgeBaseRolePrivilege.KNOWLEDGE_APPROVE in private_privileges:
                        knowledge_base_ids.append(knowledge_base_id)
                project_and_documents_query = project_and_documents_query.filter(
                    Project.knowledge_base_id.in_(knowledge_base_ids),
                )

        if query.page_no is not None and query.page_no > 0 and query.page_size is not None and query.page_size > 0:
            total: int = project_and_documents_query.count()
            items: list = (
                project_and_documents_query.offset((query.page_no - 1) * query.page_size).limit(query.page_size).all()
            )
        else:
            total = project_and_documents_query.count()
            items = project_and_documents_query.all()

        data = []

        for item in items:
            current_data = {
                "id": item[0],
                "name": item[1],
                "type": item[2],
                "parent_id": item[3],
                "prev_id": item[4],
                "next_id": item[5],
                "creator_id": item[6],
                "creator_name": complete_user_name(item[6]),
                "updator_id": item[7],
                "updator_name": complete_user_name(item[7]),
                "created_at": item[8],
                "updated_at": item[9],
                "status": item[10],
                "knowledge_base_id": item[11],
                "intro": item[12],
            }
            if query.tab != Tab.FAVORITE:
                if item.id in favorite_project_ids:
                    current_data.update(
                        {
                            "is_favorite": True,
                        }
                    )
                else:
                    current_data.update(
                        {
                            "is_favorite": False,
                        }
                    )
            else:
                current_data.update(
                    {
                        "is_favorite": True,
                    }
                )
            data.append(current_data)

        return jsonify(
            success=True,
            total=total,
            data=data,
        )


@api.get("/knowledge_bases/<int:knowledge_base_id>/manuscripts")
@api.get("/knowledge_bases/<int:knowledge_base_id>/documents")
@shop_required
@validate
def list_documents(knowledge_base_id: int, query: QueryDocumentsArgument):
    """
    罗列某知识库下的知识。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    if not checker.can_read(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权查看",
        )

    if "manuscripts" in request.path:
        query_dirty_document = True
    else:
        query_dirty_document = False

    with in_transaction() as session:
        all_latest_document_ids_query = generate_all_latest_document_ids_query(
            session, context_var.get_org_id_in_text_format(), query_dirty_document, [knowledge_base_id]
        )

        all_latest_document_ids_subquery: Subquery = all_latest_document_ids_query.group_by(
            Document.project_id,
        ).subquery()

        project_and_document_query: Query = (
            session.query(
                Project.id,
                Project.name,
                Project.parent_id,
                Project.prev_id,
                Project.next_id,
                Project.created_at,
                Project.status,
                Document.updated_at,
                Document.updator_id,
                Document.intro,
            )
            .join(
                Document,
                Document.project_id == Project.id,
            )
            .join(
                all_latest_document_ids_subquery,
                all_latest_document_ids_subquery.c.id == Document.id,
            )
            .filter(
                Project.knowledge_base_id == knowledge_base_id,
                Project.deleted_at.is_(None),
            )
        )
        if query.parent_id is not None:
            project_and_document_query = project_and_document_query.filter(
                Project.parent_id == query.parent_id,
            )

        total = project_and_document_query.count()

        if query.page_no is not None and query.page_no > 0 and query.page_size is not None and query.page_size > 0:
            project_and_document_query = project_and_document_query.offset(
                (query.page_no - 1) * query.page_size,
            ).limit(query.page_size)

        items: list[
            tuple[int, str, int, int, int, int, ProjectStatus, int, int, str]
        ] = project_and_document_query.all()

        data = []

        for item in items:
            (
                project_id,
                project_name,
                project_parent_id,
                project_prev_id,
                project_next_id,
                project_created_at,
                project_status,
                document_updated_at,
                document_updator_id,
                document_intro,
            ) = (item[0], item[1], item[2], item[3], item[4], item[5], item[6], item[7], item[8], item[9])
            data.append(
                {
                    "id": project_id,
                    "name": project_name,
                    "parent_id": project_parent_id,
                    "prev_id": project_prev_id,
                    "next_id": project_next_id,
                    "created_at": project_created_at,
                    "status": project_status,
                    "updated_at": document_updated_at,
                    "document_updator_id": document_updator_id,
                    "intro": document_intro,
                }
            )

        return jsonify(
            success=True,
            total=total,
            data=data,
        )


@api.get("/knowledge_bases/<int:knowledge_base_id>/documents/<int:project_id>/versions")
@shop_required
@validate
def list_document_versions(knowledge_base_id: int, project_id: int, query: DefaultArgument):
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    if not checker.can_read(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权查看",
        )

    project: Project | None = Project.query.filter(
        Project.id == project_id,
        Project.knowledge_base_id == knowledge_base_id,
        Project.org_id == context_var.get_org_id_in_text_format(),
        Project.deleted_at.is_(None),
        Project.type == ProjectType.DOCUMENT,
    ).first()
    if project is None:
        return jsonify(
            success=False,
            message="未找到该文档",
        )

    documents_query = (
        db.session.query(Document)
        .options(
            load_only(
                Document.id,
                Document.project_id,
                Document.updator_id,
                Document.updated_at,
                Document.is_dirty,
                Document.is_released,
                Document.version_no,
            )
        )
        .filter(
            Document.project_id == project.id,
        )
        .order_by(Document.id.desc())
    )

    if query.page_no is not None and query.page_no > 0 and query.page_size is not None and query.page_size > 0:
        total = documents_query.count()
        documents_query = documents_query.offset((query.page_no - 1) * query.page_size).limit(query.page_size)
        items: list[Document] = documents_query.all()
    else:
        total = documents_query.count()
        items = documents_query.all()

    return jsonify(
        success=True,
        total=total,
        data=[
            {
                "project_id": item.project_id,
                "version_id": item.id,
                "updator_id": item.updator_id,
                "updator_name": complete_user_name(item.updator_id),
                "updated_at": item.updated_at,
                "is_dirty": item.is_dirty,
                "is_released": item.is_released,
                "version_no": item.version_no,
            }
            for item in items
        ],
    )


@api.get("/knowledge_bases/<int:knowledge_base_id>/documents/<int:project_id>/versions/<int:version_id>")
@shop_required
@validate
def show_document_version_detail(knowledge_base_id: int, project_id: int, version_id: int):
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    if not checker.can_read(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权查看",
        )

    project: Project | None = Project.query.filter(
        Project.id == project_id,
        Project.knowledge_base_id == knowledge_base_id,
        Project.org_id == context_var.get_org_id_in_text_format(),
        Project.deleted_at.is_(None),
        Project.type == ProjectType.DOCUMENT,
    ).first()
    if project is None:
        return jsonify(
            success=False,
            message="未找到该文档",
        )

    document = Document.query.filter(
        Document.id == version_id,
    ).first()
    if document is None:
        return jsonify(
            success=False,
            message="未找到该版本",
        )

    data = project.to_data() | document.to_data()

    return jsonify(
        success=True,
        data=data,
    )


"""
我的收藏。
"""


@api.get("/favorites")
@shop_required
@validate
def get_favorites(query: DefaultArgument):
    """
    查看我的收藏。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    project_query: FlaskQuery = (
        Project.query.join(
            MyFavorite,
            MyFavorite.project_id == Project.id,
        )
        .filter(
            Project.org_id == context_var.get_org_id_in_text_format(),
            Project.deleted_at.is_(None),
            Project.type == ProjectType.DOCUMENT,
        )
        .filter(
            MyFavorite.user_id == context_var.get_feisuo_user_id(),
        )
    )

    # 分页。
    project_pagination: Pagination = project_query.paginate(
        page=query.page_no,
        per_page=query.page_size,
    )

    return jsonify(
        success=True, total=project_pagination.total, data=[item.to_data() for item in project_pagination.items]
    )


@api.post("/favorites")
@shop_required
@validate
def create_favorite(body: TagFavoriteBody):
    """
    标记收藏。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    project: Project | None = Project.query.filter(
        Project.id == body.project_id,
        Project.org_id == context_var.get_org_id_in_text_format(),
        Project.deleted_at.is_(None),
        Project.type == ProjectType.DOCUMENT,
    ).first()
    if project is None:
        return jsonify(
            success=False,
            message="未找到该文档",
        )

    if not checker.can_read(project.knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权查看",
        )

    with in_transaction() as session:
        my_favorite = MyFavorite(
            user_id=context_var.get_feisuo_user_id(),
            project_id=project.id,
        )
        session.add(my_favorite)

    return jsonify(
        success=True,
        favorite_id=my_favorite.id,
    )


@api.delete("/favorites/<int:project_id>")
@shop_required
@validate
def delete_favorite(project_id: int):
    """
    取消收藏。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    with in_transaction() as session:
        my_favorite: MyFavorite | None = (
            session.query(MyFavorite)
            .filter(
                MyFavorite.user_id == context_var.get_feisuo_user_id(),
                MyFavorite.project_id == project_id,
            )
            .first()
        )
        if my_favorite:
            session.delete(my_favorite)

    return jsonify(
        success=True,
    )


"""
知识库回收站。
"""


@api.get("/rubbishes")
@shop_required
@validate
def query_rubbishes(query: DefaultArgument):
    """
    查询回收站。（知识库）
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    if KnowledgeBaseRolePrivilege.TRASH_CHECK in checker.public_privileges:
        knowledge_base_ids: list[int] | None = None
    else:
        knowledge_base_ids = []
        knowledge_base_id_to_private_privileges_mapping = checker.get_all_private_privileges()
        if knowledge_base_id_to_private_privileges_mapping:
            for knowledge_base_id, private_privileges in knowledge_base_id_to_private_privileges_mapping.items():
                if KnowledgeBaseRolePrivilege.TRASH_CHECK in private_privileges:
                    knowledge_base_ids.append(knowledge_base_id)

    last_days = int(time.time()) - 14 * 24 * 3600

    knowledge_bases_query: FlaskQuery = KnowledgeBase.query.filter(
        KnowledgeBase.org_id == context_var.get_org_id_in_text_format(),
        KnowledgeBase.deleted_at > last_days,
    )
    if knowledge_base_ids is not None:
        knowledge_bases_query = knowledge_bases_query.filter(
            KnowledgeBase.id.in_(knowledge_base_ids),
        )

    knowledge_bases_query = knowledge_bases_query.order_by(KnowledgeBase.deleted_at.desc())

    if query.page_no is not None and query.page_no > 0 and query.page_size is not None and query.page_size > 0:
        knowledge_bases_pagination: Pagination = knowledge_bases_query.paginate(
            page=query.page_no,
            per_page=query.page_size,
        )
        total = knowledge_bases_pagination.total
        knowledge_bases = knowledge_bases_pagination.items
    else:
        total = knowledge_bases_query.count()
        knowledge_bases = knowledge_bases_query.all()

    got_knowledge_base_ids: list[int] = [kb.id for kb in knowledge_bases]
    actions = checker.get_actions(got_knowledge_base_ids)

    data = [
        TrashItem(
            id=kb.id,
            type=ItemType.KNOWLEDGE_BASE,
            name=kb.name,
            icon=kb.icon,
            deleted_at=kb.deleted_at,
            updator_id=kb.updator_id,
            updator_name=complete_user_name(kb.updator_id),
            actions=actions.get(kb.id),
        )
        for kb in knowledge_bases
    ]

    return jsonify(success=True, data=[i.dict() for i in data], total=total)


@api.put("/rubbishes/<int:knowledge_base_id>")
@shop_required
@validate
def recover_knowledge_base(knowledge_base_id: int):
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    if not checker.can_recover(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权恢复",
        )

    last_days = int(time.time()) - 14 * 24 * 3600

    # 恢复知识库。
    with in_transaction() as session:
        knowledge_base: KnowledgeBase | None = (
            session.query(KnowledgeBase)
            .filter(
                KnowledgeBase.id == knowledge_base_id,
                KnowledgeBase.org_id == context_var.get_org_id_in_text_format(),
                KnowledgeBase.deleted_at > last_days,
            )
            .first()
        )
        if knowledge_base is None:
            return jsonify(success=False, message="未找到该知识库")

        knowledge_base.deleted_at = None
        # 记录操作。
        knowledge_base_history = KnowledgeBaseHistory(
            knowledge_base_id=knowledge_base.id,
            updator_id=context_var.get_feisuo_user_id(),
            action=HistoryAction.RECOVER,
        )
        session.add(knowledge_base_history)
    return jsonify(success=True)


@api.delete("/rubbishes/<int:knowledge_base_id>")
@shop_required
@validate
def erase_knowledge_base(knowledge_base_id: int):
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    if not checker.can_erase(knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权删除",
        )

    last_days = int(time.time()) - 14 * 24 * 3600

    # 删除知识库。
    with in_transaction() as session:
        knowledge_base: KnowledgeBase | None = (
            session.query(KnowledgeBase)
            .filter(
                KnowledgeBase.id == knowledge_base_id,
                KnowledgeBase.org_id == context_var.get_org_id_in_text_format(),
                KnowledgeBase.deleted_at > last_days,
            )
            .first()
        )
        if knowledge_base is None:
            return jsonify(success=False, message="未找到该知识库")

        # 交给定时任务处理。
        knowledge_base.deleted_at = 0

    return jsonify(success=True)


"""
项目回收站。
"""


@api.get("/trashes")
@shop_required
@validate
def query_trashes(query: DefaultArgument):
    """
    查看回收站。（项目）
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    if KnowledgeBaseRolePrivilege.TRASH_CHECK in checker.public_privileges:
        knowledge_base_ids: list[int] | None = None
    else:
        knowledge_base_ids = []
        knowledge_base_id_to_private_privileges_mapping = checker.get_all_private_privileges()
        if knowledge_base_id_to_private_privileges_mapping:
            for knowledge_base_id, private_privileges in knowledge_base_id_to_private_privileges_mapping.items():
                if KnowledgeBaseRolePrivilege.TRASH_CHECK in private_privileges:
                    knowledge_base_ids.append(knowledge_base_id)

    last_days = int(time.time()) - 14 * 24 * 3600

    projects_query: FlaskQuery = Project.query.filter(
        Project.org_id == context_var.get_org_id_in_text_format(),
        Project.deleted_at > last_days,
    )
    if knowledge_base_ids is not None:
        projects_query = projects_query.filter(
            Project.knowledge_base_id.in_(knowledge_base_ids),
        )

    projects_query = projects_query.order_by(
        Project.deleted_at.desc(),
    )

    if query.page_no is not None and query.page_no > 0 and query.page_size is not None and query.page_size > 0:
        projects_pagination: Pagination = projects_query.paginate(
            page=query.page_no,
            per_page=query.page_size,
        )
        total = projects_pagination.total
        projects = projects_pagination.items
    else:
        total = projects_query.count()
        projects = projects_query.all()

    got_knowledge_base_ids: list[int] = [project.knowledge_base_id for project in projects]
    actions = checker.get_actions(got_knowledge_base_ids)

    data: list[TrashItem] = [
        TrashItem(
            id=pr.id,
            type=ItemType.PROJECT,
            name=pr.name,
            project_type=pr.type,
            deleted_at=pr.deleted_at,
            updator_id=pr.updator_id,
            updator_name=complete_user_name(pr.updator_id),
            knowledge_base_id=pr.knowledge_base_id,
            actions=actions.get(pr.knowledge_base_id),
        )
        for pr in projects
    ]

    return jsonify(success=True, data=[i.dict() for i in data], total=total)


@api.put("/trashes/<int:project_id>")
@shop_required
@validate
def recover_trash(project_id: int, body: RecoverTrashBody):
    """
    从回收站恢复项目。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    last_days = int(time.time()) - 14 * 24 * 3600

    project: Project | None = (
        db.session.query(Project)
        .filter(
            Project.id == project_id,
            Project.org_id == context_var.get_org_id_in_text_format(),
            Project.deleted_at > last_days,
        )
        .first()
    )
    if project is None:
        return jsonify(success=False, message="未找到需要恢复的项目")

    if not checker.can_recover(project.knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权恢复",
        )

    if body.move_action != MoveAction.ADD_CHILD and body.move_to_project_id is None:
        return jsonify(success=False, message="没有指定移动关联的项目")

    # 进行项目恢复，并记录操作日志。
    with in_transaction() as session:
        need_recovered_project, err = move_project_location(
            current_project_id=project_id,
            target_project_id=body.move_to_project_id,
            move_action=body.move_action,
            org_id=context_var.get_org_id_in_text_format(),
            query_deleted_project=True,
            target_knowledge_base_id=body.move_to_knowledge_base_id,
        )
        if err is not None:
            logger.error(f"project_id: {project_id}, err: {err}, body: {body.dict()}")
            return jsonify(success=False, message=err)

        # 恢复项目
        need_recovered_project = cast(Project, need_recovered_project)
        need_recovered_project.updator_id = context_var.get_feisuo_user_id()
        need_recovered_project.deleted_at = None
        project_history = ProjectHistory(
            project_id=need_recovered_project.id,
            updator_id=context_var.get_feisuo_user_id(),
            action=HistoryAction.RECOVER,
            data=ProjectHistoryData(
                recover_content=ProjectHistoryData.RecoverContent(
                    move_to_project_id=body.move_to_project_id,
                    move_action=body.move_action,
                )
            ).dict(),
        )
        session.add(project_history)

    return jsonify(success=True)


@api.delete("/trashes/<int:project_id>")
@shop_required
@validate
def delete_trash(project_id: int):
    """
    从回收站删除项目。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)
    checker = PrivilegeChecker(context_var)

    last_days = int(time.time()) - 14 * 24 * 3600

    project: Project | None = (
        db.session.query(Project)
        .filter(
            Project.id == project_id,
            Project.org_id == context_var.get_org_id_in_text_format(),
            Project.deleted_at > last_days,
        )
        .first()
    )
    if project is None:
        return jsonify(success=False, message="未找到需要删除的项目")

    if not checker.can_erase(project.knowledge_base_id):
        return jsonify(
            success=False,
            message="角色无权删除",
        )

    # 交给定时任务处理。
    project.deleted_at = 0
    db.session.commit()

    return jsonify(success=True)


"""
操作日志。
"""


@api.get("/histories")
@shop_required
@validate
def get_histories(query: QueryHistoriesArgument):
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    if query.type == ItemType.KNOWLEDGE_BASE:
        histories_query: FlaskQuery = (
            KnowledgeBaseHistory.query.join(
                KnowledgeBase,
                KnowledgeBase.id == KnowledgeBaseHistory.knowledge_base_id,
            )
            .filter(
                KnowledgeBaseHistory.knowledge_base_id == query.target_id,
            )
            .filter(
                KnowledgeBase.org_id == context_var.get_org_id_in_text_format(),
            )
            .order_by(
                KnowledgeBaseHistory.id.desc(),
            )
        )
    else:
        histories_query = (
            ProjectHistory.query.join(
                Project,
                ProjectHistory.project_id == Project.id,
            )
            .filter(
                ProjectHistory.project_id == query.target_id,
            )
            .filter(
                Project.org_id == context_var.get_org_id_in_text_format(),
            )
            .order_by(
                ProjectHistory.id.desc(),
            )
        )

    if query.page_no is not None and query.page_no > 0 and query.page_size is not None and query.page_size > 0:
        # 分页。
        histories_pagination: Pagination = histories_query.paginate(
            page=query.page_no,
            per_page=query.page_size,
        )
        total = histories_pagination.total
        items = histories_pagination.items
    else:
        # 不分页。
        total = histories_query.count()
        items = histories_query.all()

    return jsonify(success=True, total=total, data=[item.to_data() for item in items])


@api.get("/histories/<int:history_id>")
@shop_required
@validate
def get_history_detail(history_id: int, query: GetHistoryDetailArgument):
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    if query.type == ItemType.KNOWLEDGE_BASE:
        history_query: Query = (
            KnowledgeBaseHistory.query.join(
                KnowledgeBase,
                KnowledgeBase.id == KnowledgeBaseHistory.knowledge_base_id,
            )
            .filter(
                KnowledgeBaseHistory.id == history_id,
            )
            .filter(
                KnowledgeBase.org_id == context_var.get_org_id_in_text_format(),
            )
        )
    else:
        history_query = (
            ProjectHistory.query.join(
                Project,
                ProjectHistory.project_id == Project.id,
            )
            .filter(
                ProjectHistory.id == history_id,
            )
            .filter(
                Project.org_id == context_var.get_org_id_in_text_format(),
            )
        )

    history: KnowledgeBaseHistory | ProjectHistory | None = history_query.first()
    if history is None:
        return jsonify(success=False, message="未找到历史记录")

    return jsonify(
        success=True,
        data=history.to_detail(),
    )


"""
知识库权限。
"""


@api.get("/privileges")
@shop_required
def get_privileges():
    """
    查询所有可用于配置的知识库权限。
    """
    data = [
        {
            "知识库": [
                {"label": KnowledgeBaseRolePrivilege(member).label, "value": member}
                for member in KnowledgeBaseRolePrivilege.members()
                if member.startswith("KNOWLEDGE")
                and member not in ["KNOWLEDGE_IMPORT", "KNOWLEDGE_EXPORT", "KNOWLEDGE_APPROVE"]
            ]
        },
        {
            "回收站": [
                {"label": KnowledgeBaseRolePrivilege(member).label, "value": member}
                for member in KnowledgeBaseRolePrivilege.members()
                if member.startswith("TRASH")
            ]
        },
    ]
    return jsonify(success=True, data=data)


@api.get("/actions")
@shop_required
@validate
def get_actions(query: QueryActionsArgument):
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    checker = PrivilegeChecker(context_var)
    actions = checker.get_actions(query.knowledge_base_ids)

    return jsonify(
        success=True,
        data=actions,
    )


@api.get("/current_roles")
@shop_required
def get_current_roles():
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    roles: list[KnowledgeBaseRole] = get_user_knowledge_base_roles(
        org_id=context_var.get_org_id_in_text_format(),
        user_id=context_var.get_feisuo_user_id(),
        groups=context_var.get_feisuo_groups(),
    )

    return jsonify(
        success=True,
        data=[role.to_data() for role in roles],
    )


@api.get("/roles")
@shop_required
def get_roles():
    """
    查看所有知识库角色。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    roles: list[KnowledgeBaseRole] = KnowledgeBaseRole.query.filter(
        KnowledgeBaseRole.org_id == context_var.get_org_id_in_text_format(),
        KnowledgeBaseRole.deleted_at.is_(None),
    ).all()

    role_ids: list[int] = [role.id for role in roles]

    # 查询角色绑定的员工信息。
    staffs: list[KnowledgeBaseRoleToStaffMapping] = (
        db.session.query(KnowledgeBaseRoleToStaffMapping)
        .filter(
            KnowledgeBaseRoleToStaffMapping.role_id.in_(role_ids),
        )
        .all()
    )
    role_id_to_staffs_mapping: dict[int, list[KnowledgeBaseRoleToStaffMapping]] = {}
    for staff in staffs:
        staffs_for_current_role = role_id_to_staffs_mapping.get(staff.role_id) or []
        staffs_for_current_role.append(staff)
        role_id_to_staffs_mapping[staff.role_id] = staffs_for_current_role

    data = []

    for role in roles:
        staffs_for_current_role = role_id_to_staffs_mapping.get(role.id) or []
        data.append(role.to_data() | {"staffs": [s.to_data() for s in staffs_for_current_role]})

    return jsonify(success=True, data=data)


@api.post("/roles")
@shop_required
@validate
def create_knowledge_base_role(body: CreateOrUpdateKnowledgeBaseRoleBody):
    """
    新建知识库角色。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    if body.name is None:
        return jsonify(success=False, message="未填写名称")
    if not body.privileges:
        return jsonify(success=False, message="未配置权限")

    with in_transaction() as session:
        if session.query(
            session.query(KnowledgeBaseRole)
            .filter(
                KnowledgeBaseRole.org_id == context_var.get_org_id_in_text_format(),
                KnowledgeBaseRole.deleted_at.is_(None),
                KnowledgeBaseRole.name == body.name,
            )
            .exists()
        ).scalar():
            return jsonify(success=False, message="已存在同名的角色")

        role = KnowledgeBaseRole(
            org_id=context_var.get_org_id_in_text_format(),
            creator_id=context_var.get_feisuo_user_id(),
            updator_id=context_var.get_feisuo_user_id(),
            name=body.name,
            intro=body.intro or "",
            privileges=body.privileges,
        )
        session.add(role)
        session.flush()

        # 为角色绑定员工。
        if body.staffs is not None:
            for staff in body.staffs:
                session.add(
                    KnowledgeBaseRoleToStaffMapping(
                        role_id=role.id,
                        mold=staff.mold,
                        tag=staff.tag,
                    )
                )

        # 为角色绑定知识库
        if body.knowledge_bases is not None and body.knowledge_bases.knowledge_base_ids is not None:
            for knowledge_base_id in body.knowledge_bases.knowledge_base_ids:
                session.add(
                    KnowledgeBaseRoleToKnowledgeBaseMapping(
                        role_id=role.id,
                        knowledge_base_id=knowledge_base_id,
                    )
                )

    return jsonify(success=True, role_id=role.id)


@api.get("/roles/<int:role_id>")
@shop_required
@validate
def get_roles_detail(role_id: int):
    """
    查看角色详情。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    # 查询角色。
    role: KnowledgeBaseRole | None = (
        db.session.query(KnowledgeBaseRole)
        .filter(
            KnowledgeBaseRole.id == role_id,
            KnowledgeBaseRole.org_id == context_var.get_org_id_in_text_format(),
            KnowledgeBaseRole.deleted_at.is_(None),
        )
        .first()
    )
    if role is None:
        return jsonify(success=False, message="未找到该角色")

    # 查询角色绑定的员工信息。
    staffs: list[KnowledgeBaseRoleToStaffMapping] = (
        db.session.query(KnowledgeBaseRoleToStaffMapping)
        .filter(
            KnowledgeBaseRoleToStaffMapping.role_id == role_id,
        )
        .all()
    )

    # 检测是否有绑定知识库。
    kbs: list[dict[str, Any]] | None = None
    if role.has_limited_knowledge_base:
        knowledge_bases: list[KnowledgeBase] = (
            db.session.query(KnowledgeBase)
            .join(
                KnowledgeBaseRoleToKnowledgeBaseMapping,
                KnowledgeBaseRoleToKnowledgeBaseMapping.knowledge_base_id == KnowledgeBase.id,
            )
            .filter(
                KnowledgeBaseRoleToKnowledgeBaseMapping.role_id == role_id,
            )
            .all()
        )
        kbs = [
            {
                "id": kb.id,
                "name": kb.name,
                "is_deleted": kb.deleted_at is not None,
            }
            for kb in knowledge_bases
        ]

    data = role.to_data() | {"staffs": [staff.to_data() for staff in staffs], "knowledge_bases": kbs}

    return jsonify(success=True, data=data)


@api.put("/roles/<int:role_id>")
@shop_required
@validate
def modify_role_info(role_id: int, body: CreateOrUpdateKnowledgeBaseRoleBody):
    """
    修改角色信息。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    if (
        body.knowledge_bases is not None
        and body.knowledge_bases.select_type == KnowledgeBaseSelectType.PART
        and body.knowledge_bases.knowledge_base_ids is None
    ):
        return jsonify(success=False, message="未填写需要关联的知识库")

    with in_transaction() as session:
        # 查询角色。
        role: KnowledgeBaseRole | None = (
            session.query(KnowledgeBaseRole)
            .filter(
                KnowledgeBaseRole.id == role_id,
                KnowledgeBaseRole.org_id == context_var.get_org_id_in_text_format(),
                KnowledgeBaseRole.deleted_at.is_(None),
            )
            .first()
        )
        if role is None:
            return jsonify(success=False, message="未找到该角色")

        role.updator_id = context_var.get_feisuo_user_id()

        # 改名。
        if body.name is not None and body.name != role.name:
            if session.query(
                session.query(KnowledgeBaseRole)
                .filter(
                    KnowledgeBaseRole.org_id == context_var.get_org_id_in_text_format(),
                    KnowledgeBaseRole.deleted_at.is_(None),
                    KnowledgeBaseRole.name == body.name,
                )
                .exists()
            ).scalar():
                return jsonify(success=False, message="已存在同名的角色")
            role.name = body.name

        if body.intro is not None and body.intro != role.intro:
            role.intro = body.intro

        # 配置权限。
        if body.privileges is not None:
            role.privileges = body.privileges
            flag_modified(role, "privileges")

        # 如果前端愿意在数据没有变动的时候，就把该字段置为 None。就可以减少以下繁琐操作的频率了。
        if body.knowledge_bases is not None:
            # 配置知识库。
            # 如果原先限制了应用的知识库，但是现在不再限制。
            if (
                body.knowledge_bases.select_type == KnowledgeBaseSelectType.ALL
                and role.has_limited_knowledge_base is True
            ):
                role.has_limited_knowledge_base = False
                session.query(KnowledgeBaseRoleToKnowledgeBaseMapping).filter(
                    KnowledgeBaseRoleToKnowledgeBaseMapping.role_id == role_id,
                ).delete()
            # 如果原先限制了应用的知识库，但是现在有所改动。
            elif (
                body.knowledge_bases.select_type == KnowledgeBaseSelectType.PART
                and body.knowledge_bases.knowledge_base_ids is not None
                and role.has_limited_knowledge_base is True
            ):

                knowledge_bases: list[KnowledgeBaseRoleToKnowledgeBaseMapping] = (
                    session.query(KnowledgeBaseRoleToKnowledgeBaseMapping)
                    .filter(
                        KnowledgeBaseRoleToKnowledgeBaseMapping.role_id == role_id,
                    )
                    .all()
                )
                original_knowledge_base_ids_set: set[int] = set()
                knowledge_base_id_to_knowledge_base_mapping: dict[int, KnowledgeBaseRoleToKnowledgeBaseMapping] = {}
                for knowledge_base in knowledge_bases:
                    original_knowledge_base_ids_set.add(knowledge_base.knowledge_base_id)
                    knowledge_base_id_to_knowledge_base_mapping[knowledge_base.knowledge_base_id] = knowledge_base
                now_knowledge_base_ids_set: set[int] = set(body.knowledge_bases.knowledge_base_ids)

                for need_add_knowledge_base_id in now_knowledge_base_ids_set - original_knowledge_base_ids_set:
                    session.add(
                        KnowledgeBaseRoleToKnowledgeBaseMapping(
                            role_id=role_id,
                            knowledge_base_id=need_add_knowledge_base_id,
                        )
                    )
                for need_delete_knowledge_base_id in original_knowledge_base_ids_set - now_knowledge_base_ids_set:
                    if need_delete_knowledge_base := knowledge_base_id_to_knowledge_base_mapping.get(
                        need_delete_knowledge_base_id
                    ):
                        session.delete(need_delete_knowledge_base)
            # 如果原先没有限制应用的知识库，但是现在限制了。
            elif (
                body.knowledge_bases.select_type == KnowledgeBaseSelectType.PART
                and body.knowledge_bases.knowledge_base_ids is not None
                and role.has_limited_knowledge_base is False
            ):
                role.has_limited_knowledge_base = True
                for knowledge_base_id in body.knowledge_bases.knowledge_base_ids:
                    session.add(
                        KnowledgeBaseRoleToKnowledgeBaseMapping(
                            role_id=role_id,
                            knowledge_base_id=knowledge_base_id,
                        )
                    )

        # 配置员工
        if body.staffs is not None:
            # 找到原先配置的员工信息
            original_staffs: list[KnowledgeBaseRoleToStaffMapping] = (
                session.query(KnowledgeBaseRoleToStaffMapping)
                .filter(
                    KnowledgeBaseRoleToStaffMapping.role_id == role_id,
                )
                .all()
            )

            # 进行员工信息比对。
            original_staff_tags_set: set[str] = {staff.tag for staff in original_staffs}
            current_staff_tags_set: set[str] = {staff.tag for staff in body.staffs}
            current_staff_tag_to_staff_mold_mapping: dict[str, StaffMold] = {
                staff.tag: staff.mold for staff in body.staffs
            }
            original_staff_tag_to_staff_mapping: dict[str, KnowledgeBaseRoleToStaffMapping] = {
                staff.tag: staff for staff in original_staffs
            }
            need_delete_staff_tags = list(original_staff_tags_set - current_staff_tags_set)
            need_add_staff_tags = list(current_staff_tags_set - original_staff_tags_set)

            # 删除员工
            for need_delete_staff_tag in need_delete_staff_tags:
                if need_delete_staff := original_staff_tag_to_staff_mapping.get(need_delete_staff_tag):
                    session.delete(need_delete_staff)

            # 新增员工
            for need_add_staff_tag in need_add_staff_tags:
                if staff_mold := current_staff_tag_to_staff_mold_mapping.get(need_add_staff_tag):
                    session.add(
                        KnowledgeBaseRoleToStaffMapping(
                            role_id=role_id,
                            mold=staff_mold,
                            tag=need_add_staff_tag,
                        )
                    )

    return jsonify(success=True, role_id=role.id)


@api.delete("/roles/<int:role_id>")
@shop_required
@validate
def delete_role(role_id: int):
    """
    删除角色。
    """
    context_var, err = get_context_var(g)
    if err:
        return jsonify(
            success=False,
            message=err,
        )
    context_var = cast(ContextVar, context_var)

    with in_transaction() as session:
        # 查询角色。
        role: KnowledgeBaseRole | None = (
            session.query(KnowledgeBaseRole)
            .filter(
                KnowledgeBaseRole.id == role_id,
                KnowledgeBaseRole.org_id == context_var.get_org_id_in_text_format(),
                KnowledgeBaseRole.deleted_at.is_(None),
            )
            .first()
        )
        if role is None:
            return jsonify(success=False, message="未找到该角色")

        role.updator_id = context_var.get_feisuo_user_id()
        role.deleted_at = int(time.time())

    return jsonify(success=True)
