from loguru import logger
from sqlalchemy.orm.query import RowReturning<PERSON>uery

from robot_processor.ext import db
from robot_processor.knowledge.entities import ContextVar
from robot_processor.knowledge.enums import KnowledgeBaseRolePrivilege
from robot_processor.knowledge.models import KnowledgeBase
from robot_processor.knowledge.models import KnowledgeBaseRole
from robot_processor.knowledge.models import KnowledgeBaseRoleToKnowledgeBaseMapping
from robot_processor.knowledge.models import KnowledgeBaseRoleToStaffMapping
from robot_processor.knowledge.models import KnowledgeBaseToShopMapping
from robot_processor.users.models import LeyanUserShopMapping


class PrivilegeChecker:
    def __init__(
        self,
        context_var: ContextVar,
    ):
        self.context_var = context_var

        self._roles: list[KnowledgeBaseRole] | None = None
        self._channel_ids: list[int] | None = None
        self._public_privileges: set[KnowledgeBaseRolePrivilege] | None = None

    @property
    def user_bind_channel_ids(self) -> list[int]:
        if self._channel_ids is not None:
            return self._channel_ids
        shops = LeyanUserShopMapping.query.filter(
            LeyanUserShopMapping.user_id == self.context_var.get_feisuo_user_id()
        ).all()
        channel_ids: list[int] = [shop.shop_id for shop in shops]
        self._channel_ids = channel_ids
        return channel_ids

    @property
    def roles(self) -> list[KnowledgeBaseRole]:
        """
        获取账号及其账号组对应的所有知识库角色。
        """
        if self._roles is not None:
            return self._roles
        tags = self.context_var.get_feisuo_groups() + [str(self.context_var.get_feisuo_user_id())]
        roles: list[KnowledgeBaseRole] = (
            KnowledgeBaseRole.query.join(
                KnowledgeBaseRoleToStaffMapping,
                KnowledgeBaseRoleToStaffMapping.role_id == KnowledgeBaseRole.id,
            )
            .filter(
                KnowledgeBaseRole.org_id == self.context_var.get_org_id_in_text_format(),
                KnowledgeBaseRole.deleted_at.is_(None),
            )
            .filter(
                KnowledgeBaseRoleToStaffMapping.tag.in_(tags),
            )
            .all()
        )
        self._roles = roles

        return self._roles

    @property
    def public_privileges(self) -> set[KnowledgeBaseRolePrivilege]:
        if self._public_privileges is not None:
            return self._public_privileges

        public_privileges: set[KnowledgeBaseRolePrivilege] = set()
        for role in self.roles:
            if not role.has_limited_knowledge_base:
                public_privileges = public_privileges | set(role.privileges)

        self._public_privileges = public_privileges
        return self._public_privileges

    def get_all_private_privileges(self) -> dict[int, set[KnowledgeBaseRolePrivilege]]:
        private_privilege_roles: list[KnowledgeBaseRoleToKnowledgeBaseMapping] = (
            db.session.query(
                KnowledgeBaseRoleToKnowledgeBaseMapping,
            )
            .filter(
                KnowledgeBaseRoleToKnowledgeBaseMapping.role_id.in_(
                    [role.id for role in self.roles if role.has_limited_knowledge_base]
                ),
            )
            .all()
        )

        knowledge_base_id_to_private_privileges_mapping: dict[int, set[KnowledgeBaseRolePrivilege]] = {}
        role_id_to_role_mapping: dict[int, KnowledgeBaseRole] = {role.id: role for role in self.roles}
        for private_privilege_role in private_privilege_roles:
            private_privileges = (
                knowledge_base_id_to_private_privileges_mapping.get(private_privilege_role.knowledge_base_id) or set()
            )
            if role_info := role_id_to_role_mapping.get(private_privilege_role.role_id):
                private_privileges = private_privileges | set(role_info.privileges)
            knowledge_base_id_to_private_privileges_mapping[
                private_privilege_role.knowledge_base_id
            ] = private_privileges

        return knowledge_base_id_to_private_privileges_mapping

    def get_private_privileges(self, knowledge_base_id: int) -> set[KnowledgeBaseRolePrivilege]:
        private_privileges: set[KnowledgeBaseRolePrivilege] = set()
        private_privilege_role_ids_query: RowReturningQuery[tuple[int,]] = db.session.query(
            KnowledgeBaseRoleToKnowledgeBaseMapping.role_id,
        ).filter(
            KnowledgeBaseRoleToKnowledgeBaseMapping.knowledge_base_id == knowledge_base_id,
            KnowledgeBaseRoleToKnowledgeBaseMapping.role_id.in_(
                [role.id for role in self.roles if role.has_limited_knowledge_base]
            ),
        )
        private_privilege_role_ids: list[int] = [
            private_privilege_role_id for (private_privilege_role_id,) in private_privilege_role_ids_query
        ]
        role_id_to_role_mapping: dict[int, KnowledgeBaseRole] = {role.id: role for role in self.roles}
        for private_privilege_role_id in private_privilege_role_ids:
            if private_privilege_role := role_id_to_role_mapping.get(private_privilege_role_id):
                private_privileges = private_privileges | set(private_privilege_role.privileges)

        return private_privileges

    def can_visit(self, knowledge_base_id: int, ignore_user_bind_channels: bool = True) -> bool:
        knowledge_base: KnowledgeBase | None = (
            db.session.query(KnowledgeBase)
            .filter(
                KnowledgeBase.id == knowledge_base_id,
                KnowledgeBase.org_id == self.context_var.get_org_id_in_text_format(),
            )
            .first()
        )
        if knowledge_base is None:
            logger.warning(f"未找到知识库: {knowledge_base_id}, org_id: {self.context_var.get_org_id_in_text_format()}")
            return False

        if ignore_user_bind_channels:
            knowledge_base_ids_query = db.session.query(KnowledgeBaseToShopMapping.knowledge_base_id).filter(
                KnowledgeBaseToShopMapping.channel_id == self.context_var.channel_id,
            )
            knowledge_base_ids = [knowledge_base_id for (knowledge_base_id,) in knowledge_base_ids_query]
            if not knowledge_base_ids:
                return True
            if knowledge_base_id in knowledge_base_ids:
                return True
        else:
            channel_ids = self.user_bind_channel_ids + [self.context_var.channel_id]
            kbs: list[KnowledgeBaseToShopMapping] = (
                db.session.query(KnowledgeBaseToShopMapping)
                .filter(KnowledgeBaseToShopMapping.channel_id.in_(channel_ids))
                .all()
            )
            if len(kbs) == 0:
                return True
            channel_id_to_knowledge_base_ids_mapping: dict[int, list[int]] = {}
            for kb in kbs:
                knowledge_base_ids = channel_id_to_knowledge_base_ids_mapping.get(kb.channel_id) or []
                knowledge_base_ids.append(kb.knowledge_base_id)
                channel_id_to_knowledge_base_ids_mapping[kb.channel_id] = knowledge_base_ids

            for knowledge_base_ids in channel_id_to_knowledge_base_ids_mapping.values():
                if len(knowledge_base_ids) == 0:
                    return True
                if knowledge_base_id in knowledge_base_ids:
                    return True

        return False

    def can_read(self, knowledge_base_id: int) -> bool:
        if KnowledgeBaseRolePrivilege.KNOWLEDGE_READ in self.public_privileges:
            return True
        if KnowledgeBaseRolePrivilege.KNOWLEDGE_READ in self.get_private_privileges(knowledge_base_id):
            return True
        return False

    def can_edit(self, knowledge_base_id: int) -> bool:
        if KnowledgeBaseRolePrivilege.KNOWLEDGE_EDIT in self.public_privileges:
            return True
        if KnowledgeBaseRolePrivilege.KNOWLEDGE_EDIT in self.get_private_privileges(knowledge_base_id):
            return True
        return False

    def can_delete(self, knowledge_base_id: int) -> bool:
        if KnowledgeBaseRolePrivilege.KNOWLEDGE_DELETE in self.public_privileges:
            return True
        if KnowledgeBaseRolePrivilege.KNOWLEDGE_DELETE in self.get_private_privileges(knowledge_base_id):
            return True
        return False

    def can_approve(self, knowledge_base_id: int) -> bool:
        if KnowledgeBaseRolePrivilege.KNOWLEDGE_EDIT in self.public_privileges:
            return True
        if KnowledgeBaseRolePrivilege.KNOWLEDGE_EDIT in self.get_private_privileges(knowledge_base_id):
            return True
        return False

    def can_import(self, knowledge_base_id: int) -> bool:
        if KnowledgeBaseRolePrivilege.KNOWLEDGE_IMPORT in self.public_privileges:
            return True
        if KnowledgeBaseRolePrivilege.KNOWLEDGE_IMPORT in self.get_private_privileges(knowledge_base_id):
            return True
        return False

    def can_export(self, knowledge_base_id: int) -> bool:
        if KnowledgeBaseRolePrivilege.KNOWLEDGE_EXPORT in self.public_privileges:
            return True
        if KnowledgeBaseRolePrivilege.KNOWLEDGE_EXPORT in self.get_private_privileges(knowledge_base_id):
            return True
        return False

    def can_check(self, knowledge_base_id: int) -> bool:
        """
        回收站的查看权限。
        """
        if KnowledgeBaseRolePrivilege.TRASH_CHECK in self.public_privileges:
            return True
        if KnowledgeBaseRolePrivilege.TRASH_CHECK in self.get_private_privileges(knowledge_base_id):
            return True
        return False

    def can_erase(self, knowledge_base_id: int) -> bool:
        """
        回收站的彻底删除权限。
        """
        if KnowledgeBaseRolePrivilege.TRASH_ERASE in self.public_privileges:
            return True
        if KnowledgeBaseRolePrivilege.TRASH_ERASE in self.get_private_privileges(knowledge_base_id):
            return True
        return False

    def can_recover(self, knowledge_base_id: int) -> bool:
        """
        回收站的恢复权限。
        """
        if KnowledgeBaseRolePrivilege.TRASH_RECOVER in self.public_privileges:
            return True
        if KnowledgeBaseRolePrivilege.TRASH_RECOVER in self.get_private_privileges(knowledge_base_id):
            return True
        return False

    def get_actions(self, knowledge_base_ids: list[int]) -> dict[int, list[KnowledgeBaseRolePrivilege]]:
        knowledge_base_id_to_private_privileges_mapping = self.get_all_private_privileges()
        actions: dict[int, list[KnowledgeBaseRolePrivilege]] = {}
        for knowledge_base_id in knowledge_base_ids:
            if (
                private_privileges := knowledge_base_id_to_private_privileges_mapping.get(knowledge_base_id)
            ) is not None:
                actions[knowledge_base_id] = list(private_privileges | self.public_privileges)
            else:
                actions[knowledge_base_id] = list(self.public_privileges)
        return actions
