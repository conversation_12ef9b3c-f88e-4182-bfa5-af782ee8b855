from enum import StrEnum


class ProjectType(StrEnum):
    """
    知识的类型。
    """

    CATEGORY = "CATEGORY"
    DOCUMENT = "DOCUMENT"


class HistoryAction(StrEnum):
    CREATE = "CREATE"  # 新建
    MODIFY_CONTENT = "MODIFY_CONTENT"  # 修改内容
    MODIFY_RELATION_SHOP = "MODIFY_RELATION_SHOP"  # 修改应用的店铺
    MODIFY_ROLE = "MODIFY_ROLE"  # 修改应用的角色
    RENAME = "RENAME"  # 重命名
    DELETE = "DELETE"  # 删除
    PASSTHROUGH = "PASSTHROUGH"  # 审批通过
    OVERRULE = "OVERRULE"  # 驳回
    RECOVER = "RECOVER"  # 恢复
    MOVE = "MOVE"  # 移动项目位置


class DocumentAction(StrEnum):
    """
    知识的相关操作。
    """

    SAVE = "SAVE"  # 保存
    RELEASE = "RELEASE"  # 保存并发布
    PASSTHROUGH = "PASSTHROUGH"  # 审核通过
    OVERRULE = "OVERRULE"  # 审核驳回


class ProjectStatus(StrEnum):
    EDITING = "EDITING"  # 编辑中
    TO_BE_RELEASED = "TO_BE_RELEASED"  # 待发布
    TO_BE_APPROVED = "TO_BE_APPROVED"  # 待审核
    RELEASED = "RELEASED"  # 已发布
    REVERTED = "REVERTED"  # 已驳回
    INACTIVE = "INACTIVE"  # 已失效


class KnowledgeBaseRoleStatus(StrEnum):
    ACTIVATED = "ACTIVATED"  # 可用
    INACTIVATED = "INACTIVATED"  # 停用
    DELETED = "DELETED"  # 已删除


class ItemType(StrEnum):
    """
    类型。
    """

    KNOWLEDGE_BASE = "KNOWLEDGE_BASE"  # 知识库
    PROJECT = "PROJECT"  # 项目


class MoveAction(StrEnum):
    """
    项目的移动操作。
    """

    ADD_CHILD = "ADD_CHILD"  # 新增子节点
    MOVE_BEFORE = "MOVE_BEFORE"  # 移动到该节点前
    MOVE_AFTER = "MOVE_AFTER"  # 移动到该节点后


class SearchLocation(StrEnum):
    """
    查询到的位置。
    """

    TITLE = "TITLE"  # 标题
    INTRO = "INTRO"  # 摘要
    CONTENT = "CONTENT"  # 正文


class KnowledgeBaseOrderField(StrEnum):
    CREATED_AT = "CREATED_AT"
    UPDATED_AT = "UPDATED_AT"


class Sort(StrEnum):
    DESC = "DESC"
    ASC = "ASC"


class StaffMold(StrEnum):
    ACCOUNT = "ACCOUNT"
    GROUP = "GROUP"


class KnowledgeBaseSelectType(StrEnum):
    ALL = "ALL"  # 适用于所有知识库
    PART = "PART"  # 适用于指定的知识库


class KnowledgeBaseRolePrivilege(StrEnum):
    KNOWLEDGE_READ = "KNOWLEDGE_READ"  # 可查看知识
    KNOWLEDGE_EDIT = "KNOWLEDGE_EDIT"  # 可编辑知识
    KNOWLEDGE_DELETE = "KNOWLEDGE_DELETE"  # 可删除知识
    KNOWLEDGE_IMPORT = "KNOWLEDGE_IMPORT"  # 可导入知识
    KNOWLEDGE_EXPORT = "KNOWLEDGE_EXPORT"  # 可导出知识
    KNOWLEDGE_APPROVE = "KNOWLEDGE_APPROVE"  # 可审批知识

    TRASH_CHECK = "TRASH_CHECK"  # 可查看回收站内容
    TRASH_RECOVER = "TRASH_RECOVER"  # 可恢复回收站内容
    TRASH_ERASE = "TRASH_ERASE"  # 可彻底删除回收站内容

    @classmethod
    def members(cls) -> list[str]:
        return [str(member) for member in cls]

    @property
    def label(self) -> str:
        return {
            KnowledgeBaseRolePrivilege.KNOWLEDGE_READ: "查看",
            KnowledgeBaseRolePrivilege.KNOWLEDGE_EDIT: "编辑",
            KnowledgeBaseRolePrivilege.KNOWLEDGE_DELETE: "删除",
            KnowledgeBaseRolePrivilege.KNOWLEDGE_IMPORT: "导入",
            KnowledgeBaseRolePrivilege.KNOWLEDGE_EXPORT: "导出",
            KnowledgeBaseRolePrivilege.KNOWLEDGE_APPROVE: "审批",
            KnowledgeBaseRolePrivilege.TRASH_CHECK: "查看",
            KnowledgeBaseRolePrivilege.TRASH_RECOVER: "恢复",
            KnowledgeBaseRolePrivilege.TRASH_ERASE: "删除",
        }[self]


class Tab(StrEnum):
    FAVORITE = "FAVORITE"
    CREATED = "CREATED"
    WAITING_APPROVE = "WAITING_APPROVE"
