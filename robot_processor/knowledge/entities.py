from dataclasses import dataclass
from typing import TypedDict

from pydantic.fields import Field
from pydantic.main import BaseModel

from robot_processor.assistant.schema import AccountDetailV2
from robot_processor.knowledge.enums import ItemType
from robot_processor.knowledge.enums import KnowledgeBaseRolePrivilege
from robot_processor.knowledge.enums import MoveAction
from robot_processor.knowledge.enums import ProjectType
from robot_processor.knowledge.enums import StaffMold


@dataclass
class ContextVar:
    org_id: str
    user: AccountDetailV2
    channel_id: int

    def get_org_id_in_number_format(self) -> int:
        """
        整数类型的 ORG ID。
        """
        return int(self.org_id)

    def get_org_id_in_text_format(self) -> str:
        """
        文本类型的 ORG ID。
        """
        return self.org_id

    def get_feisuo_user_id(self) -> int:
        """
        飞梭账号 ID。
        """
        return self.user.feisuo_user_id  # type: ignore[return-value]

    def get_feisuo_groups(self) -> list[str]:
        """
        飞梭账号组。
        """
        group_uuids: list[str] = []
        for group in self.user.feisuo_groups:
            if group_uuid := group.get("uuid"):
                group_uuids.append(group_uuid)
        return group_uuids


class Staff(BaseModel):
    mold: StaffMold = Field(description="是员工账号还是账号组")
    tag: str = Field(description="乐言账号 ID 或者账号组的 UUID")


class TrashItem(BaseModel):
    id: int
    type: ItemType
    name: str
    icon: str | None = None
    deleted_at: int
    project_type: ProjectType | None = None
    updator_id: int | None = None
    updator_name: str | None = None
    knowledge_base_id: int | None = None
    actions: list[KnowledgeBaseRolePrivilege] | None = None


class DocumentLockInfo(TypedDict):
    key: str
    user_id: int
    last_updated_at: int


class ProjectHistoryData(BaseModel):
    """
    项目操作历史的内容。
    """

    class Location(BaseModel):
        target_id: int = Field(description="需要移动到的位置的锚点项目 ID")
        action: MoveAction = Field(description="移动操作")

    class ModifyContent(BaseModel):
        name: str | None = Field(default=None, description="项目名称")
        intro: str | None = Field(default=None, description="摘要")
        content: dict | None = Field(default=None, description="文档的 Lexical 格式的数据")
        preview: str | None = Field(default=None, description="文档的纯文本内容")
        knowledge_base_id: int | None = Field(default=None, description="知识库的 ID")
        parent_id: int | None = Field(default=None, description="父项目的 ID。如果该字段为 0，则说明该项目位于最顶层。")

    class RecoverContent(BaseModel):
        move_to_project_id: int | None = Field(description="移动到的项目的 ID")
        move_action: MoveAction | None = Field(description="移动操作")

    class RenameContent(BaseModel):
        original_name: str | None = Field(default=None, description="原标题")
        modified_name: str | None = Field(default=None, description="修改后的标题")

    location: Location | None = Field(default=None, description="移动操作")
    reason: str | None = Field(default=None, description="操作原由")
    modify_content: ModifyContent | None = Field(default=None, description="修改内容")
    recover_content: RecoverContent | None = Field(default=None, description="恢复操作")
    rename_content: RenameContent | None = Field(default=None, description="修改标题")


class KnowledgeBaseHistoryData(BaseModel):
    class RenameContent(BaseModel):
        original_name: str | None = Field(default=None, description="原标题")
        modified_name: str | None = Field(default=None, description="修改后的标题")

    class ModifyContent(BaseModel):
        icon: str | None = Field(default=None, description="知识库的 icon")
        name: str | None = Field(default=None, description="知识库的名称")
        intro: str | None = Field(default=None, description="知识库的简介")

    reason: str | None = Field(default=None, description="操作原由")
    rename_content: RenameContent | None = Field(default=None, description="修改标题")
    modify_content: ModifyContent | None = Field(default=None, description="修改内容")
