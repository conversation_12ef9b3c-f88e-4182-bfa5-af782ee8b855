from typing import Literal

from pydantic import BaseModel
from pydantic.fields import Field

from robot_processor.knowledge.entities import Staff
from robot_processor.knowledge.enums import DocumentAction
from robot_processor.knowledge.enums import ItemType
from robot_processor.knowledge.enums import KnowledgeBaseOrderField
from robot_processor.knowledge.enums import KnowledgeBaseRolePrivilege
from robot_processor.knowledge.enums import KnowledgeBaseSelectType
from robot_processor.knowledge.enums import MoveAction
from robot_processor.knowledge.enums import ProjectType
from robot_processor.knowledge.enums import Sort
from robot_processor.knowledge.enums import Tab


class DefaultArgument(BaseModel):
    """
    默认的查询参数。
    如果没有传递页码等信息，则不分页。
    """

    page_no: int | None = Field(description="页码")
    page_size: int | None = Field(description="每页的数据量")


class DefaultBody(BaseModel):
    """
    默认的请求体。
    """

    pass


class KnowledgeBaseArgument(DefaultArgument):
    """
    查询知识库的参数。
    """

    creator_id: int | None = Field(None, description="创建人的乐言账号 ID")
    order_field: KnowledgeBaseOrderField = Field(KnowledgeBaseOrderField.CREATED_AT, description="排序选用的字段")
    sort: Sort = Field(Sort.DESC, description="倒序还是正序")


class CreateKnowledgeBaseBody(DefaultBody):
    """
    新建知识库的请求体。
    """

    icon: str = Field(description="知识库的 icon")
    name: str = Field(description="知识库的名称")
    intro: str | None = Field(default=None, description="知识库的简介")


class ModifyKnowledgeBaseContentBody(DefaultBody):
    """
    修改知识库内容的请求体。
    """

    icon: str | None = Field(description="知识库的 icon")
    name: str | None = Field(description="知识库的名称")
    intro: str | None = Field(description="知识库的简介")


class DeleteKnowledgeBaseBody(DefaultBody):
    """
    删除知识库的请求体。
    """

    reason: str = Field(description="删除原因")


class QueryLatestProjectArgument(DefaultArgument):
    knowledge_base_ids: list[int] = Field(description="需要查询的知识库的 ID 组成的列表")
    project_type: ProjectType | None = Field(default=ProjectType.DOCUMENT, description="项目类型")


class QueryProjectArgument(DefaultArgument):
    """
    查询项目的所用参数。
    """

    parent_id: int | None = Field(default=None, description="父项目的 ID")
    project_type: ProjectType | None = Field(default=None, description="项目的类型")


class MoveProjectBody(DefaultBody):
    """
    移动项目的请求体。
    """

    target_id: int
    action: MoveAction = Field(description="移动操作")


class SearchDocumentsArgument(DefaultArgument):
    """
    查询文档的参数。
    """

    keyword: str = Field(description="关键词")


class FindDocumentsArgument(DefaultArgument):
    keyword: str | None = Field(default=None, description="关键词")
    knowledge_base_ids: list[int] | None = Field(default=None, description="知识库 ID")
    tab: Tab | None = Field(default=None, description="指定标签页")


class QueryDocumentsArgument(DefaultArgument):
    """
    罗列文档的参数。
    """

    parent_id: int | None = Field(default=None, description="文档的所属目录，如果没有填写，则说明要查全部")


class CreateCategoryBody(DefaultBody):
    """
    创建分类的请求体。
    """

    name: str = Field(description="分类标题")
    parent_id: int = Field(default=0, description="父项目的 ID。如果该字段为 0，则说明该分类位于最顶层。")


class RenameProjectBody(DefaultBody):
    """
    修改项目的标题。
    """

    name: str = Field(description="项目标题")


class CreateDocumentBody(DefaultBody):
    """
    新建知识的请求体。
    """

    name: str = Field(description="文章标题")
    parent_id: int = Field(default=0, description="父项目的 ID。如果该字段为 0，则说明该文档位于最顶层。")


class ModifyDocumentBody(DefaultBody):
    """
    修改知识内容的请求体。
    """

    name: str | None = Field(description="文章标题")
    intro: str | None = Field(description="文档的摘要")
    preview: str | None = Field(description="文档的纯文本内容")
    content: dict | None = Field(description="文档的 Lexical 格式的数据")
    action: Literal[DocumentAction.SAVE, DocumentAction.RELEASE] = Field(description="本次操作是保存还是发布")


class AutoSaveDocumentBody(DefaultBody):
    """
    自动保存知识内容的请求体。
    """

    intro: str | None = Field(description="文档的摘要")
    preview: str | None = Field(description="文档的纯文本内容")
    content: dict | None = Field(description="文档的 Lexical 格式的数据")


class ApproveDocumentBody(DefaultBody):
    """
    对知识进行审核的请求体。
    """

    action: Literal[DocumentAction.PASSTHROUGH, DocumentAction.OVERRULE] = Field(description="审核操作")
    reason: str = Field(default="", description="驳回原因")


class DeleteProjectBody(DefaultBody):
    """
    删除项目的请求体。
    """

    reason: str = Field(description="删除原因")


class TagFavoriteBody(DefaultBody):
    """
    标记收藏。
    """

    project_id: int = Field(description="需要收藏的项目的 ID")


class QueryRubbishArgument(DefaultArgument):
    pass


class RecoverTrashBody(DefaultBody):
    """
    从垃圾站恢复项目的请求体。
    """

    move_to_project_id: int | None = Field(description="移动到的项目的 ID。如果不填，则是移动到知识库的最外层。")
    move_to_knowledge_base_id: int = Field(description="需要移动到的知识库的 ID。")
    move_action: MoveAction = Field(description="移动操作")


class QueryHistoriesArgument(DefaultArgument):
    """
    查询操作历史的参数。
    """

    target_id: int = Field(description="需要查询的对象的 ID")
    type: ItemType = Field(description="需要查看的是知识库的历史还是项目的历史")


class GetHistoryDetailArgument(DefaultArgument):
    """
    查看操作历史详情的参数。
    """

    type: ItemType = Field(description="需要查看的是知识库的历史还是项目的历史")


class CreateOrUpdateKnowledgeBaseRoleBody(DefaultBody):
    """
    新建或修改角色信息
    """

    class SelectedKnowledgeBase(BaseModel):
        select_type: KnowledgeBaseSelectType = Field(description="选择类型")
        knowledge_base_ids: list[int] | None = Field(
            default=None,
            description="适用于的知识库，如果选择了 ALL，则可以不关心该字段",
            min_items=1,
        )

    name: str | None = Field(description="需要新建的知识库角色的名称，如果为 None 就是不修改")
    intro: str | None = Field(description="角色备注")
    staffs: list[Staff] | None = Field(default=None, description="绑定的员工信息，如果为 None 就是不修改", min_items=1)
    privileges: list[KnowledgeBaseRolePrivilege] | None = Field(
        default=None, description="相应的权限组成的列表，如果为 None 就是不修改", min_items=1
    )
    knowledge_bases: SelectedKnowledgeBase | None = Field(default=None, description="适用的知识库范围，如果为 None 就是不修改")


class ModifyShopBindsBody(BaseModel):
    """
    修改店铺绑定的请求体。
    """

    knowledge_base_ids: list[int] | None = Field(
        default=None,
        description="适用于的知识库，如果为空，则说明适用于所有知识库",
        min_items=1,
    )


class QueryShopArgument(DefaultArgument):
    keyword: str | None = Field(description="查询店铺名称的 Keyword")


class QueryActionsArgument(DefaultArgument):
    knowledge_base_ids: list[int] = Field(description="需要检测的知识库的 ID")
