import datetime
import json
import time
from io import BytesIO

import oss2
from flask import current_app
from loguru import logger

from robot_processor.client import oss_client
from robot_processor.ext import db
from robot_processor.knowledge.enums import ItemType
from robot_processor.knowledge.models import Document
from robot_processor.knowledge.models import KnowledgeBase
from robot_processor.knowledge.models import KnowledgeBaseHistory
from robot_processor.knowledge.models import KnowledgeBaseRoleToKnowledgeBaseMapping
from robot_processor.knowledge.models import KnowledgeBaseToShopMapping
from robot_processor.knowledge.models import MyFavorite
from robot_processor.knowledge.models import Project
from robot_processor.knowledge.models import ProjectHistory


def upload_json_to_oss(json_string: str, org_id: str, item_type: ItemType, file_name: str) -> tuple[str, str | None]:
    now = datetime.datetime.now()
    today = now.today().date().strftime("%Y-%m-%d")

    base_url = current_app.config.get("OSS_BIZ_BASE_URL", "")
    headers = {
        "x-oss-storage-class": oss2.BUCKET_STORAGE_CLASS_STANDARD,
        "x-oss-object-acl": oss2.OBJECT_ACL_PUBLIC_READ,
    }

    key = "knowledge_archives/{date}/{org_id}/{item_type}/{file_name}".format(
        org_id=org_id,
        date=today,
        item_type=item_type,
        file_name=file_name,
    )

    bytes_io = BytesIO()
    bytes_io.write(json_string.encode("utf-8"))
    data = bytes_io.read()
    object_key = "business_order/count/" + key

    try:
        result = oss_client.bucket.put_object(object_key, data, headers=headers)
    except Exception as e:
        logger.error("OSS 请求失败：{}".format(e))
        return "", "OSS 请求失败"
    if result.status >= 400:
        logger.error("于 OSS 创建文件失败：{}".format(result.resp))
        return "", "OSS 创建文件失败"

    url = base_url + object_key
    return url, None


def archive_knowledge_base(knowledge_base_id: int):
    logger.info(f"开始归档知识库: {knowledge_base_id}")
    knowledge_base: KnowledgeBase | None = (
        db.session.query(KnowledgeBase)
        .filter(
            KnowledgeBase.id == knowledge_base_id,
        )
        .first()
    )
    if not knowledge_base:
        return None

    knowledge_base_histories: list[KnowledgeBaseHistory] = (
        db.session.query(KnowledgeBaseHistory)
        .filter(
            KnowledgeBaseHistory.knowledge_base_id == knowledge_base_id,
        )
        .all()
    )

    shops: list[KnowledgeBaseToShopMapping] = (
        db.session.query(KnowledgeBaseToShopMapping)
        .filter(
            KnowledgeBaseToShopMapping.knowledge_base_id == knowledge_base_id,
        )
        .all()
    )

    roles: list[KnowledgeBaseRoleToKnowledgeBaseMapping] = (
        db.session.query(KnowledgeBaseRoleToKnowledgeBaseMapping)
        .filter(
            KnowledgeBaseRoleToKnowledgeBaseMapping.knowledge_base_id == knowledge_base_id,
        )
        .all()
    )

    # todo: 可以考虑下关联角色是否不需要归档。
    roles_json_string = json.dumps([{"role_id": r.id} for r in roles], ensure_ascii=False)

    # todo: 可以考虑下关联店铺是否不需要归档。
    shops_json_string = json.dumps([{"channel_id": s.channel_id} for s in shops], ensure_ascii=False)

    knowledge_base_histories_json_string = json.dumps(
        [
            {
                "updator_id": kbh.updator_id,
                "action": str(kbh.action),
                "data": kbh.data,
            }
            for kbh in knowledge_base_histories
        ],
        ensure_ascii=False,
    )

    knowledge_base_json_string = json.dumps(
        {
            "icon": knowledge_base.icon,
            "name": knowledge_base.name,
            "intro": knowledge_base.intro,
            "org_id": knowledge_base.org_id,
            "creator_id": knowledge_base.creator_id,
            "updator_id": knowledge_base.updator_id,
        },
        ensure_ascii=False,
    )

    db.session.delete(knowledge_base)
    for knowledge_base_history in knowledge_base_histories:
        db.session.delete(knowledge_base_history)
    for shop in shops:
        db.session.delete(shop)
    for role in roles:
        db.session.delete(role)
    db.session.commit()
    logger.info("知识库已删除")

    prefix = "knowledge_base_{}".format(str(knowledge_base_id))
    upload_json_to_oss(
        json_string=knowledge_base_json_string,
        org_id=knowledge_base.org_id,
        item_type=ItemType.KNOWLEDGE_BASE,
        file_name=prefix + "knowledge_base.json",
    )
    upload_json_to_oss(
        json_string=knowledge_base_histories_json_string,
        org_id=knowledge_base.org_id,
        item_type=ItemType.KNOWLEDGE_BASE,
        file_name=prefix + "knowledge_base_histories.json",
    )
    upload_json_to_oss(
        json_string=shops_json_string,
        org_id=knowledge_base.org_id,
        item_type=ItemType.KNOWLEDGE_BASE,
        file_name=prefix + "shops.json",
    )
    upload_json_to_oss(
        json_string=roles_json_string,
        org_id=knowledge_base.org_id,
        item_type=ItemType.KNOWLEDGE_BASE,
        file_name=prefix + "roles.json",
    )

    logger.info("知识库归档已完成，开始归档知识库项目")

    project_ids_query = db.session.query(Project.id,).filter(
        Project.knowledge_base_id == knowledge_base_id,
    )
    project_ids = [project_id for (project_id,) in project_ids_query]
    for project_id in project_ids:
        archive_project(project_id)

    logger.info(f"知识库: {knowledge_base_id} 归档完成")
    return None


def archive_project(project_id: int):
    logger.info(f"开始归档知识库项目: {project_id}")
    project: Project | None = (
        db.session.query(Project)
        .filter(
            Project.id == project_id,
        )
        .first()
    )
    if not project:
        return None

    project_histories: list[ProjectHistory] = (
        db.session.query(ProjectHistory)
        .filter(
            ProjectHistory.project_id == project.id,
        )
        .all()
    )

    documents: list[Document] = (
        db.session.query(Document)
        .filter(
            Document.project_id == project.id,
        )
        .all()
    )

    favorites: list[MyFavorite] = (
        db.session.query(MyFavorite)
        .filter(
            MyFavorite.project_id == project.id,
        )
        .all()
    )

    # todo: 可以考虑下收藏是否不需要归档。
    favorites_json_string = json.dumps(
        [
            {
                "user_id": f.user_id,
            }
            for f in favorites
        ],
        ensure_ascii=False,
    )

    documents_json_string = json.dumps(
        [
            {
                "updator_id": d.updator_id,
                "intro": d.intro,
                "preview": d.preview,
                "content": d.content,
                "is_dirty": d.is_dirty,
                "is_released": d.is_released,
            }
            for d in documents
        ],
        ensure_ascii=False,
    )

    project_histories_json_string = json.dumps(
        [
            {
                "updator_id": ph.updator_id,
                "action": str(ph.action),
                "data": ph.data,
            }
            for ph in project_histories
        ],
        ensure_ascii=False,
    )

    project_json_string = json.dumps(
        {
            "name": project.name,
            "type": project.type,
            "org_id": project.org_id,
            "creator_id": project.creator_id,
            "updator_id": project.updator_id,
            "status": project.status,
        },
        ensure_ascii=False,
    )

    db.session.delete(project)
    for project_history in project_histories:
        db.session.delete(project_history)
    for document in documents:
        db.session.delete(document)
    for favorite in favorites:
        db.session.delete(favorite)
    db.session.commit()
    logger.info("项目已删除")

    prefix = "project_{}".format(str(project.id))
    upload_json_to_oss(
        json_string=project_json_string,
        org_id=project.org_id,
        item_type=ItemType.PROJECT,
        file_name=prefix + "project.json",
    )
    upload_json_to_oss(
        json_string=project_histories_json_string,
        org_id=project.org_id,
        item_type=ItemType.PROJECT,
        file_name=prefix + "project_histories.json",
    )
    upload_json_to_oss(
        json_string=documents_json_string,
        org_id=project.org_id,
        item_type=ItemType.PROJECT,
        file_name=prefix + "documents.json",
    )
    upload_json_to_oss(
        json_string=favorites_json_string,
        org_id=project.org_id,
        item_type=ItemType.PROJECT,
        file_name=prefix + "favorites.json",
    )

    logger.info(f"知识库项目: {project_id} 归档完成")
    return None


def archive_org_knowledge_bases(
    org_id: str,
):
    logger.info(f"正在对租户 {org_id} 的知识库进行归档")

    last_days = int(time.time()) - 14 * 24 * 3600
    knowledge_base_ids_query = db.session.query(KnowledgeBase.id).filter(
        KnowledgeBase.org_id == org_id,
        KnowledgeBase.deleted_at < last_days,
    )
    knowledge_base_ids = [knowledge_base_id for (knowledge_base_id,) in knowledge_base_ids_query]
    if not knowledge_base_ids:
        logger.info(f"租户 {org_id} 没有需要归档的知识库")
    else:
        logger.info(f"租户 {org_id} 需要归档 {len(knowledge_base_ids)} 个知识库: {knowledge_base_ids}")
    for knowledge_base_id in knowledge_base_ids:
        archive_knowledge_base(knowledge_base_id)


def archive_org_projects(
    org_id: str,
):
    logger.info(f"正在对租户 {org_id} 的知识库项目进行归档")

    last_days = int(time.time()) - 14 * 24 * 3600
    project_ids_query = db.session.query(Project.id).filter(
        Project.org_id == org_id,
        Project.deleted_at < last_days,
    )
    project_ids = [project_id for (project_id,) in project_ids_query]
    if not project_ids:
        logger.info(f"租户 {org_id} 没有需要归档的知识库项目")
    else:
        logger.info(f"租户 {org_id} 需要归档 {len(project_ids)} 个知识库项目: {project_ids}")
    for project_id in project_ids:
        archive_project(project_id)


def get_org_ids() -> list[str]:
    org_ids_query = db.session.query(KnowledgeBase.org_id.distinct()).all()
    org_ids = [org_id for (org_id,) in org_ids_query]
    return org_ids


def archive_deleted_knowledge_bases():
    org_ids = get_org_ids()
    for org_id in org_ids:
        archive_org_knowledge_bases(org_id)


def archive_deleted_projects() -> None:
    org_ids = get_org_ids()
    for org_id in org_ids:
        archive_org_projects(org_id)
