from typing import Any

import sqlalchemy as sa
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship

from robot_processor.db import BasicMixin
from robot_processor.db import DbBaseModel
from robot_processor.knowledge.enums import HistoryAction
from robot_processor.knowledge.enums import KnowledgeBaseRolePrivilege
from robot_processor.knowledge.enums import ProjectStatus
from robot_processor.knowledge.enums import ProjectType
from robot_processor.knowledge.enums import StaffMold


def complete_user_name(user_id: int) -> str:
    """
    获取用户名称。
    """
    from robot_processor.users.models import LeyanUser

    leyan_user: LeyanUser | None = LeyanUser.query.filter(
        LeyanUser.id == user_id,
    ).first()
    if not leyan_user:
        return ""
    else:
        return leyan_user.nickname


class KnowledgeBase(DbBaseModel, BasicMixin):
    """
    知识库。
    """

    icon: Mapped[str] = mapped_column(sa.String(64), comment="知识库的 ICON 的标识符")
    name: Mapped[str] = mapped_column(sa.String(64), comment="知识库的名称")
    intro: Mapped[str] = mapped_column(sa.TEXT, comment="知识库的简介")
    org_id: Mapped[str] = mapped_column(sa.String(32), comment="知识库所属租户的 ID")
    creator_id: Mapped[int] = mapped_column(sa.Integer, comment="创建人的乐言账号 ID")
    updator_id: Mapped[int] = mapped_column(sa.Integer, comment="最近更新人的乐言账号 ID")
    deleted_at: Mapped[int | None] = mapped_column(sa.Integer, nullable=True, comment="删除时的时间戳，如果为 None，则说明未被删除")

    def to_data(self) -> dict[str, Any]:
        return {
            "id": self.id,
            "icon": self.icon,
            "name": self.name,
            "intro": self.intro,
            "org_id": self.org_id,
            "creator_id": self.creator_id,
            "creator_name": complete_user_name(self.creator_id),
            "updator_id": self.updator_id,
            "updator_name": complete_user_name(self.updator_id),
            "created_at": self.created_at,
            "updated_at": self.updated_at,
        }


class KnowledgeBaseHistory(DbBaseModel, BasicMixin):
    """
    项目修改历史。
    """

    knowledge_base_id: Mapped[int] = mapped_column(sa.Integer, comment="知识库的 ID")
    updator_id: Mapped[int] = mapped_column(sa.Integer, comment="更新人的乐言账号 ID")
    action: Mapped[HistoryAction] = mapped_column(sa.String(64), comment="执行的操作")
    data: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="修改的内容，不同的操作对应的数据格式会有所差异")

    def to_data(self) -> dict[str, Any]:
        return {
            "id": self.id,
            "knowledge_base_id": self.knowledge_base_id,
            "created_at": self.created_at,
            "updator_id": self.updator_id,
            "updator_name": complete_user_name(self.updator_id),
            "action": self.action,
        }

    def to_detail(self) -> dict[str, Any]:
        return {
            "id": self.id,
            "knowledge_base_id": self.knowledge_base_id,
            "updator_id": self.updator_id,
            "updator_name": complete_user_name(self.updator_id),
            "action": self.action,
            "data": self.data,
            "created_at": self.created_at,
        }


class KnowledgeBaseToShopMapping(DbBaseModel, BasicMixin):
    """
    知识库的应用店铺范围。
    """

    knowledge_base_id: Mapped[int] = mapped_column(sa.Integer, comment="知识库的 ID")
    channel_id: Mapped[int] = mapped_column(sa.Integer, comment="知识库的应用店铺的 channel_id")


class KnowledgeBaseRole(BasicMixin, DbBaseModel):
    """
    知识库角色。
    """

    org_id: Mapped[str] = mapped_column(sa.String(64), comment="角色所属的租户 ID")
    name: Mapped[str] = mapped_column(sa.String(64), comment="角色名")
    intro: Mapped[str] = mapped_column(sa.TEXT, default="", comment="角色备注")
    creator_id: Mapped[int] = mapped_column(sa.Integer, comment="创建人的 ID")
    updator_id: Mapped[int] = mapped_column(sa.Integer, comment="最新的操作人的 ID")
    deleted_at: Mapped[int | None] = mapped_column(sa.Integer, nullable=True, comment="是否已删除")
    privileges: Mapped[list[KnowledgeBaseRolePrivilege]] = mapped_column(sa.JSON, default=list, comment="角色具备的权限")
    has_limited_knowledge_base: Mapped[bool] = mapped_column(
        sa.Boolean,
        nullable=False,
        default=False,
        comment="是否限制了知识库的使用范围",
    )

    def to_data(self) -> dict[str, Any]:
        return {
            "id": self.id,
            "org_id": self.org_id,
            "name": self.name,
            "intro": self.intro,
            "privileges": self.privileges,
            "has_limited_knowledge_base": self.has_limited_knowledge_base,
        }


class KnowledgeBaseRoleToStaffMapping(DbBaseModel, BasicMixin):
    """
    知识库角色与乐言账号、账号组的映射关系。
    """

    role_id: Mapped[int] = mapped_column(sa.Integer, comment="知识库角色的 ID")
    mold: Mapped[StaffMold] = mapped_column(sa.String(64), comment="存储的类型")
    tag: Mapped[str] = mapped_column(sa.String(64), comment="用户的 ID、组的 UUID")

    def to_data(self) -> dict[str, Any]:
        return {
            "role_id": self.role_id,
            "mold": self.mold,
            "tag": self.tag,
        }


class KnowledgeBaseRoleToKnowledgeBaseMapping(DbBaseModel, BasicMixin):
    """
    知识库角色和知识库的关联关系。
    """

    role_id: Mapped[int] = mapped_column(sa.Integer, comment="知识库角色的 ID")
    knowledge_base_id: Mapped[int] = mapped_column(sa.Integer, comment="所属知识库的 ID")


class Project(DbBaseModel, BasicMixin):
    """
    知识库内的项目。可以是分类（目录），也可以是知识（文档）。
    """

    name: Mapped[str] = mapped_column(sa.String(64), comment="项目的名称")
    type: Mapped[ProjectType] = mapped_column(sa.String(32), comment="项目的类型，主要看是分类还是知识")
    org_id: Mapped[str] = mapped_column(sa.String(32), comment="项目所属租户的 ID")
    knowledge_base_id: Mapped[int] = mapped_column(sa.Integer, comment="所属知识库的 ID")
    parent_id: Mapped[int] = mapped_column(sa.Integer, default=0, comment="父项目的 ID。如果为 0，则说明其位于最上层")
    prev_id: Mapped[int] = mapped_column(sa.Integer, default=0, comment="前一项目的 ID。如果为 0，则说明其在最前面")
    next_id: Mapped[int] = mapped_column(sa.Integer, default=0, comment="后一项目的 ID。如果为 0，则说明其在最后面")
    creator_id: Mapped[int] = mapped_column(sa.Integer, comment="创建人的乐言账号 ID")
    updator_id: Mapped[int] = mapped_column(sa.Integer, comment="最近更新人的乐言账号 ID")
    deleted_at: Mapped[int | None] = mapped_column(sa.Integer, nullable=True, comment="删除时间，如果为 None，则说明还没删除")
    status: Mapped[ProjectStatus] = mapped_column(sa.String(64), default=ProjectStatus.RELEASED, comment="文档的状态")

    def to_data(self) -> dict[str, Any]:
        return {
            "id": self.id,
            "name": self.name,
            "type": self.type,
            "parent_id": self.parent_id,
            "prev_id": self.prev_id,
            "next_id": self.next_id,
            "creator_id": self.creator_id,
            "creator_name": complete_user_name(self.creator_id),
            "updator_id": self.updator_id,
            "updator_name": complete_user_name(self.updator_id),
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "status": self.status,
            "knowledge_base_id": self.knowledge_base_id,
        }


class ProjectHistory(DbBaseModel, BasicMixin):
    """
    项目修改历史。
    """

    project_id: Mapped[int] = mapped_column(sa.Integer, comment="项目的 ID")
    updator_id: Mapped[int] = mapped_column(sa.Integer, comment="更新人的乐言账号 ID")
    action: Mapped[HistoryAction] = mapped_column(sa.String(64), comment="执行的操作")
    data: Mapped[dict] = mapped_column(sa.JSON, default=dict, comment="修改的内容，不同的操作对应的数据格式会有所差异")

    # 关联的逻辑外键对应的数据
    project: Mapped[Project] = relationship(
        "Project", viewonly=True, primaryjoin="foreign(Project.id)==ProjectHistory.project_id"
    )

    def to_data(self) -> dict[str, Any]:
        return {
            "id": self.id,
            "project_id": self.project_id,
            "updator_id": self.updator_id,
            "updator_name": complete_user_name(self.updator_id),
            "action": self.action,
            "created_at": self.created_at,
        }

    def to_detail(self) -> dict[str, Any]:
        return {
            "id": self.id,
            "project_id": self.project_id,
            "updator_id": self.updator_id,
            "updator_name": complete_user_name(self.updator_id),
            "action": self.action,
            "data": self.data,
            "created_at": self.created_at,
        }


class Document(BasicMixin, DbBaseModel):
    """
    文档。也就是“知识”的最新版本的详细内容。
    """

    # 基础列
    updator_id: Mapped[int] = mapped_column(sa.Integer, comment="更新人的乐言账号 ID")
    intro: Mapped[str] = mapped_column(sa.String(255), comment="摘要")
    preview: Mapped[str] = mapped_column(sa.Text, comment="纯粹的文本内容，用于全文索引")
    content: Mapped[dict] = mapped_column(sa.JSON, comment="预览所用的 lexical 格式的数据")
    project_id: Mapped[int] = mapped_column(sa.Integer, comment="所属项目的 ID")
    is_dirty: Mapped[bool] = mapped_column(sa.Boolean, default=True, comment="是否为草稿状态")
    is_released: Mapped[bool] = mapped_column(sa.Boolean, default=False, comment="是否已发布过")
    version_no: Mapped[str] = mapped_column(sa.String(64), default="", comment="拟定一个版本")

    # 关联的逻辑外键对应的数据
    project: Mapped[Project] = relationship(
        "Project", viewonly=True, primaryjoin="foreign(Project.id)==Document.project_id"
    )

    def to_data(self) -> dict[str, Any]:
        return {
            "intro": self.intro,
            "content": self.content,
            "preview": self.preview,
            "is_dirty": self.is_dirty,
            "is_released": self.is_released,
            "version_no": self.version_no,
        }


class MyFavorite(BasicMixin, DbBaseModel):
    """
    用户收藏的知识。
    """

    user_id: Mapped[int] = mapped_column(sa.Integer, comment="乐言账号 ID")
    project_id: Mapped[int] = mapped_column(sa.Integer, comment="该用户收藏的项目的 ID")
