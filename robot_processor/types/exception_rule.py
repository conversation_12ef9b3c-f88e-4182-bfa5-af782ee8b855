from dataclasses import asdict
from dataclasses import dataclass
from typing import TYPE_CHECKING
from typing import List
from typing import NewType

from typing_extensions import TypedDict

if TYPE_CHECKING:
    from robot_processor.business_order.exception_rule import ExceptionRule

RuleScope = NewType("RuleScope", str)
# 没有特定限制的 scope，任何 scope 都可以使用这个 scope 里的规则
rule_scope_all = RuleScope("_all")
# 兜底方案的 scope，任何 scope 都可以使用这个 scope 里的规则，
# 和 all 不同的是，这里的规则在业务上是触发了兜底方案，需要跟进并补充
rule_scope_fallback = RuleScope("_fallback")


@dataclass
class RenderResult:
    reason: str
    suggestion: str


@dataclass(frozen=True)
class MatchResult:
    match_rule: "ExceptionRule.View.Matcher"
    match_pattern: str

    def is_match(self):
        # 是否匹配到了异常
        from robot_processor.business_order.exception_rule.ruler import fallback_exceptional_rule

        return self.match_rule != fallback_exceptional_rule


@dataclass(frozen=True)
class ExcInfo:
    scope: RuleScope
    raw: str


@dataclass(frozen=True)
class ProcessResult:
    exc_info: ExcInfo
    match_result: MatchResult
    render_result: RenderResult

    def to_dict(self):
        return {
            "exc_info": asdict(self.exc_info),
            "match_result": {
                "match_rule": self.match_result.match_rule.dict(),
                "match_pattern": self.match_result.match_pattern,
            },
            "render_result": asdict(self.render_result),
        }


class _ExceptionAction(TypedDict):
    """规则支持的操作，如重试/跳过/关闭"""

    name: str
    value: bool


class ExceptionRuleExtraConfig(TypedDict):
    """异常规则的额外配置信息，如是否支持跳过/重试等"""

    actions: List[_ExceptionAction]
    auto_retry: bool
