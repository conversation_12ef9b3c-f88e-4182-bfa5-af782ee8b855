#  Copyright 2023 Leyantech Ltd. All Rights Reserved.
import json
import os
import time
from datetime import datetime
from datetime import timedelta
from functools import wraps
from typing import List

import arrow
import jwt
import requests
from flask import current_app
from leyan_grpc.client.venice import GaiaStub
from leyan_proto.tcron.tcron_schedule_pb2 import JobMethodIdentifier
from leyan_proto.tcron.tcron_schedule_pb2_grpc import TCronScheduleServiceStub
from loguru import logger
from sqlalchemy.orm.attributes import flag_modified
from tcron_jobs import runner

from robot_processor.business_order.archive_task import business_order_archive
from robot_processor.business_order.archive_task import form_version_archive
from robot_processor.business_order.models import Job
from robot_processor.business_order.models import JobExecuteCron
from robot_processor.business_order.models import JobTask
from robot_processor.business_order.models import db
from robot_processor.business_order.notice.models import NotifyOrgException
from robot_processor.business_order.tasks import package_jobs
from robot_processor.client import action_client
from robot_processor.client import logistics_client
from robot_processor.client.dingding import DingdingMessageSender
from robot_processor.client.dingding import SenderEnum
from robot_processor.client.feishu import Feish<PERSON>
from robot_processor.client.wecom import WeComSender
from robot_processor.constants import BAISHOUTAO_TRADE_STATUS_JOB_CACHE_KEY
from robot_processor.constants import JOB_CRON_EXECUTE_LAST_TIME_KEY
from robot_processor.constants import TIME_ZONE
from robot_processor.enums import BusinessOrderStatus
from robot_processor.enums import BusinessOrderTimeoutType
from robot_processor.enums import ContainsType
from robot_processor.enums import JobStatus
from robot_processor.enums import JobType
from robot_processor.enums import LogisticsCancelStatus
from robot_processor.ext import cache
from robot_processor.form.api.form_editor_schema import ReceiverType
from robot_processor.job.confirm_logistics_cancel import JobTaskRunStatus
from robot_processor.job.job_model_wrapper import JobArguments
from robot_processor.job.job_record import JobRecord
from robot_processor.job.job_record.models import DailyJobRecord
from robot_processor.logging import vars as log_vars
from robot_processor.shop.models import Shop
from robot_processor.utils import convert_data


def lookup():
    """如果 package 没有被 import，相关的任务就不会被注册到 runner.jobs 中"""
    from robot_processor.customize import pdd_refund
    from robot_processor.customize.customize_org_222 import fetch_rate
    from robot_processor.invoice.tasks import jobs as invoice_tasks
    from robot_processor.invoice.workflow.third_party import doudian
    from robot_processor.invoice.workflow.third_party import pdd
    from robot_processor.invoice.workflow.third_party import qianniu
    from robot_processor.invoice.workflow.third_party import wdt

    return {
        pdd_refund,
        fetch_rate,
        invoice_tasks,
        doudian.poll_doudian_invoice_list,
        qianniu.poll_qn_invoice_list,
        wdt.poll_wdtulti_invoice_list,
        pdd.poll_pdd_invoice_list,
    }


def wrap_tcron_job(func):
    """装饰器，为函数添加 app context"""

    @wraps(func)
    def wrapper(*args, **kwargs):
        from flask import has_app_context

        from robot_processor.app import app

        log_vars.TCronJobName.set(func.__name__)
        try:
            if has_app_context():
                return func(*args, **kwargs)
            else:
                with app.app_context():
                    return func(*args, **kwargs)
        except Exception as e:
            logger.opt(exception=e).error(f"{func.__name__} raise {e} args: {args} kwargs: {kwargs}")
            return None
        finally:
            from leyan_logging import context

            context.clear()

    return wrapper


@runner.register
@wrap_tcron_job
def check_job_timeout():
    """更新工单超时信息"""
    from robot_processor.types.job_deadline import DeadlineInfo

    now = int(time.time())
    logger.info("start to find job timeout")

    for job in (
        Job.query.filter(Job.status == JobStatus.PENDING, Job.updated_at > now - 300)
        .order_by(Job.id.desc())
        .limit(1000)
    ):
        if not job.business_order:
            continue
        if job.business_order.status in [
            BusinessOrderStatus.CLOSE,
            BusinessOrderStatus.SUCCEED,
        ]:
            continue
        notifier = job.raw_step_v2.get("notifier") or {}
        if notifier.get("enabled"):
            info = DeadlineInfo.parse_obj(job.deadline_info or {})
            job.business_order.message_ts = info.job_ts
            if now > info.job_ts:
                job.business_order.is_timeout = BusinessOrderTimeoutType.TIMEOUT.value
            else:
                job.business_order.is_timeout = BusinessOrderTimeoutType.NOT_TIMEOUT.value
            try:
                db.session.commit()
            except BaseException as e:
                logger.error(str(e))
                db.session.rollback()
        else:
            job.business_order.message_ts = 0
            job.business_order.is_timeout = BusinessOrderTimeoutType.NOT_SET.value
            db.session.commit()


@runner.register
@wrap_tcron_job
def job_remind_log():
    """任务催促的日志"""
    from robot_processor.business_order.models import JobRemindRecord
    from robot_processor.ext import db

    skip_ids: set[int] = set()

    while True:
        with db.session.no_autoflush:
            record: JobRemindRecord | None = JobRemindRecord.query.filter(
                JobRemindRecord.created_at <= arrow.now(TIME_ZONE).shift(minutes=-3).int_timestamp,
                ~JobRemindRecord.id.in_(list(skip_ids)),
            ).first()
            if not record:
                break
            records = (
                JobRemindRecord.query.filter(
                    JobRemindRecord.v_from_user_id == record.v_from_user_id,
                    JobRemindRecord.v_to_user_id == record.v_to_user_id,
                    JobRemindRecord.message_digest == record.message_digest,
                    JobRemindRecord.created_at <= record.created_at + 3 * 60,
                )
                .order_by(JobRemindRecord.id.asc())
                .all()
            )
            if len(records) > 1:
                template = "[{}]3分钟内多次催促[{}]尽快处理[{}]"
            else:
                template = "[{}]催促了[{}]尽快处理[{}]"

            content = template.format(
                record.from_user["user_nick"],
                record.to_user["user_nick"],
                record.data["step_name"],
            )
            try:
                action_client.create_action_log_by_kafka(
                    dict(
                        org_id=record.data["org_id"],
                        sid=record.data["sid"],
                        user=record.from_user["user_nick"],
                        platform="",
                        label=record.data["form_name"],
                        operator="remind",
                        model="business_orders",
                        object_id=str(record.data["business_order_id"]),
                        operate_ts=arrow.now(TIME_ZONE).int_timestamp,
                        raw_json=json.dumps(dict(comment=content), ensure_ascii=True),
                    )
                )
            except Exception as e:  # noqa
                skip_ids.add(record.id)
                logger.exception("job remind log failed.")
            else:
                for per_record in records:
                    db.session.delete(per_record)
                db.session.commit()


def get_execute_flag():
    """判断是否可以开始执行定时任务，如果上一个定时任务（10分钟之内的）还未结束，则返回False"""
    last_execute_time: int = cache.get(JOB_CRON_EXECUTE_LAST_TIME_KEY) or 0
    execute_time_expire_secs = current_app.config.get("JOB_CRON_EXECUTE_EXPIRE", 60 * 10)
    cur_ts = int(time.time())
    if cur_ts - last_execute_time > execute_time_expire_secs:
        return True
    logger.info(f"last_execute_time:<{last_execute_time}>")
    return False


@runner.register
@wrap_tcron_job
def job_cron_execute():
    """定时触发定时JOB的执行，开始执行缓存中写入JOB_CRON_EXECUTE_LAST_TIME：开始执行时间"""
    logger.info("start to execute cron job")
    if not get_execute_flag():
        logger.info("last cron job execution is not over yet")
        return False
    cur_ts = int(time.time())
    cache.set(JOB_CRON_EXECUTE_LAST_TIME_KEY, cur_ts, timeout=-1)
    execute_time_expire_range = current_app.config.get("JOB_CRON_EXECUTE_RANGE", 60 * 60 * 12)
    bach_size = current_app.config.get("JOB_CRON_EXECUTE_BATCH_SIZE", 1000)
    for jobExecuteCron in (
        JobExecuteCron.query.filter(
            JobExecuteCron.status == JobStatus.INIT,
            JobExecuteCron.expect_execute_time > cur_ts - execute_time_expire_range,
            JobExecuteCron.expect_execute_time < cur_ts + 1,
        )
        .order_by(JobExecuteCron.id.asc())
        .with_for_update()
        .limit(bach_size)
    ):
        jobExecuteCron.complete_job()
    cache.set(JOB_CRON_EXECUTE_LAST_TIME_KEY, 0, timeout=-1)


def make_header(shop):
    jwt_key = current_app.config.get("BAISHOUTAO_SECRET_KEY", "jwtS")
    payload = {
        "shop": {
            "id": shop.sid,
            "title": shop.title,
            "nickname": shop.nick,
            "site": "baishoutao",
            "namespace": "baishoutao-erp",
        }
    }
    token = jwt.encode(payload, jwt_key, algorithm="HS256")
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token,
    }
    return headers


@runner.register
@wrap_tcron_job
def check_baishoutao_trade_status():
    job_dict: dict = cache.get(BAISHOUTAO_TRADE_STATUS_JOB_CACHE_KEY) or {}
    logger.info(f"start to check baishoutao_trade_status {job_dict}")
    jobs = Job.query.filter(Job.id.in_(job_dict.keys()))

    for job in jobs:
        times = job_dict.get(job.id, 1)
        data = convert_data(job)
        request_data = {"keyword": data.get("mobile") or data.get("sign")}
        headers = make_header(job.shop)
        url = current_app.config["BAISHOUTAO_ENDPOINT"] + "get-trades-list"
        time_out = current_app.config.get("BAISHOUTAO_REQUEST_TIMEOUT", 10)
        try:
            job.extra_data.update({"baishoutao_trade_status": JobStatus.RUNNING})
            times = times - 1
            job_dict.update({job.id: times})
            resp = requests.post(url, headers=headers, json=request_data, timeout=time_out)
            logger.info(f"{job.id} get baishoutao_trade_status request: {request_data}, resp: {resp.text}")
        except BaseException as exc:
            logger.error(f"{job.id} get baishoutao_trade_status request: {request_data}, resp: {exc}")
        else:
            resp_json = resp.json()
            if (
                resp.ok
                and resp_json.get("success")
                and resp_json.get("code") == 200
                and resp_json.get("result")
                and resp_json.get("result")[0].get("status", "") == "已完成"
            ):
                job.extra_data.update({"baishoutao_trade_status": JobStatus.SUCCEED})
                logger.info(f"{job.id} get baishoutao_trade_status success")
        if times <= 0:
            job_dict.pop(job.id, "")
            if not job.extra_data.get("baishoutao_trade_status") == JobStatus.SUCCEED:
                job.extra_data.update({"baishoutao_trade_status": JobStatus.FAILED})
                logger.info(f"{job.id} get baishoutao_trade_status failed ")

        cache.set(BAISHOUTAO_TRADE_STATUS_JOB_CACHE_KEY, job_dict or {}, timeout=-1)
        flag_modified(job, "_extra_data")
        db.session.add(job)
        db.session.commit()
        package_jobs.send(job.business_order_id, job.prev.id)  # type: ignore[union-attr]


@runner.register
@wrap_tcron_job
def confirm_logistics_cancel_status():
    from robot_processor.utils import unwrap_optional

    def get_tid(tid):
        if isinstance(tid, list):
            if not tid:
                return None
            return tid[0].get("tid")
        elif isinstance(tid, dict):
            return tid.get("tid")
        else:
            return tid

    def check_result(trace_list, logistics_intercept_rules):
        def convert_match_rules(condition_rules, match_rules):
            if not condition_rules or match_rules:
                return match_rules

            return [{"rule": "any", "data": item} for item in condition_rules]

        def contains_keywords(status_desc, match_rules: list) -> List[bool]:
            # 处理所有的物流规则，返回所有物流规则匹配的结果
            contains_list = []
            for item in match_rules:
                check_list = [(keyword in status_desc) for keyword in item["data"]]
                if item["rule"] == ContainsType.all.value:
                    # 物流规则 - 包含全部
                    contains_list.append(True if False not in check_list else False)
                else:
                    # 物流规则 - 包含任一
                    contains_list.append(True if True in check_list else False)

            return contains_list

        def check_keywords(traces, condition_option, match_rules):
            # 处理规则1 && 规则2
            for trace in traces:
                status_desc = trace.get("status_desc", "")
                check_list = contains_keywords(status_desc, match_rules)
                # 工单模板 -- 当满足所有/满足其一
                if condition_option == "contains_all":
                    # 物流规则 -- 包含所有
                    is_contains = True if False not in check_list else False
                else:
                    # 物流规则 -- 包含任一
                    is_contains = True if True in check_list else False

                if is_contains:
                    return True, trace

            return False, None

        logistics_cancel_keywords = current_app.config.get(
            "LOGISTICS_CANCEL_STATUS_QUERY_KEYWORDS",
            [
                "撤单成功",
                "撤单",
                "退回中",
                "退回",
                "安排退回",
                "签收人：退回商家",
                "取消",
            ],
        )
        if not logistics_intercept_rules:
            logistics_intercept_rules = [
                {
                    "condition_option": "contains_any",
                    "condition_result": "截单成功",
                    "match_rules": [{"rule": "any", "data": logistics_cancel_keywords}],
                }
            ]

        for intercept_rules in logistics_intercept_rules:
            # 工单模板匹配的规则：全包含或者包含任一
            condition_option = intercept_rules.get("condition_option", "contains_any")
            # 用户自己配的关键字词组 - 需要适配成 match_rules
            condition_rules = intercept_rules.get("condition_rules")
            # new - 用户自己配的关键字词组
            match_rules = intercept_rules.get("match_rules")
            # 进行匹配规则
            is_match, trace = check_keywords(
                trace_list,
                condition_option,
                convert_match_rules(condition_rules, match_rules),
            )
            if is_match:
                return (
                    intercept_rules.get("condition_result"),
                    trace.get("status_time"),
                    trace.get("status_desc"),
                )
        return "", trace_list[-1].get("status_time"), trace_list[-1].get("status_desc")

    def send_result(status_time, status_desc, cancel_status, jobTask):
        job.extra_data.update(
            {
                "logistics_cancel_status": {
                    "status": cancel_status,
                    "status_time": status_time,
                    "status_desc": status_desc,
                }
            }
        )
        flag_modified(job, "_extra_data")
        db.session.add(job)
        jobTask.deleted = True
        flag_modified(jobTask, "data")
        db.session.add(jobTask)
        db.session.commit()
        package_jobs.send(job.business_order_id, unwrap_optional(job.prev).id)

    logger.info("start to confirm_logistics_cancel_status")
    times_limit = current_app.config.get("LOGISTICS_CANCEL_STATUS_QUERY_TIMES_LIMIT", 15)

    jobTasks = JobTask.query.filter_by(deleted=False, job_type=JobType.CONFIRM_LOGISTICS_CANCEL.value).all()
    for jobTask in jobTasks:
        try:
            job = unwrap_optional(Job.query.filter_by(id=jobTask.job_id).first())
            if job.extra_data.get("logistics_cancel_status"):
                job.extra_data.pop("logistics_cancel_status", None)
                flag_modified(job, "_extra_data")
                db.session.add(job)

            business_data = convert_data(job)
            tid = get_tid(business_data.get("tid"))
            logistics_no = business_data.get("logistics_no")
            jobTask.run_status = JobTaskRunStatus.RUNNING.value
            jobTask.run_times += 1  # type: ignore[operator]

            shop = job.shop
            record = shop.get_recent_record()

            status_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if not tid or not logistics_no:
                status_desc = "没有订单号或物流单号"
                logistics_result = {"error_msg": status_desc}
                jobTask.run_status = JobTaskRunStatus.FAILED.value
                jobTask.data = logistics_result
                send_result(
                    status_time,
                    status_desc,
                    LogisticsCancelStatus.FAILED.value,
                    jobTask,
                )
            elif not record or not record.access_token:
                logger.warning(f"tid: {tid}, logistics_no: {logistics_no} logistics no access_token")
                status_desc = "没有淘宝授权"
                logistics_result = {"error_msg": status_desc}
                jobTask.data = logistics_result
                jobTask.run_status = JobTaskRunStatus.FAILED.value
                send_result(
                    status_time,
                    status_desc,
                    LogisticsCancelStatus.FAILED.value,
                    jobTask,
                )
            else:
                logistics_list = logistics_client.get_logistics_list(record.access_token, "", [tid], 1, 40)
                logistics = next(
                    (
                        logistics
                        for logistics in logistics_list.get("shippings", [])
                        if logistics.get("out_sid") == logistics_no
                    ),
                    None,
                )
                if not logistics:
                    if jobTask.run_times >= times_limit:
                        logger.warning(f"tid: {tid}, logistics_no: {logistics_no} logistics not found")
                        status_desc = "未查询到物流信息"
                        logistics_result = {"error_msg": status_desc}
                        jobTask.run_status = JobTaskRunStatus.SUCCEED.value
                        jobTask.data = logistics_result
                        send_result(
                            status_time,
                            status_desc,
                            LogisticsCancelStatus.FAILED.value,
                            jobTask,
                        )
                    else:
                        jobTask.run_status = JobTaskRunStatus.RUNNING.value
                        db.session.commit()
                else:
                    trace_list = logistics.get("logisticsTrace", []).get("trace_list", [])
                    flag, status_time, status_desc = check_result(
                        trace_list, business_data.get("logistics_intercept_rules")
                    )
                    logistics_result = {"logistics_result": logistics}
                    if flag:
                        jobTask.data = logistics_result
                        jobTask.run_status = JobTaskRunStatus.SUCCEED.value
                        send_result(
                            status_time,
                            status_desc,
                            flag or LogisticsCancelStatus.FAILED.value,
                            jobTask,
                        )
                    elif jobTask.run_times >= times_limit:
                        jobTask.data = {"error_msg": "超过查询次数"}
                    else:
                        # 没超过重试次数，flag为空，取错误信息
                        jobTask.data = {"error_msg": status_desc}
                    db.session.commit()
        except Exception as e:
            logger.error(e)
            logger.error(f"confirm_logistics_cancel_status处理失败 {jobTask.id}")


@runner.register
@wrap_tcron_job
def task_queue_metrics():
    from robot_metrics import Stats
    from robot_processor.constants import TASK_QUEUE_BACKGROUND_TASK
    from robot_processor.constants import TASK_QUEUE_JOB
    from robot_processor.constants import TASK_QUEUE_JOB_RETRIES
    from robot_processor.ext import task_queue

    for queue in [TASK_QUEUE_JOB, TASK_QUEUE_BACKGROUND_TASK, TASK_QUEUE_JOB_RETRIES]:
        Stats.TaskQueue.current_queue_messages(
            queue,
            "standard",
            task_queue.broker.client.hlen("dramatiq:{}.msgs".format(queue)),
        )
        Stats.TaskQueue.current_queue_messages(
            queue,
            "delayed",
            task_queue.broker.client.hlen("dramatiq:{}.DQ.msgs".format(queue)),
        )
        Stats.TaskQueue.current_queue_messages(
            queue,
            "dead-lettered",
            task_queue.broker.client.hlen("dramatiq:{}.XQ.msgs".format(queue)),
        )


@runner.register
@wrap_tcron_job
def cron_job_and_job_pool_cleanup():
    from robot_processor.business_order.archive_task import clean_invalid_job_pools
    from robot_processor.business_order.archive_task import clean_invalid_jobs

    clean_invalid_jobs.send()
    clean_invalid_job_pools.send()


@runner.register
@wrap_tcron_job
def corn_call_erp_trade():
    task_map = {
        task.job_id: task
        for task in JobTask.query.filter(JobTask.job_type == JobType.TRADE.value, JobTask.deleted.isnot(True))
    }
    jobs = Job.query.filter(Job.id.in_(list(task_map.keys())), Job.status == JobStatus.RUNNING).limit(5000)

    delay = 3000
    for index, job in enumerate(jobs):
        try:
            # 取模，每50个延迟加5分钟
            # 每5分钟跑一波，跑100波，耗时6小时
            if index != 0 and index % 50 == 0:
                delay += 5 * 60 * 1000
            package_jobs.send_with_options(args=(job.business_order.id,), delay=delay)
        except Exception as e:
            logger.error(e)


def cron_olap_job_record(start: int, end: int):
    commit_helper = 0
    jr: JobRecord
    for jr in (
        JobRecord.query.filter(JobRecord.end.is_not(None)).filter(JobRecord.end > start, JobRecord.end < end).all()
    ):
        daily_job_record = DailyJobRecord.find_or_create(
            date=arrow.now().floor("day").naive,
            assigner_name=jr.assigner_name,
            assigner_id=jr.assigner_id,
            assigner_type=jr.assigner_type,
            step_name=jr.step_name,
            step_type=jr.step_type,
            step_uuid=jr.step_uuid,
            sid=jr.sid,
            form_name=jr.form_name,
            form_id=jr.form_id,
            bo_id=jr.bo_id,
        )
        db.session.add(daily_job_record)
        db.session.flush()
        daily_job_record.execute_times_total += 1
        daily_job_record.cost += jr.cost or 0
        commit_helper += 1
        if commit_helper % 100 == 0:
            db.session.commit()
    db.session.commit()


@runner.register
@wrap_tcron_job
def divide_process_job_daily_record():
    """
    每日job数据量过大，担心一个cron容器跑不动，分而治之
    """
    yesterday = arrow.now().shift(days=-1)
    start = yesterday.floor("day")
    next = start.shift(minutes=5)
    end = yesterday.ceil("day")
    while start < end:
        logger.info("process daily job record {} {}", start, next)
        cron_olap_job_record(start.int_timestamp, next.int_timestamp)
        start = next
        next = next.shift(minutes=5)


@cache.memoize(timeout=3600)
def get_sids():
    return [res[0] for res in db.session.query(Shop.sid.distinct())]


@runner.register
@wrap_tcron_job
def form_version_clear():
    """
    清楚无工单实例的模板版本
    """
    sids = get_sids()
    logger.info(f"Sending store {sids} form clear task")
    size = 10
    page = 0
    while batch := sids[page * size : (page + 1) * size]:
        page += 1
        form_version_archive.send_with_options(args=(batch,), delay=5000 * page)


@runner.register
@wrap_tcron_job
def cron_business_order_archive():
    sids = get_sids()
    logger.info(f"Sending store {sids} business order archive task")
    size = 10
    page = 0
    while batch := sids[page * size : (page + 1) * size]:
        page += 1
        business_order_archive.send_with_options(args=(batch,), delay=5000 * page)


@runner.register
@wrap_tcron_job
def cron_duplicate_l_id():
    """根据重复的快递单号，自动创建工单"""
    from robot_processor.customize.create_bo_by_duplicate_l_id import auto_create_bo_by_duplicate_l_id

    sids = current_app.config.get("cron_duplicate_l_id_sids", [])
    logger.info(f"Sending store {sids} cron_duplicate_l_id task")
    for sid in sids:
        result = auto_create_bo_by_duplicate_l_id.send_with_options(args=(sid,), delay=5000)
        logger.info(f"cron_duplicate_l_id {sid} {result}")


@runner.register
@wrap_tcron_job
def cron_sms_send_results():
    """
    循环获取短信回执
    """
    from robot_processor.sms.moguyun import cron_msg_report

    cron_msg_report()


@runner.register
@wrap_tcron_job
def cron_count_business_order_usage(count_limit: int, dingtalk_secret: str, dingtalk_access_token: str):
    """
    统计合同期租户的前 24 小时内的工单使用量。

    :param count_limit:
    :param dingtalk_secret:
    :param dingtalk_access_token:
    :return:
    """
    from robot_processor.business_order.usage_monitor import count_usage

    logger.info("开始统计工单使用量……")
    result = count_usage(count_limit, dingtalk_secret, dingtalk_access_token)
    logger.info("统计完成，结果为 {}".format(result))


@runner.register
@wrap_tcron_job
def cron_archive_deleted_knowledge_bases():
    from robot_processor.knowledge.tasks import archive_deleted_knowledge_bases

    archive_deleted_knowledge_bases()


@runner.register
@wrap_tcron_job
def cron_archive_deleted_projects():
    from robot_processor.knowledge.tasks import archive_deleted_projects

    archive_deleted_projects()


@runner.register
@wrap_tcron_job
def cron_refresh_taobao_access_token():
    from robot_processor.shop.tasks import refresh_shop_access_token

    logger.info("开始刷新淘系 access_token...")
    refresh_shop_access_token()
    logger.info("刷新结束")


@runner.register
@wrap_tcron_job
def sync_ks_lfx_access_token():
    from robot_processor.shop.tasks import refresh_ks_lfx_access_token

    refresh_ks_lfx_access_token()


@runner.register
@wrap_tcron_job
def sync_xhs_access_token():
    from robot_processor.shop.tasks import refresh_xhs_erp_access_token

    refresh_xhs_erp_access_token()


@runner.register
@wrap_tcron_job
def org_1791(org_id: int):
    from robot_processor.customize.customize_org_1791 import org_1791

    log_vars.OrgId.set(org_id)
    return org_1791(org_id)


@runner.register
@wrap_tcron_job
def org_240(org_id: int):
    from robot_processor.customize.customize_org_240 import org_240

    log_vars.OrgId.set(org_id)
    return org_240(org_id)


@runner.register
@wrap_tcron_job
def kuaimai_split_order(org_id: int):
    from robot_processor.customize.kuaimai_split_order import kuaimai_split_order

    return kuaimai_split_order(org_id)


@runner.register
@wrap_tcron_job
def customize_redmine_652891(shop_id: int):
    from robot_processor.customize.customize_redmine_652891 import customize_redmine_652891

    return customize_redmine_652891(shop_id)


@runner.register
@wrap_tcron_job
def customize_redmine_651607(shop_id: int):
    from robot_processor.customize.customize_redmine_651607 import customize_redmine_651607

    return customize_redmine_651607()


@runner.register
@wrap_tcron_job
def customize_org_2829(shop_id: int):
    from robot_processor.customize.customize_org_2829 import customize_org_2829

    return customize_org_2829(shop_id)


@runner.register
@wrap_tcron_job
def kuaimai_stock_empty():
    from robot_processor.customize.kuaimai_stock_empty import kuaimai_stock_empty

    return kuaimai_stock_empty()


@runner.register
@wrap_tcron_job
def jst_split_order(org_id: int):
    from robot_processor.customize.jst_split_order import jst_split_order

    log_vars.OrgId.set(org_id)
    return jst_split_order(org_id)


@runner.register
@wrap_tcron_job
def new_jst_split_order(org_id: int):
    from robot_processor.customize.new_jst_split_order import new_jst_split_order

    log_vars.OrgId.set(org_id)
    return new_jst_split_order(org_id)


@runner.register
@wrap_tcron_job
def jst_abnormal_order(org_id: int):
    from robot_processor.customize.jst_abnormal_order import jst_abnormal_order

    log_vars.OrgId.set(org_id)
    return jst_abnormal_order(org_id)


@runner.register
@wrap_tcron_job
def jst_cancel_order_after_ship(org_id: int):
    from robot_processor.customize.jst_cancel_order_after_ship import jst_cancel_order_after_ship

    log_vars.OrgId.set(org_id)
    return jst_cancel_order_after_ship(org_id)


@runner.register
@wrap_tcron_job
def customize_org_2241(org_id: int):
    from robot_processor.customize.customize_org_2241 import customize_org_2241

    log_vars.OrgId.set(org_id)
    return customize_org_2241(org_id)


@runner.register
@wrap_tcron_job
def jst_refund(org_id: int):
    from robot_processor.customize.jst_refund import jst_refund

    log_vars.OrgId.set(org_id)
    return jst_refund(org_id)


@runner.register
@wrap_tcron_job
def taobao_abnormal_refund_with_return_goods(org_id: int):
    from robot_processor.customize.taobao_abnormal_refund_with_return_goods import \
        taobao_abnormal_refund_with_return_goods

    log_vars.OrgId.set(org_id)
    return taobao_abnormal_refund_with_return_goods(org_id)


@runner.register
@wrap_tcron_job
def kuaimai_exchange_goods():
    from robot_processor.customize.kuaimai_exchange_goods import kuaimai_exchange_goods

    return kuaimai_exchange_goods()


@runner.register
@wrap_tcron_job
def kuaimai_aftersale():
    from robot_processor.customize.kuaimai_aftersale import kuaimai_aftersale

    return kuaimai_aftersale()


@runner.register
@wrap_tcron_job
def doudian_aftersale(shop_id: int):
    from robot_processor.customize.doudian_aftersale_refund_only import doudian_aftersale_refund_only

    return doudian_aftersale_refund_only(shop_id)


@runner.register
@wrap_tcron_job
def doudian_aftersale_refund_with_return(shop_id: int):
    from robot_processor.customize.doudian_aftersale_refund_with_return import doudian_aftersale_refund_with_return

    return doudian_aftersale_refund_with_return(shop_id)


@runner.register
@wrap_tcron_job
def customize_redmine_637238(org_id: int):
    from robot_processor.customize.customize_redmine_637238 import customize_redmine_637238

    log_vars.OrgId.set(org_id)
    return customize_redmine_637238(org_id)


@runner.register
@wrap_tcron_job
def customize_redmine_642132(org_id: int):
    from robot_processor.customize.customize_redmine_642132 import customize_redmine_642132

    log_vars.OrgId.set(org_id)
    return customize_redmine_642132(org_id)


@runner.register
@wrap_tcron_job
def refresh_doudian_token():
    from robot_processor.client.doudian import DoudianCloudClient
    from robot_processor.client.doudian import DoudianCloudServiceError
    from robot_processor.shop.models import GrantRecord
    from robot_processor.shop.models import Shop

    current_ts_ms = int(time.time() * 1000)
    # 选取离过期还差30min内的授权做刷新
    # 参考 https://op.jinritemai.com/docs/api-docs/162/1601
    ts_ms = current_ts_ms + 1800 * 1000
    stmt = GrantRecord.query.join(Shop, Shop.id == GrantRecord.shop_id).filter(
        Shop.platform == "DOUDIAN", GrantRecord.expires_at_ms < ts_ms
    )
    records = stmt.all()
    for gr in records:
        if not gr.access_token:
            logger.error("grant record缺少access_token: [{}]", gr.id)
            continue
        if not gr.refresh_token:
            logger.error("grant record缺少refresh_token: [{}]", gr.id)
            continue
        try:
            DoudianCloudClient.refresh_token_on_need(gr)
        except DoudianCloudServiceError:
            pass


@runner.register
@wrap_tcron_job
def wln_split_order(org_id: int):
    from robot_processor.customize.wln_split_order import wln_split_order

    log_vars.OrgId.set(org_id)
    return wln_split_order(org_id)


@runner.register
@wrap_tcron_job
def send_out_app_notify():
    """发送各种站外的通知"""
    from robot_processor.form.api.form_editor_schema import StepNotifyConfig

    # 遍历NotifyOrgException取config来发消息，并置is_informed为True
    need_informed_org = db.session.query(NotifyOrgException.org_id).filter_by(is_informed=0).distinct().all()
    for org_id_tuple in need_informed_org:
        org_id = org_id_tuple[0]
        logger.info(f"开始处理租户{org_id}的异常通知")
        for notify_org_exception in NotifyOrgException.query.filter_by(is_informed=0, org_id=org_id).all():
            try:
                notice = notify_org_exception.notice
                config = StepNotifyConfig(**notice.exception_notify_config)
                job = Job.query.filter_by(id=notify_org_exception.job_id).first()
                if not job:
                    logger.warning("找不到job_id为{}的job".format(notify_org_exception.job_id))
                    continue
                args = JobArguments.extract_from_job_orm(job)
                title = args._render_text_template(config.title)  # noqa
                content = args._render_text_template(config.content)  # noqa
                # 不支持at指定人，支持at所有人
                if config.receive_type == ReceiverType.all:
                    at_mode = SenderEnum.AT_ALL
                else:
                    at_mode = None
                for channel in config.notify_channels:
                    from robot_processor.form.api.form_editor_schema import NotifyChannel

                    is_succeed = True
                    err = ""
                    if channel == NotifyChannel.dingding:
                        is_succeed, err = DingdingMessageSender().send(
                            url=config.dingding_webhook,
                            signature=config.dingding_secret,
                            content=title + "\n" + content,
                            image_urls=[],
                            sender={"value": at_mode},
                        )
                    if channel == NotifyChannel.qiwei:
                        is_succeed, err = WeComSender.send_message(
                            url=config.qiwei_webhook,
                            sender={"value": at_mode},
                            image_urls=[],
                            text_picture_merge=False,
                            content=title + "\n" + content,
                        )
                    if channel == NotifyChannel.feishu:
                        resp = Feishu().send_feishu_group_with_webhook(
                            webhook=config.feishu_webhook,  # type: ignore[arg-type]
                            content=title + "\n" + content,
                            secret=config.feishu_secret,  # type: ignore[arg-type]
                        )
                        if resp.StatusCode == 200:
                            is_succeed = True
                        else:
                            is_succeed = False
                            err = resp.msg
                    if not is_succeed:
                        logger.opt(exception=Exception(err)).error(f"send_out_app_notify error: {err}")

            except Exception as e:
                logger.opt(exception=e).error(f"send_out_app_notify error: {e}")
            finally:
                notify_org_exception.is_informed = True
                db.session.add(notify_org_exception)
                db.session.commit()


@runner.register
@wrap_tcron_job
def sync_rds_by_contract() -> int:
    from robot_processor.client import taobao_client
    from robot_processor.ext import cache
    from robot_processor.shop.models import ContractInfo

    count = 0
    cache_key = "sync_rds_by_contract"
    last_timestamp = cache.get(cache_key) or 0
    now_timestamp = int(time.time())
    contracts = (
        ContractInfo.query.filter(ContractInfo.end_ts.between(last_timestamp, now_timestamp))
        .group_by(ContractInfo.org_id)
        .all()
    )
    org_ids = list({contract.org_id for contract in contracts})
    logger.info(
        "[{} - {}]已过期租户为 {}".format(
            (arrow.get(last_timestamp).format("YYYY-MM-DD HH:mm:ss") if last_timestamp else "<初始化>"),
            arrow.get(now_timestamp).format("YYYY-MM-DD HH:mm:ss"),
            org_ids,
        )
    )
    for org_id in org_ids:
        shops = Shop.query.filter(Shop.org_id == str(org_id), Shop.Filters.platform_taobao).all()
        for shop in shops:
            if not (shop.channel_id and shop.seller_id):
                logger.bind(sid=shop.sid).warning("shop:{} 缺少channel_id或seller_id".format(shop.id))
                continue
            rds_status = (
                taobao_client.jushita_jdp_users_get(user_id=shop.seller_id)
                .map(lambda res: res.get("users", []))
                .map(lambda users: users[0].get("status", -1) if users else -1)
                .unwrap_or(-1)
            )
            rds_status_zh = {
                0: "暂停",
                1: "正常",
                2: "session失效，停止",
                3: "已删除",
                -1: "未知",
            }.get(rds_status, str(rds_status))
            if rds_status == 1:
                if (result := taobao_client.jushita_jdp_user_delete(shop.nick)).is_ok():
                    logger.bind(sid=shop.sid).info("已停止订单库推送")
                    count += 1
                else:
                    logger.bind(sid=shop.sid).error(f"停止订单库推送失败 {result.unwrap_err()}")
            else:
                logger.bind(sid=shop.sid).info(f"店铺状态: {rds_status_zh} 无需处理")
    cache.set(cache_key, now_timestamp, timeout=60 * 60 * 24 * 3)
    return count


@runner.register
@wrap_tcron_job
def refresh_kuaimai_grant_records():
    from robot_processor.client.conf import app_config
    from robot_processor.enums import AuthType
    from robot_processor.shop.auth_manager import Credentials
    from rpa.erp.kuaimai.kuaimai import KuaimaiOpenPlatformAPIClient

    now_timestamp = int(time.time())
    next_week = now_timestamp + (3600 * 24 * app_config.KM_REFRESH_TOKEN_BEFORE_DAYS)
    credentials: list[Credentials] = Credentials.query.filter(
        Credentials.auth_type == AuthType.KUAIMAI,
        Credentials.expire_timestamp.between(now_timestamp, next_week),
    ).all()
    logger.info("需要处理的快麦授权数量为: {}", len(credentials))
    for credential in credentials:
        try:
            refresh_token: str | None = credential.auth_extra_data.get("refreshToken")
            if not refresh_token:
                logger.error("快麦授权异常，org_id: {}, auth_id: {}".format(credential.org_id, credential.id))
                continue
            kuaimai_client = KuaimaiOpenPlatformAPIClient(credential.auth_extra_data)
            km_refresh_session_response = kuaimai_client.refresh_session(refresh_token)
            credential.expire_timestamp = int(time.time()) + km_refresh_session_response.session.expires_in
            db.session.commit()
            logger.info("快麦授权已刷新，org_id: {}, auth_id: {}".format(credential.org_id, credential.id))
        except Exception as e:
            logger.error("快麦刷新授权失败，auth_id: {}， err: {}".format(credential.id, e))
            continue


class Scheduler:
    _stub: TCronScheduleServiceStub

    def __init__(self):
        self._stub = GaiaStub.with_consul_address(TCronScheduleServiceStub, "consul://tcron.worker")  # noqa

    def _get_job_identifier(self, func):
        match os.environ.get("env"):
            case "stgzb" | "stg":
                env = "stg"
            case "prdzb" | "prd":
                env = "prd"
            case _ as env:
                if current_app.config.get("TESTING"):
                    env = "unittest"
                else:
                    raise Exception(f"不支持的环境{env}")
        return JobMethodIdentifier(env=env, app="robot-processor", proc="tcron-jobs", job_name=func.__name__)

    def run_now(self, func, timeout=30, **kwargs):
        from google.protobuf.duration_pb2 import Duration
        from leyan_proto.tcron.tcron_schedule_pb2 import CreateScheduleRequest

        from robot_processor.utils import proto_to_json

        request = CreateScheduleRequest(
            job_identifier=self._get_job_identifier(func),
            schedule_name="sdk-run-now",
            job_timeout=Duration(seconds=timeout),
            delay_ms=0,
            argument="",
        )
        if kwargs:
            request.argument = json.dumps(kwargs, ensure_ascii=False)
        logger.info(f"run_now {proto_to_json(request)}")
        return self._stub.create_schedule(request)

    def run_later(self, func, delay: timedelta, timeout=30, **kwargs):
        from google.protobuf.duration_pb2 import Duration
        from leyan_proto.tcron.tcron_schedule_pb2 import CreateScheduleRequest

        from robot_processor.utils import proto_to_json

        request = CreateScheduleRequest(
            job_identifier=self._get_job_identifier(func),
            schedule_name="sdk-run-later",
            job_timeout=Duration(seconds=timeout),
            delay_ms=int(delay.total_seconds() * 1000),
            argument="",
        )
        if kwargs:
            request.argument = json.dumps(kwargs, ensure_ascii=False)
            logger.info(f"run_later {proto_to_json(request)}")
        return self._stub.create_schedule(request)

    def create_schedule(self, func, schedule_name: str, cron: str, timeout=30, **kwargs):
        from google.protobuf.duration_pb2 import Duration
        from leyan_proto.tcron.tcron_schedule_pb2 import CreateScheduleRequest

        from robot_processor.utils import proto_to_json

        request = CreateScheduleRequest(
            job_identifier=self._get_job_identifier(func),
            schedule_name=schedule_name,
            cron_schedule=cron,
            job_timeout=Duration(seconds=timeout),
        )
        if kwargs:
            request.argument = json.dumps(kwargs, ensure_ascii=False)
        logger.info(f"schedule {proto_to_json(request)}")
        return self._stub.create_schedule(request)

    def update_schedule(self, schedule_id: str, schedule_name: str, cron: str, timeout=30, **kwargs):
        from google.protobuf.duration_pb2 import Duration
        from leyan_proto.tcron.tcron_schedule_pb2 import UpdateScheduleRequest

        from robot_processor.utils import proto_to_json

        request = UpdateScheduleRequest(
            schedule_id=schedule_id,
            schedule_name=schedule_name,
            cron_schedule=cron,
            job_timeout=Duration(seconds=timeout),
        )
        if kwargs:
            request.argument = json.dumps(kwargs, ensure_ascii=False)
        logger.info(f"update_schedule {proto_to_json(request)}")
        return self._stub.update_schedule(request)

    def close_schedule(self, schedule_id: str):
        from leyan_proto.tcron.tcron_schedule_pb2 import CloseScheduleRequest

        request = CloseScheduleRequest(schedule_id=schedule_id)
        return self._stub.close_schedule(request)


scheduler = Scheduler()
