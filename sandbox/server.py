from flask import Flask
from flask import jsonify
from flask import request
from loguru import logger

app = Flask(__name__)


@app.post("/invoke")
def invoke():
    from robot_types.core import FnSignature
    from robot_types.helper import deserialize

    from sandbox.runner import CodeRunner

    data = request.get_json()
    code_runner = CodeRunner(data["code"], deserialize(data["fn_signature"], FnSignature))
    params = code_runner.prepare_params(data["params"], data.get("context"))
    result = code_runner.run(params)
    if result.is_err():
        logger.opt(exception=result.err()).error(f"request: {data} result: {result}")
        return jsonify(success=False, output=None, error=str(result.err()))
    else:
        output = result.unwrap()
        logger.info(f"params: {data['params']} return: {output}")
        return jsonify(success=True, output=code_runner.jsonify_return_value(output), error=None)


@app.post("/debug")
def debug():
    import traceback

    from robot_types.core import Symbol
    from robot_types.helper import deserialize
    from robot_types.helper.data_projection import label_based

    from sandbox.code import generate_fn_signature
    from sandbox.runner import CodeRunner

    data = request.get_json()
    param_symbols = deserialize(data["param_symbols"], list[Symbol])
    return_symbols = deserialize(data["return_symbols"], list[Symbol])
    signature = generate_fn_signature(param_symbols, return_symbols)
    code_runner = CodeRunner(data["code"], signature)
    params = label_based.convert_to_label_map(data["params"], param_symbols)
    result = code_runner.run(params, data.get("context"))
    if result.is_err():
        error = result.err()
        logger.opt(exception=error).error(f"request: {data} error: {error}")
        tb_str = "".join(traceback.format_exception(type(error), error, error.__traceback__))
        return jsonify(success=False, output=None, error=str(result.err()), traceback=tb_str)
    else:
        return jsonify(success=True, output=code_runner.jsonify_return_value(result.unwrap()), error=None)


@app.get("/health")
def health():
    return "OK"
