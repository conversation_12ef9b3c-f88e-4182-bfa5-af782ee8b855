from robot_types.core import FnSignature
from robot_types.core import Symbol
from robot_types.core import TypeSpec
from robot_types.helper.data_converter.type_conversion import type_spec_to_type_hint_code


def fn_signature_to_type_hint_code(fn_signature: FnSignature):
    params = TypeSpec("collection", typename="Params", properties=fn_signature.params)
    rtype = fn_signature.rtype
    return {
        "params": type_spec_to_type_hint_code(params),
        "return": type_spec_to_type_hint_code(rtype),
    }


def generate_fn_signature(param_symbols: list[Symbol], return_symbols: list[Symbol], name: str = "main") -> FnSignature:
    return FnSignature(
        name=name,
        params={symbol.label: symbol.type_spec for symbol in param_symbols},
        rtype=TypeSpec(
            "collection",
            typename="Return",
            properties={symbol.label: symbol.type_spec for symbol in return_symbols},
        ),
    )
